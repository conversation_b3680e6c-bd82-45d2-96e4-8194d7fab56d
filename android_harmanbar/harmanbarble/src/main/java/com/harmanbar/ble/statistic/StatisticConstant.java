package com.harmanbar.ble.statistic;

public class StatisticConstant {

    public static final String ACTION = "action";
    public static final String MODULE = "module";
    public static final String LEVEL = "level";
    public static final String PAYLOAD = "payload";
    public static final String PLAYLOAD = "playload";


    public static final String LEVEL_INFO = "INFO";
    public static final String LEVEL_WARNING = "WARNING";
    public static final String LEVEL_ERROR = "ERROR";

    public static final String ACTION_BLE_CONNECT_SUCCESS = "BLEConnectSuccess";
    public static final String ACTION_BLE_CONNECT_FAIL = "BLEConnectFail";


    public static final String MODULE_START_SEARCH = "startSearch";//开始搜索BLE信号
    public static final String MODULE_START_CONECT_BLE = "startConnectBLE";//开始连接BLE
    public static final String MODULE_NO_BLE_SERVICE = "noBLEService";//BLE连接未找到service
    public static final String MODULE_NO_BLE_CHARACTERISTICS = "noBLECharacteristics";//BLE连接找到service,但未找到Characteristics
    public static final String MODULE_START_CONNECT_WIFI = "startConnectWiFi";//开始连接WiFi
    public static final String MODULE_SEND_CONNECT_FAIL = "sendConnectFail";//	发送BLE指令失败
    public static final String MODULE_CONNECT_WIFI_SUCCESS = "connectWiFiSuccess";//BLE回复连接WiFi成功
    public static final String MODULE_CONNECT_WIFI_FAIL = "connectWiFiFail";//备回复连接失败，并带有状态码
    public static final String MODULE_WIFI_CONNECTED = "WiFiConnected";//局域网发现
    public static final String MODULE_WIFI_TIMEOUT = "WiFiTimeout";//局域网发现超时


    public static final int HarmanID = 0xFDDF;
    public static final int LP_COMMAND_HEADER_TAG = 0x4c50;
    public static final int[] LP_COMMAND_PRODUCT_ID = new int[]{0x2071, 0x2072, 0x2073, 0x2074, 0x2075, 0x208C};//设备广播包唯一设备标识,和pid对应

    public static final int PLATFORM_A88 = 0x0010;
    public static final int PLATFORM_A98 = 0x0020;
    public static final int PLATFORM_A98L = 0x0021;
    public static final int PLATFORM_A118 = 0x0030;
    public static final int PLATFORM_WALNUT = 0x0040;

    public static final int LP_GET_DEVICEINFO_CMD = 0x1001;//Speaker Information (model, MAC, SW version, etc.)
    public static final int LP_SEND_SSID_PASSWORD = 0x1002;
    public static final int LP_SET_COUNTRY_CODE = 0x1003;
    public static final int LP_WIFI_SETUP_START = 0x1005;//Device to notify apps the result of Wi-Fi setup.
    public static final int LP_WIFI_SETUP_END = 0x1006;//Device to notify apps the result of Wi-Fi setup.
    public static final int LP_AUTH_START = 0x100E;//{"timeout": 60} Firmware use this timeout value to know when the auth process should timeout
    public static final int LP_AUTH_RESULT = 0x100F;//Firmware confirm validity When user press the button on the device, firmware should send a notify to app
    public static final int LP_AUTH_CANCEL = 0x1010;//Apps request device to cancel authentication process.
    public static final int LP_AUTH_PAIR = 0x1013;
    public static final int LP_AUTH_PAIRING = 0x1014;
    public static final int LP_GET_AP_LIST_CMD = 0x1007;
    public static final int CONFIG_SOUND_TIMEOUT = 30;//{"timeout": 30}

    //Settings
    public static final int LP_SET_BT_MODE = 0x1101;
    public static final int LP_SET_WIFI_AP_INFO = 0x1102;
    public static final int LP_GET_BT_SOURCE_LIST_CMD = 0x1103;
    public static final int LP_SET_BT_SOURCE_CONN_CMD = 0x1104;
    public static final int LP_GET_OTA_STATUS = 0x1306;
    public static final int LP_REQ_DEV_OTA = 0x1307;
    public static final int LP_GET_OTA_ACCESS_POINT = 0x1308;
    public static final int LP_SET_SPRINT_NETWORK_STATE = 0xf001;
    public static final int CLEAR_HISTORY_ONE_OS_VERSION = 0x1707;
    public static final int SET_DEVICE_NAME = 0x1801;
    public static final int GET_DEVICE_NAME = 0x1802;
    public static final int GET_REAR_SPEAKER_VOLUME = 0x1810;
    public static final int SET_REAR_SPEAKER_VOLUME = 0x180F;
    public static final int GET_PROD_SETTING = 0x1709;
    public static final int SET_PROD_SETTING = 0x1708;

    public static final int SET_BLUETOOTH_CONFIG = 0x1813;
    public static final int GET_BLUETOOTH_CONFIG = 0x1814;
    public static final int SET_FACTORY_RESTORE = 0x1809;
    public static final int GET_BATTERY = 0x1815;
    public static final int SET_EQ = 0x1901;
    public static final int GET_EQ = 0x1902;
    public static final int SET_CALIBRATION = 0x1905;
    public static final int GET_CALIBRATION_STATE = 0x1913;
    public static final int CANCEL_CALIBRATION = 0x190F;
    public static final int LP_PLAY_DEMO_SOUND = 0x1910;
    public static final int LP_CANCEL_DEMO_SOUND = 0x1911;
    public static final int GET_STREAMING_STATUS = 0x1912;
    public static final int SET_ACTIVE_EQ = 0x1916;
    public static final int GET_EQ_LIST = 0x1917;
    public static final int LP_SET_CAST_GROUP = 0x1D01;
    public static final int LP_DESTROY_CAST_GROUP = 0x1D02;
    public static final int LP_GET_GROUP_INFO = 0x1d03;
    public static final int LP_GROUP_CALIBRATION = 0x1d04;
    public static final int LP_RENAME_GROUP = 0x1d05;
    public static final int LP_SWITCH_STEREO_CHANNEL = 0x1d06;
    public static final int LP_TRIGGER_CAST_LED = 0x1d07;
    public static final int LP_SET_CAST_GROUP_RESULT = 0x1d08;
    public static final int LP_SKIP_DEMO_SOUND = 0x1d0a;
    public static final int LP_GET_GROUP_PARAMETER = 0x1d0b;
    public static final int LP_NOTIFY_GROUP_PARAMETER = 0x1d0c;

    public static final int GET_GROUP_DEVICES_OTA_STATUS = 0x1d12;

    public static final int GET_GROUP_DEVICES_FLAG = 0x1d13;

    public static final int SET_GENERAL_CONFIG = 0x1E01;
    public static final int GET_GENERAL_CONFIG = 0x1E02;
    public static final int SET_PLAY_PARTY_SOUND = 0x1E03;
    public static final int SET_DJ_PAD = 0x1E04;
    public static final int GET_DJ_PAD = 0x1E05;
    public static final int SET_DJ_EVENT = 0x1E06;
    public static final int SET_LIGHT_COLOR_PICKER = 0x1E07;
    public static final int GET_LIGHT_COLOR_PICKER = 0x1E08;
    public static final int NOTIFY_GENERAL_CONFIG = 0x1E09;
    public static final int ENTER_AURACAST = 0x1F01;
    public static final int EXIT_AURACAST = 0x1F02;
    public static final int SET_AURACAST_SQ_MODE = 0x1F03;
    public static final int GET_AURACAST_SQ_MODE = 0x1F04;
    public static final int NOTIFY_AURACAST_SQ_MODE = 0x1F05;

    public static final int SEND_APP_CONTROLLER = 0x1812;

    public static final int SET_VOLUME_LEVEL = 0x1817;
    public static final int GET_VOLUME_LEVEL = 0x1818;
    public static final int GET_MEDIA_SOURCE_STATUS = 0x1819;
    public static final int NOTIFY_VOLUME_LEVEL = 0x181A;
    public static final int NOTIFY_META_DATA = 0x181B;
    public static final int NOTIFY_MEDIA_SOURCE_STATUS = 0x181C;
    public static final int SET_AUTO_POWER_OFF_TIMER = 0x181D;
    public static final int GET_AUTO_POWER_OFF_TIMER = 0x181E;

    public static final int SET_FEED_BACK_TONE_CONFIG = 0x181F;
    public static final int GET_FEED_BACK_TONE_CONFIG = 0x1820;

    public static final int SET_BATTERY_SAVING_MODE = 0x1823;
    public static final int GET_BATTERY_SAVING_MODE = 0x1824;

    public static final int GET_MEDIA_SOURCE = 0x1806;
    public static final int SET_SLEEP_TIMER = 0x1807;
    public static final int GET_SLEEP_TIMER = 0x1808;

    public static final int SET_SMART_BUTTON_CONFIG = 0x180B;
    public static final int GET_SMART_BUTTON_CONFIG = 0x180C;
    public static final int PREVIEW_SOUND_SCAPE = 0x1821;
    public static final int CANCEL_SOUND_SCAPE = 0x1822;
    public static final int SET_SMART_MODE = 0x1828;
    public static final int GET_SMART_MODE = 0x1829;

    public static final int CONTROL_SOUNDSCAPE_V2 = 0x182C;
    public static final int GET_SOUNDSCAPE_V2_STATE = 0x182D;
    public static final int NOTIFY_SOUNDSCAPE_V2_STATE = 0x182E;
    public static final int SET_SOUNDSCAPE_V2_CONFIG = 0x182F;
    public static final int GET_SOUNDSCAPE_V2_CONFIG = 0x1830;
    public static final int MONITOR_SOUNDSCAPE_V2_DEMO_TRACK = 0x1831;
    public static final int MONITOR_SOUNDSCAPE_V2_SETTINGS = 0x1832;

    public static final int SET_LIGHT_INFO = 0x1833;
    public static final int GET_LIGHT_INFO = 0x1834;
    public static final int NOTIFY_LIGHT_INFO = 0x1835;
    public static final int RESET_LIGHT_PATTERN_COLOR = 0x1836;

    public static final int GET_REAR_SPEAKER_STATUS = 0x1825;
    public static final int SET_PERSONAL_LISTENING_MODE = 0x1826;
    public static final int GET_PERSONAL_LISTENING_MODE = 0x1827;
    public static final int SET_IR_LEARN = 0x180A;

    public static final int NOTIFY_EQ_CHANGED = 0x1915;

    public static final int GET_PRODUCT_USAGE = 0x1703;
    public static final int SET_WIFI_COUNTRY_CODE = 0x1704;

    public static final int GET_FEATURE_SUPPORT = 0x1011;
    public static final int GET_BLE_ENCRYPTION_KEY = 0x1012; // Apps request device to get the key.

    public static final int GET_CBL_STATUS = 0x1209;
    public static final int CBL_LOGOUT = 0x120A;

    public static final int SET_C4A_PERMISSION_STATUS = 0x1B01;
    public static final int GET_C4A_PERMISSION_STATUS = 0x1B02;
    public static final int SET_CHROME_CAST_OPT_IN = 0x1B03;
    public static final int GET_CHROME_CAST_OPT_IN = 0x1B04;

    public static final int WALNUT_RANDOM_CODE = 0x2001;
    public static final int RESPONSE_ACK = 0x8000;
    public static final int RESPONSE_NO_ERROR = 0x0000;
    public static final int RESPONSE_INVALID_CMD = 0x0001;
    public static final int RESPONSE_INVALID_DATA_PACKET = 0x0002;
    public static final int RESPONSE_PARAM_LEN_OUT_OF_RANGE = 0x0003;
    public static final int RESPONSE_PARAM_LEN_TOO_SHORT = 0x0004;
    public static final int RESPONSE_CMD_HANDLING_FAILED = 0x0005;
    public static final int RESPONSE_WAITING_RSP_TIMEOUT = 0x0006;
    public static final int RESPONSE_DATA_XFER_ALREADY_STARTED = 0x0007;
    public static final int RESPONSE_DATA_XFER_NOT_STARTED_YET = 0x0008;
    public static final int RESPONSE_DATA_SEGMENT_CRC_CHECK_FAILED = 0x0009;
    public static final int RESPONSE_WHOLE_DATA_CRC_CHECK_FAILED = 0x000a;
    public static final int RESPONSE_DATA_XFER_LEN_NOT_MATCHED = 0x000b;
    public static final int RESPONSE_MUSIC_ALREADY_PLAYING = 0x000c;
    public static final int RESPONSE_MUSIC_NOT_PLAYING_YET = 0x000d;
    public static final int NO_ENOUGH_MEMORY = 0x000e;

    public static final int RESPONSE_BATTERY_STATUS = 0x1816;
    public static final int RESPONSE_REAR_SPEAKER_STATUS = 0x182A;

    //BLE Device hexName
    public static final String specificDataStartCharacter = "0f6da118";

    //service UID
    public static String[] serviceUUID = new String[]{
            "65786365-6c70-6f69-6e74-2e636f6d0000"};

    //write characteristic UID
    public static String[] characteristicWriteUID = new String[]{
            "65786365-6c70-6f69-6e74-2e636f6d0002"};


    //Service Data
    public static final int DATA_TYPE_SERVICE_DATA_16_BIT = 0x16;

    public static final int DATA_TYPE_SERVICE_UUIDS_16_BIT_COMPLETE = 0x03;


    public static final String LZK_KDLKSI_KCLKI = "~linkplayble@#==";// 加密解密密钥
    public static final String IV_PARAM = "0000000000000000";

}
