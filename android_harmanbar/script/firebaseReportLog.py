import subprocess
import datetime
import os
import sys

# need Python 3.x env
# how to use:
# 1. connect your device to your computer with adb
# 2. run this script -> python3 ./script/firebaseReportLog.py
# 3. do your actions in app after terminal display: Press Ctrl+C to stop logging...
# 4. open the log file in your desktop

def run_adb_command(command):
    try:
        subprocess.run(['adb'] + command.split(), check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error executing ADB command: {command}")
        print(f"Error details: {e}")
        return False

def save_logcat():
    # Get current timestamp for filename
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    desktop_path = os.path.expanduser("~/Desktop")
    output_file = os.path.join(desktop_path, f"{timestamp}.txt")

    # Set verbose logging properties
    if not run_adb_command("shell setprop log.tag.FA VERBOSE"):
        return
    if not run_adb_command("shell setprop log.tag.FA-SVC VERBOSE"):
        return

    print(f"Saving logcat output to: {output_file}")
    print("Press Ctrl+C to stop logging...")

    try:
        # Start logcat and save output
        with open(output_file, 'w') as f:
            process = subprocess.Popen(
                ['adb', 'logcat', '-v', 'time', '-s', 'FA', 'FA-SVC'],
                stdout=f,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            # Wait for the process to complete (or Ctrl+C)
            process.wait()

    except KeyboardInterrupt:
        print("\nStopping logcat capture...")
        process.terminate()
        print(f"Logcat output saved to: {output_file}")
    except Exception as e:
        print(f"Error occurred: {e}")
        sys.exit(1)
    
    return output_file

def extract_firebase_events(log_file):
    """Extract Firebase event data from the log file"""
    print(f"Processing log file to extract Firebase event data...")
    
    # Check if the log file exists
    if not os.path.exists(log_file):
        print(f"Error: Log file not found at {log_file}")
        return
    
    event_data_file = log_file.replace('.txt', '_events.txt')
    
    try:
        with open(log_file, 'r') as f:
            log_content = f.read()
        
        # Extract all event blocks
        events = []
        event_start_index = 0
        
        while True:
            # Find the start of an event block
            event_start_index = log_content.find("event {", event_start_index)
            if event_start_index == -1:
                break
            
            # Track braces to find the end of the event block
            brace_count = 1  # We've already found the opening brace of "event {"
            current_index = event_start_index + 7  # Move past "event {"
            
            while brace_count > 0 and current_index < len(log_content):
                if log_content[current_index] == '{':
                    brace_count += 1
                elif log_content[current_index] == '}':
                    brace_count -= 1
                current_index += 1
            
            if brace_count == 0:
                # We found the matching closing brace
                event_text = log_content[event_start_index:current_index]
                events.append(event_text)
                event_start_index = current_index
            else:
                # Unmatched braces, move to next occurrence
                event_start_index += 7
        
        # Write the extracted events to file
        if events:
            with open(event_data_file, 'w') as f:
                for i, event_text in enumerate(events):
                    f.write(f"--- Event {i+1} ---\n")
                    f.write(event_text)
                    f.write("\n\n")
            
            print(f"Extracted {len(events)} Firebase events to: {event_data_file}")
        else:
            print("No Firebase events found in the log file.")
    except Exception as e:
        print(f"Error processing log file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the logcat capture
    output_file = save_logcat()
    
    # Process the log file after capture
    if output_file:
        try:
            extract_firebase_events(output_file)
        except Exception as e:
            print(f"Error during extraction: {e}")
            import traceback
            traceback.print_exc()