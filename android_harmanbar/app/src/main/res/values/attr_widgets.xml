<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CircularRingProgressView">
        <attr name="crpv_max" format="integer" />
        <attr name="crpv_ring_width" format="dimension" />
        <attr name="crpv_middle_circle_border_width" format="dimension" />
        <attr name="crpv_long_point_width" format="dimension" />
        <attr name="crpv_long_point_height" format="dimension" />
        <attr name="crpv_value" format="integer" />
        <attr name="crpv_debug" format="boolean" />
    </declare-styleable>

    <declare-styleable name="CircleSliderView">
        <attr name="csv_ring_width" format="dimension" />
        <attr name="csv_point_radius" format="dimension" />
        <attr name="csv_point_border_width" format="dimension" />
        <attr name="csv_text_size" format="dimension" />
        <attr name="csv_value" format="integer" />
        <attr name="csv_debug" format="boolean" />
    </declare-styleable>

    <declare-styleable name="VerticalScaleView">
        <attr name="vsv_max" format="integer" />
        <attr name="vsv_value" format="integer" />
        <attr name="vsv_debug" format="boolean" />
    </declare-styleable>

    <declare-styleable name="HorizontalScaleView">
        <!--max eg:hsv_max=10,value=[0,10]-->
        <attr name="hsv_max" format="integer" />
        <attr name="hsv_value" format="integer" />
        <attr name="hsv_debug" format="boolean" />
        <attr name="hsv_space" format="dimension" />
        <attr name="hsv_line_width" format="dimension" />
        <attr name="hsv_scale_size" format="integer" />

    </declare-styleable>

    <declare-styleable name="GuitarLooperCircleButton">
        <!--  / READY,RECORDING,PLAY, RECORDFINISH, PAUSE, STOP-->
        <attr name="glcb_type">
            <enum name="ready" value="0" />
            <enum name="recording" value="1" />
            <enum name="play" value="2" />
            <enum name="recordfinish" value="3" />
            <enum name="pause" value="4" />
            <enum name="stop" value="5" />
        </attr>
        <attr name="glcb_debug" format="boolean" />
    </declare-styleable>

    <declare-styleable name="GuitarLooperTimeScaleView">
        <!---->
        <attr name="tsv_max" format="integer" />
        <attr name="tsv_viewport_show_count" format="integer" />
        <attr name="tsv_text_size" format="dimension" />
        <attr name="tsv_debug" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ComponentControlSlider">
        <attr name="ccs_max_value" format="integer" />
        <attr name="ccs_current_value" format="integer" />
        <attr name="ccs_segment_count" format="integer" />
        <attr name="ccs_border_color" format="color" />
        <attr name="ccs_debug" format="boolean" />
        <attr name="ccs_type">
            <enum name="continuedvalue" value="1" />
            <enum name="uncontinuedvalue" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="ScaleContainer">
        <attr name="sc_scale_factor" format="float" />
    </declare-styleable>

</resources>