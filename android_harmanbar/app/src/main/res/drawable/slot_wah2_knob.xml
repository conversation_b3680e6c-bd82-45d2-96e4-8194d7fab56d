<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="285dp"
    android:height="461dp"
    android:viewportWidth="285"
    android:viewportHeight="461">
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:fillColor="#DAC7A5"/>
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:strokeWidth="3"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="191"
          android:startY="45.5"
          android:endX="265.5"
          android:endY="441.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFE5DFC5"/>
        <item android:offset="1" android:color="#FF918A69"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M26.74,24h114v102h-114z"/>
    <path
        android:pathData="M55.3,109.68C49.66,104.03 45.81,96.83 44.25,89C42.69,81.16 43.49,73.04 46.55,65.66C49.61,58.28 54.78,51.98 61.42,47.54C68.07,43.1 75.88,40.73 83.86,40.73C91.85,40.73 99.66,43.1 106.3,47.54C112.95,51.98 118.12,58.28 121.18,65.66C124.24,73.04 125.04,81.16 123.48,89C121.92,96.83 118.07,104.03 112.42,109.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.31,109.68C49.66,104.03 45.81,96.84 44.25,89C42.69,81.17 43.49,73.04 46.55,65.66C48.6,60.71 51.6,56.25 55.37,52.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.3,109.68C49.65,104.03 45.81,96.84 44.25,89C42.69,81.17 43.49,73.04 46.55,65.66C48.59,60.74 51.57,56.29 55.3,52.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.86,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M83.86,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M68.17,64.74L65.41,61.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M144.74,24h114v102h-114z"/>
    <path
        android:pathData="M173.3,109.68C167.65,104.03 163.81,96.83 162.25,89C160.69,81.16 161.49,73.04 164.55,65.66C167.61,58.28 172.78,51.98 179.43,47.54C186.07,43.1 193.88,40.73 201.86,40.73C209.85,40.73 217.66,43.1 224.3,47.54C230.95,51.98 236.12,58.28 239.18,65.66C242.24,73.04 243.04,81.16 241.48,89C239.92,96.83 236.07,104.03 230.42,109.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.3,109.68C167.66,104.03 163.81,96.84 162.25,89C160.69,81.17 161.49,73.04 164.55,65.66C166.6,60.71 169.6,56.25 173.37,52.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.3,109.68C167.65,104.03 163.81,96.84 162.25,89C160.69,81.17 161.49,73.04 164.55,65.66C166.59,60.74 169.57,56.29 173.3,52.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.86,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M201.86,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M186.17,64.74L183.41,61.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M26.74,138h114v102h-114z"/>
    <path
        android:pathData="M55.3,223.68C49.66,218.03 45.81,210.84 44.25,203C42.69,195.16 43.49,187.04 46.55,179.66C49.61,172.28 54.78,165.98 61.42,161.54C68.07,157.1 75.88,154.73 83.86,154.73C91.85,154.73 99.66,157.1 106.3,161.54C112.95,165.98 118.12,172.28 121.18,179.66C124.24,187.04 125.04,195.16 123.48,203C121.92,210.84 118.07,218.03 112.42,223.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.31,223.68C49.66,218.03 45.81,210.84 44.25,203C42.69,195.17 43.49,187.04 46.55,179.66C48.6,174.71 51.6,170.24 55.37,166.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.3,223.68C49.65,218.03 45.81,210.84 44.25,203C42.69,195.16 43.49,187.04 46.55,179.66C48.59,174.74 51.57,170.29 55.3,166.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.86,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M83.86,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M68.17,178.74L65.41,175.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M144.74,138h114v102h-114z"/>
    <path
        android:pathData="M173.3,223.68C167.65,218.03 163.81,210.84 162.25,203C160.69,195.16 161.49,187.04 164.55,179.66C167.61,172.28 172.78,165.98 179.43,161.54C186.07,157.1 193.88,154.73 201.86,154.73C209.85,154.73 217.66,157.1 224.3,161.54C230.95,165.98 236.12,172.28 239.18,179.66C242.24,187.04 243.04,195.16 241.48,203C239.92,210.84 236.07,218.03 230.42,223.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.3,223.68C167.66,218.03 163.81,210.84 162.25,203C160.69,195.17 161.49,187.04 164.55,179.66C166.6,174.71 169.6,170.24 173.37,166.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.3,223.68C167.65,218.03 163.81,210.84 162.25,203C160.69,195.16 161.49,187.04 164.55,179.66C166.59,174.74 169.57,170.29 173.3,166.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.86,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M201.86,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M186.17,178.74L183.41,175.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M85.24,252h114v102h-114z"/>
    <path
        android:pathData="M113.8,337.68C108.15,332.03 104.31,324.83 102.75,317C101.19,309.17 101.99,301.04 105.05,293.66C108.11,286.28 113.28,279.98 119.93,275.54C126.57,271.1 134.38,268.73 142.36,268.73C150.35,268.73 158.16,271.1 164.8,275.54C171.45,279.98 176.62,286.28 179.68,293.66C182.74,301.04 183.54,309.17 181.98,317C180.42,324.83 176.57,332.03 170.92,337.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.81,337.68C108.16,332.03 104.31,324.84 102.75,317C101.19,309.17 101.99,301.05 105.05,293.67C107.1,288.71 110.11,284.24 113.87,280.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M113.8,337.68C108.15,332.03 104.31,324.83 102.75,317C101.19,309.17 101.99,301.04 105.05,293.66C107.09,288.74 110.07,284.29 113.8,280.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M142.36,309.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M142.36,309.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M126.67,292.73L123.91,289.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <path
      android:pathData="M272,204C275.58,205.19 278,208.55 278,212.32V240.68C278,244.45 275.58,247.81 272,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="275"
          android:startY="208"
          android:endX="275"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,235.7H282.81L281.19,242C280.96,242.88 280.17,243.5 279.25,243.5H277.52V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="282.81"
          android:startY="240.1"
          android:endX="277.52"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,209.11H279.13C280.1,209.11 280.93,209.8 281.1,210.76L282.81,220.33H277.52V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280"
          android:startY="197"
          android:endX="280"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.5,220.15L282.81,220.15V235.74H277.52L277.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280.17"
          android:startY="219.33"
          android:endX="280.17"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13,204C9.42,205.19 7,208.55 7,212.32V240.68C7,244.45 9.42,247.81 13,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10"
          android:startY="208"
          android:endX="10"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,235.7H2.19L3.81,242C4.04,242.88 4.83,243.5 5.75,243.5H7.48V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="2.19"
          android:startY="240.1"
          android:endX="7.48"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,209.11H5.87C4.9,209.11 4.07,209.8 3.9,210.76L2.19,220.33H7.48V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5"
          android:startY="197"
          android:endX="5"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.5,220.15L2.19,220.15V235.74H7.48L7.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="4.84"
          android:startY="219.33"
          android:endX="4.84"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
