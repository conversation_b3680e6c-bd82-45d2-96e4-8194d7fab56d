<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="86dp"
    android:height="127dp"
    android:viewportWidth="86"
    android:viewportHeight="127">
  <group>
    <clip-path
        android:pathData="M15.5,8h51v82.5h-51z"/>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:fillColor="#222628"/>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:strokeWidth="2"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="9.64"
            android:startY="-18.02"
            android:endX="61.18"
            android:endY="89.26"
            android:type="linear">
          <item android:offset="0" android:color="#FF434343"/>
          <item android:offset="0.26" android:color="#FF7D858B"/>
          <item android:offset="0.49" android:color="#FF42474C"/>
          <item android:offset="1" android:color="#FF3B3F40"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#66676A"/>
    <path
        android:pathData="M64.59,44.17C65.23,44.38 65.66,44.98 65.66,45.66V50.73C65.66,51.41 65.23,52.01 64.59,52.22V44.17Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="65.12"
            android:startY="44.88"
            android:endX="65.12"
            android:endY="53.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF4B4B4B"/>
          <item android:offset="0.53" android:color="#FF292929"/>
          <item android:offset="1" android:color="#FF8E8D8D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,49.84H66.52L66.23,50.97C66.19,51.13 66.05,51.24 65.88,51.24H65.57V49.84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.52"
            android:startY="50.63"
            android:endX="65.57"
            android:endY="50.63"
            android:type="linear">
          <item android:offset="0" android:color="#FF878787"/>
          <item android:offset="1" android:color="#FF737373"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,45.08H65.86C66.04,45.08 66.18,45.21 66.21,45.38L66.52,47.09H65.57V45.08Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.02"
            android:startY="42.92"
            android:endX="66.02"
            android:endY="46.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF9E9E9E"/>
          <item android:offset="1" android:color="#FF7E7E7E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,47.06L66.52,47.06V49.85H65.57L65.57,47.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.05"
            android:startY="46.91"
            android:endX="66.05"
            android:endY="49.95"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF999999"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.24,44.17C17.6,44.38 17.16,44.98 17.16,45.66V50.73C17.16,51.41 17.6,52.01 18.24,52.22V44.17Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.7"
            android:startY="44.88"
            android:endX="17.7"
            android:endY="53.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF4B4B4B"/>
          <item android:offset="0.53" android:color="#FF292929"/>
          <item android:offset="1" android:color="#FF8E8D8D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,49.84H16.3L16.59,50.97C16.63,51.13 16.78,51.24 16.94,51.24H17.25V49.84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.3"
            android:startY="50.63"
            android:endX="17.25"
            android:endY="50.63"
            android:type="linear">
          <item android:offset="0" android:color="#FF878787"/>
          <item android:offset="1" android:color="#FF737373"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,45.08H16.96C16.79,45.08 16.64,45.21 16.61,45.38L16.3,47.09H17.25V45.08Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.81"
            android:startY="42.92"
            android:endX="16.81"
            android:endY="46.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF9E9E9E"/>
          <item android:offset="1" android:color="#FF7E7E7E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,47.06L16.3,47.06V49.85H17.25L17.25,47.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.78"
            android:startY="46.91"
            android:endX="16.78"
            android:endY="49.95"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF999999"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M41,56m-13,0a13,13 0,1 1,26 0a13,13 0,1 1,-26 0"
      android:strokeWidth="2"
      android:fillColor="#ffffff"
      android:fillAlpha="0.5"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M41,56m-24,0a24,24 0,1 1,48 0a24,24 0,1 1,-48 0"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M39.73,63.87C39.77,66.82 40.15,69.76 40.86,72.63C41.47,75.19 43.1,83.99 43.17,85.67C43.37,87.85 43.96,89.97 44.92,91.93C45.61,93.82 46.13,95.77 46.49,97.75C46.6,98.24 46.75,98.72 46.94,99.19C46.61,99.16 46.28,99.18 45.96,99.27C45.6,99.53 45.3,99.86 45.09,100.25C44.78,99.85 45.3,99.74 44.68,98.75C44.06,97.75 43.42,97.96 43.13,97.61C43.32,97.22 43.42,96.8 43.44,96.37C43.45,95.94 43.36,95.52 43.2,95.12C42.99,93.99 42.61,92.91 42.06,91.91C41.6,91.32 41.05,90.8 40.43,90.39C39.1,89.61 38.19,89.71 37.64,90.39C36.7,91.55 37.72,94.36 37.72,94.36C37.66,94.66 37.63,94.96 37.63,95.26C37.06,96.37 36.75,97.58 36.71,98.82C36.65,99.54 38.11,101.55 39.38,103.48C40.65,105.41 41.8,108.39 43.24,109.29C46.22,111 49.36,112.42 52.62,113.52C55.72,114.37 58.88,115.01 62.07,115.42C64.54,116.02 66.88,117.72 68.82,118.15C74.52,119.91 84.84,103.16 84.37,102.38C83.89,101.6 84.2,101.33 83.64,101.26C83.08,101.19 83.19,100.72 82.44,99.87C82.18,99.53 81.35,99.15 81.02,98.71C80.12,97.52 76.82,88.79 74.75,84.37C74.33,83.47 73.73,82.67 72.99,82.01C72.32,81.34 72.99,82.01 71.77,81.16C70.5,80.08 69.14,79.11 67.71,78.26C67.34,78.05 66.94,77.93 66.52,77.89C66.1,77.86 65.67,77.91 65.28,78.06C64.33,76.57 63.07,75.32 61.57,74.4C61.03,74.2 60.45,74.16 59.88,74.28C59.32,74.4 58.8,74.68 58.39,75.08C57.53,74.11 56.56,73.25 55.5,72.5C54.59,71.85 52.95,72.4 51.29,73.19C50.56,73.76 49.96,74.49 49.54,75.31C49.12,76.14 48.88,77.05 48.84,77.98C48.77,79.86 47.44,72.42 46.68,70.5C46.42,69.88 46.05,67.21 45.67,65.33C45.54,64.28 45.23,63.27 44.76,62.33C44.32,61.56 43.99,61.11 43.45,61.01C41.34,60.86 40.06,61.41 39.58,63.85L39.73,63.87Z"
      android:fillColor="#ffffff"/>
</vector>
