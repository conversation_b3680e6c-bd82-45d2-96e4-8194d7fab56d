<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="92dp"
    android:height="118dp"
    android:viewportWidth="92"
    android:viewportHeight="118">
  <group>
    <clip-path
        android:pathData="M15.5,27h51v82.5h-51z"/>
    <path
        android:pathData="M21.1,29.09L61.72,29.09A1.86,1.86 0,0 1,63.58 30.96L63.58,105.4A1.86,1.86 0,0 1,61.72 107.26L21.1,107.26A1.86,1.86 0,0 1,19.23 105.4L19.23,30.96A1.86,1.86 0,0 1,21.1 29.09z"
        android:fillColor="#222628"/>
    <path
        android:pathData="M21.1,29.09L61.72,29.09A1.86,1.86 0,0 1,63.58 30.96L63.58,105.4A1.86,1.86 0,0 1,61.72 107.26L21.1,107.26A1.86,1.86 0,0 1,19.23 105.4L19.23,30.96A1.86,1.86 0,0 1,21.1 29.09z"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M21.1,29.09L61.72,29.09A1.86,1.86 0,0 1,63.58 30.96L63.58,105.4A1.86,1.86 0,0 1,61.72 107.26L21.1,107.26A1.86,1.86 0,0 1,19.23 105.4L19.23,30.96A1.86,1.86 0,0 1,21.1 29.09z"
        android:strokeWidth="2"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="9.64"
            android:startY="0.98"
            android:endX="61.18"
            android:endY="108.26"
            android:type="linear">
          <item android:offset="0" android:color="#FF434343"/>
          <item android:offset="0.26" android:color="#FF7D858B"/>
          <item android:offset="0.49" android:color="#FF42474C"/>
          <item android:offset="1" android:color="#FF3B3F40"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M21.1,29.09L61.72,29.09A1.86,1.86 0,0 1,63.58 30.96L63.58,105.4A1.86,1.86 0,0 1,61.72 107.26L21.1,107.26A1.86,1.86 0,0 1,19.23 105.4L19.23,30.96A1.86,1.86 0,0 1,21.1 29.09z"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#C3C6C8"/>
    <path
        android:pathData="M64.59,63.17C65.23,63.38 65.66,63.98 65.66,64.66V69.73C65.66,70.41 65.23,71.01 64.59,71.22V63.17Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="65.12"
            android:startY="63.88"
            android:endX="65.12"
            android:endY="72.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF4B4B4B"/>
          <item android:offset="0.53" android:color="#FF292929"/>
          <item android:offset="1" android:color="#FF8E8D8D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,68.84H66.52L66.23,69.97C66.19,70.13 66.05,70.24 65.88,70.24H65.57V68.84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.52"
            android:startY="69.63"
            android:endX="65.57"
            android:endY="69.63"
            android:type="linear">
          <item android:offset="0" android:color="#FF878787"/>
          <item android:offset="1" android:color="#FF737373"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,64.08H65.86C66.04,64.08 66.18,64.21 66.21,64.38L66.52,66.09H65.57V64.08Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.02"
            android:startY="61.92"
            android:endX="66.02"
            android:endY="65.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF9E9E9E"/>
          <item android:offset="1" android:color="#FF7E7E7E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,66.06L66.52,66.06V68.85H65.57L65.57,66.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.05"
            android:startY="65.91"
            android:endX="66.05"
            android:endY="68.95"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF999999"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.24,63.17C17.6,63.38 17.16,63.98 17.16,64.66V69.73C17.16,70.41 17.6,71.01 18.24,71.22V63.17Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.7"
            android:startY="63.88"
            android:endX="17.7"
            android:endY="72.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF4B4B4B"/>
          <item android:offset="0.53" android:color="#FF292929"/>
          <item android:offset="1" android:color="#FF8E8D8D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,68.84H16.3L16.59,69.97C16.63,70.13 16.78,70.24 16.94,70.24H17.25V68.84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.3"
            android:startY="69.63"
            android:endX="17.25"
            android:endY="69.63"
            android:type="linear">
          <item android:offset="0" android:color="#FF878787"/>
          <item android:offset="1" android:color="#FF737373"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,64.08H16.96C16.79,64.08 16.64,64.21 16.61,64.38L16.3,66.09H17.25V64.08Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.81"
            android:startY="61.92"
            android:endX="16.81"
            android:endY="65.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF9E9E9E"/>
          <item android:offset="1" android:color="#FF7E7E7E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,66.06L16.3,66.06V68.85H17.25L17.25,66.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.78"
            android:startY="65.91"
            android:endX="16.78"
            android:endY="68.95"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF999999"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M32.66,75.61C34.07,78.21 35.78,80.63 37.74,82.84C39.48,84.82 45.02,91.85 45.86,93.31C47.04,95.14 48.56,96.74 50.32,98.03C51.81,99.39 53.18,100.87 54.43,102.45C54.75,102.83 55.1,103.19 55.49,103.52C55.18,103.64 54.9,103.82 54.66,104.04C54.46,104.44 54.35,104.87 54.35,105.32C53.88,105.11 54.29,104.77 53.28,104.18C52.27,103.58 51.8,104.07 51.38,103.89C51.37,103.46 51.27,103.04 51.08,102.65C50.89,102.27 50.61,101.93 50.28,101.66C49.58,100.76 48.73,99.98 47.78,99.35C47.1,99.04 46.37,98.84 45.63,98.75C44.09,98.69 43.33,99.2 43.16,100.06C42.87,101.52 45.08,103.53 45.08,103.53C45.17,103.82 45.28,104.11 45.42,104.38C45.43,105.61 45.72,106.84 46.26,107.95C46.54,108.62 48.78,109.71 50.8,110.83C52.81,111.95 55.22,114.05 56.92,114.18C60.35,114.31 63.79,114.1 67.18,113.55C70.33,112.87 73.42,111.96 76.43,110.83C78.9,110.22 81.76,110.63 83.68,110.11C89.54,109.01 90.88,89.39 90.1,88.92C89.31,88.44 89.46,88.06 88.93,88.26C88.4,88.46 88.28,88 87.22,87.59C86.83,87.41 85.92,87.46 85.42,87.22C84.08,86.59 77.09,80.4 73.2,77.45C72.41,76.85 71.51,76.42 70.55,76.18C69.64,75.9 70.55,76.18 69.07,76C67.44,75.63 65.79,75.4 64.13,75.32C63.7,75.31 63.29,75.39 62.9,75.55C62.51,75.72 62.16,75.96 61.88,76.28C60.35,75.4 58.65,74.89 56.9,74.77C56.33,74.84 55.79,75.08 55.34,75.44C54.9,75.81 54.57,76.3 54.39,76.85C53.18,76.39 51.92,76.08 50.64,75.9C49.53,75.76 48.33,77.01 47.24,78.48C46.85,79.33 46.66,80.25 46.67,81.18C46.68,82.1 46.9,83.02 47.3,83.86C48.11,85.55 43.47,79.59 41.9,78.24C41.38,77.82 39.81,75.63 38.6,74.14C38,73.28 37.25,72.52 36.4,71.91C35.65,71.43 35.15,71.19 34.62,71.35C32.69,72.2 31.81,73.28 32.52,75.66L32.66,75.61Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M40.69,0.63L31.02,17.36L50.35,17.36L40.69,0.63ZM42.36,47.49L42.36,15.69L39.01,15.69L39.01,47.49L42.36,47.49Z"
      android:fillColor="#ffffff"/>
</vector>
