<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="285dp"
    android:height="461dp"
    android:viewportWidth="285"
    android:viewportHeight="461">
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:fillColor="#EC9BC0"/>
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:strokeWidth="3"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="70"
          android:startY="14"
          android:endX="265.5"
          android:endY="441.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFA7CF"/>
        <item android:offset="1" android:color="#FFCB86A6"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M26.5,24h114v102h-114z"/>
    <path
        android:pathData="M55.06,109.68C49.41,104.03 45.57,96.83 44.01,89C42.45,81.16 43.25,73.04 46.31,65.66C49.36,58.28 54.54,51.98 61.18,47.54C67.82,43.1 75.63,40.73 83.62,40.73C91.61,40.73 99.42,43.1 106.06,47.54C112.7,51.98 117.88,58.28 120.94,65.66C124,73.04 124.79,81.16 123.24,89C121.68,96.83 117.83,104.03 112.18,109.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.06,109.68C49.41,104.03 45.57,96.84 44.01,89C42.45,81.17 43.25,73.04 46.31,65.66C48.36,60.71 51.36,56.25 55.12,52.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.06,109.68C49.41,104.03 45.57,96.84 44.01,89C42.45,81.17 43.25,73.04 46.31,65.66C48.35,60.74 51.33,56.29 55.06,52.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.62,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M83.62,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M67.93,64.74L65.16,61.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M144.5,24h114v102h-114z"/>
    <path
        android:pathData="M173.06,109.68C167.41,104.03 163.57,96.83 162.01,89C160.45,81.16 161.25,73.04 164.31,65.66C167.36,58.28 172.54,51.98 179.18,47.54C185.82,43.1 193.63,40.73 201.62,40.73C209.61,40.73 217.42,43.1 224.06,47.54C230.7,51.98 235.88,58.28 238.94,65.66C241.99,73.04 242.79,81.16 241.24,89C239.68,96.83 235.83,104.03 230.18,109.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.06,109.68C167.41,104.03 163.57,96.84 162.01,89C160.45,81.17 161.25,73.04 164.31,65.66C166.36,60.71 169.36,56.25 173.12,52.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.06,109.68C167.41,104.03 163.57,96.84 162.01,89C160.45,81.17 161.25,73.04 164.31,65.66C166.35,60.74 169.33,56.29 173.06,52.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.62,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M201.62,81.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M185.93,64.74L183.16,61.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M26.5,138h114v102h-114z"/>
    <path
        android:pathData="M55.06,223.68C49.41,218.03 45.57,210.84 44.01,203C42.45,195.16 43.25,187.04 46.31,179.66C49.36,172.28 54.54,165.98 61.18,161.54C67.82,157.1 75.63,154.73 83.62,154.73C91.61,154.73 99.42,157.1 106.06,161.54C112.7,165.98 117.88,172.28 120.94,179.66C124,187.04 124.79,195.16 123.24,203C121.68,210.84 117.83,218.03 112.18,223.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.06,223.68C49.41,218.03 45.57,210.84 44.01,203C42.45,195.17 43.25,187.04 46.31,179.66C48.36,174.71 51.36,170.24 55.12,166.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.06,223.68C49.41,218.03 45.57,210.84 44.01,203C42.45,195.17 43.25,187.04 46.31,179.66C48.35,174.74 51.33,170.29 55.06,166.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.62,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M83.62,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M67.93,178.74L65.16,175.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M144.5,138h114v102h-114z"/>
    <path
        android:pathData="M173.06,223.68C167.41,218.03 163.57,210.84 162.01,203C160.45,195.16 161.25,187.04 164.31,179.66C167.36,172.28 172.54,165.98 179.18,161.54C185.82,157.1 193.63,154.73 201.62,154.73C209.61,154.73 217.42,157.1 224.06,161.54C230.7,165.98 235.88,172.28 238.94,179.66C241.99,187.04 242.79,195.16 241.24,203C239.68,210.84 235.83,218.03 230.18,223.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.06,223.68C167.41,218.03 163.57,210.84 162.01,203C160.45,195.17 161.25,187.04 164.31,179.66C166.36,174.71 169.36,170.24 173.12,166.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.06,223.68C167.41,218.03 163.57,210.84 162.01,203C160.45,195.17 161.25,187.04 164.31,179.66C166.35,174.74 169.33,170.29 173.06,166.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.62,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M201.62,195.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M185.93,178.74L183.16,175.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <path
      android:pathData="M272,204C275.58,205.19 278,208.55 278,212.32V240.68C278,244.45 275.58,247.81 272,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="275"
          android:startY="208"
          android:endX="275"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,235.7H282.81L281.19,242C280.96,242.88 280.17,243.5 279.25,243.5H277.52V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="282.81"
          android:startY="240.1"
          android:endX="277.52"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,209.11H279.13C280.1,209.11 280.93,209.8 281.1,210.76L282.81,220.33H277.52V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280"
          android:startY="197"
          android:endX="280"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.5,220.15L282.81,220.15V235.74H277.52L277.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280.17"
          android:startY="219.33"
          android:endX="280.17"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13,204C9.42,205.19 7,208.55 7,212.32V240.68C7,244.45 9.42,247.81 13,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10"
          android:startY="208"
          android:endX="10"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,235.7H2.19L3.81,242C4.04,242.88 4.83,243.5 5.75,243.5H7.48V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="2.19"
          android:startY="240.1"
          android:endX="7.48"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,209.11H5.87C4.9,209.11 4.07,209.8 3.9,210.76L2.19,220.33H7.48V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5"
          android:startY="197"
          android:endX="5"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.5,220.15L2.19,220.15V235.74H7.48L7.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="4.84"
          android:startY="219.33"
          android:endX="4.84"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
