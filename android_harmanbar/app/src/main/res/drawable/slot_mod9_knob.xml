<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="285dp"
    android:height="461dp"
    android:viewportWidth="285"
    android:viewportHeight="461">
  <path
      android:pathData="M29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24C14.5,15.99 20.99,9.5 29,9.5Z"
      android:fillColor="#3E83E2"/>
  <path
      android:pathData="M29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24C14.5,15.99 20.99,9.5 29,9.5Z"
      android:strokeWidth="3"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="191"
          android:startY="45.5"
          android:endX="265.5"
          android:endY="441.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFA3CAFF"/>
        <item android:offset="1" android:color="#FF608CC7"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M26.5,95h114v102h-114z"/>
    <path
        android:pathData="M55.06,180.68C49.41,175.03 45.57,167.84 44.01,160C42.45,152.16 43.25,144.04 46.31,136.66C49.36,129.28 54.54,122.97 61.18,118.54C67.82,114.1 75.63,111.73 83.62,111.73C91.61,111.73 99.42,114.1 106.06,118.54C112.7,122.97 117.88,129.28 120.94,136.66C124,144.04 124.79,152.16 123.24,160C121.68,167.84 117.83,175.03 112.18,180.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.06,180.68C49.41,175.03 45.57,167.84 44.01,160C42.45,152.17 43.25,144.04 46.31,136.66C48.36,131.71 51.36,127.25 55.12,123.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M55.06,180.68C49.41,175.03 45.57,167.84 44.01,160C42.45,152.17 43.25,144.04 46.31,136.66C48.35,131.74 51.33,127.29 55.06,123.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.62,152.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M83.62,152.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M67.93,135.74L65.16,132.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M144.5,95h114v102h-114z"/>
    <path
        android:pathData="M173.06,180.68C167.41,175.03 163.57,167.84 162.01,160C160.45,152.16 161.25,144.04 164.31,136.66C167.36,129.28 172.54,122.97 179.18,118.54C185.82,114.1 193.63,111.73 201.62,111.73C209.61,111.73 217.42,114.1 224.06,118.54C230.7,122.97 235.88,129.28 238.94,136.66C241.99,144.04 242.79,152.16 241.24,160C239.68,167.84 235.83,175.03 230.18,180.68"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#000000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.06,180.68C167.41,175.03 163.57,167.84 162.01,160C160.45,152.17 161.25,144.04 164.31,136.66C166.36,131.71 169.36,127.25 173.12,123.5"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M173.06,180.68C167.41,175.03 163.57,167.84 162.01,160C160.45,152.17 161.25,144.04 164.31,136.66C166.35,131.74 169.33,127.29 173.06,123.56"
        android:strokeWidth="4.14257"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M201.62,152.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:fillColor="#1E1E1E"/>
    <path
        android:pathData="M201.62,152.12m-28.65,0a28.65,28.65 0,1 1,57.31 0a28.65,28.65 0,1 1,-57.31 0"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M185.93,135.74L183.16,132.97"
        android:strokeWidth="2.76172"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
  <path
      android:pathData="M272,204C275.58,205.19 278,208.55 278,212.32V240.68C278,244.45 275.58,247.81 272,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="275"
          android:startY="208"
          android:endX="275"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,235.7H282.81L281.19,242C280.96,242.88 280.17,243.5 279.25,243.5H277.52V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="282.81"
          android:startY="240.1"
          android:endX="277.52"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,209.11H279.13C280.1,209.11 280.93,209.8 281.1,210.76L282.81,220.33H277.52V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280"
          android:startY="197"
          android:endX="280"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.5,220.15L282.81,220.15V235.74H277.52L277.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280.17"
          android:startY="219.33"
          android:endX="280.17"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13,204C9.42,205.19 7,208.55 7,212.32V240.68C7,244.45 9.42,247.81 13,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10"
          android:startY="208"
          android:endX="10"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,235.7H2.19L3.81,242C4.04,242.88 4.83,243.5 5.75,243.5H7.48V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="2.19"
          android:startY="240.1"
          android:endX="7.48"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,209.11H5.87C4.9,209.11 4.07,209.8 3.9,210.76L2.19,220.33H7.48V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5"
          android:startY="197"
          android:endX="5"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.5,220.15L2.19,220.15V235.74H7.48L7.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="4.84"
          android:startY="219.33"
          android:endX="4.84"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
