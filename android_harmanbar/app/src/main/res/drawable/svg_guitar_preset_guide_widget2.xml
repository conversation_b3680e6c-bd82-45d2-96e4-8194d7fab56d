<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="92dp"
    android:height="119dp"
    android:viewportWidth="92"
    android:viewportHeight="119">
  <group>
    <clip-path
        android:pathData="M15.5,8h51v82.5h-51z"/>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:fillColor="#222628"/>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:strokeWidth="2"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="9.64"
            android:startY="-18.02"
            android:endX="61.18"
            android:endY="89.26"
            android:type="linear">
          <item android:offset="0" android:color="#FF434343"/>
          <item android:offset="0.26" android:color="#FF7D858B"/>
          <item android:offset="0.49" android:color="#FF42474C"/>
          <item android:offset="1" android:color="#FF3B3F40"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M21.1,10.09L61.72,10.09A1.86,1.86 0,0 1,63.58 11.96L63.58,86.4A1.86,1.86 0,0 1,61.72 88.26L21.1,88.26A1.86,1.86 0,0 1,19.23 86.4L19.23,11.96A1.86,1.86 0,0 1,21.1 10.09z"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#C3C6C8"/>
    <path
        android:pathData="M64.59,44.17C65.23,44.38 65.66,44.98 65.66,45.66V50.73C65.66,51.41 65.23,52.01 64.59,52.22V44.17Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="65.12"
            android:startY="44.88"
            android:endX="65.12"
            android:endY="53.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF4B4B4B"/>
          <item android:offset="0.53" android:color="#FF292929"/>
          <item android:offset="1" android:color="#FF8E8D8D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,49.84H66.52L66.23,50.97C66.19,51.13 66.05,51.24 65.88,51.24H65.57V49.84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.52"
            android:startY="50.63"
            android:endX="65.57"
            android:endY="50.63"
            android:type="linear">
          <item android:offset="0" android:color="#FF878787"/>
          <item android:offset="1" android:color="#FF737373"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,45.08H65.86C66.04,45.08 66.18,45.21 66.21,45.38L66.52,47.09H65.57V45.08Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.02"
            android:startY="42.92"
            android:endX="66.02"
            android:endY="46.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF9E9E9E"/>
          <item android:offset="1" android:color="#FF7E7E7E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M65.57,47.06L66.52,47.06V49.85H65.57L65.57,47.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66.05"
            android:startY="46.91"
            android:endX="66.05"
            android:endY="49.95"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF999999"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.24,44.17C17.6,44.38 17.16,44.98 17.16,45.66V50.73C17.16,51.41 17.6,52.01 18.24,52.22V44.17Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.7"
            android:startY="44.88"
            android:endX="17.7"
            android:endY="53.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF4B4B4B"/>
          <item android:offset="0.53" android:color="#FF292929"/>
          <item android:offset="1" android:color="#FF8E8D8D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,49.84H16.3L16.59,50.97C16.63,51.13 16.78,51.24 16.94,51.24H17.25V49.84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.3"
            android:startY="50.63"
            android:endX="17.25"
            android:endY="50.63"
            android:type="linear">
          <item android:offset="0" android:color="#FF878787"/>
          <item android:offset="1" android:color="#FF737373"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,45.08H16.96C16.79,45.08 16.64,45.21 16.61,45.38L16.3,47.09H17.25V45.08Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.81"
            android:startY="42.92"
            android:endX="16.81"
            android:endY="46.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF9E9E9E"/>
          <item android:offset="1" android:color="#FF7E7E7E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.25,47.06L16.3,47.06V49.85H17.25L17.25,47.06Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.78"
            android:startY="46.91"
            android:endX="16.78"
            android:endY="49.95"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF999999"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M32.66,58.1C34.07,55.51 35.78,53.08 37.74,50.88C39.48,48.89 45.02,41.86 45.86,40.41C47.04,38.57 48.56,36.97 50.32,35.69C51.81,34.33 53.18,32.85 54.43,31.26C54.75,30.88 55.1,30.52 55.49,30.2C55.18,30.07 54.9,29.9 54.66,29.67C54.46,29.28 54.35,28.84 54.35,28.4C53.88,28.61 54.29,28.94 53.28,29.54C52.27,30.13 51.8,29.64 51.38,29.82C51.37,30.25 51.27,30.67 51.08,31.06C50.89,31.45 50.61,31.79 50.28,32.06C49.58,32.96 48.73,33.74 47.78,34.37C47.1,34.68 46.37,34.88 45.63,34.96C44.09,35.03 43.33,34.51 43.16,33.65C42.87,32.19 45.08,30.18 45.08,30.18C45.17,29.89 45.28,29.61 45.42,29.34C45.43,28.1 45.72,26.88 46.26,25.76C46.54,25.09 48.78,24 50.8,22.88C52.81,21.76 55.22,19.66 56.92,19.54C60.35,19.41 63.79,19.61 67.18,20.16C70.33,20.85 73.42,21.76 76.43,22.88C78.9,23.49 81.76,23.09 83.68,23.61C89.54,24.71 90.88,44.33 90.1,44.8C89.31,45.27 89.46,45.65 88.93,45.45C88.4,45.25 88.28,45.72 87.22,46.12C86.83,46.3 85.92,46.25 85.42,46.49C84.08,47.13 77.09,53.32 73.2,56.26C72.41,56.86 71.51,57.29 70.55,57.53C69.64,57.81 70.55,57.53 69.07,57.72C67.44,58.08 65.79,58.31 64.13,58.39C63.7,58.41 63.29,58.33 62.9,58.16C62.51,58 62.16,57.75 61.88,57.44C60.35,58.31 58.65,58.83 56.9,58.95C56.33,58.87 55.79,58.64 55.34,58.27C54.9,57.9 54.57,57.42 54.39,56.87C53.18,57.32 51.92,57.64 50.64,57.81C49.53,57.95 48.33,56.7 47.24,55.24C46.85,54.39 46.66,53.47 46.67,52.54C46.68,51.61 46.9,50.69 47.3,49.86C48.11,48.16 43.47,54.13 41.9,55.47C41.38,55.9 39.81,58.08 38.6,59.57C38,60.44 37.25,61.19 36.4,61.81C35.65,62.28 35.15,62.53 34.62,62.36C32.69,61.51 31.81,60.43 32.52,58.05L32.66,58.1Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M40.69,118.49L31.02,101.75L50.35,101.75L40.69,118.49ZM42.36,71.63L42.36,103.43L39.01,103.43L39.01,71.63L42.36,71.63Z"
      android:fillColor="#ffffff"/>
</vector>
