<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="285dp"
    android:height="461dp"
    android:viewportWidth="285"
    android:viewportHeight="461">
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:fillColor="#948F8C"/>
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:fillAlpha="0.2"/>
  <path
      android:pathData="M14.5,24C14.5,15.99 20.99,9.5 29,9.5H256C264.01,9.5 270.5,15.99 270.5,24V440C270.5,448.01 264.01,454.5 256,454.5H29C20.99,454.5 14.5,448.01 14.5,440V24Z"
      android:strokeWidth="3"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="-75"
          android:startY="35.5"
          android:endX="357"
          android:endY="500.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFCDCDCD"/>
        <item android:offset="1" android:color="#FF7C7C7C"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M272,204C275.58,205.19 278,208.55 278,212.32V240.68C278,244.45 275.58,247.81 272,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="275"
          android:startY="208"
          android:endX="275"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,235.7H282.81L281.19,242C280.96,242.88 280.17,243.5 279.25,243.5H277.52V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="282.81"
          android:startY="240.1"
          android:endX="277.52"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.52,209.11H279.13C280.1,209.11 280.93,209.8 281.1,210.76L282.81,220.33H277.52V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280"
          android:startY="197"
          android:endX="280"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M277.5,220.15L282.81,220.15V235.74H277.52L277.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="280.17"
          android:startY="219.33"
          android:endX="280.17"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13,204C9.42,205.19 7,208.55 7,212.32V240.68C7,244.45 9.42,247.81 13,249V204Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10"
          android:startY="208"
          android:endX="10"
          android:endY="255.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF4B4B4B"/>
        <item android:offset="0.53" android:color="#FF292929"/>
        <item android:offset="1" android:color="#FF8E8D8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,235.7H2.19L3.81,242C4.04,242.88 4.83,243.5 5.75,243.5H7.48V235.7Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="2.19"
          android:startY="240.1"
          android:endX="7.48"
          android:endY="240.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF878787"/>
        <item android:offset="1" android:color="#FF737373"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.48,209.11H5.87C4.9,209.11 4.07,209.8 3.9,210.76L2.19,220.33H7.48V209.11Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5"
          android:startY="197"
          android:endX="5"
          android:endY="216"
          android:type="linear">
        <item android:offset="0" android:color="#FF9E9E9E"/>
        <item android:offset="1" android:color="#FF7E7E7E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.5,220.15L2.19,220.15V235.74H7.48L7.5,220.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="4.84"
          android:startY="219.33"
          android:endX="4.84"
          android:endY="236.28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF999999"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
