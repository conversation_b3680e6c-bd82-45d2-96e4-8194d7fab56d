<?xml version="1.0" encoding="utf-8"?><!--<merge xmlns:android="http://schemas.android.com/apk/res/android"-->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:layout_gravity="center"
    tools:layout_height="wrap_content"
    tools:layout_width="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <ImageView
        android:id="@+id/ivMaterial"
        android:layout_width="285dp"
        android:layout_height="461dp"
        android:src="@drawable/slot_inactive1"
        app:layout_constraintStart_toEndOf="@+id/ivWidget1" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="24dp"
        app:layout_constraintTop_toTopOf="@id/ivMaterial" />

    <com.harman.partyband.widget.CircularRingProgressView
        android:id="@+id/crpv1"
        style="@style/guitarPresetSlotDashboard"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/crpv2"
        app:layout_constraintStart_toStartOf="@id/ivMaterial"
        app:layout_constraintTop_toTopOf="@id/gl1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvName1"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-8dp"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/crpv1"
        app:layout_constraintStart_toStartOf="@+id/crpv1"
        app:layout_constraintTop_toBottomOf="@+id/crpv1"
        tools:text="Treshold"
        tools:visibility="visible" />


    <com.harman.partyband.widget.CircularRingProgressView
        android:id="@+id/crpv2"
        style="@style/guitarPresetSlotDashboard"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/ivMaterial"
        app:layout_constraintStart_toEndOf="@+id/crpv1"
        app:layout_constraintTop_toTopOf="@id/crpv1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvName2"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-8dp"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/crpv2"
        app:layout_constraintStart_toStartOf="@+id/crpv2"
        app:layout_constraintTop_toBottomOf="@+id/crpv2"
        tools:text="Treshold"
        tools:visibility="visible" />

    <com.harman.partyband.widget.CircularRingProgressView
        android:id="@+id/crpv3"
        style="@style/guitarPresetSlotDashboard"
        android:layout_marginTop="40dp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/crpv4"
        app:layout_constraintStart_toStartOf="@id/ivMaterial"
        app:layout_constraintTop_toBottomOf="@+id/crpv1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvName3"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-8dp"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/crpv3"
        app:layout_constraintStart_toStartOf="@+id/crpv3"
        app:layout_constraintTop_toBottomOf="@+id/crpv3"
        tools:text="Treshold"
        tools:visibility="visible" />

    <com.harman.partyband.widget.CircularRingProgressView
        android:id="@+id/crpv4"
        style="@style/guitarPresetSlotDashboard"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/ivMaterial"
        app:layout_constraintStart_toEndOf="@+id/crpv3"
        app:layout_constraintTop_toTopOf="@id/crpv3"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvName4"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-8dp"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/crpv4"
        app:layout_constraintStart_toStartOf="@+id/crpv4"
        app:layout_constraintTop_toBottomOf="@+id/crpv4"
        tools:text="Treshold"
        tools:visibility="visible" />

    <com.harman.partyband.widget.CircularRingProgressView
        android:id="@+id/crpv5"
        style="@style/guitarPresetSlotDashboard"
        android:layout_marginTop="40dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/ivMaterial"
        app:layout_constraintStart_toStartOf="@+id/ivMaterial"
        app:layout_constraintTop_toBottomOf="@id/crpv3"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvName5"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-8dp"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/crpv5"
        app:layout_constraintStart_toStartOf="@+id/crpv5"
        app:layout_constraintTop_toBottomOf="@+id/crpv5"
        tools:text="Treshold"
        tools:visibility="visible" />
</merge>