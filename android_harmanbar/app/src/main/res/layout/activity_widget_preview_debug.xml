<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black"
    tools:context="com.harman.partyband.widget.WidgetPreviewActivity">

    <Space
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="@color/red"
        />
    <Button
        android:id="@+id/bt_to_launch"
        android:text="to splash ac"
        android:layout_width="200dp"
        android:layout_height="50dp"/>
    <Button
        android:id="@+id/bt_to_guitarlooper"
        android:text="to guitar looper"
        android:layout_width="200dp"
        android:layout_height="50dp"/>
    <Button
        android:id="@+id/bt_to_output"
        android:text="to out_put"
        android:layout_width="200dp"
        android:layout_height="50dp"/>
    <TextView
        android:id="@+id/tv_center_horizontal_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:textStyle="bold" />

    <com.harman.partyband.widget.HorizontalScaleView
        android:id="@+id/view_horizontal_scale_view_1"
        android:layout_width="375dp"
        android:layout_height="54dp"
        android:layout_gravity="center_horizontal"
        app:hsv_debug="false"
        app:hsv_max="10"
        app:hsv_value="0" />
    <Space
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/red"
        />
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black">

        <TextView
            android:id="@+id/tv_vertical_scale_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:textColor="@color/white"
            android:textSize="30sp"
            android:textStyle="bold" />

        <com.harman.partyband.widget.VerticalScaleView
            android:id="@+id/view_vertical_scale_view_1"
            android:layout_width="215dp"
            android:layout_height="348dp"
            android:layout_below="@+id/tv_vertical_scale_view"
            app:vsv_debug="false"
            app:vsv_max="120"
            app:vsv_value="10"
            android:layout_centerInParent="true" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/but_vertical_scale_1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="-" />

        <Space
            android:layout_width="10dp"
            android:layout_height="match_parent" />

        <Button
            android:id="@+id/but_vertical_scale_2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="+" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#dd9395"
            android:orientation="vertical">

            <Space
                android:layout_width="match_parent"
                android:layout_height="40dp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="10dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FF1A1A1A"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="400dp">

                    <com.harman.partyband.widget.CircleSliderView
                        android:id="@+id/view_widget_circle_slider"
                        android:layout_width="270dp"
                        android:layout_height="270dp"
                        android:layout_centerInParent="true"
                        app:csv_debug="false"
                        app:csv_value="120" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/but_circle_slider1"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="-" />

                    <Space
                        android:layout_width="10dp"
                        android:layout_height="match_parent" />

                    <Button
                        android:id="@+id/but_circle_slider2"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="+" />
                </LinearLayout>

            </LinearLayout>


            <RelativeLayout

                android:layout_width="match_parent"
                android:layout_height="200dp">

                <com.harman.partyband.widget.CircularRingProgressView
                    android:id="@+id/view_widget_circle_progress"
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:layout_centerInParent="true"
                    android:background="#00FFFFFF"
                    app:crpv_debug="false"
                    app:crpv_max="20"
                    app:crpv_middle_circle_border_width="8dp"
                    app:crpv_ring_width="10dp"
                    app:crpv_value="18" />

                <TextView
                    android:id="@+id/tv_show_value_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center_horizontal"
                    android:text="0"
                    android:textColor="@color/white"
                    android:textSize="22sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="10dp" />

            <com.harman.partyband.widget.CircularRingProgressView
                android:id="@+id/view_widget_circle_progress2"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="#00FFFFFF"
                app:crpv_max="100"
                app:crpv_value="50" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/but_1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="-"
                    android:textColor="@color/black" />

                <Space
                    android:layout_width="10dp"
                    android:layout_height="match_parent" />

                <Button
                    android:id="@+id/but_2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="+"
                    android:textColor="@color/black" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_edit"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:inputType="number"
                    android:text="90" />

                <Space
                    android:layout_width="10dp"
                    android:layout_height="match_parent" />

                <Button
                    android:id="@+id/but_ok"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="Set" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>


</LinearLayout>