<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_opacity_overlay"
        android:clickable="true"
        android:focusable="true">

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/circularProgress"
            style="@style/Widget.Material3.CircularProgressIndicator.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            app:indicatorColor="@color/white"
            app:indicatorInset="0dp"
            app:indicatorSize="32dp"
            app:layout_constraintBottom_toTopOf="@+id/textView"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/textView"
            style="@style/Text_Body_Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/fg_primary"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/circularProgress"
            tools:text="Loading" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>