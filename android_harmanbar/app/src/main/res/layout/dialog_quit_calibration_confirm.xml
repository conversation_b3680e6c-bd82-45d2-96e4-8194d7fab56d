<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.calibration.QuitCalibrationConfirmDialog" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:paddingHorizontal="@dimen/dimen_24dp"
        android:paddingTop="@dimen/dimen_20dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text_Title_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:text="@string/Cancel_Calibration_To_Calibrate_later_find_the_Calibration_option_from_product_control_page"
            android:textColor="@color/fg_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_stay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_48dp"
            android:layout_marginEnd="@dimen/dimen_60dp"
            android:gravity="center"
            android:onClick="@{() -> dialog.onCancelBtnClick()}"
            app:btn_text="@string/harmanbar_jbl_Cancel"
            app:btn_type="btn_warning"
            app:layout_constraintBottom_toTopOf="@+id/btn_exit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_max="@dimen/dimen_255dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_exit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_60dp"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:gravity="center"
            android:onClick="@{() -> dialog.onStayBtnClick()}"
            app:btn_text="@string/harmanbar_jbl_STAY"
            app:btn_type="btn_text_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_max="@dimen/dimen_255dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_stay" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>