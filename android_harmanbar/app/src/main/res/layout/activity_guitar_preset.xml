<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp2"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llCurPreset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="20dp"
        android:gravity="center_vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar">

        <ImageView
            android:id="@+id/ivPresetOnProduct"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_speaker" />

        <TextView
            android:id="@+id/tvCurPreset"
            style="@style/Text_Title_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/fg_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="01 Silver Ship" />

        <ImageView
            android:id="@+id/ivAllPreset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_down" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="24dp"
        android:src="@drawable/icon_loading"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/llCurPreset"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/llCurPreset"
        tools:visibility="visible" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clGuide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_opacity_overlay"
        android:visibility="invisible"
        tools:visibility="visible">


        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vpGuide"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.harman.hkone.HMPagerIndicator
            android:id="@+id/indicatorGuide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="88dp"
            app:indicatorSize="8dp"
            app:indicatorSpace="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:selectedColor="@color/fg_primary"
            app:totalCount="3"
            app:unselectedColor="@color/bg_opacity_30" />

        <TextView
            android:id="@+id/tvGuideGotIt"
            style="@style/Text_Button"
            android:layout_width="255dp"
            android:layout_height="48dp"
            android:background="@drawable/radius_round_fg_primary"
            android:gravity="center"
            android:text="@string/got_it"
            android:textColor="@color/fg_inverse"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/indicatorGuide" />

        <include
            android:id="@+id/appbarGuide"
            layout="@layout/view_appbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>