<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.harman.radio.bean.ScanStationListBean" />

        <variable
            name="listener"
            type="com.harman.radio.ScanStationAdapter.ScanStationClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_surface"
        android:paddingHorizontal="@dimen/dimen_20dp"
        android:paddingVertical="@dimen/dimen_16dp"
        android:onClick="@{() -> listener.onClick(bean)}"
        app:setBackgroundResource="@{bean.backgroundColor}">

        <TextView
            style="@style/Text_Body_Medium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{bean.stationName}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:setTextColorResource="@{bean.textColor}"
            tools:text="89.9"
            tools:textColor="@color/fg_primary" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>