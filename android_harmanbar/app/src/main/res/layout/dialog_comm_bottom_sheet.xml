<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/radius_large_top_bg_card"
    android:orientation="vertical"
    tools:layout_gravity="bottom">

    <TextView
        android:id="@+id/tvMain"
        style="@style/Text_Title_1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="20dp"
        android:textColor="@color/fg_primary"
        tools:text="@string/your_partystage_settings_are_not_complete_yet" />

    <TextView
        android:id="@+id/action1"
        style="@style/Text_Button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_marginHorizontal="@dimen/dimen_60dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:background="@drawable/bg_white_radius_24"
        android:gravity="center"
        android:textAllCaps="true"
        android:textColor="@color/fg_inverse"
        tools:text="@string/resume" />

    <TextView
        android:id="@+id/action2"
        style="@style/Text_Button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_marginHorizontal="@dimen/dimen_60dp"
        android:layout_marginBottom="@dimen/dimen_48dp"
        android:gravity="center"
        android:textAllCaps="true"
        android:textColor="@color/red_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/action1"
        tools:text="@string/harmanbar_jbl_EXIT" />

</LinearLayout>