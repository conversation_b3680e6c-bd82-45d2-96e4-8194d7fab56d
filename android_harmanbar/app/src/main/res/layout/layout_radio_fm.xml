<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.harman.radio.RadioActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/sp_player">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_header"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onBackBtnClick()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/ic_arrow_down"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_radio_source"
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fm"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_switch_radio_source"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onSwitchRadioSourceClick()}"
                android:src="@drawable/ic_radio_switch"
                app:isVisible="@{activity.ableSwitchRadioSource}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_radio_source"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_album"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dimen_18dp"
            android:layout_marginBottom="@dimen/dimen_20dp"
            android:background="@drawable/radius_medium_fixed_white_10"
            app:layout_constraintBottom_toTopOf="@id/layout_frequency"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_header">

            <ImageView
                android:id="@+id/ic_input_source"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_input_source_fm_large"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_frequency"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_40dp"
            android:minHeight="@dimen/dimen_60dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/layout_player_control">

            <TextView
                android:id="@+id/tv_scanning"
                style="@style/Text_Heading_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/Scanning"
                android:textColor="@color/fixed_white"
                app:isVisible="@{activity.isScanning}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />

            <ImageView
                android:id="@+id/iv_frequency"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onFrequencyClick()}"
                android:src="@drawable/ic_frequency_fixed_white"
                app:decoEnable="@{!activity.isScanning}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/layout_frequency_handy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:isVisible="@{!activity.isScanning}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_frequency_handy_minus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:onClick="@{() -> activity.onFreqHandyMinusClick()}"
                    android:src="@drawable/ic_frequency_handy_minus" />

                <TextView
                    android:gravity="center"
                    android:id="@+id/tv_frequency_number"
                    style="@style/Text_Display_1"
                    android:layout_width="wrap_content"
                    android:minWidth="@dimen/width_140"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/dimen_8dp"
                    android:text="@{activity.frequencyNum}"
                    android:textColor="@color/fixed_white"
                    tools:text="87.50" />

                <ImageView
                    android:id="@+id/iv_frequency_handy_plus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:onClick="@{() -> activity.onFreqHandyPlusClick()}"
                    android:src="@drawable/ic_frequency_handy_plus" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_player_control"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:layout_marginBottom="@dimen/dimen_36dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/layout_volume">

            <ImageView
                android:id="@+id/ic_play_pause"
                android:layout_width="@dimen/dimen_64dp"
                android:layout_height="@dimen/dimen_64dp"
                android:onClick="@{() -> activity.onPlayStopClick()}"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:setImgRes="@{activity.playStopImgRes}"
                tools:src="@drawable/ic_round_pause_2" />

            <ImageView
                android:id="@+id/ic_previous"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_24dp"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onPreviousClick()}"
                android:padding="@dimen/dimen_8dp"
                android:src="@drawable/btn_ic_skip_previous"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/ic_play_pause"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ic_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_24dp"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onNextClick()}"
                android:padding="@dimen/dimen_8dp"
                android:src="@drawable/btn_ic_skip_next"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ic_play_pause"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_favourite_unselected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onFavouriteClick(false)}"
                android:src="@drawable/ic_favourite_fg_primary"
                app:decoEnable="@{activity.ableControl}"
                app:isVisible="@{!activity.isPreset}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_favourite_selected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onFavouriteClick(true)}"
                app:isVisible="@{activity.ableControl &amp;&amp; activity.isPreset}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_favourite_orange_2"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="@{activity.presetIndex}"
                    android:textColor="@color/fixed_white"
                    android:textSize="9.19dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="1" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/iv_station_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onStationListClick()}"
                android:src="@drawable/ic_station_list_fixed_white"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_volume"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_36dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_device_name">

            <ImageView
                android:id="@+id/ic_volume"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_player_volume" />

                <SeekBar
                    android:id="@+id/sb_volume"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:layout_marginStart="@dimen/dimen_12dp"
                    android:max="100"
                    android:maxHeight="@dimen/dimen_4dp"
                    android:minHeight="@dimen/dimen_4dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:paddingStart="0dp"
                    android:paddingEnd="0dp"
                    android:progressDrawable="@drawable/progressbar_fixed_white_fixed_white_10"
                    android:thumb="@drawable/thumb_radius_12_fixed_white"
                    android:thumbOffset="0dp"
                    android:splitTrack="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ic_volume"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:progress="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_device_name"
            style="@style/Text_Body_Strong"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dimen_26dp"
            android:layout_marginBottom="@dimen/dimen_36dp"
            android:drawablePadding="@dimen/dimen_4dp"
            android:text="@{activity.deviceName}"
            android:textColor="@color/fixed_white_50"
            app:drawableStartCompat="@drawable/ic_device_small_fixed_white_50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Spk_Name" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>