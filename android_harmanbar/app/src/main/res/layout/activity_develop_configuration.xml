<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="activity"
            type="com.wifiaudio.app.debug.DevelopConfigurationActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/titleLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="62dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Internal Settings"
                android:textColor="@color/fg_primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_marginEnd="20dp"
                android:src="@mipmap/close_back"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/tv_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleLayout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp">

                <CheckBox
                    android:id="@+id/cb_show_customer_service"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/selector_check_uncheck"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/titleLayout" />

                <TextView
                    android:id="@+id/tv_show_customer_service"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="Show Customer Service"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/cb_show_customer_service"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cb_show_customer_service"
                    app:layout_constraintTop_toTopOf="@+id/cb_show_customer_service" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="The function for upload device log. if enable will be show customer service item in device product settings."
                    android:textColor="@color/fg_secondary"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tv_show_customer_service"
                    app:layout_constraintTop_toBottomOf="@+id/tv_show_customer_service" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="12dp">

                <CheckBox
                    android:id="@+id/cb_show_close_all_oobe_popups"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/selector_check_uncheck"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/titleLayout" />

                <TextView
                    android:id="@+id/tv_show_cb_show_oobe_popups"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="Show Close All for OOBE popups"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/cb_show_close_all_oobe_popups"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cb_show_close_all_oobe_popups"
                    app:layout_constraintTop_toTopOf="@+id/cb_show_close_all_oobe_popups" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="The function for upload device log. if enable will be show customer service item in device product settings."
                    android:textColor="@color/cancel_8b8b8b"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tv_show_cb_show_oobe_popups"
                    app:layout_constraintTop_toBottomOf="@+id/tv_show_cb_show_oobe_popups" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/debug_activity_entrance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="5dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="Debug entrance for 3 in 1"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="Circle upgrade"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/cb_allow_circle_upgrade"
                    app:layout_constraintTop_toTopOf="parent" />

                <CheckBox
                    android:id="@+id/cb_allow_circle_upgrade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/selector_check_uncheck"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/debug_hybrid_dashboard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="5dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="Hybrid Dashboard"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="5dp">

                <TextView
                    android:id="@+id/tv_hybrid_dashboard_custom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="IP:Host"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/et_ip_and_port"
                    app:layout_constraintStart_toStartOf="parent" />

                <EditText
                    android:id="@+id/et_ip_and_port"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_10dp"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:hint="*************:8080"
                    android:maxLines="1"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:textColor="@color/fg_primary"
                    android:textColorHint="@color/bg_on_card"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tv_open"
                    app:layout_constraintStart_toEndOf="@id/tv_hybrid_dashboard_custom" />

                <TextView
                    android:id="@+id/tv_open"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_10dp"
                    android:layout_marginEnd="@dimen/dimen_10dp"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="Open"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/cb_allow_circle_upgrade"
                    app:layout_constraintStart_toEndOf="@id/et_ip_and_port"
                    app:layout_constraintTop_toTopOf="parent" />

                <CheckBox
                    android:id="@+id/cb_remote_dashboard_url"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/selector_check_uncheck"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_open"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/debug_one_cloud_analytics"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="5dp"
                android:onClick="@{() -> activity.onOneCloudAnalyticsLogClick()}">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="One Cloud Analytics Log"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="start|center_vertical"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="Force Report"
                    android:textColor="@color/fg_primary"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/cb_force_report"
                    app:layout_constraintTop_toTopOf="parent" />

                <CheckBox
                    android:id="@+id/cb_force_report"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/selector_check_uncheck"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleLayout" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>