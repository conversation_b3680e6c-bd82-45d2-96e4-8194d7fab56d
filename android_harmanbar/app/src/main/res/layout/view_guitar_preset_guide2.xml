<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_surface">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/svg_guitar_preset_guide_widget1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_marginStart="60dp"
        tools:layout_marginTop="33dp" />

    <TextView
        style="@style/Text_Heading_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="@string/bypass_pedal"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="@+id/iv"
        app:layout_constraintStart_toEndOf="@+id/iv"
        app:layout_constraintTop_toTopOf="@+id/iv" />

    <ImageView
        android:id="@+id/iv2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/svg_guitar_preset_guide_widget2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:layout_marginBottom="177dp"
        tools:layout_marginStart="60dp" />

    <TextView
        style="@style/Text_Heading_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="@string/cancel_bypass"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="@+id/iv2"
        app:layout_constraintStart_toEndOf="@+id/iv2"
        app:layout_constraintTop_toTopOf="@+id/iv2" />
</androidx.constraintlayout.widget.ConstraintLayout>