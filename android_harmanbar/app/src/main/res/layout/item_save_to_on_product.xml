<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    tools:background="@color/bg_card">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvName"
            style="@style/Text_Body_Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="4dp"
            android:maxLines="1"
            android:textColor="@color/fg_primary"
            tools:drawableStart="@drawable/ic_add"
            tools:text="@string/guitar_33" />

        <TextView
            android:id="@+id/tvName2"
            style="@style/Text_Body_Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:drawablePadding="4dp"
            android:maxLines="1"
            android:textColor="@color/fg_activate"
            app:drawableStartCompat="@drawable/icon_copy"
            app:drawableTint="@color/fg_activate"
            tools:text="@string/guitar_33" />
    </LinearLayout>

    <ImageView
        android:id="@+id/ivChecker"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/svg_icon_security_unselected" />
</LinearLayout>