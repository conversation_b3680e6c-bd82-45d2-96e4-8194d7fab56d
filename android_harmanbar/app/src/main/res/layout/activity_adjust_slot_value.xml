<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface">

    <TextView
        android:id="@+id/tvValueName"
        style="@style/Text_Heading_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toTopOf="@+id/tvValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Thresh" />

    <TextView
        android:id="@+id/tvValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/fg_primary"
        android:textSize="84sp"
        app:layout_constraintBottom_toTopOf="@+id/vsv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvValueName"
        tools:text="18.2" />

    <com.harman.partyband.widget.VerticalScaleView
        android:id="@+id/vsv"
        android:layout_width="215dp"
        android:layout_height="280dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvValue"
        app:vsv_debug="false" />


</androidx.constraintlayout.widget.ConstraintLayout>
