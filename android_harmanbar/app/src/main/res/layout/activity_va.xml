<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


        <variable
            name="activity"
            type="com.harman.va.refactor.VAActivity" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"



            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_header"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_44dp"
                app:layout_constraintTop_toTopOf="parent" android:visibility="gone">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:onClick="@{() -> activity.onBackBtnClick()}"
                    android:padding="@dimen/dimen_10dp"
                    android:src="@drawable/select_icon_menu_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/Text_Title_2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/Create_a_System"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:id="@+id/top_frag_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:activity="@{activity}"
                app:fragmentTag="@{`top`}"
                app:targetFragment="@{activity.topFragment}" />

        </LinearLayout>


    </RelativeLayout>
</layout>