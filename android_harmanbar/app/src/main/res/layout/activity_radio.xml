<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.harman.radio.RadioActivity" />

        <import type="com.harman.radio.RadioActivity.EnumPageStyle" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/layout_radio_loading"
            layout="@layout/layout_radio_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:activity="@{activity}"
            app:isVisible="@{EnumPageStyle.LOADING == activity.pageStyle}" />

        <include
            android:id="@+id/layout_radio_fm"
            layout="@layout/layout_radio_fm"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:activity="@{activity}"
            app:isVisible="@{EnumPageStyle.FM == activity.pageStyle}" />

        <include
            android:id="@+id/layout_radio_dab"
            layout="@layout/layout_radio_dab"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:activity="@{activity}"
            app:isVisible="@{EnumPageStyle.DAB == activity.pageStyle}" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>