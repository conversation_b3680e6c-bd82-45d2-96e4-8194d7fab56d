<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/radius_medium_bg_card"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_16dp"
        android:paddingBottom="@dimen/dimen_12dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/number_of_bars"
            android:textColor="@color/fg_secondary"
            android:textSize="@dimen/font_14" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_12dp"/>
    </LinearLayout>
</layout>