<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.harman.radio.RadioActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/sp_player">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_header"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onBackBtnClick()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/ic_arrow_down"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_radio_source"
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dab"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_switch_radio_source"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onSwitchRadioSourceClick()}"
                android:src="@drawable/ic_radio_switch"
                app:isVisible="@{activity.ableSwitchRadioSource}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_radio_source"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_album"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dimen_18dp"
            android:layout_marginBottom="@dimen/dimen_20dp"
            android:background="@drawable/radius_medium_fixed_white_10"
            app:layout_constraintBottom_toTopOf="@id/layout_dab"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_header">

            <ImageView
                android:id="@+id/ic_input_source"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_input_source_dab_large"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_dab"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_40dp"
            android:minHeight="@dimen/dimen_60dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/layout_player_control">

            <LinearLayout
                android:id="@+id/layout_dab_playing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:isVisible="@{!activity.dabNeedScan}"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/Text_Heading_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxLines="1"
                    android:text="@{activity.dabTitle}"
                    android:textColor="@color/fixed_white"
                    tools:text="BBC Radio 5 live" />

                <TextView
                    android:id="@+id/tv_sub_title"
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxLines="1"
                    android:text="@{activity.dabSubTitle}"
                    android:textColor="@color/fixed_white_50"
                    android:visibility="gone"
                    tools:text="Leah Dou" />

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_dab_empty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:isVisible="@{activity.dabNeedScan}"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone">

                <com.harman.widget.ComponentSmallButton
                    android:id="@+id/btn_dab_scan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="@{() -> activity.onDABScanBtnClick()}"
                    android:text="@string/scan"
                    app:csb_btn_type="highlight"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_dab_scan_tips"
                    style="@style/Text_Body_Regular"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="56dp"
                    android:text="@string/scan_to_find_all_available_stations_it_will_take_a_few_minutes"
                    android:textColor="@color/fixed_white_50"
                    app:layout_constraintEnd_toStartOf="@id/btn_dab_scan"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_player_control"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:layout_marginBottom="@dimen/dimen_36dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/layout_volume">

            <ImageView
                android:id="@+id/ic_play_pause"
                android:layout_width="@dimen/dimen_64dp"
                android:layout_height="@dimen/dimen_64dp"
                android:onClick="@{() -> activity.onPlayStopClick()}"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:setImgRes="@{activity.playStopImgRes}"
                tools:src="@drawable/ic_round_pause_2" />

            <ImageView
                android:id="@+id/ic_previous"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_24dp"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onPreviousClick()}"
                android:padding="@dimen/dimen_8dp"
                android:src="@drawable/btn_ic_skip_previous"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/ic_play_pause"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ic_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_24dp"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onNextClick()}"
                android:padding="@dimen/dimen_8dp"
                android:src="@drawable/btn_ic_skip_next"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ic_play_pause"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_favourite_unselected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onFavouriteClick(false)}"
                android:src="@drawable/ic_favourite_fg_primary"
                app:decoEnable="@{activity.ableControl}"
                app:isVisible="@{!activity.isPreset}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_favourite_selected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onFavouriteClick(true)}"
                app:isVisible="@{activity.ableControl &amp;&amp; activity.isPreset}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_favourite_orange_2"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="@{activity.presetIndex}"
                    android:textColor="@color/fixed_white"
                    android:textSize="@dimen/font_10sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="1" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/iv_station_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> activity.onStationListClick()}"
                android:src="@drawable/ic_station_list_fixed_white"
                app:decoEnable="@{activity.ableControl}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_volume"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_36dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_device_name">

            <ImageView
                android:id="@+id/ic_volume"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_player_volume" />

                <SeekBar
                    android:id="@+id/sb_volume"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:layout_marginStart="@dimen/dimen_12dp"
                    android:max="100"
                    android:maxHeight="@dimen/dimen_4dp"
                    android:minHeight="@dimen/dimen_4dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:paddingStart="0dp"
                    android:paddingEnd="0dp"
                    android:progressDrawable="@drawable/progressbar_fixed_white_fixed_white_10"
                    android:thumb="@drawable/thumb_radius_12_fixed_white"
                    android:thumbOffset="0dp"
                    android:splitTrack="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ic_volume"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:progress="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_device_name"
            style="@style/Text_Body_Strong"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dimen_26dp"
            android:layout_marginBottom="@dimen/dimen_36dp"
            android:drawablePadding="@dimen/dimen_4dp"
            android:text="@{activity.deviceName}"
            android:textColor="@color/fixed_white_50"
            app:drawableStartCompat="@drawable/ic_device_small_fixed_white_50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Spk_Name" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>