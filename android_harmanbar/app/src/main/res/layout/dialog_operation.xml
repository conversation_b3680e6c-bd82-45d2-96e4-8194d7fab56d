<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/radius_large_top_bg_card"
    android:orientation="vertical"
    android:paddingBottom="48dp">

    <TextView
        android:id="@+id/tvPresetName"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:layout_marginEnd="@dimen/dimen_20dp"
        android:textColor="@color/fg_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Preset" />

    <TextView
        android:id="@+id/tvDuplicate"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="32dp"
        android:text="@string/Duplicate"
        android:textColor="@color/fg_primary"
        app:drawableStartCompat="@drawable/ic_copy2" />

    <TextView
        android:id="@+id/tvResetToDefault"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="32dp"
        android:text="@string/reset_to_default"
        android:textColor="@color/fg_primary"
        app:drawableStartCompat="@drawable/ic_revert" />

    <TextView
        android:id="@+id/tvRename"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="32dp"
        android:text="@string/rename_text"
        android:textColor="@color/fg_primary"
        app:drawableStartCompat="@drawable/ic_rename_filled" />

    <TextView
        android:id="@+id/tvRemoveFrom"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="32dp"
        android:text="@string/remove_from_on_product_list"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_remove"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvSaveTo"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="32dp"
        android:text="@string/save_to_on_product_list"
        android:textColor="@color/fg_primary"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_save"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvDeletePreset"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="32dp"
        android:text="@string/delete_preset"
        android:textColor="@color/fg_primary"
        app:drawableStartCompat="@drawable/ic_remove_red" />

</LinearLayout>