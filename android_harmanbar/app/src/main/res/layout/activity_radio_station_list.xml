<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.harman.radio.RadioStationListActivity" />

        <import type="com.harman.radio.RadioStationListActivity.EnumPageStyle" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onBackBtnClick()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/svg_icon_arrow_backward"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_scan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onSyncBtnClick()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/ic_sync_fixed_white"
                app:isVisible="@{EnumPageStyle.Scanning != activity.pageStyle}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title_label"
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/stations"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ProgressBar
            android:id="@+id/progressBar"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:max="100"
            android:progress="1"
            android:progressDrawable="@drawable/horizontal_custom_progress_style"
            app:layout_constraintTop_toBottomOf="@id/layout_title_bar" />

        <TextView
            android:id="@+id/tv_scanning_tips"
            style="@style/Text_Caption_1_Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_12dp"
            android:text="@string/Scanning"
            android:textColor="@color/fg_disabled"
            android:visibility="gone"
            app:isVisible="@{EnumPageStyle.Scanning == activity.pageStyle}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressBar" />

        <TextView
            android:id="@+id/tv_no_station_tips"
            style="@style/Text_Caption_1_Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_12dp"
            android:text="@string/no_station_found"
            android:textColor="@color/fg_disabled"
            android:visibility="gone"
            app:isVisible="@{EnumPageStyle.Empty == activity.pageStyle}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progressBar" />

        <!--<androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/preset_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dimen_20dp"
            android:paddingVertical="@dimen/dimen_16dp"
            app:isVisible="@{EnumPageStyle.NotEmpty == activity.pageStyle}"
            app:layout_constraintBottom_toTopOf="@+id/station_list"
            app:layout_constraintTop_toBottomOf="@id/layout_title_bar">

            <include
                android:id="@+id/preset_radio_slot_one"
                layout="@layout/layout_preset_radio_slot"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toStartOf="@+id/preset_radio_slot_two"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:selectedPresetStation="@{activity.selectedStation}"
                app:slotPresetStation="@{activity.presetStationOne}" />

            <include
                android:id="@+id/preset_radio_slot_two"
                layout="@layout/layout_preset_radio_slot"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dimen_15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toStartOf="@+id/preset_radio_slot_three"
                app:layout_constraintStart_toEndOf="@id/preset_radio_slot_one"
                app:layout_constraintTop_toTopOf="parent"
                app:selectedPresetStation="@{activity.selectedStation}"
                app:slotPresetStation="@{activity.presetStationTwo}" />

            <include
                android:id="@+id/preset_radio_slot_three"
                layout="@layout/layout_preset_radio_slot"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dimen_15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toStartOf="@+id/preset_radio_slot_four"
                app:layout_constraintStart_toEndOf="@id/preset_radio_slot_two"
                app:layout_constraintTop_toTopOf="parent"
                app:selectedPresetStation="@{activity.selectedStation}"
                app:slotPresetStation="@{activity.presetStationThree}" />

            <include
                android:id="@+id/preset_radio_slot_four"
                layout="@layout/layout_preset_radio_slot"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dimen_15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toStartOf="@+id/preset_radio_slot_five"
                app:layout_constraintStart_toEndOf="@id/preset_radio_slot_three"
                app:layout_constraintTop_toTopOf="parent"
                app:selectedPresetStation="@{activity.selectedStation}"
                app:slotPresetStation="@{activity.presetStationFour}" />

            <include
                android:id="@+id/preset_radio_slot_five"
                layout="@layout/layout_preset_radio_slot"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dimen_15dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/preset_radio_slot_four"
                app:layout_constraintTop_toTopOf="parent"
                app:selectedPresetStation="@{activity.selectedStation}"
                app:slotPresetStation="@{activity.presetStationFive}" />

        </androidx.constraintlayout.widget.ConstraintLayout>-->

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/station_recycle_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:listSelector="@drawable/transparent"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:isVisible="@{EnumPageStyle.NotEmpty == activity.pageStyle}"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_scanning_tips"
            app:setMultiAdapter="@{activity.adapter}"
            app:submitMultiList="@{activity.stations}"
            tools:listitem="@layout/item_scan_station" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>