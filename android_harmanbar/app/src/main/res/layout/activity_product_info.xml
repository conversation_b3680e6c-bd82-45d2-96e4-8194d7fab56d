<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="device"
            type="com.harman.discover.bean.BaseDevice" />

        <variable
            name="viewModel"
            type="com.harman.product.info.ProductInfoViewModel" />

        <variable
            name="activity"
            type="com.harman.product.info.ProductInfoActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> activity.onBackPressed()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/select_icon_arrow_l"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title_label"
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/product_information"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_title_bar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_general_title"
                    style="@style/Text_Title_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:gravity="center_vertical"
                    android:text="@string/harmanbar_jbl_General"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_general"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/radius_medium_bg_card"
                    android:minHeight="35dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_general_title">

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_product_model"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:descVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:setDeviceModelNameDescText="@{ device }"
                        app:titleText="@string/jbl_Product_Model" />

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_serial_number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:descVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/item_product_model"
                        app:setSerialNumberDescText="@{ viewModel.device }"
                        app:titleText="@string/harmanbar_jbl_Serial_Number" />

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_mac_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:descVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:isVisible="@{activity.showMacAddress}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/item_serial_number"
                        app:setDeviceMacDescText="@{ viewModel.device }"
                        app:titleText="@string/jbl_MAC_Address" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_software"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_16dp"
                    android:background="@drawable/radius_medium_bg_card"
                    android:minHeight="35dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_general">

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_software"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="96dp"
                        app:descVisibility="visible"
                        app:endIconImage="@drawable/icon_ota_update"
                        app:extraActionVisibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/item_serial_number"
                        app:setDescForSettingsOptionsItem="@{activity.fwDesc}"
                        app:setEndImgClickListener="@{() -> activity.onOtaIconClick()}"
                        app:titleText="@string/product_software" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_learn_more_title"
                    style="@style/Text_Title_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:gravity="center_vertical"
                    android:text="@string/harmanbar_jbl_Learn_More"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_software" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_learn_more"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/radius_medium_bg_card"
                    android:minHeight="35dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_learn_more_title">

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_qsg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:onClick="@{() -> activity.launchQsg() }"
                        app:descVisibility="gone"
                        app:endIconImage="@drawable/ic_arrow_forward_fg_primary"
                        app:endIconImageVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/item_serial_number"
                        app:titleText="@string/harmanbar_jbl_Quick_Start_Guide" />

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_product_support"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:onClick="@{() -> activity.launchProductSupport() }"
                        android:visibility="@{activity.hasProductSupportUrl == true ? View.VISIBLE : View.GONE}"
                        app:descVisibility="gone"
                        app:endIconImage="@drawable/icon_arrow_outward"
                        app:endIconImageVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/item_qsg"
                        app:titleText="@string/harmanbar_jbl_Product_Support" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>