<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_surface">

    <View
        android:id="@+id/vL"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginStart="17dp"
        android:layout_marginEnd="24dp"
        android:background="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="@+id/ivNoiseGate"
        app:layout_constraintTop_toTopOf="@+id/ivNoiseGate" />

    <View
        android:id="@+id/vC"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/layer_list_size16_radius8_stroke2"
        android:rotation="90"
        app:layout_constraintStart_toEndOf="@+id/vL"
        app:layout_constraintTop_toTopOf="@id/vL" />

    <View
        android:id="@+id/vL2"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:background="@color/fg_primary"
        app:layout_constraintBottom_toTopOf="@+id/vC2"
        app:layout_constraintEnd_toEndOf="@+id/vC2"
        app:layout_constraintTop_toBottomOf="@+id/vC" />

    <View
        android:id="@+id/vC2"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/layer_list_size16_radius8_stroke2"
        android:rotation="180"
        app:layout_constraintBottom_toBottomOf="@+id/vL3"
        app:layout_constraintStart_toEndOf="@+id/vL3" />

    <View
        android:id="@+id/vL3"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:background="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="@+id/ivCab"
        app:layout_constraintTop_toTopOf="@+id/ivCab" />

    <View
        android:id="@+id/vL4"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="17dp"
        android:background="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="@+id/ivMod"
        app:layout_constraintTop_toTopOf="@+id/ivMod" />

    <View
        android:id="@+id/vC3"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/layer_list_size16_radius8_stroke2"
        android:rotation="0"
        app:layout_constraintEnd_toStartOf="@+id/vL3"
        app:layout_constraintTop_toTopOf="@+id/vL3" />

    <View
        android:id="@+id/vC4"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/layer_list_size16_radius8_stroke2"
        android:rotation="270"
        app:layout_constraintBottom_toBottomOf="@+id/vL4"
        app:layout_constraintEnd_toStartOf="@+id/vL4" />

    <View
        android:id="@+id/vL5"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:background="@color/fg_primary"
        app:layout_constraintBottom_toTopOf="@+id/vC4"
        app:layout_constraintStart_toStartOf="@+id/vC4"
        app:layout_constraintTop_toBottomOf="@+id/vC3" />

    <ImageView
        android:id="@+id/ivNoiseGate"
        style="@style/guitarPresetSlot1"
        app:layout_constraintBottom_toTopOf="@+id/ivCab"
        app:layout_constraintEnd_toStartOf="@+id/ivCompressor"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvNoiseGate"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivNoiseGate"
        app:layout_constraintStart_toStartOf="@+id/ivNoiseGate"
        app:layout_constraintTop_toBottomOf="@+id/ivNoiseGate"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivCompressor"
        style="@style/guitarPresetSlot1"
        app:layout_constraintEnd_toStartOf="@+id/ivWah"
        app:layout_constraintStart_toEndOf="@+id/ivNoiseGate"
        app:layout_constraintTop_toTopOf="@+id/ivNoiseGate" />

    <TextView
        android:id="@+id/tvCompressor"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivCompressor"
        app:layout_constraintStart_toStartOf="@+id/ivCompressor"
        app:layout_constraintTop_toBottomOf="@+id/ivCompressor"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivWah"
        style="@style/guitarPresetSlot1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivCompressor"
        app:layout_constraintTop_toTopOf="@+id/ivNoiseGate" />

    <TextView
        android:id="@+id/tvWah"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivWah"
        app:layout_constraintStart_toStartOf="@+id/ivWah"
        app:layout_constraintTop_toBottomOf="@+id/ivWah"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivCab"
        style="@style/guitarPresetSlot2"
        android:layout_marginVertical="88dp"
        app:layout_constraintBottom_toTopOf="@+id/ivMod"
        app:layout_constraintEnd_toStartOf="@+id/ivAmp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivNoiseGate" />

    <TextView
        android:id="@+id/tvCab"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivCab"
        app:layout_constraintStart_toStartOf="@+id/ivCab"
        app:layout_constraintTop_toBottomOf="@+id/ivCab"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivAmp"
        style="@style/guitarPresetSlot3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivCab"
        app:layout_constraintTop_toTopOf="@+id/ivCab" />

    <TextView
        android:id="@+id/tvAmp"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivAmp"
        app:layout_constraintStart_toStartOf="@+id/ivAmp"
        app:layout_constraintTop_toBottomOf="@+id/ivAmp"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivMod"
        style="@style/guitarPresetSlot1"
        app:layout_constraintBottom_toTopOf="@+id/tvSaveAs"
        app:layout_constraintEnd_toStartOf="@+id/ivDelay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivCab" />

    <TextView
        android:id="@+id/tvMod"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivMod"
        app:layout_constraintStart_toStartOf="@+id/ivMod"
        app:layout_constraintTop_toBottomOf="@+id/ivMod"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivDelay"
        style="@style/guitarPresetSlot1"
        app:layout_constraintEnd_toStartOf="@+id/ivReverb"
        app:layout_constraintStart_toEndOf="@+id/ivMod"
        app:layout_constraintTop_toTopOf="@+id/ivMod" />

    <TextView
        android:id="@+id/tvDelay"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivDelay"
        app:layout_constraintStart_toStartOf="@+id/ivDelay"
        app:layout_constraintTop_toBottomOf="@+id/ivDelay"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <ImageView
        android:id="@+id/ivReverb"
        style="@style/guitarPresetSlot1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivDelay"
        app:layout_constraintTop_toTopOf="@+id/ivMod" />

    <TextView
        android:id="@+id/tvReverb"
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/ivReverb"
        app:layout_constraintStart_toStartOf="@+id/ivReverb"
        app:layout_constraintTop_toBottomOf="@+id/ivReverb"
        tools:text="Noise Gate"
        tools:textColor="@color/fg_primary" />

    <TextView
        android:id="@+id/tvSave"
        style="@style/Text_Button"
        android:layout_width="140dp"
        android:layout_height="48dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/radius_round_fg_primary"
        android:gravity="center"
        android:text="@string/jbl_SAVE"
        android:textColor="@color/fg_inverse"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvSaveAs" />

    <TextView
        android:id="@+id/tvSaveAs"
        style="@style/Text_Button"
        android:layout_width="140dp"
        android:layout_height="48dp"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:text="@string/title_save_as"
        android:textColor="@color/fg_primary"
        app:layout_constraintEnd_toStartOf="@+id/tvSave"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvSave" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gSaveArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:constraint_referenced_ids="tvSave,tvSaveAs"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>