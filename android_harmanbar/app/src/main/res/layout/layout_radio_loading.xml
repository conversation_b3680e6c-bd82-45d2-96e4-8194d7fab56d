<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="com.harman.radio.RadioActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/sp_player">

        <ImageView
            android:id="@+id/iv_loading"
            android:layout_width="@dimen/dimen_36dp"
            android:layout_height="@dimen/dimen_36dp"
            android:src="@drawable/loading_fg_primary"
            app:loadingStyle2="@{activity.switchingSource}"
            app:layout_constraintBottom_toTopOf="@+id/tv_loading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_loading"
            style="@style/Text_Body_Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_12dp"
            android:text="@string/loading"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_loading" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>