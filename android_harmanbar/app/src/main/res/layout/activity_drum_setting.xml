<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.harman.partyband.looper.DrumSettingActivity"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="44dp"
            android:paddingHorizontal="@dimen/dimen_16dp"
            tools:background="@color/bg_surface">

            <ImageView
                android:id="@+id/ivLeading"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/icon_arrow_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                style="@style/Text_Title_2"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tv_add"
                app:layout_constraintStart_toEndOf="@+id/ivLeading"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="this is titlethis is title" />

            <TextView
                android:id="@+id/tv_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:text="@string/Add"
                android:textColor="@color/fg_inverse"
                android:textSize="@dimen/font_14"
                android:paddingHorizontal="@dimen/dimen_12dp"
                android:paddingVertical="@dimen/dimen_4dp"
                android:background="@drawable/radius_round_bg_highlighted"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_track_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/fg_primary"
            android:textSize="@dimen/font_16"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/track_setting"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_device"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:paddingHorizontal="@dimen/dimen_17dp"
            android:paddingVertical="@dimen/dimen_14dp"
            android:background="@drawable/radius_medium_bg_card">

            <TextView
                style="@style/Text_Body_Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:text="@string/bpm"
                android:textColor="@color/fg_primary"
                android:textSize="@dimen/font_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/ivSub"
            android:layout_width="@dimen/dimen_26dp"
            android:layout_height="@dimen/dimen_26dp"
            android:layout_marginEnd="20dp"
            android:src="@drawable/ic_round_sub"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tvBpm"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tvBpm"
            style="@style/Text_Heading_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:gravity="center"
            android:minWidth="60dp"
            android:textColor="@color/fg_primary"
            android:textSize="@dimen/font_17"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ivAdd"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="120" />

        <ImageView
            android:id="@+id/ivAdd"
            android:layout_width="@dimen/dimen_26dp"
            android:layout_height="@dimen/dimen_26dp"
            android:layout_marginStart="20dp"
            android:src="@drawable/ic_round_add"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_rhythm_title"
            style="@style/Text_Title_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="28dp"
            android:text="@string/styles"
            android:textColor="@color/fg_primary" />

        <LinearLayout
            android:id="@+id/rgBeat"
            android:layout_width="wrap_content"
            android:layout_height="37dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:divider="@drawable/divider_h_12"
            android:orientation="horizontal"
            android:showDividers="middle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/indicator">

            <TextView
                android:id="@+id/rbBeat44"
                style="@style/Text_Body_Medium"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="4/4"
                tools:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/rbBeat43"
                style="@style/Text_Body_Medium"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="4/3"
                tools:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/rbBeat42"
                style="@style/Text_Body_Medium"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="4/2"
                tools:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/rbBeat68"
                style="@style/Text_Body_Medium"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="6/8"
                tools:textColor="@color/fg_primary" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_metronome_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/svg_icon_info_filled"
            android:textSize="@dimen/font_12"
            android:textColor="@color/fg_secondary"
            android:drawablePadding="@dimen/dimen_4dp"
            android:layout_marginHorizontal="@dimen/dimen_17dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/metronome_will_not_be_recorded"
            android:visibility="gone"/>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvTypes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginVertical="8dp"
            android:background="@drawable/radius_medium_bg_card"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>