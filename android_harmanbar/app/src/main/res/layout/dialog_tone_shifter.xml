<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/radius_large_top_bg_card"
    android:orientation="vertical">

    <TextView
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:layout_marginEnd="@dimen/dimen_20dp"
        android:text="@string/pitch_shifter"
        android:textColor="@color/fg_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/btnConfirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:src="@drawable/icon_check_fg_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvValue"
        style="@style/Text_Display_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="130dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="-50"
        tools:textColor="@color/fg_primary" />

    <TextView
        android:id="@+id/tvValueDesc"
        style="@style/Text_Body_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvValue"
        tools:text="Standard"
        tools:textColor="@color/fg_primary" />


    <com.harman.partyband.widget.HorizontalScaleView
        android:id="@+id/hsvValue"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="40dp"
        app:hsv_max="10"
        app:hsv_value="5"
        app:layout_constraintTop_toBottomOf="@+id/tvValueDesc" />


    <com.harman.widget.ComponentSmallButton
        android:id="@+id/btnReset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="46dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="40dp"
        android:text="@string/jbl_RESET"
        app:csb_btn_type="red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hsvValue" />

</androidx.constraintlayout.widget.ConstraintLayout>