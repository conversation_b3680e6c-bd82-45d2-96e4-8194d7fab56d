<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="8dp"
    tools:background="@color/fg_inverse">

    <ImageView
        android:id="@+id/ivSlot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:maxWidth="123dp"
        android:maxHeight="81dp"
        tools:src="@drawable/slot_amp1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlgorithmName"
        style="@style/Text_Caption_1_Strong"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        tools:text="Mod"
        tools:textColor="@color/fg_primary" />
</LinearLayout>