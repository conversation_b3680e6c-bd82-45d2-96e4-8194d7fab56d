<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="fragment"
            type="com.harman.product.setting.AppSettingsFragment" />
        <variable
            name="viewModel"
            type="com.harman.product.setting.AppSettingsViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dimen_44dp"
            android:paddingBottom="@dimen/settings_heading_padding_bottom">

            <TextView
                android:id="@+id/vtitle"
                style="@style/Text_Brand_Heading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:text="@string/harmanbar_jbl_Settings"
                android:textColor="@color/fg_primary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/layout_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_account"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:onClick="@{() -> fragment.onClickCount()}"
                        app:descVisibility="visible"
                        app:endIconImage="@drawable/ic_arrow_forward_square"
                        app:endIconImageVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:setDescForSettingsOptionsItem="@{fragment.accountEmail}"
                        app:setTitleForSettingsOptionsItem="@{fragment.accountName}"
                        app:startIconImage="@drawable/icon_setting_account"
                        app:startIconVisibility="visible"
                        tools:layout_editor_absoluteX="0dp"
                        tools:layout_editor_absoluteY="0dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_add_new_product"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_24dp"
                    android:background="@drawable/ripple_list_item_background">

                    <com.harman.widget.ComponentCardStandardItem
                        android:id="@+id/item_add_new_product"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:onClick="@{() -> fragment.onAddNewProduct()}"
                        app:descVisibility="gone"
                        app:endIconImage="@drawable/ic_arrow_forward_square"
                        app:endIconImageVisibility="visible"
                        app:extraActionVisibility="gone"
                        app:startIconImage="@drawable/icon_setting_add_new_product"
                        app:startIconVisibility="visible"
                        app:titleText="@string/add_new_product" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_without_count"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_general_title2"
                        style="@style/Text_Body_Strong"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_35dp"
                        android:layout_marginTop="@dimen/dimen_16dp"
                        android:gravity="center_vertical"
                        android:text="@string/jbl_general"
                        android:textColor="@color/fg_primary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:id="@+id/layout_general2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ripple_list_item_background"
                        android:minHeight="35dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_general_title2" android:orientation="vertical">

                        <com.harman.widget.ComponentCardStandardItem
                            android:id="@+id/item_language2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:onClick="@{() -> fragment.onClickLanguage()}"
                            app:descVisibility="gone"
                            app:endIconImage="@drawable/ic_arrow_forward_square"
                            app:endIconImageVisibility="visible"
                            app:extraActionVisibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:startIconImage="@drawable/jbl_language_icon"
                            app:startIconVisibility="visible"
                            app:titleText="@string/harmanbar_jbl_Language" />

                        <com.harman.widget.ComponentCardStandardItem
                            android:id="@+id/item_legal2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:onClick="@{() -> fragment.onClickLegal()}"
                            app:descVisibility="gone"
                            app:endIconImage="@drawable/ic_arrow_forward_square"
                            app:endIconImageVisibility="visible"
                            app:extraActionVisibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/item_language2"
                            app:startIconImage="@drawable/jbl_legal_icon"
                            app:startIconVisibility="visible"
                            app:titleText="@string/harmanbar_jbl_Legal" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_support"
                        style="@style/Text_Body_Strong"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_35dp"
                        android:layout_marginTop="@dimen/dimen_16dp"
                        android:gravity="center_vertical"
                        android:text="@string/harmanbar_support"
                        android:textColor="@color/fg_primary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_general2" />

                    <LinearLayout
                        android:id="@+id/layout_support"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ripple_list_item_background"
                        android:minHeight="35dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_support" android:orientation="vertical">

                        <com.harman.widget.ComponentCardStandardItem
                            android:id="@+id/item_customer_service2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:onClick="@{() -> fragment.onCustomerService()}"
                            app:descVisibility="gone"
                            app:endIconImage="@drawable/ic_arrow_forward_square"
                            app:endIconImageVisibility="visible"
                            app:extraActionVisibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:startIconImage="@drawable/customer_service_icon"
                            app:startIconVisibility="visible"
                            app:titleText="@string/harmanbar_jbl_Customer_service" />

                        <com.harman.widget.ComponentCardStandardItem
                            android:id="@+id/item_product_register"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:onClick="@{() -> fragment.onClickProductRegistration()}"
                            android:visibility="@{viewModel.accountSupport ? View.GONE : View.VISIBLE}"
                            app:descVisibility="gone"
                            app:endIconImage="@drawable/ic_arrow_forward_square"
                            app:endIconImageVisibility="visible"
                            app:extraActionVisibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/item_customer_service2"
                            app:startIconImage="@drawable/jbl_product_registration_icon"
                            app:startIconVisibility="visible"
                            app:titleText="@string/harmanbar_jbl_Product_Registration" />

                        <com.harman.widget.ComponentCardStandardItem
                            android:id="@+id/item_sign_up_news_letter"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:onClick="@{() -> fragment.onClickSignUpNewLetter()}"
                            android:visibility="@{viewModel.accountSupport ? View.GONE : View.VISIBLE}"
                            app:descVisibility="gone"
                            app:endIconImage="@drawable/icon_arrow_outward"
                            app:endIconImageVisibility="visible"
                            app:extraActionVisibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/item_product_register"
                            app:startIconImage="@drawable/jbl_sign_up_newsletter_icon"
                            app:startIconVisibility="visible"
                            app:titleText="@string/harmanbar_jbl_Sign_Up_Newsletter" />

                    </LinearLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_app_version"
                    style="@style/Text_Caption_1_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_24dp"
                    android:layout_marginBottom="@dimen/dimen_24dp"
                    android:gravity="center_horizontal|top"
                    android:onClick="@{() -> fragment.onClickVersion()}"
                    android:paddingBottom="@dimen/dimen_24dp"
                    android:text="@{fragment.appVersion}"
                    android:textColor="@color/fg_secondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_without_count"
                    tools:text="App Version: 1.2.3" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</layout>