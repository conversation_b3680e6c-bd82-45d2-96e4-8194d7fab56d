<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    tools:background="@color/bg_card">

    <TextView
        android:id="@+id/tv"
        style="@style/Text_Body_Medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/fg_primary"
        tools:text="@string/guitar_33" />

    <ImageView
        android:id="@+id/ivCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        android:src="@drawable/svg_icon_security_unselected" />
</LinearLayout>