<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.harman.DashboardDialogViewModel" />

        <import type="com.harman.DebugDashboardDialog" />

        <variable
            name="viewModel"
            type="com.harman.DashboardDialogViewModel" />

        <variable
            name="dialog"
            type="com.harman.DebugDashboardDialog" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_surface"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/layout_commands"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:orientation="vertical">

            <RadioGroup
                android:id="@+id/rb_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onCheckedChanged="@{dialog.onSessionPriChanged}"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rb_wifi"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/width_50"
                    android:button="@null"
                    android:checked="true"
                    android:drawableEnd="@drawable/placement_rb_select_bg"
                    android:drawablePadding="5dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:text="Wi-Fi"
                    android:textColor="@color/fg_primary"
                    android:textSize="@dimen/font_16" />

                <RadioButton
                    android:id="@+id/rb_ble"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/width_50"
                    android:button="@null"
                    android:drawableEnd="@drawable/placement_rb_select_bg"
                    android:drawablePadding="5dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:text="BLE"
                    android:textColor="@color/fg_primary"
                    android:textSize="@dimen/font_16" />

                <RadioButton
                    android:id="@+id/rb_auto"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/width_50"
                    android:button="@null"
                    android:drawableEnd="@drawable/placement_rb_select_bg"
                    android:drawablePadding="5dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:text="Auto"
                    android:textColor="@color/fg_primary"
                    android:textSize="@dimen/font_16" />

            </RadioGroup>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="@{() -> dialog.onClickChromeCastC4A()}">

                <TextView
                    android:id="@+id/tv_chromecast_c4a"
                    style="@style/Text_Button"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginTop="5dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="GOOGLE CAST"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switcher_chromecast_c4a_state"
                    app:layout_constraintHorizontal_weight="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ToggleButton
                    android:id="@+id/switcher_chromecast_c4a_state"
                    android:layout_width="@dimen/dimen_44dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:layout_marginStart="@dimen/dimen_10dp"
                    android:layout_marginEnd="@dimen/dimen_10dp"
                    android:background="@drawable/global_togger_button"
                    android:checked="@{viewModel.isGoogleCastEnable}"
                    android:clickable="false"
                    android:textOff=""
                    android:textOn=""
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_chromecast_c4a"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:checked="true"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_get_soundscape_v2_config"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetSoundscapeV2Config()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET SOUNDSCAPE V2 CONFIG"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_set_soundscape_v2_config"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneSetSoundscapeV2Config()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="SET SOUNDSCAPE V2 CONFIG"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_soundscape_v2_state"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetSoundscapeV2State()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET SOUNDSCAPE V2 STATE"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_control_soundscape_v2_state"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneControlSoundscapeV2()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="CONTROL SOUNDSCAPE V2"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_enable_mix_music"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneSoundscapeV2EnableMomentMixMusic()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="ENABLE MIX MUSIC"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_smart_btn_config"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetSmartBtnConfig()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET SMART BTN CONFIG"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_set_smart_btn_config"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneSetSmartBtnConfig()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="SET SMART BTN CONFIG"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_dev_info"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetDevInfo()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="REQ DEVICE INFO(1001)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_set_dev_info"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneSetDevInfo()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="SET DEVICE NAME(1801)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_ap_list"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetAPList()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET AP LIST(1007)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_set_cast_group"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneSetCastGroup()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="SET CAST GROUP(1D01)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_volume"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetVolume()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET VOLUME(1818)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_feature_support"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetFeatSupport()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET FEATURE SUPPORT(1011)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_eq_list"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneEQList()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET EQ LIST(1917)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_get_ota_access_point"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneGetOtaAccessPoint()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="GET OTA ACCESS POINT(1308)"
                android:textColor="@color/fg_inverse" />

            <TextView
                android:id="@+id/tv_check_fw_version"
                style="@style/Text_Button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_rect_29_bg_s1"
                android:gravity="center"
                android:onClick="@{() -> dialog.onClickJBLOneCheckFwVersion()}"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="CHECK FW VERSION(checkFwVersion)"
                android:textColor="@color/fg_inverse" />

        </LinearLayout>

    </ScrollView>
</layout>