<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/radius_large_top_bg_card"
    tools:layout_gravity="bottom">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:text="@string/harmanbar_content_Preset"
        android:textColor="@color/fg_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivConfirm"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginEnd="24dp"
        android:src="@drawable/ic_complete_fg_primary"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvGenreType"
        android:layout_width="match_parent"
        android:layout_height="67dp"
        android:layout_marginTop="24dp"
        android:clipToPadding="false"
        android:paddingHorizontal="24dp"
        app:layout_constraintTop_toBottomOf="@+id/ivConfirm" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvPresetList"
        android:layout_width="match_parent"
        android:layout_height="500dp"
        app:layout_constraintTop_toBottomOf="@+id/rvGenreType" />

</androidx.constraintlayout.widget.ConstraintLayout>