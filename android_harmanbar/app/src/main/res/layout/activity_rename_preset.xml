<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_32dp"
        android:background="@drawable/radius_medium_bg_card"
        android:inputType="textNoSuggestions|none"
        android:minHeight="56dp"
        android:paddingHorizontal="@dimen/dimen_16dp"
        android:paddingVertical="@dimen/dimen_12dp"
        android:textColor="@color/fg_primary"
        android:textCursorDrawable="@drawable/textcursordrawable"
        tools:text="deviceName" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.wifiaudio.view.component.ComponentButton
        android:id="@+id/cbSave"
        android:layout_width="255dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="32dp"
        app:btn_text="@string/Save"
        app:btn_type="btn_regular" />

</LinearLayout>