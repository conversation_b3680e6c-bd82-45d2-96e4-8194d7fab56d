<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.harman.DebugActivity" />

        <import type="com.harman.discover.bean.BaseDevice" />

        <import type="com.harman.DebugActivityViewModel" />

        <import type="android.view.View" />

        <variable
            name="activity"
            type="com.harman.DebugActivity" />

        <variable
            name="device"
            type="BaseDevice" />

        <variable
            name="viewModel"
            type="com.harman.DebugActivityViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_bg_card"
        android:onClick="@{() -> activity.onDeviceClick(device)}"
        android:onLongClick="@{() -> activity.onDeviceLongClick(device)}"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingBottom="@dimen/dimen_16dp">

        <ImageView
            android:id="@+id/selected_dot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_5"
            android:background="@drawable/tunein_music_more_d"
            android:visibility="@{viewModel.multiSelectedDevices.contains(device) ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_device_img"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_device_img"
            android:layout_width="@dimen/dimen_44dp"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_device_detail"
            app:layout_constraintStart_toEndOf="@id/selected_dot">

            <ImageView
                android:id="@+id/iv_device"
                android:layout_width="@dimen/dimen_44dp"
                android:layout_height="@dimen/dimen_44dp"
                android:background="@drawable/radius_round_bg_on_card"
                android:padding="10dp"
                app:auraCastDeviceImgForce="@{device}"
                app:layout_constraintBottom_toTopOf="@+id/layout_online_status"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/layout_online_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_device">

                <ImageView
                    android:id="@+id/iv_icon_wifi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/component_svg_icon_connect_type_wifi"
                    android:visibility="@{device.WiFiOnline ? View.VISIBLE : View.GONE}" />

                <ImageView
                    android:id="@+id/iv_icon_ble"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/component_svg_icon_connect_type_bt"
                    android:visibility="@{device.BLEOnline ? View.VISIBLE : View.GONE}" />
            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_device_detail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_platform"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@id/layout_device_img"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_model_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="start"
                android:gravity="start|center_vertical"
                android:maxLines="1"
                android:textColor="@color/fg_primary"
                android:textSize="@dimen/font_16sp"
                app:deviceName='@{device}'
                app:layout_constraintBottom_toTopOf="@+id/layout_device_info"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="...Partybox Stage 320" />

            <LinearLayout
                android:id="@+id/layout_device_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_name">

                <TextView
                    android:id="@+id/tv_pid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/fg_secondary"
                    android:textSize="@dimen/font_12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:pid="@{device}"
                    tools:text="20e1" />

                <TextView
                    android:id="@+id/tv_uuid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxLines="1"
                    android:text="@{}"
                    android:textColor="@color/fg_secondary"
                    android:textSize="@dimen/font_12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:uuid="@{device}"
                    tools:text="1a2b" />

                <TextView
                    android:id="@+id/tv_ble_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/fg_secondary"
                    android:textSize="@dimen/font_12sp"
                    app:bleAddress="@{device}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="11:22:33:AA:BB:CC" />

                <TextView
                    android:id="@+id/tv_mac_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start|center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/fg_secondary"
                    android:textSize="@dimen/font_12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:macAddress="@{device}"
                    tools:text="11:22:33:AA:BB:CC" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_platform"
            android:layout_width="@dimen/dimen_44dp"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/layout_device_detail"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_platform"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:decoPlatform="@{device}" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>