<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.calibration.CalibrationGuideDialog" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:paddingHorizontal="@dimen/dimen_24dp"
        android:paddingTop="@dimen/dimen_20dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text_Title_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/lets_calibrate_your_product_to_get_the_best_sound"
            android:textColor="@color/fg_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_device"
            android:layout_width="@dimen/dimen_280dp"
            android:layout_height="194dp"
            android:layout_marginTop="@dimen/dimen_12dp"
            android:scaleType="centerInside"
            app:devicePid="@{dialog.pid}"
            app:deviceCid="@{dialog.cid}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:src="@drawable/ic_device_fg_disable" />

        <LinearLayout
            android:id="@+id/layout_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_20dp"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/iv_device">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_time_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dimen_20dp"
                app:isVisible="@{dialog.supportRearSpeaker()}">

                <ImageView
                    android:id="@+id/ic_sod_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_clock_fg_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tv_time_tips"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_time_tips"
                    style="@style/Text_Body_Regular"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:text="@string/calibration_takes_about_3_minutes"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ic_sod_volume"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_tone_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/ic_volume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_calibration_volume_fg_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tv_tone_tips"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_tone_tips"
                    style="@style/Text_Body_Regular"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:text="@string/calibratioon_tone"
                    android:textColor="@color/fg_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ic_volume"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_calibration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_60dp"
            android:onClick="@{() -> dialog.onCalibrationBtnClick()}"
            app:btn_text="@string/harmanbar_jbl_Calibration"
            app:btn_type="btn_regular"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_tips"
            app:layout_constraintWidth_min="@dimen/dimen_255dp" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_later"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:gravity="center"
            android:onClick="@{() -> dialog.onLaterBtnClick()}"
            app:btn_text="@string/harmanbar_jbl_LATER"
            app:btn_type="btn_text_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_calibration"
            app:layout_constraintWidth_max="@dimen/dimen_255dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>