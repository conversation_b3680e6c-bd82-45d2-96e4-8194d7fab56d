<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    tools:layout_gravity="bottom">

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_purple_1"
        android:backgroundTint="@color/bg_card"
        android:orientation="vertical"
        android:paddingTop="225dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.harman.hkone.HMPagerIndicator
            android:id="@+id/indicator"
            android:layout_width="wrap_content"
            android:layout_height="56dp"
            android:layout_gravity="center_horizontal"
            app:selectedColor="@color/fg_primary"
            app:unselectedColor="@color/bg_opacity_50" />

        <TextView
            android:id="@+id/tvDesc"
            style="@style/Text_Body_Medium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="20dp"
            android:textColor="@color/fg_primary"
            tools:text="Hello! What type of musician you are?" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btnConfirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="60dp"
            android:layout_marginVertical="48dp"
            app:btn_text="@string/harmanbar_jbl_DONE" />
    </LinearLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp2"
        android:layout_width="match_parent"
        android:layout_height="450dp"
        android:layout_marginBottom="-225dp"
        app:layout_constraintBottom_toTopOf="@+id/llContent" />

</androidx.constraintlayout.widget.ConstraintLayout>
