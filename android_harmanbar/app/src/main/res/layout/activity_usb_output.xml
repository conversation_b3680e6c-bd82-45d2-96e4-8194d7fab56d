<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="10dp" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dimen_16dp">
            <!--output to layout-->
            <LinearLayout
                android:id="@+id/layout_output_to"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/radius_medium_bg_card"
                android:orientation="vertical"
                android:padding="@dimen/dimen_16dp">

                <TextView
                    style="@style/Text_Body_Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/output_to"
                    android:textColor="@color/fg_primary" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginHorizontal="@dimen/dimen_36dp"
                    android:layout_marginTop="@dimen/dimen_16dp"
                    android:background="@drawable/radius_round_stroke2_bg_on_card"
                    android:padding="4dp">

                    <ImageButton
                        android:id="@+id/ib_this_phone"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/round_rect_85_feature_sp1"
                        android:backgroundTint="@color/white"
                        android:src="@drawable/icon_phone"
                        app:tint="@color/fg_inverse" />


                    <ImageButton
                        android:id="@+id/ib_flash_drive"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/round_rect_85_feature_sp1"
                        android:backgroundTint="@color/bg_card"
                        android:src="@drawable/ic_flash_driver"
                        app:tint="@color/fg_secondary" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_36dp"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:id="@+id/tv_this_phone"
                        style="@style/Text_Caption_1_Regular"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:textStyle="bold"
                        android:text="@string/this_phone"
                        android:textColor="@color/white" />

                    <TextView
                        android:id="@+id/tv_flash_drive"
                        style="@style/Text_Caption_1_Regular"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/flash_drive"
                        android:textColor="@color/fg_secondary" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_warning"
                    style="@style/Text_Caption_1_Regular"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_6dp"
                    android:gravity="top"
                    android:text="@string/please_connect_your_phone"
                    android:textColor="@color/red_1" />
            </LinearLayout>


            <!--record layout-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:background="@drawable/radius_medium_bg_card"
                android:orientation="vertical"
                android:padding="@dimen/dimen_16dp"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_16dp">

                <TextView
                    android:id="@+id/tv_record_title"
                    style="@style/Text_Body_Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/record"
                    android:textColor="@color/fg_primary" />

                <TextView
                    android:id="@+id/tv_record_desc"
                    style="@style/Text_Caption_1_Regular"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_6dp"
                    android:text="@string/The_music_channel_is"
                    android:textColor="@color/fg_secondary" />

                <TextView
                    android:id="@+id/tv_record_time"
                    style="@style/Text_Caption_1_Regular"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_6dp"
                    android:textColor="@color/fg_primary"
                    android:textSize="@dimen/font_12"
                    tools:text="00:00" />

                <com.harman.partyband.widget.GuitarLooperCircleButton
                    android:id="@+id/bt_guitarloopercircle_button"
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dimen_36dp"
                    android:layout_marginBottom="@dimen/dimen_32dp"
                    app:glcb_debug="false"
                    app:glcb_type="ready" />

<!--                <com.harman.partyband.widget.GuitarLooperAudioWaveView-->
<!--                    android:id="@+id/view_guitarlooper_audiowave_view"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="@dimen/dimen_30dp"-->
<!--                    android:layout_marginLeft="4dp"-->
<!--                    android:layout_marginRight="4dp"-->
<!--                    android:visibility="gone" />-->

                <com.harman.partyband.widget.RealTimeAudioWaveView
                    android:id="@+id/view_realtime_audiowave"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_30dp"
                    android:layout_marginLeft="4dp"
                    android:layout_marginRight="4dp" />

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_21dp" />


                <!--Separate Output layout-->
                <RelativeLayout
                    android:id="@+id/layout_separate_output"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/radius_medium_bg_card"
                    android:backgroundTint="@color/fg_inverse"
                    android:paddingHorizontal="@dimen/dimen_16dp">

                    <TextView
                        android:id="@+id/tv_switch_separate_output"
                        style="@style/Text_Body_Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical"
                        android:text="@string/separate_channel_output"
                        android:textColor="@color/fg_primary" />

                    <Switch
                        android:id="@+id/switch_separate_output"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:checked="true"
                        android:minWidth="@dimen/width_44"
                        android:minHeight="@dimen/width_24"
                        android:thumb="@drawable/switch_thumb"
                        android:track="@drawable/switch_track"
                        tools:ignore="UseSwitchCompatOrMaterialXml" />
                </RelativeLayout>


                <Space
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_21dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_recordfiles_title"
                style="@style/Text_Body_Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="28dp"
                android:layout_marginBottom="16dp"
                android:gravity="center_vertical"
                android:text="@string/recored_file"
                android:textColor="@color/fg_primary" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rc_list_audio_files"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:divider="@null"
                android:listSelector="@drawable/transparent" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>