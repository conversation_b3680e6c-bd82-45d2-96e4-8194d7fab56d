<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_value"
            android:layout_width="@dimen/dimen_32dp"
            android:layout_height="@dimen/dimen_32dp"
            android:textSize="@dimen/font_14"
            android:textColor="@color/fg_secondary"
            android:background="@drawable/selector_bg_surface"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center"
            android:text="" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>