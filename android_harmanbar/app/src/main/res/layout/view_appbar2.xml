<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="44dp"
    tools:background="@color/bg_surface">

    <ImageView
        android:id="@+id/ivLeading"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:src="@drawable/icon_arrow_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/Text_Title_2"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="this is titlethis is title" />

    <com.harman.widget.ComponentSmallButton
        android:id="@+id/csbAction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/Save" />
</androidx.constraintlayout.widget.ConstraintLayout>