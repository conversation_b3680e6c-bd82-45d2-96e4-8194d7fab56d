<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toTopOf="@+id/tvTips"
        app:layout_constraintTop_toBottomOf="@id/appbar" />

    <TextView
        android:id="@+id/tvTips"
        style="@style/Text_Body_Regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:text="@string/ptl_update_tip"
        android:textColor="@color/fg_secondary"
        app:layout_constraintBottom_toTopOf="@+id/tvConfirm"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvConfirm"
        style="@style/Text_Button"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="60dp"
        android:layout_marginBottom="48dp"
        android:background="@drawable/bg_rect_max_radiu"
        android:gravity="center"
        android:text="@string/ptl_update_all"
        android:textColor="@color/fg_inverse"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="rv,tvTips"
        tools:visibility="visible" />

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/cpi"
        style="@style/Widget.Material3.CircularProgressIndicator.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        app:indicatorColor="@color/fg_primary"
        app:indicatorInset="0dp"
        app:indicatorSize="54dp"
        app:layout_constraintBottom_toTopOf="@+id/tvChecking"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvChecking"
        style="@style/Text_Body_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/checking_for_update"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cpi" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="cpi,tvChecking"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>