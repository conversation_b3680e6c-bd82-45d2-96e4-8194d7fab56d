<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_surface"
    tools:layout_height="430dp">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:lottie_fileName="lottie/partyband/ukulele/ukulele.json"
        tools:lottie_imageAssetsFolder="lottie/partyband/ukulele/" />

    <!--    guitar 3-3-->
    <TextView
        android:id="@+id/tv33String4"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.17"
        tools:text="4" />

    <TextView
        android:id="@+id/tv33String5"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv33String4"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.38"
        tools:text="5" />

    <TextView
        android:id="@+id/tv33String6"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv33String4"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.59"
        tools:text="6" />


    <TextView
        android:id="@+id/tv33String3"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.94"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv33String4"
        tools:text="3" />

    <TextView
        android:id="@+id/tv33String2"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintEnd_toEndOf="@+id/tv33String3"
        app:layout_constraintTop_toTopOf="@+id/tv33String5"
        tools:text="2" />

    <TextView
        android:id="@+id/tv33String1"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintEnd_toEndOf="@+id/tv33String3"
        app:layout_constraintTop_toTopOf="@+id/tv33String6"
        tools:text="1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/g33Guitar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:constraint_referenced_ids="tv33String3,tv33String2,tv33String6,tv33String4,tv33String5,tv33String1"
        tools:visibility="visible" />

    <!--guitar 6 in line-->
        <TextView
            android:id="@+id/tv6String1"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.03"
            tools:text="1" />

        <TextView
            android:id="@+id/tv6String2"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv6String1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.18"
            tools:text="2" />

        <TextView
            android:id="@+id/tv6String3"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv6String1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.33"
            tools:text="3" />


        <TextView
            android:id="@+id/tv6String4"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv6String1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.48"
            tools:text="4" />

        <TextView
            android:id="@+id/tv6String5"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv6String1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.63"
            tools:text="5" />

        <TextView
            android:id="@+id/tv6String6"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv6String1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.78"
            tools:text="6" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/g6Guitar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:constraint_referenced_ids="tv6String6,tv6String2,tv6String3,tv6String4,tv6String5,tv6String1"
            tools:visibility="visible" />


    <!--    bass-->
        <TextView
            android:id="@+id/tvBassString1"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.11"
            tools:text="1" />

        <TextView
            android:id="@+id/tvBassString2"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvBassString1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.26"
            tools:text="2" />

        <TextView
            android:id="@+id/tvBassString3"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvBassString1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.41"
            tools:text="3" />


        <TextView
            android:id="@+id/tvBassString4"
            style="@style/Text_Title_2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/radius_round_bg_card"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvBassString1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.56"
            tools:text="4" />


        <androidx.constraintlayout.widget.Group
            android:id="@+id/gBass"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:constraint_referenced_ids="tvBassString1,tvBassString2,tvBassString3,tvBassString4"
            tools:visibility="visible" />


    <!--    ukulele-->
    <TextView
        android:id="@+id/tvUkString3"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.2"
        tools:text="3" />

    <TextView
        android:id="@+id/tvUkString4"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvUkString3"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.52"
        tools:text="4" />

    <TextView
        android:id="@+id/tvUkString2"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.94"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvUkString3"
        tools:text="2" />


    <TextView
        android:id="@+id/tvUkString1"
        style="@style/Text_Title_2"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/radius_round_bg_card"
        android:gravity="center"
        android:textColor="@color/fg_primary"
        app:layout_constraintStart_toStartOf="@+id/tvUkString2"
        app:layout_constraintTop_toTopOf="@+id/tvUkString4"
        tools:text="1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gUkulele"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:constraint_referenced_ids="tvUkString3,tvUkString4,tvUkString2,tvUkString1"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
