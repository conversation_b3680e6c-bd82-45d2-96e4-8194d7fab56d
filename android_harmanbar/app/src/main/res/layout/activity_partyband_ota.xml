<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_device"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintBottom_toTopOf="@+id/layout_view_style"
        app:layout_constraintTop_toBottomOf="@id/layout_title_bar">

        <ImageView
            android:id="@+id/img_device"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_min="180dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_min="255dp" />

        <ImageView
            android:id="@+id/ivAlert"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.5"
            android:src="@drawable/ic_round_warn_fg_primary"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tvNewVersion"
            style="@style/Text_Title_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="24dp"
            android:textColor="@color/fg_primary"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Version ***********.10" />

        <TextView
            android:id="@+id/tvNewVersionDesc"
            style="@style/Text_Body_Regular"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_12dp"
            android:ellipsize="end"
            android:maxLines="4"
            android:paddingHorizontal="24dp"
            android:textColor="@color/fg_secondary"
            app:layout_constraintTop_toBottomOf="@+id/tvNewVersion"
            tools:text="1. Fixed bugs\n2. Improved performance of connection. Please help to make sure the Bluetooth of your mobile device is turned on ..." />

        <TextView
            android:id="@+id/tvNewVersionDescLearnMore"
            style="@style/Text_Body_Strong"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_12dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="24dp"
            android:text="@string/learn_more"
            android:textColor="@color/fg_activate"
            android:visibility="invisible"
            app:layout_constraintTop_toBottomOf="@+id/tvNewVersionDesc"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gNewVersion"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:constraint_referenced_ids="tvNewVersion,tvNewVersionDesc"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvMainTitle"
            style="@style/Text_Title_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:gravity="center"
            android:textColor="@color/fg_primary"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Transferring" />

        <TextView
            android:id="@+id/tvMainDesc"
            style="@style/Text_Body_Regular"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:textColor="@color/fg_secondary"
            app:layout_constraintTop_toBottomOf="@+id/tvMainTitle"
            tools:text="Transferring" />

        <LinearLayout
            android:id="@+id/layout_control"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingBottom="48dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/tvControlTip"
                style="@style/Text_Caption_1_Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dimen_12dp"
                android:drawablePadding="@dimen/dimen_5dp"
                android:gravity="center_vertical"
                android:textColor="@color/fg_secondary"
                android:visibility="gone"
                app:drawableLeftCompat="@drawable/ic_round_warn_fg_secondary"
                tools:text="@string/harmanbar_jbl_Plug_in_power_to_continue"
                tools:visibility="visible" />

            <com.wifiaudio.view.component.ComponentTextProgressBar
                android:id="@+id/pb"
                style="@style/TextProgressBarStyle"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginStart="@dimen/dimen_60dp"
                android:layout_marginEnd="@dimen/dimen_60dp"
                android:maxWidth="@dimen/dimen_255dp"
                android:visibility="gone"
                app:pb_text="0%"
                app:pb_text_color="@color/fg_primary"
                app:pb_text_cross_color="@color/fg_inverse"
                app:pb_text_font="Button_Strong"
                tools:text="15 %"
                tools:visibility="visible" />

            <com.wifiaudio.view.component.ComponentButton
                android:id="@+id/btnMain"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginStart="@dimen/dimen_60dp"
                android:layout_marginEnd="@dimen/dimen_60dp"
                app:btn_text="@string/jbl_UPDATE"
                tools:visibility="visible" />

            <com.wifiaudio.view.component.ComponentButton
                android:id="@+id/btnSecond"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginStart="@dimen/dimen_60dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="@dimen/dimen_60dp"
                android:visibility="gone"
                app:btn_text="@string/jbl_UPDATE_LATER"
                app:btn_type="btn_text_button"
                tools:visibility="visible" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>