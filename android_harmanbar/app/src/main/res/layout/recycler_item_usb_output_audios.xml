<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/round_rect_4_bg"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginVertical="@dimen/dimen_16dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_status"
                android:layout_width="@dimen/dimen_24dp"
                android:layout_height="@dimen/dimen_24dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_play_all_normal" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_16dp"
                android:layout_toRightOf="@+id/iv_status"
                android:backgroundTint="@color/bg_card"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tittle"
                    style="@style/Text_Caption_1_Strong"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="@color/fg_primary"
                    tools:text="2025-01-06_01.wave" />

                <TextView
                    android:id="@+id/tv_path"
                    style="@style/Text_Caption_2_Regular"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="@color/fg_secondary"
                    tools:text="/music/jbl0/" />


            </LinearLayout>

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:layout_toStartOf="@+id/iv_share"
                android:src="@drawable/ic_trashcan_delete" />

            <ImageView
                android:id="@+id/iv_share"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/icon_share" />
        </RelativeLayout>

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:maxHeight="4dp"
            android:minHeight="4dp"
            android:max="100"
            android:progress="0"
            tools:progress="10"
            android:progressDrawable="@drawable/seekbar_custom_progress"
            android:secondaryProgress="100"
            android:thumbOffset="4dp"
            android:thumb="@drawable/seekbar_custom_thumb"
            android:thumbTint="@color/fg_primary"
           />
    </LinearLayout>


</layout>