<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    tools:layout_gravity="bottom">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="308dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/radius_large_top_purple_1"
        android:backgroundTint="@color/bg_card"
        android:orientation="vertical">


        <TextView
            android:id="@+id/tv_desc"
            style="@style/Text_Caption_1_Strong"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingHorizontal="24dp"
            android:paddingVertical="24dp"
            android:text="@string/harmanbar_jbl_Restore_will_clear_all_your_personal_settings_on_this_product_and_reset_the_product_software_to_Out_"
            android:textColor="@color/fg_primary" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="vertical">

            <com.wifiaudio.view.component.ComponentButton
                android:id="@+id/bt_confirm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_60"
                app:btn_text="@string/DISCARD"
                app:btn_text_font="Button"
                app:btn_type="btn_warning" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="8dp" />

            <com.wifiaudio.view.component.ComponentButton
                android:id="@+id/bt_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_60"
                app:btn_text="@string/harmanbar_jbl_CANCEL"
                app:btn_text_font="Button"
                app:btn_type="btn_text_button" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="48dp" />

        </LinearLayout>

    </RelativeLayout>


</RelativeLayout>
