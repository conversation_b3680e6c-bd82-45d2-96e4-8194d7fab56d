<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/radius_large_top_bg_card"
    android:orientation="vertical"
    android:paddingBottom="48dp"
    tools:layout_gravity="bottom">

    <TextView
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:layout_marginEnd="@dimen/dimen_20dp"
        android:text="@string/pickup"
        android:textColor="@color/fg_primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/btnConfirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:src="@drawable/icon_check_fg_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llSwitch"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginHorizontal="36dp"
        android:layout_marginTop="80dp"
        android:background="@drawable/radius_round_stroke2_bg_on_card"
        android:padding="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvPassive"
            style="@style/Text_Brand_Feature"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/passive"
            tools:background="@drawable/radius_round_bg_inverse"
            tools:textColor="@color/fg_inverse" />


        <TextView
            android:id="@+id/tvActive"
            style="@style/Text_Brand_Feature"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/active"
            tools:textColor="@color/fg_secondary" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvPickupType"
        style="@style/Text_Body_Strong"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="42dp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:text="@string/pickup_types"
        android:textColor="@color/fg_secondary"
        app:drawableStartCompat="@drawable/ic_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llSwitch" />

    <ImageView
        android:id="@+id/ivExpand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_expand"
        app:layout_constraintBottom_toBottomOf="@+id/tvPickupType"
        app:layout_constraintStart_toEndOf="@+id/tvPickupType"
        app:layout_constraintTop_toTopOf="@id/tvPickupType" />

    <TextView
        android:id="@+id/tvPickupDesc"
        style="@style/Text_Body_Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="4dp"
        android:textColor="@color/fg_secondary"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tvPickupType"
        tools:text="@string/pickup_desc2"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>