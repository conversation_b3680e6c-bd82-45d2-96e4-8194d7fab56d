<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:orientation="vertical"
            android:padding="@dimen/dimen_16dp"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_16dp">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="vertical"
                android:padding="@dimen/dimen_16dp"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_16dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/radius_8_fg_primary">

                    <TextView
                        android:id="@+id/tv_filename"
                        style="@style/Text_Caption_2_Regular"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="12dp"
                        android:text="@string/file_name"
                        android:textColor="@color/fg_secondary"
                        android:textSize="10sp" />

                    <EditText
                        android:id="@+id/et_input_filename"
                        style="@style/Text_Body_Regular"
                        android:layout_width="match_parent"
                        android:layout_height="51dp"
                        android:background="@color/transparent"
                        android:layout_marginTop="@dimen/dimen_12dp"
                        android:hint="input filename"
                        android:maxLength="32"
                        android:maxLines="1"
                        android:paddingLeft="10dp"
                        android:text="BandBox_Trio_01"
                        android:textColor="@color/fg_primary"
                        android:textColorHint="@color/fg_primary"
                        android:textSize="@dimen/font_14sp" />

                    <ImageView
                        android:id="@+id/iv_clear"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/ic_close_fg_secondary" />
                </RelativeLayout>


                <TextView
                    android:id="@+id/tv_path"
                    style="@style/Text_Body_Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_marginVertical="@dimen/dimen_16dp"
                    android:drawableLeft="@drawable/ic_folder"
                    android:drawablePadding="5dp"
                    android:text="/Music/partyband/"
                    android:textColor="@color/fg_secondary"
                     />
            </LinearLayout>

            <com.wifiaudio.view.component.ComponentButton
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_60"
                android:layout_marginTop="40dp"
                app:btn_text="@string/harmanbar_devicelist_Save"
                app:btn_text_font="Button"
                app:btn_type="btn_regular" />

            <com.wifiaudio.view.component.ComponentButton
                android:id="@+id/bt_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_60"
                android:layout_marginTop="@dimen/dimen_16dp"
                app:btn_text="@string/discard"
                app:btn_text_font="Button"
                app:btn_type="btn_text_button" />

        </LinearLayout>
    </RelativeLayout>
</LinearLayout>