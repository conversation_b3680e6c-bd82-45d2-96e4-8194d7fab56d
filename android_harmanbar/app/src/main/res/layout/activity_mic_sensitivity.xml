<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="16dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginHorizontal="16dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rc_list_classify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@null"
            android:listSelector="@drawable/transparent" />
    </RelativeLayout>
    <Space
        android:layout_width="wrap_content"
        android:layout_height="28dp" />
    <TextView
        android:id="@+id/tv_content_title"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginVertical="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:gravity="center_vertical"
        android:text="Level"
        android:textColor="@color/fg_primary" />

    <!--default level layout-->
    <RelativeLayout
        android:id="@+id/layout_sensitivity_in_level"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/round_rect_12_bg_m1"
        android:backgroundTint="@color/bg_card">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rc_list_sensitivity_in_level"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@null"
            android:listSelector="@drawable/transparent" />
    </RelativeLayout>

    <!--others models layout-->
    <RelativeLayout
        android:id="@+id/layout_other_models"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:visibility="gone"
        android:background="@drawable/round_rect_12_bg_m1"
        android:backgroundTint="@color/bg_card">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rc_list_models"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@null"
            android:listSelector="@drawable/transparent" />
    </RelativeLayout>

    <!--db layout-->
    <LinearLayout
        android:id="@+id/layout_mic_sensitivity_in_dbv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/round_rect_12_bg_m1"
        android:backgroundTint="@color/bg_card"
        android:minHeight="40dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginVertical="@dimen/dimen_16dp">

            <TextView
                android:id="@+id/tv_dbv"
                style="@style/Text_Body_Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical"
                android:text="@string/level_low"
                android:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/tv_mid"
                style="@style/Text_Body_Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/fg_primary"
                android:layout_centerHorizontal="true"
                android:text="@string/level_mid"/>

            <TextView
                android:id="@+id/tv_db_value"
                style="@style/Text_Body_Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="8dp"
                android:gravity="center_vertical"
                android:text="@string/level_high"
                android:textColor="@color/fg_primary" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_left_db"
                style="@style/Text_Body_Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical"
                android:text="-40"
                android:visibility="gone"
                android:textColor="@color/fg_primary" />

            <com.harman.widget.ComponentControlSlider
                android:id="@+id/ccs_view"
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_weight="1"
                app:ccs_current_value="0"
                app:ccs_max_value="60"
                app:ccs_segment_count="0"
                app:ccs_type="continuedvalue"
                />

            <TextView
                android:id="@+id/tv_right_db"
                style="@style/Text_Body_Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:layout_alignParentStart="true"
                android:gravity="center_vertical"
                android:text="0"
                android:visibility="gone"
                android:textColor="@color/fg_primary" />
        </LinearLayout>
        <Space
            android:layout_width="wrap_content"
            android:layout_height="16dp" />
    </LinearLayout>
    <!--end-->

</LinearLayout>