<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="10dp" />

    <com.harman.partyband.widget.GuitarLooperAudioWaveView
        android:id="@+id/view_guitarlooper_audiowave_view"
        android:layout_width="match_parent"
        android:layout_height="187dp"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="10dp" />

    <com.harman.partyband.widget.GuitarLooperTimeScaleView
        android:id="@+id/view_timescale_view"
        android:layout_width="match_parent"
        android:layout_height="43dp"
        app:tsv_debug="false"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        app:tsv_max="60"
        android:visibility="gone"
        app:tsv_text_size="10sp" />

    <TextView
        android:id="@+id/tv_record_time"
        android:textColor="@color/fg_primary"
        android:textSize="@dimen/font_16sp"
        android:layout_width="wrap_content"
        android:textStyle="bold"
        android:layout_gravity="center_horizontal"
        android:layout_height="wrap_content"/>

    <com.harman.partyband.looper.LooperBarsView
        android:id="@+id/looper_bars_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/width_93"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dimen_20dp"/>

    <Space
        android:layout_width="wrap_content"
        android:layout_height="67dp" />

    <com.harman.partyband.widget.GuitarLooperCircleButton
        android:id="@+id/bt_guitarloopercircle_button"
        android:layout_width="160dp"
        android:layout_height="160dp"
        android:layout_gravity="center_horizontal"
        app:glcb_debug="false"
        app:glcb_type="ready" />
    <TextView
        android:id="@+id/tv_circle_but_type"
        android:textColor="@color/fg_primary"
        android:textSize="@dimen/font_14sp"
        android:layout_width="wrap_content"
        android:layout_marginTop="@dimen/dimen_20dp"
        android:layout_gravity="center_horizontal"
        android:layout_height="wrap_content"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_drum"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_12dp"
        android:layout_marginTop="@dimen/dimen_30dp"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:background="@drawable/radius_medium_bg_card">

        <TextView
            android:id="@+id/tv_looper_drum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/fg_primary"
            android:textSize="@dimen/font_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            android:text=""
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:textColor="@color/fg_primary"
            android:textSize="@dimen/font_14"
            android:background="@drawable/bg_on_card_radius_24"
            android:paddingHorizontal="@dimen/dimen_12dp"
            android:paddingVertical="@dimen/dimen_5dp"
            android:text="@string/text_add"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:layout_marginHorizontal="@dimen/dimen_8dp"
            android:layout_marginTop="@dimen/dimen_20dp"
            android:background="@drawable/radius_round_stroke2_bg_on_card"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_add"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dimen_12dp"
            android:padding="4dp">

            <Button
                android:id="@+id/btn_drum"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/radius_round_bg_inverse"
                android:backgroundTint="@color/white"
                android:text="@string/capital_drum"
                android:textStyle="bold"
                android:textColor="@color/fg_inverse" />


            <Button
                android:id="@+id/btn_metronome"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:textStyle="bold"
                android:background="@null"
                android:text="@string/capital_metronome"
                android:textColor="@color/fg_secondary"/>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_50dp"
        android:orientation="horizontal"
        android:visibility="gone"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/ll_undo"
            android:layout_width="@dimen/dimen_96dp"
            android:layout_height="@dimen/dimen_63dp"
            android:background="@drawable/radius_medium_bg_card"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/dimen_28dp"
                android:layout_height="@dimen/dimen_28dp"
                android:background="@drawable/icon_undo"/>

            <TextView
                android:id="@+id/tv_loop_undo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/undo"
                android:textColor="@color/fg_secondary"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:textSize="@dimen/font_10sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_play"
            android:layout_width="@dimen/dimen_96dp"
            android:layout_height="@dimen/dimen_63dp"
            android:background="@drawable/radius_medium_bg_card"
            android:gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_8dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_record_play"
                android:layout_width="@dimen/dimen_28dp"
                android:layout_height="@dimen/dimen_28dp"
                android:src="@drawable/icon_pause_filled"/>

            <TextView
                android:id="@+id/tv_loop_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/play"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:textColor="@color/fg_secondary"
                android:textSize="@dimen/font_10sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_delete"
            android:layout_width="@dimen/dimen_96dp"
            android:layout_height="@dimen/dimen_63dp"
            android:background="@drawable/radius_medium_bg_card"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/dimen_28dp"
                android:layout_height="@dimen/dimen_28dp"
                android:background="@drawable/icon_delete"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/discard"
                android:textColor="@color/fg_secondary"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:textSize="@dimen/font_10sp" />
        </LinearLayout>

    </LinearLayout>



</LinearLayout>