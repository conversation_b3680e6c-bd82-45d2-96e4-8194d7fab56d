<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/radius_large_top_bg_card"
    android:orientation="vertical"
    android:paddingBottom="48dp"
    tools:layout_gravity="bottom">

    <TextView
        android:id="@+id/tvPresetName"
        style="@style/Text_Title_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:text="@string/save_to_on_product_list"
        android:textColor="@color/fg_primary"
        app:drawableEndCompat="@mipmap/calm_radio_video_icon_download" />

    <TextView
        style="@style/Text_Caption_1_Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="4dp"
        android:text="@string/select_a_slot_to_replace"
        android:textColor="@color/fg_secondary" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <TextView
        android:id="@+id/tvDone"
        style="@style/Text_Button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_marginHorizontal="@dimen/dimen_60dp"
        android:layout_marginTop="32dp"
        android:background="@drawable/bg_white_radius_24"
        android:gravity="center"
        android:text="@string/harmanbar_jbl_DONE"
        android:textAllCaps="true"
        android:textColor="@color/fg_inverse" />

    <TextView
        android:id="@+id/tvCancel"
        style="@style/Text_Button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_48dp"
        android:layout_marginHorizontal="@dimen/dimen_60dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/cancel"
        android:textAllCaps="true"
        android:textColor="@color/fg_primary" />
</LinearLayout>