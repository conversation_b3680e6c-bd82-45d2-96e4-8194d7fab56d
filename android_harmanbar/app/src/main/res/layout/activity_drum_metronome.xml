<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_surface"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/appbar"
        layout="@layout/view_appbar"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_device"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintBottom_toTopOf="@+id/layout_view_style"
        app:layout_constraintTop_toBottomOf="@id/layout_title_bar">

        <ImageView
            android:id="@+id/ivSub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="20dp"
            android:src="@drawable/ic_round_sub"
            app:layout_constraintEnd_toStartOf="@+id/tvBpm"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvBpm"
            style="@style/Text_Heading_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:gravity="center"
            android:minWidth="60dp"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toBottomOf="@id/ivSub"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivSub"
            tools:text="120" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lavTapping"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/ivSub"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivSub"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/partyband/loading dots_fast.json"
            app:lottie_loop="true" />

        <ImageView
            android:id="@+id/ivAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:src="@drawable/ic_round_add"
            app:layout_constraintStart_toEndOf="@+id/tvBpm"
            app:layout_constraintTop_toTopOf="@+id/ivSub" />


        <TextView
            style="@style/Text_Body_Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="30dp"
            android:text="@string/bpm"
            android:textColor="@color/fg_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivSub" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clBpmSetting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.harman.partyband.widget.CircleSliderView
                android:id="@+id/csvBpm"
                android:layout_width="300dp"
                android:layout_height="300dp"
                app:csv_debug="false"
                app:csv_value="30"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivPlay"
                android:layout_width="28dp"
                android:layout_height="28dp"
                app:layout_constraintBottom_toBottomOf="@+id/csvBpm"
                app:layout_constraintEnd_toEndOf="@+id/csvBpm"
                app:layout_constraintStart_toStartOf="@+id/csvBpm"
                app:layout_constraintTop_toTopOf="@+id/csvBpm"
                app:layout_constraintVertical_bias="0.32"
                tools:src="@drawable/ic_mini_player_play" />

            <View
                android:id="@+id/vDivider"
                android:layout_width="130dp"
                android:layout_height="2dp"
                android:foreground="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="@+id/csvBpm"
                app:layout_constraintEnd_toEndOf="@+id/csvBpm"
                app:layout_constraintStart_toStartOf="@+id/csvBpm"
                app:layout_constraintTop_toTopOf="@+id/csvBpm"
                app:layout_constraintVertical_bias="0.485" />

            <ImageView
                android:id="@+id/ivPalm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_palm"
                app:layout_constraintBottom_toBottomOf="@+id/csvBpm"
                app:layout_constraintEnd_toEndOf="@+id/csvBpm"
                app:layout_constraintStart_toStartOf="@+id/csvBpm"
                app:layout_constraintTop_toTopOf="@+id/csvBpm"
                app:layout_constraintVertical_bias="0.66" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/btnDrum44"
            style="@style/Text_Body_Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56dp"
            android:background="@drawable/radius_round_stroke_fg_primary"
            android:gravity="center"
            android:paddingHorizontal="40dp"
            android:paddingVertical="8dp"
            android:textColor="@color/fg_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clBpmSetting"
            tools:text="4/4\nFloor" />

        <LinearLayout
            android:id="@+id/rgMetronome"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginTop="47dp"
            android:divider="@drawable/divider_h_8"
            android:dividerPadding="80dp"
            android:orientation="horizontal"
            android:showDividers="middle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clBpmSetting"
            tools:visibility="gone">

            <TextView
                android:id="@+id/rbBeat44"
                style="@style/Text_Body_Medium"
                android:layout_width="65dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="4/4"
                tools:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/rbBeat43"
                style="@style/Text_Body_Medium"
                android:layout_width="65dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="4/3"
                tools:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/rbBeat42"
                style="@style/Text_Body_Medium"
                android:layout_width="65dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="4/2"
                tools:textColor="@color/fg_primary" />

            <TextView
                android:id="@+id/rbBeat68"
                style="@style/Text_Body_Medium"
                android:layout_width="65dp"
                android:layout_height="match_parent"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                tools:background="@drawable/radius_round_bg_card"
                tools:text="6/8"
                tools:textColor="@color/fg_primary" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>