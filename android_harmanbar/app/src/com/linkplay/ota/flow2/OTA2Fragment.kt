package com.linkplay.ota.flow2

import android.animation.ValueAnimator
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import com.harman.WirelessConnector
import com.harman.bar.app.R
import com.harman.bean.AuthDevice
import com.harman.hkone.DeviceImageUtil
import com.harman.hkone.HarmanDeviceManager
import com.harman.hkone.model.BatteryInfo
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.ProductFeature
import com.linkplay.ota.model.OTAStatus
import com.linkplay.ota.model.UPNPSubscribeOTAStatus
import com.linkplay.ota.presenter.LinkplayOTA
import com.linkplay.ota.presenter.LinkplayOTAListener
import com.linkplay.ota.view.BaseOTAFragment
import com.linkplay.ota.view.DeviceUpgradeActivity
import com.linkplay.ota.view.FragOTAUpToDate
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.IInitView
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.presenter.autotrack.AutoTrackKit
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.GsonParseUtil
import com.wifiaudio.view.component.ComponentTextProgressBar
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLBatteryItem
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLBatteryItem.Companion.AC_MODE
import com.wifiaudio.view.pagesmsccontent.easylink.CONSTANTS.OTA_BATTERY_LEVEL_THRESHOLD
import config.AppLogTagUtil
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
class OTA2Fragment : BaseOTAFragment(), IInitView {

    var cview: View? = null
    var btm_layout: RelativeLayout? = null
    var id_progress: ProgressBar? = null
    var iv_device_logo: ImageView? = null
    var tv_remain: TextView? = null
    var img_hint: ImageView? = null
    var tv_info1: TextView? = null
    var tv_title: TextView? = null
    var tv_hint: TextView? = null
    var iv_battery_power: ImageView? = null
    var textProgressBar: ComponentTextProgressBar? = null

    private var otaDeviceItem: DeviceItem? = null

    private val TOTAL_PRECENT = 100 //最大进度值
    private var strUUID = "" //设备UUID
    private var bUpdateSuccess = false

    var linkplayOTA: LinkplayOTA? = null

    /**
     * 当FirmwareNewUpdater里doGetUpdateStatus()方法的status为null，或者response.body取到的值为“unknown command”的时候，
     * onUpdateListener将使用onUpdateNocmd()方法更新进度，而这个变量即为当前下载进度
     */
    private val uihd = Handler(Looper.myLooper()!!)

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.fragment_ota_2, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun onResume() {
        super.onResume()
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

    override fun onPause() {
        super.onPause()

        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this)
    }

    override fun initView() {

        otaDeviceItem = (activity as DeviceUpgradeActivity?)!!.deviceItem

//        btm_layout = cview?.findViewById(R.id.btm_layout)
        id_progress = cview?.findViewById(R.id.id_progress)
        tv_info1 = cview?.findViewById(R.id.tv_info1)
        tv_remain = cview?.findViewById(R.id.tv_time)
        img_hint = cview?.findViewById(R.id.img_hint)
        tv_hint = cview?.findViewById(R.id.tv_hint)
        iv_device_logo = cview?.findViewById(R.id.iv_device_logo)
        iv_battery_power = cview?.findViewById(R.id.iv_battery_power)
        textProgressBar = cview?.findViewById(R.id.textProgressBar)

        /*tv_title = cview?.findViewById(R.id.vtxt_title)
        tv_title?.apply {
            text = SkinResourcesUtils.getString("jbl_Updating")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

        }*/

        //提示
        tv_info1?.apply {
            text = SkinResourcesUtils.getString("jbl_product_will_be_updated_to_a_latest_software_to_ensure_better_user_experience")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        tv_hint?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        iv_device_logo?.apply {
            val imgName = otaDeviceItem?.let { DeviceImageUtil.getDeviceImgNameByDeviceItem(it) }
            val imgDraw = SkinResourcesUtils.getMipMapDrawable(imgName)
            if (imgDraw != null) setImageDrawable(imgDraw)
        }

        //剩余时间
        tv_remain?.text = "--"
        getDeviceBatterInfo()

    }

    private fun startOTA() {
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG startOTA")
        if (activity == null || activity !is DeviceUpgradeActivity) return

        val d = (activity as DeviceUpgradeActivity?)!!.deviceItem ?: return
        //block ota start for several times
        if (!TextUtils.isEmpty(strUUID)) return
        strUUID = d.uuid

        showCheckUpdateLoading(true)

//        tv_remain?.visibility = View.VISIBLE
        tv_remain?.visibility = View.GONE

        val timeout = 3 * 60 * 1000

        linkplayOTA = LinkplayOTA(d, object : LinkplayOTAListener {
            override fun otaStatusUpdated(otaStatus: OTAStatus) {
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG LinkplayOTAListener otaStatusUpdated otaStatus:$otaStatus")
                showCheckUpdateLoading(false)
                updateOtaStatus(otaStatus)
            }

            override fun otaSuccess(deviceItem: DeviceItem) {
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG LinkplayOTAListener otaSuccess deviceItem:$deviceItem")
                onOTASuccess(deviceItem)
            }

            override fun otaFailed(otaStatus: OTAStatus) {
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG LinkplayOTAListener otaFailed otaStatus:$otaStatus")
                onOTAFailed(otaStatus, timeout)
            }
        })

        linkplayOTA?.firmwareStartUpdate()
    }

    private fun onOTAFailed(otaStatus: OTAStatus, timeout: Int) {

        uihd.post {
            tv_remain?.visibility = View.GONE
        }

        showCheckUpdateLoading(false)

        if (otaStatus.status != OTAStatus.MV_UP_STATUS_OTA_FAILED) {
            deleteDevice()
        }

        otaFinished()

        var deviceItem = WAUpnpDeviceManager.me().getDeviceItemByMac(otaDeviceItem?.devStatus?.mac)

        when (otaStatus.status) {
            OTAStatus.MV_UP_STATUS_DOWNLOAD_FAILED -> {

            }

            OTAStatus.MV_UP_STATUS_WRITE_FAILED -> {

            }

            OTAStatus.MV_UP_STATUS_OTA_FAILED -> {

            }
        }

        if (deviceItem == null) {
            navigate(OTA2TimeoutFragment(), false)
        } else {
            val failedFragment = OTA2FailedFragment()
            failedFragment.setTimeout(timeout)
            navigate(failedFragment, false)
        }

//        if (otaStatus.status == OTAStatus.MV_UP_STATUS_WRITE_FAILED
//            || otaStatus.status == OTAStatus.MV_UP_STATUS_DOWNLOAD_FAILED
//            || otaStatus.status == OTAStatus.MV_UP_STATUS_OTA_FAILED
//        ) {
//
//        } else {
//
//        }
    }

    /**
     * 关闭页面
     */
    private fun otaFinished() {
        //AutoTrackKit.geInstance().ota(strUUID, destVersion, if (bUpdateSuccess) 1 else 0)
        strUUID = "";
    }

    private fun deleteDevice() {

        if (activity == null || activity !is DeviceUpgradeActivity) return
        val d = (activity as DeviceUpgradeActivity?)!!.deviceItem ?: return
        val devuuid = d.uuid
        WAApplication.me.reconfirmRecord(devuuid)
    }

    private fun startTVRemainAnimator(start: Int, end: Int, animTime: Long) {
        val valueAnimator = ValueAnimator.ofInt(start, end)
        valueAnimator.duration = animTime
        valueAnimator.addUpdateListener {
            val i = Integer.valueOf(it.animatedValue.toString())
            tv_remain?.text = String.format(SkinResourcesUtils.getString("jbl____MINUTE"), i)
        }
        valueAnimator.start()
    }

    private fun startProgressAnimator(start: Int, end: Int, animTime: Long, deviceItem: DeviceItem?) {

        val valueAnimator = ValueAnimator.ofInt(start, end)
        valueAnimator.duration = animTime
        valueAnimator.addUpdateListener {
            val i = Integer.valueOf(it.animatedValue.toString())
            id_progress?.progress = i
            textProgressBar?.setProgress(i.toFloat())

            if (i >= 100) {
                if (deviceItem == null) return@addUpdateListener
                WAApplication.me.deviceItem = WAUpnpDeviceManager.me().getDeviceItemByMac(deviceItem.devStatus.mac)

                uihd.post {


                    val vfrag = FragOTAUpToDate()
                    vfrag.otaDevice = deviceItem ?: WAApplication.me.deviceItem
                    vfrag.bOtaSuccess = true
                    navigate(vfrag, false)
                }

                bUpdateSuccess = true
                otaFinished()
            }
        }

        valueAnimator.start()
    }

    private fun updateSuccessUI(deviceItem: DeviceItem?) {

        val animRemainTimer: Long = 2000

        var txtRemain: String? = tv_remain?.text.toString()
        txtRemain = txtRemain?.replace(String.format(SkinResourcesUtils.getString("jbl____MINUTE"), ""), "")
            ?.replace(String.format(SkinResourcesUtils.getString("jbl____MINUTE"), ""), "")
            ?.trim()

        var timer: Int? = (if (TextUtils.isDigitsOnly(txtRemain)) txtRemain else "0")?.toInt()

        if (timer != null) {
            startTVRemainAnimator(timer, 1, animRemainTimer)
        }

        val animProgressTimer: Long = 2500

//        var tmpProgress: Int? = id_progress?.progress
        var tmpProgress: Int? = textProgressBar?.progress

        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG updateSuccessUI tmpProgress:$tmpProgress")
        if (tmpProgress != null) {
            startProgressAnimator(tmpProgress, 100, animProgressTimer, deviceItem)
        }
    }

    private fun onOTASuccess(deviceItem: DeviceItem?) {
        updateSuccessUI(deviceItem)
    }

    private fun showCheckUpdateLoading(shown: Boolean) {
        if (activity == null) return
        uihd.post {
            if (shown) {
                WAApplication.me.showProgDlg(activity, true, SkinResourcesUtils.getString("devicelist_Please_wait"))
            } else {
                WAApplication.me.showProgDlg(activity, false, null)
            }
        }
    }

    private fun updateOtaStatus(otaStatus: OTAStatus?) {
        if (otaStatus == null) return
        uihd.post {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG updateOtaStatus otaStatus: ${otaStatus.status}")
            when (otaStatus.status) {

                OTAStatus.MV_UP_STATUS_DOWNLOAD_START -> {
//                    tv_state?.text = SkinResourcesUtils.getString("devicelist_Download") + "..."
//                    tv_stage_status?.text =
//                        SkinResourcesUtils.getString("newadddevice_Stage") + ": 1/3"
                }

                OTAStatus.MV_UP_STATUS_WRITE_START -> {
//                    tv_state?.text = SkinResourcesUtils.getString("devicelist_Update") + "..."
//                    tv_stage_status?.text =
//                        SkinResourcesUtils.getString("newadddevice_Stage") + ": 2/3"
                }

                OTAStatus.MV_UP_STATUS_COMPLETE -> {
//                    tv_state?.text =
//                        SkinResourcesUtils.getString("devicelist_Device_Reboot") + "..."
//                    tv_stage_status?.text =
//                        SkinResourcesUtils.getString("newadddevice_Stage") + ": 3/3"
                }
            }

            if (otaStatus.getRemainTime() > 0) {
                var remain = otaStatus.getRemainTime() / 1000
                if (remain > 60) {
                    var min = remain / 60
                    //这里文字秒数是粗略计算，则整除后的剩余秒数不计
                    if (min >= 22)
                        min = 22
                    tv_remain?.text = String.format(SkinResourcesUtils.getString("jbl____MINUTE"), min)

                } else if (remain <= 60) {
                    remain = 1
                    tv_remain?.text = String.format(SkinResourcesUtils.getString("jbl____MINUTE"), remain)
                }
            }

            val progress = (otaStatus.downloadPercent + otaStatus.updatePercent + otaStatus.rebootPercent) / 3
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG updateOtaStatus downloadPercent: ${otaStatus.downloadPercent}")
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG updateOtaStatus updatePercent: ${otaStatus.updatePercent}")
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG updateOtaStatus rebootPercent: ${otaStatus.rebootPercent}")
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG updateOtaStatus progress: $progress")
            id_progress?.progress = progress
            textProgressBar?.setProgress(progress.toFloat())
        }
    }

    override fun bindSlots() {
//        btn_done?.setOnClickListener {
//            exitOTA()
//        }
    }

    override fun initUtils() {
        initNormalTheme()
        //背景色
//        updateHeaderBG(cview)
//        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private fun initNormalTheme() {
        val hintImg = resources.getDrawable(R.drawable.ic_round_warn_fg_secondary)
        //图片处理
        val hintWrap = SkinResourcesUtils.getSingleTintDrawable(
            WAApplication.me,
            hintImg,
            GlobalUIConfig.color_upgrade_hight
        )
        if (hintImg != null) {
            img_hint?.visibility = View.VISIBLE
            img_hint?.setImageDrawable(hintImg)
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun updateOTAEvent(otaStatus: UPNPSubscribeOTAStatus) {
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG UPNP:UpdateOTAStatus: $otaStatus")

        if (TextUtils.isEmpty(otaStatus.UUID)) {
            return
        }

        if (!TextUtils.equals(otaStatus.UUID, otaDeviceItem?.uuid)) {
            return
        }

        linkplayOTA?.updateFirmwareStatus(otaStatus.otaStatus)
    }

    private fun getDeviceBatterInfo() {
        val config = otaDeviceItem?.devStatus?.project?.let {
            AppConfigurationUtils.getModelConfigByModelName(it)
        }
        val supportBattery = config?.capability?.contains(ProductFeature.Battery) ?: false
        if (!supportBattery) {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG supportBattery:$supportBattery")
            tv_hint?.text = SkinResourcesUtils.getString("jbl_Do_NOT_unplug_power_cable_or_leave_the_app")
            uihd.post {
                AutoTrackKit.geInstance().updateDuration()
                startOTA()
            }
            return
        }
        var authDevice = AuthDevice(otaDeviceItem)
        otaDeviceItem?.apply {
            WirelessConnector.getDeviceBatteryStatus(authDevice, object : DeviceSettingActionCallback {
                override fun onSuccess(content: String?) {
                    LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG getBattery:Success:$content")
                    var batteryInfo = content?.let { GsonParseUtil.instance.fromJson(it, JBLBatteryItem::class.java) }
                    batteryInfo?.also {
                        doUpdateOtaHint(it)
                        if (isBatteryStatusMatched(it)) {
                            uihd.post {
                                AutoTrackKit.geInstance().updateDuration()
                                startOTA()
                            }
                        }
                    }
                }

                override fun onFailure(e: Throwable?) {
                    LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG getBattery:Failed:${e?.localizedMessage}")
                }
            })
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBatterInfoEvent(batteryInfo: BatteryInfo) {
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG onBatterInfoEvent:batteryInfo:$batteryInfo")
        if (otaDeviceItem == null || TextUtils.isEmpty(batteryInfo.crc)) return

        var crc = HarmanDeviceManager.getInstance().getCrc(otaDeviceItem)
        if (!TextUtils.equals(batteryInfo.crc, crc)) {
            LogsUtil.d(AppLogTagUtil.Firmware_TAG, "$TAG onBatterInfoEvent:device crc not match :$crc")
            return
        }
        doUpdateOtaHint(batteryInfo.itemList[0])
        if (isBatteryStatusMatched(batteryInfo.itemList[0])) {
            uihd.post {
                AutoTrackKit.geInstance().updateDuration()
                startOTA()
            }
        }
    }

    private fun doUpdateOtaHint(batteryInfo: JBLBatteryItem) {
        uihd.post {
            if (isBatteryStatusMatched(batteryInfo)) {
                // 80 | AC_MODE   80| DC_MODE   20 | AC_MODE
                tv_hint?.text = SkinResourcesUtils.getString("newStructure_Do_NOT_turn_off_product")
                /*id_progress?.visibility = View.VISIBLE
                tv_remain?.visibility = View.VISIBLE*/
                id_progress?.visibility = View.GONE
                tv_remain?.visibility = View.GONE
                iv_battery_power?.visibility = View.GONE
            } else {
                // 20 & DC_MODE
                tv_hint?.text = SkinResourcesUtils.getString("jbl_Plug_in_power_to_continue")
                id_progress?.visibility = View.GONE
                tv_remain?.visibility = View.GONE
                iv_battery_power?.visibility = View.VISIBLE
            }
        }
    }

    private fun isBatteryStatusMatched(batteryInfo: JBLBatteryItem): Boolean {
        return batteryInfo.dcAcMode == AC_MODE || batteryInfo.batteryCapacity > OTA_BATTERY_LEVEL_THRESHOLD
    }

    companion object {
        val TAG:String = OTA2Fragment::class.java.simpleName
    }
}