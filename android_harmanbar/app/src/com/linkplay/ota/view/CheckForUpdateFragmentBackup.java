package com.linkplay.ota.view;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Html;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.harman.bar.app.R;
import com.harman.hkone.DeviceImageUtil;
import com.linkplay.ota.flow2.OTA2Fragment;
import com.linkplay.ota.model.OTAStatus;
import com.skin.FontUtil;
import com.skin.SkinResourcesUtils;
import com.skin.font.FontUtils;
import com.skin.font.LPFontUtils;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.app.WAApplication;
import com.wifiaudio.model.DeviceItem;
import com.wifiaudio.model.DeviceProperty;
import com.wifiaudio.utils.MixTextImg;
import com.wifiaudio.utils.StringSpannableUtils;
import com.wifiaudio.utils.okhttp.HttpRequestUtils;
import com.wifiaudio.utils.okhttp.IOkHttpRequestCallback;
import com.wifiaudio.utils.okhttp.OkHttpResponseItem;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicInteger;

import config.AppLogTagUtil;
import config.GlobalUIConfig;
import config.LogTags;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2019/07/08 16:45
 * @Description: TODO{}
 */
public class CheckForUpdateFragmentBackup extends BaseOTAFragment {

    private static final String TAG = "LinkplayOTA";

    private TextView tvCheck, tvHint, tvToast;

    DeviceItem otaDevice = null;

    private int oldCheckCount = -1;//设备上线记录的状态

    private Timer checkTimer;

    private Handler uiHandler;

    ProgressBar progressBar;
    //temp solution about bug:https://jira.harman.com/jira/browse/HOP-6388
    private Boolean otaChecking = false;
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getActivity() != null && getActivity() instanceof DeviceUpgradeActivity) {
            otaDevice = ((DeviceUpgradeActivity) getActivity()).getDeviceItem();
//            otaDevice=new DeviceItem();
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_check_for_update_backup, null);
        LogsUtil.i(LogTags.UI, "page=" + getClass().getSimpleName() + ":onCreate");
        tvCheck = view.findViewById(R.id.tv_check);
        tvHint = view.findViewById(R.id.tv_hint);
        tvToast = view.findViewById(R.id.tv_toast);

        if (tvCheck != null) {
           tvCheck.setVisibility(View.GONE);
        }

//        updateTheme();
//        updateFragmentBG(view);

        if (tvHint != null) {
            tvHint.setText(SkinResourcesUtils.getString("jbl_Checking_the_latest_firmware__it_may_take_20_seconds_"));
//            tvHint.setTextColor(GlobalUIConfig.color_ez_normal);
//            tvHint.setAlpha(0.85f);
            tvHint.setTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular));
        }

        //#target: bHarmanDemo



        TextView vtxt_title = view.findViewById(R.id.vtxt_title);
        if (vtxt_title != null) {
            vtxt_title.setText(R.string.jbl_Check_for_Update);
//            vtxt_title.setTextColor(GlobalUIConfig.color_ez_normal);
            vtxt_title.setTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold));
        }

        progressBar = view.findViewById(R.id.check_progress);
        TextView tv_checking = view.findViewById(R.id.tv_checking);

        ImageView iv_device_logo = view.findViewById(R.id.iv_device_logo);

        if (progressBar != null) {
            progressBar.setMax((TIME_INTERVAL * MAX_TIME) / 1000);
            progressBar.setProgress(0);
        }

        if (iv_device_logo != null) {
            if (otaDevice != null) {
                String imgName = DeviceImageUtil.INSTANCE.getDeviceImgNameByDeviceItem(otaDevice);
                Drawable imgDraw = SkinResourcesUtils.getMipMapDrawable(imgName);
                if (imgDraw != null) iv_device_logo.setImageDrawable(imgDraw);
            }
        }

        if (tv_checking != null) {
            tv_checking.setText(SkinResourcesUtils.getString("jbl_CHECKING"));
//            tv_checking.setTextColor(WAApplication.mResources.getColor(R.color.fg_disable));
            tv_checking.setTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold));
        }
        updateContainerBG(view);

        //#targetend


        LPFontUtils.getInstance().setStyle(tvCheck, LPFontUtils.LP_Enum_Text_Type.Text_Body_Title);
        LPFontUtils.getInstance().setStyle(tvToast, LPFontUtils.LP_Enum_Text_Type.Text_Body_Normal);

        return view;
    }

    private void updateCheckingProgress(int progress) {

        if (progressBar == null)
            return;

        progressBar.setProgress(progress);
    }

    private void updateTheme() {

        if (tvCheck != null)
            tvCheck.setTextColor(GlobalUIConfig.color_ez_normal);

        if (tvHint != null)
            tvHint.setTextColor(GlobalUIConfig.color_ez_gray);
    }

    private void setLabelHint() {

        String strHint = SkinResourcesUtils.getString("newadddevice_It_may_take_____");

        String fontColor = MixTextImg.RGBA2TxtRGB(WAApplication.me.getColor(R.color.fg_activate));

        String strTime = SkinResourcesUtils.getString("newadddevice_20_s");


        CharSequence text = Html.fromHtml(String.format(strHint,
                "<font color='" + fontColor + "'>" + strTime + "</font>"));

        StringSpannableUtils stringUtil = new StringSpannableUtils();
        stringUtil.setTextBody(text.toString());
        stringUtil.setTextHighlight(strTime, WAApplication.me.getColor(R.color.fg_activate));
        stringUtil.setUnderlineText(false);
        stringUtil.setSpannableString(null);

        FontUtil.setText(tvHint, stringUtil.getSpannableString(), FontUtil.MODE_CUSTOM_TWO);
        tvHint.setHighlightColor(Color.TRANSPARENT);
        tvHint.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    public void onResume() {
        super.onResume();
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "check for update onResume otaChecking:" + otaChecking);
        if (otaChecking) return;

        if (otaDevice == null || otaDevice.devStatus == null) {
            checkFWUpdateInitially();
            return;
        }

        tvToast.setVisibility(View.INVISIBLE);
        oldCheckCount = otaDevice.devStatus.update_check_count;

        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "check for update, oldCheckCount:" + oldCheckCount);

        if (uiHandler == null) {
            uiHandler = new Handler(Looper.getMainLooper());
        }

        if (DeviceProperty.DEV_STATE_OTA.equals(otaDevice.devStatus.dev_state)) {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "dev_state:ota ---> ota process");
            otaProgress();// dev_state 为 ota
            return;
        }

        checkFWUpdateInitially();
    }
    private void checkFWUpdateInitially() {
        if (checkTimer == null) {
            checkTimer = new Timer();
        }
        checkTimer.schedule(new TimerTask() {

            AtomicInteger atomic = new AtomicInteger(1);

            @Override
            public void run() {
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "check for update:" + atomic.get());
                otaChecking = true;
                if (atomic.getAndAdd(1) > MAX_TIME) {
                    if (otaDevice != null && otaDevice.devStatus != null && otaDevice.devStatus.getInternet() == 1) {
                        nextStep();
                    } else {
                        timeout();
                    }
                } else {
                    if (lastCheckedOtaStatus != null && lastCheckedOtaStatus.getStatus() != OTAStatus.MV_UP_STATUS_UNKNOWN && atomic.get() <= 12) {
                        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "ota status change: set40s waiting:" + atomic.get() + " status: " + lastCheckedOtaStatus.getStatus());
                        atomic.set(12);
                    } else if (lastCheckedOtaStatus != null && lastCheckedOtaStatus.getStatus() == OTAStatus.MV_UP_STATUS_UNKNOWN && atomic.get() >= 12) {
                        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "ota status change: timeout:" + atomic.get() + " status: " + lastCheckedOtaStatus.getStatus());
                        timeout();
                    } else {
                        //#target: bHarmanDemo
                        updateCheckingProgress((atomic.get() * TIME_INTERVAL) / 1000);
                        //#targetend

                        getOtaStatus(otaDevice);
                    }
                }
            }
        }, 0, TIME_INTERVAL);
    }


    private void cancelTimer() {
        if (checkTimer != null) {
            checkTimer.cancel();
            checkTimer = null;
        }

        if (uiHandler != null)
            uiHandler.removeCallbacksAndMessages(null);
    }


    private static final int MAX_TIME = 20;
    private static final int TIME_INTERVAL = 5000;


    @Override
    public void onPause() {
        super.onPause();
        otaChecking = false;
        cancelTimer();//onPause
        if (uiHandler != null) {
            uiHandler.removeCallbacksAndMessages(null);
            uiHandler = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

    }

    private OTAStatus lastCheckedOtaStatus;

    private void getOtaStatus(DeviceItem deviceItem) {
        if(deviceItem==null) {
            return;
        }
        HttpRequestUtils requestUtils = HttpRequestUtils.getRequestUtils(deviceItem);

        String url = HttpRequestUtils.getRequestPrefix(deviceItem) + "getOtaStatus";

        requestUtils.get(url, new IOkHttpRequestCallback() {

            @Override
            public void onSuccess(Object response) {
                super.onSuccess(response);

                if (response == null
                        || !(response instanceof OkHttpResponseItem)) {
                    onFailure(new Exception("err"));
                    return;
                }

                OkHttpResponseItem responseItem = (OkHttpResponseItem) response;
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "getOtaStatus onSuccess " + responseItem.body);

                try {
                    JSONObject obj = new JSONObject(responseItem.body);

                    if (obj.has("ota_status")) {
                        OTAStatus otaStatus = OTAStatus.convert(obj.getString("ota_status"));
                        lastCheckedOtaStatus = otaStatus;
                        if (otaStatus.getStatus() == OTAStatus.MV_UP_STATUS_UNKNOWN) {
                            //还未检测到新版本
                        } else if(otaStatus.getStatus() == OTAStatus.MV_UP_STATUS_DOWNLOAD_START){
                            otaProgress();//检测到新版本，开始升级
                        } else if(otaStatus.getStatus() == 3 || otaStatus.getStatus() == 8){
                            nextStep();//检测已是最新版本
                            //@for test
//                            uiHandler.postDelayed(()-> nextStep(),2000);
                        }
                    }

                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Exception e) {
                super.onFailure(e);

                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "getOtaStatus onFailure " + e.getLocalizedMessage());
                failed();
            }
        });
    }


    private void otaProgress() {
        cancelTimer();//otaProcess

        if(uiHandler == null){
            return;
        }

        uiHandler.post(() -> {
            if(tvToast != null) {
                tvToast.setVisibility(View.INVISIBLE);
                tvToast.setText("");
            }
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "ota page");

            navigate(new OTA2Fragment(), false);
        });
    }

    private void nextStep() {
        cancelTimer();//nextStep

        if(uiHandler == null) return;

        uiHandler.post(() -> {
            if(tvToast != null) {
                tvToast.setVisibility(View.INVISIBLE);
                tvToast.setText("");
            }
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "next page");
            rename();
        });
    }

    private void timeout() {
        cancelTimer();//timeout
        if(uiHandler == null) return;

        uiHandler.post(() -> {
            if(tvToast != null) {
                tvToast.setVisibility(View.INVISIBLE);
                tvToast.setText("");
            }
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "timeout next page");
            rename();
        });
    }

    /**
     * 一切失败结束该页面，跳到下一页
     */
    private void failed() {
        rename();
    }

    private void rename() {
        otaChecking = false;
        if (getActivity() == null) return;

        //#target: bHarmanDemo



        updateCheckingProgress((MAX_TIME * TIME_INTERVAL) / 1000);
        FragOTAUpToDate vfrag = new FragOTAUpToDate();
        vfrag.setOtaDevice(otaDevice);
        vfrag.setBOtaSuccess(false);
        navigate(vfrag, false);

        //#targetend
    }

}
