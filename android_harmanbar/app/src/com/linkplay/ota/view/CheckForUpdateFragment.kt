package com.linkplay.ota.view

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView



import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil.getDeviceImgNameByDeviceItem
import com.linkplay.ota.flow2.OTA2Fragment
import com.linkplay.ota.model.OTAStatus
import com.skin.FontUtil
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DeviceProperty
import com.wifiaudio.utils.MixTextImg
import com.wifiaudio.utils.StringSpannableUtils
import com.wifiaudio.utils.okhttp.HttpRequestUtils
import com.wifiaudio.utils.okhttp.IOkHttpRequestCallback
import com.wifiaudio.utils.okhttp.OkHttpResponseItem
import com.wifiaudio.view.component.CircleProgress
import config.AppLogTagUtil
import config.LogTags
import org.json.JSONException
import org.json.JSONObject
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.atomic.AtomicInteger

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2019/07/08 16:45
 * @Description: TODO{}
 */
class CheckForUpdateFragment : BaseOTAFragment() {

    private var tvHint: TextView? = null
    var otaDevice: DeviceItem? = null
    private var oldCheckCount = -1 //设备上线记录的状态
    private var checkTimer: Timer? = null
    private var uiHandler: Handler? = null
    var progressBar: ProgressBar? = null
    var progressCheck: ProgressBar? = null
    private var circleProgress: CircleProgress? = null

    //temp solution about bug:https://jira.harman.com/jira/browse/HOP-6388
    private var otaChecking = false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity != null && activity is DeviceUpgradeActivity) {
            otaDevice = (activity as DeviceUpgradeActivity?)!!.deviceItem
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.fragment_check_for_update, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")

        tvHint = view.findViewById(R.id.tv_hint)

        tvHint?.apply {
            text = SkinResourcesUtils.getString("jbl_Checking_the_latest_firmware__it_may_take_20_seconds_")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        progressBar = view.findViewById(R.id.progressbar)
        progressCheck = view.findViewById(R.id.check_progress)
        circleProgress = view.findViewById(R.id.circleProgress)
        progressBar?.apply {
            visibility = View.GONE
        }

        progressCheck?.apply {
            visibility = View.GONE
        }
        progressCheck?.apply {
            max = TIME_INTERVAL * MAX_TIME / 1000
            progress = 0
        }
        circleProgress?.apply {
            val maxProgress = TIME_INTERVAL * MAX_TIME / 1000
            setMaxProgress(maxProgress.toFloat())
            setProgress(0f, true)
        }

        val ivDeviceLogo = view.findViewById<ImageView>(R.id.iv_device_logo)
        ivDeviceLogo?.apply {
            if (otaDevice != null) {
                val imgName = getDeviceImgNameByDeviceItem(otaDevice)
                val imgDraw = SkinResourcesUtils.getMipMapDrawable(imgName)
                if (imgDraw != null) setImageDrawable(imgDraw)
            }
        }

        updateContainerBG(view)

        return view
    }

    private fun updateCheckingProgress(progress: Int) {
        if (progressCheck == null) return
        progressCheck?.progress = progress
        circleProgress?.apply {
            setProgress(progress.toFloat(), true)
        }
    }

    private fun setLabelHint() {
        val strHint = SkinResourcesUtils.getString("newadddevice_It_may_take_____")
        val fontColor = MixTextImg.RGBA2TxtRGB(WAApplication.me.getColor(R.color.fg_activate))
        val strTime = SkinResourcesUtils.getString("newadddevice_20_s")
        val text: CharSequence = Html.fromHtml(
            String.format(strHint, "<font color='$fontColor'>$strTime</font>"), Html.FROM_HTML_MODE_COMPACT
        )
        val stringUtil = StringSpannableUtils()
        stringUtil.setTextBody(text.toString())
        stringUtil.setTextHighlight(strTime, WAApplication.me.getColor(R.color.fg_activate))
        stringUtil.setUnderlineText(false)
        stringUtil.setSpannableString(null)
        FontUtil.setText(tvHint, stringUtil.spannableString, FontUtil.MODE_CUSTOM_TWO)
        tvHint?.highlightColor = Color.TRANSPARENT
        tvHint?.movementMethod = LinkMovementMethod.getInstance()
    }

    override fun onResume() {
        super.onResume()
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG check for update onResume otaChecking:$otaChecking")
        if (otaChecking) return
        if (otaDevice == null || otaDevice!!.devStatus == null) {
            checkFWUpdateInitially()
            return
        }
        oldCheckCount = otaDevice!!.devStatus.update_check_count
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG check for update, oldCheckCount:$oldCheckCount")
        if (uiHandler == null) {
            uiHandler = Handler(Looper.getMainLooper())
        }
        if (DeviceProperty.DEV_STATE_OTA == otaDevice!!.devStatus.dev_state) {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG dev_state:ota ---> ota process")
            otaProgress() // dev_state 为 ota
            return
        }
        checkFWUpdateInitially()
    }

    private fun checkFWUpdateInitially() {
        if (checkTimer == null) {
            checkTimer = Timer()
        }
        checkTimer?.schedule(object : TimerTask() {
            var atomic = AtomicInteger(1)
            override fun run() {
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG check for update:${atomic.get()}")
                LogsUtil.d(AppLogTagUtil.Firmware_TAG, "$TAG check for update OTAStatus:$lastCheckedOtaStatus")
                otaChecking = true
                if (atomic.getAndAdd(1) > MAX_TIME) {
                    if (otaDevice != null && otaDevice!!.devStatus != null && otaDevice!!.devStatus.internet == 1) {
                        nextStep()
                    } else {
                        timeout()
                    }
                } else {
                    if (lastCheckedOtaStatus != null && lastCheckedOtaStatus!!.status != OTAStatus.MV_UP_STATUS_UNKNOWN && atomic.get() <= 12) {
                        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG ota status change: set40s waiting:${atomic.get()}, status:${lastCheckedOtaStatus?.status}")
                        atomic.set(12)
                    } else if (lastCheckedOtaStatus != null && lastCheckedOtaStatus!!.status == OTAStatus.MV_UP_STATUS_UNKNOWN && atomic.get() >= 12) {
                        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG ota status change: timeout: ${atomic.get()}, status: ${lastCheckedOtaStatus?.status}")
                        timeout()
                    } else {
                        //#target: bHarmanDemo
                        updateCheckingProgress(atomic.get() * TIME_INTERVAL / 1000)
                        //#targetend
                        getOtaStatus(otaDevice)
                    }
                }
            }
        }, 0, TIME_INTERVAL.toLong())
    }

    private fun cancelTimer() {
        if (checkTimer != null) {
            checkTimer?.cancel()
            checkTimer = null
        }
        if (uiHandler != null) uiHandler?.removeCallbacksAndMessages(null)
    }

    override fun onPause() {
        super.onPause()
        otaChecking = false
        cancelTimer() //onPause
        if (uiHandler != null) {
            uiHandler?.removeCallbacksAndMessages(null)
            uiHandler = null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    private var lastCheckedOtaStatus: OTAStatus? = null
    private fun getOtaStatus(deviceItem: DeviceItem?) {
        if (deviceItem == null) {
            return
        }
        val requestUtils = HttpRequestUtils.getRequestUtils(deviceItem)
        val url = HttpRequestUtils.getRequestPrefix(deviceItem) + "getOtaStatus"
        requestUtils[url, object : IOkHttpRequestCallback() {
            override fun onSuccess(response: Any) {
                super.onSuccess(response)
                if (response !is OkHttpResponseItem) {
                    onFailure(Exception("err"))
                    return
                }
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG getOtaStatus onSuccess " + response.body)
                try {
                    val obj = JSONObject(response.body)
                    if (obj.has("ota_status")) {
                        val otaStatus = OTAStatus.convert(obj.getString("ota_status"))
                        lastCheckedOtaStatus = otaStatus
                        when (otaStatus.status) {
                            OTAStatus.MV_UP_STATUS_UNKNOWN -> {
                                //还未检测到新版本
                            }

                            OTAStatus.MV_UP_STATUS_DOWNLOAD_START -> {
                                otaProgress() //检测到新版本，开始升级
                            }

                            3, 8 -> {
                                nextStep() //检测已是最新版本
                                // uiHandler.postDelayed(()-> nextStep(),2000);
                            }
                        }
                    }
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
            }

            override fun onFailure(e: Exception) {
                super.onFailure(e)
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG getOtaStatus onFailure " + e.localizedMessage)
                failed()
            }
        }]
    }

    private fun otaProgress() {
        cancelTimer() //otaProcess
        if (uiHandler == null) {
            return
        }
        uiHandler?.post {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG ota page")
            navigate(OTA2Fragment(), false)
        }
    }

    private fun nextStep() {
        cancelTimer() //nextStep
        if (uiHandler == null) return
        uiHandler?.post {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG next page")
            rename()
        }
    }

    private fun timeout() {
        cancelTimer() //timeout
        if (uiHandler == null) return
        uiHandler?.post {
            LogsUtil.i(AppLogTagUtil.Firmware_TAG, "$TAG timeout next page")
            rename()
        }
    }

    /**
     * 一切失败结束该页面，跳到下一页
     */
    private fun failed() {
        rename()
    }

    private fun rename() {
        otaChecking = false
        if (activity == null) return

        //#target: bHarmanDemo

        updateCheckingProgress(MAX_TIME * TIME_INTERVAL / 1000)
        val vfrag = FragOTAUpToDate()
        vfrag.otaDevice = otaDevice
        vfrag.bOtaSuccess = false
        navigate(vfrag, false)
    }

    companion object {
        private val TAG = CheckForUpdateFragment::class.simpleName
        private const val MAX_TIME = 20
        private const val TIME_INTERVAL = 5000
    }
}