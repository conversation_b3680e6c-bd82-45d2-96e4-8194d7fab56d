package com.linkplay.ble

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil
import com.harman.widget.DotLoadingImageView
import com.harmanbar.ble.model.HBApItem
import com.harmanbar.ble.utils.BLEProfile
import com.linkplay.ota.view.DeviceUpgradeActivity
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.service.WASlaveListDeviceManager
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.BLEConnectionCallback
import com.wifiaudio.utils.device.APInfoUtil
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import config.LogTags
import java.util.*


class FragBLEResearchingWiFi : FragBLEBase() {

    private val TAG = "FragBLEResearchingWiFi"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var progressbar: DotLoadingImageView? = null
    private var tv_label: TextView? = null
    private var iv_back: ImageView? = null

    private var ip: String? = null
    private var uuid:String? = null

    private var currApItem: HBApItem? = null

    var checkDevIP: String? = null;
    var checkDevUUID: String? = null
    var checkDevMac: String? = null
    var errorMsg: String = ""
    var errorCode: Int = 0

    //check online Timer
    private var checkOnlineTimer: Timer? = null

    private val PAIR_SUCCESS = 2 //BLE pair success
    private val PAIR_FAILED = 3 //BLE pair failed

    private val bleHandler: Handler = object : Handler(Looper.myLooper()!!) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {

                PAIR_SUCCESS -> if (activity != null && isAdded) {

                    if(activity == null)
                        return

                    val intent = Intent(activity, DeviceUpgradeActivity::class.java)
                    intent.putExtra(
                        DeviceUpgradeActivity.LINKPLAY_OTA_VIEW,
                        DeviceUpgradeActivity.LINKPLAY_CHECK_FOR_UPDATE
                    )
                    intent.putExtra(DeviceUpgradeActivity.LINKPLAY_OTA_FROM_SETUP, true)
                    startActivity(intent)

                    activity?.finish()
                }

                PAIR_FAILED -> {

                    activity?.finish()
                }
            }
        }
    }

    fun setApItem(apItem: HBApItem) {
        currApItem = apItem
    }

    fun setConnectDeviceInfo(ip: String?, uuid: String?) {
        this.ip = ip
        this.uuid = uuid
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        cview = inflater.inflate(R.layout.ble_researching_wifi_view, null)

        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun initView() {
        super.initView()

        progressbar = cview?.findViewById(R.id.progressbar)
        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)
        iv_back = cview?.findViewById(R.id.iv_back)

        progressbar?.visibility = View.VISIBLE
        progressbar?.start()

        /*setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Connecting"))
        bindHeaderImageViewBtns(cview)*/

        tv_label?.apply {
            text = SkinResourcesUtils.getString("jbl_Please_wait_while_the_connection_is_completed__This_might_take_a_minute_")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        iv_back?.setOnClickListener {
            onClickNext()
        }

        val imageID = currBLEDevice?.let { DeviceImageUtil.getBLEDeviceImageByPid(it.pid.toString(16), it.colorID.toString()) }
        iv_ble_device?.also { Glide.with(WAApplication.me.applicationContext).load(imageID).into(it) }


    }

    override fun initUtils() {
        super.initUtils()

        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private var startConnect = false
    private fun connectWiFi() {
        if (startConnect) return
        startConnect = true

        ip?.let { uuid?.let { it1 -> checkDeviceAfterBle(it, it1) } }
    }

    override fun onResume() {
        super.onResume()
        connectWiFi()
    }

    override fun onPause() {
        super.onPause()
        progressbar?.stop()
    }

    private fun checkDeviceAfterBle(ip: String, uuid: String) {

        val startTime = System.currentTimeMillis()
        checkOnlineTimer = Timer()
        checkOnlineTimer?.schedule(object : TimerTask() {
            override fun run() {
                val curTime = System.currentTimeMillis()

                if (curTime - startTime > BLEProfile.BLE_CHECK_TIME_OUT) {
                    checkFailed()
                    return
                }

                /**
                 * 有可能当前设备是子设备，在当前网络下重复配网后，
                 * 如果子设备不掉线，在主设备中找不到设备时，需要去子设备中查找是否存在
                 */
                var deviceItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)

                if (deviceItem == null) deviceItem = WAUpnpDeviceManager.me().getDeviceItemByIP(ip)
                if (deviceItem == null) deviceItem =
                    WAUpnpDeviceManager.me().getDeviceItemByMac(checkDevMac)

                if (deviceItem != null) {
                    //成功后设置deviceItem
                    (activity as LinkDeviceAddActivity?)?.deviceItem = deviceItem

                    deviceItem?.uuid?.let { APInfoUtil.saveDeviceOOBERecord(it, 1) }

                    checkSuccess()
                } else {
                    deviceItem = WASlaveListDeviceManager.me().getDeviceItemByuuid(uuid)
                    if (deviceItem != null && activity != null && activity is LinkDeviceAddActivity) {
                        (activity as LinkDeviceAddActivity?)?.deviceItem = deviceItem

                        deviceItem?.uuid?.let { APInfoUtil.saveDeviceOOBERecord(it, 1) }

                        checkSuccess()
                    }
                }
            }
        }, 500, 1000)
    }

    private fun cancelCheckTimer() {
        if (checkOnlineTimer != null) {
            checkOnlineTimer!!.cancel()
            checkOnlineTimer = null
        }
    }

    private fun checkFailed() {
        if (activity == null) return
        cancelCheckTimer()
        errorCode = BLEConnectionCallback.ERROR_02
        errorMsg = SkinResourcesUtils.getString("BLE_WIFI_connection_timeout")
        bleHandler.sendEmptyMessage(PAIR_FAILED)
    }

    private fun checkSuccess() {
        cancelCheckTimer()

        val delayTimer = 3 * 1000
        bleHandler.postDelayed(
            Runnable { bleHandler.sendEmptyMessage(PAIR_SUCCESS) },
            delayTimer.toLong()
        )
    }

    override fun onClickNext() {
        super.onClickNext()
    }
}