package com.linkplay.ble

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.harman.bar.app.R
import com.harmanbar.ble.model.HBApItem
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.AssetsProjectConfigParse.Companion.instance
import com.wifiaudio.utils.BLEConnectionCallback
import com.wifiaudio.utils.FragmentHelper
import com.wifiaudio.utils.MixTextImg
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import config.LogTags

class FragBLEConnectFailed : FragBLEBase() {
    private val TAG = "FragBLEConnectFailed"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var btnConnect: Button? = null
    private var btnFAQ: TextView? = null
    private var layoutFAQ: ConstraintLayout? = null
    private var tv_label: TextView? = null
    private var currAPItem: HBApItem? = null
    private var errorCode: Int = 0
    private var errorMsg: String = ""
    private var iv_close: ImageView? = null

    fun setErrorMsg(errorMsg: String?) {
        this.errorMsg = errorMsg!!
    }

    fun setErrorCode(code: Int) {
        this.errorCode = code
    }

    fun setCurrAPItem(apItem: HBApItem) {
        currAPItem = apItem
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.ble_connect_failed_view, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreateView errorCode = $errorCode , currAPItem = $currAPItem")
        initView()
        bindSlots()
        initUtils()
        return cview
    }

    override fun initView() {
        super.initView()

        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)
        btnConnect = cview?.findViewById(R.id.btn_connect)
        btnFAQ = cview?.findViewById(R.id.btn_faq)
        layoutFAQ = cview?.findViewById(R.id.layout_faq)

        /*setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Connect_Failed"))
        bindHeaderImageViewBtns(cview)
        setHeaderLeftBtnStatus(cview, true)*/

        iv_close = cview?.findViewById(R.id.iv_close)
        iv_close?.setOnClickListener {
            onClickNext()
        }

        val fontColor = MixTextImg.RGBA2TxtRGB(WAApplication.me.getColor(R.color.fg_primary))
        var info = "<font color='$fontColor'>${currAPItem?.displaySSID}</font>"

        var strLabel: String = ""
        if (BLEConnectionCallback.ERROR_04 == errorCode) {
            strLabel = String.format(SkinResourcesUtils.getString("jbl_Invalid_password_for_____please_retry_"), "<b>$info</b>")
            tv_label?.text = Html.fromHtml(strLabel, Html.FROM_HTML_MODE_COMPACT)
            layoutFAQ?.apply {
                visibility = View.GONE
            }

        } else {
            strLabel = SkinResourcesUtils.getString("jbl_Sorry__we_are_unable_to_connect_your_speaker_to_Wi_Fi_")
            tv_label?.text = strLabel
            layoutFAQ?.apply {
                visibility = View.VISIBLE
            }
            btnFAQ?.apply {
                text = SkinResourcesUtils.getString("jbl_GET_HELP")
            }
        }

        /*var imgID = SkinResourcesID.getDrawableId("svg_icon_no_wifi")
        Glide.with(WAApplication.me.applicationContext).load(imgID).into(iv_ble_device)*/

        iv_ble_device?.apply {
            val drawable = SkinResourcesUtils.getDrawable("svg_icon_no_wifi")
            setImageDrawable(drawable)
        }

        btnConnect?.text = SkinResourcesUtils.getString("jbl_try_again")


    }

    override fun bindSlots() {
        super.bindSlots()

        ViewUtil.setOnClickListener(btnConnect) {
            FragmentHelper.popBack(activity)
            val frag = FragBLEInputPWD()
            currBLEDevice?.let { frag.currBLEDevice = it }
            (activity as LinkDeviceAddActivity).makeLinkWizardPageSwitch(frag, false)
        }

        layoutFAQ?.setOnClickListener {

            var linkUrl = instance.getValueByKey("harmanbar_get_help_1_url")

            if (TextUtils.isEmpty(linkUrl))
                return@setOnClickListener

            val intent = Intent()
            intent.action = Intent.ACTION_VIEW
            val url = Uri.parse(linkUrl)
            intent.data = url
            startActivity(intent)
        }
    }

    override fun initUtils() {
        super.initUtils()
        updateTheme()
        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private fun updateTheme() {

        tv_label?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        btnConnect?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
        }

        btnFAQ?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
        }
    }

    override fun onClickNext() {
        super.onClickNext()
    }
}