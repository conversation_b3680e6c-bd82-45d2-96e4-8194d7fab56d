package com.linkplay.ble

import android.animation.Animator
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Html
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil
import com.harmanbar.ble.model.HBApItem
import com.linkplay.ota.view.DeviceUpgradeActivity
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.MixTextImg
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.utils.WifiResultsUtil
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import config.LogTags

class FragBLEConnectSuccess : FragBLEBase() {
    private val TAG = "FragBLEConnectSuccess"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var btnConnect: Button? = null
    private var tv_label: TextView? = null
    private var currApItem: HBApItem? = null
    private var lt_anim_view: LottieAnimationView? = null
    private var iv_close: ImageView? = null

    private var ip: String = ""
    private var uuid: String = ""
    var checkDevMac: String? = null

    private val CHECK_READY = 0
    private val CHECK_OTA = 1
    private val CHANGE_WIFI = 2

    private var uihd = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)

            if (msg.what == CHECK_READY) {
                if (TextUtils.equals(WifiResultsUtil.getWiFiSSID(), currApItem!!.displaySSID) || WAUpnpDeviceManager.me().getDeviceItemByMac(checkDevMac) != null) {
                    sendEmptyMessageDelayed(CHECK_OTA, 1000)
                } else {
                    sendEmptyMessageDelayed(CHANGE_WIFI, 1000)
                }
            } else if (msg.what == CHANGE_WIFI) {

                lt_anim_view!!.visibility = View.GONE
                btnConnect?.text = SkinResourcesUtils.getString("jbl_DONE")
                btnConnect?.visibility = View.VISIBLE
            } else if (msg.what == CHECK_OTA) {

                if (activity == null)
                    return

                val intent = Intent(activity, DeviceUpgradeActivity::class.java)
                intent.putExtra(
                    DeviceUpgradeActivity.LINKPLAY_OTA_VIEW,
                    DeviceUpgradeActivity.LINKPLAY_CHECK_FOR_UPDATE
                )
                intent.putExtra(DeviceUpgradeActivity.LINKPLAY_OTA_FROM_SETUP, true)
                startActivity(intent)

                activity?.finish()
            }
        }
    }

    fun setCurrAPItem(apItem: HBApItem) {
        currApItem = apItem
    }

    fun setConnectDeviceInfo(ip: String?, uuid: String?) {
        if (ip != null) {
            this.ip = ip
        }
        if (uuid != null) {
            this.uuid = uuid
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        cview = inflater.inflate(R.layout.ble_connect_success_view, null)

        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun onResume() {
        super.onResume()

        lt_anim_view?.apply {
            setAnimation("connect_device_success.json")
            loop(false)
            playAnimation()
            addAnimatorListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    //开始
                }

                override fun onAnimationEnd(animation: Animator) {
                    //结束
                    uihd.sendEmptyMessage(CHECK_READY)
                }

                override fun onAnimationCancel(animation: Animator) {
                    //取消
                }

                override fun onAnimationRepeat(animation: Animator) {
                    //重复
                }
            })
        }
    }

    override fun initView() {
        super.initView()

        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)
        btnConnect = cview?.findViewById(R.id.btn_connect)
        lt_anim_view = cview?.findViewById(R.id.lt_anim_view)

        /*setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Wi_Fi_Connected"))
        bindHeaderImageViewBtns(cview)*/

        iv_close = cview?.findViewById(R.id.iv_close)
        iv_close?.setOnClickListener {
            onClickNext()
        }

        val fontColor = MixTextImg.RGBA2TxtRGB(WAApplication.me.getColor(R.color.fg_primary))
        var info = "<font color='$fontColor'>${currApItem?.displaySSID}</font>"
        var strLabel = String.format(SkinResourcesUtils.getString("jbl_Your_speaker_is_connected_to_network________successfully_"), "<b>$info</b>")
        tv_label?.text = Html.fromHtml(strLabel, Html.FROM_HTML_MODE_COMPACT)
        tv_label?.typeface =  FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

        currBLEDevice?.let {
            var imgName = DeviceImageUtil.getSoundRenderImgNameByPid(it.pid, it.colorID)
            iv_ble_device?.let { Glide.with(WAApplication.me.applicationContext).load(imgName).into(it) }
        }

        btnConnect?.visibility = View.INVISIBLE
        lt_anim_view!!.visibility = View.VISIBLE


    }

    override fun bindSlots() {
        super.bindSlots()

        ViewUtil.setOnClickListener(btnConnect) {
            if (TextUtils.equals(WifiResultsUtil.getWiFiSSID(), currApItem!!.displaySSID) || WAUpnpDeviceManager.me().getDeviceItemByMac(checkDevMac) != null) {
                val intent = Intent(activity, DeviceUpgradeActivity::class.java)
                intent.putExtra(DeviceUpgradeActivity.LINKPLAY_OTA_VIEW, DeviceUpgradeActivity.LINKPLAY_CHECK_FOR_UPDATE)
                intent.putExtra(DeviceUpgradeActivity.LINKPLAY_OTA_FROM_SETUP, true)
                startActivity(intent)
                activity?.finish()
            } else {
                changePhoneNetwork()
            }
        }

    }

    private fun changePhoneNetwork() {
        val vfrag = FragBLEConnectDiffNetwork()
        currApItem?.let { vfrag.setCurrAPItem(it) }
        currBLEDevice?.let { vfrag.currBLEDevice = it }
        vfrag.setConnectDeviceInfo(ip, uuid)
        vfrag.checkDevMac = checkDevMac
        (activity as LinkDeviceAddActivity?)!!.makeLinkWizardPageSwitch(vfrag, true)
    }

    override fun initUtils() {
        super.initUtils()
        updateTheme()
        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private fun updateTheme() {

        tv_label?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        btnConnect?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                WAApplication.mResources.getDimension(R.dimen.font_14)
            )
        }
    }

    override fun onClickNext() {
        super.onClickNext()
        uihd?.removeCallbacksAndMessages(null)
    }
}