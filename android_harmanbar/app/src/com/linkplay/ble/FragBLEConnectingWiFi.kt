package com.linkplay.ble

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.harman.bar.app.R
import com.harman.ble.HBBLEManagerCompat
import com.harman.hkone.DeviceImageUtil
import com.harman.widget.DotLoadingImageView
import com.harmanbar.ble.listener.HBConnectWiFiListener
import com.harmanbar.ble.manager.HBBLEManager
import com.harmanbar.ble.model.BLESetupResultItem
import com.harmanbar.ble.model.ConnectBLEInfo
import com.harmanbar.ble.statistic.HBBLEConnectApResult
import com.harmanbar.ble.utils.BLEProfile
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.service.WASlaveListDeviceManager
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.service.online_service.local.LocalOnlineBroadcastReceiver
import com.wifiaudio.utils.BLEConnectionCallback
import com.wifiaudio.utils.WifiResultsUtil
import com.wifiaudio.utils.device.APInfoUtil
import com.wifiaudio.utils.device.APInfoUtil.removeAPInfo
import com.wifiaudio.utils.device.APInfoUtil.saveAPInfo
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import com.wifiaudio.view.pagesmsccontent.easylink.ble_link_3.model.APItemInfo
import config.AppLogTagUtil
import config.LogTags
import java.util.Timer
import java.util.TimerTask


class FragBLEConnectingWiFi : FragBLEBase() {
    private val TAG = "FragBLEConnectingWiFi"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var progressbar: DotLoadingImageView? = null
    private var tv_label: TextView? = null
    private var SSID: String? = null
    private var PWD: String? = null
    private var currApItem: APItemInfo? = null
    private var iv_close: ImageView? = null

    var checkDevIP: String? = null;
    var checkDevUUID: String? = null
    var checkDevMac: String? = null
    var errorMsg: String = ""
    var errorCode: Int = 0

    //check online Timer
    private var checkOnlineTimer: Timer? = null

    //BLE connect Timer
    private var connTimer: Timer? = null

    private val CONNECT_FAILED = -2 //connect success, other network failed
    private val CLOSE_CONNECT = -1 //close connect
    private val CONNECT_SUCCESS = 1 //connect success
    private val PAIR_SUCCESS = 2 //BLE pair success
    private val PAIR_FAILED = 3 //BLE pair failed
    private val NO_SSID_ERROR = 4//找不到ssid，返回更改网络页面

    private val bleHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CONNECT_FAILED -> {
                    HBBLEManager.getInstance().bleAuthCancel()
                    HBBLEManager.getInstance().disConnect()

                    val vfrag = FragBLEConnectSuccess()
                    currApItem?.let { vfrag.setCurrAPItem(it.apItem) }
                    currBLEDevice?.let {
                        HBBLEManagerCompat.getInstance().disconnect(it)
                        vfrag.currBLEDevice = it }
                    vfrag.setConnectDeviceInfo(checkDevIP, checkDevUUID)
                    vfrag.checkDevMac = checkDevMac
                    (activity as LinkDeviceAddActivity?)?.makeLinkWizardPageSwitch(vfrag, true)
                }

                CLOSE_CONNECT -> currBLEDevice?.let {
                    HBBLEManagerCompat.getInstance().disconnect(it)
                     }

                CONNECT_SUCCESS -> {}

                PAIR_SUCCESS -> if (activity != null && isAdded) {
                    HBBLEManager.getInstance().bleAuthCancel()
                    HBBLEManager.getInstance().disConnect()
                    val vfrag = FragBLEConnectSuccess()
                    currApItem?.let { vfrag.setCurrAPItem(it.apItem) }
                    currBLEDevice?.let {
                        HBBLEManagerCompat.getInstance().disconnect(it)
                        vfrag.currBLEDevice = it }
                    vfrag.setConnectDeviceInfo(checkDevIP, checkDevUUID)
                    vfrag.checkDevMac = checkDevMac
                    (activity as LinkDeviceAddActivity?)?.makeLinkWizardPageSwitch(vfrag, true)
                }

                PAIR_FAILED -> {
                    if (activity == null) return
                    pairFailed = true
                    val vfrag = FragBLEConnectFailed()
                    currBLEDevice?.let { vfrag.currBLEDevice = it }
                    currApItem?.let { vfrag.setCurrAPItem(it.apItem) }
                    vfrag.setErrorMsg(errorMsg)
                    vfrag.setErrorCode(errorCode)
                    (activity as LinkDeviceAddActivity?)?.makeLinkWizardPageSwitch(vfrag, true)
                }

                NO_SSID_ERROR -> {
                    val frag = FragBLEChangeNetwork()
                    frag.setCurrSSID(SSID)
                    currBLEDevice?.let { it1 -> frag.currBLEDevice = it1 }
                    (activity as LinkDeviceAddActivity)?.makeLinkWizardPageSwitch(frag, false)
                }
            }
        }
    }

    fun setApItem(apItem: APItemInfo) {
        currApItem = apItem
    }

    fun setSSID(ssid: String?, pwd: String?) {
        SSID = ssid
        PWD = pwd
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        cview = inflater.inflate(R.layout.ble_connecting_wifi_view, null)
        initView()
        bindSlots()
        initUtils()
        connectWiFi()
        return cview
    }

    override fun initView() {
        super.initView()
        progressbar = cview?.findViewById(R.id.progressbar)
        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)

        progressbar?.visibility = View.VISIBLE

        iv_close = cview?.findViewById(R.id.iv_close)
        iv_close?.setOnClickListener {
            onClickNext()
        }

        /* setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Connecting"))
         bindHeaderImageViewBtns(cview)*/

        tv_label?.apply {
            text = SkinResourcesUtils.getString("connecting_please_wait")
//            text = SkinResourcesUtils.getString("jbl_Please_wait_while_the_connection_is_completed__This_might_take_a_minute_")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        val imageID = currBLEDevice?.let { DeviceImageUtil.getBLEDeviceImageByPid(it.pid.toString(16), it.colorID.toString()) }
        iv_ble_device?.also { Glide.with(WAApplication.me.applicationContext).load(imageID).into(it) }


    }

    override fun initUtils() {
        super.initUtils()
        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private var startConnect = false
    private fun connectWiFi() {
        if (startConnect) return
        startConnect = true
        startConnect()
        startConnTimer()
    }

    override fun onResume() {
        super.onResume()
        progressbar?.start(R.drawable.loading_fg_primary)
    }

    override fun onPause() {
        super.onPause()
        progressbar?.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
//        HBBLEManagerCompat.getInstance().release()
    }

    private fun startConnect() {

        if (currBLEDevice == null) return

        SSID = WifiResultsUtil.trimQuotation(SSID)

        LogsUtil.i(AppLogTagUtil.BLE_TAG, "$TAG Connect Network: ${currApItem?.apItem?.displaySSID}")

        var connectBLEInfo = ConnectBLEInfo()
        connectBLEInfo.apply {
            ssid = SSID
            password = PWD
            auth = currApItem?.apItem?.auth
            encry = currApItem?.apItem?.encry
            currApItem?.isOtherNetwork?.let { setbOtherNetwork(it) }
        }

        LogsUtil.i(AppLogTagUtil.BLE_TAG, "$TAG Connect Network connectBLEInfo: $connectBLEInfo")
        LogsUtil.i(AppLogTagUtil.BLE_TAG, "$TAG Connect Network currBLEDevice: $currBLEDevice")

        HBBLEManagerCompat.getInstance().connectWLAN(currBLEDevice, connectBLEInfo, connectWiFiListener)
    }

    //connect wifi listener
    private var connectWiFiListener: HBConnectWiFiListener = object : HBConnectWiFiListener {

        override fun ConnectResult(bleConnectApResult: HBBLEConnectApResult?, resultItem: BLESetupResultItem?) {

            LogsUtil.i(AppLogTagUtil.BLE_TAG, "$TAG LPConnectResult: ${bleConnectApResult?.name}, body: ${resultItem?.ip} , ${resultItem?.uuid}, ${resultItem?.wlan0_mac}")
            LogsUtil.d(AppLogTagUtil.BLE_TAG, "$TAG LPConnectResult: $resultItem")
            bleConnectApResult?.let { itBleConnectApResult ->
                when (itBleConnectApResult) {

                    HBBLEConnectApResult.CONNECT_AP_SUCCESS -> {
                        var ip: String = ""
                        var uuid: String = ""
                        val code = resultItem?.code
                        if (code == 0) {
                            LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail7.")
                            ip = resultItem.ip
                            uuid = resultItem.uuid
                            checkDevMac = resultItem.wlan0_mac
                            connectSuccess(ip, uuid)
                        } else {
                            errorCode = BLEConnectionCallback.ERROR_07
                            handlerError()
                            LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail2.")
                            connectFail()
                        }
                    }

                    HBBLEConnectApResult.CONNECT_AP_PASSWORD_ERROR -> {
                        errorCode = BLEConnectionCallback.ERROR_04
                        handlerError()
                        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail3.")
                        connectFail()
                    }

                    HBBLEConnectApResult.CONNECT_AP_WIFI_TIMEOUT -> {
                        errorCode = BLEConnectionCallback.ERROR_UNKNOWN
                        handlerError()
                        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail4.")
                        connectFail()
                    }

                    HBBLEConnectApResult.CONNECT_AP_NO_SSID -> {
                        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail6.")
                        bleHandler.sendEmptyMessage(NO_SSID_ERROR)
                    }

                    else -> {

                    }
                }
            }
        }

        override fun ConnectFailed(e: Exception?) {
            errorCode = BLEConnectionCallback.ERROR_UNKNOWN
            handlerError()
            LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail5.")
            connectFail()
        }

    }

    private fun startConnTimer() {

        if (connTimer != null) {
            connTimer?.cancel()
            connTimer = null
        }

        val startTime = System.currentTimeMillis()
        connTimer = Timer()
        connTimer?.schedule(object : TimerTask() {
            override fun run() {
                val curTime = System.currentTimeMillis()
                if (curTime - startTime > BLEProfile.BLE_CONN_TIME_OUT) {
                    LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG startConnTimer:timeout")
                    connTimer?.cancel()
                    if (pairFailed == true) return
                    bleHandler?.sendEmptyMessage(PAIR_FAILED)
                }
            }
        }, 0, 2000)
    }

    private var pairFailed: Boolean? = false

    private fun connectFail() {
        if (!isVisible) return

        startConnect = false

        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail.")

        cancelConnTimer()
        if (pairFailed == true) return
        bleHandler.sendEmptyMessage(PAIR_FAILED)
    }

    private fun handlerError() {
        val error = errorCode
        when (error) {
            BLEConnectionCallback.ERROR_01 -> errorMsg = SkinResourcesUtils.getString("BLE_Have_not_scanned__the_specified_SSID")

            BLEConnectionCallback.ERROR_02 -> errorMsg = SkinResourcesUtils.getString("BLE_WIFI_connection_timeout")

            BLEConnectionCallback.ERROR_03 -> errorMsg = SkinResourcesUtils.getString("BLE_DHCP_timeout")

            BLEConnectionCallback.ERROR_04 -> errorMsg = SkinResourcesUtils.getString("BLE_The_password_you_entered_is_incorrect")

            BLEConnectionCallback.ERROR_05 -> errorMsg = SkinResourcesUtils.getString("BLE_Unsupported_router_encryption_protocol")

            BLEConnectionCallback.ERROR_06 -> errorMsg = SkinResourcesUtils.getString("BLE_Parameter_error")

            BLEConnectionCallback.ERROR_07 -> errorMsg = SkinResourcesUtils.getString("BLE_Other_errors")

            else -> {}
        }
        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG the errorCode:$error, errorMessage: $errorMsg")
        if (error == BLEConnectionCallback.ERROR_04) {
            // 密码错误, 取消保存的自动连接apItem
            removeAPInfo(currApItem?.apItem?.displaySSID)
        }
    }

    private fun connectSuccess(ip: String, uuid: String) {
        startConnect = false
        checkDevIP = ip
        checkDevUUID = uuid
        cancelConnTimer()
        checkDeviceAfterBle(ip, uuid)
    }

    private fun cancelConnTimer() {

        if (connTimer != null) {
            connTimer?.cancel()
            connTimer = null
        }
    }

    private fun cancelCheckTimer() {
        if (checkOnlineTimer != null) {
            checkOnlineTimer?.cancel()
            checkOnlineTimer = null
        }
    }

    private fun checkDeviceAfterBle(ip: String, uuid: String) {

        //传输IP，防止一定几率找不到UPNP
        val intent = Intent(LocalOnlineBroadcastReceiver.IP_LOC_ONLINE_ACTION)
        intent.putExtra("uuid", uuid)
        intent.putExtra("ip", ip)
        if (activity != null)
            activity?.sendBroadcast(intent)

        if (checkOnlineTimer != null) {
            checkOnlineTimer?.cancel()
        }

        //[假如把A路由上的设备配到B路由上，当前手机又连A上，所以此时check时可能找到设备了，只不过是假的了,判断网络是不是同一个要有一个上下线]
        if (WifiResultsUtil.getWiFiSSID() == currApItem?.apItem?.displaySSID) {
        } else {
            WAApplication.clearDevice()
            try {
                Thread.sleep(3000)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }

        val startTime = System.currentTimeMillis()
        checkOnlineTimer = Timer()
        checkOnlineTimer?.schedule(object : TimerTask() {
            override fun run() {
                val curTime = System.currentTimeMillis()

                if (curTime - startTime > BLEProfile.BLE_CHECK_TIME_OUT) {
                    LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail it takes more than 90s to detect the device after wifi setup.")
                    checkFailed()
                    return
                }

                if (curTime - startTime > 30 * 1000) {

                    if (WifiResultsUtil.getWiFiSSID() != currApItem?.apItem?.displaySSID) {
                        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail different wifi")
                        checkFailed()
                        return
                    }
                }
                /**
                 * 有可能当前设备是子设备，在当前网络下重复配网后，
                 * 如果子设备不掉线，在主设备中找不到设备时，需要去子设备中查找是否存在
                 */
                var deviceItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
                if (deviceItem == null) deviceItem = WAUpnpDeviceManager.me().getDeviceItemByIP(ip)
                if (deviceItem == null) deviceItem =
                    WAUpnpDeviceManager.me().getDeviceItemByMac(checkDevMac)

                if (deviceItem != null) {
                    //成功后设置deviceItem
                    (activity as LinkDeviceAddActivity?)?.deviceItem = deviceItem

                    saveOOBERecord(deviceItem)

                    checkSuccess()
                } else {
                    deviceItem = WASlaveListDeviceManager.me().getDeviceItemByuuid(uuid)
                    if (deviceItem != null && activity != null && activity is LinkDeviceAddActivity) {
                        (activity as LinkDeviceAddActivity?)?.deviceItem = deviceItem

                        saveOOBERecord(deviceItem)

                        checkSuccess()
                    }
                }
            }
        }, 500, 1000)
    }

    /**
     * @see HomeDeviceListFragment.oobePopup()
     * status ==1 will pop
     */
    private fun saveOOBERecord(deviceItem: DeviceItem?) {
        //check  currBLEDevice has setup Wifi before or not
        if (currBLEDevice != null && currBLEDevice?.oobe == 1) {
            LogsUtil.i(AppLogTagUtil.BLE_TAG, "$TAG saveOOBERecord return ${currBLEDevice?.oobe}")
            return
        }

        deviceItem?.uuid?.let { APInfoUtil.saveDeviceOOBERecord(it, 1) }
    }

    private fun checkFailed() {
        if (activity == null) return
        cancelCheckTimer()
        if (WifiResultsUtil.getWiFiSSID() == currApItem?.apItem?.displaySSID) {
            errorCode = BLEConnectionCallback.ERROR_07
            handlerError()
            LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectFail1.")
            connectFail()
        } else {
            bleHandler.sendEmptyMessage(CONNECT_FAILED)
        }
    }

    private fun checkSuccess() {
        cancelCheckTimer()
        val apItemInfo = currApItem?.let { PWD?.let { it1 -> APItemInfo(it.apItem, it1) } }
        if (apItemInfo != null) {
            saveAPInfo(apItemInfo)
        }

        LogsUtil.e(AppLogTagUtil.BLE_TAG, "$TAG connectSuccess.")

        val delayTimer = 3 * 1000
        bleHandler.postDelayed(
            Runnable { bleHandler.sendEmptyMessage(PAIR_SUCCESS) },
            delayTimer.toLong()
        )
    }

    override fun onClickNext() {
        super.onClickNext()
    }
}