package com.linkplay.ble

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.harman.bar.app.R
import com.harmanbar.ble.entity.HBBluetoothDevice
import com.harmanbar.ble.manager.HBBLEManager
import com.linkplay.base.LPFragment
import com.skin.font.FontUtils
import com.wifiaudio.app.IInitView
import com.wifiaudio.app.WAApplication
import com.wifiaudio.service.WAUpnpDeviceManager

open class FragBLEBase : LPFragment(), IInitView {

    var currBLEDevice: HBBluetoothDevice? = null

    companion object {
        var mBGBitmap: Bitmap? = null

        //从哪个页面进来的，中途x掉，要回到哪个页面
        var bBLEEnterType = 0 //0:设备列表，1：Add Product 2：Discovering product页面
    }

    override fun onResume() {
        super.onResume()
        if (activity?.window != null)
            activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onPause() {
        super.onPause()
        if (activity?.window != null)
            activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun initView() {

    }

    override fun bindSlots() {

    }

    override fun initUtils() {

    }

    override fun updateFragmentBG(cview: View?) {

//        cview?.apply {
//            background = ColorDrawable(Color.parseColor("#70000000"))
//        }
//        if (mBGBitmap == null) {
//            super.updateFragmentBG(cview)
//            return
//        }
//
//        if (mBGBitmap!!.isRecycled) {
//            super.updateFragmentBG(cview)
//            return
//        }
//
////        val blurBitmap = FastBlur.createBlurBitmap1(
////            mBGBitmap,
////            2, activity
////        )
//
//        val blurBitmap = FastBlur.getTransparentBitmap(mBGBitmap, 50)
//
//        cview?.apply {
//            background = BitmapDrawable(blurBitmap)
//
//        }
    }

    override fun setHeaderTitle(cview: View?, strTitle: String?) {

        if (cview == null) return

        var title = cview.findViewById(R.id.ble_title) as TextView
        if (title != null) {
            title.text = strTitle
            title.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

        }
    }


    override fun setHeaderLeftIVBtnTheme(cview: View?, drawable: Drawable) {

        if (cview == null)
            return

        if (drawable == null)
            return

        var btnBack = cview.findViewById(R.id.iv_back) as ImageView

        btnBack?.apply {
            visibility = View.VISIBLE
            drawable.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
            setImageDrawable(drawable)
        }
    }

    override fun setHeaderRightBtnStatus(cview: View?, visible: Boolean) {
        if (cview == null)
            return

        var btnIVRight = cview.findViewById(R.id.iv_close) as ImageView
        btnIVRight?.apply {
            visibility = View.VISIBLE.takeIf { visible } ?: View.INVISIBLE
        }
    }

    override fun setHeaderLeftBtnStatus(cview: View?, visible: Boolean) {

        if (cview == null)
            return

        var btnIVLeft = cview.findViewById(R.id.iv_back) as ImageView
        btnIVLeft?.apply {
            visibility = View.VISIBLE.takeIf { visible } ?: View.INVISIBLE
        }
    }

    override fun bindHeaderImageViewBtns(cview: View?) {

        if (cview == null)
            return

        var btnIVLeft = cview.findViewById(R.id.iv_back) as ImageView
        btnIVLeft?.apply {
            setOnClickListener {
                onClickPrev()
            }
        }

        var btnIVRight = cview.findViewById(R.id.iv_close) as ImageView
        btnIVRight?.apply {
            setOnClickListener {
                onClickNext()
            }
        }
    }

    fun toDiscoverPage() {

        if (activity == null)
            return

        HBBLEManager.getInstance().bleAuthCancel()
        HBBLEManager.getInstance().disConnect()
        //这里要区分从哪里来的

        when (bBLEEnterType) {
            0 -> {
                activity?.finish()
            }
            2 -> {

            }
            else -> {
            }
        }
    }

    /**
     * 更新容器背景图
     */
    fun updateContainerBG(cview: View?) {

        var container = cview?.findViewById<View>(R.id.container)

        val layout_bg = container?.background as GradientDrawable

        var radius = WAApplication.mResources.getDimension(R.dimen.width_15).toFloat()
        layout_bg?.cornerRadii = floatArrayOf(
            radius,
            radius,
            radius,
            radius,
            radius,
            radius,
            radius,
            radius
        )

        var containerParams = container?.layoutParams as ViewGroup.MarginLayoutParams
        containerParams?.apply {
            var w = WAApplication.mResources.getDimension(R.dimen.width_16).toInt()
            leftMargin = w
            rightMargin = w
            bottomMargin = WAApplication.mResources.getDimension(R.dimen.width_20).toInt()
        }
        container?.layoutParams = containerParams

        container?.background = WAApplication.me.getDrawable(R.drawable.shape_choose_speaker)
    }

    override fun onClickNext() {
        super.onClickNext()

        var dlgView = activity?.let { ExitSetupDlgView(it) }
        dlgView?.apply {
            setDlgClickListener(object : BLESetupBaseView.IOnDlgBtnClickListener {
                override fun onConfirm(any: Any?) {



                    if (WAUpnpDeviceManager.me().hasDevices())
                        activity?.finish()
                    else {
                        toDiscoverPage()
                    }
                }

                override fun onCancel() {

                }
            })
            show()
        }
    }
}