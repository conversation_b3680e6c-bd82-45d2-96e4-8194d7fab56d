package com.linkplay.privacy

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Intent
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.google.firebase.FirebaseApp
import com.harman.FirebaseLogEventSharePreference.Companion.clearLogEventUpload
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.home.HomePagesActivity
import com.harman.legallib.LegalManager
import com.harman.log.Logger
import com.harman.rating.RatingInAppMgr
import com.harman.remote.config.RemoteConfig
import com.harman.remote.config.RemoteConfigCallback
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.LocalSharedPreferenceUtil
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.utils.device.APInfoUtil.clearOOBERecord
import com.wifiaudio.utils.statusbar.StatusBarUtils
import com.wifiaudio.view.component.ComponentButton
import com.wifiaudio.view.custom_view.CustomVideoView
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.GlobalUIConfig

public class FragLicensePrivacy : FragPrivacyBase() {

    var cview: View? = null
    var btn_accept: ComponentButton? = null
    var tb_layout: RelativeLayout? = null
    var tv_info1: TextView? = null
    var tv_info2: TextView? = null
    var vv_mp4: CustomVideoView? = null
    var bottom_layout: RelativeLayout? = null
    var root_layout: RelativeLayout? = null
    var iv_welcome_anim: LottieAnimationView? = null
    var vsplash_logo: ImageView? = null
    var dataInfo: DataFragInfo? = null

    private var uihd: Handler = object : Handler(Looper.myLooper()!!) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == 1) {
                bottom_layout?.visibility = View.VISIBLE
                root_layout?.visibility = View.VISIBLE
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.frag_license_privacy, null)

        initView()
        bindSlots()
        initUtils()

        autoShowPrivacyDialog()

        return cview
    }

    override fun initView() {
        super.initView()

        btn_accept = cview?.findViewById(R.id.btn_accept)
        tv_info1 = cview?.findViewById(R.id.tv_info1)
        tv_info2 = cview?.findViewById(R.id.tv_info2)
        vv_mp4 = cview?.findViewById(R.id.vv_mp4)
        bottom_layout = cview?.findViewById(R.id.bottom_layout)
        root_layout = cview?.findViewById(R.id.root_layout)
        iv_welcome_anim = cview?.findViewById(R.id.iv_welcome_anim)
        vsplash_logo = cview?.findViewById(R.id.vsplash_logo)
        bottom_layout?.visibility = View.INVISIBLE
        root_layout?.visibility = View.INVISIBLE

        iv_welcome_anim?.apply {

            addAnimatorListener(object : android.animation.Animator.AnimatorListener {
                override fun onAnimationStart(animation: android.animation.Animator) {

                }

                override fun onAnimationEnd(animation: android.animation.Animator) {
                    LocalSharedPreferenceUtil.setPrivacyStatusVisible(false);
                    activity?.finish()
                }

                override fun onAnimationCancel(animation: android.animation.Animator) {

                }

                override fun onAnimationRepeat(animation: android.animation.Animator) {

                }
            })
        }
    }

    private fun autoShowPrivacyDialog() {

        uihd.postDelayed({

            StatusBarUtils.setPageStatusBarPaddingTop(cview, false)
            StatusBarUtils.setCustomStatusBarColor(
                activity,
                true,
                GlobalUIConfig.color_navigationbar_bg
            )

            goAcceptPrivacyTerms()

        }, 1000)

        uihd.sendEmptyMessageDelayed(1, 1500)
    }

    private fun updateLabelVisible() {
        var set = AnimatorSet();
        set.duration = 300
        if (BuildConfig.FLAVOR == "jblone") {
            iv_welcome_anim?.playAnimation()
            var objAnimator1 = ObjectAnimator.ofFloat(root_layout, "alpha", 1f, 0f)
            set.play(objAnimator1)
        }
        var objAnimator2 = ObjectAnimator.ofFloat(btn_accept, "alpha", 1f, 0f)
        set.play(objAnimator2)
        set.start()
        set.addListener(object : android.animation.Animator.AnimatorListener {
            override fun onAnimationStart(animation: android.animation.Animator) {

            }

            override fun onAnimationEnd(animation: android.animation.Animator) {
                LocalSharedPreferenceUtil.setPrivacyStatusVisible(false)
                Logger.d("FragLicensePrivacy", "to HomePagesActivity")
                val intent = Intent(activity, HomePagesActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                startActivity(intent)
                activity?.finish()
            }

            override fun onAnimationCancel(animation: android.animation.Animator) {

            }

            override fun onAnimationRepeat(animation: android.animation.Animator) {

            }
        })
    }

    override fun bindSlots() {
        super.bindSlots()

        btn_accept?.setOnClickListener {
            if (context == null) {
                Log.d("FragPrivacyStep1", "context is null")
            }
            val hasNewVersion = context?.let { it1 -> LegalManager.isNewVersionAvailable(it1) }
//            val hasNewVersion=  LegalManager.isNewVersionAvailable(WAApplication.me.applicationContext)
            if (hasNewVersion == false) {
                initFirebase()
                btn_accept?.isEnabled = false
                updateLabelVisible()
            } else {
                goAcceptPrivacyTerms()
            }
        }
    }

    private fun initFirebase() {

        //#target: bHarmanDemo

        //每次进入App时，清空之前保存记录的一些数据
        WAApplication.me.sendBroadcast(Intent(WAApplication.LATE_INIT_CONFIG))

        clearLogEventUpload()
        clearOOBERecord()
        FirebaseApp.initializeApp(WAApplication.me.applicationContext)
        com.google.firebase.analytics.FirebaseAnalytics.getInstance(WAApplication.me.applicationContext).setAnalyticsCollectionEnabled(true)
        com.google.firebase.crashlytics.FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)
        RemoteConfig.instance.setCallback(object : RemoteConfigCallback {
            override fun onFailure() {
                LogsUtil.i("Firebase Remote Config Failed")
            }

            override fun onSuccess() {
                LogsUtil.i("Firebase Remote Config Success")
                RatingInAppMgr.instance.init(
                    WAApplication.me.applicationContext,
                    null,
                    RemoteConfig.instance.getString(RemoteConfig.KEY_RATING_IN_APP)
                )
            }
        })

        //#targetend
    }

    private fun goAcceptPrivacyTerms() {

        var vfrag = FragPrivacyStep1()
        vfrag.dataInfo = dataInfo
        dataInfo?.frameId?.let { it1 -> FragTabUtils.addFrag(activity, it1, vfrag, true) }
    }

    override fun initUtils() {
        super.initUtils()

//        StatusBarUtils.setPageStatusBarPaddingTop(cview, true)
//        StatusBarUtils.setCustomStatusBarColor(
//            activity,
//            true,
//            GlobalUIConfig.color_navigationbar_bg
//        )

        updateTheme()
        updateFragmentBG(cview)
    }

    private fun updateTheme() {

        tv_info1?.apply {
            text = SkinResourcesUtils.getString("jbl_Welcome")
            tv_info2?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                WAApplication.mResources.getDimension(R.dimen.font_24)
            )
        }

        tv_info2?.apply {
            text =
                SkinResourcesUtils.getString("jbl_Enjoy_the_immersive_audio_experience_provided_by_JBL_One_Platform_")
            tv_info2?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                WAApplication.mResources.getDimension(R.dimen.font_16)
            )
        }

        btn_accept?.let {
            it.setBtnText(SkinResourcesUtils.getString("jbl_GET_STARTED").uppercase())
        }
        /*btn_accept?.apply {
            text = SkinResourcesUtils.getString("jbl_GET_STARTED").toUpperCase()
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
//                background = tintDrawable
            setTextColor(context.getColor(R.color.fg3))
        }*/
    }

    override fun updateFragmentBG(cview: View?) {
//        super.updateFragmentBG(cview)

        vv_mp4?.apply {
            this.setAudioFocusRequest(AudioManager.AUDIOFOCUS_NONE)
            setVideoURI(Uri.parse("android.resource://${WAApplication.me.packageName}/raw/welcome_video_placeholder"))
            setOnPreparedListener {
                it.isLooping = true
//                val width = WAApplication.me.PhoneScreenWidth
//                var height = WAApplication.me.PhoneScreenHeight
//                if(it.videoWidth != 0 )
//                    height = (it.videoHeight * width / it.videoWidth)
//
//                setVideoSize(width, height)

                it.start()

                setAnimWaveVisible()
            }
            start()
        }
    }

    private fun setAnimWaveVisible() {

        uihd.postDelayed({
            var isHKone = BuildConfig.FLAVOR == "hkone"
            iv_welcome_anim?.visibility = if (isHKone) View.GONE else View.VISIBLE
            vsplash_logo?.visibility = if (isHKone) View.VISIBLE else View.GONE
        }, 50)
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
    }
}