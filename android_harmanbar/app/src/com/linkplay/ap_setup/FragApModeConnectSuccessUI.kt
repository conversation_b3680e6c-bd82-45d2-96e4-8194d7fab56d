package com.linkplay.ap_setup

import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.GsonUtils
import com.harman.bar.app.R
import com.linkplay.ota.view.DeviceUpgradeActivity
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.*
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import config.LogTags

class FragApModeConnectSuccessUI : FragApModeBaseUI() {
    private val TAG = "FragApModeConnectSuccessUI"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var btnConnect: Button? = null
    private var tv_label: TextView? = null

    private var mTargetSSID: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        cview = inflater.inflate(R.layout.frag_apmode_connect_success_view, null)

        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun onResume() {
        super.onResume()
        deleteWifiConfiguration()
    }

    override fun initView() {
        super.initView()

        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)
        btnConnect = cview?.findViewById(R.id.btn_connect)

        setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Wi_Fi_Connected"))
        bindHeaderImageViewBtns(cview)

        mTargetSSID = ""
        if (LinkDeviceAddActivity.apScanItem != null) {
            mTargetSSID = ByteIntConverter.convertHex2GBK(LinkDeviceAddActivity.apScanItem.SSID)
        }

        val fontColor = MixTextImg.RGBA2TxtRGB(WAApplication.me.getColor(R.color.fg_primary))
        var info = "<font color='$fontColor'>${mTargetSSID}</font>"
        var strLabel =
            String.format(SkinResourcesUtils.getString("jbl_Your_speaker_is_connected_to_network________successfully_"), info)
        tv_label?.text = Html.fromHtml(strLabel)

//        (activity as LinkDeviceAddActivity?)?.deviceItem?.let {
//            var imgID = DeviceImageUtil.getConnectSuccessImgByDeviceItem(
//                it
//            )
//            Glide.with(WAApplication.me.applicationContext).load(imgID).into(iv_ble_device)
//        }

        btnConnect?.text = SkinResourcesUtils.getString("jbl_DONE")


    }

    override fun bindSlots() {
        super.bindSlots()

        ViewUtil.setOnClickListener(btnConnect) {

            if (WifiResultsUtil.getWiFiSSID() == mTargetSSID) {

                Log.d(TAG, GsonUtils.toJson(currBLEDevice))
                val intent = Intent(activity, DeviceUpgradeActivity::class.java)
                intent.putExtra(
                    DeviceUpgradeActivity.LINKPLAY_OTA_VIEW,
                    DeviceUpgradeActivity.LINKPLAY_CHECK_FOR_UPDATE
                )
                intent.putExtra(DeviceUpgradeActivity.LINKPLAY_OTA_FROM_SETUP, true)
                startActivity(intent)

                activity?.finish()
            } else {
                changePhoneNetwork()
            }
        }
    }

    var wifiAdmin: WifiAdmin? = null

    fun deleteWifiConfiguration() {

        //删除当前Wisuper.onClickNext();fiConfiguration
        if (wifiAdmin == null) {
            wifiAdmin = WifiAdmin(WAApplication.me)
        }
        val mSelectWiFi = LinkDeviceAddActivity.mSelectWiFi
        if (mSelectWiFi != null) {
            LogsUtil.i(LogTags.DirectConnect, "$TAG Delete WifiConfiguration " + mSelectWiFi.SSID)
            wifiAdmin!!.delNetwork(mSelectWiFi.SSID)
        }
    }

    private fun changePhoneNetwork() {

        val vfrag = FragApModeConnectDiffNetworkUI()
        vfrag.currBLEDevice = currBLEDevice
        (activity as LinkDeviceAddActivity?)!!.makeLinkWizardPageSwitch(vfrag, true)
    }

    override fun initUtils() {
        super.initUtils()

        updateTheme()
        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private fun updateTheme() {

        tv_label?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        btnConnect?.apply {

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                WAApplication.mResources.getDimension(R.dimen.font_14)
            )
        }
    }

    override fun onClickNext() {
        super.onClickNext()
    }
}