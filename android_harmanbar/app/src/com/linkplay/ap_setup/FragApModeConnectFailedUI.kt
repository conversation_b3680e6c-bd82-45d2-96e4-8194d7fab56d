package com.linkplay.ap_setup

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.harman.bar.app.R
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.AssetsProjectConfigParse

import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import config.LogTags

class FragApModeConnectFailedUI: FragApModeBaseUI() {
    private val TAG = "FragApModeConnectFailedUI"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var btnConnect: Button? = null
    private var btnFAQ: Button? = null
    private var tv_label: TextView? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        cview = inflater.inflate(R.layout.frag_apmode_connect_failed_view, null)

        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun initView() {
        super.initView()

        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)
        btnConnect = cview?.findViewById(R.id.btn_connect)
        btnFAQ = cview?.findViewById(R.id.btn_faq)

        setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Connect_Failed"))
        bindHeaderImageViewBtns(cview)

        var strLabel = SkinResourcesUtils.getString("jbl_Sorry__we_are_unable_to_connect_your_speaker_to_Wi_Fi_")
        tv_label?.text = strLabel

        btnFAQ?.apply {
            visibility = View.VISIBLE
            text = SkinResourcesUtils.getString("jbl_FAQ")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

        }

        /*var imgID = SkinResourcesID.getDrawableId("svg_icon_no_wifi")
        Glide.with(WAApplication.me.applicationContext).load(imgID).into(iv_ble_device)*/

        iv_ble_device?.apply {
            val drawable = SkinResourcesUtils.getDrawable("svg_icon_no_wifi")
            setImageDrawable(drawable)
        }

        btnConnect?.text = SkinResourcesUtils.getString("jbl_try_again")


    }

    override fun bindSlots() {
        super.bindSlots()

        ViewUtil.setOnClickListener(btnConnect) {
            var vfrag = FragAPModeInputPWDUI()
            vfrag.currBLEDevice = currBLEDevice

            (activity as LinkDeviceAddActivity).makeLinkWizardPageSwitch(vfrag, false)
        }

        btnFAQ?.setOnClickListener {

            var linkUrl = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_get_help_1_url")

            if(TextUtils.isEmpty(linkUrl))
                return@setOnClickListener

            val intent = Intent()
            intent.action = "android.intent.action.VIEW"
            val url = Uri.parse(linkUrl)
            intent.data = url
            startActivity(intent)
        }
    }

    override fun initUtils() {
        super.initUtils()

        updateTheme()
        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    private fun updateTheme() {


        tv_label?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        }


        btnConnect?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
        }

        btnFAQ?.apply {
            background = null
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
        }
    }

    override fun onClickNext() {
        super.onClickNext()
    }
}