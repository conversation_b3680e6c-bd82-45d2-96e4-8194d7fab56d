package com.linkplay.ap_setup

import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.os.SystemClock
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.ConvertUtils
import com.bumptech.glide.Glide
import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil
import com.harman.widget.DotLoadingImageView
import com.rxjava.rxlife.RxLife
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.ApcliConfigAction
import com.wifiaudio.action.DeviceSettingAction
import com.wifiaudio.action.DeviceSettingAction.IDevicePropertyRequest
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.ApScanItem
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DeviceProperty
import com.wifiaudio.presenter.autotrack.DirectConnectTrack
import com.wifiaudio.presenter.autotrack.DirectConnectTrack.reportDirectSetup
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.ByteIntConverter
import com.wifiaudio.utils.WiFiUtils.getSSID
import com.wifiaudio.utils.WiFiUtils.isLinkplayAp
import com.wifiaudio.utils.device.APInfoUtil
import com.wifiaudio.utils.okhttp.IOkHttpRequestCallback
import com.wifiaudio.utils.okhttp.OkHttpResponseItem
import com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity
import config.LogTags
import io.reactivex.Single
import io.reactivex.SingleEmitter
import io.reactivex.schedulers.Schedulers
import org.teleal.cling.protocol.UpnpUDNManager

class FragApModeConnectingWiFiUI : FragApModeBaseUI() {

    private val TAG = "FragApModeConnectingWiFiUI"

    private var cview: View? = null
    private var iv_ble_device: ImageView? = null
    private var progressbar: DotLoadingImageView? = null
    private var tv_label: TextView? = null
    var deviceItem: DeviceItem? = null

    private var mTargetSSID: String? = null

    private val uihd: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        cview = inflater.inflate(R.layout.frag_apmode_connecting_wifi_view, null)

        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun initView() {
        super.initView()

        progressbar = cview?.findViewById(R.id.progressbar)
        iv_ble_device = cview?.findViewById(R.id.ic_location)
        tv_label = cview?.findViewById(R.id.tv_label)

        progressbar?.visibility = View.VISIBLE
        progressbar?.start()
        setHeaderTitle(cview, SkinResourcesUtils.getString("jbl_Connecting"))
        bindHeaderImageViewBtns(cview)

        tv_label?.apply {
            text =
                SkinResourcesUtils.getString("jbl_Please_wait_while_the_connection_is_completed__This_might_take_a_minute_")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        }

        mTargetSSID = ""
        if (LinkDeviceAddActivity.apScanItem != null) {
            mTargetSSID = ByteIntConverter.convertHex2GBK(LinkDeviceAddActivity.apScanItem.SSID)
        }

        val imageID = currBLEDevice?.let { DeviceImageUtil.getBLEDeviceImageByPid(it.pid.toString(16), it.colorID.toString()) }
        iv_ble_device?.also { Glide.with(WAApplication.me.applicationContext).load(imageID).into(it) }

        deviceItem = WAApplication.me.deviceItem



        configNetwork()
    }

    override fun initUtils() {
        super.initUtils()

        updateFragmentBG(cview)
        updateContainerBG(cview)
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onPause() {
        super.onPause()
        progressbar?.stop()
    }
    private fun configNetwork() {
        if (activity == null) return
        connectAp(LinkDeviceAddActivity.apScanItem, (activity as LinkDeviceAddActivity?)!!.apPwd)
    }

    private fun connectAp(apitem: ApScanItem, pwd: String) {

        ApcliConfigAction.connectAp(
            deviceItem,
            apitem,
            pwd,
            object : IOkHttpRequestCallback() {
                override fun onSuccess(response: Any) {
                    super.onSuccess(response)
                    val responseItem = response as OkHttpResponseItem
                    if (responseItem != null) {
                        LogsUtil.d(
                            LogTags.DirectConnect,
                            "$TAG:onSuccess: connectAp onSuccess " + responseItem.body
                        )
                    }
                    waitDeviceOnline()
                }

                override fun onFailure(e: Exception) {
                    super.onFailure(e)
                    LogsUtil.e(
                        LogTags.DirectConnect,
                        "$TAG:onFailure: connectAp onFailure: $e"
                    )
                    LinkDeviceAddActivity.apConnected = false
                    toFailPage()
                }
            })
    }

    private fun toFailPage() {
        LogsUtil.d(LogTags.DirectConnect, "$TAG:toFailPage: toFailPage")
        if (activity == null) return

        var vfrag = FragApModeConnectFailedUI()
        vfrag.currBLEDevice = currBLEDevice
        (activity as LinkDeviceAddActivity).makeLinkWizardPageSwitch(vfrag, false)
    }

    private var check_flag = false
    var start: Long = 0
    var bOnceSetNetWork = true //只发送一次setnetwork

    private fun waitDeviceOnline() {

        val task = Single.create { emitter: SingleEmitter<DeviceItem?>? ->
            check_flag = true
            Thread.sleep(6 * 1000L)
            bOnceSetNetWork = true
            start = SystemClock.elapsedRealtime()
            requireNotNull(deviceItem) { "$TAG deviceItem is Null!!!" }
            val uuid: String = deviceItem!!.uuid
            LogsUtil.i(
                LogTags.DirectConnect,
                "$TAG:waitDeviceOnline start [thread:" + Thread.currentThread()
                    .id + "]"
            )
            while (check_flag) {
                if (SystemClock.elapsedRealtime() - start >= 90 * 1000L) {
                    if (UpnpUDNManager.getIntance().isExistUDN(uuid)) {
                        reportDirectSetup(-1, DirectConnectTrack.Direct_Setup_Search_Device_Not_Online, deviceItem)
                    } else {
                        reportDirectSetup(-1, DirectConnectTrack.Direct_Setup_Device_Not_Online, deviceItem)
                    }
                    toFailPage()
                    check_flag = false //timeout
                    break
                }
                val find = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
                if (SystemClock.elapsedRealtime() - start >= 20 * 1000L && find == null) {
                    if (!isPhoneConnectSuccess()) {
                        toChangeWiFiPage()
                        check_flag = false //Wi-Fi 错误
                        break
                    }
                }
                if (find != null) {
                    WAApplication.me.deviceItem = find
                    (activity as LinkDeviceAddActivity?)!!.deviceItem = find
                    LogsUtil.i(
                        LogTags.DirectConnect,
                        "$TAG:waitDeviceOnline find != null"
                    )
                    if (isLinkplayAp()) {
                        LogsUtil.i(
                            LogTags.DirectConnect,
                            "$TAG:waitDeviceOnline is LinkplayAp"
                        )
                        DeviceSettingAction.getStatusEx(find, object : IDevicePropertyRequest {
                            override fun onSuccess(
                                content: String,
                                deviceProperty: DeviceProperty
                            ) {
                                LogsUtil.i(
                                    LogTags.DirectConnect,
                                    "$TAG:waitDeviceOnline:getStatusEx: response=$content"
                                )
                                if (deviceProperty.netstat == 2 && TextUtils.equals(
                                        deviceProperty.SSIDStrategy,
                                        "2"
                                    )
                                ) {
                                    //SSIDStrategy == 2
                                    LogsUtil.d(
                                        LogTags.DirectConnect,
                                        "$TAG:setNetWork"
                                    )
                                    if (bOnceSetNetWork) {
                                        bOnceSetNetWork = false
                                        start = SystemClock.elapsedRealtime()
                                        DeviceSettingAction.setDevicePassword(
                                            find,
                                            getRandomPWD(),
                                            object :
                                                IOkHttpRequestCallback() {
                                                override fun onSuccess(response: Any) {
                                                    super.onSuccess(response)
                                                    LogsUtil.d(
                                                        LogTags.DirectConnect,
                                                        "$TAG:setNetWork success"
                                                    )
                                                }

                                                override fun onFailure(e: Exception) {
                                                    super.onFailure(e)
                                                    LogsUtil.d(
                                                        LogTags.DirectConnect,
                                                        "$TAG:setNetWork failed"
                                                    )
                                                }
                                            })
                                    }
                                }
                            }

                            override fun onFailed(e: Throwable) {
                                LogsUtil.e(
                                    LogTags.DirectConnect,
                                    "$TAG:waitDeviceOnline essid:" + find.devStatus.essid
                                )
                                if (find.devStatus != null && find.devStatus.netstat == 2 && isSpeakerConnectSuccess(
                                        find
                                    )
                                ) {
                                    toSuccessPage()
                                    check_flag = false //getStatusEx failed
                                }
                            }
                        })
                    } else {
                        LogsUtil.i(
                            LogTags.DirectConnect,
                            "$TAG:waitDeviceOnline is not LinkplayAp"
                        )
                        if (find.devStatus != null && find.devStatus.netstat == 2 && isSpeakerConnectSuccess(
                                find
                            )
                        ) {
                            toSuccessPage()
                            check_flag = false //success
                        }
                    }
                } else {
                    LogsUtil.i(
                        LogTags.DirectConnect,
                        "$TAG:waitDeviceOnline find == null"
                    )
                }
                Thread.sleep(2 * 1000L)
            }
        }
        task.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .`as`(RxLife.`as`(this))
            .subscribe(
                { deviceItem: DeviceItem? ->
                    LogsUtil.i(
                        LogTags.DirectConnect,
                        "[thread:" + Thread.currentThread()
                            .id + "] " + "$TAG:waitDeviceOnline success:"
                    )
                }
            ) { throwable: Throwable ->
                LogsUtil.e(
                    LogTags.DirectConnect,
                    "[thread:" + Thread.currentThread()
                        .id + "] " + "$TAG:waitDeviceOnline Error:" + throwable
                )
            }
    }

    //随机16位数字密码,用参数是防止重复调用，造成密码不一样
    var strRand: String? = ""
    private fun getRandomPWD(): String? {
        if (strRand == null) strRand = String()
        if (strRand != null && strRand!!.length == 16) return strRand
        strRand = ""
        for (i in 0..15) {
            strRand += (Math.random() * 10).toString()
        }
        return strRand
    }

    private fun toSuccessPage() {
        LogsUtil.d(LogTags.DirectConnect, "$TAG:toSuccessPage: toSuccessPage")
        if (activity == null) return
        var vfrag = FragApModeConnectSuccessUI()
        vfrag.currBLEDevice = currBLEDevice
        (activity as LinkDeviceAddActivity).makeLinkWizardPageSwitch(vfrag, false)

        deviceItem?.uuid?.let { APInfoUtil.saveDeviceOOBERecord(it, 1) }

        reportDirectSetup(0, DirectConnectTrack.Direct_Setup_Success, deviceItem)
    }

    private fun isSpeakerConnectSuccess(deviceItem: DeviceItem?): Boolean {
        if (deviceItem?.devStatus == null) {
            return false
        }
        val speakerSSID = deviceItem.devStatus.essid
        val apScanItem = LinkDeviceAddActivity.apScanItem
        val targetSSID = String(ConvertUtils.hexString2Bytes(apScanItem.SSID))
        val speakerConnectSuccess = TextUtils.equals(speakerSSID, targetSSID)
        if (speakerConnectSuccess) {
            LogsUtil.i(
                LogTags.DirectConnect,
                "$TAG:essid=$speakerSSID,destSSID=$targetSSID:phone direct connect, equal, wait"
            )
        } else {
            LogsUtil.e(
                LogTags.DirectConnect,
                "$TAG:essid=$speakerSSID,destSSID=$targetSSID:phone direct connect, not equal, failed"
            )
        }
        return speakerConnectSuccess
    }

    private fun toChangeWiFiPage(){

        if (activity == null) return
        var vfrag = FragApModeConnectDiffNetworkUI()
        vfrag.currBLEDevice = currBLEDevice
        (activity as LinkDeviceAddActivity).makeLinkWizardPageSwitch(vfrag, true)
    }

    private fun isPhoneConnectSuccess(): Boolean {
        val phoneSSID = getSSID()
        val apScanItem = LinkDeviceAddActivity.apScanItem
        val targetSSID = String(ConvertUtils.hexString2Bytes(apScanItem.SSID))
        val phoneConnectSuccess = TextUtils.equals(phoneSSID, targetSSID)
        LogsUtil.d(
            LogTags.DirectConnect,
            "$TAG:isPhoneConnectSuccess:phoneConnectSuccess=$phoneConnectSuccess"
        )
        return phoneConnectSuccess
    }

    override fun onClickNext() {
        super.onClickNext()
    }
}