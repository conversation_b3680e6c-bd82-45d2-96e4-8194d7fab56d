package com.wifiaudio.action.lan

import java.io.Serializable

open class LocaleLan : Serializable {
    companion object {

        val LAN_EN = "EN"
        val LAN_FR = "FR"
        val LAN_DA = "DA"
        val LAN_DE = "DE"
        val LAN_ES = "ES"
        val LAN_ESMX = "ESMX"
        val LAN_FI = "FI"
        val LAN_IT = "IT"
        val LAN_NL = "NL"
        val LAN_NO = "NB"
        val LAN_PL = "PL"
        val LAN_PT = "PT"
        val LAN_PTBR = "PTBR"
        val LAN_SK = "SK"
        val LAN_SV = "SV"
        val LAN_RU = "RU"
        val LAN_ZH = "ZH"
        val LAN_TW = "TW"
        val LAN_ID = "IN"
        val LAN_JP = "JA"
        val LAN_KO = "KO"
        val LAN_AR = "AR"
        val LAN_HE = "HE"
        val LAN_CS = "CS"

        fun getDisplayLan(lan: String): String {
            return when (lan) {
                LAN_EN -> "English"
                LAN_ZH -> "简体中文"
                LAN_FR -> "Français"
                LAN_DA -> "Dansk"
                LAN_DE -> "Deutsch"
                LAN_ES -> "Español"
                LAN_ESMX -> "Español (México)"
                LAN_FI -> "Suomi"
                LAN_IT -> "Italiano"
                LAN_NL -> "Nederlands"
                LAN_NO -> "Norsk"
                LAN_PL -> "Polski"
                LAN_PT -> "Português"
                LAN_PTBR -> "Português(Brasil)"
                LAN_SK -> "Slovenčina"
                LAN_SV -> "Svenska"
                LAN_RU -> "РУССКИЙ"
                LAN_TW -> "繁體中文"
                LAN_ID -> "Bahasa Indonesia"
                LAN_JP -> "日本語"
                LAN_KO -> "한국어"
                LAN_AR -> "العربية"
                LAN_HE -> "עברית"
                LAN_CS -> "čeština"
                else -> "English"
            }
        }

    }
}