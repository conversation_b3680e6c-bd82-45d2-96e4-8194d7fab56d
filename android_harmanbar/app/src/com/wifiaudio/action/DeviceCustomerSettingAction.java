package com.wifiaudio.action;

import android.text.TextUtils;

import com.blankj.utilcode.util.GsonUtils;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.utils.okhttp.EncryptedOkHttp;
import com.wifiaudio.utils.okhttp.HttpRequestUtils;
import com.wifiaudio.utils.okhttp.IOkHttpRequestCallback;
import com.wifiaudio.utils.okhttp.OkHttpResponseItem;
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.GeneralConfig;
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.LightColorPicker;
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.partypad.DJPad;

import java.util.ArrayList;
import java.util.List;

import config.AppLogTagUtil;

//设备设置
public class DeviceCustomerSettingAction {

    public static void httppostASPRequest(String devIP, String command, String payload, final DeviceSettingActionCallback callback) {

        HttpRequestUtils requestUtils = EncryptedOkHttp.getInstance();

        String url = String.format("https://%s/httpapi.asp?", devIP);

        String requestBody = String.format("command=%s&payload=%s", command, payload);
        StringBuffer sb = new StringBuffer();
        sb.append("http body post ");
        sb.append(url);
        sb.append(requestBody);

        LogsUtil.i(AppLogTagUtil.DEVICE_TAG, sb.toString());

        requestUtils.post(url, new IOkHttpRequestCallback() {
            @Override
            public void onSuccess(Object response) {
                super.onSuccess(response);
                if (response == null) {
                    onFailure(new Exception("err"));
                    return;
                }

                OkHttpResponseItem responseItem = (OkHttpResponseItem) response;
                if (responseItem == null) {
                    onFailure(new Exception("err"));
                    return;
                }

                if (callback != null) {
//                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "http body post request :" + url + requestBody);
                    sb.append("        response = ");
                    sb.append(responseItem.body);
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, sb.toString());
                    callback.onSuccess(responseItem.body);
                }
            }

            @Override
            public void onFailure(Exception e) {
                super.onFailure(e);
                if (callback != null)
                    callback.onFailure(e);
            }

        }, null, requestBody);
    }

    public static void httpget(String devIP, String command, String payload, DeviceSettingActionCallback callback) {

        HttpRequestUtils requestUtils = EncryptedOkHttp.getInstance();

        String url = String.format("https://%s/httpapi.asp?command=%s&payload=%s", devIP, command, payload);
        StringBuffer sb = new StringBuffer();
        sb.append("http body get ");
        sb.append(url);

        LogsUtil.i(AppLogTagUtil.DEVICE_TAG, sb.toString());

        requestUtils.get(url, new IOkHttpRequestCallback() {

            @Override
            public void onSuccess(Object response) {
                super.onSuccess(response);

                if (response == null) {
                    onFailure(new Exception("err"));
                    return;
                }

                OkHttpResponseItem responseItem = (OkHttpResponseItem) response;

                if (responseItem == null) {
                    onFailure(new Exception("err"));
                    return;
                }

                if (callback != null) {
//                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "http body get request :" + url );
                    sb.append("      response = ");
                    sb.append(responseItem.body);
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, sb.toString());
                    callback.onSuccess(responseItem.body);
                }
            }

            @Override
            public void onFailure(Exception e) {
                super.onFailure(e);
                if (callback != null)
                    callback.onFailure(e);
            }
        });
    }

    public static void httpgetCusCommandRequest(String devIP, String command, DeviceSettingActionCallback callback) {

        HttpRequestUtils requestUtils = EncryptedOkHttp.getInstance();

        String url = String.format("https://%s/httpapi.asp?command=%s", devIP, command);
        StringBuffer sb = new StringBuffer();
        sb.append("http body get ");
        sb.append(url);

        LogsUtil.i(AppLogTagUtil.DEVICE_TAG, sb.toString());

        requestUtils.get(url, new IOkHttpRequestCallback() {

            @Override
            public void onSuccess(Object response) {
                super.onSuccess(response);

                if (response == null) {
                    onFailure(new Exception("err"));
                    return;
                }

                OkHttpResponseItem responseItem = (OkHttpResponseItem) response;

                if (responseItem == null) {
                    onFailure(new Exception("err"));
                    return;
                }

                if (callback != null) {
//                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "http body get request :" + url );
                    sb.append("      response = ");
                    sb.append(responseItem.body);
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, sb.toString());
                    callback.onSuccess(responseItem.body);
                }
            }

            @Override
            public void onFailure(Exception e) {
                super.onFailure(e);
                if (callback != null)
                    callback.onFailure(e);
            }
        });
    }

    public static void makeDeviceDeleteWiFiHistory(String devIP, String body, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "deleteWifiHistory", body, callback);
    }

    public static void makeDeviceGetWiFiHistory(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getWifiHistory", callback);
    }


    public static void makeDeviceSetSysSetting(String devIP, String tzID, boolean bDST, final DeviceSettingActionCallback callback) {

        String payload = String.format("{\"timezone\": {\"name\": \"%s\",\"DST\": \"%s\"}}", tzID, bDST);

        httppostASPRequest(devIP, "setSysSetting", payload, callback);
    }

    public static void makeDeviceSetWifiCountryCode(String devIP, String countryCode, final DeviceSettingActionCallback callback) {

        String payload = String.format("{\"country_code\": \"%s\"}", countryCode);

        httppostASPRequest(devIP, "setWifiCountryCode", payload, callback);
    }


    public static void makeDeviceSetSmartBtnConfig(String devIP, String configJsonInfo, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "setSmartButtonConfig", configJsonInfo, callback);
    }

    public static void makeDeviceGetSmartBtnConfig(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getSmartButtonConfig", callback);
    }

    public static void makeDeviceTriggerSmartBtn(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "triggerSmartButton", callback);
    }

    public static void makeDeviceRequestDeviceOta(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "requestDeviceOta", callback);
    }

    public static void makeDeviceSetCalibration(String devIP, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "setCalibration", "", callback);
    }

    public static void makeDeviceGetCalibrationState(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getCalibrationState", callback);
    }

    public static void makeDeviceCancelCalibration(String devIP, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "cancelCalibration", "", callback);
    }

    public static void makeDevicePlayDemoSound(String devIP, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "playDemoSound", "", callback);
    }

    public static void makeDeviceCancelDemoSound(String devIP, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "cancelDemoSound", "", callback);
    }

    public static void makeDeviceGetAtmosLevel(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getAtmosLevel", callback);
    }

    public static void makeDeviceSetAtmosLevel(String devIP, String atmosJson, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "setAtmosLevel", atmosJson, callback);
    }

    public static void makeDeviceGetAudioSync(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getAudioSync", callback);
    }

    public static void makeDeviceGetEQ(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getEQ", callback);
    }

    public static void makeDeviceGetFeatureSupport(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getFeatureSupport", callback);
    }

    public static void enterAuracast(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "enterAuracast", "", callback);
    }

    public static void exitAuracast(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "exitAuracast", "", callback);
    }

    public static void makeDeviceSendAppController(String devIP, String key_value, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"key_pressed\": \"%s\"}", key_value);

        httppostASPRequest(devIP, "sendAppController", payload, callback);
    }

    private static void makeDeviceSetEQ(String devIP, String eqJson, final DeviceSettingActionCallback callback) {

//        httppostASPRequest(devIP, "setEQ", eqJson, callback);
        httpget(devIP, "setEQ", eqJson, callback);

    }

    public static void makeDevicePostEQ(String devIP, String eqJson, final DeviceSettingActionCallback callback) {

        httppostASPRequest(devIP, "setEQ", eqJson, callback);

    }

    public static void makeDeviceSetAudioSync(String devIP, int audio_sync, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"audio_sync\": \"%s\"}", audio_sync);

        httppostASPRequest(devIP, "setAudioSync", payload, callback);
    }

    public static void makeDeviceSetC4aPermissionStatus(String devIP, String status, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"status\": \"%s\"}", status);

        httppostASPRequest(devIP, "setC4aPermissionStatus", payload, callback);
    }

    public static void makeDeviceSetChromecastOptIn(String devIP, String opt, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"opt_in\": \"%s\"}", opt);

        httppostASPRequest(devIP, "setChromecastOptIn", payload, callback);
    }

    public static void makeDeviceGetChromecastOptIn(String devIP, DeviceSettingActionCallback callback) {
//        String payload = String.format("{\"opt_in\": \"%s\"}", opt);

        httpgetCusCommandRequest(devIP, "getChromecastOptIn", callback);
    }

    public static void makeDeviceGetC4aPermissionStatus(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getC4aPermissionStatus", callback);
    }

    public static void makeDeviceGetRearSpeakerVolume(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getRearSpeakerVolume", callback);
    }

    public static void makeDeviceSetRearSpeakerVolume(String devIP, int volume, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"volume\": \"%d\"}", volume);

        httppostASPRequest(devIP, "setRearSpeakerVolume", payload, callback);
    }

    public static void makeDeviceGetDeviceName(String devIP, DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getDeviceName", callback);
    }

    public static void makeDeviceSetDeviceName(String devIP, String name, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"device_name\": \"%s\"}", name);

        httppostASPRequest(devIP, "setDeviceName", payload, callback);
    }

    public static void renameGroup(String devIP, String name, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"name\": \"%s\"}", name);

        httppostASPRequest(devIP, "renameGroup", payload, callback);
    }

    public static void makeDeviceGetAvsInfo(String devIP, DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getAvsInfo", callback);
    }


    public static void makeDeviceUploadProductLog(String devIP, final DeviceSettingActionCallback callback) {
        String payload = "";

        httppostASPRequest(devIP, "uploadDeviceLog", payload, callback);
    }

    public static void makeDeviceEnableRemoteDebug(String devIP, String debug_mode, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"debug_mode\": \"%s\"}", debug_mode);

        httppostASPRequest(devIP, "setDebugMode", payload, callback);
    }

    public static void makeDeviceGetRemoteDebug(String devIP, DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(devIP, "getDebugMode", callback);
    }

    public static void makeDeviceSetBluetoothConfig(String devIP, String config, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"automatic\": \"%s\"}", config);

        httppostASPRequest(devIP, "setBluetoothConfig", payload, callback);
    }

    public static void makeDeviceGetBluetoothConfig(String devIP, final DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(devIP, "getBluetoothConfig", callback);
    }

    public static void makeDeviceGetBatteryStatus(String devIP, final DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(devIP, "getBatteryStatus", callback);
    }

    //todo duplicate with
    public static void makeDeviceShutdownTimeSearch(String devIP, final DeviceSettingActionCallback shutdownCallback) {
        httpgetCusCommandRequest(devIP, "getSleepTimer", shutdownCallback);
    }

    public static void makeDeviceSetAuthStart(String devIP, int timeout, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"timeout\":%d}", timeout);

        httppostASPRequest(devIP, "setAuthStart", payload, callback);
    }

    public static void makeDeviceSetAuthCancel(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "setAuthCancel", callback);
    }

    public static void setAnchorIndicate(String devIP, int timeout, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"timeout\":%d}", timeout);

        httppostASPRequest(devIP, "setAnchorIndicate", payload, callback);
    }

    public static void setAnchorCancel(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "setAnchorCancel", callback);
    }

    public static void makeDeviceGetOTAAccessPoint(String devIP, final DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(devIP, "getOTAAccessPoint", callback);
    }

    public static void getDeviceInfo(String devIP, final DeviceSettingActionCallback callback) {

        httpgetCusCommandRequest(devIP, "getDeviceInfo", callback);
    }

    public static void setGeneralConfig(String devIP, GeneralConfig config, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"type\":\"%s\",\"value\":%d}", config.getType(), config.getValue());
        if (!TextUtils.isEmpty(config.getSubType())) {
            payload = String.format("{\"type\":\"%s\",\"sub_type\":\"%s\",\"value\":%d}", config.getType(), config.getSubType(), config.getValue());
        }
        httppostASPRequest(devIP, "setGernalConfig", payload, callback);
    }

    public static void getGeneralConfig(String devIP, String type, final DeviceSettingActionCallback callback) {
        getGeneralConfig(devIP, type, "", callback);
    }

    public static void getGeneralConfig(String devIP, String type, String subType, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"type\":\"%s\"}", type);
        if (!TextUtils.isEmpty(subType)) {
            payload = String.format("{\"type\":\"%s\",\"sub_type\":\"%s\"}", type, subType);
        }
        httppostASPRequest(devIP, "getGernalConfig", payload, callback);
    }

    public static void setLightColorPicker(String devIP, LightColorPicker colorPicker, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"loop\":%d,\"r\":%d,\"g\":%d,\"b\":%d }", colorPicker.getLoop(), colorPicker.getRed(), colorPicker.getGreen(), colorPicker.getBlue());
        httppostASPRequest(devIP, "setLightColorPicker", payload, callback);
    }

    public static void getLightColorPicker(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "getLightColorPicker", "", callback);
    }

    public static void playPartySound(String devIP, int value, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"index\":%d}", value);
        httppostASPRequest(devIP, "playPartySound", payload, callback);
    }

    public static void setDjEvent(String devIP, String value, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"set\": \"%s\"}}", value);
        httppostASPRequest(devIP, "setDJEvent", payload, callback);
    }

    public static void setDJPad(String devIP, String type, String set1, String set2, String set3, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"type\":\"%s\",\"set1\":\"%s\",\"set2\":\"%s\",\"set3\":\"%s\"}", type, set1, set2, set3);
        httppostASPRequest(devIP, "setDJPad", payload, callback);
    }

    public static void setDJPad(String devIP, DJPad djPad, DeviceSettingActionCallback callback) {
        List<Integer> curSet = djPad.getCurSet();
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < curSet.size(); i++) {
            sb.append(curSet.get(i));
            if (i < curSet.size() - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        String formatCurSet = sb.toString();

        String customizeSetPayload = GsonUtils.toJson(djPad.getCustomizeSet());
        LogsUtil.d("DeviceCustomerSettingAction", "formatCurSet: " + formatCurSet);
        String payload = String.format("{\"type\":\"%s\",\"status\":\"%s\",\"cur_set\":\"%s\",\"customize_set\":%s}", djPad.getType(), djPad.getStatus(), formatCurSet, customizeSetPayload);

        LogsUtil.d("DeviceCustomerSettingAction", "payload: " + payload);
        httppostASPRequest(devIP, "setDJPad", payload, callback);
    }

    public static void getDJPad(String devIP, DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(devIP, "getDJPad", callback);
    }

    public static void destroyCastGroup(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "destroyCastGroup", "", callback);
    }

    public static void getGroupParameter(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "getGroupParameter", "", callback);
    }

    public static void setCastGroup(String devIP, String payload, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "setCastGroup", payload, callback);
    }

    public static void getGroupInfo(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "getGroupInfo", "", callback);
    }

    public static void groupCalibration(String devIP, String id, final DeviceSettingActionCallback callback) {
        String payload = null;
        if (TextUtils.isEmpty(id)) {
            payload = "{}";
        } else {
            payload = String.format("{\"anchor\": \"%s\"}", id);
        }
        httppostASPRequest(devIP, "groupCalibration", payload, callback);
    }

    public static void getCalibrationRoomState(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "getCalibrationRoomState", "", callback);
    }


    public static void skipDemoSound(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "skipDemoSound", "", callback);
    }

    public static void switchStereoChannel(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "switchStereoChannel", "", callback);
    }

    public static void triggerCastLED(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "triggerCastLED", "", callback);
    }


    public static void makeDeviceGetCustomerID(String devIP, final DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(devIP, "getCustomerID", callback);


    }

    public static void makeDeviceSendCustomerID(String devIP, String key_value, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"customer_id\": \"%s\",\"acc_type\": \"guest\"}", key_value);
        httppostASPRequest(devIP, "setCustomerID", payload, callback);
    }

    public static void setVoiceRequestStartTone(String ip, String source, int status, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\",\"status\": \"%s\"}", source, String.valueOf(status));
        httppostASPRequest(ip, "setVoiceRequestStartTone", payload, callback);
    }

    public static void getVoiceRequestStartTone(String ip, String source, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\"}", source);
        httppostASPRequest(ip, "getVoiceRequestStartTone", payload, callback);
    }

    public static void setVoiceRequestEndTone(String ip, String source, int status, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\",\"status\": \"%s\"}", source, String.valueOf(status));
        httppostASPRequest(ip, "setVoiceRequestEndTone", payload, callback);
    }

    public static void getVoiceRequestEndTone(String ip, String source, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\"}", source);
        httppostASPRequest(ip, "getVoiceRequestEndTone", payload, callback);
    }

    public static void setVoiceLanguage(String ip, String source, String locale, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\",\"locale\": \"%s\"}", source, locale);
        httppostASPRequest(ip, "setVoiceLanguage", payload, callback);
    }

    public static void getVoiceLanguage(String ip, String source, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\"}", source);
        httppostASPRequest(ip, "getVoiceLanguage", payload, callback);
    }

    public static void getSupportedVoiceLanguage(String ip, String source, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"source\": \"%s\"}", source);
        httppostASPRequest(ip, "getSupportedVoiceLanguage", payload, callback);
    }

    public static void getLWAState(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getLWAState", "", callback);
    }

    public static void setLWAAuthCode(String ip, String authorizationCode, String clientId, String redirectUri, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"authorization_code\": \"%s\",\"client_id\": \"%s\",\"redirect_uri\": \"%s\"}", authorizationCode, clientId, redirectUri);
        httppostASPRequest(ip, "setLWAAuthCode", payload, callback);
    }

    public static void requestLWALogout(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "requestLWALogout", "", callback);
    }

    public static void setAutoPowerOffTimer(String ip, int time, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"timer\":%d}", time);
        httppostASPRequest(ip, "setAutoPowerOffTimer", payload, callback);
    }

    public static void getAutoPowerOffTimer(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getAutoPowerOffTimer", "", callback);
    }

    public static void setFeedbackToneConfig(String ip, boolean checked, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
        httppostASPRequest(ip, "setFeedbackToneConfig", payload, callback);
    }


    public static void requestGoogleLogout(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "requestGoogleLogout", "", callback);
    }

    public static void getFeedbackToneConfig(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getFeedbackToneConfig", "", callback);
    }

    public static void setBatterySavingMode(String ip, boolean checked, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
        httppostASPRequest(ip, "setBatterySavingMode", payload, callback);
    }

    public static void getBatterySavingMode(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getBatterySavingMode", "", callback);
    }


    public static void getProductUsage(String ip, DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getProductUsage", "", callback);
    }

    public static void makeDevicePreviewSoundscape(String ip, int soundscape_id, int duration, DeviceSettingActionCallback callback) {
        String payload = String.format("{\"soundscape_id\": %d,\"duration\": %d}", soundscape_id, duration);
        httppostASPRequest(ip, "previewSoundscape", payload, callback);
    }

    public static void makeDeviceCancelSoundscape(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "cancelSoundscape", "", callback);
    }

    public static void clearHistoryOneOSVersion(String devIP, final DeviceSettingActionCallback callback) {
        httppostASPRequest(devIP, "clearHistoryOneOSVersion", "", callback);
    }

    public static void setSmartMode(String ip, boolean checked, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
        httppostASPRequest(ip, "setSmartMode", payload, callback);
    }

    public static void getSmartMode(String ip, final DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getSmartMode", "", callback);
    }

    public static void getRearSpeakerStatus(String ip, final DeviceSettingActionCallback callback) {
        httpgetCusCommandRequest(ip, "getRearSpeakerStatus", callback);
    }

    public static void setPersonalListeningMode(String ip, boolean checked, final DeviceSettingActionCallback callback) {
        String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
        httppostASPRequest(ip, "setPersonalListeningMode", payload, callback);
    }

    public static void getPersonalListeningMode(String ip, final DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "getPersonalListeningMode", "", callback);
    }

    public static void setIRLearn(String ip, final DeviceSettingActionCallback callback) {
        httppostASPRequest(ip, "setIRLearn", "", callback);
    }

}
