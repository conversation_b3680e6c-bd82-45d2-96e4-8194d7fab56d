package com.wifiaudio.app;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.NetworkInfo.State;
import android.os.Build.VERSION;
import android.os.Build.VERSION_CODES;
import android.os.StrictMode;
import android.text.TextUtils;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.AppUtils.AppInfo;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.harman.bar.app.BuildConfig;
import com.harman.bar.app.R;
import com.harman.log.Logger;
import com.harman.multichannel.HarmancastManager;
import com.harman.discover.filter.DeviceFilterKt;
import com.harman.hkone.Util;
import com.harman.legallib.LegalConfig;
import com.harman.legallib.LegalManager;
import com.harman.log.DebugHelper;
import com.harman.oobe.wifi.LocalCacheAdapter;
import com.harman.support.IWiFiStatusListener;
import com.harman.support.LinkPlaySDKHelperImpl;
import com.harman.support.LinkPlaySDKHelperNonEventsImpl;
import com.harman.support.LinkPlaySDKHelperProxy;
import com.jbl.one.configuration.AppConfigurationUtils;
import com.tencent.mmkv.MMKV;
import com.wifiaudio.action.LocalSharedPreferenceUtil;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.model.EventMessageItem;
import com.wifiaudio.utils.WiFiConnectTools;
import com.wifiaudio.utils.WifiResultsUtil;
import com.wifiaudio.view.pagesmsccontent.DeviceLoserFragmentActivity;

import org.greenrobot.eventbus.EventBus;

import java.util.HashSet;
import java.util.List;

import config.AppLogTagUtil;

public class WAApplication extends LinkplayApplication {

    public static HashSet<String> customerIDDevices;
    private Thread.UncaughtExceptionHandler uncaughtExceptionHandler;
    private static final String TAG = "WAApplication";

    @Override
    public void onCreate() {
        customerIDDevices = new HashSet<>();

        if (Logger.isUsable) {
            DebugHelper.init(this);
        }
        AppInfo ai = AppUtils.getAppInfo();
        if (null != ai) {
            Logger.d(TAG, "APP launch >>> appInfo:[" + ai + "]");
        }
        new GlideBuilder(this).setMemoryCache(new LruResourceCache(40 * 1024 * 1024));

        LinkPlaySDKHelperProxy.INSTANCE.setNonEventHandler(LinkPlaySDKHelperNonEventsImpl.INSTANCE);
        LinkPlayLauncher.INSTANCE.beforeApplicationCreate(
                this,
                genKeyStorePass(),
                BuildConfig.DEBUG
        );
        super.onCreate();
        if (Logger.isUsable) {
            LogsUtil.init();
        }

        LinkPlayLauncher.INSTANCE.afterApplicationCreate(
                this,
                mWiFiStatusListener,
                new DeviceManagerObserver(),
                LinkPlaySDKHelperImpl.INSTANCE
        );
        DeviceFilterKt.bindBLEAdvFilters();

        Util.updateSeq();

        //init crash handler
        if (BuildConfig.DEBUG) {
            Thread.setDefaultUncaughtExceptionHandler(getUncaughtExceptionHandler());
        }

        initCustomerId();

//        AssetsJsonParser.parseProductListConfig();
//        HarmancastManager.getInstance().parseMultiChannelConfig(this);

        loadRemoteConfig();
        //#target: bHarmanDemo

        //每次进入App时，清空之前保存记录的一些数据
        if (!LocalSharedPreferenceUtil.getPrivacyStatusVisible()) {
            // init later in MyProductsViewModel after got permissions.
            LinkPlayLauncher.INSTANCE.initUPNPSearch();
        }

        com.harman.FirebaseLogEventSharePreference.Companion.clearLogEventUpload();
        com.wifiaudio.utils.device.APInfoUtil.INSTANCE.clearOOBERecord();
        com.google.firebase.FirebaseApp.initializeApp(this);
        com.google.firebase.analytics.FirebaseAnalytics.getInstance(this).setAnalyticsCollectionEnabled(true);
        com.google.firebase.crashlytics.FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true);
        com.harman.remote.config.RemoteConfig.getInstance().setCallback(new com.harman.remote.config.RemoteConfigCallback() {

            @Override
            public void onFailure() {
                LogsUtil.i("Firebase Remote Config Failed");
            }

            @Override
            public void onSuccess() {
                LogsUtil.i("Firebase Remote Config Success");
                com.harman.rating.RatingInAppMgr.getInstance().init(getApplicationContext(),
                        null,
                        com.harman.remote.config.RemoteConfig.getInstance().getString(com.harman.remote.config.RemoteConfig.KEY_RATING_IN_APP));
            }
        });

        MMKV.initialize(this);
        //#targetend
        this.init();
        LegalManager.INSTANCE.init(this.getApplicationContext(), BuildConfig.APP_SERVER_HOST + "/general/legal/v2/legal.json", LegalConfig.DEFAULT_LEGAL_VERSION);

        // init
//        PartyBoxGattCommandProcessor.INSTANCE.setEqMaps(LocalPartyBoxEqPreset.INSTANCE.getOrParse(this));
        LocalCacheAdapter.INSTANCE.init();
    }

    private void initStrictMode() {
        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder()
//                .detectDiskReads()
//                .detectDiskWrites()
//                .detectNetwork()   // or .detectAll() for all detectable problems

                .detectAll()
                .penaltyLog()
                .build());
//        StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder()
////                .detectLeakedSqlLiteObjects()
////                .detectLeakedClosableObjects()
//                        .detectAll()
//                .penaltyLog()
//                .penaltyDeath()
//                .build());
    }

    private void initCustomerId() {
        String keyCustomerId = "KEY_CUSTOMER_ID";
        SharedPreferences pref = getSharedPreferences(this.getPackageName() + "_preferences", Context.MODE_PRIVATE);
        String customerID = pref.getString(keyCustomerId, "");
        LogsUtil.i("Customer Id is : " + customerID);
        if (customerID == null || customerID.isEmpty()) {
            pref.edit().putString(keyCustomerId, "A" + System.currentTimeMillis()).apply();
        }
    }

    // 初始化Application
    private void init() {

        IntentFilter wffilter = new IntentFilter();
        wffilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        wffilter.addAction(LATE_INIT_CONFIG);

        if (VERSION.SDK_INT >= VERSION_CODES.TIRAMISU) {
            registerReceiver(wificast, wffilter, RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(wificast, wffilter);
        }
    }

    BroadcastReceiver wificast = new BroadcastReceiver() {
        @Override
        public synchronized void onReceive(Context arg0, Intent intent) {
            // TODO Auto-generated method stub
            String action = intent.getAction();
            if (ConnectivityManager.CONNECTIVITY_ACTION.equals(action)) {
                ConnectivityManager connManager = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);
                if (connManager != null) {
                    NetworkInfo networkInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
                    if (networkInfo != null) {
                        State state = networkInfo.getState();
                        if (state == State.CONNECTED) { // 链接上
                            sendBroadcast(new Intent(DeviceLoserFragmentActivity.Action_WIFI_Connected));
                            WAApplication.me.needReplaceCurrentQueue = true;
                        } else if (state == State.DISCONNECTED) { // 失去链接
                            sendBroadcast(new Intent(DeviceLoserFragmentActivity.Action_WIFI_Disconnected));
                        }
                    }
                }
            } else if (TextUtils.equals(action, LATE_INIT_CONFIG)) {
                initUPNPSearch();
                // WIFI连接方式，是否正连接在设备上
                WAApplication.me.isWifiConnectAP = !WifiResultsUtil.isWiimuWifi();
            }
        }
    };

    // 结束所有的后台通信
    @Override
    public void destroyAllBack() {
        // 关闭数据库
        try {
            dbHelper.close();
        } catch (Exception e) {
            // TODO: handle exception
        }

        // 停用upnp
        upnptempl.stopUpnpSearch();

        try {
            unregisterReceiver(wificast);
        } catch (Exception e) {
            // TODO: handle exception
        }

        LogsUtil.i(AppLogTagUtil.LogTag, "----------end of main----------");


        WiFiConnectTools.getInstance().unRegisterWiFiReceiver(this);
    }

    private Thread.UncaughtExceptionHandler getUncaughtExceptionHandler() {
        if (this.uncaughtExceptionHandler == null) {
            this.uncaughtExceptionHandler = CrashExceptionHandler.getInstance(this);
        }
        return this.uncaughtExceptionHandler;
    }

    @Override
    public void exitApp() {
        super.exitApp();
    }

    @Override
    public List<String> getOnlineDeviceProjects() {
        List<String> onlineDeviceProjects = AppConfigurationUtils.INSTANCE.getSupportModelNameList();
        LogsUtil.i(AppLogTagUtil.LogTag, "----------end of getOnlineDeviceProjects----------" + onlineDeviceProjects);
        return onlineDeviceProjects;
    }

    private final IWiFiStatusListener mWiFiStatusListener = new IWiFiStatusListener() {
        @Override
        public void onWiFiConnected() {
            EventMessageItem eventItem = new EventMessageItem();
            eventItem.setType(EventMessageItem.MessageType.WiFi_Type);
            EventBus.getDefault().post(eventItem);
        }

        @Override
        public void onWiFiDisconnected() {
            EventMessageItem eventItem = new EventMessageItem();
            eventItem.setType(EventMessageItem.MessageType.WiFi_Type);
            EventBus.getDefault().post(eventItem);
        }
    };

    private String genKeyStorePass() {
        if (BuildConfig.FLAVOR.equalsIgnoreCase("jblone")) {
            if (BuildConfig.DEBUG) {
                return "2896E632FA6DCACCBD3E6262D89BB748C3E5C0A10EEEDD52199266FBB795D3D4";
            } else {
                return "2FF205534E9A4697A5082836DA2A097E877404DEC9373B4F386FF44FDA7E07FE";
            }
        } else {
            if (BuildConfig.DEBUG) {
                return "B75EF637331DCFD6746660A0A9A3F1FF743A28E5F105FDCB771F8A93FFC4C7B8";
            } else {
                return "EE5069E4F0ED95A980556E93FF0EA27746D9514A43CE12FC5114EA36E59225C1";
            }
        }
    }

    private void loadRemoteConfig() {
        AppConfigurationUtils.INSTANCE.loadEssentialConfig(this.getApplicationContext());
//        AppConfigurationUtils.INSTANCE.loadMultichannelConfig();
    }

    @Override
    protected void startLogInfo() {
        if (Logger.isUsable) {
            super.startLogInfo();
        }
    }

}
