package com.wifiaudio.app

import android.os.Handler
import android.os.Looper
import com.blankj.utilcode.util.ObjectUtils
import com.harman.bean.AuthDevice
import com.harman.log.Logger
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.rightfrag_obervable.MenuRightFragInstaller
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.UTCTimeUtils
import config.LogTags

class DeviceManagerObserver : IDeviceManagerObserver {

    var mHandler: Handler? = null

    override fun LPDeviceOnline(deviceItem: DeviceItem?) {
        WAUpnpDeviceManager.me().addDeviceItemByUuid(deviceItem?.uuid,deviceItem)
        if (mHandler == null)
            mHandler = Handler(Looper.getMainLooper())
        val authDevice = AuthDevice(deviceItem)
        authDevice.isWifiDeviceAuth = true
        Logger.i(TAG,"DeviceManagerObserver online " + authDevice?.deviceCrc + "  " + deviceItem?.speakerName + "  " + deviceItem?.IP + "  " + ObjectUtils.toString(deviceItem?.sleepTimerItem))
        setTimeZone(deviceItem)
    }

    private fun setTimeZone(devItem: DeviceItem?) {
        if (devItem == null) return
        Logger.i(LogTags.Device, "makeDeviceSetSysSetting:Request:" + devItem.IP)
        val tzID = DateTimes.getTimezone()
        val dstOffset = UTCTimeUtils.getDstOffset()
        val bDST = if (dstOffset == 0f) false else true
        DeviceCustomerSettingAction.makeDeviceSetSysSetting(
            devItem.IP,
            tzID,
            bDST,
            object : DeviceSettingActionCallback {
                override fun onSuccess(content: String) {
                    Logger.i(
                        LogTags.Device,
                        "makeDeviceSetSysSetting:Success:$content"
                    )
                }

                override fun onFailure(e: Throwable) {
                    Logger.i(
                        LogTags.Device,
                        "makeDeviceSetSysSetting:Failed:" + e.localizedMessage
                    )
                }
            })
    }

    override fun LPDeviceOffline(deviceItem: DeviceItem?) {
        val authDevice = AuthDevice(deviceItem)
        Logger.i(TAG,"DeviceManagerObserver wifi offline ${authDevice.deviceCrc} $deviceItem" )
        Handler(Looper.getMainLooper()).postDelayed({
            MenuRightFragInstaller.getInstance().notifyErrorDeviceRemove()
        },1500)

    }

    override fun LPDeviceUpdate(deviceItem: DeviceItem?) {
        MenuRightFragInstaller.getInstance().notifyErrorDeviceRemove()
    }

    private val TAG = "'DeviceManagerObserver'"
}