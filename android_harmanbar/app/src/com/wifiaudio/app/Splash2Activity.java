package com.wifiaudio.app;

import static com.wifiaudio.view.pagesmsccontent.ContainerActivity.FRAGMENT_TAG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.StateListDrawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo.State;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.harman.bar.app.R;
import com.harman.home.HomePagesActivity;
import com.harman.legallib.LegalManager;
import com.harman.log.Logger;
import com.harman.utils.Utils;
import com.menucontroller.drawermenu.library.MenuDrawer.MenuSizeHelper;
import com.skin.ResourceManage;
import com.skin.SkinResourcesUtils;
import com.skin.SkinResourcesValue;
import com.wifiaudio.action.lan.LocaleLanConfigUtil;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.utils.NavigationBarColorHelper;
import com.wifiaudio.view.pagesmsccontent.ContainerActivity;

import config.AppLogTagUtil;
import config.GlobalUIConfig;

public class Splash2Activity extends AppCompatActivity {

    private Handler uihd = new Handler();
    private TextView vappver, vprivacy;
    private Button btn_Search, btnNext;
    private ImageView vsplashlog = null;
    private RelativeLayout vLayoutSign;
    private TextView vTxtSignUp;
    private Button vSignIn;
    ImageView vSplash;
    private Context mContext;
    private final int OVERLAY_PERMISSION_REQ_CODE = 1234;
    public static final String EULA_SATUS = "EULASatus";

    public static final int ACT_RESULT_BACK = 100;

    /***
     *释放内存
     */
    @Override
    public void onDestroy() {
        super.onDestroy();

        LogsUtil.v(AppLogTagUtil.LogTag, "Splash2Activity onDestroy");

        if (uihd != null) {
            uihd.removeCallbacksAndMessages(null);
        }
    }

    private boolean isProcessed = false;

    @Override
    protected void onResume() {
        super.onResume();
        LogsUtil.v(AppLogTagUtil.LogTag, "Splash2Activity onResume");
        if (isProcessed) {
            finish();
        }
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        if (newBase == null)
            super.attachBaseContext(newBase);
        else {
            Context baseContext = LocaleLanConfigUtil.INSTANCE.changeAppLan(newBase);
            super.attachBaseContext(baseContext);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        Utils.decorateHarmanWindow(this);
        mContext = this;
        super.onCreate(savedInstanceState);

        //有虚拟键时底部导航栏遮挡问题


        int layoutId = R.layout.act_splash;

        final View view = View.inflate(this, layoutId, null);

        setContentView(view);

        NavigationBarColorHelper.INSTANCE.setNavigationBarColor(this);


        vSplash = (ImageView) findViewById(R.id.vsplash);


        WAApplication.me.PhoneStatusHeight = MenuSizeHelper.getStatusHeight(Splash2Activity.this);
        WAApplication.me.PhoneScreenWidth = MenuSizeHelper.getMaxMenuWidth(Splash2Activity.this);
        WAApplication.me.PhoneScreenHeight = MenuSizeHelper.getMaxMenuHeight(Splash2Activity.this);


        if (WAApplication.me.PhoneScreenHeight > 1920) {//有些手机不是1080x1920，变形

            if (vSplash != null) {
                vSplash.setScaleType(ImageView.ScaleType.CENTER_CROP);
            }
        }

        vappver = (TextView) findViewById(R.id.vapp_version);
        btn_Search = (Button) findViewById(R.id.vsplash_search);
        vsplashlog = (ImageView) findViewById(R.id.vsplash_logo);

        if (showIOTButton()) {
            vLayoutSign = (RelativeLayout) findViewById(R.id.layout_sign_up);
            vSignIn = (Button) findViewById(R.id.btn_sign_in);
            vTxtSignUp = (TextView) findViewById(R.id.txt_sign_up);

            String text = (String) vTxtSignUp.getText();
            String strHighlight = "Sign up";
            int index = text.indexOf(strHighlight);
            SpannableString spannableString = new SpannableString(text);
            spannableString.setSpan(new ClickableSpan() {
                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(GlobalUIConfig.color_app_theme);
                    ds.setUnderlineText(true);
                }

                @Override
                public void onClick(View widget) {
                }
            }, index, index + strHighlight.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            vTxtSignUp.setHighlightColor(Color.TRANSPARENT);
            vTxtSignUp.setText(spannableString);
        }

        String tmpVersion = WAApplication.me.getVersion();
        int index = tmpVersion.lastIndexOf(".");//将版本号日期，CommitID给除掉
        tmpVersion = tmpVersion.substring(0, index);
        vappver.setText(tmpVersion);

        updateTheme();

        this.initSearchButton();

        vsplashlog.setVisibility(View.VISIBLE);


        if (vsplashlog != null) {

            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) vsplashlog.getLayoutParams();
            if (params != null) {
                params.addRule(RelativeLayout.CENTER_IN_PARENT);
                vsplashlog.setLayoutParams(params);
            }
        }


        bindSlots();

        //有些设置可以异步执行
    }

    private void startAlphaAnim(ImageView view) {
        AlphaAnimation aa = new AlphaAnimation(0.1f, 1.0f);
        aa.setDuration(3000);
        view.startAnimation(aa);
        aa.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationEnd(Animation arg0) {
                if (showIOTButton()) {
                    vLayoutSign.setVisibility(View.VISIBLE);
                } else {
                    initView();
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }

            @Override
            public void onAnimationStart(Animation animation) {
            }

        });
    }

    /**
     * @return
     * <AUTHOR> changed on 2015年8月11日
     * @description:( 是否有搜索按钮)
     */
    private boolean hasSearchButton() {

        return false;
    }

    private void clickSearchBtn() {

        if (uihd == null)
            return;

        uihd.post(new Runnable() {
            @Override
            public void run() {

                initView();
            }
        });
    }

    /**
     * <AUTHOR> changed on 2015年8月11日
     * @description:(加载搜索按钮)
     */
    private void initSearchButton() {

        if (hasSearchButton()) {
            // 有搜索按钮
            btn_Search.setVisibility(View.VISIBLE);
            btn_Search.setOnClickListener(view -> clickSearchBtn());
            return;
        }

        // 如果没有搜索按钮，等待500ms，自动搜索
        int delayTime = 2500;

        btn_Search.setVisibility(View.GONE);
        uihd.postDelayed(() -> {
            if (showIOTButton()) {
                vLayoutSign.setVisibility(View.VISIBLE);
            } else {
                initView();
            }
        }, delayTime);
    }

    private void startPrivacy() {

        //判断是否需要显示privacy
        boolean hasNewVersion = LegalManager.INSTANCE.isNewVersionAvailable(this.getApplicationContext());
        LogsUtil.i(AppLogTagUtil.LogTag, "startPrivacy:hasNewVersion:" + hasNewVersion);
        if (hasNewVersion) {
            startActivity(new Intent(this, ContainerActivity.class).putExtra(FRAGMENT_TAG, ContainerActivity.PRIVACY_DETAILS));
        } else {
            Logger.d("Splash2Activity", "to HomePagesActivity");
            Intent intent = new Intent(mContext, HomePagesActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            startActivity(intent);
        }
    }

    @SuppressLint("CheckResult")
    private void initView() {

        startPrivacy();
        // Hide content
        if (vsplashlog != null) {
            vsplashlog.setVisibility(View.INVISIBLE);
        }
        isProcessed = true;
    }


    public void updateTheme() {


        if (hasSearchButton()) {

            btn_Search.setText(SkinResourcesUtils.getString("content_Search"));

            StateListDrawable draw2 = (StateListDrawable) ResourceManage.getInstance(getApplicationContext())
                    .getBtnDrawable(getResources(), SkinResourcesValue.getSkinPackname(),
                            "global_button_default", "global_button_highlighted");
            if (draw2 != null && btn_Search != null) {
                btn_Search.setBackgroundDrawable(draw2);
            }
        }

        if (vappver != null)
            vappver.setTextColor(GlobalUIConfig.color_ez_normal);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        // TODO Auto-generated method stub
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            Intent i = new Intent(Intent.ACTION_MAIN);
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            i.addCategory(Intent.CATEGORY_HOME);
            startActivity(i); // onResume

            // finish(); //onDestroy

            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == OVERLAY_PERMISSION_REQ_CODE) {
            if (Build.VERSION.SDK_INT >= 23) {
                if (!Settings.canDrawOverlays(this)) {
                    //权限授予失败
                    WAApplication.me.exitApp();
                }
            }
        } else if (requestCode == ACT_RESULT_BACK) {
            initView();
        }
    }

    /**
     * 检查是否连接上wifi
     *
     * @return
     */
    private boolean getNetWorkState() {
        // 检测WIFI 是否通畅
        ConnectivityManager connManager = (ConnectivityManager) this.getSystemService(Context.CONNECTIVITY_SERVICE);
        State state = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI).getState();

        // Wifi 没连接提示去连接
        if (state != State.CONNECTED) {
            return false;
        } else {
            return true;
        }
    }

    private void bindSlots() {

        if (btnNext != null) {
            btnNext.setOnClickListener(v -> {
            });
        }
    }

    private boolean showIOTButton() {
        return false;
    }

}
