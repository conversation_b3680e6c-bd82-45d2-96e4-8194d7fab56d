package com.wifiaudio.app.debug

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.EditText
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.widget.addTextChangedListener
import com.harman.bar.app.R
import com.harman.DebugActivity
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.databinding.ActivityDevelopConfigurationBinding
import com.harman.log.Logger
import com.harman.report.OneCloudReporter
import com.harman.webview.DashboardDebugHybridActivity
import com.harman.utils.Utils
import com.linkplay.baseui.FragmentTabUtils
import com.tencent.mmkv.MMKV
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.utils.statusbar.StatusBarUtils
import config.LogTags

class DevelopConfigurationActivity : AppCompatActivity(), View.OnClickListener, CompoundButton.OnCheckedChangeListener {

    private var closeIcon: ImageView? = null
    private var showCustomerService: CheckBox? = null
    private var showCloseAllOobePopups: CheckBox? = null
    private var circleUpgrade: CheckBox? = null
    private var useRemoteDashboardUrl: CheckBox? = null
    private var etRemoteDashboardUrl: EditText? = null
    private var oneCloudAnalyticsForceReport: CheckBox? = null

    private var binding: ActivityDevelopConfigurationBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        StatusBarUtils.setCustomStatusBarColor(this, true, ActivityCompat.getColor(this, R.color.bg_surface))

        val binding = ActivityDevelopConfigurationBinding.inflate(layoutInflater)
        <EMAIL> = binding

        binding.activity = this@DevelopConfigurationActivity
        binding.lifecycleOwner = this@DevelopConfigurationActivity

        setContentView(binding.root)
        initView()
    }

    private fun initView() {
        closeIcon = findViewById(R.id.iv_close)
        showCustomerService = findViewById(R.id.cb_show_customer_service)
        showCloseAllOobePopups = findViewById(R.id.cb_show_close_all_oobe_popups)
        circleUpgrade = findViewById(R.id.cb_allow_circle_upgrade)
        useRemoteDashboardUrl = findViewById(R.id.cb_remote_dashboard_url)
        etRemoteDashboardUrl = findViewById(R.id.et_ip_and_port)
        oneCloudAnalyticsForceReport = findViewById(R.id.cb_force_report)

        closeIcon?.setOnClickListener(this)
        showCustomerService?.setOnCheckedChangeListener(this)
        showCloseAllOobePopups?.setOnCheckedChangeListener(this)
        circleUpgrade?.setOnCheckedChangeListener(this)
        useRemoteDashboardUrl?.setOnCheckedChangeListener(this)
        oneCloudAnalyticsForceReport?.setOnCheckedChangeListener(this)

        showCustomerService?.isChecked = MMKV.defaultMMKV().decodeBool("SHOW_CUSTOMER_SERVICE", false)
        showCloseAllOobePopups?.isChecked = MMKV.defaultMMKV().decodeBool("SHOW_CLOSE_ALL", "debug" == BuildConfig.BUILD_TYPE && BuildConfig.DEBUG)

        circleUpgrade?.isChecked = DebugConfigKey.circleUpgrade

        useRemoteDashboardUrl?.isChecked = DebugConfigKey.useRemoteDashboardUrl

        etRemoteDashboardUrl?.setText(DebugConfigKey.remoteDashboardUrl)
        etRemoteDashboardUrl?.addTextChangedListener { et ->
            DebugConfigKey.remoteDashboardUrl = et?.toString()
        }

        oneCloudAnalyticsForceReport?.isChecked = OneCloudReporter.isReportEveryTime

        findViewById<View>(R.id.debug_activity_entrance).setOnClickListener {
            startActivity(Intent(this@DevelopConfigurationActivity, DebugActivity::class.java))
        }

        findViewById<View>(R.id.debug_hybrid_dashboard).setOnClickListener {
            DashboardDebugHybridActivity.portal(activity = this)
        }
    }

    override fun onStart() {
        super.onStart()
        LogsUtil.d(TAG, "onStart")
    }

    override fun onResume() {
        super.onResume()
        LogsUtil.d(TAG, "onResume")
    }

    override fun onPause() {
        super.onPause()
        LogsUtil.d(TAG, "onPause")
    }

    override fun onStop() {
        super.onStop()
        LogsUtil.d(TAG, "onStop")
    }

    override fun onDestroy() {
        super.onDestroy()
        LogsUtil.d(TAG, "onDestroy")
    }

    companion object {
        val TAG: String = DevelopConfigurationActivity::class.java.simpleName
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_close -> {
                finish()
            }
        }
    }

    override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
        LogsUtil.d(TAG, "onCheckedChanged")
        buttonView?.let { view ->
            when (view.id) {
                R.id.cb_show_customer_service -> {
                    if (isChecked) {
                        MMKV.defaultMMKV().encode("SHOW_CUSTOMER_SERVICE", true)
                    } else {
                        MMKV.defaultMMKV().encode("SHOW_CUSTOMER_SERVICE", false)
                    }
                }

                R.id.cb_show_close_all_oobe_popups -> {
                    if (isChecked) {
                        MMKV.defaultMMKV().encode("SHOW_CLOSE_ALL", true)
                    } else {
                        MMKV.defaultMMKV().encode("SHOW_CLOSE_ALL", false)
                    }
                }
                R.id.cb_allow_circle_upgrade -> {
                    DebugConfigKey.circleUpgrade = isChecked
                }
                R.id.cb_remote_dashboard_url -> {
                    DebugConfigKey.useRemoteDashboardUrl = isChecked
                }
                R.id.cb_force_report -> {
                    OneCloudReporter.isReportEveryTime = isChecked
                }
                else -> Unit
            }
        }
    }

    fun onOneCloudAnalyticsLogClick() {
        FragmentTabUtils.addFragment(
            this,
            OneCloudReportLocalFragment(),
            R.id.fragment_container,
            true,
            true
        )
    }
}