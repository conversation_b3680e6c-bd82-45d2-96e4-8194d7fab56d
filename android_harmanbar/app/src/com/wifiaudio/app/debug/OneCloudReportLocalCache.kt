package com.wifiaudio.app.debug

import android.content.Context
import android.util.Log
import androidx.annotation.WorkerThread
import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import com.harman.discover.bean.Device
import com.harman.log.DateUtils
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.FileWriter

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/14.
 */
object OneCloudReportLocalCache {

    private const val TAG = "OneCloudReportLocalCache"

    private val lineSeparator = System.getProperty("line.separator", "\r\n")

    @WorkerThread
    fun saveDeviceReport(context: Context?, device: Device, jsStr: String) {
        context ?: return

        val dirName = getLogDirName(context = context)
        if (dirName.isNullOrBlank()) {
            Log.e(TAG, "saveDeviceReport: dirName is null or blank")
            return
        }

        val dir = File(dirName)
        if (!dir.exists() && !dir.mkdirs()) {
            Log.e(TAG, "saveDeviceReport: mkdirs failed")
            return
        }

        val currentMills = System.currentTimeMillis()
        val todayFileName = DateUtils.getTimeTextYMD(currentMills) + ".txt"

        // Delete other date files
        cleanOldFiles(dir, todayFileName)

        // Create today's file if not exists
        val todayFile = File(dirName, todayFileName)
        
        try {
            if (!todayFile.exists() && !todayFile.createNewFile()) {
                Log.d(TAG, "saveDeviceReport: createNewFile fail")
                return
            }

            val fileWriter = FileWriter(todayFile.path, true) // Append mode
            fileWriter.use {
                fileWriter.write(device = device, jsStr = jsStr)
            }
        } catch (e: Exception) {
            Log.e(TAG, "saveDeviceReport: exception while writing to file", e)
        }
    }

    @WorkerThread
    private fun FileWriter.write(device: Device, jsStr: String) {
        val fileWriter = this

        // Format device info and content
        val time = DateUtils.formatDateTime(System.currentTimeMillis())

        val deviceInfo = "PID[${device.pid}] UUID[${device.UUID}]"
        val reportContent = "$deviceInfo\n${formatJson(jsStr)}"

        fileWriter.write("$time: ")
        fileWriter.write(reportContent)
        fileWriter.write(lineSeparator)
        fileWriter.flush()
    }

    @WorkerThread
    private fun cleanOldFiles(dir: File, todayFileName: String) {
        val files = dir.listFiles()
        
        files?.forEach { file ->
            if (file.isFile && file.name != todayFileName) {
                val deleted = file.delete()
                Log.d(TAG, "cleanOldFiles: deleted ${file.name}: $deleted")
            }
        }
    }

    private fun getLogDirName(context: Context): String? {
        val files = context.getExternalFilesDirs(null)
        if (!files.isNullOrEmpty() && files[0] != null && files[0].exists()) {
            return files[0].absolutePath + File.separator + "OneCloudReportLocalCache"
        }

        return null
    }

    /**
     * 读取LogDir目录下所有文件的内容并返回合并后的字符串
     * @param context 上下文
     * @return 所有文件内容的合集，如果没有文件或读取失败则返回空字符串
     */
    @WorkerThread
    fun readAllLogs(context: Context?): String {
        context ?: return ""
        
        val dirName = getLogDirName(context)
        if (dirName.isNullOrBlank()) {
            Log.e(TAG, "readAllLogs: dirName is null or blank")
            return ""
        }
        
        val dir = File(dirName)
        if (!dir.exists() || !dir.isDirectory) {
            Log.e(TAG, "readAllLogs: directory does not exist")
            return ""
        }
        
        val files = dir.listFiles()?.filter { it.isFile && it.name.endsWith(".txt") }
        if (files.isNullOrEmpty()) {
            Log.d(TAG, "readAllLogs: no log files found")
            return ""
        }
        
        val contentBuilder = StringBuilder()
        
        try {
            // 按文件名排序，通常日期格式的文件名可以按字母顺序排序
            files.sortedBy { it.name }.forEach { file ->
                contentBuilder.append("===== FILE: ${file.name} =====\n")
                
                BufferedReader(FileReader(file)).use { reader ->
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        contentBuilder.append(line).append("\n")
                    }
                }
                
                contentBuilder.append("\n")
            }
            
            Log.d(TAG, "readAllLogs: successfully read ${files.size} files")
            return contentBuilder.toString()
        } catch (e: Exception) {
            Log.e(TAG, "readAllLogs: exception while reading files", e)
            return ""
        }
    }

    private val gsonBuilder by lazy {
        GsonBuilder().setPrettyPrinting().create()
    }

    private fun formatJson(jsonString: String): String =
        try {
            val jsonElement = JsonParser.parseString(jsonString)
            gsonBuilder.toJson(jsonElement)
        } catch (e: Exception) {
            jsonString
        }
}
