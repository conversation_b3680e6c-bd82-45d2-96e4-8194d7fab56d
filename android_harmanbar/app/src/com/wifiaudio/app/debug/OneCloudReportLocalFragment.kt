package com.wifiaudio.app.debug

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.databinding.FragmentOneCloudReportLocalFragmentBinding
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harmanbar.ble.utils.GsonUtil
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/14.
 */
class OneCloudReportLocalFragment : Fragment() {

    private val gson = GsonUtil.GSON

    private val _log = MutableLiveData<String?>()
    val log: LiveData<String?>
        get() = _log

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentOneCloudReportLocalFragmentBinding.inflate(
            inflater,
            container,
            false
        )

        binding.fragment = this@OneCloudReportLocalFragment
        binding.lifecycleOwner = this@OneCloudReportLocalFragment

        return binding.root
    }

    override fun onStart() {
        super.onStart()

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            _log.value = withContext(DISPATCHER_IO) {
                OneCloudReportLocalCache.readAllLogs(context)
            }
        }
    }

}