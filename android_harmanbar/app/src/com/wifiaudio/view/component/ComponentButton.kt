package com.wifiaudio.view.component

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.harman.bar.app.R
import com.harman.utils.ViewUtils


class ComponentButton : ConstraintLayout, ValueAnimator.AnimatorUpdateListener {

    private var btnRootLayout: ConstraintLayout? = null
    private var btnText: ComponentTextView? = null
    private var btnFrontIcon: ImageView? = null
    private var btnRearIcon: ImageView? = null
    private val normalColor = 0
    private val pressedColor = 0
    private val enabledColor = 0
    private val gravity = 0
    private val radiusSize = 0
    private val viewUtils = ViewUtils()
    private var rotationAnimator: ValueAnimator? = null
    private var btnType = ComponentConfig.BTN_REGULAR

    constructor(context: Context) : this(context, null) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        init(context, attrs)
    }

    @SuppressLint("CustomViewStyleable")
    private fun init(context: Context, attrs: AttributeSet?) {
        LayoutInflater.from(context).inflate(R.layout.layout_component_button, this, true)
        val array = context.obtainStyledAttributes(attrs, R.styleable.ComponentButton)

        val btnTextStr = array.getString(R.styleable.ComponentButton_btn_text)
        val btnTextSize = array.getFloat(R.styleable.ComponentButton_btn_text_size, 14f)
        val textFont = array.getInteger(R.styleable.ComponentButton_btn_text_font, ComponentConfig.Button)
        btnType = array.getInteger(R.styleable.ComponentButton_btn_type, ComponentConfig.BTN_REGULAR)
        val btnTextColor = array.getInteger(R.styleable.ComponentButton_btn_text_color, -1)
        val btnFrontIconRes = array.getResourceId(R.styleable.ComponentButton_btn_front_icon_res, -1)
        val btnRearIconRes = array.getResourceId(R.styleable.ComponentButton_btn_rear_icon_res, -1)
        val textAllCaps = array.getBoolean(R.styleable.ComponentButton_textAllCaps, true)

        array.recycle()

        btnRootLayout = findViewById(R.id.btn_root_layout)
        btnText = findViewById(R.id.btn_text)
        btnFrontIcon = findViewById(R.id.btn_icon_front)
        btnRearIcon = findViewById(R.id.btn_icon_rear)
        initAnimator()

        btnText?.typeface = FontUtil.getFontTypefaceByType(context, textFont)
        btnText?.textSize = btnTextSize

        decoBtn(btnType = btnType)

        if (btnTextColor != -1) {
            btnText?.setTextColor(btnTextColor)
        }

        if (!TextUtils.isEmpty(btnTextStr)) {
            btnText?.text = btnTextStr
        }

        if (btnFrontIconRes != -1) {
            btnFrontIcon?.setImageResource(btnFrontIconRes)
            btnFrontIcon?.isVisible = true
        }

        if (btnRearIconRes != -1) {
            btnRearIcon?.setImageResource(btnRearIconRes)
            btnRearIcon?.isVisible = true
        }

        if (textAllCaps) {
            btnText?.isAllCaps = true
        }
    }

    private fun initAnimator() {
        rotationAnimator = ValueAnimator.ofFloat(0f, 360f)
        rotationAnimator?.interpolator = LinearInterpolator()
        rotationAnimator?.repeatCount = ValueAnimator.INFINITE
        rotationAnimator?.repeatMode = ValueAnimator.RESTART
        rotationAnimator?.duration = 1500
        rotationAnimator?.addUpdateListener(this)
    }

    /**
     * 得到实心的drawable, 一般作为选中，点中的效果
     *
     * @param cornerRadius 圆角半径
     * @param solidColor   实心颜色
     * @return 得到实心效果
     */
    fun getSolidRectDrawable(cornerRadius: Int, solidColor: Int): GradientDrawable {
        val gradientDrawable = GradientDrawable()
        // 设置矩形的圆角半径
        gradientDrawable.cornerRadius = cornerRadius.toFloat()
        // 设置绘画图片色值
        gradientDrawable.mutate()
        gradientDrawable.setColor(solidColor)
        // 绘画的是矩形
        gradientDrawable.gradientType = GradientDrawable.RADIAL_GRADIENT
        return gradientDrawable
    }

    fun setBtnBackground(background: Drawable) {
        btnRootLayout?.background = background
        invalidate()
    }

    fun setBtnTextColor(context: Context, color: Int) {
        btnText?.setTextColor(context.getColor(color))
        invalidate()
    }

    fun setBtnText(btnTextStr: String?) {
        btnText?.text = btnTextStr
        invalidate()
    }

    fun getBtnText(): String {
        return btnText?.text.toString()
    }

    fun setBtnTextFont(context: Context, fontStyle: Int) {
        btnText?.paint?.let { FontUtil.setTextStyle(context, it, fontStyle) }
        invalidate()
    }

    fun setBtnTextSize(textSize: Float) {
        btnText?.textSize = textSize
        invalidate()
    }

    fun setBtnEnabled(isEnabled: Boolean) {
        btnRootLayout?.isEnabled = isEnabled
        //just support BTN_REGULAR
        if (btnType == ComponentConfig.BTN_REGULAR) {
            if (isEnabled) {
                btnText?.setTextColor(context.getColor(R.color.fg_inverse))
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_inverse))
            } else {
                btnText?.setTextColor(context.getColor(R.color.fg_disabled))
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_disable))
            }
        }
        invalidate()
    }

    fun isBtnEnable(): Boolean {
        return btnRootLayout?.isEnabled == true
    }

    /**
     * @param btnType @see [ComponentConfig]
     */
    fun setBtnType(btnType: Int) {
        this.btnType = btnType
        decoBtn(btnType = btnType)
        invalidate()
    }

    private fun decoBtn(btnType: Int) {
        when (btnType) {
            ComponentConfig.BTN_REGULAR -> {
                btnRootLayout?.isEnabled = true
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_inverse))
                btnText?.setTextColor(context.getColor(R.color.fg_inverse))
                btnText?.visibility = View.VISIBLE
                btnFrontIcon?.visibility = View.GONE
                stopAnimation()
            }

            ComponentConfig.BTN_TEXT_BUTTON -> {
                btnRootLayout?.isEnabled = true
                btnRootLayout?.background = null
                btnText?.setTextColor(context.getColor(R.color.fg_primary))
                btnText?.visibility = View.VISIBLE
                btnFrontIcon?.visibility = View.GONE
                stopAnimation()
            }

            ComponentConfig.BTN_WARNING -> {
                btnRootLayout?.isEnabled = true
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_on_card))
                btnText?.setTextColor(context.getColor(R.color.red_1))
                btnText?.visibility = View.VISIBLE
                btnFrontIcon?.visibility = View.GONE
                stopAnimation()
            }

            ComponentConfig.BTN_DISABLE -> {
                btnRootLayout?.isEnabled = false
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_disable))
                btnText?.setTextColor(context.getColor(R.color.fg_disabled))
                btnText?.visibility = View.VISIBLE
                btnFrontIcon?.visibility = View.GONE
                stopAnimation()
            }

            ComponentConfig.BTN_NON_EMPHASIS -> {
                btnRootLayout?.isEnabled = true
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_on_card))
                btnText?.setTextColor(context.getColor(R.color.fg_primary))
                btnText?.visibility = View.VISIBLE
                btnFrontIcon?.visibility = View.GONE
                stopAnimation()
            }

            ComponentConfig.BTN_LOADING -> {
                btnRootLayout?.isEnabled = true
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_inverse))
                btnText?.setTextColor(context.getColor(R.color.fg_inverse))
                btnFrontIcon?.setImageResource(R.drawable.svg_icon_vector_loading_3)
                btnText?.visibility = View.GONE
                btnFrontIcon?.visibility = View.VISIBLE
                startAnimation()
            }

            else -> {
                btnRootLayout?.isEnabled = true
                btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, R.color.bg_inverse))
                btnText?.setTextColor(context.getColor(R.color.fg_inverse))
                btnText?.visibility = View.VISIBLE
                btnFrontIcon?.visibility = View.GONE
                stopAnimation()
            }
        }
    }

    fun setBtnImageResource(resId: Int) {
        btnFrontIcon?.setImageResource(resId)
    }

    fun setBtnBackgroundColor(colorId: Int) {
        btnRootLayout?.background = getSolidRectDrawable(dip2px(context, 30f), ContextCompat.getColor(context, colorId))
    }

    /**
     * 背景选择器
     *
     * @param pressedDrawable 按下状态的Drawable
     * @param normalDrawable  正常状态的Drawable
     * @return 状态选择器
     */
    private fun getStateListDrawable(pressedDrawable: Drawable?, normalDrawable: Drawable?): StateListDrawable {
        val stateListDrawable = StateListDrawable()
        stateListDrawable.addState(intArrayOf(android.R.attr.state_enabled, android.R.attr.state_pressed), pressedDrawable)
        stateListDrawable.addState(intArrayOf(android.R.attr.state_enabled), normalDrawable)
        //设置不能用的状态
        //默认其他状态背景
        val gray = getSolidRectDrawable(radiusSize, enabledColor)
        stateListDrawable.addState(intArrayOf(), gray)
        return stateListDrawable
    }

    fun dip2px(context: Context, dipValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dipValue * scale + 0.5f).toInt()
    }

    fun px2dip(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    override fun onAnimationUpdate(animation: ValueAnimator) {
        val rotation = animation.animatedValue as Float
        btnFrontIcon?.rotation = rotation
    }

    fun startAnimation() {
        rotationAnimator?.let {
            if (it.isRunning) {
                it.cancel()
            }
            it.start()
        }
    }

    fun stopAnimation() {
        rotationAnimator?.let {
            if (it.isRunning) {
                it.cancel()
            }
        }
    }

    companion object {
        private val TAG = ComponentButton::class.java.simpleName
    }

}