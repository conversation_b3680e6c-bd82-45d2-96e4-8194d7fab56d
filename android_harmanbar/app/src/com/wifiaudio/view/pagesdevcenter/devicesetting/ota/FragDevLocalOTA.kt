package com.wifiaudio.view.pagesdevcenter.devicesetting.ota

import android.animation.ValueAnimator
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil
import com.linkplay.ota.model.OTAStatus
import com.linkplay.ota.presenter.LinkplayOTA
import com.linkplay.ota.presenter.LinkplayOTAListener
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.GsonParseUtil
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp
import com.wifiaudio.view.pagesdevcenter.devicesetting.FragDevSettingBaseUI
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLDataUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLErrorCode
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.AppLogTagUtil
import config.LogTags

class FragDevLocalOTA : FragDevSettingBaseUI() {

    var vback: Button? = null
    var id_progress: ProgressBar? = null
    var iv_logo: ImageView? = null
    var tv_remain: TextView? = null
    var tv_progress: TextView? = null
    var tv_label_1: TextView? = null
    var tv_label_2: TextView? = null
    var tv_label_3: TextView? = null
    var tv_label_4: TextView? = null
    var dataInfo: DataFragInfo? = null
    var fwRsp: CheckFWResp? = null

    var linkplayOTA: LinkplayOTA? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        cview = inflater.inflate(R.layout.frag_device_local_ota, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        checkOTA()

        return cview
    }

    override fun onResume() {
        super.onResume()
        if (activity?.window != null)
            activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onPause() {
        super.onPause()
        if (activity?.window != null)
            activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun initView() {
        super.initView()

        tv_label_1 = cview.findViewById(R.id.tv_label_1)
        tv_label_2 = cview.findViewById(R.id.tv_label_2)
        tv_label_3 = cview.findViewById(R.id.tv_label_3)
        tv_label_4 = cview.findViewById(R.id.tv_label_4)
        iv_logo = cview.findViewById(R.id.iv_logo)
        id_progress = cview?.findViewById(R.id.id_progress)
        tv_progress = cview?.findViewById(R.id.tv_progress)
        tv_remain = cview?.findViewById(R.id.tv_time)
        vback = cview.findViewById(R.id.vback)
        vback?.setBackgroundResource(R.drawable.select_icon_menu_back)

        tv_progress?.apply {
            setTextColor(Color.RED)
        }

        tv_label_1?.apply {
            text = SkinResourcesUtils.getString("jbl_Software_Update")
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)
        }

        tv_label_2?.apply {
            text = SkinResourcesUtils.getString("newStructure_Do_NOT_turn_off_product")
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_secondary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        tv_label_3?.apply {
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        tv_label_4?.apply {
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_secondary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        iv_logo?.apply {
            val imgName = dataInfo?.deviceItem?.let {
                DeviceImageUtil.getDeviceImgNameByDeviceItem(it)
            }
            val imgDraw = SkinResourcesUtils.getMipMapDrawable(imgName)
            if (imgDraw != null) setImageDrawable(imgDraw)
        }

        vback?.apply {

            isEnabled = false
            isClickable = false

            /*val btnImgDraw = context.resources.getDrawable(R.drawable.select_icon_menu_back)
            var wrappedImgDraw = SkinResourcesUtils.getWrapDrawable(btnImgDraw)
            wrappedImgDraw = SkinResourcesUtils.getTintListDrawable(
                wrappedImgDraw,
                SkinResourcesUtils.getBtnClickColorStateList(
                    GlobalUIConfig.color_gray,
                    GlobalUIConfig.color_gray
                )
            )
            background = wrappedImgDraw*/
        }

        updateProgress(0)
        updateProgressInfo(SkinResourcesUtils.getString("Downloading"))

//        uihd.postDelayed({
//            val animProgressTimer: Long = 2500
//            startProgressAnimator(0, 100, animProgressTimer, dataInfo?.deviceItem)
//        }, 5000)
    }

    override fun bindSlots() {
        super.bindSlots()
    }

    override fun initUtils() {
        super.initUtils()
    }

    private fun checkOTA() {

        showCheckUpdateLoading(true)

        DeviceCustomerSettingAction.makeDeviceRequestDeviceOta(dataInfo?.deviceItem?.IP, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {

                showCheckUpdateLoading(false)

                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "request device ota success: $content")
                var errorCodeItem = content?.let {
                        GsonParseUtil.instance.fromJson(it, JBLErrorCode::class.java)
                    }

                if (TextUtils.equals(errorCodeItem?.error_code, "0")) {
                    startOTA()
                }
            }

            override fun onFailure(e: Throwable?) {
                showCheckUpdateLoading(false)
                LogsUtil.i(AppLogTagUtil.Firmware_TAG, "request device ota fail: ${e?.localizedMessage}")
            }
        })
    }

    private fun startOTA() {
        LogsUtil.i(AppLogTagUtil.Firmware_TAG, "start ota")

        val timeout = 3 * 60 * 1000

        linkplayOTA = LinkplayOTA(dataInfo?.deviceItem, object : LinkplayOTAListener {
            override fun otaStatusUpdated(otaStatus: OTAStatus) {
                showCheckUpdateLoading(false)
                updateOtaStatus(otaStatus)
            }

            override fun otaSuccess(deviceItem: DeviceItem) {

                onOTASuccess(deviceItem)
            }

            override fun otaFailed(otaStatus: OTAStatus) {
                onOTAFailed(otaStatus, timeout)
            }
        })

        linkplayOTA?.firmwareStartUpdate()
    }

    private fun showCheckUpdateLoading(shown: Boolean) {

        if (activity == null) return

        uihd.post {
            if (shown) {
                WAApplication.me.showProgDlg(activity, true, SkinResourcesUtils.getString("devicelist_Please_wait"))
            } else {
                WAApplication.me.showProgDlg(activity, false, null)
            }
        }
    }

    private fun updateOtaStatus(otaStatus: OTAStatus?) {
        if (otaStatus == null) return

        uihd.post {

            when (otaStatus.status) {

                OTAStatus.MV_UP_STATUS_DOWNLOAD_START -> {
//                    tv_state?.text = SkinResourcesUtils.getString("devicelist_Download") + "..."
//                    tv_stage_status?.text =
//                        SkinResourcesUtils.getString("newadddevice_Stage") + ": 1/3"
                }

                OTAStatus.MV_UP_STATUS_WRITE_START -> {
//                    tv_state?.text = SkinResourcesUtils.getString("devicelist_Update") + "..."
//                    tv_stage_status?.text =
//                        SkinResourcesUtils.getString("newadddevice_Stage") + ": 2/3"
                }

                OTAStatus.MV_UP_STATUS_COMPLETE -> {
//                    tv_state?.text =
//                        SkinResourcesUtils.getString("devicelist_Device_Reboot") + "..."
//                    tv_stage_status?.text =
//                        SkinResourcesUtils.getString("newadddevice_Stage") + ": 3/3"
                }
            }

            if (otaStatus.getRemainTime() > 0) {
                var remain = otaStatus.getRemainTime() / 1000

                if (remain > 60) {
                    var min = remain / 60
                    //这里文字秒数是粗略计算，则整除后的剩余秒数不计

                    if (min >= 22)
                        min = 22

                    tv_remain?.text = String.format(SkinResourcesUtils.getString("jbl____MINUTE"), min)

                } else if (remain <= 60) {
                    remain = 1
                    tv_remain?.text = String.format(SkinResourcesUtils.getString("jbl____MINUTE"), remain)
                }
            }

            val progress =
                (otaStatus.downloadPercent + otaStatus.updatePercent + otaStatus.rebootPercent) / 3

            updateProgress(progress)
            //id_progress?.progress = progress
        }
    }

    private fun updateProgress(progress: Int) {
        tv_progress?.text = "$progress %"
        id_progress?.progress = progress
    }

    private fun updateProgressInfo(info: String) {
        tv_label_3?.apply {
            text = info
        }

        tv_label_4?.apply {
            text = SkinResourcesUtils.getString("3 min remain")
        }
    }

    private fun startProgressAnimator(start: Int, end: Int, animTime: Long, deviceItem: DeviceItem?) {

        val valueAnimator = ValueAnimator.ofInt(start, end)
        valueAnimator.duration = animTime
        valueAnimator.addUpdateListener {

            val i = Integer.valueOf(it.animatedValue.toString())

            id_progress?.progress = i

            if (i >= 100) {

                if (deviceItem == null) return@addUpdateListener

                WAApplication.me.deviceItem = WAUpnpDeviceManager.me().getDeviceItemByMac(deviceItem.devStatus.mac)

                uihd.post {



                    otaSuccess(deviceItem)
                }
            }
        }

        valueAnimator.start()
    }

    private fun onOTASuccess(deviceItem: DeviceItem?) {

        updateSuccessUI(deviceItem)
    }

    private fun updateSuccessUI(deviceItem: DeviceItem?) {

        val animRemainTimer: Long = 2000

        var txtRemain: String? = tv_remain?.text.toString()

        var timer: Int? = (txtRemain?.replace(String.format(SkinResourcesUtils.getString("jbl____MINUTE"), ""), "")
            ?.replace(String.format(SkinResourcesUtils.getString("jbl____MINUTE"), ""), "")
            ?.trim())?.toInt()

        if (timer != null) {
            startTVRemainAnimator(timer, 1, animRemainTimer)
        }

        val animProgressTimer: Long = 2500

        var tmpProgress: Int? = id_progress?.progress

        if (tmpProgress != null) {
            startProgressAnimator(tmpProgress, 100, animProgressTimer, deviceItem)
        }
    }

    private fun startTVRemainAnimator(start: Int, end: Int, animTime: Long) {

        val valueAnimator = ValueAnimator.ofInt(start, end)
        valueAnimator.duration = animTime
        valueAnimator.addUpdateListener {

            val i = Integer.valueOf(it.animatedValue.toString())

            tv_remain?.text = String.format(SkinResourcesUtils.getString("jbl____MINUTE"), i)
        }

        valueAnimator.start()
    }

    private fun onOTAFailed(otaStatus: OTAStatus, timeout: Int) {

        uihd.post {
            tv_remain?.visibility = View.INVISIBLE
        }

        showCheckUpdateLoading(false)

        if (otaStatus.status != OTAStatus.MV_UP_STATUS_OTA_FAILED) {
            deleteDevice()
        }

        var deviceItem = WAUpnpDeviceManager.me().getDeviceItemByMac(dataInfo?.deviceItem?.devStatus?.mac)

        when (otaStatus.status) {
            OTAStatus.MV_UP_STATUS_DOWNLOAD_FAILED -> {

            }

            OTAStatus.MV_UP_STATUS_WRITE_FAILED -> {

            }

            OTAStatus.MV_UP_STATUS_OTA_FAILED -> {

            }
        }

        if (deviceItem == null) {
            otaFailed(JBLDataUtil.OTA_NOT_CONNECTED)
        } else {
            otaFailed(JBLDataUtil.OTA_UPDATE_FAIL)
        }

//        if (otaStatus.status == OTAStatus.MV_UP_STATUS_WRITE_FAILED
//            || otaStatus.status == OTAStatus.MV_UP_STATUS_DOWNLOAD_FAILED
//            || otaStatus.status == OTAStatus.MV_UP_STATUS_OTA_FAILED
//        ) {
//
//        } else {
//
//        }
    }

    private fun deleteDevice() {
        WAApplication.me.reconfirmRecord(dataInfo?.deviceItem?.uuid)
    }


    private fun otaFailed(failedType: Int) {

        var vfrag = FragDevLocalOTAFailed()
        vfrag.dataInfo = dataInfo
        vfrag.failedType = failedType
        vfrag.fwRsp = fwRsp
        dataInfo?.let { it1 ->
            FragTabUtils.addFrag(
                activity,
                it1.frameId,
                vfrag,
                false
            )
        }
    }

    private fun otaSuccess(deviceItem: DeviceItem?) {

        var vfrag = FragDevLocalOTASuccess()
        dataInfo?.deviceItem = deviceItem ?: WAApplication.me.deviceItem
        vfrag.dataInfo = dataInfo
        dataInfo?.let { it1 ->
            FragTabUtils.addFrag(activity, it1.frameId, vfrag, false)
        }
    }
}