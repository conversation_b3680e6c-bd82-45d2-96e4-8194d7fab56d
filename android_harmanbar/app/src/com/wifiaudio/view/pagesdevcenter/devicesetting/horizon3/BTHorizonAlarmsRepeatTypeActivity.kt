package com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.databinding.ActivityHorizonAlarmsRepeatTypeBinding
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.discover.bean.Device
import com.harman.hkone.ScreenUtil
import com.harman.log.Logger
import com.harman.parseAsPartyBoxDevice
import com.harman.utils.Utils
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.WrapContentLinearLayoutManager
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.adapter.AlarmsRepeatTypeAdapter
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.adapter.AlarmsRepeatWeekdayAdapter
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.adapter.GridSpaceItemDecoration2
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.viewmodel.BTHorizonAlarmsViewModel
import config.LogTags

class BTHorizonAlarmsRepeatTypeActivity : AppCompatActivity() {

    private val binding by lazy { ActivityHorizonAlarmsRepeatTypeBinding.inflate(layoutInflater) }

    private val alarmsViewModel by viewModels<BTHorizonAlarmsViewModel>()

    private var mAlarmsRepeatTypeAdapter: AlarmsRepeatTypeAdapter? = null
    private var mAlarmsRepeatWeekdayAdapter: AlarmsRepeatWeekdayAdapter? = null

    private val mainDev by lazy {
        parseAsPartyBoxDevice()
    }

    private val currentAlarm by lazy {
        intent.getSerializableExtra("alarm") as? AlarmInfo.Alarm
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        if (null == mainDev) {

            finish()
            return
        }
        alarmsViewModel.mainDev = mainDev!!
        lifecycle.addObserver(alarmsViewModel)
        setContentView(binding.root)
        initView()
    }

    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) super.attachBaseContext(newBase) else {
            val baseContext = LocaleLanConfigUtil.changeAppLan(newBase)
            WAApplication.me.mAppLanguageContext = baseContext
            super.attachBaseContext(baseContext)
        }
    }

    private fun initView() {
        binding.appbar.ivLeading.setOnClickListener { onBackPressed() }
        binding.appbar.tvTitle.text = SkinResourcesUtils.getString("repeat")
        initRepeatDay()
    }

    private fun initRepeatDay() {
        val weekday = AlarmInfo.RepeatType.Weekday
        val weekend = AlarmInfo.RepeatType.Weekend
        val everyday = AlarmInfo.RepeatType.EveryDay
        val once = AlarmInfo.RepeatType.Once
        val customize = AlarmInfo.RepeatType.Custom
        customize.value = currentAlarm?.repeatType?.value ?: alarmsViewModel.customValue
        Logger.e(TAG, "ALARM_RELATED EDIT_ALARM customize: $customize")
        val repeatTypeList = mutableListOf<AlarmInfo.RepeatType>(weekday, weekend, everyday, once, customize)
        val repeatDayList = mutableListOf<String>("S", "M", "T", "W", "T", "F", "S")
        binding.repeatTypeRecycleView.layoutManager =
            WrapContentLinearLayoutManager(this@BTHorizonAlarmsRepeatTypeActivity, RecyclerView.VERTICAL, false)
        binding.repeatDayRecycleView.layoutManager = GridLayoutManager(this@BTHorizonAlarmsRepeatTypeActivity, repeatDayList.size)
//            binding.repeatDayRecycleView.layoutManager = WrapContentLinearLayoutManager(itActivity, RecyclerView.HORIZONTAL, false)

        (binding.repeatTypeRecycleView.itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false
        (binding.repeatDayRecycleView.itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false


        currentAlarm?.let { itAlarm ->
            Logger.d(TAG, "ALARM_RELATED EDIT_ALARM currentAlarm: $itAlarm")
            val tempAlarm = itAlarm.deepCopy()
            itAlarm.repeatType?.value?.let { repeatTypeValue ->
                alarmsViewModel.customValue = repeatTypeValue
            }
            mAlarmsRepeatTypeAdapter = AlarmsRepeatTypeAdapter(this@BTHorizonAlarmsRepeatTypeActivity, repeatTypeList, itAlarm)
            mAlarmsRepeatWeekdayAdapter = AlarmsRepeatWeekdayAdapter(this@BTHorizonAlarmsRepeatTypeActivity, repeatDayList, itAlarm)

            binding.repeatTypeRecycleView.adapter = mAlarmsRepeatTypeAdapter
            mAlarmsRepeatTypeAdapter?.updateSelectedPosition(itAlarm.repeatType)
            mAlarmsRepeatTypeAdapter?.setOnItemClickListener(object : AlarmsRepeatTypeAdapter.OnItemClickListener {
                override fun onItemClick(view: View, position: Int, type: AlarmInfo.RepeatType) {
                    Logger.d(TAG, "ALARM_RELATED EDIT_ALARM RepeatTypeAdapter itemClick type:$type")
                    when (type) {
                        AlarmInfo.RepeatType.Custom -> {
                            itAlarm.repeatType?.value = alarmsViewModel.customValue
                            binding.repeatDayRecycleView.isEnabled = true
                            mAlarmsRepeatWeekdayAdapter?.setClickable(true)
                            binding.repeatDayRecycleView.alpha = 1.0f
                        }

                        else -> {
                            itAlarm.repeatType?.value = null
                            binding.repeatDayRecycleView.isEnabled = false
                            mAlarmsRepeatWeekdayAdapter?.setClickable(false)
                            binding.repeatDayRecycleView.alpha = 0.5f
                        }
                    }
                    tempAlarm.repeatType = type
                    alarmsViewModel.updateCurrentAlarm(tempAlarm)

                    val intent = Intent()
                    intent.putExtra("repeatType", type)
                    setResult(Activity.RESULT_OK, intent)
                }
            })

            binding.repeatDayRecycleView.layoutManager = GridLayoutManager(this@BTHorizonAlarmsRepeatTypeActivity, repeatDayList.size)
            val itemSpace = ScreenUtil.dip2px(applicationContext, 6f)
            val itemDecorationCount = binding.repeatDayRecycleView.itemDecorationCount
            if (itemDecorationCount == 0) {
//                    binding.repeatDayRecycleView.addItemDecoration(GridSpaceItemDecoration(repeatDayList.size, itemSpace, itemSpace))
                binding.repeatDayRecycleView.addItemDecoration(GridSpaceItemDecoration2(repeatDayList.size, itemSpace, false))
            }
            binding.repeatDayRecycleView.adapter = mAlarmsRepeatWeekdayAdapter

            /*val viewTreeObserver = binding.repeatDayRecycleView.viewTreeObserver
            viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    binding.repeatDayRecycleView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    val recyclerViewWidth = binding.repeatDayRecycleView.width
                    Logger.d(TAG, "repeatDayRecycleView width: $recyclerViewWidth")

                    val allItemWidth = ScreenUtil.dip2px(requireContext(), 48f) * repeatDayList.size
                    if (repeatDayList.size > 1) {
                        itemSpace = ((recyclerViewWidth - allItemWidth) / (repeatDayList.size - 1)) / 2
                    }

                    val itemDecorationCount = binding.repeatDayRecycleView.itemDecorationCount
                    if (itemDecorationCount == 0) {
                        binding.repeatDayRecycleView.addItemDecoration(SpaceItemDecoration(itemSpace, SpaceItemDecoration.HORIZONTAL))
                    }
                    binding.repeatDayRecycleView.adapter = mAlarmsRepeatWeekdayAdapter

                }
            })*/

            mAlarmsRepeatWeekdayAdapter?.setOnItemClickListener(object : AlarmsRepeatWeekdayAdapter.OnItemClickListener {
                override fun onItemClick(view: View, position: Int, selectedList: MutableSet<Int>) {
                    Logger.d(TAG, "ALARM_RELATED EDIT_ALARM RepeatWeekdayAdapter itemClick selectedList:$selectedList")
                    itAlarm.repeatType = AlarmInfo.RepeatType.Custom
                    val customValue = convertSetToBinaryString(selectedList)
                    tempAlarm.repeatType = AlarmInfo.RepeatType.Custom
                    tempAlarm.repeatType?.value = customValue
                    alarmsViewModel.customValue = customValue
                    alarmsViewModel.updateCurrentAlarm(tempAlarm)
                    Logger.i(TAG, "ALARM_RELATED EDIT_ALARM RepeatWeekdayAdapter itemClick customValue:$customValue")
                    Logger.d(TAG, "ALARM_RELATED EDIT_ALARM RepeatWeekdayAdapter itemClick tempAlarm:$tempAlarm")
                }

            })

            itAlarm.repeatType?.let { itRepeatType ->
                when (itRepeatType) {
                    AlarmInfo.RepeatType.Custom -> {
                        itRepeatType.value?.let { itValue ->
                            val valueList = itValue.toCharArray().map { value ->
                                value.toString().toInt()
                            }
                            val mutableSet: MutableSet<Int> = mutableSetOf()

                            // 遍历列表，记录值不为0的索引
                            valueList.forEachIndexed { index, value ->
                                if (value != 0) {
                                    mutableSet.add(index)
                                }
                            }
                            mAlarmsRepeatWeekdayAdapter?.setSelectedPositions(mutableSet)
                            Logger.e(TAG, "ALARM_RELATED EDIT_ALARM itRepeatType value: $valueList, valueList index mutableSet: $mutableSet")
                        }
                        binding.repeatDayRecycleView.isEnabled = true
                        mAlarmsRepeatWeekdayAdapter?.setClickable(true)
                        binding.repeatDayRecycleView.alpha = 1.0f
                    }

                    else -> {
                        binding.repeatDayRecycleView.isEnabled = false
                        mAlarmsRepeatWeekdayAdapter?.setClickable(false)
                        binding.repeatDayRecycleView.alpha = 0.5f
                        alarmsViewModel.customValue.let { itValue ->
                            val valueList = itValue.toCharArray().map { value ->
                                value.toString().toInt()
                            }
                            val mutableSet: MutableSet<Int> = mutableSetOf()

                            // 遍历列表，记录值不为0的索引
                            valueList.forEachIndexed { index, value ->
                                if (value != 0) {
                                    mutableSet.add(index)
                                }
                            }
                            mAlarmsRepeatWeekdayAdapter?.setSelectedPositions(mutableSet)
                            Logger.e(TAG, "ALARM_RELATED EDIT_ALARM itRepeatType value: $valueList, valueList index mutableSet: $mutableSet")
                        }
                    }
                }
            }

        }
    }

    private fun convertSetToBinaryString(set: MutableSet<Int>): String {
        // 确定索引的最大值
        val maxIndex = set.maxOrNull() ?: 0

        // 初始化一个长度为 maxIndex + 1 的数组，初始值为0
        val binaryArray = IntArray(7) { 0 }

        // 遍历 set，将对应索引位置的值设置为1
        for (index in set) {
            if (index in 0 until 7) {
                binaryArray[index] = 1
            }
        }

        // 将数组转换为字符串
        return binaryArray.joinToString(separator = "")
    }

    companion object {
        val TAG: String = BTHorizonAlarmsRepeatTypeActivity::class.java.simpleName

        fun portal(
            activity: Activity,
            device: Device,
            alarm: AlarmInfo.Alarm?,
            requestCode: Int
        ) {
            device.pid?.also {
                val intent: Intent = Intent(activity, BTHorizonAlarmsRepeatTypeActivity::class.java)
                intent.putExtra("UUID", device.UUID)
                intent.putExtra("alarm", alarm)
                Logger.d(TAG, "startActivity")
                activity.startActivityForResult(intent, requestCode)
            }
        }
    }
}