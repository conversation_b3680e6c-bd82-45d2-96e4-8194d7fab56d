package com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.viewmodel

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.syncGetAlarmInfo
import com.harman.connect.syncSetAlarmInfo
import com.harman.discover.bean.PartyBoxDevice
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.view.pagesdevcenter.SingleLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import java.time.DateTimeException
import java.time.Duration
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.locks.ReentrantLock

class BTHorizonAlarmsViewModel : ViewModel(), LifecycleEventObserver {

    lateinit var mainDev: PartyBoxDevice

    var alarmListSorted: MutableList<AlarmInfo.Alarm> = ArrayList()
    var customValue = CUSTOM_INITIAL_VALUE

    private val _alarmInfoLiveData = MutableLiveData<AlarmInfo>()
    var alarmInfoLiveData: LiveData<AlarmInfo> = _alarmInfoLiveData

    private var notifyAlarmList = CopyOnWriteArrayList<AlarmInfo.Alarm>()
    private val _alarmListLiveData = MutableLiveData<MutableList<AlarmInfo.Alarm>>()
    var alarmListLiveData: LiveData<MutableList<AlarmInfo.Alarm>> = _alarmListLiveData

    private val _updateAlarmLiveData = SingleLiveData<Boolean>()
    var updateAlarmLiveData: LiveData<Boolean> = _updateAlarmLiveData

    private val _enableAlarmLiveData = SingleLiveData<Boolean>()
    var enableAlarmLiveData: LiveData<Boolean> = _enableAlarmLiveData

    private val _createAlarmLiveData = SingleLiveData<Boolean>()
    var createAlarmLiveData: LiveData<Boolean> = _createAlarmLiveData

    private val _removeAlarmLiveData = SingleLiveData<Boolean>()
    var removeAlarmLiveData: LiveData<Boolean> = _removeAlarmLiveData

    private val _snoozeStatusLiveData = MutableLiveData<Boolean>()
    var snoozeStatusLiveData: LiveData<Boolean> = _snoozeStatusLiveData

    private val _wakeupSoundLiveData = MutableLiveData<Boolean>()
    var wakeupSoundLiveData: LiveData<Boolean> = _wakeupSoundLiveData


    private val _stopPlayWakeupSoundLiveData = MutableLiveData<Boolean>()
    var stopPlayWakeupSoundLiveData: LiveData<Boolean> = _stopPlayWakeupSoundLiveData


    private val _previewVolumeLiveData = MutableLiveData<Boolean>()
    var previewVolumeLiveData: LiveData<Boolean> = _previewVolumeLiveData

    val ringToneList = mutableListOf<AlarmInfo.RingTone>(
        AlarmInfo.RingTone.Daybreak,
        AlarmInfo.RingTone.Sunbeam,
        AlarmInfo.RingTone.SoftChimes,
        AlarmInfo.RingTone.Forest,
        AlarmInfo.RingTone.Buzzer
    )

    private val _currentAlarmLiveData = SingleLiveData<AlarmInfo.Alarm?>()
    var currentAlarmLiveData: LiveData<AlarmInfo.Alarm?> = _currentAlarmLiveData

    init {
        Logger.d(TAG, "init--->>>")
    }

    fun onStart() {
        mainDev.registerDeviceListener(observer)
    }

    fun onStop() {
        Logger.d(TAG, "onPause:")
        mainDev.unregisterDeviceListener(observer)
    }

    val observer = object : IPartyBoxDeviceListener {
        override fun onAlarmInfo(alarmInfo: AlarmInfo?) {
            super.onAlarmInfo(alarmInfo)
            Logger.i(TAG, "ALARM_RELATED onAlarmInfo:$alarmInfo")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                alarmInfo?.let { itAlarmInfo ->
                    itAlarmInfo.alarmList?.let {
                        notifyAlarmList.clear()
                        notifyAlarmList.addAll(it)
                        _alarmListLiveData.postValue(it)
                    }
                }

            }

            /*(mainDev as? PartyBoxDevice)?.alarmInfo?.let { itAlarmInfo ->
                Logger.i(TAG, "ALARM_RELATED message notify:$itAlarmInfo")
                Logger.d(TAG, "ALARM_RELATED notifyAlarmList:${notifyAlarmList}")
                notifyAlarmList.distinctLast().takeIf { it.isNotEmpty() }?.let {
                    updateAlarmList(it, itAlarmInfo.alarmList)
                } ?: let {
                    itAlarmInfo.alarmList?.let { itAlarmList ->
                        Logger.e(TAG, "ALARM_RELATED message notify notifyAlarmList init:$itAlarmInfo")
                        notifyAlarmList.clear()
                        notifyAlarmList.addAll(itAlarmList)
                        _alarmListLiveData.postValue(notifyAlarmList.toMutableList())
                    }
                }
            }*/
        }

    }

    private fun updateAlarmList(alarmList: MutableList<AlarmInfo.Alarm>, updatedAlarmList: MutableList<AlarmInfo.Alarm>?) {
        if (alarmList.isNotEmpty()) {
            Logger.d(TAG, "ALARM_RELATED updateAlarmList pre alarmList:${alarmList}")
            Logger.i(TAG, "ALARM_RELATED updateAlarmList pre updatedAlarmList:${updatedAlarmList}")
            val alarmMap = alarmList.filter { it.alarmID != null }.associateBy { it.alarmID } // 将现有列表转换为Map，以alarmID为键
            updatedAlarmList?.filter { it.alarmID != null }?.let { itUpdatedAlarmList ->
                for (updatedAlarm in itUpdatedAlarmList) {

                    if (alarmMap.containsKey(updatedAlarm.alarmID)) {
                        val index = alarmList.indexOfFirst { it.alarmID == updatedAlarm.alarmID }
                        if (index != -1) {
                            alarmList[index] = updatedAlarm
                        }
                    } else {
                        // 如果没有找到相同的alarmID，则添加
                        alarmList.add(updatedAlarm)
                    }

                }
            }

            Logger.d(TAG, "ALARM_RELATED updateAlarmList after alarmList:${alarmList}")

            notifyAlarmList.clear()
            notifyAlarmList.addAll(alarmList.distinctLast())
            _alarmListLiveData.postValue(notifyAlarmList.distinctLast())

        }

    }

    private fun <T> MutableList<T>.distinctLast(): MutableList<T> {
        return this.foldRight(mutableListOf<T>()) { item, acc ->
            if (item !in acc) acc.add(0, item)
            acc
        }
    }

    fun getSortedAlarmList(alarmList: MutableList<AlarmInfo.Alarm>): MutableList<AlarmInfo.Alarm> {
        Logger.d(TAG, "ALARM_RELATED getSortedAlarmList original alarmList:$alarmList")
        // 根据 Timee 中的 hour, minute, second 进行排序
        val sortedList = alarmList.sortedWith(compareBy({ it.alarmTime?.hour }, { it.alarmTime?.minute }, { it.alarmTime?.second }))
        Logger.i(TAG, "ALARM_RELATED getSortedAlarmList sorted alarmList:$sortedList")
        alarmListSorted = sortedList.toMutableList()
        return sortedList.toMutableList()
    }

    fun initAddAlarmInfo(): AlarmInfo.Alarm {
        val alarm = AlarmInfo.Alarm()
        alarm.repeatType = AlarmInfo.RepeatType.Weekday
        val alarmTime = AlarmInfo.Timee(hour = 8, minute = 0, second = 0)
        alarm.isLightActive = false
        alarm.alarmTime = alarmTime
        val wakeupSound = AlarmInfo.WakeupSound(AlarmInfo.SoundType.RingTone, AlarmInfo.RingTone.Daybreak.code)
        alarm.wakeupSound = wakeupSound
        alarm.volume = 50
        return alarm
    }

    fun updateCurrentAlarm(alarm: AlarmInfo.Alarm?) {
        _currentAlarmLiveData.postValue(alarm)
    }

    fun resetCurrentAlarm() {
        _currentAlarmLiveData.postValue(null)
    }

    fun getRingToneByIndex(index: Int): AlarmInfo.RingTone? {
        return AlarmInfo.RingTone.values().find { it.code == index }
    }

    fun getAlarmInfoList() {
        if (!mainDev.isOnline) {
            Logger.d(TAG, "ALARM_RELATED createAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED getAlarmInfoList")

            mainDev.reqAlarmInfo(protocol = mainDev.getBLEProtocol(), requestType = AlarmInfo.RequestType.AlarmList)
            val requestResult = kotlin.runCatching {
                withTimeout(10 * 1000) {
                    mainDev.syncGetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), requestType = AlarmInfo.RequestType.AlarmList)
                }
            }
            Logger.d(TAG, "ALARM_RELATED getAlarmInfoList requestResult:${requestResult.getOrNull()}")

            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            mainDev.alarmInfo?.let {
                _alarmListLiveData.postValue(it.alarmList)
            }
        }
    }

    fun createAlarm(alarm: AlarmInfo.Alarm) {
        if (!mainDev.isOnline) {
            Logger.d(TAG, "ALARM_RELATED createAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED createAlarm alarmInfo: $alarm")
            val requestResult = kotlin.runCatching {
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = alarm, cmd = AlarmInfo.Command.FunctionalityCreate) }
            }
            Logger.i(TAG, "ALARM_RELATED createAlarm requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _createAlarmLiveData.postValue(it)
            }
        }

    }

    fun enableAlarm(alarm: AlarmInfo.Alarm) {
        if (!mainDev.isOnline) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED enableAlarm alarmInfo: $alarm")
            val requestResult = kotlin.runCatching {
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = alarm, cmd = AlarmInfo.Command.IsActive) }
            }
            Logger.i(TAG, "ALARM_RELATED enableAlarm requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _enableAlarmLiveData.postValue(it)
            }
        }

    }

    fun updateAlarm(alarm: AlarmInfo.Alarm) {
        if (!mainDev.isOnline) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED updateAlarm alarmInfo: $alarm")
            val requestResult = kotlin.runCatching {
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = alarm, cmd = AlarmInfo.Command.FunctionalityUpdate) }
            }
            Logger.i(TAG, "ALARM_RELATED updateAlarm requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _updateAlarmLiveData.postValue(it)
            }
        }

    }

    fun removeAlarm(alarm: AlarmInfo.Alarm) {
        if (!mainDev.isOnline) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        if (alarm.alarmID == null) {
            Logger.d(TAG, "ALARM_RELATED removeAlarm:alarm id is null")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED removeAlarm alarm: $alarm")
            val requestResult = kotlin.runCatching {
                val info = AlarmInfo.Alarm().apply { alarmID = alarm.alarmID!! }
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = info, cmd = AlarmInfo.Command.FunctionalityRemove) }
            }
            Logger.i(TAG, "ALARM_RELATED removeAlarm requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                if (it) {
                    notifyAlarmList = CopyOnWriteArrayList(notifyAlarmList.filter { itAlarm -> itAlarm.alarmID != alarm.alarmID })
                    mainDev.alarmInfo?.alarmList = mainDev.alarmInfo?.alarmList?.filter { itAlarm -> itAlarm.alarmID != alarm.alarmID }?.toMutableList()
                    _alarmListLiveData.postValue(_alarmListLiveData.value?.filter { itAlarm ->
                        itAlarm.alarmID != alarm.alarmID
                    } as MutableList<AlarmInfo.Alarm>?)
                }
                _removeAlarmLiveData.postValue(it)
            }
        }

    }

    fun playDemoWithSound(sound: AlarmInfo.WakeupSound, volume: Int?) {
        if (!mainDev.ableSendCmd) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED playDemoWithSound sound: $sound")
            val requestResult = kotlin.runCatching {
                val alarm = AlarmInfo.Alarm().apply {
                    this.wakeupSound = sound
                    this.volume = volume
                }
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = alarm, cmd = AlarmInfo.Command.FunctionalityPlayDemo) }
            }
            Logger.i(TAG, "ALARM_RELATED playDemoWithSound requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _wakeupSoundLiveData.postValue(it)
            }
        }
    }

    fun stopPlayDemoWithSound(sound: AlarmInfo.WakeupSound, volume: Int?) {
        if (!mainDev.ableSendCmd) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED stopPlayDemoWithSound sound: $sound")
            val requestResult = kotlin.runCatching {
                val alarm = AlarmInfo.Alarm().apply {
                    this.wakeupSound = sound
                    this.volume = volume
                }
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = alarm, cmd = AlarmInfo.Command.FunctionalityStopPlayDemo) }
            }
            Logger.i(TAG, "ALARM_RELATED stopPlayDemoWithSound requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _stopPlayWakeupSoundLiveData.postValue(it)
            }
        }
    }

    fun previewVolume(sound: AlarmInfo.WakeupSound?, volume: Int) {
        if (!mainDev.ableSendCmd) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED previewVolume sound: $volume")
            val requestResult = kotlin.runCatching {
                val alarm = AlarmInfo.Alarm().apply {
                    this.wakeupSound = sound
                    this.volume = volume
                }
                withTimeout(10 * 1000) { mainDev.syncSetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), alarm = alarm, cmd = AlarmInfo.Command.FunctionalityPreviewVolume) }
            }
            Logger.i(TAG, "ALARM_RELATED previewVolume requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _previewVolumeLiveData.postValue(it)
            }
        }
    }

    fun getSnoozeStatus() {
        if (!mainDev.ableSendCmd) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED getSnoozeStatus")
            val requestResult = kotlin.runCatching {
                withTimeout(10 * 1000) { mainDev.syncGetAlarmInfo(logTag = TAG, protocol = mainDev.getBLEProtocol(), requestType = AlarmInfo.RequestType.AlarmSetting) }
            }
            Logger.i(TAG, "ALARM_RELATED getSnoozeStatus requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            mainDev.alarmInfo?.setting?.snoozeStatus?.let {
                _snoozeStatusLiveData.postValue(it)
            }
        }
    }

    fun setSnoozeStatus(status: Boolean) {
        if (!mainDev.ableSendCmd) {
            Logger.d(TAG, "enableAlarm updateAlarm:ble is not connected:")
            return
        }
        viewModelScope.launch(Dispatchers.Main) {
            Logger.d(TAG, "ALARM_RELATED setSnoozeStatus status: $status")
            val requestResult = kotlin.runCatching {
                withTimeout(10 * 1000) {
                    val setting = AlarmInfo.Setting().apply { this.snoozeStatus = status }
                    withTimeout(10 * 1000) {
                        mainDev.syncSetAlarmInfo(
                            logTag = TAG,
                            protocol = mainDev.getBLEProtocol(),
                            setting = setting,
                            cmd = AlarmInfo.Command.FunctionalityUpdateSetting
                        )
                    }
                }
            }
            Logger.i(TAG, "ALARM_RELATED setSnoozeStatus requestResult:${requestResult.getOrNull()}")
            if (requestResult.isFailure || requestResult.getOrNull() == null) {
                return@launch
            }
            requestResult.getOrNull()?.let {
                _snoozeStatusLiveData.postValue(status)
            }
        }
    }

    /**
     * 根据指定的时间 T 和时间间隔 S 来计算前推的时间值
     * 注意规避 0:0 的问题，使用格式 00:00
     * @param timeT
     * @param intervalS
     * @return
     */
    fun calculatePreviousTime(timeT: String, intervalS: Int): String {
        // 将输入的时间字符串解析为 LocalTime 对象
        val formatter = DateTimeFormatter.ofPattern("HH:mm")
        val time = LocalTime.parse(timeT, formatter)

        // 计算前推的时间
        val previousTime = time.minusMinutes(intervalS.toLong())

        // 将结果格式化为字符串并返回
        return previousTime.format(formatter)
    }

    fun calculateTimeDuration(time1: String, time2: String): Duration {
        //注意规避 0:0的问题，使用格式 00:00
        val formatter = DateTimeFormatter.ofPattern("HH:mm")
        val t1 = LocalTime.parse(time1, formatter)
        val t2 = LocalTime.parse(time2, formatter)

        // 计算时间间隔
        val duration = if (t2 >= t1) {
            Duration.between(t1, t2)
        } else {
            // 如果 t2 小于 t1，说明时间跨过了整点，需要加上一天的时间
            Duration.between(t1, LocalTime.MAX).plusMinutes(1).plus(Duration.between(LocalTime.MIN, t2))
        }

        return duration

    }

    fun calculateTimeDifference(startTime: LocalTime, endTime: LocalTime): Duration {
        // 如果结束时间在开始时间之前，说明跨过了午夜
        return if (endTime < startTime) {
            // 计算从开始时间到午夜的时间差
            val midnight = LocalTime.MAX
            val durationToMidnight = Duration.between(startTime, midnight)

            // 计算从午夜到结束时间的时间差
            val durationFromMidnight = Duration.between(LocalTime.MIN, endTime)

            // 总时间差是两部分时间差的和
            durationToMidnight + durationFromMidnight
        } else {
            // 如果没有跨过午夜，直接计算时间差
            Duration.between(startTime, endTime)
        }
    }


    companion object {
        val TAG: String = BTHorizonAlarmsViewModel::class.java.simpleName
        const val LIGHT_OFF = "light_off"
        const val LIGHT_ON_NO_DURATION = "light_on_no_duration"
        const val LIGHT_ON_WITH_DURATION = "light_on_with_duration"
        const val CUSTOM_INITIAL_VALUE = "0000000"

        fun getLightTimeGap(alarm: AlarmInfo.Alarm): Int {
            alarm.alarmTime?.let { itAlarmTime ->
                alarm.lightStartAt?.let { itLightStartAt ->
                    try {
                        val alarmHour = itAlarmTime.hour
                        val alarmMinute = itAlarmTime.minute
                        val lightStarHour = itLightStartAt.hour
                        val lightStarMinute = itLightStartAt.minute
                        if (alarmHour > 60 || alarmMinute > 60 || lightStarHour > 60 || lightStarMinute > 60) return@let
                        val time1 = LocalTime.of(lightStarHour, lightStarMinute)
                        val time2 = LocalTime.of(alarmHour, alarmMinute)
                        // 如果time2在time1之前，说明跨过了午夜
                        if (time2 < time1) {
                            // 计算从time1到午夜的时间差
                            val midnight = LocalTime.MAX
                            val durationToMidnight = Duration.between(time1, midnight)
                            // 计算从午夜到time2的时间差
                            val durationFromMidnight = Duration.between(LocalTime.MIN, time2)
                            // 总时间差是两部分时间差的和
                            val duration = durationToMidnight + durationFromMidnight
                            return duration.toMinutes().toInt()
                        } else {
                            // 如果没有跨过午夜，直接计算时间差
                            val duration = Duration.between(time1, time2)
                            return duration.toMinutes().toInt()
                        }
                    } catch (e: Exception) {
                        Logger.d(TAG, "ALARM_RELATED GetLightTimeGap Exception: ${e.message}")
                        return 0
                    }
                }
            }
            return 0
        }

        fun getLightTimeGapCompact(alarm: AlarmInfo.Alarm): Int {
            val alarmTime = alarm.alarmTime ?: return 0
            val lightStartAt = alarm.lightStartAt ?: return 0

            return try {
                val alarmHour = alarmTime.hour
                val alarmMinute = alarmTime.minute
                val lightStartHour = lightStartAt.hour
                val lightStartMinute = lightStartAt.minute

                // 验证时间值的有效性
                if (alarmHour > 23 || alarmMinute > 59 || lightStartHour > 23 || lightStartMinute > 59) {
                    return 0
                }

                val alarmLocalTime = LocalTime.of(alarmHour, alarmMinute)
                val lightStartLocalTime = LocalTime.of(lightStartHour, lightStartMinute)

                // 计算时间差（自动处理跨午夜的情况）
                val duration = if (alarmLocalTime < lightStartLocalTime) {
                    lightStartLocalTime.until(LocalTime.MAX, ChronoUnit.MINUTES) + 1 + // 到午夜的时间差
                            LocalTime.MIN.until(alarmLocalTime, ChronoUnit.MINUTES)           // 午夜到 alarmLocalTime 的时间差
                } else {
                    lightStartLocalTime.until(alarmLocalTime, ChronoUnit.MINUTES)     // 直接计算时间差
                }

                duration.toInt()

            } catch (e: DateTimeException) {
                Logger.d(TAG, "ALARM_RELATED GetLightTimeGap Exception: ${e.message}")
                0
            }
        }

        fun getAlarmType(alarm: AlarmInfo.Alarm): String {
            if (alarm.isLightActive == true) {
                return if (alarm.lightStartAt != null) {
                    LIGHT_ON_WITH_DURATION
                } else {
                    LIGHT_ON_NO_DURATION
                }
            }
            return LIGHT_OFF
        }
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_START -> onStart()
            Lifecycle.Event.ON_STOP -> onStop()
            else -> {}
        }
    }
}