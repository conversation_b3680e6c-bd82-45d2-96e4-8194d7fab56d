package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.Html
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceSettingAction
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.DevSleepTimerItem
import com.wifiaudio.model.DlnaPlayerStatus
import com.wifiaudio.model.albuminfo.AlbumMetadataUpdater
import com.wifiaudio.model.playviewmore.PlayMoreSleepTimerItem
import com.wifiaudio.model.rightfrag_obervable.MessageDataItem
import com.wifiaudio.utils.GsonParseUtil
import com.wifiaudio.utils.MixTextImg
import com.wifiaudio.utils.devmanager.LPDeviceManagerUtil
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.AppLogTagUtil
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FragDevSleepTimerUI : FragDevSettingBaseUI() {

    private var tv_label: TextView? = null
    private var tv_title: TextView? = null
    private var btnClose: ImageView? = null
    private var btnContinue: Button? = null
    private var recyclerView: RecyclerView? = null
    private var mAdapter: DevSleepTimerAdapter? = null
    private var currList: ArrayList<PlayMoreSleepTimerItem> = ArrayList()
    var lastIndex = 0//选择的索引

    var bSendSleepTime: Boolean = true //是否发送时间
    var blurBitmap: Bitmap? = null

    var dataInfo: DataFragInfo? = null

    var defaultSelectTimer: String = "" //默认选中的时间，配合 bSendSleepTime

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.frag_device_sleep_timer, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun onResume() {
        super.onResume()

        EventBus.getDefault().register(this)
    }

    override fun onPause() {
        super.onPause()
        EventBus.getDefault().unregister(this)
    }

    override fun initView() {
        super.initView()

        tv_label = cview?.findViewById(R.id.tv_label)
        recyclerView = cview?.findViewById(R.id.recycle_view)
        tv_title = cview.findViewById(R.id.ble_title)
        btnClose = cview.findViewById(R.id.iv_close)
        btnContinue = cview.findViewById(R.id.btn_connect)

        tv_title?.apply {
            text = SkinResourcesUtils.getString("jbl_Auto_Off")
//            setTextColor(WAApplication.mResources.getColor(R.color.fg1))

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)


        }

        tv_label?.apply {
            text = SkinResourcesUtils.getString("jbl_Inactive")
//            setTextColor(GlobalUIConfig.color_info_normal)
//            alpha = 0.85f
            visibility = if(bSendSleepTime)
                View.VISIBLE
            else
                View.GONE
//            setTextColor(WAApplication.mResources.getColor(R.color.fg1))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        }

        initTimerItems()

        mAdapter = activity?.let { DevSleepTimerAdapter(it) }
        mAdapter?.currList = currList
        recyclerView?.apply {
            adapter = mAdapter
        }

        if(bSendSleepTime)
            getSleepTimer()
        else {
            refreshTimerListDefaultSelect(defaultSelectTimer.toInt())
        }
    }

    override fun bindSlots() {
        super.bindSlots()

        btnClose?.setOnClickListener {
            onBackKeyDown()
        }

        mAdapter?.setOnItemClickListener(object : DevSleepTimerAdapter.IOnItemClickListener {
            override fun onItemClicked(position: Int, item: PlayMoreSleepTimerItem?) {

                if (lastIndex in 0 until mAdapter!!.itemCount!!) {
                    val lastItem = mAdapter!!.currList[lastIndex]
                    lastItem.isSelected = false
                    mAdapter!!.notifyItemChanged(lastIndex, R.id.ic_more)
                }

                lastIndex = position
                val item = mAdapter!!.currList[lastIndex]
                item.isSelected = true
                mAdapter!!.notifyItemChanged(position, R.id.ic_more)

                if (!bSendSleepTime) {//从不同的页面进入，需要判断是否需要发送timer
                    AlbumMetadataUpdater.me().notifyAlarmContextChanged(item)
                    FragTabUtils.popBack(activity)
                    return
                } else {
                    item?.seconds?.let { setTimer(it) }
                }
            }
        })
    }



    private fun setTimer(timer: Int) {

        DeviceSettingAction.makeDeviceShutdownAt(
            dataInfo?.deviceItem,
            timer,
            object : DeviceSettingAction.IDeviceShutdownCallback {
                override fun onSuccess(content: String?) {
                    if (dataInfo?.deviceItem?.sleepTimerItem == null)
                        dataInfo?.deviceItem?.sleepTimerItem =
                            dataInfo?.deviceItem?.uuid?.let { it2 ->
                                DevSleepTimerItem(
                                    it2
                                )
                            }

                    dataInfo?.deviceItem?.sleepTimerItem?.remain_time = timer

                    LogsUtil.i(
                        AppLogTagUtil.DEVICE_TAG,
                        "Set Sleep Timer Success: $content"
                    )

                    back()
                }

                override fun onFailure(e: Throwable?) {
                    LogsUtil.i(
                        AppLogTagUtil.DEVICE_TAG,
                        "Set Sleep Timer Failed: ${e?.localizedMessage}"
                    )

                    back()
                }
            })
    }

    override fun initUtils() {
        super.initUtils()

        updateTheme()
        updateBlurBG()
        updateContainerBG(cview)
    }

    override fun updateContainerBG(cview: View?) {

        var container = cview?.findViewById<RelativeLayout>(R.id.container)

        val layout_bg = container?.background as GradientDrawable
        val startColor = WAApplication.mResources.getColor(R.color.bg_card)
        val centerColor = WAApplication.mResources.getColor(R.color.bg_card)
        val endColor = WAApplication.mResources.getColor(R.color.bg_card)
        layout_bg.colors = intArrayOf(
            startColor,
            centerColor,
            endColor
        )


        var radius = WAApplication.mResources.getDimension(R.dimen.width_15).toFloat()
        layout_bg?.cornerRadii = floatArrayOf(
            radius,
            radius,
            radius,
            radius,
            radius,
            radius,
            radius,
            radius
        )

        var containerParams = container?.layoutParams as ViewGroup.MarginLayoutParams
        containerParams?.apply {
            var w = WAApplication.mResources.getDimension(R.dimen.width_10).toInt()
            leftMargin = w
            rightMargin = w
            0
        }
        container?.layoutParams = containerParams


        container?.background = layout_bg
    }

    /**
     * 更新模糊化背景
     */
    private fun updateBlurBG() {

        cview.background = ColorDrawable(Color.parseColor("#7f000000"))
    }

    private fun refreshTimerListDefaultSelect(defaultTimer: Int) {

        if (currList == null || currList.size <= 0)
            return

        if (lastIndex in 0 until currList.size) {
            var item = currList[lastIndex]
            item.isSelected = false
            mAdapter?.notifyItemChanged(lastIndex, R.id.ic_more)
        }

        for (i in 0 until currList.size) {

            var item = currList[i]
            if (defaultTimer == item.seconds) {
                item.isSelected = true
                lastIndex = i
                mAdapter?.notifyItemChanged(i, R.id.ic_more)
                break
            }
        }
    }

    private fun initTimerItems() {

        val _0item = PlayMoreSleepTimerItem()
        _0item.title = SkinResourcesUtils.getString("jbl_Never")
        _0item.seconds = 0 * 60
        _0item.type = 1
        _0item.isSelected = true
        currList.add(_0item)
        lastIndex = 0

        val _5item = PlayMoreSleepTimerItem()
        _5item.title = "5 " + SkinResourcesUtils.getString("jbl_Minutes")
        _5item.seconds = 5 * 60
        _5item.type = 1
        //currList.add(_5item)

        val _15item = PlayMoreSleepTimerItem()
        _15item.title = "15 " + SkinResourcesUtils.getString("jbl_Minutes")
        _15item.seconds = 15 * 60
        _15item.type = 1
        currList.add(_15item)

        val _30item = PlayMoreSleepTimerItem()
        _30item.title = "30 " + SkinResourcesUtils.getString("jbl_Minutes")
        _30item.seconds = 30 * 60
        _30item.type = 1
        currList.add(_30item)

        val _45item = PlayMoreSleepTimerItem()
        _45item.title = "45 " + SkinResourcesUtils.getString("jbl_Minutes")
        _45item.seconds = 45 * 60
        _45item.type = 1
        currList.add(_45item)

        val _1hitem = PlayMoreSleepTimerItem()
        _1hitem.title = "60 " + SkinResourcesUtils.getString("jbl_Minutes")
        _1hitem.seconds = 60 * 60
        _1hitem.type = 1
        currList.add(_1hitem)

        var item = PlayMoreSleepTimerItem()
        item.title = "75 " + SkinResourcesUtils.getString("jbl_Minutes")
        item.seconds = 75 * 60
        item.type = 1
        //currList.add(item)

        item = PlayMoreSleepTimerItem()
        item.title = "90 " + SkinResourcesUtils.getString("jbl_Minutes")
        item.seconds = 90 * 60
        item.type = 1
        //currList.add(item)

        item = PlayMoreSleepTimerItem()
        item.title = "105 " + SkinResourcesUtils.getString("jbl_Minutes")
        item.seconds = 105 * 60
        item.type = 1
        //currList.add(item)

        item = PlayMoreSleepTimerItem()
        item.title = "120 " + SkinResourcesUtils.getString("jbl_Minutes")
        item.seconds = 120 * 60
        item.type = 1
        //currList.add(item)
    }

    private fun updateTheme() {

//        tv_label?.setTextColor(GlobalUIConfig.color_gray)

        btnContinue?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                WAApplication.mResources.getDimension(R.dimen.font_14)
            )

            text = if (bSendSleepTime)
                SkinResourcesUtils.getString("jbl_START")
            else
                SkinResourcesUtils.getString("jbl_SAVE")

            val tintDrawable = SkinResourcesUtils.getTintListDrawable(
                "btn_background_bg_s1", GlobalUIConfig.color_btn_normal, GlobalUIConfig.color_btn_press
            )
            background = tintDrawable
//            setTextColor(GlobalUIConfig.color_solid_btn_font_color)
        }
    }

    private fun back() {
        uihd.postDelayed({
            onBackKeyDown()
        }, 200)
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        if(bSendSleepTime)
            getSleepTimer()

        FragTabUtils.popBack(activity)
    }

    private fun getSleepTimer() {

        DeviceSettingAction.makeDeviceShutdownTimeSearch(
            dataInfo?.deviceItem,
            object : DeviceSettingAction.IDeviceShutdownCallback {
                override fun onSuccess(content: String?) {
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "Get Sleep Timer Success: $content")

                    var jblTimerItem =
                        content?.let {
                            GsonParseUtil.instance.fromJson(
                                it,
                                DevSleepTimerItem::class.java
                            )
                        }

                    jblTimerItem?.sleep_timer?.let { refreshTimerListDefaultSelect(it) }
                }

                override fun onFailure(e: Throwable?) {
                    LogsUtil.i(
                        AppLogTagUtil.DEVICE_TAG,
                        "Get Sleep Timer Failed: ${e?.localizedMessage}"
                    )
                }
            })
    }

    private fun updateSleepTimer(timer: Int) {

        tv_label?.apply {
            val fontColor1 = MixTextImg.RGBA2TxtRGB(GlobalUIConfig.color_app_theme)
            if (timer <= 0) {
                text = SkinResourcesUtils.getString("jbl_Inactive")
                setTextColor(WAApplication.mResources.getColor(R.color.fg_primary))
                alpha = 0.85f
            } else {
                setTextColor(WAApplication.mResources.getColor(R.color.fg_primary))
                alpha = 1.0f
                text = Html.fromHtml(
                    String.format(SkinResourcesUtils.getString("jbl_Sleep_in___"), "<font color=$fontColor1> " + DlnaPlayerStatus.toTimeString(
                        timer.toLong()
                    ) + "</font>")
                )
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDevSleepTimerItemEvent(item: MessageDataItem) {

        if (LPDeviceManagerUtil.getInstance()
                .isSameDevice(item.strDevUUID, dataInfo?.deviceItem?.uuid)
        ) {
            var item = item.obj as JBLTimer
            updateSleepTimer(item.sleep_timer.toInt())
        }
    }
}