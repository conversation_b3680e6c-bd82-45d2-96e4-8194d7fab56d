package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Editable
import android.text.Selection
import android.text.TextUtils
import android.text.TextWatcher
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import com.harman.WirelessConnector
import com.harman.bar.app.R
import com.harman.bean.AuthDevice
import com.harman.hkone.deviceoffline.OfflineDeviceHandler
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.rightfrag_obervable.MessageDataItem
import com.wifiaudio.utils.*
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus


class FragDevRename : FragDevSettingBaseUI() {

    var authDevice: AuthDevice? = null
    var vback: Button? = null
    var vTitle: TextView? = null
    var btn_save: Button? = null
    var cannot_rename_group: Group? = null
    var et_rename: EditText? = null
    var isFromGroup: Boolean? = false

    var dataInfo: DataFragInfo? = null
    private var oldDeviceName: String? = ""
    private val EDIT_TEXT_MAX_LENGTH = 28
    private val ET_TEXT_CHANGE = 1

    private var preEditText: String = ""

    private var mHandler = object : Handler(Looper.myLooper()!!) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == ET_TEXT_CHANGE) {
                updateBtnStatus()
            }
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.frag_device_rename, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()
        return cview
    }

    override fun onResume() {
        super.onResume()
        ScreenUtil.controlKeyboardLayout2(cview, btn_save)
    }

    override fun initView() {
        super.initView()
        btn_save = cview?.findViewById(R.id.btn_save)
        et_rename = cview?.findViewById(R.id.et_rename)
        vback = cview.findViewById(R.id.vback)
        vTitle = cview.findViewById(R.id.vtitle)
        cannot_rename_group = cview.findViewById(R.id.cannot_rename_group)

        vTitle?.apply {
            isFromGroup?.let { isFrom ->
                text = if (isFrom) {
                    SkinResourcesUtils.getString("newStructure_Rename_This_Group")
                } else {
                    SkinResourcesUtils.getString("jbl_Rename_My_Product")
                }
            }
            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_16))
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        vback?.setBackgroundResource(R.drawable.select_icon_menu_back)

        oldDeviceName = dataInfo?.deviceItem?.Name ?: dataInfo?.deviceItem?.ssidName
        oldDeviceName = authDevice?.getDisplayName(false, false)

        preEditText = oldDeviceName.toString()

        et_rename?.apply {
            setText(oldDeviceName)
            hint = oldDeviceName
            isEnabled = false
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_disabled))
        }

        getDeviceNameStatus()

        updateBtnStatus()
    }

    override fun bindSlots() {
        super.bindSlots()

        vback?.setOnClickListener {
            onBackKeyDown()
        }

        btn_save?.setOnClickListener {
            hideInputMethod(et_rename)
            setDeviceName()
        }

        val cursorLine = ContextCompat.getDrawable(
            requireContext().applicationContext, R.drawable.cursor_line
        )

        et_rename?.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                activity?.let {
                    showInputMethod(v)
                    btn_save?.apply {
                        fadeIn(this)
                    }
                }
            } else {
                hideInputMethod(et_rename)
            }
        }


        et_rename?.addTextChangedListener(object : TextWatcher {

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val inputText = s.toString()
                val inputByteLen = inputText.toByteArray().size

                if( inputByteLen > EDIT_TEXT_MAX_LENGTH ){
                    et_rename?.setText(preEditText)
                    et_rename?.text?.let { Selection.setSelection( it, it.length ) }
                } else{
                    preEditText = inputText
                }

                mHandler.sendEmptyMessage(ET_TEXT_CHANGE)

/*              var textMaxLength = 0
                var editable = et_rename?.text

                var text: String = editable.toString()
                if (text.length > EDIT_TEXT_MAX_LENGTH) {
                    text = text.substring(0, EDIT_TEXT_MAX_LENGTH - 1)
                }

                //最初字段的长度大小
                var selEndIndex = Selection.getSelectionEnd(editable)
                //取出每个字符进行判断，字母数字标点符号为+1，汉字则+2
                for (i in text.indices) {
                    var charAt = text[i]
                    if (charAt >= 32.toChar() && charAt <= 122.toChar()) {
                        textMaxLength++
                    } else {
                        textMaxLength += 2
                    }

                    if (textMaxLength > EDIT_TEXT_MAX_LENGTH) {
                        var newStr = text.substring(0, i)
                        et_rename!!.setText(newStr)

                        editable = et_rename?.text
                        var newLen = editable?.length
                        if (selEndIndex > newLen!!) {
                            selEndIndex = editable?.length!!
                        }

                        Selection.setSelection(editable, selEndIndex)
                        break
                    }
                }*/
            }

            override fun afterTextChanged(s: Editable?) {}
        })
    }

    override fun initUtils() {
        super.initUtils()
        updateTheme()
    }

   /* fun isSpecialText(text: String): Boolean {
        return if (TextUtils.isEmpty(text)) {
            false
        } else {
            text.contains(":") || text.contains(";") || text.contains("/") || text.contains("\\") || text.contains("`")
        }
    }*/


    private fun setDeviceName() {

        var newName = et_rename?.text.toString()

        /* val ret = isSpecialText(newName)

        if (ret) {
            val strErrorInfor = SkinResourcesUtils.getString("jbl_Only_numbers__letters_and_underscore_are_allowed")
            WAApplication.me.toastMessage(activity, true, strErrorInfor)
            return
        }*/

        if (StringUtils.isEmpty(newName.trim())) {
            WAApplication.me.toastMessage(
                activity, true,
                SkinResourcesUtils.getString("jbl_The_name_of_device_is_empty_")
            )
            return
        }

        if (TextUtils.equals(newName, oldDeviceName)) {
            onBackKeyDown()
            return
        }

        val byteName = newName.toByteArray()
        if (byteName.size > 32) {
            val strErrorInfor = SkinResourcesUtils.getString("jbl_The_length_of_name_is_too_long")
            WAApplication.me.toastMessage(
                activity, true,
                strErrorInfor
            )
            return
        }

        WAApplication.me.showProgDlg(
            activity,
            (15 * 1000).toLong(),
            SkinResourcesUtils.getString("setting_Please_wait")
        )
        WirelessConnector.rename(authDevice, newName, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                WAApplication.me.showProgDlg(activity, false, null)

                var errorCodeItem =
                    content?.let {
                        GsonParseUtil.instance.fromJson(
                            it,
                            JBLErrorCode::class.java
                        )
                    }
                if (TextUtils.equals(errorCodeItem?.error_code, "0")) {
//                    authDevice?.friendlyName = newName
                    oldDeviceName = newName
                    var group = OfflineDeviceHandler.get().getGroup(authDevice?.deviceCrc)
                    group?.group_info?.group?.name = newName
//                    OfflineDeviceHandler.get().updateGroup(authDevice?.deviceCrc, group)

                    var authDeviceInLocal = OfflineDeviceHandler.get().getDevice(authDevice?.deviceCrc)
                    if (authDeviceInLocal != null) {
                        if (authDeviceInLocal.isBLEDevice) {
                            authDeviceInLocal.bleDevice.name = newName
                        } else {
                            authDeviceInLocal?.wifiDevice?.Name = newName
                        }
                    }

                    var msg = MessageDataItem()
                    msg.strDevUUID = dataInfo?.deviceItem?.uuid
                    var nameInfo = JBLDeviceNameInfo()
                    nameInfo.device_name = oldDeviceName
                    nameInfo.device_crc = authDevice?.deviceCrc
                    msg.obj = nameInfo
                    EventBus.getDefault().post(msg)
                }

                onBackKeyDown()
            }

            override fun onFailure(e: Throwable?) {

                WAApplication.me.showProgDlg(activity, false, null)

                WAApplication.me.toastMessage(
                    activity, true,
                    SkinResourcesUtils.getString("jbl_Fail")
                )
            }
        })


    }

    private fun updateTheme() {
        btn_save?.apply {
            text = SkinResourcesUtils.getString("jbl_SAVE")
            val tintDrawable = SkinResourcesUtils.getTintListDrawable(
                "btn_background_bg_s1",
                ContextCompat.getColor(requireContext(), R.color.bg_inverse), GlobalUIConfig.color_btn_press
            )
            background = tintDrawable
            setTextColor(ContextCompat.getColor(requireContext(), R.color.fg_inverse))
        }
    }

    fun hideInputMethod(view: View?) {
        activity?.let {
            val imm = it.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            if (imm.isActive) {
                imm.hideSoftInputFromWindow(view?.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
            }
        }
    }

    fun showInputMethod(view: View?) {
        activity?.let {
            val imm = it.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
        }
    }

    private fun updateBtnStatus() {
        val newName = et_rename?.text.toString()
        btn_save?.apply {
            setTextColor(ContextCompat.getColor(requireContext(), R.color.fg_inverse))
            if (newName.isEmpty()) {
                isEnabled = false
                alpha = 0.5f
            } else {
                isEnabled = true
                alpha = 1.0f
            }
        }
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        hideInputMethod(et_rename)
        FragTabUtils.popBack(activity)
    }

    private fun fadeIn(view: View) {
        fadeIn(view, 0f, 1f, 500)
        view.isEnabled = true
    }

    private fun fadeIn(view: View, startAlpha: Float, endAlpha: Float, duration: Long) {
        if (view.visibility == View.VISIBLE) return
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.duration = duration
        view.startAnimation(animation)
        view.visibility = View.VISIBLE
    }

    private fun fadeOut(view: View) {
        if (view.visibility != View.VISIBLE) return
        view.isEnabled = false
        val animation: Animation = AlphaAnimation(1f, 0f)
        animation.duration = 500
        view.startAnimation(animation)
        view.visibility = View.INVISIBLE
    }



    private var devNameInfo: JBLDeviceNameInfo? = JBLDeviceNameInfo()
    private fun getDeviceNameStatus() {
        WirelessConnector.makeDeviceGetDeviceName(authDevice, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                LogsUtil.i(LogTags.Device, "FragDevRename: getDeviceName: Success: $content")
                devNameInfo = content?.let {
                    GsonParseUtil.instance.fromJson(it, JBLDeviceNameInfo::class.java)
                }
                updateRenameStatus()
            }

            override fun onFailure(e: Throwable?) {
                LogsUtil.i(LogTags.Device, "FragDevRename: getDeviceName: Failed:${e?.localizedMessage}")
            }
        })
    }


    private fun updateRenameStatus() {
        devNameInfo?.name_editable?.let { it ->
            et_rename?.isEnabled = it
            et_rename?.setTextColor(ContextCompat.getColor(WAApplication.me, if (it) R.color.fg_primary else R.color.fg_disabled))
            cannot_rename_group?.visibility = if (it) View.GONE else View.VISIBLE
        }
    }

}