package com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Switch
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityHorizonAlarmsBinding
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.discover.bean.Device
import com.harman.log.Logger
import com.harman.parseAsPartyBoxDevice
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.adapter.AlarmsDetailsAdapter
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.viewmodel.BTHorizonAlarmsViewModel
import config.LogTags
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class BTHorizonAlarmsActivity : AppCompatActivity(), View.OnClickListener {

    private var mAlarmsDetailsAdapter: AlarmsDetailsAdapter? = null
    private val binding by lazy { ActivityHorizonAlarmsBinding.inflate(layoutInflater) }

    private val alarmsViewModel by viewModels<BTHorizonAlarmsViewModel>()
    private val mainDev by lazy {
        parseAsPartyBoxDevice()
    }
    private var removeAlarmPosition = -1

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        if (null == mainDev) {

            finish()
            return
        }
        alarmsViewModel.mainDev = mainDev!!
        lifecycle.addObserver(alarmsViewModel)
        setContentView(binding.root)
        initView()
    }

    override fun onResume() {
        super.onResume()
        initData()
    }

    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) super.attachBaseContext(newBase) else {
            val baseContext = LocaleLanConfigUtil.changeAppLan(newBase)
            WAApplication.me.mAppLanguageContext = baseContext
            super.attachBaseContext(baseContext)
        }
    }

    private fun initView() {
        binding.appbar.ivLeading.setOnClickListener { onBackPressed() }
        binding.appbar.tvTitle.text = SkinResourcesUtils.getString("harmanbar_NewPlayView_Alarm")
        binding.tvNoAlarms.text = SkinResourcesUtils.getString("no_alarm")
        updateAlarmListView()

        binding.ivAddAlarm.setOnClickListener(this)
        binding.appbar.ivAction.setImageResource(R.drawable.icon_settings)
        binding.appbar.ivAction.setOnClickListener {
            BTHorizonAlarmsSettingActivity.portal(this@BTHorizonAlarmsActivity, mainDev!!)
        }
    }

    private fun updateAlarmListView() {
        val linearLayoutManager = LinearLayoutManager(this@BTHorizonAlarmsActivity, RecyclerView.VERTICAL, false)
        binding.alarmsRecyclerView.visibility = View.VISIBLE
        binding.nestedScrollView.visibility = View.VISIBLE
        binding.tvNoAlarms.visibility = View.GONE
        binding.alarmsRecyclerView.layoutManager = linearLayoutManager
        mAlarmsDetailsAdapter = AlarmsDetailsAdapter(this@BTHorizonAlarmsActivity, alarmsViewModel.alarmListSorted)

        /*val callback: ItemTouchHelper.Callback = SimpleItemTouchHelperCallback(mAlarmsDetailsAdapter!!)
        val touchHelper = ItemTouchHelper(callback)
        touchHelper.attachToRecyclerView(binding.alarmsRecyclerView)*/

        binding.alarmsRecyclerView.adapter = mAlarmsDetailsAdapter

        mAlarmsDetailsAdapter?.setOnSwipeListener(object : AlarmsDetailsAdapter.OnSwipeListener {
            override fun onDelete(view: View, position: Int, alarm: AlarmInfo.Alarm) {
                removeAlarmPosition = position
                mAlarmsDetailsAdapter?.getAlarm(position)?.let { itAlarm ->
//                        LPMSCommonUtils.showWaitDlg(requireActivity(), 15 * 1000)
                    WAApplication.me.showProgDlg(
                        this@BTHorizonAlarmsActivity, true, SkinResourcesUtils.getString("adddevice_Loading____"), 15 * 1000
                    )
                    alarmsViewModel.removeAlarm(itAlarm)
                }
            }

        })

        mAlarmsDetailsAdapter?.setOnItemClickListener(object : AlarmsDetailsAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int, alarm: AlarmInfo.Alarm) {
                when (view.id) {
                    R.id.item_root_layout -> {
                        BTHorizonAddEditAlarmsActivity.portal(
                            this@BTHorizonAlarmsActivity,
                            mainDev!!,
                            BTHorizonAddEditAlarmsActivity.EDIT_ALARM,
                            alarm = alarmsViewModel.alarmListSorted[position]
                        )
                    }

                    R.id.alarm_switch -> {
                        if (view is Switch) {
                            alarm.isActiveAlarm = view.isChecked
                            alarmsViewModel.enableAlarm(alarm)
                        }

                    }
                }
            }
        })

        alarmsViewModel.alarmListLiveData.observe(this) { alarmList ->
            Logger.d(TAG, "ALARM_RELATED alarmListLiveData :$alarmList")
            alarmList?.let {
                alarmsViewModel.alarmListSorted = alarmsViewModel.getSortedAlarmList(alarmList)
                Logger.d(TAG, "ALARM_RELATED alarmListLiveData :$alarmList, mAlarmsDetailsAdapter = $mAlarmsDetailsAdapter")
                mAlarmsDetailsAdapter?.updateDataList(alarmsViewModel.alarmListSorted)
                if (alarmList.isNotEmpty()) {
                    binding.nestedScrollView.visibility = View.VISIBLE
                    binding.alarmsRecyclerView.visibility = View.VISIBLE
                    binding.tvNoAlarms.visibility = View.GONE
                } else {
                    binding.nestedScrollView.visibility = View.GONE
                    binding.alarmsRecyclerView.visibility = View.GONE
                    binding.tvNoAlarms.visibility = View.VISIBLE
                }
            }
        }

        alarmsViewModel.removeAlarmLiveData.observe(this) { isRemoved ->
            Logger.d(TAG, "ALARM_RELATED removeAlarmLiveData :$isRemoved, removeAlarmPosition :$removeAlarmPosition")
//                LPMSCommonUtils.closeProgDlg(requireActivity())
            WAApplication.me.showProgDlg(this@BTHorizonAlarmsActivity, false, null)
            GlobalScope.launch(Dispatchers.IO) {
                delay(500)
                alarmsViewModel.getAlarmInfoList()
            }
        }

        alarmsViewModel.enableAlarmLiveData.observe(this) { isSuccess ->
            Logger.d(TAG, "ALARM_RELATED enableAlarmLiveData :$isSuccess")
            if (isSuccess) {
                GlobalScope.launch(Dispatchers.IO) {
                    delay(500)
                    alarmsViewModel.getAlarmInfoList()
                }
                mAlarmsDetailsAdapter?.notifyDataSetChanged()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initData() {
        alarmsViewModel.getAlarmInfoList()
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(300)
            alarmsViewModel.getSnoozeStatus()
        }
    }

    override fun onClick(v: View?) {
        v?.let { itView ->
            when (itView.id) {

                R.id.iv_add_alarm -> {
                    if (alarmsViewModel.alarmListSorted.size >= 20) {
                        val dialogFragment = AlarmsNumberTipDialogFragment()
                        dialogFragment.show(supportFragmentManager)
                    } else {
                        mainDev?.let {
                            BTHorizonAddEditAlarmsActivity.portal(
                                this@BTHorizonAlarmsActivity, it, BTHorizonAddEditAlarmsActivity.ADD_ALARM, alarm = alarmsViewModel.initAddAlarmInfo()
                            )
                        }
                    }
                }

                else -> {}
            }
        }
    }

    companion object {
        val TAG: String = BTHorizonAlarmsActivity::class.java.simpleName
        private const val REQUEST_CODE_B = 1
        private const val REQUEST_CODE_C = 2
        private const val EXTRA_REQUEST_CODE = "extra_request_code"

        fun portal(
            activity: Activity, device: Device
        ) {
            device.pid?.also {
                val intent: Intent = Intent(activity, BTHorizonAlarmsActivity::class.java)
                intent.putExtra("UUID", device.UUID)
                Logger.d(TAG, "startActivity")
                activity.startActivity(intent)
            }
        }
    }
}