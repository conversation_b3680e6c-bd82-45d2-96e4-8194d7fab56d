package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.animation.ArgbEvaluator
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.harman.CoulsonManager
import com.harman.EventUtils
import com.harman.bar.app.R
import com.harman.bean.CommonEvent
import com.harman.bean.Data
import com.harman.hkone.DeviceImageUtil
import com.jbl.one.configuration.model.StreamService
import com.harman.widget.CollapsingToolBar
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.ApplicationViewModel
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.utils.ScreenUtil
import com.wifiaudio.utils.statusbar.StatusBarUtils
import com.wifiaudio.view.dlg.ChromecastDialogProvider
import com.wifiaudio.view.oobe.SetupGVANormalDialogFragment
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import com.wifiaudio.view.pagesmsccontent.amazon.DataInfo
import com.wifiaudio.view.pagesmsccontent.home.ComposeStatus
import com.wifiaudio.view.pagesmsccontent.home.ComposeStatusListener
import com.wifiaudio.view.pagesmsccontent.home.ComposeStatusViewModel
import com.wifiaudio.view.pagesmsccontent.menu_settings.EnterFromType
import com.wifiaudio.view.pagesmsccontent.menu_settings.SETTING_ITEM_TYPE
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.AlexaStatusItem
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.AlexaVoiceAssistantFragment
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.AmazonAlexaLoginStatusUtil
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.FragAmazonAlexaStatus
import config.AppLogTagUtil
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus


class WifiStreamingFragment : FragDevSettingBaseUI(), View.OnClickListener {

    private var collapsingToolBar: CollapsingToolBar? = null
    private var recyclerView: RecyclerView? = null
    private var tvEnable: TextView? = null
    private var learnMoreLayout: ConstraintLayout? = null
    private var bottomMaskLayout: ConstraintLayout? = null
    private var wifiStreamingAdapter: WifiStreamingAdapter? = null
    private val mStreamServiceDataList: MutableList<StreamServiceInfo> = ArrayList()
    private var containerLayout: MotionLayout? = null
    private var ivBack: ImageView? = null
    private val mHandler = Handler(Looper.getMainLooper())

    var data: Data? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.fragment_wifi_streaming_1, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()
        return cview
    }


    override fun onBackKeyDown() {
        super.onBackKeyDown()
        containerLayout?.setTransitionListener(null)
        StatusBarUtils.setPageStatusBarPaddingTop(cview, true)
        StatusBarUtils.setCustomStatusBarColor(activity, true, GlobalUIConfig.color_navigationbar_bg)
        FragTabUtils.popBack(activity)
    }

    override fun initView() {
        super.initView()
//        collapsingToolBar = cview.findViewById(R.id.collapsing_toolbar)
        tvEnable = cview.findViewById(R.id.tv_enable)
        containerLayout = cview.findViewById(R.id.rl_container)
        learnMoreLayout = cview.findViewById(R.id.learn_more_layout)
        bottomMaskLayout = cview.findViewById(R.id.layout_bottom_mask)
        ivBack = cview.findViewById(R.id.iv_back)

        val padding = ScreenUtil.dip2px(requireContext(), 100F)
        /*collapsingToolBar?.let {
            it.coverImageView?.setPadding(padding, padding, padding, padding)
            it.coverImageView?.setBackgroundResource(R.drawable.blue_print_background)
            it.coverImageView?.setImageResource(R.drawable.icon_learn_more_group)
            it.setNavigationListener(object : CollapsingToolBar.NavigationListener {
                override fun onBack() {
                    onBackKeyDown()
                }

                override fun onNext() {
                }
            })
        }*/
        initRecyclerView()

        tvEnable?.setOnClickListener(this)
        learnMoreLayout?.setOnClickListener(this)
        ivBack?.setOnClickListener(this)
        val window = requireActivity().window

        /*val startSet = ConstraintSet()
        val endSet = ConstraintSet()
        startSet.clone(containerLayout?.getConstraintSet(R.id.constraint_set_start))
        startSet.setVisibilityMode(R.id.layout_bottom_mask, ConstraintSet.VISIBILITY_MODE_IGNORE)
        startSet.applyTo(containerLayout)

        endSet.clone(containerLayout?.getConstraintSet(R.id.constraint_set_end))
        endSet.setVisibilityMode(R.id.layout_bottom_mask, ConstraintSet.VISIBILITY_MODE_IGNORE)
        endSet.applyTo(containerLayout)*/

        containerLayout?.let {
            it.setTransitionListener(object : MotionLayout.TransitionListener {
                override fun onTransitionStarted(motionLayout: MotionLayout?, startId: Int, endId: Int) {}

                override fun onTransitionChange(motionLayout: MotionLayout?, startId: Int, endId: Int, progress: Float) {
                    val startColor = ActivityCompat.getColor(requireActivity(), R.color.color_00B4B2)
                    val endColor = ActivityCompat.getColor(requireActivity(), R.color.bg_surface)
                    window.statusBarColor = ArgbEvaluator().evaluate(progress, startColor, endColor) as Int
                }

                override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
                    motionLayout?.let { layout ->
                        when (it.currentState) {
                            layout.startState -> {
                                window.statusBarColor = ActivityCompat.getColor(requireActivity(), R.color.color_00B4B2)
                            }
                            layout.endState -> {
                                window.statusBarColor = ActivityCompat.getColor(requireActivity(), R.color.bg_surface)
                            }
                        }
                    }
                }

                override fun onTransitionTrigger(motionLayout: MotionLayout?, triggerId: Int, positive: Boolean, progress: Float) {}

            })
        }
    }

    private fun initRecyclerView() {
        recyclerView = cview.findViewById(R.id.recyclerView)
        val streamServiceList = getStreamServiceList()
        streamServiceList?.let {
            for (streamService in streamServiceList) {
                when (streamService) {
                    StreamService.AmazonAlexa, StreamService.AmazonAlexaVA -> {
                        mStreamServiceDataList.add(StreamServiceInfo(R.drawable.icon_stream_service_alexa,
                            getString(R.string.harmanbar_jbl_Amazon_Alexa), null, true,
                            "https://www.amazon.com/b?ie=UTF8&node=17910796011"))
                    }
                    StreamService.SpotifyConnect -> {
                        mStreamServiceDataList.add(StreamServiceInfo(
                            R.drawable.icon_stream_service_spotify, getString(R.string.jbl_Spotify_Connect),
                            getString(R.string.newStructure_Ready_to_use_on_Spotify_App_2), false,
                            "https://connect.spotify.com/howto"))
                    }
                    else -> {}
                }
            }
        }

        var featureSupport = ApplicationViewModel.getInstance().getFeatureSupport(data?.authDevice?.deviceCrc)
        var isTidalConnectSupport = TextUtils.equals("true", featureSupport?.featureSupport?.tidalConnect?.support)
        if (isTidalConnectSupport) {
            mStreamServiceDataList.add(StreamServiceInfo(R.drawable.icon_stream_service_tidal,
                    getString(R.string.tidal_connect), getString(R.string.use_on_tidal), false,
                    "https://tidal.com/connect"))
        }

//        mStreamServiceDataList.add(StreamServiceInfo(R.drawable.icon_stream_service_airplay,
//                getString(R.string.harmanbar_devicelist_AirPlay), getString(R.string.use_on_airplay), false,
//                "https://support.apple.com/en-us/HT202809"))
        wifiStreamingAdapter = WifiStreamingAdapter(requireContext(), mStreamServiceDataList.toMutableList())
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())
        recyclerView?.adapter = wifiStreamingAdapter
        val itemDecorationCount = recyclerView?.itemDecorationCount
        if (itemDecorationCount == 0) {
            recyclerView?.addItemDecoration(SpaceItemDecoration(ScreenUtil.dip2px(requireContext(), 8f)))
        }
        wifiStreamingAdapter?.setOnItemClickListener(object : WifiStreamingAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int, itemData: StreamServiceInfo) {
                when (view.id) {
                    R.id.item_root_layout -> {
                        if (itemData.isSelected) return
                        goWebLink(itemData.linkUrl)
                    }
                    R.id.tv_item_right -> {
                        onEnableClick(position)
                    }
                    R.id.iv_item_right -> {
                        goWebLink(itemData.linkUrl)
                    }
                }
            }
        })
    }

    private fun goWebLink( linkUrl:String?){
        var intent = Intent()
        intent.action = "android.intent.action.VIEW"
        val url = Uri.parse(linkUrl)
        intent.data = url
        startActivity(intent)
    }


    private fun onEnableClick(position: Int) {
        if (position != 0 && !mStreamServiceDataList[position].isSelected) return

        var streamServiceList = getStreamServiceList()
        LogsUtil.d(AppLogTagUtil.DEVICE_TAG, "$TAG onEnableClick streamServiceList= $streamServiceList")
        val dataInfo = DataFragInfo()
        dataInfo.deviceItem = data?.authDevice?.wifiDevice
        dataInfo.frameId = data?.frameId!!
        dataInfo.type = DataInfo.TYPE_SETTING
        if (streamServiceList.isNullOrEmpty()) {
            AmazonAlexaLoginStatusUtil.clearAll()
            var vfrag = FragAmazonAlexaStatus()
            vfrag.dataInfo = dataInfo
            vfrag.authDevice = data?.authDevice
            data?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
        } else {
            var optional = streamServiceList.stream().filter { StreamService.AmazonAlexaVA == it }.findFirst()
            if (optional.isPresent) {
                AmazonAlexaLoginStatusUtil.clearAll()
                var vfrag = AlexaVoiceAssistantFragment()
                vfrag.enterFrom = EnterFromType.TYPE_SETTING
                vfrag.dataInfo = dataInfo
                vfrag.authDevice = data?.authDevice
                data?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
                setupGVA = false
//                CoulsonManager.instance.recordCoulsonEntryValue(EventUtils.post_oobe_wifi_streaming)
            } else {
                AmazonAlexaLoginStatusUtil.clearAll()
                var vfrag = FragAmazonAlexaStatus()
                vfrag.dataInfo = dataInfo
                vfrag.authDevice = data?.authDevice
                data?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
            }
        }
    }

    override fun bindSlots() {
        super.bindSlots()
    }

    override fun initUtils() {
        super.initUtils()
        StatusBarUtils.setCustomStatusBarColor(activity, true, ActivityCompat.getColor(requireActivity(), R.color.color_00B4B2))
    }
    private var composeStatusViewModel: ComposeStatusViewModel? = null
    override fun onResume() {
        LogsUtil.i(AppLogTagUtil.DEVICE_TAG,"$TAG onResume")
        super.onResume()
        getComposeStatus()
    }
    private fun getComposeStatus() {
        if (composeStatusViewModel == null) {
            composeStatusViewModel = ComposeStatusViewModel()
        }
        composeStatusViewModel?.getComposeStatus(data?.authDevice?.wifiDevice, object : ComposeStatusListener {
            override fun onChanged(composeStatus: ComposeStatus?) {
                LogsUtil.i(AppLogTagUtil.DEVICE_TAG,"$TAG getComposeStatus: $composeStatus")
                var isChromecastEnable = composeStatus?.isChromecastEnable
                isChromecastEnable?.apply {
                    ApplicationViewModel.getInstance().setChromecastStatus(data?.authDevice?.deviceCrc, isChromecastEnable)
                    if(setupGVA){
//                        CoulsonManager.instance.recordVaSetupResultValue(EventUtils.gva, isChromecastEnable)
                        setupGVA = false
                    }
                    if (isChromecastEnable) {
                        //已经设置
                        hideBottomMaskView(true)
                    } else {
                        hideBottomMaskView(false)
                    }
                }

                var isAlexaEnable = composeStatus?.isAlexaEnable
                isAlexaEnable?.apply {
                    var item = AlexaStatusItem()
                    item.title = data?.authDevice?.wifiDevice?.Name ?: ""

                    item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
                    item.uuid = data?.authDevice?.wifiDevice?.uuid
                    item.icon_name = DeviceImageUtil.getDeviceImgNameByDeviceItem(data?.authDevice?.wifiDevice)
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG,"$TAG isAlexaEnable: $this")
                    if (this) {
                        item.item_type = SETTING_ITEM_TYPE.OPTION_1
                        AmazonAlexaLoginStatusUtil.addLoginItem(item)
                        updateAlexaListItem(false)
                    } else {
                        item.sub_desc = SkinResourcesUtils.getString("jbl_Enable")
                        item.item_type = SETTING_ITEM_TYPE.OPTION_2
                        AmazonAlexaLoginStatusUtil.addNoLoginItem(item)
                        //for test
                        updateAlexaListItem(true)
                    }
                }

            }
        })
    }


    private fun getStreamServiceList(): List<StreamService>? {
        return data?.authDevice?.modelName?.let { AppConfigurationUtils.getModelConfigByModelName(it)?.streamService }
    }

    override fun onClick(view: View?) {
        view?.let {
            when (it.id) {
                R.id.tv_enable -> {
                    onChromecastBuiltInClick()
                }
                R.id.learn_more_layout -> {
                    var intent = Intent()
                    intent.action = "android.intent.action.VIEW"
                    val url = Uri.parse("https://www.google.com/chromecast/built-in/learn/audio/")
                    intent.data = url
                    startActivity(intent)
                }
                R.id.iv_back -> {
                    onBackKeyDown()
                }
            }
        }
    }
    private var chromecastDialogProvider: ChromecastDialogProvider? = null
    private fun onChromecastBuiltInClick() {
        var streamServiceList = getStreamServiceList()
        if (streamServiceList.isNullOrEmpty()) {
            showChromecastBuiltInDialog()
        } else {
            var optional = streamServiceList.stream().filter { StreamService.GoogleVA == it }.findFirst()
            if (optional.isPresent) {
                showGoogleAssistantsDialog()
            } else {
                showChromecastBuiltInDialog()
            }
        }
    }
    private var setupGoogleAssistantAndChromecastDialogFragment: SetupGVANormalDialogFragment? = null
    private fun showGoogleAssistantsDialog() {
        if (setupGoogleAssistantAndChromecastDialogFragment == null) {
            setupGoogleAssistantAndChromecastDialogFragment = SetupGVANormalDialogFragment()
            setupGoogleAssistantAndChromecastDialogFragment?.onCloseListener = object : SetupGVANormalDialogFragment.OnCloseListener {
                override fun onClose() {
//                    onBackKeyDown()
                }
            }
        }
        if (setupGoogleAssistantAndChromecastDialogFragment?.isAdded == false) {
            setupGoogleAssistantAndChromecastDialogFragment?.show(requireActivity().supportFragmentManager, SetupGVANormalDialogFragment::class.java.simpleName)
//            CoulsonManager.instance.recordCoulsonEntryValue(EventUtils.post_oobe_wifi_streaming)
//            CoulsonManager.instance.recordVaSetupOptionValueForce(EventUtils.gva)
            setupGVA = true
        }
    }
    var setupGVA = false

    private fun showChromecastBuiltInDialog() {
        if (chromecastDialogProvider == null) {
            chromecastDialogProvider = activity?.let { ChromecastDialogProvider(it) }
        }
        chromecastDialogProvider?.authDevice = data?.authDevice
        chromecastDialogProvider?.showEnableServiceDialog(object : ChromecastDialogProvider.Callback {
            override fun onConfirm() {
                hideBottomMaskView(true)
                EventBus.getDefault().post(CommonEvent(CommonEvent.EVENT_ENABLE_CHROMECAST))
            }

            override fun onCancel() {

            }
        })
    }

    private fun updateAlexaListItem(selected: Boolean) {
        for (streamService in mStreamServiceDataList) {
            streamService.streamServiceName?.let { serviceName ->
                when(serviceName) {
                    getString(R.string.harmanbar_jbl_Amazon_Alexa)-> {
                        streamService.isSelected = selected
                    }
                }
            }
        }
        LogsUtil.i(AppLogTagUtil.DEVICE_TAG,"$TAG mStreamServiceDataList: $mStreamServiceDataList")
        wifiStreamingAdapter?.notifyItemChanged(0,R.id.tv_item_right)
        wifiStreamingAdapter?.notifyItemChanged(0,R.id.iv_item_right)
    }

    private fun hideBottomMaskView(hide: Boolean){
        bottomMaskLayout?.id?.let {
            LogsUtil.i(AppLogTagUtil.DEVICE_TAG,"$TAG hideBottomMaskView id = ${resources.getResourceEntryName(it)}, hide: $hide")
        }
        if (hide) {
//            bottomMaskLayout?.visibility = View.INVISIBLE
            bottomMaskLayout?.let {
                fadeOut(it)
            }
            /*cview.findViewById<View>(R.id.iv_masking)?.visibility = View.INVISIBLE
            cview.findViewById<View>(R.id.iv_streaming_cast_solid)?.visibility = View.INVISIBLE
            cview.findViewById<View>(R.id.tv_enable_cast)?.visibility = View.INVISIBLE
            cview.findViewById<View>(R.id.tv_enable)?.visibility = View.INVISIBLE*/
        } else {
//            bottomMaskLayout?.visibility = View.VISIBLE
            bottomMaskLayout?.let {
                fadeIn(it)
            }
            /*cview.findViewById<View>(R.id.iv_masking)?.visibility = View.VISIBLE
            cview.findViewById<View>(R.id.iv_streaming_cast_solid)?.visibility = View.VISIBLE
            cview.findViewById<View>(R.id.tv_enable_cast)?.visibility = View.VISIBLE
            cview.findViewById<View>(R.id.tv_enable)?.visibility = View.VISIBLE*/
        }
    }

    private fun fadeIn(view: View, startAlpha: Float, endAlpha: Float, duration: Long) {
        if (view.visibility == View.VISIBLE) return
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.duration = duration
        view.startAnimation(animation)
        view.visibility = View.VISIBLE
    }

    private fun fadeIn(view: View) {
        fadeIn(view, 0f, 1f, 400)

        // We disabled the button in fadeOut(), so enable it here.
        view.isEnabled = true
    }

    private fun fadeOut(view: View) {
        if (view.visibility != View.VISIBLE) return

        // Since the button is still clickable before fade-out animation
        // ends, we disable the button first to block click.
        view.isEnabled = false
        val animation: Animation = AlphaAnimation(1f, 0f)
        animation.duration = 400
        view.startAnimation(animation)
        view.visibility = View.INVISIBLE
    }

    companion object {
        val TAG: String = WifiStreamingFragment::class.java.simpleName
    }

}