package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.hkone.DeviceImageUtil
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.rightfrag_obervable.MessageDataItem
import com.wifiaudio.utils.DeviceProjectParseUtil
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.AppLogTagUtil
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus


class FragFeatureSupportUI : FragDevSettingBaseUI() {

    var vback: ImageView? = null
    var vTitle: TextView? = null
    var dataInfo: DataFragInfo? = null
    var recyclerView: RecyclerView? = null
    var currList: ArrayList<DevOptionItem> = ArrayList()
    var mAdapter: DevFeatureSupportUIAdapter? = null

    var vibrator: Vibrator? = null

    var bOperateControl = false//是否操作了remote control

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.frag_device_feature_support, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        return cview
    }

    override fun initView() {
        super.initView()

        recyclerView = cview.findViewById(R.id.recycle_view)
        vback = cview.findViewById(R.id.vback)
        vback?.setBackgroundResource(R.drawable.select_icon_menu_back)

        /*vTitle = cview.findViewById(R.id.vtitle)
        vTitle?.apply {
            text = SkinResourcesUtils.getString("remote_control")
            setTextColor(ContextCompat.getColor(requireContext(), R.color.fg1))
            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_16))
            typeface = FontUtil.getFontTypeface(requireContext(), ComponentConfig.OPEN_SANS_BOLD)
        }*/

        vibrator = activity?.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator?

        bOperateControl = false

        if (BuildConfig.FLAVOR.equals("jblone", ignoreCase = true)) {
            initRemoteControlListForJBLOne()
            recyclerView?.layoutManager = getJBLBarLayoutManager()
            recyclerView?.addItemDecoration(SpacesItemDecoration(WAApplication.mResources.getDimension(R.dimen.width_12)))
        }
        else{
            initRemoteControlListForHKOne()
            recyclerView?.layoutManager = getHKBarLayoutManager()
            recyclerView?.addItemDecoration(SpacesItemDecoration(WAApplication.mResources.getDimension(R.dimen.width_16)))
        }

        mAdapter = activity?.let { DevFeatureSupportUIAdapter(it) }
        mAdapter?.setCurrList(currList)

        recyclerView?.apply {
            adapter = mAdapter
            setHasFixedSize(true)
            isNestedScrollingEnabled = false
        }
    }

    private fun getHKBarLayoutManager(): GridLayoutManager {
        lateinit var gridLayoutManager: GridLayoutManager
        GridLayoutManager(activity, 6).also { gridLayoutManager = it }

        if (gridLayoutManager != null) {
            gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {

                override fun getSpanSize(position: Int): Int {
                    return when (position) {
                        0, 4, 5 -> { gridLayoutManager.spanCount }
                        1, 2, 3 -> { 2 }
                        else -> { 3 }
                    }
                }
            }
        }
        return gridLayoutManager
    }


    private fun getJBLBarLayoutManager(): StaggeredGridLayoutManager {
        val spanCount = 2
        var staggeredGridLayoutManager = StaggeredGridLayoutManager( spanCount, GridLayoutManager.VERTICAL )
        staggeredGridLayoutManager.reverseLayout = false
        return staggeredGridLayoutManager
    }



    private fun initRemoteControlListForJBLOne() {
        var iconNameArray = arrayOf<String>()

        iconNameArray += arrayOf( "jbl_icon_remote_control_onoff", "jbl_icon_remote_control_mute",
            "jbl_icon_remote_control_tv", "jbl_icon_remote_control_volume_bg", "jbl_icon_remote_control_bt",
            "jbl_icon_remote_control_hdmi", "jbl_icon_remote_control_pause"
        )

        iconNameArray += if (DeviceImageUtil.isJBL700(dataInfo?.deviceItem?.devStatus?.project)) {
            "jbl_icon_remote_control_atmos_700"
        } else{
            "jbl_icon_remote_control_atmos"
        }

        iconNameArray += arrayOf( "jbl_icon_remote_control_favorite", "jbl_icon_remote_control_bass", "jbl_icon_remote_control_calibr" )

        currList.clear()
        var item: DevOptionItem ?
        for ( iconName in iconNameArray) {
            item = DevOptionItem()
            item.title = ""
            item.id = IDS.ID_NONE
            item.hasIcon = true
            item.icon_Name = iconName
            item.bClickItem = true
            currList.add(item)
        }

        if (DeviceImageUtil.isJBL700(dataInfo?.deviceItem?.devStatus?.project)
            || DeviceProjectParseUtil.JBLOneProject.isJBL800(dataInfo?.deviceItem?.devStatus?.project)
            || DeviceProjectParseUtil.JBLOneProject.isJBL1000(dataInfo?.deviceItem?.devStatus?.project)
            || DeviceProjectParseUtil.JBLOneProject.isJBL1300(dataInfo?.deviceItem?.devStatus?.project)
        ){
/*
            item = DevOptionItem()
            item.title = ""
            item.id = IDS.ID_NONE
            item.hasIcon = false
            item.icon_Name = ""
            item.bClickItem = false
            currList.add(item)
*/

            item = DevOptionItem()
            item.title = ""
            item.id = IDS.ID_NONE
            item.hasIcon = true
            item.icon_Name = "jbl_icon_remote_control_rear"
            item.bClickItem = true
            currList.add(item)
        }
    }


    private fun initRemoteControlListForHKOne(){
        currList.clear()
        val iconNameArray = arrayOf(
            "hk_icon_remote_control_onoff",
            "hk_icon_remote_control_tv", "hk_icon_remote_control_bt", "hk_icon_remote_control_hdmi",
            "hk_icon_remote_control_volume_bg",
            "hk_icon_remote_control_mute",
            "hk_icon_remote_control_bass", "hk_icon_remote_control_moment",
            "hk_icon_remote_control_atmos", "hk_icon_remote_control_surround",
            "hk_icon_remote_control_calibr", "hk_icon_remote_control_pause"
        )

        var item: DevOptionItem?
        for (iconName in iconNameArray) {
            item = DevOptionItem()
            item.title = ""
            item.id = IDS.ID_NONE
            item.hasIcon = true
            item.icon_Name = iconName
            item.bClickItem = true
            currList.add(item)
        }
    }




    class SpacesItemDecoration(space: Float) : RecyclerView.ItemDecoration() {
        private var space = 0
        init {
            this.space = space.toInt()
        }

        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            outRect.left = 0//space
            outRect.right = 0//space
            outRect.bottom = space
            outRect.top = space
        }
    }

    override fun bindSlots() {
        super.bindSlots()

        vback?.setOnClickListener {
            onBackKeyDown()
        }

        mAdapter?.setOnItemClickListener(object : DevFeatureSupportUIAdapter.IOnItemClickListener {

            override fun onItemClicked(pos: Int, item: DevOptionItem?) {
                when (item?.icon_Name) {
                    "jbl_icon_remote_control_onoff", "hk_icon_remote_control_onoff" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_POWER)
                    }

                    "jbl_icon_remote_control_mute", "hk_icon_remote_control_mute" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_MUTE)
                    }

                    "jbl_icon_remote_control_tv", "hk_icon_remote_control_tv" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_SOURCETV)
                    }

                    "jbl_icon_remote_control_bt", "hk_icon_remote_control_bt"-> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_BT)
                    }

                    "jbl_icon_remote_control_hdmi", "hk_icon_remote_control_hdmi" -> {
                        if (DeviceImageUtil.isJBL700(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceProjectParseUtil.JBLOneProject.isJBL800(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceProjectParseUtil.JBLOneProject.isJBL1000(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceProjectParseUtil.JBLOneProject.isJBL1300(dataInfo?.deviceItem?.devStatus?.project)
                        ) {
                            setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_SOURCEHDMI_SWITCH)
                        } else {
                            setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_SOURCEHDMI)
                        }
                    }

                    "jbl_icon_remote_control_pause", "hk_icon_remote_control_pause" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_PLAY_PAUSE)
                    }

                    "jbl_icon_remote_control_atmos", "jbl_icon_remote_control_atmos_700", "hk_icon_remote_control_atmos" -> {
                        /*if (DeviceImageUtil.isJBL700(dataInfo?.deviceItem?.devStatus?.project)) {
                            setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_ATMOS_LEVEL)
                            eventAction.dimensions[EventUtils.di_action_item] = EventUtils.remoteController_atmos_Level
                        } else*/ if (DeviceProjectParseUtil.JBLOneProject.isJBL800(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceProjectParseUtil.JBLOneProject.isJBL1000(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceProjectParseUtil.JBLOneProject.isJBL1300(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceImageUtil.isHKC710(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceImageUtil.isHKC1110(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceImageUtil.isHKEnchantBar(dataInfo?.deviceItem?.devStatus?.project)
                            || DeviceImageUtil.isHKEnchantBarPro(dataInfo?.deviceItem?.devStatus?.project)
                        ) {
                            setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_ATMOS_LEVEL)
                        } else {
                            setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_ATMOS)
                        }
                    }

                    "jbl_icon_remote_control_favorite", "hk_icon_remote_control_moment" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_SMARTTEINGER)
                    }

                    "jbl_icon_remote_control_bass", "hk_icon_remote_control_bass" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_BASS)
                    }

                    "jbl_icon_remote_control_calibr", "hk_icon_remote_control_calibr" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_CALIBRATION)
                    }

                    "jbl_icon_remote_control_rear" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_REAR)
                    }

                    "hk_icon_remote_control_surround" -> {
                        setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_SURROUND)
                    }
                }

                onVibratorEvent()
            }

            override fun onVolumeDown() {
                onVibratorEvent()
                setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_VOLDN)
            }

            override fun onVolumeUp() {
                onVibratorEvent()
                setKeyPressed(JBLDataUtil.MCU_INPUT_KEY_VOLUP)
            }
        })
    }

    private fun setKeyPressed(value: String) {

        bOperateControl = true

        uihd.removeCallbacksAndMessages(null)

        uihd.postDelayed({
            LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "$TAG setKeyPressed:$value")

            DeviceCustomerSettingAction.makeDeviceSendAppController(dataInfo?.deviceItem?.IP, value, object : DeviceSettingActionCallback {

                override fun onSuccess(content: String?) {
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "$TAG setKeyPressed:Success:$content")
                }

                override fun onFailure(e: Throwable?) {
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "$TAG setKeyPressed:Failed:${e?.localizedMessage}")
                }
            })
        }, 400)
    }

    private fun onVibratorEvent() {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            var vef = VibrationEffect.createOneShot(200, 10)
            vibrator?.cancel()
            vibrator?.vibrate(vef)
        } else {
            vibrator?.cancel()
            vibrator?.vibrate(200);
        }
    }

    override fun initUtils() {
        super.initUtils()
//        updateTheme()
    }

    private fun updateTheme() {

        var bgDraw: Drawable? = null
        bgDraw = ColorDrawable(WAApplication.mResources.getColor(R.color.color_3E4355))

        cview.background = bgDraw

        vTitle?.setTextColor(GlobalUIConfig.color_navigationbar_title)
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        FragTabUtils.popBack(activity)

        if (bOperateControl) {
            val msg = MessageDataItem()
            msg.strDevUUID = dataInfo?.deviceItem?.uuid
            msg.obj = JBLRemoteControlItem()
            EventBus.getDefault().post(msg)
            bOperateControl = false
        }
    }

    companion object {
        val TAG: String = FragFeatureSupportUI::class.java.simpleName
    }

}
