package com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.partypad

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.TransitionDrawable
import android.os.Bundle
import android.view.View
import android.view.animation.Animation
import android.view.animation.TranslateAnimation
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.GsonUtils
import com.harman.bar.app.R
import com.harman.hkone.ScreenUtil
import com.harman.utils.Utils
import com.skin.font.FontUtils
import com.tencent.mmkv.MMKV
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.GridSpaceItemDecoration
import com.harman.command.one.bean.CustomizeSet
import com.harman.command.one.bean.DJPad
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.SvgDrawableUtil


class CreatePartyPadActivity : AppCompatActivity(), View.OnClickListener, CreatePartyPadCommonAdapter.OnItemClickListener {

    private var backView: ImageView? = null
    private var tvSave: TextView? = null
    private var layoutLeft: RelativeLayout? = null
    private var layoutMiddle: RelativeLayout? = null
    private var layoutRight: RelativeLayout? = null
    private var soundRecyclerView: RecyclerView? = null
    private var effectRecyclerView: RecyclerView? = null
    private var toneRecyclerView: RecyclerView? = null
    private var nestedScrollView: NestedScrollView? = null
    private var partyPadSoundAdapter: CreatePartyPadCommonAdapter? = null
    private var partyPadEffectAdapter: CreatePartyPadCommonAdapter? = null
    private var partyPadToneAdapter: CreatePartyPadCommonAdapter? = null
    private var translateAniShow: TranslateAnimation? = null
    private var translateAniHide: TranslateAnimation? = null
    private var ivPadLeft: ImageView? = null
    private var ivPadMiddle: ImageView? = null
    private var ivPadRight: ImageView? = null
    private var currentSelectPad = PAD_SELECT_NONE
    private var currentSelectPadItem: PartyPad.PartyPadItem? = null
    private var alreadySetPadLeft: PartyPad.PartyPadItem? = null
    private var alreadySetPadMiddle: PartyPad.PartyPadItem? = null
    private var alreadySetPadRight: PartyPad.PartyPadItem? = null
    private var needUnSelectAllItem = false
    private var mCustomizeSet: CustomizeSet? = null
    private var tvLeft: TextView? = null
    private var tvMiddle: TextView? = null
    private var tvRight: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_create_party_pad)
        initView()
    }

    private fun initView() {
        backView = findViewById(R.id.iv_back)
        tvSave = findViewById(R.id.tv_save)
        layoutLeft = findViewById(R.id.layout_left)
        layoutMiddle = findViewById(R.id.layout_middle)
        layoutRight = findViewById(R.id.layout_right)
        ivPadLeft = findViewById(R.id.iv_pad_left)
        ivPadMiddle = findViewById(R.id.iv_pad_middle)
        ivPadRight = findViewById(R.id.iv_pad_right)
        nestedScrollView = findViewById(R.id.nestedScrollView)
        tvLeft = findViewById(R.id.tv_left)
        tvMiddle = findViewById(R.id.tv_middle)
        tvRight = findViewById(R.id.tv_right)

        backView?.setOnClickListener(this)
        tvSave?.setOnClickListener(this)
        layoutLeft?.setOnClickListener(this)
        layoutMiddle?.setOnClickListener(this)
        layoutRight?.setOnClickListener(this)
        tvSave?.isEnabled = false
        tvSave?.alpha = 0.5f
        nestedScrollView?.visibility = View.GONE

        initCurSetData()
        mCustomizeSet?.let {
            initCurSetView(it)
        } ?: updatePadStatus(PAD_SELECT_NONE)

        initSoundRecyclerView()
        initEffectRecyclerView()
        initToneRecyclerView()
        translateTopAnimation()
        translateBottomAnimation()
    }

    private fun initCurSetData() {
        val stringExtra = intent.getStringExtra(DJPadParamsUtil.KEY_CUR_SET)
        val decodeString = MMKV.defaultMMKV().decodeString(DJPadParamsUtil.KEY_CUR_SET)
        stringExtra?.let { extraStr ->
            mCustomizeSet = GsonUtils.fromJson(extraStr, CustomizeSet::class.java)
            LogsUtil.d(TAG, "initCurSetData toJson mCustomizeSet: $mCustomizeSet")
            mCustomizeSet?.let {
                alreadySetPadLeft = PartyPad().PartyPadItem()
                alreadySetPadMiddle = PartyPad().PartyPadItem()
                alreadySetPadRight = PartyPad().PartyPadItem()
                alreadySetPadLeft?.value = it.intSets?.elementAtOrNull(0) ?: 0
                alreadySetPadMiddle?.value = it.intSets?.elementAtOrNull(1) ?: 0
                alreadySetPadRight?.value = it.intSets?.elementAtOrNull(2) ?: 0
            }
        }
    }

    private fun initCurSetView(customizeSet: CustomizeSet?) {
        customizeSet?.let { itCustomizeSet ->
            val curSet = itCustomizeSet.intSets
            curSet?.let { itCurSet ->
                val leftValue = itCurSet.elementAtOrNull(0) ?: 0
                val centerValue = itCurSet.elementAtOrNull(1) ?: 0
                val rightValue = itCurSet.elementAtOrNull(2) ?: 0
                layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left)
                layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle)
                layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right)
                DJPadParamsUtil.getDJPadImageResMap()[leftValue]?.let { ivPadLeft?.setImageResource(it) }
                DJPadParamsUtil.getDJPadImageResMap()[centerValue]?.let { ivPadMiddle?.setImageResource(it) }
                DJPadParamsUtil.getDJPadImageResMap()[rightValue]?.let { ivPadRight?.setImageResource(it) }

                tvLeft?.setTextColor(getColor(R.color.fg_primary))
                tvLeft?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvMiddle?.setTextColor(getColor(R.color.fg_primary))
                tvMiddle?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvRight?.setTextColor(getColor(R.color.fg_primary))
                tvRight?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
            }
        }
    }

    private fun initSoundRecyclerView() {
        soundRecyclerView = findViewById(R.id.sound_recyclerView)
        soundRecyclerView?.let {
            partyPadSoundAdapter = CreatePartyPadCommonAdapter(this, initSoundDataList())
            it.layoutManager = GridLayoutManager(this, 2)
            it.adapter = partyPadSoundAdapter
            it.isNestedScrollingEnabled = false
            it.setHasFixedSize(true)
            it.addItemDecoration(GridSpaceItemDecoration(2, ScreenUtil.dip2px(this, 12f), ScreenUtil.dip2px(this, 12f)))
            partyPadSoundAdapter?.setOnItemClickListener(this)
        }
    }

    private fun initEffectRecyclerView() {
        effectRecyclerView = findViewById(R.id.effect_recyclerView)
        effectRecyclerView?.let {
            partyPadEffectAdapter = CreatePartyPadCommonAdapter(this, initEffectDataList())
            it.layoutManager = GridLayoutManager(this, 2)
            it.adapter = partyPadEffectAdapter
            it.isNestedScrollingEnabled = false
            it.setHasFixedSize(true)
            it.addItemDecoration(GridSpaceItemDecoration(2, ScreenUtil.dip2px(this, 12f), ScreenUtil.dip2px(this, 12f)))
            partyPadEffectAdapter?.setOnItemClickListener(this)
        }
    }

    private fun initToneRecyclerView() {
        toneRecyclerView = findViewById(R.id.tone_recyclerView)
        toneRecyclerView?.let {
            partyPadToneAdapter = CreatePartyPadCommonAdapter(this, initToneDataList())
            it.layoutManager = GridLayoutManager(this, 2)
            it.adapter = partyPadToneAdapter
            it.isNestedScrollingEnabled = false
            it.setHasFixedSize(true)
            it.addItemDecoration(GridSpaceItemDecoration(2, ScreenUtil.dip2px(this, 12f), ScreenUtil.dip2px(this, 12f)))
            partyPadToneAdapter?.setOnItemClickListener(this)
        }
    }

    private fun initSoundDataList(): MutableList<PartyPad.PartyPadItem> {
        val partyPadDataList: MutableList<PartyPad.PartyPadItem> = ArrayList()
        for (i in DJPadParamsUtil.mSoundValueList.indices) {
            val partyPadItem = PartyPad().PartyPadItem()
            partyPadItem.itemType = PARTY_PAD_ITEM_TYPE_SOUND
            partyPadItem.isSelectStatus = false
            partyPadItem.value = DJPadParamsUtil.mSoundValueList[i]
            DJPadParamsUtil.mSoundImageList.elementAtOrNull(i)?.let {
                partyPadItem.resId = it
            }
            partyPadDataList.add(partyPadItem)
        }
        return partyPadDataList
    }

    private fun initEffectDataList(): MutableList<PartyPad.PartyPadItem> {
        val partyPadDataList: MutableList<PartyPad.PartyPadItem> = ArrayList()
        for (i in DJPadParamsUtil.mEffectValueList.indices) {
            val partyPadItem = PartyPad().PartyPadItem()
            partyPadItem.itemType = PARTY_PAD_ITEM_TYPE_EFFECT
            partyPadItem.isSelectStatus = false
            partyPadItem.value = DJPadParamsUtil.mEffectValueList[i]
            DJPadParamsUtil.mEffectImageList.elementAtOrNull(i)?.let {
                partyPadItem.resId = it
            }
            partyPadDataList.add(partyPadItem)
        }
        return partyPadDataList
    }

    private fun initToneDataList(): MutableList<PartyPad.PartyPadItem> {
        val partyPadDataList: MutableList<PartyPad.PartyPadItem> = ArrayList()
        for (i in DJPadParamsUtil.mToneValueList.indices) {
            val partyPadItem = PartyPad().PartyPadItem()
            partyPadItem.itemType = PARTY_PAD_ITEM_TYPE_TONE
            partyPadItem.isSelectStatus = false
            partyPadItem.value = DJPadParamsUtil.mToneValueList[i]
            DJPadParamsUtil.mToneImageList.elementAtOrNull(i)?.let {
                partyPadItem.resId = it
            }
            partyPadDataList.add(partyPadItem)
        }
        return partyPadDataList
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_back -> {
                nestedScrollViewAniHide()
                finish()
            }
            R.id.tv_save -> {
                if (tvSave?.isEnabled == true) {
                    nestedScrollViewAniHide()
                    try {
                        val leftValue = alreadySetPadLeft?.value ?: 0
                        val centerValue = alreadySetPadMiddle?.value ?: 0
                        val rightValue = alreadySetPadRight?.value ?: 0
                        val intent = Intent()
                        val djPad = DJPad()
                        djPad.type = DJPadParamsUtil.TYPE5
                        djPad.status = DJPadParamsUtil.PARTY_PAD_ON
                        djPad.curSet = arrayListOf(leftValue, centerValue, rightValue)
                        val customizeSet = CustomizeSet()
                        customizeSet.intSets = listOf(leftValue, centerValue, rightValue)
                        customizeSet.type = DJPadParamsUtil.TYPE5
                        djPad.customizeSet = arrayListOf(customizeSet)
                        val toJsonData = GsonUtils.toJson(djPad)
                        intent.putExtra(DJPadParamsUtil.KEY_CUSTOMIZE_SET, toJsonData)
                        setResult(RESULT_OK, intent)
                        finish()
                    } catch (e: Exception) {
                        LogsUtil.d(TAG, "toJson Exception")
                    }
                }
            }
            R.id.layout_left -> {
                nestedScrollViewAniShow()
                needUnSelectAllItem = true
                mCustomizeSet?.let {
                    updateCurSetView(PAD_SELECT_LEFT)
                } ?: updatePadStatus(PAD_SELECT_LEFT)
            }
            R.id.layout_middle -> {
                needUnSelectAllItem = true
                nestedScrollViewAniShow()
                mCustomizeSet?.let {
                    updateCurSetView(PAD_SELECT_MIDDLE)
                } ?: updatePadStatus(PAD_SELECT_MIDDLE)
            }
            R.id.layout_right -> {
                needUnSelectAllItem = true
                nestedScrollViewAniShow()
                mCustomizeSet?.let {
                    updateCurSetView(PAD_SELECT_RIGHT)
                } ?: updatePadStatus(PAD_SELECT_RIGHT)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updateCurSetView(status: Int) {
        tvSave?.isEnabled = true
        tvSave?.alpha = 1.0f
        when (status) {
            PAD_SELECT_LEFT -> {
                layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left_select)
                layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle)
                layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right)
                currentSelectPadItem?.let {
                    it.resId?.let { it1 -> ivPadLeft?.setImageResource(it1) }
                } ?: let {
                    alreadySetPadLeft?.let {
                        it.resId?.let { it1 -> ivPadLeft?.setImageResource(it1) }
                    }
                }
                currentSelectPad = PAD_SELECT_LEFT
            }
            PAD_SELECT_MIDDLE -> {
                layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left)
                layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle_select)
                layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right)
                currentSelectPadItem?.let {
                    it.resId?.let { it1 -> ivPadMiddle?.setImageResource(it1) }
                } ?: let {
                    alreadySetPadMiddle?.let {
                        it.resId?.let { it1 -> ivPadMiddle?.setImageResource(it1) }
                    }
                }

                currentSelectPad = PAD_SELECT_MIDDLE
            }
            PAD_SELECT_RIGHT -> {
                layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left)
                layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle)
                layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right_select)
                currentSelectPadItem?.let {
                    it.resId?.let { it1 -> ivPadRight?.setImageResource(it1) }
                } ?: let {
                    alreadySetPadRight?.let {
                        it.resId?.let { it1 -> ivPadRight?.setImageResource(it1) }
                    }
                }

                currentSelectPad = PAD_SELECT_RIGHT
            }
        }

        currentSelectPadItem = null
        if (needUnSelectAllItem) {
            nestedScrollView?.smoothScrollTo(0, 0)
            partyPadSoundAdapter?.let {
                it.setItemAllUnselect()
                it.notifyDataSetChanged()
            }
            partyPadEffectAdapter?.let {
                it.setItemAllUnselect()
                it.notifyDataSetChanged()
            }
            partyPadToneAdapter?.let {
                it.setItemAllUnselect()
                it.notifyDataSetChanged()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updatePadStatus(status: Int) {
        when (status) {
            PAD_SELECT_NONE -> {
                layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left)
                layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle)
                layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right)
                ivPadLeft?.setImageResource(R.drawable.icon_add_grey)
                ivPadMiddle?.setImageResource(R.drawable.icon_add_grey)
                ivPadRight?.setImageResource(R.drawable.icon_add_grey)
                currentSelectPad = PAD_SELECT_NONE
                tvLeft?.setTextColor(getColor(R.color.fg_primary))
                tvLeft?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvMiddle?.setTextColor(getColor(R.color.fg_primary))
                tvMiddle?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvRight?.setTextColor(getColor(R.color.fg_primary))
                tvRight?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
            }
            PAD_SELECT_LEFT -> {
                layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left_select)

                tvLeft?.setTextColor(getColor(R.color.fg_activate))
                tvLeft?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)

                tvMiddle?.setTextColor(getColor(R.color.fg_primary))
                tvMiddle?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvRight?.setTextColor(getColor(R.color.fg_primary))
                tvRight?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                currentSelectPadItem?.let {
                    it.resId?.let { it1 ->
                        val selectedDrawable = resources.getDrawable(it1)
                        SvgDrawableUtil.tintDrawable(selectedDrawable, ActivityCompat.getColor(this, R.color.fg_activate))
                        ivPadLeft?.setImageDrawable(selectedDrawable)
                    }
                } ?: let {
                    alreadySetPadLeft?.let {
                        it.resId?.let { it1 -> ivPadLeft?.setImageResource(it1) }
                    } ?: let {
                        ivPadLeft?.setImageResource(R.drawable.icon_add_green)
                    }
                }
                alreadySetPadMiddle?.let {
                    layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle)
                    it.resId?.let { it1 -> ivPadMiddle?.setImageResource(it1) }
                } ?: let {
                    layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle_dash)
                    ivPadMiddle?.setImageResource(R.drawable.icon_add_grey)
                }
                alreadySetPadRight?.let {
                    layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right)
                    it.resId?.let { it1 -> ivPadRight?.setImageResource(it1) }
                } ?: let {
                    layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right_dash)
                    ivPadRight?.setImageResource(R.drawable.icon_add_grey)
                }
                currentSelectPad = PAD_SELECT_LEFT
            }
            PAD_SELECT_MIDDLE -> {
                layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle_select)

                tvLeft?.setTextColor(getColor(R.color.fg_primary))
                tvLeft?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvMiddle?.setTextColor(getColor(R.color.fg_activate))
                tvMiddle?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)

                tvRight?.setTextColor(getColor(R.color.fg_primary))
                tvRight?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                currentSelectPadItem?.let {
                    it.resId?.let { it1 ->
                        val selectedDrawable = resources.getDrawable(it1)
                        SvgDrawableUtil.tintDrawable(selectedDrawable, ActivityCompat.getColor(this, R.color.fg_activate))
                        ivPadMiddle?.setImageDrawable(selectedDrawable)
                    }
                } ?: let {
                    alreadySetPadMiddle?.let {
                        it.resId?.let { it1 -> ivPadMiddle?.setImageResource(it1) }
                    } ?: let {
                        ivPadMiddle?.setImageResource(R.drawable.icon_add_green)
                    }
                }
                alreadySetPadLeft?.let {
                    layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left)
                    it.resId?.let { it1 -> ivPadLeft?.setImageResource(it1) }
                } ?: let {
                    layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left_dash)
                    ivPadLeft?.setImageResource(R.drawable.icon_add_grey)
                }
                alreadySetPadRight?.let {
                    layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right)
                    it.resId?.let { it1 -> ivPadRight?.setImageResource(it1) }
                } ?: let {
                    layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right_dash)
                    ivPadRight?.setImageResource(R.drawable.icon_add_grey)
                }
                currentSelectPad = PAD_SELECT_MIDDLE
            }
            PAD_SELECT_RIGHT -> {
                layoutRight?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_right_select)

                tvLeft?.setTextColor(getColor(R.color.fg_primary))
                tvLeft?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvMiddle?.setTextColor(getColor(R.color.fg_primary))
                tvMiddle?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

                tvRight?.setTextColor(getColor(R.color.fg_activate))
                tvRight?.typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)

                currentSelectPadItem?.let {
                    it.resId?.let { it1 ->
                        val selectedDrawable = resources.getDrawable(it1)
                        SvgDrawableUtil.tintDrawable(selectedDrawable, ActivityCompat.getColor(this, R.color.fg_activate))
                        ivPadRight?.setImageDrawable(selectedDrawable)
                    }
                } ?: let {
                    alreadySetPadRight?.let {
                        it.resId?.let { it1 -> ivPadRight?.setImageResource(it1) }
                    } ?: let {
                        ivPadRight?.setImageResource(R.drawable.icon_add_green)
                    }
                }
                alreadySetPadLeft?.let {
                    layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left)
                    it.resId?.let { it1 -> ivPadLeft?.setImageResource(it1) }
                } ?: let {
                    layoutLeft?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_left_dash)
                    ivPadLeft?.setImageResource(R.drawable.icon_add_grey)
                }
                alreadySetPadMiddle?.let {
                    layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle)
                    it.resId?.let { it1 -> ivPadMiddle?.setImageResource(it1) }
                } ?: let {
                    layoutMiddle?.setBackgroundResource(R.drawable.shape_create_party_pad_bg_middle_dash)
                    ivPadMiddle?.setImageResource(R.drawable.icon_add_grey)
                }
                currentSelectPad = PAD_SELECT_RIGHT
            }
        }
        currentSelectPadItem = null
        if (needUnSelectAllItem) {
            nestedScrollView?.smoothScrollTo(0, 0)
            partyPadSoundAdapter?.let {
                it.setItemAllUnselect()
                it.notifyDataSetChanged()
            }
            partyPadEffectAdapter?.let {
                it.setItemAllUnselect()
                it.notifyDataSetChanged()
            }
            partyPadToneAdapter?.let {
                it.setItemAllUnselect()
                it.notifyDataSetChanged()
            }
        }
    }

    override fun onItemClick(view: View, position: Int, partyPadItem: PartyPad.PartyPadItem) {
        when (view.id) {
            R.id.layout_item -> {
                currentSelectPadItem = partyPadItem
                needUnSelectAllItem = false
                updateItemSelectStatus(partyPadItem, position)

                mCustomizeSet?.let {
                    updateCurSetView(currentSelectPad)
                } ?: updatePadStatus(currentSelectPad)

                when (currentSelectPad) {
                    PAD_SELECT_LEFT -> {
                        alreadySetPadLeft = partyPadItem
                    }
                    PAD_SELECT_MIDDLE -> {
                        alreadySetPadMiddle = partyPadItem
                    }
                    PAD_SELECT_RIGHT -> {
                        alreadySetPadRight = partyPadItem
                    }
                }
                tvSave?.isEnabled = alreadySetPadLeft != null && alreadySetPadMiddle != null && alreadySetPadRight != null
                tvSave?.alpha = if (tvSave?.isEnabled == true) 1.0f else 0.5f
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updateItemSelectStatus(partyPadItem: PartyPad.PartyPadItem, position: Int) {
        when (partyPadItem.itemType) {
            PARTY_PAD_ITEM_TYPE_SOUND -> {
                partyPadSoundAdapter?.let {
                    it.setItemSelected(position)
                    it.notifyDataSetChanged()
                }
                partyPadEffectAdapter?.let {
                    it.setItemAllUnselect()
                    it.notifyDataSetChanged()
                }
                partyPadToneAdapter?.let {
                    it.setItemAllUnselect()
                    it.notifyDataSetChanged()
                }
            }
            PARTY_PAD_ITEM_TYPE_EFFECT -> {
                partyPadSoundAdapter?.let {
                    it.setItemAllUnselect()
                    it.notifyDataSetChanged()
                }
                partyPadEffectAdapter?.let {
                    it.setItemSelected(position)
                    it.notifyDataSetChanged()
                }
                partyPadToneAdapter?.let {
                    it.setItemAllUnselect()
                    it.notifyDataSetChanged()
                }
            }
            PARTY_PAD_ITEM_TYPE_TONE -> {
                partyPadSoundAdapter?.let {
                    it.setItemAllUnselect()
                    it.notifyDataSetChanged()
                }
                partyPadEffectAdapter?.let {
                    it.setItemAllUnselect()
                    it.notifyDataSetChanged()
                }
                partyPadToneAdapter?.let {
                    it.setItemSelected(position)
                    it.notifyDataSetChanged()
                }
            }
        }
    }

    private fun nestedScrollViewAniShow() {
        translateAniShow?.let {
            if (nestedScrollView?.visibility == View.GONE) {
                nestedScrollView?.startAnimation(it)
                nestedScrollView?.visibility = View.VISIBLE
            }
        }
    }

    private fun nestedScrollViewAniHide() {
        translateAniHide?.let {
            if (nestedScrollView?.visibility == View.VISIBLE) {
                nestedScrollView?.startAnimation(it)
                nestedScrollView?.visibility = View.GONE
            }
        }
    }

    private fun translateTopAnimation() {
        translateAniShow = TranslateAnimation(
            Animation.RELATIVE_TO_SELF, 0f,
            Animation.RELATIVE_TO_SELF, 0f,
            Animation.RELATIVE_TO_SELF, 1f,
            Animation.RELATIVE_TO_SELF, 0f
        )
        translateAniShow?.repeatMode = Animation.REVERSE
        translateAniShow?.duration = 300
    }

    private fun translateBottomAnimation() {
        translateAniHide = TranslateAnimation(
            Animation.RELATIVE_TO_SELF, 0f,
            Animation.RELATIVE_TO_SELF, 0f,
            Animation.RELATIVE_TO_SELF, 0f,
            Animation.RELATIVE_TO_SELF, 1f
        )
        translateAniHide?.repeatMode = Animation.REVERSE
        translateAniHide?.duration = 300
    }

    private fun transitionAnimation(view: View, preResId: Int, toResId: Int) {
        val preDrawable = ContextCompat.getDrawable(this, preResId)
        val toDrawable = ContextCompat.getDrawable(this, toResId)
        val transitionDrawable = TransitionDrawable(arrayOf(preDrawable, toDrawable))
        if (view is ImageView) {
            view.setImageDrawable(transitionDrawable)
        } else {
            view.background = transitionDrawable
        }
        transitionDrawable.isCrossFadeEnabled = true
        transitionDrawable.startTransition(300)

    }

    companion object {
        const val PAD_SELECT_NONE = 0
        const val PAD_SELECT_LEFT = 1
        const val PAD_SELECT_MIDDLE = 2
        const val PAD_SELECT_RIGHT = 3
        const val PARTY_PAD_ITEM_TYPE_SOUND = 4
        const val PARTY_PAD_ITEM_TYPE_EFFECT = 5
        const val PARTY_PAD_ITEM_TYPE_TONE = 6
        val TAG: String = CreatePartyPadActivity::class.java.simpleName
    }
}