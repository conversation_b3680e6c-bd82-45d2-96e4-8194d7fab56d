package com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.partypad

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.SvgDrawableUtil


class CreatePartyPadCommonAdapter(private val context: Context, private val dataList: MutableList<PartyPad.PartyPadItem>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var onItemClickListener: OnItemClickListener? = null

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.onItemClickListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return CommonViewHolder(LayoutInflater.from(context).inflate(R.layout.item_create_party_pad_common, parent, false))
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is CommonViewHolder -> {
                updateCommonViewHolder(holder, position, dataList[position])
            }
        }
    }

    override fun getItemCount(): Int {
        LogsUtil.d(TAG, "getItemCount: ${dataList.size}")
        return dataList.size
    }

    private fun updateCommonViewHolder(holder: CommonViewHolder, position: Int, partyPadItem: PartyPad.PartyPadItem) {
        holder.layoutItem.setOnClickListener {
            onItemClickListener?.onItemClick(it, position, partyPadItem)
        }
        holder.layoutItem.isSelected = partyPadItem.isSelectStatus
        if (partyPadItem.isSelectStatus) {
            holder.ivItemRight.setImageResource(R.drawable.check_circle)
            val drawable = partyPadItem.resId?.let { ContextCompat.getDrawable(context, it) }
            drawable?.let {
                SvgDrawableUtil.tintDrawable(drawable, ActivityCompat.getColor(context, R.color.my_party_pad_select_color))
                holder.ivItemLeft.setImageDrawable(it)
            }
        } else {
            holder.ivItemRight.setImageResource(R.drawable.icon_create_party_pad_add)
            partyPadItem.resId?.let { holder.ivItemLeft.setImageResource(it) }
        }

    }

    fun setItemSelected(position: Int) {
        for (i in dataList.indices) {
            dataList[i].isSelectStatus = i == position
        }
    }

    fun setItemAllUnselect() {
        for (i in dataList.indices) {
            dataList[i].isSelectStatus = false
        }
    }

    inner class CommonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var layoutItem: RelativeLayout = itemView.findViewById(R.id.layout_item)
        var ivItemLeft: ImageView = itemView.findViewById(R.id.iv_item_left)
        var ivItemRight: ImageView = itemView.findViewById(R.id.iv_item_right)

    }

    interface OnItemClickListener {
        fun onItemClick(view: View, position: Int, partyPadItem: PartyPad.PartyPadItem)
    }

    companion object {
        val TAG = CreatePartyPadCommonAdapter::class.simpleName
    }

}