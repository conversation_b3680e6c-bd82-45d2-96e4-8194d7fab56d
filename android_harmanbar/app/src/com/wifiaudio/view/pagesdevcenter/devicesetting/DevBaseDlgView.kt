package com.wifiaudio.view.pagesdevcenter.devicesetting

import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.harman.bar.app.R
import com.harman.bean.AuthDevice
import com.harman.utils.Utils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.view.dlg.ExtendDialogNew
import config.LogTags

open class DevBaseDlgView(activity: FragmentActivity) {

    private var extendDlg: ExtendDialogNew? = null
    var activity: FragmentActivity? = null
    var dataInfo: DataFragInfo? = null
    var authDevice: AuthDevice? = null
    var listener: IOnDlgBtnClickListener? = null
    var bListenerActionBack = true//返回键是否作用于弹框
    var uihd = Handler(Looper.getMainLooper())
    var rootView: View? = null

    init {
        this.activity = activity
    }

    fun init(layout: Int) {

        if (extendDlg == null)
            extendDlg = ExtendDialogNew(activity)
                .build(layout)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setbListenerActionBack(bListenerActionBack)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View?) {
                        updateLayoutUI(rootView)
//                        updateDlgContainerBG(rootView)
                    }

                    override fun onDismissDlg() {
                        dismissDialog()
//                        listener?.onCancel()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                        if (bListenerActionBack) {
                            dismissDialog()
                            listener?.onCancel()
                        }
                    }
                })
    }

    open fun setDlgClickListener(listener: IOnDlgBtnClickListener) {
        this.listener = listener
    }

    /**
     * 更新views
     */
    open fun updateLayoutUI(rootView: View?) {
        this.rootView = rootView
        activity?.apply {
//            uihd.postDelayed({ rootView?.setBackgroundColor(ContextCompat.getColor(activity!!, R.color.bg_overlay)) }, 260)
        }
    }

    /**
     * 基类更新弹框背景
     */
    open fun updateDlgContainerBG(cview: View?) {

    }

    open fun show() {
        extendDlg?.show()
    }

    open fun hideDlg() {
        extendDlg?.hideDlg()
    }

    open fun isShowing(): Boolean? {
        if (extendDlg == null)
            return false

        return extendDlg?.isShowing
    }

    open fun dismissDialog() {
        uihd.post {
            rootView?.setBackgroundColor(ContextCompat.getColor(activity!!, R.color.transparent))
            activity?.apply { Utils.decorateHarmanWindow(activity) }
        }
        LogsUtil.i(LogTags.UI, "${javaClass.simpleName} dismissDialog extendDlg $extendDlg")
        extendDlg?.apply {
            uihd.postDelayed({
                dismissDlg()
                extendDlg = null
            }, 10)
        }
    }

    interface IOnDlgBtnClickListener {
        fun onConfirm(any: Any?)
        fun onCancel()
    }
}