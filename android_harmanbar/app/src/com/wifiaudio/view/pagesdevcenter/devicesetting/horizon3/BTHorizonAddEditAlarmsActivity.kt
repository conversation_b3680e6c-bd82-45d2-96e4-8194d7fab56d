package com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.SeekBar
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityHorizonAlarmsAddEditBinding
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.discover.bean.Device
import com.harman.log.Logger
import com.harman.parseAsPartyBoxDevice
import com.harman.utils.Utils
import com.harman.widget.EditLightTimeView
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.viewmodel.BTHorizonAlarmsViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Calendar

class BTHorizonAddEditAlarmsActivity : AppCompatActivity(), View.OnClickListener {
    private val binding by lazy { ActivityHorizonAlarmsAddEditBinding.inflate(layoutInflater) }

    private val alarmsViewModel: BTHorizonAlarmsViewModel by viewModels<BTHorizonAlarmsViewModel>()

    //    private val radioViewModel: BTHorizonRadioViewModel by viewModels<BTHorizonRadioViewModel>()
    var fromPage: String? = null
    private var timeStep = 10

    private val mainDev by lazy {
        parseAsPartyBoxDevice()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        if (null == mainDev) {

            finish()
            return
        }
        currentAlarm = intent.getSerializableExtra("alarm") as? AlarmInfo.Alarm
        fromPage = intent?.getStringExtra(ALARM_FROM)
        alarmsViewModel.mainDev = mainDev!!
        lifecycle.addObserver(alarmsViewModel)
        setContentView(binding.root)
        initView()
//        initData()
        initObserve()
    }

    fun initView() {
        binding.ivBack.setOnClickListener(this)
        binding.tvSave.setOnClickListener(this)
        binding.tvSave.text = SkinResourcesUtils.getString("eq_save")
        binding.tvSave.enableButton(true)
//        binding.tvWakeUpAt.text = SkinResourcesUtils.getString("wake_up_at")
        binding.tvLight.text = SkinResourcesUtils.getString("sunrise")
        binding.tvRepeat.text = SkinResourcesUtils.getString("repeat")
        binding.tvWakeUpSound.text = SkinResourcesUtils.getString("sound")
        binding.tvVolume.text = SkinResourcesUtils.getString("jbl_Volume")
        Logger.d(TAG, "ALARM_RELATED EDIT_ALARM currentAlarm:${currentAlarm}")
        fromPage?.let {
            when (it) {
                ADD_ALARM -> {
                    binding.tvTitle.text = SkinResourcesUtils.getString("add_alarm")
                    binding.btnDeleteAlarm.visibility = View.GONE
                }

                EDIT_ALARM -> {
                    binding.tvTitle.text = SkinResourcesUtils.getString("set_alarm")
                    binding.btnDeleteAlarm.visibility = View.VISIBLE
                }
            }
        }

        binding.ivLightInfoTip.setOnClickListener(this)
        binding.tvRepeat.setOnClickListener(this)
        binding.tvRepeatDay.setOnClickListener(this)
        binding.ivRepeatMoreArrow.setOnClickListener(this)
        binding.tvWakeUpSound.setOnClickListener(this)
        binding.tvSoundType.setOnClickListener(this)
        binding.ivSoundMoreArrow.setOnClickListener(this)
        binding.btnDeleteAlarm.setOnClickListener(this)

        updateUI()

        binding.editLightTimeView.setOnSeekBarChangeListener(object : EditLightTimeView.IOnSeekBarChangeListener {
            override fun onPosition(position: Int) {
                Logger.d(TAG, "ALARM_RELATED IOnSeekBarChangeListener position:${position}")
                val hour = String.format("%02d", binding.alarmPicker.selectedHour)
                val minute = String.format("%02d", binding.alarmPicker.selectedMinute)
                val formattedTime = "$hour:$minute"
                Logger.d(TAG, "ALARM_RELATED IOnSeekBarChangeListener hour:${hour}, minute:$minute")

                val numberOfDots = binding.editLightTimeView.getNumberOfDots()
                val stepNumber = numberOfDots - position - 1
                val calculatePreviousTime = alarmsViewModel.calculatePreviousTime(formattedTime, stepNumber * timeStep)
                Logger.d(TAG, "ALARM_RELATED IOnSeekBarChangeListener calculatePreviousTime:${calculatePreviousTime}")
                val split = calculatePreviousTime.split(":")
                binding.editLightTimeView.setLightStartTime(split[0].toInt(), split[1].toInt())
                currentAlarm?.let { itAlarm ->
                    itAlarm.lightStartAt = AlarmInfo.Timee(hour = split[0].toInt(), minute = split[1].toInt(), second = 0)
                }
            }

        })

        binding.alarmVolumeSeekbar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentAlarm?.let { itAlarm ->
                        itAlarm.volume = progress
                        binding.tvVolumeValue.text = progress.toString()
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                seekBar?.let { itSeekBar ->
                    currentAlarm?.let { itAlarm ->
                        itAlarm.volume = itSeekBar.progress
                        binding.tvVolumeValue.text = itSeekBar.progress.toString()
                        /*val sound = if (alarmsViewModel.currentAlarmLiveData.value != null) {
                            alarmsViewModel.currentAlarmLiveData.value?.wakeupSound ?: return@let
                        } else {
                            itAlarm.wakeupSound ?: return@let
                        }
                        alarmsViewModel.previewVolume(sound = sound, volume = itSeekBar.progress)*/
                        alarmsViewModel.previewVolume(sound = itAlarm.wakeupSound, volume = itSeekBar.progress)
                        Logger.d(TAG, "ALARM_RELATED currentAlarm previewVolume onStopTrackingTouch:$itAlarm")
                    }

                }
            }
        })
    }

    fun updateUI() {
        binding.alarmPicker.apply {
            val calendar = Calendar.getInstance()
            val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
            val currentMinute = calendar.get(Calendar.MINUTE)
            fromPage?.let {
                when (it) {
                    ADD_ALARM -> {
                        selectedHour = 0
                        selectedMinute = 0
                    }

                    EDIT_ALARM -> {
                        currentAlarm?.let { itAlarm ->
                            itAlarm.alarmTime?.let { itAlarmTime ->
                                Logger.d(TAG, "ALARM_RELATED curHour:${currentHour}, curMinute:$currentMinute, hour:${itAlarmTime.hour}, minute:${itAlarmTime.minute}")
                                selectedHour = itAlarmTime.hour
                                selectedMinute = itAlarmTime.minute
                            }
                        }
                    }

                    else -> {
                        selectedHour = 0
                        selectedMinute = 0
                    }
                }
            }
            setAtmospheric(true)
            setCurtain(true)
            curtainColor = Color.TRANSPARENT
            setHourFrame(0, 23)
            setMinuteFrame(0, 59)
//            setTimeFormatFrame("AM", "PM")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            val color = ActivityCompat.getColor(applicationContext, R.color.fg_activate)
            Logger.d(TAG, "AlarmPicker selectedItemTextColor1:${color}")
            selectedItemTextColor = color

            setOnTimeSelectedListener { picker, alarmTime ->
                val currentPosition = binding.editLightTimeView.getCurrentPosition()
                val numberOfDots = binding.editLightTimeView.getNumberOfDots()
                val stepNumber = numberOfDots - currentPosition - 1
                Logger.d(TAG, "AlarmPicker setOnTimeSelectedListener alarmTime:${alarmTime}")
                if (alarmTime.contains(":")) {
                    val parts = alarmTime.split(":")
                    val hour = parts[0].toInt()
                    val minute = parts[1].toInt()
                    binding.editLightTimeView.setLightTime(hour, minute)

                    val calculatePreviousTime = alarmsViewModel.calculatePreviousTime(alarmTime, stepNumber * timeStep)
                    val split = calculatePreviousTime.split(":")
                    binding.editLightTimeView.setLightStartTime(split[0].toInt(), split[1].toInt())

                    currentAlarm?.let { itAlarm ->
                        val lightStartAtTime = AlarmInfo.Timee(hour = split[0].toInt(), minute = split[1].toInt(), second = 0)
                        itAlarm.lightStartAt = lightStartAtTime
                    }
                }

            }
        }

        currentAlarm?.let { itAlarm ->
            itAlarm.repeatType?.let { itRepeatType ->
                when (itRepeatType.code) {
                    AlarmInfo.RepeatType.Weekday.code -> {
                        binding.tvRepeatDay.text = SkinResourcesUtils.getString("weekday")
                    }

                    AlarmInfo.RepeatType.Weekend.code -> {
                        binding.tvRepeatDay.text = SkinResourcesUtils.getString("weekend")
                    }

                    AlarmInfo.RepeatType.EveryDay.code -> {
                        binding.tvRepeatDay.text = SkinResourcesUtils.getString("everyday")
                    }

                    AlarmInfo.RepeatType.Once.code -> {
                        binding.tvRepeatDay.text = SkinResourcesUtils.getString("once")
                    }

                    AlarmInfo.RepeatType.Custom.code -> {
                        binding.tvRepeatDay.text = SkinResourcesUtils.getString("customize")
                    }

                    else -> {}
                }
            }

            itAlarm.wakeupSound?.let { itWakeupSound ->
                Logger.d(TAG, "ALARM_RELATED wakeupSound:${itWakeupSound}")

                when (itWakeupSound.type) {
                    AlarmInfo.SoundType.PresetRadioStation -> {
//                        radioViewModel.presetStationList.elementAtOrNull(itWakeupSound.index - 1)?.let { presetStation ->
//                            presetStation.station?.let { itStation ->
//                                val formatText = String.format(SkinResourcesUtils.getString("preset_x"), itWakeupSound.index)
//                                binding.tvSoundType.text = formatText
//                            }
//
//                        }
                    }

                    AlarmInfo.SoundType.RingTone -> {
                        val ringToneByIndex = getRingToneByIndex(itWakeupSound.index)
                        binding.tvSoundType.text = ringToneByIndex?.desc
                    }

                    else -> {}
                }
            }

            itAlarm.volume?.let { itVolume ->
                binding.tvVolumeValue.text = itVolume.toString()
                binding.alarmVolumeSeekbar.progress = itVolume
            }

            binding.lightSwitch.isChecked = itAlarm.isLightActive == true
            binding.editLightTimeView.isEnabled = itAlarm.isLightActive == true
            binding.editLightTimeView.canTouched = itAlarm.isLightActive == true
            binding.editLightTimeView.alpha = if (itAlarm.isLightActive == true) 1.0f else 0.5f

            binding.lightSwitch.setOnCheckedChangeListener { view, isChecked ->
                if (!view.isPressed) return@setOnCheckedChangeListener
                itAlarm.isLightActive = isChecked
                itAlarm.isSunRise = isChecked
                binding.editLightTimeView.canTouched = isChecked
                binding.editLightTimeView.alpha = if (isChecked) 1.0f else 0.5f
                val currentHour = binding.alarmPicker.currentHour
                val currentMinute = binding.alarmPicker.currentMinute
                if (isChecked) {
                    val lightStartTimePair = binding.editLightTimeView.getLightStartTime()
                    val lightStartAtTime = AlarmInfo.Timee(hour = lightStartTimePair.first, minute = lightStartTimePair.second, second = 0)
                    itAlarm.lightStartAt = lightStartAtTime
                } else {
                    itAlarm.lightStartAt = null
                }
                Logger.d(TAG, "ALARM_RELATED lightSwitch OnCheckedChangeListener itAlarm:${itAlarm}")
            }

            val hour = String.format("%02d", binding.alarmPicker.selectedHour)
            val minute = String.format("%02d", binding.alarmPicker.selectedMinute)
            val formattedTime = "$hour:$minute"
            val numberOfDots = binding.editLightTimeView.getNumberOfDots()

            Logger.d(TAG, "ALARM_RELATED IOnSeekBarChangeListener hour:${hour}, minute:$minute")
            binding.editLightTimeView.setLightTime(binding.alarmPicker.selectedHour, binding.alarmPicker.selectedMinute)

            fromPage?.let {
                if (it == EDIT_ALARM) {
                    val formattedStartHour = String.format("%02d", itAlarm.lightStartAt?.hour)
                    val formattedStartMinute = String.format("%02d", itAlarm.lightStartAt?.minute)
                    val formattedStartAtTime = "${formattedStartHour}:${formattedStartMinute}"

                    val formattedHour = String.format("%02d", itAlarm.alarmTime?.hour)
                    val formattedMinute = String.format("%02d", itAlarm.alarmTime?.minute)
                    val formattedAlarmTime = "${formattedHour}:${formattedMinute}"

                    val calculateTimeDuration = alarmsViewModel.calculateTimeDuration(formattedStartAtTime, formattedAlarmTime)
                    var timeGapNumber = (calculateTimeDuration.toMinutes() / timeStep).toInt()
                    if (calculateTimeDuration.toMinutes() > 60) {
                        timeGapNumber = 3
                    }
                    binding.editLightTimeView.setCurrentPosition(numberOfDots - timeGapNumber - 1)

                    Logger.d(TAG, "ALARM_RELATED calculateTimeDuration formattedStartAtTime:${formattedStartAtTime}")
                    Logger.d(TAG, "ALARM_RELATED calculateTimeDuration formattedAlarmTime:${formattedAlarmTime}")
                    Logger.d(TAG, "ALARM_RELATED calculateTimeDuration formattedTime:${formattedTime}")
                    Logger.d(TAG, "ALARM_RELATED calculateTimeDuration toMinutes:${calculateTimeDuration.toMinutes()}")
                    Logger.d(TAG, "ALARM_RELATED calculateTimeDuration timeGapNumber:${timeGapNumber}")
                    Logger.d(TAG, "ALARM_RELATED calculateTimeDuration currentPosition:${binding.editLightTimeView.getCurrentPosition()}")

                }

            }

            val currentPosition = binding.editLightTimeView.getCurrentPosition()
            val stepNumber = numberOfDots - currentPosition - 1
            val calculatePreviousTime = alarmsViewModel.calculatePreviousTime(formattedTime, stepNumber * timeStep)
            val split = calculatePreviousTime.split(":")
            binding.editLightTimeView.setLightStartTime(split[0].toInt(), split[1].toInt())
        }
    }

    fun initObserve() {
        alarmsViewModel.currentAlarmLiveData.observe(this) { alarm ->
            alarm?.let { itAlarm ->
                Logger.d(TAG, "ALARM_RELATED EDIT_ALARM currentAlarmLiveData pre:${itAlarm}")
                itAlarm.repeatType?.let { itRepeatType ->
                    when (itRepeatType.code) {
                        AlarmInfo.RepeatType.Weekday.code -> {
                            binding.tvRepeatDay.text = SkinResourcesUtils.getString("weekday")
                        }

                        AlarmInfo.RepeatType.Weekend.code -> {
                            binding.tvRepeatDay.text = SkinResourcesUtils.getString("weekend")
                        }

                        AlarmInfo.RepeatType.EveryDay.code -> {
                            binding.tvRepeatDay.text = SkinResourcesUtils.getString("everyday")
                        }

                        AlarmInfo.RepeatType.Once.code -> {
                            binding.tvRepeatDay.text = SkinResourcesUtils.getString("once")
                        }

                        AlarmInfo.RepeatType.Custom.code -> {
                            if (itRepeatType.value == "0000000") {
                                binding.tvRepeatDay.text = SkinResourcesUtils.getString("once")
                                itAlarm.repeatType = AlarmInfo.RepeatType.Once
                            } else {
                                binding.tvRepeatDay.text = SkinResourcesUtils.getString("customize")
                            }

                        }

                        else -> {}
                    }
                }

                itAlarm.wakeupSound?.let { itWakeupSound ->
                    when (itWakeupSound.type) {
                        AlarmInfo.SoundType.PresetRadioStation -> {
//                            radioViewModel.presetStationList.elementAtOrNull(itWakeupSound.index - 1)?.let { presetStation ->
//                                presetStation.station?.let { itStation ->
//                                    val formatText = String.format(SkinResourcesUtils.getString("preset_x"), itWakeupSound.index)
//                                    binding.tvSoundType.text = formatText
//                                }
//
//                            }
                        }

                        AlarmInfo.SoundType.RingTone -> {
                            val ringToneByIndex = getRingToneByIndex(itWakeupSound.index)
                            binding.tvSoundType.text = ringToneByIndex?.desc
                        }

                        else -> {}
                    }
                }

                itAlarm.volume?.let { itVolume ->
                    binding.tvVolumeValue.text = itVolume.toString()
                    binding.alarmVolumeSeekbar.progress = itVolume
                }

                if (itAlarm.isLightActive == true) {
                    if (itAlarm.lightStartAt == null) {
                        itAlarm.lightStartAt = AlarmInfo.Timee(hour = 8, minute = 0, second = 0)
                    }
                }

                currentAlarm = itAlarm

                Logger.d(TAG, "ALARM_RELATED EDIT_ALARM after currentAlarm:${currentAlarm}")
                Logger.i(TAG, "ALARM_RELATED EDIT_ALARM after currentAlarmLiveData:${itAlarm}")

            }
        }

        alarmsViewModel.updateAlarmLiveData.observe(this) {
            Logger.d(TAG, "ALARM_RELATED updateAlarmLiveData:${it}")
            if (it.equals(true)) {
                onBackKeyDown()
            }
        }

        alarmsViewModel.createAlarmLiveData.observe(this) {
            Logger.d(TAG, "ALARM_RELATED createAlarmLiveData:${it}")
            if (it.equals(true)) {
                onBackKeyDown()
            }
        }

        alarmsViewModel.removeAlarmLiveData.observe(this) {
            Logger.d(TAG, "ALARM_RELATED removeAlarmLiveData:${it}")
            if (it.equals(true)) {
                onBackKeyDown()
            }
        }
    }

    private fun getRingToneByIndex(index: Int): AlarmInfo.RingTone? {
        return AlarmInfo.RingTone.values().find { it.code == index }
    }

    override fun onClick(v: View?) {
        v?.let { itView ->
            Logger.d(TAG, "ALARM_RELATED onClick id = ${resources.getResourceEntryName(v.id)}")
            when (itView.id) {
                R.id.iv_back -> {
                    onBackKeyDown()
                }

                R.id.tv_save -> {
                    val currentHour = binding.alarmPicker.currentHour
                    val currentMinute = binding.alarmPicker.currentMinute
                    currentAlarm?.let { itAlarm ->
                        itAlarm.alarmTime?.let { itAlarmTime ->
                            itAlarmTime.hour = currentHour
                            itAlarmTime.minute = currentMinute
                        }
                        fromPage?.let {
                            when (it) {
                                ADD_ALARM -> {
                                    itAlarm.isActiveAlarm = true
                                    alarmsViewModel.createAlarm(itAlarm)
                                }

                                EDIT_ALARM -> {
                                    alarmsViewModel.updateAlarm(itAlarm)
                                }
                            }
                        }
                        /*GlobalScope.launch(Dispatchers.IO) {
                            delay(300)
                            alarmsViewModel.getAlarmInfoList()
                        }*/
                    }
                    Logger.d(TAG, "ALARM_RELATED AlarmPicker currentHour:${currentHour}, currentMinute:${currentMinute}")
                }

                R.id.iv_light_info_tip -> {
                    val dialogFragment = AlarmsLightTipDialogFragment()
                    dialogFragment.show(supportFragmentManager)
                }
//
                R.id.tv_repeat, R.id.tv_repeat_day, R.id.iv_repeat_more_arrow -> {
                    BTHorizonAlarmsRepeatTypeActivity.portal(this@BTHorizonAddEditAlarmsActivity, mainDev!!, currentAlarm, REQUEST_CODE_REPEAT)
                }

                R.id.tv_wake_up_sound, R.id.tv_sound_type, R.id.iv_sound_more_arrow -> {
                    BTHorizonAlarmsWakeUpSoundActivity.portal(this@BTHorizonAddEditAlarmsActivity, mainDev!!, currentAlarm, REQUEST_CODE_SOUND)
                }

                R.id.btn_delete_alarm -> {
                    currentAlarm?.let { itAlarm ->
                        alarmsViewModel.removeAlarm(itAlarm)
                        GlobalScope.launch(Dispatchers.IO) {
                            delay(300)
                            alarmsViewModel.getAlarmInfoList()
                        }
                    }
                }

                else -> {}
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_REPEAT) {
            if (resultCode == Activity.RESULT_OK && data?.hasExtra("repeatType") == true) {
                val repeatType = data.getSerializableExtra("repeatType") as AlarmInfo.RepeatType
                currentAlarm?.repeatType = repeatType
                alarmsViewModel.updateCurrentAlarm(currentAlarm)
            }
        } else if (requestCode == REQUEST_CODE_SOUND) {
            if (resultCode == Activity.RESULT_OK && data?.hasExtra("wakeupSound") == true) {
                val wakeupSound = data.getSerializableExtra("wakeupSound") as AlarmInfo.WakeupSound
                currentAlarm?.wakeupSound = wakeupSound
                alarmsViewModel.updateCurrentAlarm(currentAlarm)
            }
        }
    }

    fun onBackKeyDown() {
        currentAlarm?.let { itAlarm ->
            /*val sound = if (alarmsViewModel.currentAlarmLiveData.value != null) {
                alarmsViewModel.currentAlarmLiveData.value?.wakeupSound ?: return@let
            } else {
                itAlarm.wakeupSound ?: return@let
            }*/
            itAlarm.wakeupSound?.let { itWakeupSound ->
                alarmsViewModel.stopPlayDemoWithSound(sound = itWakeupSound, volume = itAlarm.volume)
            }
            Logger.d(TAG, "ALARM_RELATED currentAlarm stopPlayDemoWithSound:${currentAlarm}")
        }
        GlobalScope.launch(Dispatchers.IO) {
            delay(300)
            alarmsViewModel.getAlarmInfoList()
        }
        alarmsViewModel.customValue = BTHorizonAlarmsViewModel.CUSTOM_INITIAL_VALUE
        onBackPressed()
    }

    companion object {
        val TAG: String = ActivityHorizonAlarmsAddEditBinding::class.java.simpleName
        private const val ALARM_FROM = "fromPage"
        const val ADD_ALARM = "add_alarm"
        const val EDIT_ALARM = "edit_alarm"
        private const val REQUEST_CODE_REPEAT = 1
        private const val REQUEST_CODE_SOUND = 2
        private const val EXTRA_REQUEST_CODE = "extra_request_code"
        var currentAlarm: AlarmInfo.Alarm? = null
        fun portal(
            activity: Activity,
            device: Device,
            fromPage: String,
            alarm: AlarmInfo.Alarm? = null
        ) {
            device.pid?.also {
                val intent: Intent = Intent(activity, BTHorizonAddEditAlarmsActivity::class.java)
                intent.putExtra("UUID", device.UUID)
                intent.putExtra(ALARM_FROM, fromPage)
                intent.putExtra("alarm", alarm)
                Logger.d(TAG, "startActivity")
                activity.startActivity(intent)
            }
        }
    }
}