package com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.databinding.ActivityHorizonAlarmsWakeUpSoundBinding
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.discover.bean.Device
import com.harman.log.Logger
import com.harman.parseAsPartyBoxDevice
import com.harman.utils.Utils
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.WrapContentLinearLayoutManager
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.adapter.AlarmsRadioAdapter
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.adapter.AlarmsRingToneAdapter
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.bean.PresetStation
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.viewmodel.BTHorizonAlarmsViewModel
import config.LogTags

class BTHorizonAlarmsWakeUpSoundActivity : AppCompatActivity() {
    private val binding by lazy { ActivityHorizonAlarmsWakeUpSoundBinding.inflate(layoutInflater) }

    private val alarmsViewModel by viewModels<BTHorizonAlarmsViewModel>()
    private var mAlarmsRingToneAdapter: AlarmsRingToneAdapter? = null
    private var mAlarmsRadioAdapter: AlarmsRadioAdapter? = null
    private val mainDev by lazy {
        parseAsPartyBoxDevice()
    }
    private val currentAlarm by lazy {
        intent.getSerializableExtra("alarm") as? AlarmInfo.Alarm
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        if (null == mainDev) {

            finish()
            return
        }
        alarmsViewModel.mainDev = mainDev!!
        lifecycle.addObserver(alarmsViewModel)
        setContentView(binding.root)
        initView()
    }

    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) super.attachBaseContext(newBase) else {
            val baseContext = LocaleLanConfigUtil.changeAppLan(newBase)
            WAApplication.me.mAppLanguageContext = baseContext
            super.attachBaseContext(baseContext)
        }
    }

    override fun onBackPressed() {
        onBackKeyDown()
        super.onBackPressed()
    }

    private fun initView() {
        binding.appbar.ivLeading.setOnClickListener { onBackPressed() }
        binding.appbar.tvTitle.text = SkinResourcesUtils.getString("sound")

        updateWakeUpSound()
    }

    private fun updateWakeUpSound() {
        val itActivity = this@BTHorizonAlarmsWakeUpSoundActivity
        val ringToneManager = WrapContentLinearLayoutManager(itActivity, RecyclerView.VERTICAL, false)
        val radioManager = WrapContentLinearLayoutManager(itActivity, RecyclerView.VERTICAL, false)
        binding.ringToneRecycleView.layoutManager = ringToneManager
        binding.radioRecycleView.layoutManager = radioManager
        (binding.ringToneRecycleView.itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false
        (binding.radioRecycleView.itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false

        val ringToneList = alarmsViewModel.ringToneList
//        val radioList = radioViewModel.presetStationList
        currentAlarm?.let { itAlarm ->
            mAlarmsRingToneAdapter = AlarmsRingToneAdapter(itActivity, ringToneList, itAlarm)
//            mAlarmsRadioAdapter = AlarmsRadioAdapter(itActivity, radioList)
            binding.ringToneRecycleView.adapter = mAlarmsRingToneAdapter
//            binding.radioRecycleView.adapter = mAlarmsRadioAdapter

            itAlarm.wakeupSound?.let { itWakeupSound ->
                when (itWakeupSound.type) {
                    AlarmInfo.SoundType.RingTone -> {
                        val ringToneByIndex = alarmsViewModel.getRingToneByIndex(itWakeupSound.index)
                        mAlarmsRingToneAdapter?.updateSelectedPosition(ringToneByIndex)
                        mAlarmsRadioAdapter?.resetSelectedPosition()
                    }

                    AlarmInfo.SoundType.PresetRadioStation -> {
//                        mAlarmsRadioAdapter?.updateSelectedPosition(getRadioByIndex(itWakeupSound))
                        mAlarmsRingToneAdapter?.resetSelectedPosition()
                    }

                    else -> {}
                }

            }

            Logger.d(TAG, "ALARM_RELATED before itemClick itAlarm:$itAlarm")
            mAlarmsRingToneAdapter?.setOnItemClickListener(object : AlarmsRingToneAdapter.OnItemClickListener {
                override fun onItemClick(view: View, position: Int, type: AlarmInfo.RingTone) {
                    Logger.d(TAG, "ALARM_RELATED AlarmsRingToneAdapter itemClick position:$position, type:$type")
                    val index = type.code
                    val wakeupSound = AlarmInfo.WakeupSound(AlarmInfo.SoundType.RingTone, index)
                    val tempAlarm = itAlarm.deepCopy()
                    tempAlarm.wakeupSound = wakeupSound
                    alarmsViewModel.updateCurrentAlarm(tempAlarm)
                    alarmsViewModel.playDemoWithSound(sound = wakeupSound, volume = itAlarm.volume)
                    Logger.i(TAG, "ALARM_RELATED after itemClick itAlarm:$itAlarm")
                    Logger.d(TAG, "ALARM_RELATED after itemClick tempAlarm:$tempAlarm")
                    mAlarmsRadioAdapter?.resetSelectedPosition()
                }
            })

            mAlarmsRadioAdapter?.setOnItemClickListener(object : AlarmsRadioAdapter.OnItemClickListener {
                override fun onItemClick(view: View, position: Int, presetStation: PresetStation?) {
                    Logger.d(TAG, "ALARM_RELATED AlarmsRadioAdapter itemClick position:$position, presetStation:$presetStation")
                    presetStation?.let { itPresetStation ->
                        itPresetStation.station?.let { itStation ->
                            val wakeupSound = AlarmInfo.WakeupSound(AlarmInfo.SoundType.PresetRadioStation, itStation.stationIndex ?: 1)
                            val tempAlarm = itAlarm.deepCopy()
                            tempAlarm.wakeupSound = wakeupSound
                            alarmsViewModel.updateCurrentAlarm(tempAlarm)
                            alarmsViewModel.playDemoWithSound(sound = wakeupSound, volume = itAlarm.volume)
                            Logger.d(TAG, "ALARM_RELATED AlarmsRadioAdapter itemClick itAlarm:$itAlarm")
                        }
                    }
                    mAlarmsRingToneAdapter?.resetSelectedPosition()
                }

            })
        }
    }

//    private fun getRadioByIndex(wakeupSound: AlarmInfo.WakeupSound): PresetStation? {
//        return wakeupSound.type.takeIf { it == AlarmInfo.SoundType.PresetRadioStation }?.let {
//            radioViewModel.presetStationList.elementAtOrNull(wakeupSound.index - 1)
//        }
//    }

    fun onBackKeyDown() {
        alarmsViewModel.currentAlarmLiveData.value?.let {
            val sound = it.wakeupSound ?: return@let
            val intent = Intent()
            intent.putExtra("wakeupSound", sound)
            setResult(Activity.RESULT_OK, intent)
            alarmsViewModel.stopPlayDemoWithSound(sound = sound, volume = it.volume)
        }
    }

    companion object {
        val TAG: String = BTHorizonAlarmsWakeUpSoundActivity::class.java.simpleName

        fun portal(
            activity: Activity,
            device: Device,
            alarm: AlarmInfo.Alarm?,
            requestCode: Int
        ) {
            device.pid?.also {
                val intent: Intent = Intent(activity, BTHorizonAlarmsWakeUpSoundActivity::class.java)
                intent.putExtra("UUID", device.UUID)
                intent.putExtra("alarm", alarm)
                Logger.d(TAG, "startActivity")
                activity.startActivityForResult(intent, requestCode)
            }
        }
    }
}