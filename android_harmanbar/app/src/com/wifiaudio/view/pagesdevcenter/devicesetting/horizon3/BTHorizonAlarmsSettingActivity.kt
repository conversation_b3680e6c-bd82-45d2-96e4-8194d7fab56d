package com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.harman.bar.app.databinding.ActivityHorizonAlarmsSettingBinding
import com.harman.discover.bean.Device
import com.harman.log.Logger
import com.harman.parseAsPartyBoxDevice
import com.harman.utils.Utils
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.viewmodel.BTHorizonAlarmsViewModel

class BTHorizonAlarmsSettingActivity : AppCompatActivity() {
    private val binding by lazy { ActivityHorizonAlarmsSettingBinding.inflate(layoutInflater) }

    private val alarmsViewModel by viewModels<BTHorizonAlarmsViewModel>()

    private val mainDev by lazy {
        parseAsPartyBoxDevice()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
        if (null == mainDev) {

            finish()
            return
        }
        alarmsViewModel.mainDev = mainDev!!
        lifecycle.addObserver(alarmsViewModel)
        setContentView(binding.root)
        initView()
    }

    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) super.attachBaseContext(newBase) else {
            val baseContext = LocaleLanConfigUtil.changeAppLan(newBase)
            WAApplication.me.mAppLanguageContext = baseContext
            super.attachBaseContext(baseContext)
        }
    }

    private fun initView() {
        binding.appbar.ivLeading.setOnClickListener { onBackPressed() }
        binding.appbar.tvTitle.text = SkinResourcesUtils.getString("alarm_setting")

        binding.tvSnooze.text = SkinResourcesUtils.getString("snooze")
        alarmsViewModel.getSnoozeStatus()
        alarmsViewModel.snoozeStatusLiveData.observe(this) { isActive ->
            Logger.d(TAG, "ALARM_RELATED snoozeStatusLiveData :$isActive")
            binding.snoozeSwitch.isChecked = isActive
            if (isActive) {
                binding.tvSnoozeDesc.text = SkinResourcesUtils.getString("short_press_the_product_knob_will_trigger_something")
            } else {
                binding.tvSnoozeDesc.text = SkinResourcesUtils.getString("Short_press_the_product_knob_will_stop_the_alarm")
            }
        }

        binding.snoozeSwitch.setOnClickListener {
            if (binding.snoozeSwitch.isChecked) {
                binding.tvSnoozeDesc.text = SkinResourcesUtils.getString("short_press_the_product_knob_will_trigger_something")
            } else {
                binding.tvSnoozeDesc.text = SkinResourcesUtils.getString("Short_press_the_product_knob_will_stop_the_alarm")
            }
            alarmsViewModel.setSnoozeStatus(status = binding.snoozeSwitch.isChecked)
        }
    }

    companion object {
        val TAG: String = BTHorizonAlarmsSettingActivity::class.java.simpleName

        fun portal(
            activity: Activity,
            device: Device
        ) {
            device.pid?.also {
                val intent: Intent = Intent(activity, BTHorizonAlarmsSettingActivity::class.java)
                intent.putExtra("UUID", device.UUID)
                Logger.d(TAG, "startActivity")
                activity.startActivity(intent)
            }
        }
    }
}