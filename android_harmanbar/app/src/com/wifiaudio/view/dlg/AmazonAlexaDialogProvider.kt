package com.wifiaudio.view.dlg

import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.harman.bar.app.R
import com.harman.bean.AuthDevice
import com.harman.utils.Utils
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import com.wifiaudio.view.pagesmsccontent.menu_settings.AlexaLoginWebViewFragment

class AmazonAlexaDialogProvider(activity: FragmentActivity) {
    var frameId: Int?=0
    var authDevice: AuthDevice? = null
    var activity: FragmentActivity? = null
    var extDlg: ExtendDialogNew? = null
    var handler = Handler(Looper.getMainLooper())
    init {
        this.activity = activity
    }

    fun showAlexaLoginDialog(callback: Callback?){
        if(activity==null) return
        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                    .build(R.layout.dlg_alexa_status)
                    .setCancelable(true)
                    .setCanceledOnTouchOutside(false)
                    .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                        override fun updateView(rootView: View?) {
                            updateDlgViews(rootView,callback)
//                            handler.postDelayed({ rootView?.setBackgroundColor(ContextCompat.getColor(activity!!, R.color.bg_overlay)) }, 260)
                        }

                        override fun onDismissDlg() {
                            dismissDlg()
                        }

                        override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                            dismissDlg()
                        }
                    })
        }

        if (extDlg?.isShowing == false) extDlg?.show()
    }
    private fun updateDlgViews(rootView: View?,callback: Callback?) {

        var containerView = rootView?.findViewById<View>(R.id.container)
        containerView?.setBackgroundResource(R.drawable.shape_chromecast_bg_feature_bluetooth)

        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Amazon_Alexa")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                dismissDlg()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {

            text = SkinResourcesUtils.getString("jbl_Login_your_Alexa_account_to_start_using_Alexa_MRM_")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            alpha = 0.85f
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        }

        var ivWarn: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivWarn?.apply {
            setImageDrawable(SkinResourcesUtils.getDrawable("jbl_one_amazon_alexa_logo"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {
            text = SkinResourcesUtils.getString("jbl_LOGIN_ALEXA")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            setOnClickListener {

                dismissDlg()

                if (authDevice == null || authDevice?.wifiDevice == null) return@setOnClickListener



                var vfrag = AlexaLoginWebViewFragment()
                val dataInfo = DataFragInfo()
                dataInfo.deviceItem = authDevice?.wifiDevice
                dataInfo.frameId = frameId!!
                vfrag.dataInfo = dataInfo
                dataInfo?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
            }
        }

    }



    private fun dismissDlg() {

        handler.post {
            extDlg?.rootView?.setBackgroundColor(Color.TRANSPARENT)
            activity?.apply { Utils.decorateHarmanWindow(activity) }
        }
        handler.postDelayed({
            extDlg?.apply {
                dismissDlg()
                extDlg = null
            }
        }, 100)
    }

     interface Callback{
        fun onConfirm()
    }
}