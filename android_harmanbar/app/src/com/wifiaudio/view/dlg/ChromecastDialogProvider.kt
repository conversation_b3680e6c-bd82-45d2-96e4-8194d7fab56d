package com.wifiaudio.view.dlg

import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Html
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.harman.bar.app.R
import com.harman.bean.AuthDevice
import com.skin.SkinResourcesID
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.*
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLC4aPermissionStatus
import config.GlobalUIConfig
import config.LogTags

class ChromecastDialogProvider(activity: FragmentActivity) {
    var authDevice: AuthDevice? = null
    var activity: FragmentActivity? = null
    var extDlg: ExtendDialogNew? = null


    init {
        this.activity = activity
    }

    fun showEnableServiceDialog(callback: Callback) {
        if (activity == null) return
        if (extDlg == null) {
            LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate, showEnableServiceDialog")
            extDlg = ExtendDialogNew(activity).build(R.layout.dialog_oobe_chromecast_builtin)
                    .setCancelable(true).setCanceledOnTouchOutside(false)
                    .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                        override fun updateView(rootView: View?) {
                            LogsUtil.i(LogTags.UI,  "${javaClass.simpleName} EnableServiceDialog updateView")
                            updateEnableServiceDialogViews(rootView, callback)
                        }

                        override fun onDismissDlg() {
                            LogsUtil.i(LogTags.UI,  "${javaClass.simpleName} EnableServiceDialog onDismissDlg")
                            dismissDlg()
                        }

                        override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                            LogsUtil.i(LogTags.UI,  "${javaClass.simpleName} EnableServiceDialog onKeyListener")
                            dismissDlg()
                        }
                    })
        }
        extDlg?.show()
    }

    private fun updateEnableServiceDialogViews(rootView: View?, callback: Callback?) {
        LogsUtil.d(LogTags.UI, "$TAG updateEnableServiceDialogViews")
        var containerView = rootView?.findViewById<View>(R.id.container)
        containerView?.setBackgroundResource(R.drawable.shape_chromecast_bg_l1)
        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Chromecast_Built_In_Available")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        rootView?.findViewById<View>(R.id.iv_title_image)?.visibility = View.VISIBLE

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            visibility = View.INVISIBLE
            setOnClickListener {
                dismissDlg()
                callback?.onCancel()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {
            text = SkinResourcesUtils.getString("jbl_stream_music_from_hundreds_of_apps_to_your_products_via_chromecast_built_in")
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            alpha = 0.8f
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        var tvInfo2: TextView? = rootView?.findViewById(R.id.tv_info2)
        tvInfo2?.apply {
            visibility = View.VISIBLE
            var strHighLabel1 = SkinResourcesUtils.getString("jbl___Google_Terms_Of_Service__")
            var strHighLabel2 = SkinResourcesUtils.getString("jbl___Privacy_Policy__")
            var strInfoLabel = SkinResourcesUtils.getString("jbl_Agree_with___Google_Terms_Of_Service___and___Privacy_Policy___to_start_using_Chromecast_built_in_")
            var stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setUnderlineText(true)
            var linkColor = WAApplication.me.getColor(R.color.fg_activate)
            stringUtil.setTextHighlight(arrayOf(strHighLabel1, strHighLabel2), linkColor)

            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            stringUtil.setSpannableStrings {

                if (it == 0) {
                    var html = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_terms_url")
                    if (TextUtils.isEmpty(html)) return@setSpannableStrings
                    val intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    val url = Uri.parse(html)
                    intent.data = url
                    activity?.startActivity(intent)

                } else if (it == 1) {
                    var html = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_privacy_url")
                    if (TextUtils.isEmpty(html)) return@setSpannableStrings
                    val intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    val url = Uri.parse(html)
                    intent.data = url
                    activity?.startActivity(intent)
                }
            }

            text = stringUtil.spannableString
            highlightColor = Color.TRANSPARENT
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            alpha = 0.8f
            movementMethod = LinkMovementMethod.getInstance()
        }

        val tvInfo3: TextView? = rootView?.findViewById(R.id.tv_info3)
        tvInfo3?.apply {
            visibility = View.VISIBLE
            val strHighLabel = SkinResourcesUtils.getString("text_here")
            val strInfoLabel = SkinResourcesUtils.getString("tap_here_to_enable_later")
            val linkColor = WAApplication.me.getColor(R.color.fg_activate)

            val stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setTextHighlight(strHighLabel, linkColor)
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))
            stringUtil.setSpannableString {
                LogsUtil.i(LogTags.UI,  "${javaClass.simpleName} EnableServiceDialog text here click")
                dismissDlg()
                callback?.onCancel()
            }
            text = stringUtil.spannableString
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            alpha = 0.8f
            movementMethod = LinkMovementMethod.getInstance()
        }

        var ivLogo: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivLogo?.apply {
            visibility = View.GONE
            setImageDrawable(SkinResourcesUtils.getDrawable("jbl_icon_stream_service"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        ViewUtil.setViewAlphaClickListener(btnOK)
        btnOK?.apply {

            text = SkinResourcesUtils.getString("jbl_ENABLE_SERVICE")

            /*var tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                "btn_background_bg_s1", GlobalUIConfig.color_btn_normal,
                "btn_background_bg_s1", GlobalUIConfig.color_btn_press
            )
            background = tintDrawable*/

            val tintDrawable: Drawable? = SkinResourcesUtils.getSingleTintDrawable(
                "btn_background_bg_s1",
                ContextCompat.getColor(WAApplication.me, R.color.bg_inverse)
            )
            background = tintDrawable
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_inverse))
            setOnClickListener {
                dismissDlg()
                enableDeviceC4Status(callback)
            }
        }
        var btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)
        ViewUtil.setViewAlphaClickListener(btnCancel)
        btnCancel?.apply {
            visibility = View.GONE
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            text = SkinResourcesUtils.getString("jbl_LATER")
            setOnClickListener {
                dismissDlg()
                callback?.onCancel()
            }
        }

    }

    private fun enableDeviceC4Status(callback: Callback?) {

        if (authDevice == null || authDevice?.wifiDevice == null) {
            LogsUtil.i(LogTags.Device, "$TAG enableDeviceC4Status: $authDevice")
            dismissDlg()
            callback?.onCancel()
        }

        DeviceCustomerSettingAction.makeDeviceSetC4aPermissionStatus(authDevice?.wifiDevice?.IP, "1", object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {

                LogsUtil.i(LogTags.Device, "$TAG setC4aPermissionStatus:Success: $content")

                val status = content?.let {
                    GsonParseUtil.instance.fromJson(it, JBLC4aPermissionStatus::class.java)
                }
                dismissDlg()
                //enable service success
                if (TextUtils.equals(status?.error_code, "0")) {
                    showGoogleImproveDialog(callback)
                } else {
                    callback?.onCancel()
                    //for test
//                    showGoogleImproveDialog(callback)
                }
            }

            override fun onFailure(e: Throwable?) {
                LogsUtil.i(LogTags.Device, "$TAG setC4aPermissionStatus:Failed: ${e?.localizedMessage}")
                dismissDlg()
                callback?.onCancel()
                //for test
//                showGoogleImproveDialog(callback)
            }
        })
    }



    fun showGoogleSetupDialog(callback: Callback?) {
        if (activity == null) return

        if (extDlg == null) {
            LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate, showGoogleSetupDialog")
            extDlg = ExtendDialogNew(activity)
                    .build(R.layout.dialog_enable_google_service)
                    .setCancelable(true)
                    .setCanceledOnTouchOutside(false)
                    .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                        override fun updateView(rootView: View?) {
                            updateGoogleSetupViews(rootView, callback)
                        }

                        override fun onDismissDlg() {
                            dismissDlg()
                        }

                        override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                            dismissDlg()
                        }
                    })
        }

        extDlg?.show()
    }

    private fun updateGoogleSetupViews(rootView: View?, callback: Callback?) {
        LogsUtil.d(LogTags.UI, "$TAG updateGoogleSetupViews")
        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("Enable_Google_Service_in_the_Google_Home_App")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                dismissDlg()
            }
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {

//            text = SkinResourcesUtils.getString("jbl_LOGIN_ALEXA")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

//            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            setOnClickListener {
                dismissDlg()
                callback?.onConfirm()
            }
        }
    }

    private fun showGoogleImproveDialog(callback: Callback?) {
        if (activity == null) return
        if (extDlg != null) dismissDlg()
        if (extDlg == null) {
            LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate, showGoogleImproveDialog")
            extDlg = ExtendDialogNew(activity).build(R.layout.dialog_oobe_chromecast_builtin)
                    .setCancelable(true).setCanceledOnTouchOutside(false)
                    .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                        override fun updateView(rootView: View?) {
                            updateImproveChromecastViews(rootView, callback)
                        }

                        override fun onDismissDlg() {
                            dismissDlg()
                        }

                        override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                            dismissDlg()
                        }
                    })
        }

        extDlg?.show()
    }

    private fun updateImproveChromecastViews(rootView: View?, callback: Callback?) {
        LogsUtil.d(LogTags.UI, "$TAG updateImproveChromecastViews")
        var containerView = rootView?.findViewById<View>(R.id.container)
        containerView?.layoutParams?.height = ScreenUtil.dip2px(activity, 500.0f)
        containerView?.setBackgroundResource(R.drawable.shape_chromecast_bg_l1)
        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Help_Improve_Chromecast")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

        }

        val ivBack: ImageView? = rootView?.findViewById(R.id.iv_back)
        ivBack?.apply {
            visibility = View.GONE
        }

        val ivTitleImage: ImageView? = rootView?.findViewById(R.id.iv_title_image)
        ivTitleImage?.apply {
            visibility = View.VISIBLE
            setImageDrawable(SkinResourcesUtils.getDrawable("svg_icon_chromecast_builtin"))
        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            visibility = View.GONE
            setOnClickListener {
                dismissDlg()
                callback?.onCancel()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {
            visibility = View.VISIBLE
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            var strHighLabel1 = SkinResourcesUtils.getString("jbl_Learn_more")
            var strBody = SkinResourcesUtils.getString("jbl_SDo_you_want_to_help_improve_everyone_s_experience_by_sharing_device_stats_and_crash_reports_with_Go")

            var strInfoLabel = String.format(strBody, "")
            var linkColor = WAApplication.me.getColor(R.color.fg_activate)
            var stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setUnderlineText(false)
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold))
            stringUtil.setTextHighlight(arrayOf(strHighLabel1), linkColor)

            stringUtil.setSpannableStrings {
                if (it == 0) {
                    var intent = Intent()
                    intent.action = Intent.ACTION_VIEW
                    var strUrl = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_learn_more_url")

                    if (TextUtils.isEmpty(strUrl)) return@setSpannableStrings

                    val url = Uri.parse(strUrl)
                    intent.data = url
                    activity?.startActivity(intent)
                }
            }

            var linkDrawId = SkinResourcesID.getDrawableId("jbl_icon_spotify_link")
            val picStr = " <img src='$linkDrawId'/> "
            text = stringUtil.spannableString

            append(Html.fromHtml(String.format("%s", picStr), MixTextImg.getImageGetterInstance(activity, linkColor), null))

            highlightColor = Color.TRANSPARENT
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            movementMethod = LinkMovementMethod.getInstance()
        }

        val tvInfo3: TextView? = rootView?.findViewById(R.id.tv_info3)
        tvInfo3?.apply {
            visibility = View.GONE
        }

        /*var ivWarn: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivWarn?.apply {
            visibility = View.VISIBLE
            setImageDrawable(SkinResourcesUtils.getDrawable("svg_icon_chromecast_builtin"))
        }*/

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {

            text = SkinResourcesUtils.getString("jbl_YES__I_M_IN")

            var tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                "btn_background_bg_s1", ContextCompat.getColor(WAApplication.me,R.color.bg_inverse),
                "btn_background_bg_s1", GlobalUIConfig.color_btn_press
            )
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

//            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            background = tintDrawable
            setOnClickListener {
                dismissDlg()
                enableImproveChromecast(callback)
            }
        }

        var btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)
        btnCancel?.apply {
            visibility = View.VISIBLE
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            text = SkinResourcesUtils.getString("jbl_NO_THANKS")
            background = null
            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                dismissDlg()
                callback?.onCancel()
            }
        }

    }

    private fun enableImproveChromecast(callback: Callback?) {
        if (authDevice == null || authDevice?.wifiDevice == null) {
            dismissDlg()
            callback?.onCancel()
            return
        }

        DeviceCustomerSettingAction.makeDeviceSetChromecastOptIn(authDevice?.wifiDevice?.IP, "true", object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {

                LogsUtil.i(LogTags.Device, "$TAG setChromecastOptIn:Success: $content")
                dismissDlg()
                showSetupReadyDialogView(callback)
            }

            override fun onFailure(e: Throwable?) {
                LogsUtil.i(LogTags.Device, "$TAG setChromecastOptIn:Failed: ${e?.localizedMessage}")
                dismissDlg()
                callback?.onCancel()
                //for test
//                showSetupReadyDialogView(callback)
            }
        })
    }

    fun showSetupReadyDialogView(callback: Callback?) {
        if (activity == null) return
        if (extDlg != null) dismissDlg()
        if (extDlg == null) {
            LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate, showSetupReadyDialogView")
            extDlg = ExtendDialogNew(activity).build(R.layout.dialog_oobe_chromecast_builtin)
                    .setCancelable(true).setCanceledOnTouchOutside(false)
                    .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                        override fun updateView(rootView: View?) {
                            updateSetupReadyDialogView(rootView, callback)
                        }

                        override fun onDismissDlg() {
                            dismissDlg()
                        }

                        override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                            dismissDlg()
                        }
                    })
        }

        extDlg?.show()
    }

    private fun updateSetupReadyDialogView(rootView: View?, callback: Callback?) {
        LogsUtil.d(LogTags.UI, "$TAG updateSetupReadyDialogView")
        var containerView = rootView?.findViewById<View>(R.id.container)
        containerView?.layoutParams?.height = ScreenUtil.dip2px(activity, 500.0f)
        containerView?.setBackgroundResource(R.drawable.shape_chromecast_bg_feature_bluetooth)
        rootView?.findViewById<View>(R.id.vheader)?.visibility = View.GONE

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            visibility = View.INVISIBLE
            setOnClickListener {
                dismissDlg()
                callback?.onCancel()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {
            visibility = View.VISIBLE
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            text = SkinResourcesUtils.getString("chromecast_setup_ready_desc")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fixed_white))
            movementMethod = LinkMovementMethod.getInstance()
        }

        var ivWarn: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivWarn?.apply {
            visibility = View.VISIBLE
            setImageDrawable(SkinResourcesUtils.getDrawable("icon_chromecast_setup_ready"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {
            text = SkinResourcesUtils.getString("jbl_DONE")
            var tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                "btn_background_bg_s1", ContextCompat.getColor(WAApplication.me,R.color.bg_inverse),
                "btn_background_bg_s1", GlobalUIConfig.color_btn_press
            )

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            background = tintDrawable
            setOnClickListener {
                dismissDlg()
                callback?.onConfirm()
            }
        }

        var btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)
        btnCancel?.apply {
            visibility = View.GONE
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

            text = SkinResourcesUtils.getString("jbl_NO_THANKS")
            background = null
            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                dismissDlg()
                callback?.onCancel()
            }
        }

    }


    interface Callback {
        fun onConfirm()
        fun onCancel()
    }

    private fun dismissDlg() {
        extDlg?.apply {
            /*activity?.let { itActivity ->
                BarUtils.setStatusBarColor(itActivity, WAApplication.me.getColor(R.color.bg_L1))
            }*/
            LogsUtil.i(LogTags.UI,  "${javaClass.simpleName} dialog dismiss")
            dismissDlg()
            extDlg = null
        }
    }

    companion object {
        val TAG: String = ChromecastDialogProvider::class.java.simpleName
    }

}