package com.wifiaudio.view.dlg

import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.*
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ResourceUtils
import com.harman.bar.app.R
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import config.GlobalUIConfig
import config.LogTags

class AlexaSetupExitDialog(fragmentActivity: FragmentActivity) : DevBaseDlgView(fragmentActivity) {

    init {
        init(R.layout.dlg_alexa_device_logout)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
    }


    override fun updateLayoutUI(rootView: View?) {
        super.updateLayoutUI(rootView)

        rootView?.findViewById<View>(R.id.iv_close)?.apply{
            visibility=View.GONE
        }
        rootView?.findViewById<View>(R.id.iv_back)?.apply{
            visibility=View.GONE
        }

        var tvTitle: TextView? = rootView?.findViewById(R.id.ble_title)
        tvTitle?.apply {
            gravity = Gravity.LEFT and  Gravity.CLIP_VERTICAL
            text = context.resources.getString(R.string.continue_setup)

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
        }
        var tvContent: TextView? = rootView?.findViewById(R.id.tv_content)
        tvContent?.apply {
            text = context.resources.getString(R.string.alexa_is_not_enabled_on_this_product)
        }


        var btn_restore: Button? = rootView?.findViewById(R.id.btn_restore)
        var btn_cancel: Button? = rootView?.findViewById(R.id.btn_cancel)

        var ivBack: ImageView? = rootView?.findViewById(R.id.iv_back)

        btn_restore?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
            text = context.resources.getString(R.string.jbl_continue)
//            val tintDrawable = SkinResourcesUtils.getTintListDrawable("btn_background_bg_s1",
//                    GlobalUIConfig.color_btn_normal, GlobalUIConfig.color_btn_press)
//            background = tintDrawable
            background = ResourceUtils.getDrawable(R.drawable.btn_background_bg_s1)
            setTextColor(ColorUtils.getColor(R.color.fg_inverse))
        }

        btn_cancel?.apply {
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
            text = context.resources.getString(R.string.harmanbar_jbl_EXIT)
        }

        ivBack?.apply {
            visibility = View.GONE
        }


        ViewUtil.setViewAlphaClickListener(btn_cancel)
        ViewUtil.setViewAlphaClickListener(btn_restore)
        btn_cancel?.setOnClickListener {
            dismissDialog()
            listener?.onCancel()
        }

        btn_restore?.setOnClickListener {

            dismissDialog()
            listener?.onConfirm(null)
        }
    }

    override fun updateDlgContainerBG(cview: View?) {
    }


}