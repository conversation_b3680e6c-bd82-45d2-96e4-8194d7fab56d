package com.wifiaudio.view.dlg

import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.*
import androidx.fragment.app.FragmentActivity
import com.harman.bar.app.R
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import config.GlobalUIConfig
import config.LogTags

class AlexaLogoutDialog(fragmentActivity: FragmentActivity) : DevBaseDlgView(fragmentActivity) {

    init {
        init(R.layout.dlg_alexa_device_logout)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
    }


    override fun updateLayoutUI(rootView: View?) {
        super.updateLayoutUI(rootView)

        rootView?.findViewById<View>(R.id.iv_close)?.apply {
            visibility = View.GONE
        }
        rootView?.findViewById<View>(R.id.iv_back)?.apply {
            visibility = View.GONE
        }

        var tvTitle: TextView? = rootView?.findViewById(R.id.ble_title)
        tvTitle?.apply {
            gravity = Gravity.LEFT and Gravity.CLIP_VERTICAL
            // title
            text = SkinResourcesUtils.getString("would_you_like_to_log_out")

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
            visibility = View.GONE
        }
        var tvContent: TextView? = rootView?.findViewById(R.id.tv_content)
        tvContent?.apply {
//            text = SkinResourcesUtils.getString("after_logging_out_alexa")
            text = String.format(WAApplication.me.getString(R.string.after_logging_out), WAApplication.me.getString(R.string.android_translated_amazon_alexa))

        }


        var btnRestore: Button? = rootView?.findViewById(R.id.btn_restore)
        var btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)

        var ivBack: ImageView? = rootView?.findViewById(R.id.iv_back)

        btnRestore?.apply {
//            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)
//            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
            text = SkinResourcesUtils.getString("log_out")
//            val tintDrawable = SkinResourcesUtils.getTintListDrawable("btn_background_bg_s1",
//                    GlobalUIConfig.color_btn_normal, GlobalUIConfig.color_btn_press)
//            background = tintDrawable
            ViewUtil.setViewAlphaClickListener(btnRestore)
            setOnClickListener {
                dismissDialog()
                listener?.onConfirm(null)
            }
        }

        btnCancel?.apply {
//            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)
//
//            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_14))
            text = SkinResourcesUtils.getString("jbl_CANCEL")
            ViewUtil.setViewAlphaClickListener(btnCancel)
            setOnClickListener {
                dismissDialog()
                listener?.onCancel()
            }
        }
    }

    override fun updateDlgContainerBG(cview: View?) {
    }


}