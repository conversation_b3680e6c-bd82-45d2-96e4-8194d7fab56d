package com.wifiaudio.view.pagesmsccontent.home;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.harman.BaseActivity;
import com.harman.BaseViewModelFactory;
import com.harman.CoulsonManager;
import com.harman.bar.app.R;
import com.harman.bean.AuthDevice;
import com.harman.va.VAGoogleActivity;
import com.harman.discover.DeviceStore;
import com.harman.discover.bean.OneDevice;
import com.harman.utils.Utils;
import com.linkplay.utils.StatusBarUtils;
import com.wifiaudio.model.DataFragInfo;

public class ChromecastAlexaComposeActivity extends BaseActivity<ComposeStatusViewModel> {


    public enum SourceType {
        TYPE_COMPOSE,
        TYPE_ALEXA,
        TYPE_GOOGLE
    }

    private SourceType sourceType;
    private AuthDevice authDevice = null;
    private OneDevice oneDevice = null;
    private DataFragInfo dataInfo = null;
    private boolean isOObe;


    private AlexaLoginStatus alexaLoginStatus = AlexaLoginStatus.NOT_LOGIN;

    @NonNull
    @Override
    protected ComposeStatusViewModel createViewModel() {
        return new ViewModelProvider(getViewModelStore(), BaseViewModelFactory.getInstance()).get(ComposeStatusViewModel.class);
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        StatusBarUtils.setFullScreen(this);
        Utils.decorateHarmanWindow(this);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chromecast_alexa_compose);
        String uuid = getIntent().getStringExtra("Bundle_UUID");
        oneDevice = DeviceStore.INSTANCE.findOne(uuid);

        sourceType = SourceType.valueOf(getIntent().getStringExtra("source_type"));
        isOObe = (Boolean) getIntent().getBooleanExtra("is_oobe", false);
        switchContent(sourceType);
    }

    public void alexaSwitch(int success) {
        if (sourceType == SourceType.TYPE_COMPOSE) {
            //alexa login success
            if (success == 1) {
                alexaLoginStatus = AlexaLoginStatus.LOGGED;
                switchContent(SourceType.TYPE_GOOGLE);
            } else if (success == -1) {
                //alexa setup block and to
                alexaLoginStatus = AlexaLoginStatus.CANCEL;
                switchContent(SourceType.TYPE_GOOGLE);
            } else {
                this.finish();
            }
        } else if (sourceType == SourceType.TYPE_ALEXA) {
            //block by
            this.finish();
        }
    }

    public void switchContent(SourceType sourceType) {
        Fragment targetFragment = null;
        String coulsonEventAction = null;
        String setupOption = null;
        switch (sourceType) {
            case TYPE_COMPOSE:
//                AmazonAlexaLoginStatusUtil.INSTANCE.clearAll();
//                ApplicationViewModel.getInstance().setCurrentAuthDevice(authDevice);
//                targetFragment = new AlexaVoiceAssistantFragment();
//                ((AlexaVoiceAssistantFragment) targetFragment).setEnterFrom(EnterFromType.TYPE_COMPOSE);
//                ((AlexaVoiceAssistantFragment) targetFragment).setAuthDevice(authDevice);
//                ((AlexaVoiceAssistantFragment) targetFragment).setDataInfo(dataInfo);
//                coulsonEventAction = EventUtils.gva_alexa_continue;
//                setupOption = EventUtils.gva_alexa;

                break;
            case TYPE_ALEXA:
//                AmazonAlexaLoginStatusUtil.INSTANCE.clearAll();
//                ApplicationViewModel.getInstance().setCurrentAuthDevice(authDevice);
//                targetFragment = new AlexaVoiceAssistantFragment();
//                ((AlexaVoiceAssistantFragment) targetFragment).setEnterFrom(EnterFromType.TYPE_COMPOSE);
//                ((AlexaVoiceAssistantFragment) targetFragment).setAuthDevice(authDevice);
//                ((AlexaVoiceAssistantFragment) targetFragment).setAlexaOnly(true);
//                ((AlexaVoiceAssistantFragment) targetFragment).setDataInfo(dataInfo);
//                coulsonEventAction = EventUtils.alexa_continue;
//                setupOption = EventUtils.alexa;
                break;

            case TYPE_GOOGLE:
//                targetFragment = new GoogleAssistantFragment();
//                ((GoogleAssistantFragment) targetFragment).setAuthDevice(authDevice);
//                ((GoogleAssistantFragment) targetFragment).setAlexLoginStatus(alexaLoginStatus);
//                ((GoogleAssistantFragment) targetFragment).setEnterFrom(EnterFromType.TYPE_COMPOSE);
//                ((GoogleAssistantFragment) targetFragment).setOObe(isOObe);
//                coulsonEventAction = EventUtils.gva_continue;
//                setupOption = EventUtils.gva;
                break;
        }
        getSupportFragmentManager().beginTransaction().replace(R.id.activity_container, targetFragment).commit();
    }

    public SourceType getSourceType() {
        return sourceType;
    }

    @Override
    public void onChanged(Object o) {

    }

    public enum AlexaLoginStatus {
        NOT_LOGIN, LOGGED, CANCEL
    }
}
