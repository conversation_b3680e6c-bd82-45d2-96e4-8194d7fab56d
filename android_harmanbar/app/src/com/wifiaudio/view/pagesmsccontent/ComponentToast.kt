package com.wifiaudio.view.pagesmsccontent

import android.view.Gravity
import android.view.LayoutInflater
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.view.updatePaddingRelative
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ComponentViewToastBinding
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible

/**
 * @Description Global toast component
 * <AUTHOR>
 * @Time 2024/11/11
 */
private var gToast: Toast? = null

/**
 * custom toast's container view
 */
private val gToastView by lazy {
    ComponentViewToastBinding.inflate(LayoutInflater.from(Utils.getApp()))
}

fun showToast(
    text: String,
    @ColorInt bgColor: Int = Utils.getApp().getColor(R.color.transparent),
    @DrawableRes leading: Int? = null,
    isLong: Boolean = false,
) {
    gToastView.llContainer.apply {
//        backgroundTintList = ColorStateList.valueOf(bgColor)
        updatePaddingRelative(
            start = if (null == leading) SizeUtils.dp2px(24f) else 0,
        )
    }
    gToastView.tvContent.apply {
        this.text = text
    }
    gToastView.ivLeading.apply {
        if (null == leading) {
            gone()
        } else {
            visible()
            setImageResource(leading)
        }
    }
    gToast?.cancel()
    gToast = Toast(Utils.getApp()).apply {
        setGravity(Gravity.BOTTOM or Gravity.FILL_HORIZONTAL, 0, SizeUtils.dp2px(84f))
        view = gToastView.root
        duration = if (isLong) Toast.LENGTH_LONG else Toast.LENGTH_SHORT
        show()
    }
}

fun showErrorToast(text: String) {
    showToast(text, leading = R.drawable.ic_info2)
}

fun showDebugToast(text: String) {
    if (BuildConfig.DEBUG) {
        ToastUtils.showLong(text)
    }
}