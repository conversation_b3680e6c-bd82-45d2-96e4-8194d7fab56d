package com.wifiaudio.view.pagesmsccontent.menu_settings.jblone

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.util.TypedValue
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.bean.AuthDevice
import com.harman.bean.CommonEvent
import com.harman.hkone.DeviceImageUtil
import com.harman.hkone.ScreenUtil
import com.harman.widget.CollapsingToolBar
import com.harman.widget.DotLoadingImageView
import com.rxjava.rxlife.lifeOnMain
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.ApplicationViewModel
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.utils.NavigationBarColorHelper
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.utils.cbl.CBLStatus
import com.wifiaudio.utils.cloudRequest.rxhttp.ApiRequest
import com.wifiaudio.view.dlg.ExtendDialogNew
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLDataUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.SpaceItemDecoration
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import com.wifiaudio.view.pagesmsccontent.home.ChromecastAlexaComposeActivity
import com.wifiaudio.view.pagesmsccontent.menu_settings.AlexaLoginWebViewFragment
import com.wifiaudio.view.pagesmsccontent.menu_settings.ContentSettingItem
import com.wifiaudio.view.pagesmsccontent.menu_settings.EnterFromType
import com.wifiaudio.view.pagesmsccontent.menu_settings.SETTING_ITEM_TYPE
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FragAmazonAlexaStatus : FragTabBackBase() {

    var authDevice: AuthDevice? = null
    var cview: View? = null
    var recyclerView: RecyclerView? = null
    var progressbar: DotLoadingImageView? = null
    var iv_logo: ImageView? = null

    var btnOpen: Button? = null
    var enterFrom: EnterFromType? = EnterFromType.TYPE_NONE //1:Stream Service 2: Manage Device 3: Language 4:Dashboard

    //var deviceItem: DeviceItem? = null
    var dataInfo: DataFragInfo? = null
    var mAdapter: AmazonAlexaStatusPageAdapter? = null

    var collapsingToolBar: CollapsingToolBar? = null
    private var currDetailList: ArrayList<ContentSettingItem> = ArrayList()//页面信息

    var masterDeviceList: ArrayList<DeviceItem> = ArrayList()

    private var vBack: Button? = null
    private var vTitle: TextView? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.frag_amazon_alexa_status, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()
        requestStatus()
        return cview
    }

    private fun requestStatus() {
        if (AmazonAlexaLoginStatusUtil.isEmptyLoginList() && AmazonAlexaLoginStatusUtil.isEmptyNoLoginList()) {
            LogsUtil.d(LogTags.Device, "$TAG requestStatus")
            getAlexaStatus()
        }
    }

    override fun onResume() {
        super.onResume()
        LogsUtil.d(LogTags.Device, "$TAG onResume")
        btnOpen?.apply { visibility = recyclerView?.visibility!! }

        if (!EventBus.getDefault().isRegistered(this)) EventBus.getDefault().register(this)
        activity?.let {
            NavigationBarColorHelper.setNavigationBarColor(it)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) EventBus.getDefault().unregister(this)
    }

    override fun initView() {
        super.initView()

        vBack = cview?.findViewById(R.id.vback)
        vTitle = cview?.findViewById(R.id.vtitle)

        iv_logo = cview?.findViewById(R.id.iv_logo)
        progressbar = cview?.findViewById(R.id.progressbar)
        btnOpen = cview?.findViewById(R.id.btn_open)

        vBack?.setBackgroundResource(R.drawable.select_icon_menu_back)
        vBack?.setOnClickListener {
            onBackKeyDown()
        }

        vTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Amazon_Alexa")
            setTextColor(ContextCompat.getColor(context, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
        }

        val iconDraw = SkinResourcesUtils.getSingleTintDrawable(
            "component_svg_icon_arrow_forward_square",
            Color.parseColor("#65FFFF")
        )
        if (iconDraw != null) {
            iconDraw.setBounds(0, 0, iconDraw.minimumWidth, iconDraw.minimumHeight)
            btnOpen?.setCompoundDrawables(null, null, iconDraw, null)
        }
        btnOpen?.setPadding(iconDraw!!.minimumWidth, 0, 0, 0)
        btnOpen?.setOnClickListener {
            //判断手机是否安装Alexa App
            if (hasInstalledAlexa(false)) {
                hasInstalledAlexa(true)
            } else {
                showDownload()
            }
        }
        recyclerView = cview?.findViewById(R.id.recycle_view)


        iv_logo?.apply {
            setImageDrawable(SkinResourcesUtils.getDrawable("icon_white_round_amazon_alexa"))
        }

        initDatas()
        mAdapter = activity?.let { AmazonAlexaStatusPageAdapter(it) }
        mAdapter?.currList = currDetailList

        recyclerView?.apply {
            adapter = mAdapter
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            isNestedScrollingEnabled = false
            val itemDecorationCount: Int = itemDecorationCount
            if (itemDecorationCount == 0) {
                addItemDecoration(SpaceItemDecoration(ScreenUtil.dip2px(requireContext(), 8f), SpaceItemDecoration.VERTICAL))
            }
        }
    }

    private fun initDatas() {

        currDetailList.clear()

        var item = AlexaStatusItem()
        item.title = SkinResourcesUtils.getString("music_streaming_with_alexa_cast")
        item.item_type = SETTING_ITEM_TYPE.OPTION_2
        item.bvisibleMore = true
        item.icon_more_name = "icon_arrow_outward"
        item.sub_desc = SkinResourcesUtils.getString("cast_music_to_your_speaker_from_the_amazon_music_app")
        currDetailList.add(item)

        item = AlexaStatusItem()
        item.title = SkinResourcesUtils.getString("multi_room_music")
        item.item_type = SETTING_ITEM_TYPE.OPTION_4
        item.bvisibleMore = true
        item.icon_more_name = "icon_arrow_outward"
        var descString = SkinResourcesUtils.getString("jbl_Alexa_Multi_Room_Music_feature_lets_you_play_the_same_music_on_JBL_One_products_and_Alexa_enabled_sp")
        descString = String.format(descString, context?.getString(R.string.applicationName))
        item.sub_desc = descString
        currDetailList.add(item)

        item = AlexaStatusItem()
        item.title = SkinResourcesUtils.getString("jbl_Setup_A_Preferred_Speaker")
        item.item_type = SETTING_ITEM_TYPE.OPTION_3
        item.bvisibleMore = true
        item.icon_more_name = "icon_arrow_outward"
        item.sub_desc = SkinResourcesUtils.getString("jbl_Alexa_Preferred_Speaker_are_super_convenient_since_they_don_t_require_any_additional_commands_for_th")
        currDetailList.add(item)

        item = AlexaStatusItem()
        item.title = SkinResourcesUtils.getString("alexa_built_in")
        item.item_type = SETTING_ITEM_TYPE.OPTION_5
        item.bvisibleMore = false
        item.icon_more_name = "icon_arrow_outward"
        item.sub_desc = SkinResourcesUtils.getString("to_setup_voice_control_from_alexa_enabled_speaker_and_multi_room_groups_tips")
        currDetailList.add(item)

        item = AlexaStatusItem()
        item.item_type = SETTING_ITEM_TYPE.NONE
        item.bvisibleMore = false
        currDetailList.add(item)
    }

    /**
     * 是否正在请求Alexa的登录状态
     */
    private fun updateSearchingStatus(searching: Boolean) {
        LogsUtil.d(LogTags.Device, "$TAG updateSearchingStatus searching: $searching")
//        uihd.post {
        progressbar?.apply {
            visibility = if (searching) View.VISIBLE else View.GONE
            if (searching) {
                progressbar?.start()
            } else {
                progressbar?.stop()
            }
        }
//        }
    }

    /**
     * 是否显示页面详细信息
     */
    private fun updateAlexaStatue(showDetails: Boolean) {
        LogsUtil.d(LogTags.Device, "$TAG updateAlexaStatue showDetails: $showDetails")
        uihd.post {
            recyclerView?.apply {
                visibility = if (showDetails) View.VISIBLE else View.GONE
            }
            btnOpen?.apply { visibility = recyclerView?.visibility!! }
        }
    }

    /**
     * 更新管理设备是否有未登录alexa的设备
     */
    private fun updateManageDeviceItem() {

//        if (currDetailList == null) return

//        for ((index, item) in currDetailList.withIndex()) {
//            item as AlexaStatusItem
//            if (item.item_type == SETTING_ITEM_TYPE.OPTION_1) {
//                item.showEllipseIcon = AmazonAlexaLoginStatusUtil.getNoLoginList() != null
//                        && AmazonAlexaLoginStatusUtil.getNoLoginList().size > 0
//
//                mAdapter?.notifyItemChanged(index)
//                break
//            }
//        }
    }

    private fun showAlexaLogoutDlg() {
        if (authDevice == null || authDevice?.wifiDevice == null) {
            LogsUtil.i(LogTags.Device, "$TAG showAlexaLogoutDlg authDevice $authDevice")
            return
        }
        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_alexa_device_logout)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View) {
                        updateDlgViews(3, rootView)
//                        uihd.postDelayed({ rootView?.setBackgroundColor(ContextCompat.getColor(activity!!, R.color.bg_overlay)) }, 260)
                    }

                    override fun onDismissDlg() {
                        dismissDlg()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                        dismissDlg()
                    }
                })
        }

        if (extDlg?.isShowing == false) extDlg?.show()
    }

    private fun updateDlgViews(dlgType: Int, rootView: View?) {

        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        if (tvTitle == null)
            tvTitle = rootView?.findViewById(R.id.ble_title)

        tvTitle?.apply {
            text = when (dlgType) {
                1 -> SkinResourcesUtils.getString("jbl_Edit")
                2 -> SkinResourcesUtils.getString("jbl_Amazon_Alexa")
                3 -> SkinResourcesUtils.getString("jbl_Would_you_like_to_Sign_Out_")
                else -> ""
            }
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                dismissDlg()
            }
        }

        when (dlgType) {
            3 -> {
                var btn_restore: Button? = rootView?.findViewById(R.id.btn_restore)
                var btn_cancel: Button? = rootView?.findViewById(R.id.btn_cancel)

                var ivBack: ImageView? = rootView?.findViewById(R.id.iv_back)

                btn_restore?.apply {
                    typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

                    setTextSize(
                        TypedValue.COMPLEX_UNIT_PX,
                        WAApplication.mResources.getDimension(R.dimen.font_14)
                    )
                    text = SkinResourcesUtils.getString("jbl_SIGN_OUT")
                    val tintDrawable = SkinResourcesUtils.getTintListDrawable(
                        "btn_background_bg_s1", GlobalUIConfig.color_btn_normal, GlobalUIConfig.color_btn_press
                    )
                    background = tintDrawable
//                    setTextColor(GlobalUIConfig.color_solid_btn_font_color)
                }

                btn_cancel?.apply {
                    typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

                    setTextSize(
                        TypedValue.COMPLEX_UNIT_PX,
                        WAApplication.mResources.getDimension(R.dimen.font_14)
                    )
                    text = SkinResourcesUtils.getString("jbl_CANCEL")
//                    setTextColor(GlobalUIConfig.color_info_normal)
                }

                ivBack?.apply {
                    visibility = View.GONE
                }

                btnClose?.apply {
                    visibility = View.GONE
                }

                ViewUtil.setViewAlphaClickListener(btn_cancel)
                ViewUtil.setViewAlphaClickListener(btn_restore)
                btn_cancel?.setOnClickListener {
                    dismissDlg()
                }

                btn_restore?.setOnClickListener {

                    dismissDlg()

                    logout()
                }
            }

            else -> {

            }
        }
    }

    private fun logout() {

//        WAApplication.me.showProgDlg(activity, true, "")

        var devItem = authDevice?.wifiDevice

        devItem?.IP?.let { it1 ->
            ApiRequest.getCBLLogout(it1)
                .lifeOnMain(this)
                .subscribe({ it ->
                    LogsUtil.i(LogTags.Device, "$TAG logout status ${it.value}")
                    if (it.value == "0") {
                        //logout success
                        ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, false)
                        val event = CommonEvent(CommonEvent.EVENT_AMAZON_LOGIN)
                        event.value = 0
                        EventBus.getDefault().post(event)
                        onBackKeyDown()
                    }
                }, {
                    LogsUtil.i(LogTags.Device, "$TAG logout status failed. ${it?.localizedMessage}")
                })
        }
    }

    private fun getAlexaStatus() {
        LogsUtil.d(LogTags.Device, "$TAG getAlexaStatus authDevice: $authDevice")
        if (authDevice == null || authDevice?.wifiDevice == null) {
            updateAlexaStatue(false)
            if (enterFrom != EnterFromType.TYPE_COMPOSE && enterFrom != EnterFromType.TYPE_SETTING) {
                showLoginAlexaDialog()
            }

            return
        }

        updateSearchingStatus(true)
        updateAlexaStatue(false)

//        val devSize = masterDeviceList.size
//        val completeAtomic = AtomicInteger(0)
//        val errorAtomic = AtomicInteger(0)

        uihd?.postDelayed({
            updateAlexaStatue(false)
            if (enterFrom != EnterFromType.TYPE_COMPOSE && enterFrom != EnterFromType.TYPE_SETTING) {
                showLoginAlexaDialog()
            }
        }, 30 * 1000)

        var devItem = authDevice?.wifiDevice

        devItem?.IP?.let { it ->
            ApiRequest.getCBLStatus(it).lifeOnMain(this).subscribe({

                LogsUtil.d(LogTags.Device, "$TAG getAlexaStatus success: ${it.toString()}")
                var item = AlexaStatusItem()
                item.title = devItem?.Name ?: ""

                item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
                item.uuid = devItem?.uuid
                item.icon_name = DeviceImageUtil.getDeviceImgNameByDeviceItem(devItem)

                if (it.state == CBLStatus.REQUESTING_TOKEN || it.state == CBLStatus.REFRESHING_TOKEN) {
                    item.item_type = SETTING_ITEM_TYPE.OPTION_1
                    AmazonAlexaLoginStatusUtil.addLoginItem(item)
                } else {
                    item.sub_desc = SkinResourcesUtils.getString("jbl_Enable")
                    item.item_type = SETTING_ITEM_TYPE.OPTION_2
                    AmazonAlexaLoginStatusUtil.addNoLoginItem(item)
                }

                uihd?.removeCallbacksAndMessages(null)

                showSearchingResult()
            }, {
                LogsUtil.d(LogTags.Device, "$TAG getAlexaStatus fail: ${it.toString()}")
                var item = AlexaStatusItem()
                item.title = devItem?.Name
                item.item_type = SETTING_ITEM_TYPE.OPTION_2
                item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
                item.uuid = devItem?.uuid
                item.sub_desc = SkinResourcesUtils.getString("jbl_Enable")
                item.icon_name = DeviceImageUtil.getDeviceImgNameByDeviceItem(devItem)
                AmazonAlexaLoginStatusUtil.addNoLoginItem(item)

                uihd?.removeCallbacksAndMessages(null)
                WAApplication.me.showProgDlg(activity, false, null)
                showSearchingResult()
            })
        }
    }

    var extDlg: ExtendDialogNew? = null

    private fun showSearchingResult() {
        LogsUtil.d(LogTags.Device, "$TAG showSearchingResult enterFrom: $enterFrom")
        updateSearchingStatus(false)
//        updateManageDeviceItem()

        if (AmazonAlexaLoginStatusUtil.getLoginList() != null
            && AmazonAlexaLoginStatusUtil.getLoginList().size > 0
        ) {
            updateAlexaStatue(true)
            return
        }
        if (enterFrom == EnterFromType.TYPE_COMPOSE || enterFrom == EnterFromType.TYPE_SETTING) {
            if (authDevice == null || authDevice?.wifiDevice == null) return



            var vfrag = AlexaLoginWebViewFragment()
            vfrag.dataInfo = dataInfo
            dataInfo?.deviceItem = authDevice?.wifiDevice
            dataInfo?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
        } else {
            showLoginAlexaDialog()
        }

    }

    private fun showLoginAlexaDialog() {
        if (activity == null)
            return

        if (extDlg == null) {
            LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate, showLoginAlexaDialog")
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_alexa_status)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View?) {
                        updateDlgViews(rootView)
//                        uihd.postDelayed({ rootView?.setBackgroundColor(ContextCompat.getColor(activity!!, R.color.bg_overlay)) }, 260)
                    }

                    override fun onDismissDlg() {
                        dismissDlg()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                        dismissDlg()
                        onBackKeyDown()
                    }
                })
        }

        if (extDlg?.isShowing == false) extDlg?.show()
    }

    private fun updateDlgViews(rootView: View?) {
        var containerView = rootView?.findViewById<View>(R.id.container)
        containerView?.setBackgroundResource(R.drawable.shape_chromecast_bg_l1)
        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Amazon_Alexa")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)

        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                onBackKeyDown()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {
            text = SkinResourcesUtils.getString("jbl_Login_your_Alexa_account_to_start_using_Alexa_MRM_")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
        }

        var ivLogo: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivLogo?.apply {
            setImageDrawable(SkinResourcesUtils.getDrawable("jbl_one_amazon_alexa_white_logo"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {
            text = SkinResourcesUtils.getString("jbl_LOGIN_ALEXA")
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            setOnClickListener {
                dismissDlg()
                if (authDevice == null || authDevice?.wifiDevice == null) return@setOnClickListener



                var vfrag = AlexaLoginWebViewFragment()
                vfrag.dataInfo = dataInfo
                dataInfo?.deviceItem = authDevice?.wifiDevice
                dataInfo?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
            }
        }

    }




    private fun dismissDlg() {

        uihd.post {
            extDlg?.rootView?.setBackgroundColor(Color.TRANSPARENT)
            /*activity?.let { itActivity ->
                BarUtils.setStatusBarColor(itActivity, WAApplication.me.getColor(R.color.bg_L1))
//                Utils.decorateHarmanWindow(itActivity)
            }*/
        }
        uihd.postDelayed({
            extDlg?.apply {
                dismissDlg()
                extDlg = null
            }
        }, 50)
    }

    override fun bindSlots() {
        super.bindSlots()

        mAdapter?.setOnItemClickListener(object : AmazonAlexaStatusPageAdapter.IOnItemClickListener {
            override fun onItemClicked(pos: Int, item: ContentSettingItem?) {

                when (item?.item_type) {

                    SETTING_ITEM_TYPE.OPTION_1 -> {
                        var vfrag = FragAlexaManageDevice()
                        vfrag.dataInfo = dataInfo
                        dataInfo?.frameId?.let {
                            FragTabUtils.replaceFrag(activity, it, vfrag, true)
                        }
                    }

                    SETTING_ITEM_TYPE.OPTION_2 -> {
                        //setup Music Streaming with Alexa Cast
                        val intent = Intent()
                        intent.action = Intent.ACTION_VIEW
                        val url = Uri.parse("https://music.amazon.com/help?nodeId=G202196760")
                        intent.data = url
                        startActivity(intent)
                    }

                    SETTING_ITEM_TYPE.OPTION_3 -> {
                        //setup a preferred speaker
                        val intent = Intent()
                        intent.action = Intent.ACTION_VIEW
                        val url = Uri.parse("https://www.amazon.com/gp/help/customer/display.html?nodeId=GKLJNKYFKRRT3FEQ")
                        intent.data = url
                        startActivity(intent)

                    }

                    SETTING_ITEM_TYPE.OPTION_4 -> {
                        //setup alexa multi-room
                        val intent = Intent()
                        intent.action = Intent.ACTION_VIEW
                        val url = Uri.parse("https://www.amazon.com/gp/help/customer/display.html?nodeId=GZ5U38E9GGBBWEM8")
                        intent.data = url
                        startActivity(intent)
                    }

                    SETTING_ITEM_TYPE.OPTION_5 -> {
                        //more features

//                        //判断手机是否安装Alexa App
//                        if (hasInstalledAlexa(false)) {
//                            hasInstalledAlexa(true)
//                        } else {
//                            showDownload()
//                        }
                    }

                    else -> {}
                }
            }

            override fun onLogout() {
                showAlexaLogoutDlg()
            }

            override fun openApp() {
                //判断手机是否安装Alexa App
                if (hasInstalledAlexa(false)) {
                    hasInstalledAlexa(true)
                } else {
                    showDownload()
                }
            }
        })
    }

    private fun showDownload() {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        val url = Uri.parse("https://play.google.com/store/apps/details?id=com.amazon.dee.app")
        intent.data = url
        startActivity(intent)
    }


    private fun hasInstalledAlexa(openApp: Boolean): Boolean {
        val packageManager = WAApplication.me.applicationContext.packageManager
        var intent: Intent? = null
        intent = packageManager.getLaunchIntentForPackage("com.amazon.dee.app")
        if (intent != null) {
            if (openApp) {
                startActivity(intent)
            }
            return true
        }
        return false
    }

    override fun initUtils() {
        super.initUtils()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventMessage(event: CommonEvent?) {
        LogsUtil.d(LogTags.Device, "$TAG onEventMessage:${event?.name}, enterFrom:${enterFrom}")
        if (event?.name.equals(CommonEvent.EVENT_AMAZON_LOGIN)) {
            if (enterFrom == EnterFromType.TYPE_COMPOSE && activity is ChromecastAlexaComposeActivity) {
                var targetAct = activity as ChromecastAlexaComposeActivity
                targetAct.alexaSwitch(event?.value!!)
            } else if (enterFrom == EnterFromType.TYPE_SETTING) {
                FragTabUtils.popBack(activity)
            } else {
                AmazonAlexaLoginStatusUtil.clearAll()
                requestStatus()
                event?.let {
                    if (it.value == 1) {
                        ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, true)
                    } else {
                        ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, false)
                    }
                }

            }
        }
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        LogsUtil.d(LogTags.Device, "$TAG onBackKeyDown, enterFrom: $enterFrom")
        dismissDlg()

        if (enterFrom == EnterFromType.TYPE_COMPOSE && activity is ChromecastAlexaComposeActivity) {
            activity?.finish()
        } else {
            FragTabUtils.popBack(activity)
        }
    }

    companion object {
        val TAG: String = FragAmazonAlexaStatus::class.java.simpleName
    }
}