package com.wifiaudio.view.pagesmsccontent.menu_settings.jblone

import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.*
import android.widget.*
import com.amazon.identity.auth.device.AuthError
import com.amazon.identity.auth.device.api.authorization.*
import com.amazon.identity.auth.device.api.workflow.RequestContext
import com.harman.*
import com.harman.bar.app.R
import com.harman.bean.*
import com.harman.hkone.DeviceImageUtil
import com.jbl.one.configuration.model.StreamService
import com.harman.utils.Utils
import com.harman.widget.CollapsingToolBar
import com.harman.widget.DotLoadingImageView
import com.jbl.one.configuration.AppConfigurationUtils
import com.rxjava.rxlife.lifeOnMain
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.ApplicationViewModel
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.utils.GsonParseUtil
import com.wifiaudio.utils.NavigationBarColorHelper
import com.wifiaudio.utils.UIUtils
import com.wifiaudio.utils.cbl.CBLStatus
import com.wifiaudio.utils.cloudRequest.rxhttp.ApiRequest
import com.wifiaudio.view.dlg.AlexaLogoutDialog
import com.wifiaudio.view.dlg.AlexaSetupExitDialog
import com.wifiaudio.view.dlg.ExtendDialogNew
import com.wifiaudio.view.oobe.AlexaSetupReadyDialogFragment
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLDataUtil
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import com.wifiaudio.view.pagesmsccontent.home.ChromecastAlexaComposeActivity
import com.wifiaudio.view.pagesmsccontent.menu_settings.AlexaLoginWebViewFragment
import com.wifiaudio.view.pagesmsccontent.menu_settings.EnterFromType
import com.wifiaudio.view.pagesmsccontent.menu_settings.SETTING_ITEM_TYPE
import com.wifiaudio.view.pagesmsccontent.menu_settings.lwa.LoginWithAmazonViewModel
import config.AppLogTagUtil
import config.LogTags
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean

class AlexaVoiceAssistantFragment : FragTabBackBase() {

    var endOfRequestSwitch: Switch? = null
    var startOfRequestSwitch: Switch? = null
    var authDevice: AuthDevice? = null
    var cview: View? = null

    //    var recyclerView: RecyclerView? = null
    var progressbar: DotLoadingImageView? = null
    var iv_logo: ImageView? = null

    var enterFrom: EnterFromType? = EnterFromType.TYPE_NONE //1:Stream Service 2: Manage Device 3: Language 4:Dashboard
    var alexaOnly: Boolean? = false
    //var deviceItem: DeviceItem? = null
    var dataInfo: DataFragInfo? = null

    var collapsingToolBar: CollapsingToolBar? = null
    var contentLayout: View? = null
    var languageLayout: RelativeLayout? = null
    var languageTextView: TextView? = null
    var lwaFeatureSupport: Boolean? = false

    var requestContext: RequestContext? = null
    var reGetLWAStateCount = 0
    private var loginWithAmazonViewModel: LoginWithAmazonViewModel? = null
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.fragment_alexa_voice_assistant, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()
        requestStatus()
        if (!EventBus.getDefault().isRegistered(this)) EventBus.getDefault().register(this)
        return cview
    }

    private fun requestStatus() {
        var jblFeatureSupport = ApplicationViewModel.getInstance().getFeatureSupport(authDevice?.deviceCrc)
        lwaFeatureSupport = TextUtils.equals("true", jblFeatureSupport?.featureSupport?.lwaLogin?.support)
        if (lwaFeatureSupport == true) {
            doInitLWAFlow()
        }
        requestTone()
        requestVoiceLanguage()
    }

    private fun doInitLWAFlow() {
        activity?.apply {
            requestContext = RequestContext.create(activity)
            requestContext?.registerListener(authorizeListener)
        }
    }

    private var hasAuthorizeResult = AtomicBoolean(false)
    private var authorizeListener = object : AuthorizeListener() {


        override fun onSuccess(result: AuthorizeResult?) {


            if (!hasAuthorizeResult.getAndSet(true)) {
                var authorizationCode = result?.authorizationCode
                var clientId = result?.clientId
                var redirectURI = result?.redirectURI
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getLWAState:authorizeListener：success:${authorizationCode},${clientId},${redirectURI}")
                //get device info
                requestLanguageFromAmazon()
//                wait for voice language from amazon server
                setupLWAState(LWAInfo(authorizationCode, clientId, redirectURI))
            }
        }

        override fun onError(p0: AuthError?) {
            Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getLWAState:authorizeListener：fail:${p0?.message}")
            showLoading(true)
            navigateToPrevPage()
        }

        override fun onCancel(p0: AuthCancellation?) {
            Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getLWAState:authorizeListener：cancel:${p0?.description}")
            //if concurrent setup select alexa and google

            if (enterFrom == EnterFromType.TYPE_COMPOSE && alexaOnly == false) {
                uihd.post { showCancelConfirmDialog() }
            }else{
                showLoading(true)
                navigateToPrevPage()
            }
        }

    }

    private fun isSupportVa(): Boolean? {
        var streamServiceList = getStreamServiceList()
        return if (streamServiceList == null || streamServiceList.isEmpty()) {
            false
        } else {
            var optional =
                streamServiceList.stream().filter { StreamService.GoogleVA == it || StreamService.AmazonAlexaVA == it }
                    .findFirst()
            optional.isPresent
        }
    }

    fun getStreamServiceList(): List<StreamService>? {
        return authDevice?.modelName?.let { AppConfigurationUtils.getModelConfigByModelName(it)?.streamService }
    }

    private var alexaSetupExitDialog: AlexaSetupExitDialog? = null
    private fun showCancelConfirmDialog() {
        if (alexaSetupExitDialog != null && alexaSetupExitDialog?.isShowing() == true) {
            Log.d(AlexaVoiceAssistantFragment::class.simpleName, "showCancelConfirmDialog: return")
            return
        }
        alexaSetupExitDialog = activity?.let { AlexaSetupExitDialog(it) }
        alexaSetupExitDialog?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
            override fun onConfirm(any: Any?) {
                alexaSetupExitDialog = null
                //continue setup
                if (activity is ChromecastAlexaComposeActivity) {
                    var targetAct = activity as ChromecastAlexaComposeActivity
                    targetAct.alexaSwitch(-1)
                }

            }

            override fun onCancel() {
                alexaSetupExitDialog = null
                showLoading(true)
                navigateToPrevPage()
            }
        })
        alexaSetupExitDialog?.show()
    }

    private var setupLwaState: Boolean? = false
    private fun setupLWAState(lwaInfo: LWAInfo) {
        if (lwaInfo == null || lwaInfo.isEmpty) {
            Log.d(AlexaVoiceAssistantFragment::class.simpleName, "setupLWAState:fail:${lwaInfo}")
            return
        }

        if (ApplicationViewModel.getInstance().currentAuthDevice == null) return
        WirelessConnector.setLWAAuthCode(ApplicationViewModel.getInstance().currentAuthDevice, lwaInfo, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "setupLWAState:success:${content}")
                var jsonObject = JSONObject(content)
                var errorCode = jsonObject.optString("error_code")
                if (TextUtils.equals("0", errorCode)) {
                    setupLwaState = true
                    reGetLWAState()
                    uihd.post {
                        loginWithAmazonViewModel?.voiceLanguage?.let {
                            currentVoiceLanguage = it
                            languageTextView?.text = currentVoiceLanguage?.language + "(${currentVoiceLanguage?.countries})"
                        }
                    }
                }
            }

            override fun onFailure(e: Throwable?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "setupLWAState:fail:${e?.message}")
            }

        })
    }

    /**
     * request and set device language
     */
    private fun requestLanguageFromAmazon() {
        if (TextUtils.isEmpty(serialNumber) || TextUtils.isEmpty(productID)) {
            LogsUtil.d(AlexaVoiceAssistantFragment::class.simpleName, "requestLanguageFromAmazon:serialNumber:${serialNumber} productID:${productID}")
            return
        }
        if (loginWithAmazonViewModel == null) {
            loginWithAmazonViewModel = LoginWithAmazonViewModel()
        }
        if (ApplicationViewModel.getInstance().currentAuthDevice == null) {
            LogsUtil.d(AlexaVoiceAssistantFragment::class.simpleName, "requestLanguageFromAmazon:currentAuthDevice==null")
            return
        }
        loginWithAmazonViewModel?.requestLanguageFromAmazon(serialNumber, productID)
    }

    /**
     * setup voice language
     */
    private fun setDeviceVoiceLanguage() {
        if (loginWithAmazonViewModel == null) {
            loginWithAmazonViewModel = LoginWithAmazonViewModel()
        }
        if (ApplicationViewModel.getInstance().currentAuthDevice == null) {
            LogsUtil.d(AlexaVoiceAssistantFragment::class.simpleName, "setDeviceVoiceLanguage:currentAuthDevice==null")
            return
        }
        //update ui

        if (loginWithAmazonViewModel?.isGetVoiceLanguageSuccess == true) {
            var language = loginWithAmazonViewModel?.voiceLanguage
            Log.d(AlexaVoiceAssistantFragment::class.java.simpleName, " setDeviceLanguage::$language")
            currentVoiceLanguage = language
            languageTextView?.text = language?.language + "(${language?.countries})"
        }
        var authDevice = ApplicationViewModel.getInstance().currentAuthDevice
        loginWithAmazonViewModel?.setDeviceLanguage(authDevice)
    }

    private var lwaSate: LWAState? = null
    private var serialNumber:String?=""
    private var productID:String?=""
    private fun getLWAState() {
        if (ApplicationViewModel.getInstance().currentAuthDevice == null) return
        updateSearchingStatus(true)
        WirelessConnector.getLWAState(ApplicationViewModel.getInstance().currentAuthDevice, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                LogsUtil.d(AlexaVoiceAssistantFragment::class.simpleName, "logEvent - EventName : LWA getLWAState:onSuccess:${content}")

                var lwaSateTemp = content?.let { GsonParseUtil.instance.fromJson(it, LWAState::class.java) }
                lwaSateTemp?.meta?.apply {
                    if (!TextUtils.isEmpty(lwaSateTemp.meta?.product_id)) productID = lwaSateTemp.meta?.product_id
                    if (!TextUtils.isEmpty(lwaSateTemp.meta?.serial_number)) serialNumber = lwaSateTemp.meta?.serial_number
                }
                if (lwaSateTemp?.error_code == 0 && lwaSateTemp?.state == LWAState.LoginState.NOT_LOGIN.ordinal) {
//                    CoulsonManager.instance.recordVaSetupResultValue(EventUtils.alexa,false)
                    //wait 1000ms to check lwa hasAuthorizeResult
                    uihd.postDelayed({
                        if (lwaSate == null) {
                            lwaSate = lwaSateTemp
                            navigateToLwaWeb()
                        } else {
                            // cancel by key back

                            //if concurrent cancel
                            if (alexaSetupExitDialog?.isShowing() == true) return@postDelayed

                            if (!hasAuthorizeResult.get() && setupLwaState == false) navigateToPrevPage()
                        }
                    }, 1000)
                } else if (lwaSateTemp?.error_code == 0 && lwaSateTemp?.state == LWAState.LoginState.LOGGING.ordinal) {
                    //todo login
                    reGetLWAState()
                } else if (lwaSateTemp?.error_code == 0 && lwaSateTemp?.state == LWAState.LoginState.LOGGED.ordinal) {
                    if (setupLwaState == false) {
                        goAfterLoginSuccess()
                    } else {
                        if (loginWithAmazonViewModel?.isGetVoiceLanguageSuccess == true || reGetLWAStateCount >= MAX_RETRY) {
                            goAfterLoginSuccess()
                        } else {
                            reGetLWAState()
                        }
                    }
                }
            }

            override fun onFailure(e: Throwable?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getLWAState:onFailure:${e?.message}")
                reGetLWAState()
            }

        })
    }
    private fun goAfterLoginSuccess(){
        updateSearchingStatus(false)

        ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, true)

    }

    private fun navigateToLwaWeb() {
        //block on resume
        doLWAAuthorize()
    }

    private fun reGetLWAState() {
        showLoading(true)
        reGetLWAStateCount++
        Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getLWAState:reGetLWAState count:$reGetLWAStateCount")
        if (reGetLWAStateCount > MAX_RETRY) {
            Log.d(AlexaVoiceAssistantFragment::class.simpleName, "reGetLWAState:fail count>8")
            uihd.postDelayed(kotlinx.coroutines.Runnable {
                WAApplication.me.showProgDlg(activity, false, "")
                showLoading(false)
                onBackKeyDown()
            }, 5000)
            return
        }
        uihd.postDelayed({ getLWAState() }, 2000)
    }

    private var currentVoiceLanguage: VoiceLanguage? = null
    private fun requestVoiceLanguage() {
        if (ApplicationViewModel.getInstance().currentAuthDevice == null) return
        WirelessConnector.getVoiceLanguage(ApplicationViewModel.getInstance().currentAuthDevice,
                VoiceLanguage(), object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getVoiceLanguage:onSuccess:${content}")
                try {
                    var jsonObject = JSONObject(content);
                    if (TextUtils.equals("0", jsonObject.optString("error_code"))) {
                        var localeString = jsonObject.optString("locale")
                        if (TextUtils.isEmpty(localeString)) return
                        var voiceLanguage = VoiceLanguage(localeString)
                        currentVoiceLanguage = voiceLanguage
                        languageTextView?.text = voiceLanguage.language + "(${voiceLanguage.countries})"
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onFailure(e: Throwable?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "getVoiceLanguage:onSuccess:${e}")
            }
        })
    }


    private fun requestTone() {
        if (ApplicationViewModel.getInstance().currentAuthDevice == null) return
        WirelessConnector.getVoiceRequestTone(ApplicationViewModel.getInstance().currentAuthDevice, "getVoiceRequestStartTone", RequestTone(), object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "requestTone:getVoiceRequestStartTone:onSuccess:${content}")
                parseVoiceRequestTone("getVoiceRequestStartTone", content)
            }

            override fun onFailure(e: Throwable?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "requestTone:getVoiceRequestStartTone:onSuccess:${e}")
            }
        })

        WirelessConnector.getVoiceRequestTone(ApplicationViewModel.getInstance().currentAuthDevice, "getVoiceRequestEndTone", RequestTone(), object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "requestTone:getVoiceRequestEndTone:onSuccess:${content}")
                parseVoiceRequestTone("getVoiceRequestEndTone", content)
            }

            override fun onFailure(e: Throwable?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "requestTone:getVoiceRequestEndTone:onFailure:${e}")
            }
        })

    }

    private fun parseVoiceRequestTone(command: String?, content: String?) {
        try {
            var jsonObject = JSONObject(content);
            var code = jsonObject.optString("error_code")
            if (TextUtils.equals("0", code)) {
                var checked = "1" == jsonObject.optString("status")
                if (TextUtils.equals("getVoiceRequestEndTone", command)) {
                    endOfRequestSwitch?.isChecked = checked
                } else if (TextUtils.equals("getVoiceRequestStartTone", command)) {
                    startOfRequestSwitch?.isChecked = checked
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(AlexaVoiceAssistantFragment::class.simpleName, "onResume")

        activity?.let {
            NavigationBarColorHelper.setNavigationBarColor(it)
        }
        requestContext?.onResume()

        //current should wait hasAuthorizeResult update(response by lwa AuthorizeListener callback)
        uihd.postDelayed({ getAlexaLoginStatus() }, 500)
    }

    private fun getAlexaLoginStatus() {
        if (lwaFeatureSupport == true || isSupportVa() == true) {
            getLWAState()
        } else if (AmazonAlexaLoginStatusUtil.isEmptyLoginList() && AmazonAlexaLoginStatusUtil.isEmptyNoLoginList()) {
            getCLBStatus()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) EventBus.getDefault().unregister(this)
        loginWithAmazonViewModel?.release()
    }

    override fun initView() {
        super.initView()
        collapsingToolBar = cview?.findViewById(R.id.collapsing_toolbar)
        collapsingToolBar?.setExpanded(false)
        collapsingToolBar?.setTitleText(SkinResourcesUtils.getString("jbl_Amazon_Alexa"))
        collapsingToolBar?.setNavigationListener(object : CollapsingToolBar.NavigationListener {
            override fun onBack() {
                onBackKeyDown()
            }

            override fun onNext() {
            }
        })
        iv_logo = cview?.findViewById(R.id.iv_logo)
        progressbar = cview?.findViewById(R.id.progressbar)
        contentLayout = cview?.findViewById(R.id.layout_content)
        var layout_chromecast_built_in = cview?.findViewById<RelativeLayout>(R.id.layout_chromecast_built_in)
        layout_chromecast_built_in?.setOnClickListener {
            UIUtils.openWebUrl(context, "https://music.amazon.com/help?nodeId=G202196760")
        }

        var layout_multi_room_music = cview?.findViewById<RelativeLayout>(R.id.layout_multi_room_music)
        layout_multi_room_music?.setOnClickListener {
            UIUtils.openWebUrl(context, "https://www.amazon.com/gp/help/customer/display.html?nodeId=GZ5U38E9GGBBWEM8")
        }

        var layout_go_to_alexa_app = cview?.findViewById<LinearLayout>(R.id.layout_go_to_alexa_app)
        layout_go_to_alexa_app?.setOnClickListener {
            UIUtils.openApp(activity, UIUtils.PACKAGE_NAME_ALEXA_APP)
        }
        languageLayout = cview?.findViewById(R.id.layout_language)
        languageLayout?.setOnClickListener {
            //open app setting page
//            UIUtils.openApp(activity, UIUtils.PACKAGE_NAME_ALEXA_APP)
//            UIUtils.openAppPage(activity, UIUtils.PACKAGE_NAME_ALEXA_APP, "https://alexa.amazon.com/?fragment=v2/device-settings/id/d0eca4b1d4694c93b996cc8832146359")
//            "https://alexa.amazon.com/spa/index.html#v2/devices-channel"
            var finalDeepLink="https://alexa.amazon.com/spa/index.html#v2/devices-channel/control-panel/all"
            UIUtils.openAppPage(activity, UIUtils.PACKAGE_NAME_ALEXA_APP, finalDeepLink)
        }
        languageTextView = cview?.findViewById(R.id.tv_language)

        startOfRequestSwitch = cview?.findViewById<Switch>(R.id.switch_start_of_request)
        startOfRequestSwitch?.setOnClickListener {
            var isChecked = startOfRequestSwitch?.isChecked!!
            doRequest(isChecked, 0)
        }
        endOfRequestSwitch = cview?.findViewById<Switch>(R.id.switch_end_of_request)
        endOfRequestSwitch?.setOnClickListener {
            var isChecked = endOfRequestSwitch?.isChecked!!
            doRequest(isChecked, 1)
        }
        cview?.findViewById<TextView>(R.id.tv_logout)?.setOnClickListener {
            showAlexaLogoutDlg()

        }
    }

    private fun doRequest(checked: Boolean, type: Int) {
        if (ApplicationViewModel.getInstance().currentAuthDevice == null) return
        var requestTone = RequestTone()
        requestTone.status = if (checked) 1 else 0
        var command = if (type == 0) "setVoiceRequestStartTone" else "setVoiceRequestEndTone"
        WirelessConnector.setVoiceRequestTone(ApplicationViewModel.getInstance().currentAuthDevice, command, requestTone, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "requestTone:setVoiceRequestStartTone:onSuccess:${content}")
            }

            override fun onFailure(e: Throwable?) {
                Log.d(AlexaVoiceAssistantFragment::class.simpleName, "requestTone:setVoiceRequestStartTone:onFailure:${e}")
            }
        })
    }

    private fun showLoading(isLoading: Boolean) {
        uihd.post {
            progressbar?.apply {
                visibility = if (isLoading) View.VISIBLE else View.GONE
                if (isLoading) {
                    progressbar?.start()
                } else {
                    progressbar?.stop()
                }
            }
        }
    }

    /**
     * 是否正在请求Alexa的登录状态
     */
    private fun updateSearchingStatus(searching: Boolean) {

        showLoading(searching)


        contentLayout?.apply {
            visibility = if (searching) View.GONE else View.VISIBLE
        }

        if (lwaFeatureSupport == true && setupLwaState == true && !searching) {
            LogsUtil.d(AppLogTagUtil.DEVICE_TAG, "logEvent - EventName : showAlexaSetupOkDialog 3")
            showAlexaSetupOkDialog()
            //set voice language
            setDeviceVoiceLanguage()

            setupLwaState = false
        }
    }

    /**
     * 是否显示页面详细信息
     */
    private fun updateAlexaStatue(showDetails: Boolean) {

        uihd.post {
//            btnOpen?.apply { visibility = if (showDetails) View.VISIBLE else View.GONE }
            contentLayout?.apply { visibility = if (showDetails) View.VISIBLE else View.GONE }
        }
    }

    /**
     * 更新管理设备是否有未登录alexa的设备
     */
    private fun updateManageDeviceItem() {

//        if (currDetailList == null) return

//        for ((index, item) in currDetailList.withIndex()) {
//            item as AlexaStatusItem
//            if (item.item_type == SETTING_ITEM_TYPE.OPTION_1) {
//                item.showEllipseIcon = AmazonAlexaLoginStatusUtil.getNoLoginList() != null
//                        && AmazonAlexaLoginStatusUtil.getNoLoginList().size > 0
//
//                mAdapter?.notifyItemChanged(index)
//                break
//            }
//        }
    }

    private fun getCLBStatus() {


        if (authDevice == null || authDevice?.wifiDevice == null) {
            updateSearchingStatus(true)
//            showLoginAlexaDialog()
            return
        }

        updateSearchingStatus(true)

        var devItem = authDevice?.wifiDevice
        devItem?.IP?.let {
            ApiRequest.getCBLStatus(it).lifeOnMain(this).subscribe({
                LogsUtil.d(AppLogTagUtil.DEVICE_TAG, "logEvent - EventName : CBL getAlexaStatus success: ${it.toString()}")
                var item = AlexaStatusItem()
                item.title = devItem?.Name ?: ""

                item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
                item.uuid = devItem?.uuid
                item.icon_name = DeviceImageUtil.getDeviceImgNameByDeviceItem(devItem)

                if (it.state == CBLStatus.REQUESTING_TOKEN || it.state == CBLStatus.REFRESHING_TOKEN) {
                    item.item_type = SETTING_ITEM_TYPE.OPTION_1
                    AmazonAlexaLoginStatusUtil.addLoginItem(item)
                    updateSearchingStatus(false)


                    ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, true)
                } else {
                    item.sub_desc = SkinResourcesUtils.getString("jbl_Enable")
                    item.item_type = SETTING_ITEM_TYPE.OPTION_2
                    AmazonAlexaLoginStatusUtil.addNoLoginItem(item)
                    onHaveNotLogin()
                }

            }, {

                LogsUtil.d(AppLogTagUtil.DEVICE_TAG, "getAlexaStatus fail: ${it.toString()}")
                var item = AlexaStatusItem()
                item.title = devItem?.Name
                item.item_type = SETTING_ITEM_TYPE.OPTION_2
                item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
                item.uuid = devItem?.uuid
                item.sub_desc = SkinResourcesUtils.getString("jbl_Enable")
                item.icon_name = DeviceImageUtil.getDeviceImgNameByDeviceItem(devItem)
                AmazonAlexaLoginStatusUtil.addNoLoginItem(item)


                onHaveNotLogin()
            })
        }
    }

    /**
     * get product_id client_id
     * if they are empty will
     */
    private fun onHaveNotLogin() {
        if (authDevice == null || authDevice?.wifiDevice == null) return

        DeviceCustomerSettingAction.makeDeviceGetAvsInfo(dataInfo?.deviceItem?.IP,
                object : DeviceSettingActionCallback {
                    override fun onSuccess(content: String?) {
                        LogsUtil.i("makeDeviceGetAvsInfo success: $content")
                        var item = content?.let { GsonParseUtil.instance.fromJson(it, AlexaAvsInfo::class.java) }

                        if (item?.result == null) {
                            navigateToPrevPage()
                        } else {
                            navigateToAmazonLoginPage(item?.result!!)
                        }
                    }

                    override fun onFailure(e: Throwable?) {

                        LogsUtil.i("makeDeviceGetAvsInfo success: ${e?.message}")
                        showLoading(false)
                        navigateToPrevPage()
                    }
                })
    }

    var extDlg: ExtendDialogNew? = null


    private fun navigateToPrevPage() {
        LogsUtil.i("makeDeviceGetAvsInfo fail:navigateToPrevPage")
        uihd.postDelayed({ onBackKeyDown() }, 250)
    }

    private fun navigateToAmazonLoginPage(result: AlexaAvsInfo.AVS_Result) {
        if (authDevice == null || authDevice?.wifiDevice == null) return



        uihd.postDelayed({
            var vfrag = AlexaLoginWebViewFragment()
            vfrag.dataInfo = dataInfo
            vfrag.avsResult = result
            dataInfo?.deviceItem = authDevice?.wifiDevice
            dataInfo?.frameId?.let { FragTabUtils.addFrag(activity, it, vfrag, true) }
        }, 1500)
    }




    private fun dismissDlg() {
        uihd.post {
            extDlg?.rootView?.setBackgroundColor(Color.TRANSPARENT)
            activity?.apply { Utils.decorateHarmanWindow(activity) }
        }
        uihd.postDelayed({
            extDlg?.apply {
                dismissDlg()
                extDlg = null
            }
        }, 10)
    }

    override fun bindSlots() {
        super.bindSlots()


    }

    private fun doLWAAuthorize() {
        if (lwaSate == null || lwaSate?.meta == null || lwaSate?.meta?.isEmpty == true) {
            LogsUtil.d(AppLogTagUtil.ALEXA_TAG, "doLWAAuthorize lwaSate: $lwaSate")
            navigateToPrevPage()
            return
        }
        val scopeData = JSONObject()
        val productInstanceAttributes = JSONObject()
        try {
            productInstanceAttributes.put("deviceSerialNumber", lwaSate?.meta?.serial_number) // TODO:
            scopeData.put("productInstanceAttributes", productInstanceAttributes);
            scopeData.put("productID", lwaSate?.meta?.product_id)
        } catch (e: JSONException) {
            throw RuntimeException(e)
        }
        LogsUtil.d(AppLogTagUtil.ALEXA_TAG, "doLWAAuthorize start: lwaSate: $lwaSate scopeData:${scopeData}")
        if(requestContext == null){
            requestContext = RequestContext.create(context)
            requestContext?.registerListener(authorizeListener)
        }
        try {
            AuthorizationManager.authorize(AuthorizeRequest.Builder(requestContext) // App Login
                    .addScopes(ProfileScope.profile(), ProfileScope.postalCode()) // TODO: Authorize an AVS Device Through a Companion App.
                    .addScopes(ScopeFactory.scopeNamed("alexa:voice_service:pre_auth"), ScopeFactory.scopeNamed("alexa:all", scopeData))
                    .forGrantType(AuthorizeRequest.GrantType.AUTHORIZATION_CODE).withProofKeyParameters(lwaSate?.meta?.code_challenge, "S256")
                    .build())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    private var logoutDialog: AlexaLogoutDialog? = null
    // need to upgrade
    private fun showAlexaLogoutDlg() {
        if (authDevice == null || authDevice?.wifiDevice == null) {
            LogsUtil.i(AppLogTagUtil.ALEXA_TAG, "showAlexaLogoutDlg authDevice $authDevice")
            return
        }
        if (logoutDialog != null && logoutDialog?.isShowing() == true) {
            Log.d(AppLogTagUtil.ALEXA_TAG, "showAlexaLogoutDlg: return")
            return
        }
        logoutDialog=activity?.let { AlexaLogoutDialog(it) }
        logoutDialog?.setDlgClickListener(object :DevBaseDlgView.IOnDlgBtnClickListener{
            override fun onConfirm(any: Any?) {
                if (lwaFeatureSupport == true) {
                    lwaLogout()
                } else {
                    logout()
                }
            }

            override fun onCancel() {
                Log.d(AppLogTagUtil.ALEXA_TAG, "showAlexaLogoutDlg: onCancel")
            }
        })
        logoutDialog?.show()
    }


    private fun lwaLogout() {
        if (authDevice == null || authDevice?.wifiDevice == null) return
        WAApplication.me.showProgDlg(activity, true, "")
        WirelessConnector.requestLWALogout(authDevice, object : DeviceSettingActionCallback {
            override fun onSuccess(content: String?) {
                LogsUtil.i(AppLogTagUtil.ALEXA_TAG, "lwaLogout success")
                uihd.postDelayed(kotlinx.coroutines.Runnable {
                    WAApplication.me.showProgDlg(activity, false, "")
                    showLoading(false)
                    ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, false)
                    EventBus.getDefault().post(CommonEvent(1, CommonEvent.EVENT_REFRESH_COMPOSE_STATUE))
                    onBackKeyDown()
                }, 5000)
            }

            override fun onFailure(e: Throwable?) {
                LogsUtil.i(AppLogTagUtil.ALEXA_TAG, "lwaLogout fail:${e?.message}")
                WAApplication.me.showProgDlg(activity, false, "")
            }
        })
    }

    private fun logout() {

        WAApplication.me.showProgDlg(activity, true, "")

        var devItem = authDevice?.wifiDevice

        devItem?.IP?.let { it1 ->
            ApiRequest.getCBLLogout(it1)
                    .lifeOnMain(this)
                    .subscribe({ it ->
                        LogsUtil.i(AppLogTagUtil.ALEXA_TAG, "logout status ${it.value}")
                        if (it.value == "0") {
                            var loginItem = AmazonAlexaLoginStatusUtil.getLoginItem(devItem.uuid)
                            uihd.postDelayed(kotlinx.coroutines.Runnable {
                                WAApplication.me.showProgDlg(activity, false, "")
                                AmazonAlexaLoginStatusUtil.addNoLoginItem(loginItem)
                                AmazonAlexaLoginStatusUtil.removeLoginItem(loginItem)
                                showLoading(false)
                                ApplicationViewModel.getInstance().setAlexaStatus(authDevice?.deviceCrc, false)
                                EventBus.getDefault().post(CommonEvent(1, CommonEvent.EVENT_REFRESH_COMPOSE_STATUE))
                                onBackKeyDown()
                            }, 5000)
                        } else {
                            WAApplication.me.showProgDlg(activity, false, "")
                        }
                    }, {
                        LogsUtil.i(AppLogTagUtil.ALEXA_TAG, "logout status failed. ${it?.localizedMessage}")
                        WAApplication.me.showProgDlg(activity, false, "")
                    })
        }
    }

    override fun initUtils() {
        super.initUtils()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventMessage(event: CommonEvent?) {
        Log.d(AlexaVoiceAssistantFragment::class.simpleName, "onEventMessage event:${event}")
        Log.d(AlexaVoiceAssistantFragment::class.simpleName, "enterFrom:${enterFrom}")

        if (!event?.name.equals(CommonEvent.EVENT_AMAZON_LOGIN)) return

        if (enterFrom == EnterFromType.TYPE_COMPOSE && activity is ChromecastAlexaComposeActivity) {
            var targetAct = activity as ChromecastAlexaComposeActivity

            if (targetAct.sourceType == ChromecastAlexaComposeActivity.SourceType.TYPE_COMPOSE) {
                targetAct.alexaSwitch(event?.value!!)
            } else if (targetAct.sourceType == ChromecastAlexaComposeActivity.SourceType.TYPE_ALEXA) {
                if (event?.value == 1) {
                    LogsUtil.d(AppLogTagUtil.DEVICE_TAG, "logEvent - EventName : showAlexaSetupOkDialog 1")
                    showAlexaSetupOkDialog()
                } else {
                    activity?.finish()
                }
            }
        } else if (enterFrom == EnterFromType.TYPE_SETTING) {
            if (event?.value == 1) {
                LogsUtil.d(AppLogTagUtil.DEVICE_TAG, "logEvent - EventName : showAlexaSetupOkDialog 2")
                showAlexaSetupOkDialog()
            } else {
//                FragTabUtils.popBack(activity)
            }
        } else {
            AmazonAlexaLoginStatusUtil.clearAll()
            requestStatus()
        }
    }

    private var alexaSetupReadyDialogFragment: AlexaSetupReadyDialogFragment? = null
    private fun showAlexaSetupOkDialog() {
        if (alexaSetupReadyDialogFragment == null) {
            alexaSetupReadyDialogFragment = AlexaSetupReadyDialogFragment()
            alexaSetupReadyDialogFragment?.onCloseListener = object : AlexaSetupReadyDialogFragment.OnCloseListener {
                override fun onClose() {
                    alexaSetupReadyDialogFragment?.dismiss()
                    //refresh event to other fragment
                    EventBus.getDefault().post(CommonEvent(1, CommonEvent.EVENT_REFRESH_COMPOSE_STATUE))
                    //check current enter type
                    if (enterFrom == EnterFromType.TYPE_COMPOSE && activity is ChromecastAlexaComposeActivity) {
                        var targetAct = activity as ChromecastAlexaComposeActivity
                        if (targetAct.sourceType == ChromecastAlexaComposeActivity.SourceType.TYPE_COMPOSE) {
                            targetAct.alexaSwitch(1)
                        }
                    } else {
//                        onBackKeyDown()
                    }
                }
            }
        }
        if (alexaSetupReadyDialogFragment?.isAdded == true) return

        activity?.supportFragmentManager?.let { alexaSetupReadyDialogFragment?.show(it, AlexaSetupReadyDialogFragment::class.java.simpleName) }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onVoiceLanguageChanged(event: VoiceLanguage?) {
        Log.d(AlexaVoiceAssistantFragment::class.simpleName, "onEventMessage:${event}")
        if (event != null && !TextUtils.isEmpty(event.language) && !TextUtils.isEmpty(event.countries)) {
            currentVoiceLanguage = event
            languageTextView?.text = event.language + "(${event.countries})"
        }
    }


    override fun onBackKeyDown() {
        super.onBackKeyDown()
        CoulsonManager.instance.reportCoulsonSetupData()
        dismissDlg()

        if (enterFrom == EnterFromType.TYPE_COMPOSE && activity is ChromecastAlexaComposeActivity) {
            activity?.finish()
        } else {
            FragTabUtils.popBack(activity)
        }
    }

    companion object {
        const val MAX_RETRY = 12
    }
}