package com.wifiaudio.view.pagesmsccontent.menu_settings

import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.widget.CollapsingToolBar
import com.skin.SkinResourcesID
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.DeviceCustomerSettingAction
import com.wifiaudio.action.DeviceSettingActionCallback
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.ContentSettingItemEvent
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.*
import com.wifiaudio.view.dlg.ExtendDialogNew
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLC4aPermissionStatus
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLChromecastOptIn
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import config.AppLogTagUtil
import config.GlobalUIConfig
import config.LogTags
import org.greenrobot.eventbus.EventBus

class ChromecastBuiltInManageDeviceFragment : FragTabBackBase() {

    var cview: View? = null

    var recyclerView: RecyclerView? = null
    var mAdapter: ChromecastBuiltInManageDeviceAdapter? = null

    var collapsingToolBar: CollapsingToolBar? = null
    private var joinDevList: ArrayList<ContentSettingItem> = ArrayList()//拼接list
    private var enabledDeviceList: ArrayList<ContentSettingItem> = ArrayList()//设置完成的列表
    private var availableDeviceList: ArrayList<ContentSettingItem> = ArrayList()//未设置的列表

    public fun setEnabledDevList(list: ArrayList<ContentSettingItem>) {
        enabledDeviceList = list
    }

    public fun setAvailableDevList(list: ArrayList<ContentSettingItem>) {
        availableDeviceList = list
    }

    var dataInfo: DataFragInfo? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.fragment_chromecast_built_in_manage_device, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()
        return cview
    }

    override fun onResume() {
        super.onResume()
        activity?.let {
            NavigationBarColorHelper.setNavigationBarColor(it)
        }
    }

    override fun initView() {
        super.initView()
        collapsingToolBar = cview?.findViewById(R.id.collapsing_toolbar)
        collapsingToolBar?.setNavigationListener(object : CollapsingToolBar.NavigationListener {
            override fun onBack() {
                onBackKeyDown()
            }

            override fun onNext() {
            }
        })
        recyclerView = cview?.findViewById(R.id.recycle_view)

        mAdapter = ChromecastBuiltInManageDeviceAdapter(activity)

        sortItems()
        fetchItemDataReportStatus()
        mAdapter?.setCurrList(joinDevList)
        recyclerView?.apply {
            adapter = mAdapter
        }
    }

    private fun fetchItemDataReportStatus() {
        enabledDeviceList.stream().forEach { it ->
            DeviceCustomerSettingAction.makeDeviceGetChromecastOptIn(it?.ip, object : DeviceSettingActionCallback {
                override fun onSuccess(content: String?) {
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "makeDeviceGetChromecastOptIn:Success: $content")
                    val chromecastOptIn = content?.let {
                        GsonParseUtil.instance.fromJson(it, JBLChromecastOptIn::class.java)
                    }
                    var index = joinDevList.indexOf(it)
                    if (index >= 0) {
                        it.optIn = TextUtils.equals(chromecastOptIn?.opt_in, "true")
                        it.bEnableClick = chromecastOptIn == null
                        joinDevList[index] = it
                        mAdapter?.notifyDataSetChanged()
                    }
                }

                override fun onFailure(e: Throwable?) {
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "makeDeviceGetChromecastOptIn:fail: ${e.toString()}")
                    var index = joinDevList.indexOf(it) as Int
                    if (index >= 0) {
                        it.bEnableClick = true
                        joinDevList[index] = it
                        mAdapter?.notifyDataSetChanged()
                    }
                }
            })
        }
    }

    //排序更新列表
    private fun sortItems() {

        if (joinDevList != null)
            joinDevList.clear()

        if (availableDeviceList != null && availableDeviceList.size > 0) {
            joinDevList.add(getGroupItems(false))
            joinDevList.addAll(availableDeviceList)
        }

        if(enabledDeviceList != null && enabledDeviceList.size > 0) {
            joinDevList.add(getGroupItems(true))
            enabledDeviceList.stream().forEach { it.bEnableClick = false }
            joinDevList.addAll(enabledDeviceList)
        }


    }

    private fun getGroupItems(enabled: Boolean): ContentSettingItem {

        var item = ContentSettingItem()
        if (enabled) {
            item.title = SkinResourcesUtils.getString("jbl_Enabled_devices")
            item.item_type = SETTING_ITEM_TYPE.NONE
            item.strBGName = ""
            item.sub_desc = SkinResourcesUtils.getString("manage_device_enable_item_sub_title")
        } else {
            item = ContentSettingItem()
            item.title = SkinResourcesUtils.getString("jbl_Available_devices")
            item.item_type = SETTING_ITEM_TYPE.NONE
            item.strBGName = ""
        }

        return item
    }

    override fun bindSlots() {
        super.bindSlots()


        mAdapter?.setOnItemClickListener(object :ChromecastBuiltInManageDeviceAdapter.IOnItemClickListener{
            override fun onItemClicked(pos: Int, item: ContentSettingItem?) {
                enableFromEnableList=false
                if (item != null) showChromecastDlg(item)
            }

            override fun onSwitchClicked(isChecked: Boolean, item: ContentSettingItem?) {
                enableFromEnableList=true
                if (!isChecked) {
                    optIn="false"
                    if (item != null) enableImproveChromecast(item)
                } else {
                    optIn="true"
                    if (item != null) enableImproveChromecast(item)
                }

            }
        })

    }

    var extDlg: ExtendDialogNew? = null
    var optIn: String? = "true"
    var enableFromEnableList:Boolean?=false
    private fun showChromecastDlg(item: ContentSettingItem) {
        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_chromecast_built_in)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View?) {
                        updateDlgViews(rootView, item)
                    }

                    override fun onDismissDlg() {
//                        dismissDlg()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
//                        dismissDlg()
                    }
                })
        }

        extDlg?.show()
    }

    private fun updateDlgViews(rootView: View?, item: ContentSettingItem) {

        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Chromecast_Built_In")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                dismissDlg()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {

            text = SkinResourcesUtils.getString("jbl_stream_music_from_hundreds_of_apps_to_your_products_via_chromecast_built_in")

//            setTextColor(GlobalUIConfig.color_normal)
//            alpha = 0.85f
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

        }

        var tvInfo2: TextView? = rootView?.findViewById(R.id.tv_info2)
        tvInfo2?.apply {

            var strHighLabel1 = SkinResourcesUtils.getString("jbl___Google_Terms_Of_Service__")
            var strHighLabel2 = SkinResourcesUtils.getString("jbl___Privacy_Policy__")
            var strInfoLabel = SkinResourcesUtils.getString("jbl_Agree_with___Google_Terms_Of_Service___and___Privacy_Policy___to_start_using_Chromecast_built_in_")
            var stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setUnderlineText(true)
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))
            var linkColor = WAApplication.me.getColor(R.color.fg_activate)
            stringUtil.setTextHighlight(
                arrayOf(strHighLabel1, strHighLabel2),
                linkColor
            )

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            stringUtil.setSpannableStrings {

                if (it == 0) {

                    var html =
                        AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_terms_url")

                    if(TextUtils.isEmpty(html))
                        return@setSpannableStrings

                    val intent = Intent()
                    intent.action = "android.intent.action.VIEW"
                    val url = Uri.parse(html)
                    intent.data = url
                    startActivity(intent)
                } else if (it == 1) {

                    var html =
                        AssetsProjectConfigParse.instance.getValueByKey("harmanbar_google_privacy_url")

                    if(TextUtils.isEmpty(html))
                        return@setSpannableStrings

                    val intent = Intent()
                    intent.action = "android.intent.action.VIEW"
                    val url = Uri.parse(html)
                    intent.data = url
                    startActivity(intent)
                }
            }

            text = stringUtil.spannableString
            highlightColor = Color.TRANSPARENT
//            setTextColor(GlobalUIConfig.color_ez_normal)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            movementMethod = LinkMovementMethod.getInstance()
        }

        val tvInfo3: TextView? = rootView?.findViewById(R.id.tv_info3)
        tvInfo3?.apply {
            visibility = View.VISIBLE
            val strHighLabel = SkinResourcesUtils.getString("text_here")
            val strInfoLabel = SkinResourcesUtils.getString("tap_here_to_enable_later")
            val linkColor = WAApplication.me.getColor(R.color.fg_activate)

            val stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setTextHighlight(strHighLabel, linkColor)
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))
            stringUtil.setSpannableString {
                dismissDlg()
            }
            text = stringUtil.spannableString
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)
            setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fixed_white))
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
            alpha = 0.8f
            movementMethod = LinkMovementMethod.getInstance()
        }

        var ivWarn: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivWarn?.apply {
            visibility = View.GONE
            setImageDrawable(SkinResourcesUtils.getDrawable("jbl_icon_stream_service"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {

            text = SkinResourcesUtils.getString("jbl_ENABLE_SERVICE")

            var tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                "btn_background_bg_s1",
                GlobalUIConfig.color_btn_normal,
                "btn_background_bg_s1",
                GlobalUIConfig.color_btn_press
            )
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
            background = tintDrawable
//            setTextColor(GlobalUIConfig.color_solid_btn_font_color)
            setOnClickListener {

                clickApprove(item)

                dismissDlg()
            }
        }
    }

    private fun dismissDlg() {
        extDlg?.apply {
            /*activity?.let { itActivity ->
                BarUtils.setStatusBarColor(itActivity, WAApplication.me.getColor(R.color.bg_L1))
            }*/
            dismissDlg()
            extDlg = null
        }
    }




     fun clickApprove(item: ContentSettingItem) {

        WAApplication.me.showProgDlg(activity, (15 * 1000).toLong(), SkinResourcesUtils.getString(""))

        var devItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(item.uuid)



        DeviceCustomerSettingAction.makeDeviceSetC4aPermissionStatus(devItem?.IP,
            "1", object : DeviceSettingActionCallback {
                override fun onSuccess(content: String?) {

                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "setC4aPermission:Success: $content")

                    val status = content?.let {
                        GsonParseUtil.instance.fromJson(it, JBLC4aPermissionStatus::class.java)
                    }

                    if (TextUtils.equals(status?.error_code, "0")) {

                        item.item_type = SETTING_ITEM_TYPE.OPTION_1

                        if (enabledDeviceList != null)
                            enabledDeviceList.add(item)

                        if (availableDeviceList != null) {
                            availableDeviceList.remove(item)
                        }
                    }

                    WAApplication.me.showProgDlg(activity, false, null)

                    createImproveChromecastDlg(item)
                }

                override fun onFailure(e: Throwable?) {

                    WAApplication.me.showProgDlg(activity, false, null)

                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "setC4aPermission:Failed: ${e?.localizedMessage}")

                    createImproveChromecastDlg(item)
                }
            })
    }

    private fun createImproveChromecastDlg(item: ContentSettingItem) {

        if (extDlg != null) {
            dismissDlg()
        }

        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_chromecast_built_in)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View?) {
                        updateImproveChromecastViews(rootView, item)
                    }

                    override fun onDismissDlg() {
//                        dismissDlg()
//                        updateList()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
//                        dismissDlg()
//                        updateList()
                    }
                })
        }

        extDlg?.show()
    }

    private fun googleHomeIntent(): Intent? {
        val packageManager =
            WAApplication.me.applicationContext.packageManager
        return packageManager.getLaunchIntentForPackage("com.google.android.apps.chromecast.app")
    }

    private fun updateImproveChromecastViews(rootView: View?, item: ContentSettingItem) {

        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        tvTitle?.apply {
            text = SkinResourcesUtils.getString("jbl_Help_Improve_Chromecast")
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)
        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                dismissDlg()
            }
        }

        var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
        tvInfo1?.apply {

            visibility = View.VISIBLE

            var strHighLabel1 = SkinResourcesUtils.getString("jbl_Learn_more")
            var strBody =
                SkinResourcesUtils.getString("jbl_SDo_you_want_to_help_improve_everyone_s_experience_by_sharing_device_stats_and_crash_reports_with_Go")

            var strInfoLabel = String.format(
                strBody, ""
            )

            var linkColor = WAApplication.me.getColor(R.color.fg_activate)

            var stringUtil = StringSpannableUtils()
            stringUtil.setTextBody(strInfoLabel)
            stringUtil.setUnderlineText(false)
            stringUtil.setHighTxtTypeface(FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold))
            stringUtil.setTextHighlight(
                arrayOf(strHighLabel1),
                linkColor
            )

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_regular)

            stringUtil.setSpannableStrings {

                if (it == 0) {

                    var intent = Intent()
                    intent.action = "android.intent.action.VIEW"
                    var strUrl = AssetsProjectConfigParse.instance.getValueByKey("harmanbar_learn_more_url")

                    if(TextUtils.isEmpty(strUrl))
                        return@setSpannableStrings

                    val url =
                        Uri.parse(strUrl)
                    intent.data = url
                    startActivity(intent)

                } else if (it == 1) {

                }
            }

            var linkDrawId = SkinResourcesID.getDrawableId("jbl_icon_spotify_link")
            val picStr = " <img src='$linkDrawId'/> "
            text = stringUtil.spannableString

            append(
                Html.fromHtml(
                    String.format(
                        "%s", picStr
                    ), MixTextImg.getImageGetterInstance(activity, linkColor), null
                )
            )

            highlightColor = Color.TRANSPARENT
//            setTextColor(GlobalUIConfig.color_ez_normal)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            alpha = 0.85f
            movementMethod = LinkMovementMethod.getInstance()
        }

        val tvInfo3: TextView? = rootView?.findViewById(R.id.tv_info3)
        tvInfo3?.apply {
            visibility = View.GONE
        }

        var ivWarn: ImageView? = rootView?.findViewById(R.id.img_logo)
        ivWarn?.apply {
            visibility = View.GONE
            setImageDrawable(SkinResourcesUtils.getDrawable("jbl_icon_stream_service"))
        }

        var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
        btnOK?.apply {

            text = SkinResourcesUtils.getString("jbl_YES__I_M_IN")
            val buttonColor=WAApplication.me.getColor(R.color.bg_inverse)
            var tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                "btn_background_bg_s1",
                buttonColor,
                "btn_background_bg_s1",
                buttonColor
            )
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
//            background = tintDrawable
            setTextColor(GlobalUIConfig.color_solid_btn_font_color)
            setOnClickListener {
                optIn="true"
                enableImproveChromecast(item)
                dismissDlg()
//                updateList()

            }
        }

        var btnCancel: Button? = rootView?.findViewById(R.id.btn_cancel)
        btnCancel?.apply {
            visibility = View.VISIBLE
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
            text = SkinResourcesUtils.getString("jbl_NO_THANKS")
            background = null
            ViewUtil.setViewAlphaClickListener(this)
            setOnClickListener {
                dismissDlg()
//                updateList()
                sortItems()
                fetchItemDataReportStatus()
                mAdapter?.notifyDataSetChanged()
            }
        }

    }

    private fun enableImproveChromecast(item: ContentSettingItem) {

        WAApplication.me.showProgDlg(activity, (30 * 1000).toLong(), SkinResourcesUtils.getString(""))

        var devItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(item.uuid)

        DeviceCustomerSettingAction.makeDeviceSetChromecastOptIn(
            devItem?.IP,
            optIn,
            object : DeviceSettingActionCallback {
                override fun onSuccess(content: String?) {

                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "SetChromecastOptIn:Success: $content")

                    WAApplication.me.showProgDlg(activity, false, null)
                    dismissDlg()
//                    updateList()
                    // there different action from parent dialog or open/close switch
                    if (enableFromEnableList == true) {
                        var index = joinDevList.indexOf(item)
                        if (index >= 0) {
                            item.optIn = optIn == "true"
                            mAdapter?.notifyItemChanged(index, R.id.switch_send_optin)
                        }
                    } else {
                        sortItems()
                        fetchItemDataReportStatus()
                        mAdapter?.notifyDataSetChanged()
                    }
                }

                override fun onFailure(e: Throwable?) {
                    LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "SetChromecastOptIn:Failed: ${e?.localizedMessage}")

                    WAApplication.me.showProgDlg(activity, false, null)
                    dismissDlg()
//                    updateList()
                }
            })
    }

    private fun updateList() {

        uihd.post {
            sortItems()
            mAdapter?.notifyDataSetChanged()
        }
    }

    override fun initUtils() {
        super.initUtils()
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        //需要发起观察者模式，上一页面需要更新图片点是否还有
        EventBus.getDefault().post(ContentSettingItemEvent())
        FragTabUtils.popBack(activity)
    }
}