package com.wifiaudio.view.pagesmsccontent.menu_settings

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.widget.CollapsingToolBar
import com.rxjava.rxlife.lifeOnMain
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.albuminfo.AlbumMetadataUpdater
import com.wifiaudio.model.albuminfo.MessageAlbumObject
import com.wifiaudio.model.albuminfo.MessageAlbumType
import com.wifiaudio.utils.NavigationBarColorHelper
import com.wifiaudio.utils.cbl.CBLStatus
import com.wifiaudio.utils.cloudRequest.rxhttp.ApiRequest
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLDataUtil
import com.wifiaudio.view.pagesmsccontent.ContainerActivity
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.AmazonAlexaLoginStatusUtil
import com.wifiaudio.view.pagesmsccontent.menu_settings.jblone.FragAmazonAlexaStatus
import config.LogTags
import java.util.*

class FragStreamingService : FragTabBackBase() {

    var cview: View? = null

    //    var headerView: View? = null
//    var vTitle: TextView? = null
//    var vback: Button? = null
//    var tv_label_1: TextView? = null
    var collapsingToolBar: CollapsingToolBar? = null
    var recyclerView: RecyclerView? = null
    var mAdapter: StreamServiceAdapter? = null

    var dataInfo: DataFragInfo? = null
    var currList: ArrayList<ContentSettingItem> = ArrayList()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.frag_streaming_service, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        bindSlots()
        initUtils()

        AlbumMetadataUpdater.me().addObserver(this)

        return cview
    }

    override fun onResume() {
        super.onResume()
        activity?.let {
            NavigationBarColorHelper.setNavigationBarColor(it)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AlbumMetadataUpdater.me().deleteObserver(this)
    }

    override fun initView() {
        super.initView()
        collapsingToolBar = cview?.findViewById(R.id.collapsing_toolbar)
        collapsingToolBar?.setTitleText(SkinResourcesUtils.getString("jbl_3rd_Party_Service"))
        collapsingToolBar?.setNavigationListener(object : CollapsingToolBar.NavigationListener {
            override fun onBack() {
                onBackKeyDown()
            }

            override fun onNext() {
            }
        })
        recyclerView = cview?.findViewById(R.id.recycle_view)

        initDatas()
        mAdapter = StreamServiceAdapter(activity)
        mAdapter?.setCurrList(currList)

        recyclerView?.apply {
            adapter = mAdapter
        }

        //getAmazonMRMStatus()
    }

    private fun initDatas() {

        if (currList == null)
            currList = ArrayList()
        else
            currList.clear()

        var item = ContentSettingItem()
        item.title = SkinResourcesUtils.getString("jbl_Manage_streaming_services")
        item.item_type = SETTING_ITEM_TYPE.NONE
        currList.add(item)

        item = ContentSettingItem()
        item.title = SkinResourcesUtils.getString("jbl_AirPlay")
        item.icon_name = "jbl_icon_airplay"
        item.sub_desc = SkinResourcesUtils.getString("jbl_Available_for_iOS_devices")
        item.bvisibleMore = false
        item.icon_more_name = "component_svg_icon_arrow_forward_square"
        item.item_type = SETTING_ITEM_TYPE.OPTION_1
        item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
        currList.add(item)

        if (!TextUtils.equals(dataInfo?.deviceItem?.devStatus?.region?.uppercase(), "CN")) {
            item = ContentSettingItem()
            item.title = SkinResourcesUtils.getString("jbl_Chromecast_Built_In")
            item.icon_name = "jbl_icon_google_chromecast"
            item.sub_desc = SkinResourcesUtils.getString("jbl_Available_from_supported_music_App")
            item.bvisibleMore = true
            item.icon_more_name = "component_svg_icon_arrow_forward_square"
            item.item_type = SETTING_ITEM_TYPE.OPTION_2
            item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
            currList.add(item)
        }

        if (!TextUtils.equals(dataInfo?.deviceItem?.devStatus?.region?.uppercase(), "CN")) {
            item = ContentSettingItem()
            item.title = SkinResourcesUtils.getString("jbl_Amazon_Alexa")
            item.icon_name = "component_svg_icon_alexa"
            item.sub_desc = SkinResourcesUtils.getString("jbl_Available_from_Amazon_music_App")
            item.bvisibleMore = true
            item.icon_more_name = "component_svg_icon_arrow_forward_square"
            item.item_type = SETTING_ITEM_TYPE.OPTION_4
            item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
            currList.add(item)
        }

        if (!TextUtils.equals(dataInfo?.deviceItem?.devStatus?.region?.uppercase(), "CN")) {
            item = ContentSettingItem()
            item.title = SkinResourcesUtils.getString("jbl_Spotify_Connect")
            item.icon_name = "component_svg_icon_spotify_connect"
            item.sub_desc = SkinResourcesUtils.getString("jbl_Availale_from_Spotify_native_App")
            item.bvisibleMore = true
            item.icon_more_name = "component_svg_icon_arrow_forward_square"
            item.item_type = SETTING_ITEM_TYPE.OPTION_3
            item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
            currList.add(item)
        }

        if (TextUtils.equals(dataInfo?.deviceItem?.devStatus?.region?.uppercase(), "CN")) {
            item = ContentSettingItem()
            item.title = SkinResourcesUtils.getString("QQ 音乐")
            item.icon_name = "jbl_icon_qqmusic"
            item.sub_desc = SkinResourcesUtils.getString("从QQ音乐App向音箱投放音乐")
            item.bvisibleMore = false
            item.icon_more_name = "component_svg_icon_arrow_forward_square"
            item.item_type = SETTING_ITEM_TYPE.OPTION_3
            item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
            currList.add(item)
        }

//        item = ContentSettingItem()
//        item.title = SkinResourcesUtils.getString("jbl_DLNA")
//        item.icon_name = "jbl_icon_dlna"
//        item.sub_desc = SkinResourcesUtils.getString("jbl_Available_from_supported_music_App")
//        item.bvisibleMore = false
//        item.icon_more_name = "jbl_icon_checked"
//        item.item_type = SETTING_ITEM_TYPE.OPTION_5
//        item.strBGName = JBLDataUtil.TYPE_ROUND_RECTANGLE
//        currList.add(item)
    }

    private fun getAmazonMRMStatus() {

        dataInfo?.deviceItem?.IP?.let { it1 ->

            ApiRequest.getCBLStatus(it1)
                .lifeOnMain(this)
                .subscribe({
                    if (it.state == CBLStatus.REQUESTING_TOKEN || it.state == CBLStatus.REFRESHING_TOKEN)
                        updateAmazonMRMStatus(true)
                    else
                        updateAmazonMRMStatus(false)
                }, { updateAmazonMRMStatus(false) })
        }
    }

    private fun updateAmazonMRMStatus(login: Boolean) {

        for (i in 0 until currList.size) {
            var item = currList[i]
            if (item.item_type == SETTING_ITEM_TYPE.OPTION_4) {
                if (login)
                    item.sub_desc = SkinResourcesUtils.getString("jbl_Enabled")
                else
                    item.sub_desc = SkinResourcesUtils.getString("jbl_Select_to_enable")

                mAdapter?.notifyItemChanged(i)
                break
            }
        }
    }



    override fun bindSlots() {
        super.bindSlots()


        mAdapter?.setOnItemClickListener { pos, item ->

            when (item.title) {
                SkinResourcesUtils.getString("jbl_Amazon_Alexa") -> {

                    AmazonAlexaLoginStatusUtil.clearAll()

                    var vfrag = FragAmazonAlexaStatus()
                    vfrag.dataInfo = dataInfo
                    vfrag.enterFrom = EnterFromType.TYPE_NONE
                    dataInfo?.frameId?.let { FragTabUtils.replaceFrag(activity, it, vfrag, true) }
                }
            }
        }
    }

    override fun initUtils() {
        super.initUtils()
//        vTitle?.setTextColor(WAApplication.mResources.getColor(R.color.fg1))
//
//        if (headerView != null) {
//            headerView!!.setBackgroundColor(GlobalUIConfig.color_ez_navigationbar_bg)
//        }
//        cview?.setBackgroundColor(WAApplication.mResources.getColor(R.color.color_D7D9DD))
//
//        val backDraw = SkinResourcesUtils.getTintDrawable(
//            WAApplication.me,
//            "svg_icon_rect_back",
//            WAApplication.mResources.getColor(R.color.fg1),
//            "svg_icon_rect_back",
//            WAApplication.mResources.getColor(R.color.fg1)
//        )
//        if (backDraw != null) {
//            backDraw.setBounds(0, 0, backDraw.minimumWidth, backDraw.minimumHeight)
//            vback?.setCompoundDrawables(backDraw, null, null, null)
//        }

//        StatusBarUtils.setPageStatusBarPaddingTop(cview, true)
//        StatusBarUtils.setCustomStatusBarColor(
//            activity,
//            true,
//            GlobalUIConfig.color_navigationbar_bg
//        )
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()

        if (activity == null)
            return

        if (activity is ContainerActivity) {

            uihd?.removeCallbacksAndMessages(null)
            FragTabUtils.clearFragmentStack(activity)
            activity?.finish()
        } else
            FragTabUtils.popBack(activity)
    }

    override fun update(observable: Observable?, data: Any?) {
        super.update(observable, data)

        if (data is MessageAlbumObject) {
            if (data.type == MessageAlbumType.TYPE_ALARM_CONTEXT_CHANGED) {

                uihd.post {
                    //getAmazonMRMStatus()
                }
            }
        }
    }
}