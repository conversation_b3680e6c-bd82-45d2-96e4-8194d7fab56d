package com.wifiaudio.view.pagesmsccontent.menu_settings.jblone

import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.widget.CollapsingToolBar
import com.rxjava.rxlife.life
import com.rxjava.rxlife.lifeOnMain
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.ContentSettingItemEvent
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.albuminfo.AlbumMetadataUpdater
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.NavigationBarColorHelper
import com.wifiaudio.utils.ViewUtil
import com.wifiaudio.utils.cloudRequest.rxhttp.ApiRequest
import com.wifiaudio.view.dlg.ExtendDialogNew
import com.wifiaudio.view.pagesmsccontent.FragTabBackBase
import com.wifiaudio.view.pagesmsccontent.FragTabUtils
import com.wifiaudio.view.pagesmsccontent.menu_settings.ContentSettingItem
import com.wifiaudio.view.pagesmsccontent.menu_settings.AlexaLoginWebViewFragment
import com.wifiaudio.view.pagesmsccontent.menu_settings.SETTING_ITEM_TYPE
import config.AppLogTagUtil
import config.GlobalUIConfig
import io.reactivex.Flowable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.TimeUnit

class FragAlexaManageDevice : FragTabBackBase() {

    var cview: View? = null

    var headerView: View? = null
    private var vback: Button? = null
    var tv_label_1: TextView? = null
    var recyclerView: RecyclerView? = null
    var mAdapter: AlexaManageDeviceAdapter? = null

    var url: String? = null //logout验证url
    private val mCBLDisposable = CompositeDisposable()

    private var alexaSettingList: ArrayList<ContentSettingItem> = ArrayList()

    private var joinDevList: ArrayList<ContentSettingItem> = ArrayList()//拼接list

    var dataInfo: DataFragInfo? = null
    var collapsingToolBar: CollapsingToolBar? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        cview = inflater.inflate(R.layout.fragment_chromecast_built_in_manage_device, null)

        initView()
        bindSlots()
        initUtils()
        return cview
    }

    override fun onResume() {
        super.onResume()
        activity?.let {
            NavigationBarColorHelper.setNavigationBarColor(it)
        }
    }

    override fun initView() {
        super.initView()
        collapsingToolBar = cview?.findViewById(R.id.collapsing_toolbar)

        collapsingToolBar?.setTitleText(SkinResourcesUtils.getString("jbl_Manage_Device"))
        collapsingToolBar?.setNavigationListener(object : CollapsingToolBar.NavigationListener {
            override fun onBack() {
                onBackKeyDown()
            }

            override fun onNext() {
            }
        })
        recyclerView = cview?.findViewById(R.id.recycle_view)
        headerView = cview?.findViewById(R.id.vheader)
        tv_label_1 = cview?.findViewById(R.id.tv_label_1)
        vback = cview?.findViewById(R.id.vback)

        tv_label_1?.apply {
            text = SkinResourcesUtils.getString("jbl_Manage_Device")

            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_extrabold)


        }

        mAdapter = activity?.let { AlexaManageDeviceAdapter(it) }

        initAlexaSettingList()
        sortItems()

        mAdapter?.currList = joinDevList
        recyclerView?.apply {
            adapter = mAdapter
        }
    }

    private fun initAlexaSettingList() {

        alexaSettingList.clear()

        var item = AlexaStatusItem()
        item.title = SkinResourcesUtils.getString("alexa_Choose_Alexa_Language")
        item.item_type = SETTING_ITEM_TYPE.OPTION_1
        item.bvisibleMore = true
        item.icon_more_name = "component_svg_icon_arrow_forward_square"
        alexaSettingList.add(item)

        item = AlexaStatusItem()
        item.title = SkinResourcesUtils.getString("jbl_Sign_out_Alexa")
        item.item_type = SETTING_ITEM_TYPE.OPTION_2
        item.bvisibleMore = true
        item.icon_more_name = "jbl_icon_alexa_logout"
        alexaSettingList.add(item)
    }

    //排序更新列表
    private fun sortItems() {

        joinDevList.clear()

        if (AmazonAlexaLoginStatusUtil.getLoginList().size > 0) {
            joinDevList.add(getGroupItems(true))
            joinDevList.addAll(AmazonAlexaLoginStatusUtil.getLoginList())
        }

        if (AmazonAlexaLoginStatusUtil.getNoLoginList().size > 0) {
            joinDevList.add(getGroupItems(false))
            joinDevList.addAll(AmazonAlexaLoginStatusUtil.getNoLoginList())
        }

    }

    private fun getGroupItems(enabled: Boolean): ContentSettingItem {

        var item = ContentSettingItem()
        if (enabled) {
            item.title = SkinResourcesUtils.getString("jbl_Enabled_devices")
            item.item_type = SETTING_ITEM_TYPE.NONE
            item.strBGName = ""
        } else {
            item = ContentSettingItem()
            item.title = SkinResourcesUtils.getString("jbl_Available_devices")
            item.item_type = SETTING_ITEM_TYPE.NONE
            item.strBGName = ""
        }

        return item
    }

    override fun bindSlots() {
        super.bindSlots()

        vback?.setOnClickListener {
            onBackKeyDown()
        }

        mAdapter?.setOnItemClickListener(object : AlexaManageDeviceAdapter.IOnItemClickListener {

            override fun onItemClicked(pos: Int, item: ContentSettingItem) {

                if (item.item_type == SETTING_ITEM_TYPE.OPTION_1) {
                    //弹框设置
                    //showAlexaSettingDlg(item)
                    showAlexaLogoutDlg(item)
                } else {
                    showAlexaLoginDlg(item)
                }
            }
        })
    }

    private fun showAlexaSettingDlg(item: ContentSettingItem) {

        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_alexa_device_setting)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View?) {
                        updateDlgViews(1, rootView, item)
                    }

                    override fun onDismissDlg() {
                        dismissDlg()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                        dismissDlg()
                    }
                })
        }

        extDlg?.show()
    }

    var extDlg: ExtendDialogNew? = null

    private fun showAlexaLoginDlg(item: ContentSettingItem) {
        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_alexa_status)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View?) {
                        updateDlgViews(2, rootView, item)
                    }

                    override fun onDismissDlg() {
                        dismissDlg()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                        dismissDlg()
                    }
                })
        }

        extDlg?.show()
    }

    private fun updateDlgViews(dlgType: Int, rootView: View?, rootItem: ContentSettingItem) {

        var tvTitle: TextView? = rootView?.findViewById(R.id.tv_title)
        if(tvTitle == null)
            tvTitle = rootView?.findViewById(R.id.ble_title)

        tvTitle?.apply {
            text = when (dlgType) {
                1 -> SkinResourcesUtils.getString("jbl_Edit")
                2 -> SkinResourcesUtils.getString("jbl_Amazon_Alexa")
                3 -> SkinResourcesUtils.getString("jbl_Would_you_like_to_Sign_Out_")
                else -> ""
            }
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

            setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
//            setTextColor(GlobalUIConfig.color_navigationbar_title)
        }

        var btnClose: ImageView? = rootView?.findViewById(R.id.iv_close)
        btnClose?.apply {
            setOnClickListener {
                dismissDlg()
            }
        }

        when (dlgType) {
            1 -> {

                var recyclerView: RecyclerView? = rootView?.findViewById(R.id.recycle_view)

                var tmpAdapter = activity?.let { AlexaSettingEditAdapter(it) }

                tmpAdapter?.currList = alexaSettingList
                recyclerView?.apply {
                    adapter = tmpAdapter
                }

                tmpAdapter?.setOnItemClickListener(object :
                    AlexaSettingEditAdapter.IOnItemClickListener {
                    override fun onItemClicked(pos: Int, item: ContentSettingItem?) {

                        dismissDlg()

                        uihd.post {

                            if (item?.item_type == SETTING_ITEM_TYPE.OPTION_1) {

                                var devItem =
                                    WAUpnpDeviceManager.me().getDeviceItemByuuid(rootItem.uuid)
                                dataInfo?.deviceItem = devItem

                                var vfrag = VoiceLanguageSettingFragment()
                                vfrag.dataInfo = dataInfo
                                vfrag.bSettingLanguageType = 1
                                dataInfo?.frameId?.let {
                                    FragTabUtils.replaceFrag(
                                        activity,
                                        it,
                                        vfrag,
                                        true
                                    )
                                }
                            } else {
                                showAlexaLogoutDlg(rootItem)
                            }

                        }
                    }
                })
            }
            2 -> {

                var tvInfo1: TextView? = rootView?.findViewById(R.id.tv_info1)
                tvInfo1?.apply {
                    text = SkinResourcesUtils.getString("jbl_Login_your_Alexa_account_to_start_using_Alexa_MRM_")
//                    setTextColor(GlobalUIConfig.color_ez_normal)
                    setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_16))
                    typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)
                }

                var ivWarn: ImageView? = rootView?.findViewById(R.id.img_logo)
                ivWarn?.apply {
                    setImageDrawable(SkinResourcesUtils.getDrawable("jbl_one_amazon_alexa_logo"))
                }

                var btnOK: Button? = rootView?.findViewById(R.id.btn_ok)
                btnOK?.apply {

                    text = SkinResourcesUtils.getString("jbl_LOGIN_ALEXA")

                    var tintDrawable: Drawable? = SkinResourcesUtils.getTintDrawable(
                        "btn_background_bg_s1",
                        GlobalUIConfig.color_btn_normal,
                        "btn_background_bg_s1",
                        GlobalUIConfig.color_btn_press
                    )
                    typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

                    setTextSize(0, WAApplication.mResources.getDimension(R.dimen.font_14))
                    background = tintDrawable
//                    setTextColor(GlobalUIConfig.color_solid_btn_font_color)
                    setOnClickListener {

                        dismissDlg()

                        uihd.post {

                            var devItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(rootItem.uuid)
                            dataInfo?.deviceItem = devItem


                            var vfrag = AlexaLoginWebViewFragment()
                            vfrag.dataInfo = dataInfo

                            dataInfo?.frameId?.let {
                                FragTabUtils.addFrag(activity, it, vfrag, false)
                            }
                        }
                    }
                }
            }
            3 -> {
                var btn_restore: Button? = rootView?.findViewById(R.id.btn_restore)
                var btn_cancel: Button? = rootView?.findViewById(R.id.btn_cancel)

                var ivBack: ImageView? = rootView?.findViewById(R.id.iv_back)

                btn_restore?.apply {
                    typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

                    setTextSize(
                        TypedValue.COMPLEX_UNIT_PX,
                        WAApplication.mResources.getDimension(R.dimen.font_14)
                    )
                    text = SkinResourcesUtils.getString("jbl_SIGN_OUT")
                    val tintDrawable = SkinResourcesUtils.getTintListDrawable(
                        "btn_background_bg_s1", GlobalUIConfig.color_btn_normal, GlobalUIConfig.color_btn_press
                    )
                    background = tintDrawable
//                    setTextColor(GlobalUIConfig.color_solid_btn_font_color)
                }

                btn_cancel?.apply {
                    typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_semibold)

                    setTextSize(
                        TypedValue.COMPLEX_UNIT_PX,
                        WAApplication.mResources.getDimension(R.dimen.font_14)
                    )
                    text = SkinResourcesUtils.getString("jbl_CANCEL")
//                    setTextColor(GlobalUIConfig.color_info_normal)
                }

                ivBack?.apply{
                    visibility = View.GONE
                }

                btnClose?.apply {
                    visibility = View.GONE
                }

                ViewUtil.setViewAlphaClickListener(btn_cancel)
                ViewUtil.setViewAlphaClickListener(btn_restore)
                btn_cancel?.setOnClickListener {
                    dismissDlg()
                }

                btn_restore?.setOnClickListener {

                    dismissDlg()

                    logout(rootItem)
                }
            }
            else -> {

            }
        }
//        updateDlgContainerBG(rootView)
    }



    private fun showLoading(showLoading: Boolean) {
        WAApplication.me.showProgDlg(activity, showLoading, "")
    }

    private fun logout(rootItem: ContentSettingItem) {

        showLoading(true)

        var devItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(rootItem.uuid)
        dataInfo?.deviceItem = devItem

        dataInfo?.deviceItem?.IP?.let { it1 ->

            ApiRequest.getCBLLogout(it1)
                .lifeOnMain(this)
                .subscribe({ it ->

                    LogsUtil.i(AppLogTagUtil.ALEXA_TAG, "logout status ${it.value}")

                    if (it.value == "0")
                        checkLogoutStatus(rootItem)
                    else
                        updateLogoutStatus(false)
                }, {
                    LogsUtil.i(
                        AppLogTagUtil.ALEXA_TAG,
                        "logout status failed. ${it?.localizedMessage}"
                    )
                    updateLogoutStatus(false)
                }
                )
        }
    }

    /**
     * 刷新列表
     */
    private fun updateList() {

        uihd.post {

            sortItems()
            mAdapter?.notifyDataSetChanged()

        }
    }

    fun updateLogoutStatus(success: Boolean) {

        showLoading(false)

        if (success) {
            AlbumMetadataUpdater.me().notifyAlarmContextChanged("")

            //刷新列表
        }
    }

    private fun checkLogoutStatus(rootItem: ContentSettingItem) {

        mCBLDisposable.add(
            Flowable.intervalRange(0, 6, 0, 5, TimeUnit.SECONDS, Schedulers.io())
                .doOnComplete {
                    updateLogoutStatus(false)
                }
                .life(this)
                .subscribe {

                    dataInfo?.deviceItem?.IP?.let { it1 ->

                        ApiRequest.getCBLStatus(it1)
                            .life(this)
                            .subscribe({ status ->
                                LogsUtil.d(AppLogTagUtil.ALEXA_TAG, "getCBLCode$it status:$status")
                                url = status.msg?.takeIf { it.contains("cbl-code") }
                                if (!TextUtils.isEmpty(url)) {
                                    updateLogoutStatus(true)

                                    rootItem.sub_desc = SkinResourcesUtils.getString("jbl_Enable")
                                    rootItem.item_type = SETTING_ITEM_TYPE.OPTION_2

                                    AmazonAlexaLoginStatusUtil.addNoLoginItem(rootItem)
                                    AmazonAlexaLoginStatusUtil.removeLoginItem(rootItem)

                                    updateList()

                                    mCBLDisposable.dispose()
                                }
                            }, { updateLogoutStatus(false) })
                    }
                })
    }

    private fun showAlexaLogoutDlg(rootItem: ContentSettingItem?) {

        if (extDlg == null) {
            extDlg = ExtendDialogNew(activity)
                .build(R.layout.dlg_alexa_device_logout)
                .setCancelable(true)
                .setCanceledOnTouchOutside(false)
                .setCustomReloadData(object : ExtendDialogNew.ExtendDialogInterface {
                    override fun updateView(rootView: View) {
                        if (rootItem != null) {
                            updateDlgViews(3, rootView, rootItem)
                        }
                    }

                    override fun onDismissDlg() {
                        dismissDlg()
                    }

                    override fun onKeyListener(type: Int, keyCode: Int, event: KeyEvent?) {
                        dismissDlg()
                    }
                })
        }

        extDlg?.show()
    }

    fun updateDlgContainerBG(cview: View?) {

        if (cview == null)
            return

        val container = cview?.findViewById<RelativeLayout>(R.id.container) ?: return

        var layoutBG: GradientDrawable? =
            if (container != null) container.background as GradientDrawable? else null
        if (layoutBG == null)
            return

        val startColor = GlobalUIConfig.dlg_ble_bg_start_color
        val centerColor = GlobalUIConfig.dlg_ble_bg_start_color
        val endColor = GlobalUIConfig.dlg_ble_bg_start_color
        layoutBG?.colors = intArrayOf(startColor, centerColor, endColor)
        val radius = WAApplication.mResources.getDimension(R.dimen.width_15)
        if (layoutBG != null) {
            layoutBG.cornerRadii = floatArrayOf(
                radius,
                radius,
                radius,
                radius,
                0.0f,
                0.0f,
                0.0f,
                0.0f
            )
        }
        if (container != null) {
            container.background = layoutBG
        }
    }

    private fun dismissDlg() {
        /*activity?.let { itActivity ->
            BarUtils.setStatusBarColor(itActivity, WAApplication.me.getColor(R.color.bg_L1))
        }*/
        extDlg?.apply {
            dismissDlg()
            extDlg = null
        }
    }

    override fun initUtils() {
        super.initUtils()

//        if (headerView != null) {
//            headerView!!.setBackgroundColor(GlobalUIConfig.color_ez_navigationbar_bg)
//        }
//
//        var bgDraw: Drawable? = null
//        bgDraw = WAApplication.mResources.getDrawable(R.drawable.launchflow_launchimage_001_an)
//
//        if (bgDraw != null) cview?.background = bgDraw
//
//        val backDraw = SkinResourcesUtils.getTintDrawable(
//            WAApplication.me,
//            "svg_icon_rect_back",
//            GlobalUIConfig.color_ez_navigationbar_back_default,
//            "svg_icon_rect_back",
//            GlobalUIConfig.color_ez_navigationbar_back_select
//        )
//        if (backDraw != null) {
//            backDraw.setBounds(0, 0, backDraw.minimumWidth, backDraw.minimumHeight)
//            vback?.setCompoundDrawables(backDraw, null, null, null)
//        }
//
//        StatusBarUtils.setPageStatusBarPaddingTop(cview, true)
//        StatusBarUtils.setCustomStatusBarColor(
//            activity,
//            true,
//            GlobalUIConfig.color_info_navigationbar_bg
//        )
    }

    override fun onBackKeyDown() {
        super.onBackKeyDown()
        //需要发起观察者模式，上一页面需要更新图片点是否还有
        EventBus.getDefault().post(ContentSettingItemEvent())
        FragTabUtils.popBack(activity)
    }
}