package com.wifiaudio.view.oobe

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.res.ResourcesCompat
import com.harman.CoulsonManager
import com.harman.EventUtils
import com.harman.bar.app.R
import com.harman.widget.CustomTypefaceSpan
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import config.LogTags

class AlexaSetupReadyDialogFragment : BaseBottomDialogFragment() {

    private var rootView: View? = null
    private var touchOutsideView: View? = null
    private var tvTips: TextView? = null

    var onCloseListener: OnCloseListener?=null
    @SuppressLint("InflateParams")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        rootView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_alexa_setup_ready, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        return rootView
    }

    override fun onStart() {
        super.onStart()
        dialog?.let { itDialog ->
            val window = itDialog.window
            window?.setBackgroundDrawable(ColorDrawable(ActivityCompat.getColor(requireContext(), R.color.bg_regular_opacity_80)))
        }
        initView()
    }

    private fun initView() {
        touchOutsideView = rootView?.findViewById(R.id.view_touch_outside)
        tvTips = rootView?.findViewById(R.id.tv_tips)
        touchOutsideView?.setOnClickListener(this)
        rootView?.findViewById<Button>(R.id.btn_ok)?.setOnClickListener(this)
        val learnMore = requireActivity().getString(R.string.amazon_alexa_app)
        val discoverMoreDesc = requireActivity().getString(R.string.set_up_alexa_va_go_app)
        val startIndex = discoverMoreDesc.indexOf(learnMore)
        val endIndex = discoverMoreDesc.lastIndex
        val ssb = SpannableStringBuilder(discoverMoreDesc)
        if (startIndex != -1) {
            ssb.setSpan(ForegroundColorSpan(requireActivity().getColor(R.color.fg_activate)), startIndex,  endIndex + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            val font = ResourcesCompat.getFont(requireContext(), R.font.poppins_semibold)
            ssb.setSpan(CustomTypefaceSpan(requireContext(), font!!), startIndex,  endIndex + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            val userClickSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    if (hasInstalledAlexa(false)) {
                        hasInstalledAlexa(true)
                    } else {
                        showDownload()
                    }
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                }
            }
            ssb.setSpan(userClickSpan, startIndex, startIndex + learnMore.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        tvTips?.text = ssb
        tvTips?.movementMethod = LinkMovementMethod.getInstance()

    }

    private fun showDownload() {
        val intent = Intent()
        intent.action = "android.intent.action.VIEW"
        val url = Uri.parse("https://play.google.com/store/apps/details?id=com.amazon.dee.app")
        intent.data = url
        startActivity(intent)
    }


    private fun hasInstalledAlexa(openApp: Boolean): Boolean {
        val packageManager = WAApplication.me.applicationContext.packageManager
        var intent: Intent? = null
        intent = packageManager.getLaunchIntentForPackage("com.amazon.dee.app")
        if (intent != null) {
            if (openApp) {
                startActivity(intent)
            }
            return true
        }
        return false
    }


    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.view_touch_outside -> {
                dismissAllowingStateLoss()
            }
            R.id.btn_ok->{
                dismissAllowingStateLoss()
                onCloseListener?.onClose()
            }
        }
    }

    interface OnCloseListener {
        fun onClose()
    }
}