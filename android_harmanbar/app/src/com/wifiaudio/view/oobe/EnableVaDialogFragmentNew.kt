package com.wifiaudio.view.oobe

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.harman.CoulsonManager
import com.harman.EventUtils
import com.harman.bar.app.R
import com.harman.discover.bean.OneDevice
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.utils.ClickHelper
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.SvgDrawableUtil
import com.wifiaudio.view.pagesmsccontent.home.ChromecastAlexaComposeActivity
import config.LogTags

class EnableVaDialogFragmentNew(private val oneDevice: OneDevice) : BaseBottomDialogFragment() {

//    var authDevice: AuthDevice? = null
//    var dataInfo: DataFragInfo? = null
    var isOObe: Boolean? = false
    private var rootView: View? = null
    private var enableVaView: View? = null
    private var setupVaView: View? = null
    private var touchOutsideView: View? = null
    private var tvLearnMore: TextView? = null
    private var tvLater: TextView? = null
    private var btnSetup: Button? = null
    private var contentLayout: FrameLayout? = null

    private var ivBack: ImageView? = null
    private var ivVaSetUpLogo: ImageView? = null
    private var alexaAndGoogleLayout: ConstraintLayout? = null
    private var alexaLayout: ConstraintLayout? = null
    private var googleLayout: ConstraintLayout? = null
    private var newLabel: RelativeLayout? = null
    private var ivAlexaSelectHook: ImageView? = null
    private var ivGoogleSelectHook: ImageView? = null
    private var btnContinue: Button? = null
    private var leftInAni: Animation? = null
    private var leftOutAni: Animation? = null
    private var rightInAni: Animation? = null
    private var rightOutAni: Animation? = null

    private var sourceType = ChromecastAlexaComposeActivity.SourceType.TYPE_COMPOSE

    @SuppressLint("InflateParams")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        rootView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_enable_va_new, null)
        enableVaView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_enable_va_add, null)
        setupVaView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_set_up_va_add, null)
        initRootView()
        initEnableVaView()
        contentLayout?.addView(enableVaView)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        return rootView
    }

    override fun onStart() {
        super.onStart()
        dialog?.let { itDialog ->
            itDialog.setOnKeyListener(object : DialogInterface.OnKeyListener {
                override fun onKey(dialog: DialogInterface?, keyCode: Int, event: KeyEvent?): Boolean {
                    if (keyCode == KeyEvent.KEYCODE_BACK) {
                        CoulsonManager.instance.reportCoulsonSetupData()
                        dismissAllowingStateLoss()
                        return true
                    }
                    return false
                }
            })
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        leftInAni?.cancel()
        leftOutAni?.cancel()
        rightInAni?.cancel()
        rightOutAni?.cancel()
    }

    private fun initRootView() {
        touchOutsideView = rootView?.findViewById(R.id.view_touch_outside)
        touchOutsideView?.setOnClickListener(this)
        contentLayout = rootView?.findViewById(R.id.layout_content)
        ivVaSetUpLogo = rootView?.findViewById(R.id.iv_va_set_up_logo)
        val randoms = (0..100).random()
        if (randoms % 2 == 0) {
            ivVaSetUpLogo?.setImageResource(R.mipmap.icon_va_set_up_google_up)
        } else {
            ivVaSetUpLogo?.setImageResource(R.mipmap.icon_va_set_up_alexa_up)
        }

        leftInAni = AnimationUtils.loadAnimation(activity, R.anim.animation_left_in)
        leftOutAni = AnimationUtils.loadAnimation(activity, R.anim.animation_left_out)
        rightInAni = AnimationUtils.loadAnimation(activity, R.anim.animation_right_in)
        rightOutAni = AnimationUtils.loadAnimation(activity, R.anim.animation_right_out)
    }

    private fun initEnableVaView() {
        tvLearnMore = enableVaView?.findViewById(R.id.tv_learn_more)
        tvLater = enableVaView?.findViewById(R.id.tv_later)
        btnSetup = enableVaView?.findViewById(R.id.btn_set_up)
        tvLearnMore?.setOnClickListener(this)
        tvLater?.setOnClickListener(this)
        btnSetup?.setOnClickListener(this)
    }

    private fun addEnableVaView() {
        initEnableVaView()
        enableVaView?.animation = leftInAni
        leftInAni?.cancel()
        leftInAni?.start()
        setupVaView?.animation = rightOutAni
        rightOutAni?.cancel()
        rightOutAni?.start()
        contentLayout?.removeView(setupVaView)
        contentLayout?.addView(enableVaView)
    }

    private fun initSetupView() {
        alexaAndGoogleLayout = setupVaView?.findViewById(R.id.layout_alexa_and_google)
        alexaLayout = setupVaView?.findViewById(R.id.layout_alexa)
        googleLayout = setupVaView?.findViewById(R.id.layout_google)
        newLabel = setupVaView?.findViewById(R.id.layout_new_label)
        ivBack = setupVaView?.findViewById(R.id.iv_back)
        ivAlexaSelectHook = setupVaView?.findViewById(R.id.iv_alexa_select_hook)
        ivGoogleSelectHook = setupVaView?.findViewById(R.id.iv_google_select_hook)
        btnContinue = setupVaView?.findViewById(R.id.btn_continue)
        ivBack?.setOnClickListener(this)
        btnContinue?.setOnClickListener(this)
        alexaAndGoogleLayout?.setOnClickListener(this)
        alexaLayout?.setOnClickListener(this)
        googleLayout?.setOnClickListener(this)
    }

    private fun addSetupVaView() {
        initSetupView()
        enableVaView?.animation = leftOutAni
        leftOutAni?.cancel()
        leftOutAni?.start()
        setupVaView?.animation = rightInAni
        rightInAni?.cancel()
        rightInAni?.start()
        contentLayout?.removeView(enableVaView)
        contentLayout?.addView(setupVaView)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.tv_learn_more -> {
                gotoLearnMore()
            }

            R.id.view_touch_outside -> {
                dismissAllowingStateLoss()
            }

            R.id.tv_later -> {
                dismissAllowingStateLoss()
            }

            R.id.btn_set_up -> {
                addSetupVaView()
            }

            R.id.btn_continue -> {
                var intent = Intent(context, ChromecastAlexaComposeActivity::class.java)
                intent.putExtra("source_type", sourceType.name)

                intent.putExtra("is_oobe", isOObe)
                intent.putExtra("Bundle_UUID", oneDevice.UUID)
                startActivity(intent)
                dismissAllowingStateLoss()

            }

            R.id.iv_back -> {
                addEnableVaView()
            }

            R.id.layout_alexa_and_google -> {
                sourceType = ChromecastAlexaComposeActivity.SourceType.TYPE_COMPOSE
                updateSelectStatus(Alexa_Google)
            }

            R.id.layout_alexa -> {
                sourceType = ChromecastAlexaComposeActivity.SourceType.TYPE_ALEXA
                updateSelectStatus(Alexa)
            }

            R.id.layout_google -> {
                sourceType = ChromecastAlexaComposeActivity.SourceType.TYPE_GOOGLE
                updateSelectStatus(Google)
            }
        }
    }

    private fun gotoLearnMore() {
        if (ClickHelper.isFastClick()) {
            return
        }
        /*val learnMoreDialogFragment = LearnMoreDialogFragment()
        if (!learnMoreDialogFragment.isAdded) {
            learnMoreDialogFragment.show(requireActivity().supportFragmentManager, LearnMoreDialogFragment::class.java.simpleName)
        }*/

        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        val url = Uri.parse("https://www.jbl.com/mae")
        intent.data = url
        startActivity(intent)
    }

    private fun updateSelectStatus(select: Int) {
        val drawable = ContextCompat.getDrawable(requireContext(), R.drawable.icon_va_set_up_select_hook)
        when (select) {
            Alexa_Google -> {
                alexaAndGoogleLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_activate)
                alexaLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_secondary)
                googleLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_secondary)
                newLabel?.setBackgroundResource(R.drawable.shape_va_setup_text_select_bg)
                drawable?.let { drawableRes ->
                    SvgDrawableUtil.tintDrawable(drawable, ActivityCompat.getColor(requireContext(), R.color.fg_activate))
                    ivAlexaSelectHook?.setImageDrawable(drawableRes)
                    ivGoogleSelectHook?.setImageDrawable(drawableRes)
                }
            }

            Alexa -> {
                alexaAndGoogleLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_secondary)
                alexaLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_activate)
                googleLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_secondary)
                newLabel?.setBackgroundResource(R.drawable.shape_va_setup_text_unselect_bg)
                drawable?.let { drawableRes ->
                    SvgDrawableUtil.tintDrawable(drawable, ActivityCompat.getColor(requireContext(), R.color.fg_disabled))
                    ivAlexaSelectHook?.setImageDrawable(drawableRes)
                    ivGoogleSelectHook?.setImageDrawable(drawableRes)
                }
            }

            Google -> {
                alexaAndGoogleLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_secondary)
                alexaLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_secondary)
                googleLayout?.setBackgroundResource(R.drawable.radius_large_stroke_fg_activate)
                newLabel?.setBackgroundResource(R.drawable.shape_va_setup_text_unselect_bg)
                drawable?.let { drawableRes ->
                    SvgDrawableUtil.tintDrawable(drawable, ActivityCompat.getColor(requireContext(), R.color.fg_disabled))
                    ivAlexaSelectHook?.setImageDrawable(drawableRes)
                    ivGoogleSelectHook?.setImageDrawable(drawableRes)
                }
            }
        }
    }

    companion object {
        const val Alexa_Google = 0
        const val Alexa = 1
        const val Google = 2
    }
}