package com.wifiaudio.view.oobe

import android.annotation.SuppressLint
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import com.harman.CoulsonManager
import com.harman.EventUtils
import com.harman.bar.app.R
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.utils.ClickHelper
import com.wifiaudio.utils.UIUtils
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import com.wifiaudio.view.pagesdevcenter.devicesetting.GoogleAssistantExitDlgView
import config.LogTags

class SetupGVAOOBEDialogFragment : BaseBottomDialogFragment() {

    private var rootView: View? = null
    private var touchOutsideView: View? = null
    private var ivClose: ImageView? = null
    private var btnSetup: Button? = null

    var onCloseListener: OnCloseListener? = null
    private var mOnSetupListener: OnSetupListener? = null

    @SuppressLint("InflateParams")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        rootView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_set_up_gva_oobe, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        return rootView
    }

    override fun onStart() {
        super.onStart()
        dialog?.let { itDialog ->
            val window = itDialog.window
            window?.setBackgroundDrawable(ColorDrawable(ActivityCompat.getColor(requireContext(), R.color.bg_regular_opacity_80)))
        }
        initView()
    }

    private fun initView() {
        rootView?.findViewById<TextView>(R.id.tv_tips)?.apply {
            var string = SkinResourcesUtils.getString("don_t_forget_to_return_to_the_jbl_one_app_for_audio_settings_when_you_re_done")
            text = String.format(string, context.getString(R.string.applicationName))
        }
        touchOutsideView = rootView?.findViewById(R.id.view_touch_outside)
        ivClose = rootView?.findViewById(R.id.iv_close)
        btnSetup = rootView?.findViewById(R.id.btn_set_up)
        btnSetup?.setOnClickListener(this)
        touchOutsideView?.setOnClickListener(this)
        ivClose?.setOnClickListener(this)
    }
    private var googleAssistantExitDlgView:GoogleAssistantExitDlgView?=null
    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.view_touch_outside, R.id.iv_close -> {
                if (googleAssistantExitDlgView != null && googleAssistantExitDlgView?.isShowing() == true) {
                    return
                }
                googleAssistantExitDlgView = activity?.let { GoogleAssistantExitDlgView(it) }
                googleAssistantExitDlgView?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
                    override fun onConfirm(any: Any?) {
                        if (ClickHelper.isFastClick()) return
                        dismissAllowingStateLoss()
                        UIUtils.openAppPage(context,UIUtils.PACKAGE_NAME_GOOGLE_HOME_APP,"googlehome://setup/device/choose-home")
                    }

                    override fun onCancel() {
                        dismissAllowingStateLoss()
                        onCloseListener?.onClose()
                    }
                })
                googleAssistantExitDlgView?.show()
            }
            R.id.btn_set_up -> {
                if (ClickHelper.isFastClick()) return
//                CoulsonManager.instance.recordNewSetup(EventUtils.gva)

                dismissAllowingStateLoss()
                mOnSetupListener?.onSetup()
                UIUtils.openAppPage(context,UIUtils.PACKAGE_NAME_GOOGLE_HOME_APP,"googlehome://setup/device/choose-home")
            }
        }
    }

    fun setOnSetupListener(onSetupListener: OnSetupListener) {
        mOnSetupListener = onSetupListener
    }

    interface OnCloseListener {
        fun onClose();
    }

    interface OnSetupListener {
        fun onSetup()
    }
}