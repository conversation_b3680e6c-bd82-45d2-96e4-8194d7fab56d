package com.wifiaudio.view.oobe

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import androidx.core.app.ActivityCompat
import com.harman.CoulsonManager
import com.harman.EventUtils
import com.harman.bar.app.R
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.utils.UIUtils
import config.LogTags

class SetupGVANormalDialogFragment : BaseBottomDialogFragment() {

    private var rootView: View? = null
    private var touchOutsideView: View? = null
    private var ivClose: ImageView? = null
    private var btnSetup: Button? = null

    var onCloseListener: OnCloseListener? = null
    private var mOnSetupListener: OnSetupListener? = null
    @SuppressLint("InflateParams")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        rootView = LayoutInflater.from(context).inflate(R.layout.dialog_fragment_setup_gva_normal, null)
        LogsUtil.i(LogTags.UI, "page=" + javaClass.simpleName + ":onCreate")
        initView()
        return rootView
    }

    override fun onStart() {
        super.onStart()
        dialog?.let { itDialog ->
            itDialog.setOnKeyListener(object :DialogInterface.OnKeyListener{
                override fun onKey(dialog: DialogInterface?, keyCode: Int, event: KeyEvent?): Boolean {
                    if(keyCode== KeyEvent.KEYCODE_BACK){
                        dismissAllowingStateLoss()
                        onCloseListener?.onClose()
                        return true
                    }
                    return false
                }
            })
            val window = itDialog.window
            window?.setBackgroundDrawable(ColorDrawable(ActivityCompat.getColor(requireContext(), R.color.bg_opacity_overlay)))
        }
    }

    private fun initView() {
        touchOutsideView = rootView?.findViewById(R.id.view_touch_outside)
        ivClose = rootView?.findViewById(R.id.iv_close)
        btnSetup = rootView?.findViewById(R.id.btn_set_up)
        btnSetup?.setOnClickListener(this)
        touchOutsideView?.setOnClickListener(this)
        ivClose?.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.view_touch_outside -> {
                dismissAllowingStateLoss()
                onCloseListener?.onClose()
            }
            R.id.iv_close -> {
                dismissAllowingStateLoss()
                onCloseListener?.onClose()
            }
            R.id.btn_set_up -> {
                dismissAllowingStateLoss()
                mOnSetupListener?.onSetup()
                UIUtils.openAppPage(context,UIUtils.PACKAGE_NAME_GOOGLE_HOME_APP,"googlehome://setup/device/choose-home")
            }
        }
    }

    fun setOnSetupListener(onSetupListener: OnSetupListener) {
        mOnSetupListener = onSetupListener
    }

    interface OnCloseListener {
        fun onClose()
    }

    interface OnSetupListener {
        fun onSetup()
    }
}