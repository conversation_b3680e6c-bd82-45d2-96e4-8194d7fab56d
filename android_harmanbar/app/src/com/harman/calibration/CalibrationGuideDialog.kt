package com.harman.calibration

import android.app.Activity
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.harman.bar.app.databinding.DialogCalibrationGuideBinding
import com.harman.BottomPopUpDialog
import com.harman.discover.bean.OneDevice
import com.harman.isOneCommander
import com.harman.supportRearSpeaker
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.ProductFeature

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/7.
 */
class CalibrationGuideDialog(
    private val activity: Activity,
    private val listener: IAudioCalibrationDialogEvent?,
    device: OneDevice
) : BottomPopUpDialog(context = activity) {

    private val mode = if (device.isOneCommander()) {
        OneCommanderMode(device = device)
    } else {
        GeneralMode(device = device)
    }

    val pid: LiveData<String?> get() = mode.pid

    val cid: LiveData<String?> get() = mode.cid

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCancelable(false)
        setCanceledOnTouchOutside(false)

        val binding = DialogCalibrationGuideBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this
        binding.dialog = this

        setContentView(binding.root)
    }

    fun onCalibrationBtnClick() {
        listener?.onCalibrationBtnClick()
        dismiss()
    }

    fun onLaterBtnClick() {
        listener?.onLaterBtnClick()
        dismiss()
    }

    fun supportRearSpeaker(): Boolean {
        return mode.supportRearSpeaker()
    }

    /**
     * Cause OneCommander's device img display and feature support judgement function are
     * diff from general single device, use an interface to sealed capabilities.
     */
    abstract class CalibrationGuideMode {

        abstract fun supportRearSpeaker(): Boolean

        abstract val pid: LiveData<String?>

        abstract val cid: LiveData<String?>

    }

    inner class GeneralMode(private val device: OneDevice): CalibrationGuideMode() {

        override val pid: LiveData<String?> = MutableLiveData<String?>(device.pid)

        override val cid: LiveData<String?> = MutableLiveData<String?>(device.cid)

        override fun supportRearSpeaker(): Boolean {
            return device.supportRearSpeaker()
        }
    }

    inner class OneCommanderMode(private val device: OneDevice): CalibrationGuideMode() {

        override val pid: LiveData<String?> = MutableLiveData<String?>("211d")

        override val cid: LiveData<String?> = MutableLiveData<String?>("01")

        /**
         * Force using 1300Mk2's pid as capabilities' source.
         */
        override fun supportRearSpeaker(): Boolean {
            return AppConfigurationUtils.getModelConfig("211d")?.capability?.contains(ProductFeature.RearSpeaker) == true
        }
    }
}

interface IAudioCalibrationDialogEvent {

    fun onCalibrationBtnClick() {}

    fun onLaterBtnClick() {}

}