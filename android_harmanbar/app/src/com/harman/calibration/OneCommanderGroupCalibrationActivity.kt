package com.harman.calibration

import androidx.lifecycle.ViewModelProvider
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.getBarPort

class OneCommanderGroupCalibrationActivity : CalibrationSODActivity() {

    override fun genViewModel(device: OneDevice, aliCmdErrorThreshold: Int): CalibrationViewModel {
        return ViewModelProvider(
            this@OneCommanderGroupCalibrationActivity,
            OneCommanderGroupCalibrationViewModelFactory(
                device = device,
                caliCmdErrorThreshold = aliCmdErrorThreshold,
                port = device.getBarPort()
            )
        )[OneCommanderGroupCalibrationViewModel::class.java]
    }


}