package com.harman.calibration

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.harman.command.one.bean.GroupDevicesChangeItem
import com.harman.discover.bean.OneDevice

/**
 * Created by sky
 *
 */
class OneCommanderGroupCalibrationViewModelFactory(
    private val device: OneDevice,
    private val caliCmdErrorThreshold: Int,
    private val port: String? = null
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OneCommanderGroupCalibrationViewModel(
            device = device,
            caliCmdErrorThreshold = caliCmdErrorThreshold,
            port = port
        ) as T
    }
}

class OneCommanderGroupCalibrationViewModel(
    device: OneDevice,
    caliCmdErrorThreshold: Int,
    port: String? = null
) : CalibrationViewModel(device, caliCmdErrorThreshold, port){

    override fun notifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {
        super.notifyGroupDevicesChange(uuid, result)
        //todo need codebug with fm team to implements details
    }

    companion object {

        private const val TAG = "OneCommanderGroupCalibrationViewModel"

    }
}