package com.harman.calibration

import android.annotation.SuppressLint
import android.view.View
import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.EnumCalibrationState
import com.harman.command.one.bean.EnumCalibrationState.Companion.success
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.printList
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/8/12.
 */
open class CalibrationSODActivity : CalibrationBaseActivity() {

    private val _uiState = MutableLiveData<IState?>()

    override val childView: LiveData<View?> = _uiState.distinctUntilChanged().map { state ->
        state ?: return@map null
        state.genRootView(activity = this, inflater = layoutInflater)
    }

    private val _enumState = MutableLiveData<EnumCalibrationState>()

    val loading : LiveData<Boolean> = _uiState.map { state ->
        when (state) {
            SodFirstTuning,
            SodSecondTuning -> true
            else -> false
        }
    }

    /**
     * Set `null` to cancel animation
     */
    private val _lottieRes = MediatorLiveData<String?>("calibration_tuning.json")
    val lottieRes: LiveData<String?>
        get() = _lottieRes.distinctUntilChanged()

    private var rearSpeakersSwappedDialog: RearSpeakersSwappedDialog? = null
    private var disconnectDuringTuningDialog: DisconnectDuringTuningDialog? = null
    private var undockTheRearSpeakersDialog: UndockTheRearSpeakersDialog? = null

    override fun dataBinding(device: OneDevice, viewModel: CalibrationViewModel) {
        super.dataBinding(device = device, viewModel = viewModel)

        viewModel.calibrationState.observe(this) { state ->
            onCalibrationChanged(calibration = state)
        }

        _uiState.value = SodFirstPlacementStepOne
    }

    override fun onBackBtnClick() {
        if (true != _uiState.value?.calibrating) {
            finish()
            return
        }

        super.onBackBtnClick()
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        onBackBtnClick()
    }

    override fun onDestroy() {
        super.onDestroy()

        rearSpeakersSwappedDialog?.dismiss()
        disconnectDuringTuningDialog?.dismiss()
        undockTheRearSpeakersDialog?.dismiss()
    }

    @MainThread
    private fun onCalibrationChanged(calibration: Calibration?) {
        val enumState = calibration?.toEnum() ?: return
        if (_enumState.value == enumState) {
            return
        }

        Logger.i(TAG, "onCalibrationChanged() >>> [${calibration.state}] feedback[${calibration.feedback}]")
        _enumState.value = enumState

        when (enumState) {
            EnumCalibrationState.SETUP -> Unit
            EnumCalibrationState.CALIBRATING -> {
                startCalibratingTimeoutJob(
                    nextUiState = if (SodFirstTuning == _uiState.value) {
                        SodSecondPlacement
                    } else {
                        Complete
                    }
                )
            }
            EnumCalibrationState.DONE,
            EnumCalibrationState.DONE2,
            EnumCalibrationState.FAILED,
            EnumCalibrationState.FAILED2,
            EnumCalibrationState.UNDOCK_REAR_SPKR,
            EnumCalibrationState.CALIBRATION_UNAVAILABLE,
            EnumCalibrationState.OFFLINE -> {
                val fatalIssues = calibration.errorCodes?.filter {
                    EnumAbnormalIssue.isSodFatalIssue(it)
                }
                onTerminalState(enumState = enumState, fatalIssues = fatalIssues)
            }
            else -> {
                // no impl
            }
        }
    }

    @MainThread
    private fun onTerminalState(enumState: EnumCalibrationState, fatalIssues: List<Int>?) {
        Logger.i(TAG, "onTerminalState() >>> state[$enumState] issues: ${fatalIssues?.printList()}")

        quitConfirmDialog?.dismiss()
        calibratingTimeoutJob?.cancel()

        viewModel?.stopLoopCalibrationObservation()
        // unbind device listener to block Upnp receiving for sure on coming state won't mix up UI
        viewModel?.unbindDeviceListener()

        if (enumState.success() && fatalIssues.isNullOrEmpty()) {
            Logger.i(TAG, "onTerminalState() >>> portal next step")
            // switch to next state if no fatal issue in `feedback`
            headToNextUiState(enumState = enumState)
        } else if (enumState == EnumCalibrationState.UNDOCK_REAR_SPKR) {
            Logger.w(TAG, "onTerminalState() >>> UNDOCK_REAR_SPKR")
            _uiState.value = SodFirstPlacementStepTwo
            showUnDockTheRearSpeakersDialog()
        } else if (!fatalIssues.isNullOrEmpty()) {
            if (fatalIssues.any { it == EnumAbnormalIssue.SOD_MAY_SWAP.id }) {
                Logger.w(TAG, "onTerminalState() >>> SOD_MAY_SWAP")
                revertToPreviousUiState(enumState = enumState)
                showSodSwappedDialog()
            } else if (fatalIssues.any { it == EnumAbnormalIssue.SOD_DISCONNECT.id }) {
                Logger.w(TAG, "onTerminalState() >>> SOD_DISCONNECT")
                // force revert to first placement.
                _uiState.value = SodFirstPlacementStepOne
                showSodDisconnectDialog()
            } else if (fatalIssues.any { it == EnumAbnormalIssue.SOD_DOCKED.id }) {
                Logger.w(TAG, "onTerminalState() >>> SOD_DOCKED")
                _uiState.value = SodFirstPlacementStepTwo
                showUnDockTheRearSpeakersDialog()
            }
        } else if (enumState == EnumCalibrationState.OFFLINE) {
            Logger.w(TAG, "onTerminalState() >>> offline")
            super.showCalibrationFailDialog()
        } else {
            Logger.w(TAG, "onTerminalState() >>> common fatal issue")
            // Terminate with state `failed`/`failed2`/`CALIBRATION UNAVAILABLE` but no feedback. show common issue dialog.
            super.showCalibrationFailDialog()
        }
    }

    /**
     * @param enumState can only be one of [EnumCalibrationState.DONE] or [EnumCalibrationState.DONE2]
     */
    @MainThread
    private fun headToNextUiState(enumState: EnumCalibrationState) {
        when (enumState) {
            EnumCalibrationState.DONE -> {
                Logger.i(TAG, "headToNextUiState() >>> UI state to SodSecondPlacement")
                _uiState.value = SodSecondPlacement
            }
            EnumCalibrationState.DONE2 -> {
                Logger.i(TAG, "headToNextUiState() >>> UI state to Complete")
                _uiState.value = Complete
            }
            else -> {
                // no impl
            }
        }
    }

    /**
     * @param enumState can only be one of [EnumCalibrationState.DONE] or [EnumCalibrationState.DONE2]
     */
    @MainThread
    private fun revertToPreviousUiState(enumState: EnumCalibrationState) {
        when (enumState) {
            EnumCalibrationState.DONE -> {
                Logger.i(TAG, "revertToPreviousUiState() >>> revert to SodFirstPlacementStepOne")
                _uiState.value = SodFirstPlacementStepOne
            }
            EnumCalibrationState.DONE2 -> {
                Logger.i(TAG, "revertToPreviousUiState() >>> revert to SodSecondPlacement")
                _uiState.value = SodSecondPlacement
            }
            else -> {
                // no impl
            }
        }
    }

    private fun showSodDisconnectDialog() {
        disconnectDuringTuningDialog?.dismiss()
        disconnectDuringTuningDialog = DisconnectDuringTuningDialog(
            context = this,
            device = device.value ?: return,
            listener = disconnectDuringTuningListener
        ).apply {
            show()
        }
    }

    private val disconnectDuringTuningListener = object : IDisconnectDuringTuningListener {
        override fun onPositiveBtnClick() {
            // do nothing
        }

        override fun onNegativeBtnClick() {
            <EMAIL>()
        }
    }

    private fun showSodSwappedDialog() {
        rearSpeakersSwappedDialog?.dismiss()
        rearSpeakersSwappedDialog = RearSpeakersSwappedDialog(
            context = this,
            listener = rearSpeakersSwappedListener
        ).apply {
            show()
        }
    }

    private val rearSpeakersSwappedListener = object : IRearSpeakersSwappedListener {
        override fun onPositiveBtnClick() {
            // do nothing
        }

        override fun onNegativeBtnClick() {
            <EMAIL>()
        }
    }

    fun onFirstPlacementStepOneBtnClick() {
        _uiState.value = SodFirstPlacementStepTwo
        viewModel?.startLoopRearSpeakerVolume()
    }

    fun onFirstPlacementStepTwoPrevBtnClick() {
        _uiState.value = SodFirstPlacementStepOne
    }

    fun onFirstPlacementStepTwoConfirmBtnClick() {
        if (true == viewModel?.sodState?.value?.attached ||
            true == viewModel?.rearSpeakerVolume?.value?.isAttached) {
            // either Upnp or rear speaker rsp indicate speakers are attached.
            showUnDockTheRearSpeakersDialog()
            return
        }

        viewModel?.stopLoopRearSpeakerVolume()
        viewModel?.startCalibration()
        _uiState.value = SodFirstTuning
    }

    private fun showUnDockTheRearSpeakersDialog() {
        undockTheRearSpeakersDialog?.dismiss()
        undockTheRearSpeakersDialog = UndockTheRearSpeakersDialog(context = this) {
            viewModel?.startLoopRearSpeakerVolume()
        }.apply { show() }
    }

    fun onSecondPlacementBtnClick() {
        _uiState.value = SodSecondTuning
        viewModel?.startCalibration()
    }

    private var calibratingTimeoutJob: Job? = null

    /**
     * Delay [CALIBRATING_WAIT_TIME_MILLS] and UI auto switch to [nextUiState] state
     * if firmware didn't callback on its own.
     */
    private fun startCalibratingTimeoutJob(nextUiState: IState) {
        calibratingTimeoutJob?.cancel()
        calibratingTimeoutJob = lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(CALIBRATING_WAIT_TIME_MILLS)
            Logger.i(TAG, "startCalibratingTimeoutJob() >>> trigger")
            _uiState.value = nextUiState // switch to done state force.
        }
        Logger.i(TAG, "startCalibratingTimeoutJob() >>> start. next[$nextUiState]")
    }

    override val caliCmdErrorThreshold: Int = 10

    companion object {
        private const val TAG = "CalibrationActivity_SOD"

        private const val CALIBRATING_WAIT_TIME_MILLS = 3 * 60 * 1000L
    }
}