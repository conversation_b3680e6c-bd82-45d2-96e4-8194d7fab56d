package com.harman.calibration

import android.content.Context
import android.os.Bundle
import androidx.annotation.StringRes
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.harman.BaseMultiAdapter
import com.harman.BottomPopUpDialog
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogCalibrationResultDetailBinding
import com.harman.bar.app.databinding.ItemCalibrationResultDetailBinding
import com.harman.discover.bean.OneDevice
import com.harman.supportRearSpeaker

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/9.
 */
class CalibrationResultDetailDialog(
    private val context: Context,
    val device: OneDevice,
    val issues: List<EnumAbnormalIssue>
) : BottomPopUpDialog(context = context) {

    private val _adapter = MutableLiveData<IssueAdapter>()
    val adapter: LiveData<IssueAdapter>
        get() = _adapter

    val constraintHeightMax: Float = context.resources.getDimension(
        if (device.supportRearSpeaker()) R.dimen.dimen_380dp else R.dimen.dimen_280dp
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val binding = DialogCalibrationResultDetailBinding.inflate(layoutInflater)
        binding.dialog = this
        binding.lifecycleOwner = this

        setContentView(binding.root)

        _adapter.value = IssueAdapter(issues = issues)
    }

    fun onGotBtnClick() {
        dismiss()
    }

    inner class IssueAdapter(
        issues: List<EnumAbnormalIssue>
    ): BaseMultiAdapter<EnumAbnormalIssue>(data = issues) {

        override fun viewType(position: Int): Int = 0

        override fun bind(binding: ViewDataBinding, position: Int) {
            if (binding !is ItemCalibrationResultDetailBinding) return

            val issue = getOrNull(position) ?: return

            if (device.supportRearSpeaker()) {
                sodTitleStringRes(issue)?.let { binding.titleRes = it }
            } else {
                nonSodTitleStringRes(issue)?.let { binding.titleRes = it }
            }

            if (device.supportRearSpeaker()) {
                sodDescStringRes(issue)?.let { binding.descRes = it }
            } else {
                nonSodDescStringRes(issue)?.let { binding.descRes = it }
            }
        }

        @StringRes
        private fun sodTitleStringRes(issue: EnumAbnormalIssue): Int? = when (issue) {
            EnumAbnormalIssue.ENV_NOISE -> R.string.reduce_environment_noise
            EnumAbnormalIssue.WALL_TOO_CLOSE -> R.string.adjust_the_soundbar_position
            EnumAbnormalIssue.OTHER_ISSUE -> R.string.check_the_rear_speakers
            else -> null
        }

        @StringRes
        private fun nonSodTitleStringRes(issue: EnumAbnormalIssue): Int? = when (issue) {
            EnumAbnormalIssue.ENV_NOISE -> R.string.reduce_environment_noise
            EnumAbnormalIssue.WALL_TOO_CLOSE -> R.string.adjust_the_soundbar_position
            EnumAbnormalIssue.OTHER_ISSUE -> R.string.check_the_rear_speakers
            else -> null
        }

        @StringRes
        private fun sodDescStringRes(issue: EnumAbnormalIssue): Int? = when (issue) {
            EnumAbnormalIssue.ENV_NOISE -> R.string.Remain_quiet_during_the_calibration_2
            EnumAbnormalIssue.WALL_TOO_CLOSE -> R.string.adjust_the_soundbar_position_desc
            EnumAbnormalIssue.OTHER_ISSUE -> R.string.check_the_rear_speakers_desc
            else -> null
        }

        @StringRes
        private fun nonSodDescStringRes(issue: EnumAbnormalIssue): Int? = when (issue) {
            EnumAbnormalIssue.ENV_NOISE -> R.string.Remain_quiet_during_the_calibration_2
            EnumAbnormalIssue.WALL_TOO_CLOSE -> R.string.adjust_the_soundbar_position_desc
            EnumAbnormalIssue.OTHER_ISSUE -> R.string.check_the_rear_speakers_desc_5
            else -> null
        }

        override fun layoutId(viewType: Int) = R.layout.item_calibration_result_detail

        override fun isSameItem(old: EnumAbnormalIssue, new: EnumAbnormalIssue) =
            old == new

        override fun isSameContent(old: EnumAbnormalIssue, new: EnumAbnormalIssue) =
            old == new
    }
}