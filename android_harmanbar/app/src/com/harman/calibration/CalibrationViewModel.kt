package com.harman.calibration

import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.EnumCalibrationState
import com.harman.command.one.bean.GroupDevicesChangeItem
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.upnp.SodState
import com.harman.connect.listener.IOneDeviceListener
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.printList
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/8/8.
 *
 * [caliCmdErrorThreshold] how many times allowed before display [CalibrationFailDialog] after
 * receiving calibration cmd error.
 */
class CalibrationViewModelFactory(
    private val device: OneDevice,
    private val caliCmdErrorThreshold: Int,
    private val port: String? = null
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return CalibrationViewModel(
            device = device,
            caliCmdErrorThreshold = caliCmdErrorThreshold,
            port = port
        ) as T
    }
}

open class CalibrationViewModel(
    private val device: OneDevice,
    private val caliCmdErrorThreshold: Int,
    private val port: String? = null
) : ViewModel(), DefaultLifecycleObserver {

    private val _calibrationState = MutableLiveData<Calibration>()
    val calibrationState : LiveData<Calibration>
        get() = _calibrationState

    private val _abnormalEvents = MutableLiveData<List<Int>>()
    val abnormalEvents: LiveData<List<Int>>
        get() = _abnormalEvents

    private val _sodState = MutableLiveData<SodState>()
    val sodState: LiveData<SodState>
        get() = _sodState

    private val _rearSpeakerVolume = MutableLiveData<RearSpeakerVolumeResponse>()
    val rearSpeakerVolume: LiveData<RearSpeakerVolumeResponse>
        get() = _rearSpeakerVolume

    private var loopGetCalibrationStateJob: Job? = null
    private var loopGetRearSpeakerVolumeJob: Job? = null

    @Volatile
    private var caliCmdErrorCount = 0

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        device.registerDeviceListener(deviceListener)
    }

    fun startCalibration() {
        Logger.i(TAG, "startCalibration() >>> ")

        caliCmdErrorCount = 0
        device.registerDeviceListener(deviceListener)
        device.setCalibration(port)

        loopGetCalibrationStateJob?.cancel()
        loopGetCalibrationStateJob = viewModelScope.launch(DISPATCHER_DEFAULT) {
            while (true) {
                delay(GET_CALIBRATION_INTERVAL_MILLS)
                device.getCalibrationState(port)// will get done status of previous calibration and show calibration success at once
            }
        }
    }

    fun getCalibrationStateOnce() {
        Logger.i(TAG, "getCalibrationStateOnce() >>> ")
        device.registerDeviceListener(deviceListener)
        device.getCalibrationState(port)
    }

    fun stopLoopCalibrationObservation() {
        Logger.i(TAG, "stopLoopCalibrationObservation() >>> ")
        loopGetCalibrationStateJob?.cancel()
    }

    fun startLoopRearSpeakerVolume() {
        Logger.i(TAG, "startLoopRearSpeakerVolume() >>> ")
        device.registerDeviceListener(deviceListener)


        loopGetRearSpeakerVolumeJob?.cancel()
        loopGetRearSpeakerVolumeJob = viewModelScope.launch(DISPATCHER_DEFAULT) {
            while (true) {
                device.getRearSpeakerVolume(port)
                delay(GET_REAR_SPEAKER_VOLUME_INTERVAL_MILLS)
            }
        }
    }

    fun stopLoopRearSpeakerVolume() {
        Logger.i(TAG, "stopLoopRearSpeakerVolume() >>> ")
        loopGetRearSpeakerVolumeJob?.cancel()
    }

    fun unbindDeviceListener() {
        device.unregisterDeviceListener(deviceListener)
    }

    fun cancelCalibration() {
        Logger.i(TAG, "cancelCalibration() >>> ")
        stopLoopCalibrationObservation()
        device.cancelCalibration(port)
    }

    private val deviceListener = object : IOneDeviceListener {
        @WorkerThread
        override fun onRspFailure(cmd: String?, e: Exception, port: String?) {
            Logger.e(TAG, "onRspFailure() >>> cmd[$cmd] e:$e")
            if (EnumCommandMapping.GET_CALIBRATION_STATE.wifiCmd != cmd) {
                return
            }

            caliCmdErrorCount++
            if (caliCmdErrorCount >= caliCmdErrorThreshold) {
                Logger.e(TAG, "onRspFailure() >>> caliCmdErrorCount[$caliCmdErrorCount] >= caliCmdErrorThreshold[$caliCmdErrorThreshold]")
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    _calibrationState.value = Calibration(
                        state = EnumCalibrationState.OFFLINE.value
                    )
                    stopLoopCalibrationObservation()
                }
            }
        }

        @WorkerThread
        override fun onCalibration(calibration: Calibration) {
            Logger.d(TAG, "onCalibration() >>> $calibration")
            caliCmdErrorCount = 0

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _calibrationState.value = calibration
                handleCalibration(calibration = calibration)
            }
        }

        @WorkerThread
        override fun onCalibrationCancelled(rsp: BasicResponse) {
            // no impl.
        }

        @WorkerThread
        override fun onUpnpCalibrationState(uuid: String, calibration: Calibration) {
            if (uuid != device.wifiDevice?.deviceItem?.uuid) {
                return
            }

            Logger.d(TAG, "onUpnpCalibrationState() >>> $calibration")
            caliCmdErrorCount = 0
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _calibrationState.value = calibration
                handleCalibration(calibration = calibration)
            }
        }

        @WorkerThread
        private fun handleCalibration(calibration: Calibration) {
            val errorCodes = calibration.errorCodes
            Logger.d(TAG, "handleCalibration() >>> errorCodes: ${errorCodes.printList()}")

            val displayedCodes = errorCodes?.filter {
                EnumAbnormalIssue.isDisplayableIssue(it)
            }

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                if (!displayedCodes.isNullOrEmpty()) {
                    _abnormalEvents.addIfNotExistsAndSort(displayedCodes)
                }
            }
        }

        @WorkerThread
        override fun onUpnpSodState(uuid: String, sodState: SodState) {
            if (uuid != device.wifiDevice?.deviceItem?.uuid) {
                return
            }

            Logger.d(TAG, "onUpnpSodState() >>> $sodState")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _sodState.value = sodState
            }
        }

        @WorkerThread
        override fun onSodState(state: SodState) {
            Logger.d(TAG, "onSodState() >>> $state")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _sodState.value = state
            }
        }

        @WorkerThread
        override fun onRearSpeakerVolume(rsp: RearSpeakerVolumeResponse) {
            Logger.d(TAG, "onRearSpeakerVolume() >>> $rsp")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _rearSpeakerVolume.value = rsp
            }
        }

        override fun onUpnpNotifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {
            notifyGroupDevicesChange(uuid, result)
        }
    }

    open fun notifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {

    }

    @MainThread
    private fun MutableLiveData<List<Int>>.addIfNotExistsAndSort(appends: List<Int>) {
        if (appends.isEmpty()) {
            return
        }

        val live = this
        val prev = live.value?.toMutableList() ?: mutableListOf()
        prev.addAll(appends)
        val distincts = prev.distinct().toMutableList()
        distincts.sort()

        live.value = distincts
    }

    override fun onDestroy(owner: LifecycleOwner) {
        Logger.i(TAG, "onDestroy() >>> ")
        super.onDestroy(owner)

        stopLoopCalibrationObservation()
        device.unregisterDeviceListener(deviceListener)
    }

    companion object {

        private const val TAG = "CalibrationViewModel"

        private const val GET_CALIBRATION_INTERVAL_MILLS = 5 * 1000L
        private const val GET_REAR_SPEAKER_VOLUME_INTERVAL_MILLS = 5 * 1000L

    }
}