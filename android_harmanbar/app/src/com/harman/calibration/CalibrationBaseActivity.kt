package com.harman.calibration

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.map
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityCalibrationBinding
import com.harman.discover.bean.OneDevice
import com.harman.isOneCommanderGroup
import com.harman.log.Logger
import com.harman.parseAsOneDevice
import com.harman.portalActivity
import com.harman.supportRearSpeaker
import com.harman.utils.Utils
import com.wifiaudio.view.pagesmsccontent.FragTabUtils

/**
 * Created by ger<PERSON><PERSON><PERSON><PERSON> on 2024/8/12.
 */
abstract class CalibrationBaseActivity : AppCompatActivity() {

    private val _device = MutableLiveData<OneDevice>()
    val device: LiveData<OneDevice>
        get() = _device

    protected var viewModel: CalibrationViewModel? = null

    private val _abnormalEventCount = MutableLiveData<Int>()
    val abnormalEventCount: LiveData<Int>
        get() = _abnormalEventCount

    val isAbnormalLayoutVisible: LiveData<Boolean> = _abnormalEventCount.map { it > 0 }

    protected var quitConfirmDialog: QuitCalibrationConfirmDialog? = null
    private var abnormalDetailDialog: CalibrationResultDetailDialog? = null
    protected var calibrationFailDialog: CalibrationFailDialog? = null

    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Utils.decorateHarmanWindow(this)

        val device = parseAsOneDevice() ?: run {
            finish()
            return
        }

        val viewModel = genViewModel(device = device,
            caliCmdErrorThreshold = caliCmdErrorThreshold)

        <EMAIL> = viewModel

        lifecycle.addObserver(viewModel)

        setContentView(genViewBinding().root)

        dataBinding(device = device, viewModel = viewModel)
    }

    open fun genViewModel(device: OneDevice, caliCmdErrorThreshold: Int): CalibrationViewModel{
        return ViewModelProvider(
            this@CalibrationBaseActivity,
            CalibrationViewModelFactory(device = device, caliCmdErrorThreshold = caliCmdErrorThreshold, port = null)
        )[CalibrationViewModel::class.java]
    }

    @CallSuper
    open fun dataBinding(device: OneDevice, viewModel: CalibrationViewModel) {
        _device.value = device

        viewModel.abnormalEvents.observe(this) { events ->
            _abnormalEventCount.value = events.count()
        }
    }

    @CallSuper
    override fun onDestroy() {
        super.onDestroy()
        viewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }
        quitConfirmDialog?.dismiss()
        abnormalDetailDialog?.dismiss()
        calibrationFailDialog?.dismiss()
    }

    /**
     * handle event after click back btn
     */
    @CallSuper
    open fun onBackBtnClick() {
        quitConfirmDialog?.dismiss()
        quitConfirmDialog = QuitCalibrationConfirmDialog(
            activity = this,
            listener = object : IQuitCalibrationConfirmListener {
                override fun onCancelBtnClick() {
                    cancelCalibration()
                    finish()
                }

                override fun onStayBtnClick() {
                    // do nothing
                }
            }
        ).apply {
            show()
        }
    }

    protected fun cancelCalibration() {
        Logger.i(TAG, "cancelCalibration() >>> ")
        viewModel?.cancelCalibration()
        finish()
    }

    /**
     * [childView] will be displayed inside [R.id.layout_container] in [R.layout.activity_calibration]
     */
    abstract val childView: LiveData<View?>

    /**
     * @link [R.layout.layout_calibration_result#btn_done]
     */
    fun onDoneBtnClick() {
        finish()
    }

    /**
     * @link [R.layout.layout_calibration_result#btn_feel_the_power]
     */
    fun onFeelThePowerBtnClick() {
        val device = device.value ?: return

        val demoFragment = CalibrationDemoFragment(
            device = device,
            listener = object : ICalibrationDemoListener {
                override fun onDoneBtnClick() {
                    Logger.d(TAG, "CalibrationDemo.onDoneBtnClick() >>> ")
                    FragTabUtils.popBack(this@CalibrationBaseActivity)
                    finish()
                }
            })

        FragTabUtils.addFrag(this, R.id.fragment_container, demoFragment, false)
    }

    /**
     * @link [R.layout.layout_calibration_result#layout_abnormal]
     */
    fun onAbnormalBannerClick() {
        val issues = viewModel?.abnormalEvents?.value?.mapNotNull {
            EnumAbnormalIssue.toDisplayableIssue(
                id = it
            )
        }
        if (issues.isNullOrEmpty()) {
            Logger.w(TAG, "onAbnormalBannerClick() >>> no available abnormal issue displayable.")
            return
        }

        abnormalDetailDialog?.dismiss()
        abnormalDetailDialog = CalibrationResultDetailDialog(
            context = this,
            device = device.value ?: return,
            issues = issues
        ).apply {
            show()
        }
    }

    private fun genViewBinding(): ActivityCalibrationBinding =
        ActivityCalibrationBinding.inflate(layoutInflater).also { binding ->
            binding.lifecycleOwner = this
            binding.activity = this
        }

    protected fun showCalibrationFailDialog() {
        if (true == calibrationFailDialog?.isShowing) {
            return
        }

        calibrationFailDialog = CalibrationFailDialog(context = this) {
            cancelCalibration()
        }.apply {
            show()
        }
    }

    companion object {
        private const val TAG = "CalibrationActivity_Base"

        fun portal(context: Context?, device: OneDevice) : Boolean =
            if (device.isOneCommanderGroup()) {
                Logger.i(TAG, "portal() >>> target: one commander group")
                context.portalActivity(target = OneCommanderGroupCalibrationActivity::class.java, device = device)
            } else if (device.supportRearSpeaker()) {
                Logger.i(TAG, "portal() >>> target: SOD")
                context.portalActivity(target = CalibrationSODActivity::class.java, device = device)
            } else {
                Logger.i(TAG, "portal() >>> target: none SOD")
                context.portalActivity(target = CalibrationActivity::class.java, device = device)
            }
    }

    abstract val caliCmdErrorThreshold: Int
}