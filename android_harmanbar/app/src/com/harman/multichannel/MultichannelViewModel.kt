package com.harman.multichannel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.google.android.gms.common.util.CollectionUtils
import com.harman.bar.app.R
import com.harman.colorId
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.Channel
import com.harman.command.one.bean.DeviceInfo
import com.harman.command.one.bean.EnumCalibrationFeedback
import com.harman.command.one.bean.EnumCalibrationFeedback.Companion.fail
import com.harman.command.one.bean.EnumCalibrationFeedback.Companion.negative
import com.harman.command.one.bean.EnumCalibrationFeedback.Companion.success
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.Group
import com.harman.command.one.bean.GroupInfo
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.Member
import com.harman.command.one.bean.SetCastGroup
import com.harman.command.one.bean.upnp.SetAnchorResult
import com.harman.connect.GENERAL_TIMEOUT_MILLS
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetDeviceInfoWithTimeout
import com.harman.connect.syncGetDeviceNameWithTimeout
import com.harman.connect.syncGetGroupCalibrationStateWithTimeout
import com.harman.connect.syncGetGroupInfoWithTimeout
import com.harman.connect.syncGetGroupParameterWithTimeout
import com.harman.deviceImgPath
import com.harman.discover.DeviceScanner
import com.harman.discover.DeviceStore
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.wlan0Mac
import com.harman.discover.util.Tools.wlan0MacWithoutColon
import com.harman.displayDeviceName
import com.harman.displayShortDeviceName
import com.harman.hkone.deviceoffline.OfflineDeviceHandler
import com.harman.isOneCommander
import com.harman.isOneCommanderGroup
import com.harman.isSoundBar
import com.harman.log.Logger
import com.harman.modelName
import com.harman.multichannel.repository.CacheRepository
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.shortName
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.CombinationItem
import com.jbl.one.configuration.model.EnumCombinationStatus
import com.jbl.one.configuration.model.ModelItem
import com.jbl.one.configuration.model.PlacementCombinationItem
import com.jbl.one.configuration.model.PlacementItem
import com.jbl.one.configuration.model.SupportItem
import com.wifiaudio.utils.GsonParseUtil.Companion.instance
import java.util.LinkedList
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by sky on 2024/8/20.
 */


class MultichannelViewModel : ViewModel() {
    lateinit var entryPoint: OneDevice
    private lateinit var wifiOnlineDevices: List<OneDevice>
    lateinit var combinationItem: CombinationItem
    var placementCombinationItem: PlacementCombinationItem? = null


    private var _selectedDevices = mutableListOf<OneDevice>()

    val maxProgress = DEFAULT_MAX_PROGRESS

    private val _multichannelStatus = MutableLiveData<com.harman.multichannel.EnumMultichannelStatus>()
    val multichannelStatus: LiveData<com.harman.multichannel.EnumMultichannelStatus>
        get() = _multichannelStatus.distinctUntilChanged()

    private val _modelNameAndColors = MutableLiveData<List<android.util.Pair<String?, String?>?>?>()
    val modelNameAndColors: LiveData<List<android.util.Pair<String?, String?>?>?>
        get() = _modelNameAndColors
    var modelNameAndColors2: List<android.util.Pair<String?, String?>?>? = null
    fun getSelectDevice(): List<OneDevice> {
        return mutableListOf<OneDevice>().apply {
            add(entryPoint)
            addAll(_selectedDevices)

        }
    }

    private fun getSelectDeviceByOrder(): List<OneDevice> {
        return mutableListOf<OneDevice>().apply {
            var tempList = getSelectDevice()
            combinationItem?.modelList?.forEach { model->
                tempList.find { temp-> model.modelName.contains(temp.modelName()) && !contains(temp) }?.also { add(it)

                }
            }
//            tempList.forEach { item ->
//                run {
//                    if (item == findGO()) {
//                        add(0, item)
//                    } else {
//                        add(item)
//                    }
//                }
//            }


        }
    }

    fun selectDevice(oneDevice: OneDevice) {
        _selectedDevices.add(oneDevice)
    }

    fun unSelectDevice(oneDevice: OneDevice) {
        _selectedDevices.remove(oneDevice)
    }

    fun clearSelectDevice() {
        _selectedDevices.clear()
    }

    fun initMultichannelDeviceList() {

        wifiOnlineDevices =
            DeviceStore.oneDevices.filter { it.isWiFiOnline && it.groupInfoExt?.groupInfo?.isSingle() == true }
    }

    private suspend fun getCombinationList(): List<CombinationItem> {
        val result = mutableListOf<CombinationItem>()
        getAllCombinationList()?.forEach { item->
            val find1 = getAvailableCombinationList()?.find { item2->item2.combinationId == item.combinationId }
            if(find1 != null){
                find1.enumCombinationStatus = EnumCombinationStatus.AVAILABLE
                result.add(find1)
            }else{
                val find2 = getDiffHmCastVersionCombination()?.find { item3->item3.combinationId == item.combinationId }
                if(find2 != null){
                    find2.enumCombinationStatus = EnumCombinationStatus.UPDATE_PRODUCT_SOFTWARE_TO_CONTINUE
                    result.add(find2)
                }else{
                    item.enumCombinationStatus = EnumCombinationStatus.CONNECT_PRODUCT_TO_WIFI_TO_CONTINUE
                    result.add(item)
                }
            }


         }

        return result

    }

    private fun getDiffHmCastVersionCombination(): List<CombinationItem>? {
//        return HarmancastManager.getInstance().combinationList
        val result =  com.harman.multichannel.HarmancastManager.getInstance().getCombination(wifiOnlineDevices)
            ?.filter { item -> item.containModelName(entryPoint.modelName()) }

        Logger.d(TAG, "combinationList getDiffHmCastVersionCombination size = ${result?.size} list = ${result?.map { it?.modelList?.map { it.modelName }?.joinToString(separator = "_")}}")
        return result
    }

    private fun getAvailableCombinationList(): List<CombinationItem>? {
//        return HarmancastManager.getInstance().combinationList
        var result: List<CombinationItem>? = null
        val list =  wifiOnlineDevices?.filter { item -> item.hmCastMajorVer() == entryPoint.hmCastMajorVer() }
        if(CollectionUtils.isEmpty(list)){
            result =  null
        }
        result =   com.harman.multichannel.HarmancastManager.getInstance()
            .getCombination(list)
            ?.filter { item -> item.containModelName(entryPoint.modelName()) }
        Logger.d(TAG, "combinationList getAvailableCombinationList  size = ${result?.size} list = ${result?.map { it?.modelList?.map { it.modelName }?.joinToString(separator = "_", prefix = "{", postfix = "}")}}")
        return result
    }

    private suspend fun getAllCombinationList(): List<CombinationItem>? {
//        return HarmancastManager.getInstance().combinationList
        val result =  withContext(DISPATCHER_IO) {
            val allOneDevice = (wifiOnlineDevices + getOfflineDevices() + LocalCacheAdapter.getOfflineDevices()
                .filterIsInstance<OneDevice>())
            allOneDevice?.forEachIndexed { index, oneDevice ->  Logger.d(TAG,"combinationList index = $index  crc = ${oneDevice.UUID} hmCastVer = ${oneDevice.hmCastVer} hmCastMajorVer = ${oneDevice.hmCastMajorVer()} isWiFiOnline = ${oneDevice.isWiFiOnline}  modelName = ${oneDevice.modelName()} shortName = ${oneDevice.shortName()} ")}
            com.harman.multichannel.HarmancastManager.getInstance().getCombination(allOneDevice)?.filter { item -> item.containModelName(entryPoint.modelName()) }
        }
        Logger.d(TAG, "combinationList getAllCombinationList  size = ${result?.size} list = ${result?.map { it?.modelList?.map { it.modelName }?.joinToString(separator = "_")}}")
        return result
    }

    operator fun <OneDevice> Collection<OneDevice>?.plus(devices: Iterable<OneDevice>): List<OneDevice> {
        this ?: return devices.toList()

        val noDuplicates = devices.filter { device ->
            device !in this
        }

        if (noDuplicates.isEmpty()) {
            return this.toList()
        }

        val result = ArrayList<OneDevice>(this.size + noDuplicates.size)
        result.addAll(this)
        result.addAll(noDuplicates)
        return result
    }

    private fun getOfflineDevices(): List<OneDevice> {
        val ret = LinkedList<OneDevice>()

        OfflineDeviceHandler.get().authDeviceList?.forEach { authDev ->
            DeviceStore.findOne(authDev.deviceCrc)?.also { oneDevice -> ret.add(oneDevice) }
                ?: run {
                    OneDevice(offlineDummy = authDev.wifiDevice).also { oneDevice ->
                        ret.add(oneDevice)
                    }
                }
        }
        return ret
    }

    suspend fun getMultichannelTypeUIModels(): List<MultichannelTypeUIModel>? {
        var result = mutableListOf<MultichannelTypeUIModel>()
        var item = MultichannelTypeUIModel()
        item.dataType = com.harman.multichannel.DataType.HEADER
        result.add(item)
        item = MultichannelTypeUIModel()
        var combinationList = getCombinationList()
        if (combinationList == null || combinationList.isEmpty()) {

            item.dataType = com.harman.multichannel.DataType.TITLE
            item.textResId = R.string.cannot_find_available_products
            result.add(item)

            item = MultichannelTypeUIModel()
            item.dataType = com.harman.multichannel.DataType.BOTTOM_TIP
            result.add(item)

        } else if (combinationList.size == 1) {
            item.dataType = com.harman.multichannel.DataType.MC_ITEM
            item.data = combinationList[0]
            item.enumCombinationStatus = combinationList[0].enumCombinationStatus
            result.add(item)

            item = MultichannelTypeUIModel()
            item.dataType = com.harman.multichannel.DataType.BOTTOM_TIP
            result.add(item)

        } else if (combinationList.size > 1) {

            item.dataType = com.harman.multichannel.DataType.TITLE
            item.textResId = R.string.multi_channel_Recommended_For_You
            result.add(item)

            item = MultichannelTypeUIModel()
            item.dataType = com.harman.multichannel.DataType.MC_ITEM
            item.data = combinationList[0]
            item.enumCombinationStatus = combinationList[0].enumCombinationStatus
            result.add(item)

            item = MultichannelTypeUIModel()
            item.dataType = com.harman.multichannel.DataType.TITLE
            item.textResId = R.string.multi_channel_Also_Available
            result.add(item)

            for (i in combinationList.indices) {
                if (i > 0) {
                    item = MultichannelTypeUIModel()
                    item.data = combinationList[i]
                    item.dataType = com.harman.multichannel.DataType.MC_ITEM
                    item.enumCombinationStatus = combinationList[i].enumCombinationStatus
                    result.add(item)
                }
            }


            item = MultichannelTypeUIModel()
            item.dataType = com.harman.multichannel.DataType.BOTTOM_TIP
            result.add(item)

        }
        return result
    }

    fun getSelectDeviceUIModels(context: Context): List<SelectDeviceUIModel>? {


        var modelList = combinationItem.getModelListDistinct()
        var result = mutableListOf<SelectDeviceUIModel>()

        modelList.forEach { modelItem ->

            var item = SelectDeviceUIModel()
            item.dataType = com.harman.multichannel.DataType.TITLE
            val count = combinationItem.getChModelNameCount(modelItem.modelName.getOrNull(0))
            item.text = if(count > 1) context.getString(
                R.string.multi_channel_Choose__products,
                count.toString()
            ) else context.getString(
                R.string.multi_channel_Choose__product,
                count.toString()
            )
            result.add(item)
            var deviceList = wifiOnlineDevices.filter { oneDevice ->
                modelItem.modelName.contains(oneDevice.modelName()) && oneDevice.hmCastMajorVer() == entryPoint.hmCastMajorVer()
            }
            deviceList.forEach { deviceItem ->
                item = SelectDeviceUIModel()
                item.dataType = com.harman.multichannel.DataType.MC_ITEM
                item.deviceName = deviceItem.displayDeviceName()
                item.imageUrl = deviceItem.deviceImgPath()
                item.isEntryPoint = deviceItem == entryPoint
                item.device = deviceItem
                result.add(item)
            }

        }
        return result
    }

    fun getSelectDeviceUIModelsForBLE(list: List<OneDevice>?): List<SelectDeviceUIModel>? {


        var result = mutableListOf<SelectDeviceUIModel>()
        var item = SelectDeviceUIModel()
        item.dataType = com.harman.multichannel.DataType.HEADER
        result.add(item)

        if (list == null || list?.isEmpty() == true) {
            item = SelectDeviceUIModel()
            item.dataType = com.harman.multichannel.DataType.BOTTOM_TIP
            result.add(item)
            return result
        }
        item = SelectDeviceUIModel()
        item.dataType = com.harman.multichannel.DataType.TITLE
        item.textResId = R.string.select_product
        result.add(item)

        list?.forEach { deviceItem ->
            item = SelectDeviceUIModel()
            item.dataType = com.harman.multichannel.DataType.MC_ITEM
            item.deviceName = deviceItem.displayDeviceName()
            item.imageUrl = deviceItem.deviceImgPath()

            item.device = deviceItem
            result.add(item)
        }
        return result
    }

    fun getProductImage(): String? {
        entryPoint.modelName() ?: return null
        entryPoint.colorId() ?: return null
        return AppConfigurationUtils.getModelRenderPathByModelName(
            entryPoint.modelName()!!, entryPoint.colorId()!!
        )
    }

    fun onlyHaveDevicesForCombinationItem(combinationItem: CombinationItem): Boolean {
        return com.harman.multichannel.HarmancastManager.getInstance()
            .sameComCombinationModel(combinationItem, wifiOnlineDevices?.filterVisibleDevice())
    }

    fun getPlacementCombinationItem(combinationItem: CombinationItem): PlacementCombinationItem? {
        return com.harman.multichannel.HarmancastManager.getInstance().placementItemList.filter { item -> item.combinationId == combinationItem.combinationId }
            .getOrNull(0)
    }

    fun getPlacementUIModels(): List<PlacementItem>? {
        return placementCombinationItem?.placementList
    }

    fun hasPlacement(): Boolean {
        return !placementCombinationItem?.placementList.isNullOrEmpty()
    }

    private val _anchorFlag = MutableLiveData<Boolean>()
    val anchorFlag: LiveData<Boolean>
        get() = _anchorFlag.distinctUntilChanged()

    private val _progress = MutableLiveData<Int>()
    val progress: LiveData<Int>
        get() = _progress.distinctUntilChanged()


    var anchorTimeout = 30
    fun setAnchorIndicate() {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            getSelectDevice().forEach { item ->
                item.registerDeviceListener(deviceListener)
                item.setAnchorIndicate(anchorTimeout)
            }
            delay(anchorTimeout.toLong() * 1000)
            if (!anchorSuccess) {
                _anchorFlag.postValue(false)
                unregisterAllDeviceListener()

            }
        }
    }

    var anchorSuccess = false
    private val deviceListener = object : IOneDeviceListener {
        override fun onUpnpSetAnchorResult(uuid: String, result: SetAnchorResult) {
            super.onUpnpSetAnchorResult(uuid, result)
            Logger.d(TAG, "anchorPoint onUpnpSetAnchorResult uuid = $uuid result = $result")
            if (result.success) {
                anchorSuccess = true
                _anchorFlag.postValue(true)

                val list = getSelectDevice()
                val item = list.firstOrNull { item -> item?.wifiDevice?.deviceItem?.uuid == uuid }
                Logger.d(TAG, "anchorPoint onUpnpSetAnchorResult uuid = $uuid anchorPoint = $item")
                val item2 = item ?: return
                unregisterAllDeviceListener()
                _selectedDevices.add(entryPoint)
                _selectedDevices.remove(item2)
                entryPoint = item2
                Logger.d(
                    TAG,
                    "anchorPoint onUpnpSetAnchorResult uuid = $uuid getSelectDevice = ${getSelectDevice()}"
                )
            }else{
                _anchorFlag.postValue(false)
                unregisterAllDeviceListener()
            }
        }

        override fun onUpnpSetCastGroup(uuid: String, result: GetGroupInfo?) {
            Logger.d(TAG, " onUpnpSetCastGroup uuid = $uuid result = $result")
            super.onUpnpSetCastGroup(uuid, result)
            if (uuid == findGO()?.wifiDevice?.deviceItem?.uuid && result?.groupMode == GroupMode.GROUP.value) {
                groupSuccess()

            }
        }

        override fun onUpnpCalibrationState(uuid: String, calibration: Calibration) {
            Logger.d(TAG, " onUpnpCalibrationState uuid = $uuid result = $calibration")
            super.onUpnpCalibrationState(uuid, calibration)
//            calibration.feedback?.also { feedback ->
//                EnumCalibrationFeedback.fromValue(feedback)?.also { updateUIByCalibrationState(it) }
//            }

        }

        override fun onUpnpNotifyGroupCalibrationState(
            uuid: String,
            rsp: GetGroupCalibrationStateRsp
        ) {
            Logger.d(TAG, " onUpnpNotifyGroupCalibrationState uuid = $uuid result = $rsp")
            super.onUpnpNotifyGroupCalibrationState(uuid, rsp)
            val state = rsp.calibration?.state
            if (state == "done" ) {
                val feedback = rsp.calibration?.feedback
                EnumCalibrationFeedback.fromValue(feedback)?.also { updateUIByCalibrationState(it) }

            }
        }


    }

    fun getGroupDuration(): Int {
        return 60 + (getSelectDevice().size - 2) * 15
    }

    private fun getCalibrationDuration(): Int {
        return if (combinationItem.containsBar()) (getSelectDevice().size - 1) * 6 + 45 else getSelectDevice().size * 6
    }

    var groupFinish = false
    fun groupSuccess() {

        if (groupFinish) {
            return
        }
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            _progress.postValue(maxProgress)
            delay(1000)
            if(combinationItem.notNeedCalibration()){
                _multichannelStatus.postValue(com.harman.multichannel.EnumMultichannelStatus.CALIBRATION_SUCCESS)
            }else{
                _multichannelStatus.postValue(com.harman.multichannel.EnumMultichannelStatus.CALIBRATING)
            }

        }


        groupFinish = true
    }

    var calibrationFailTimes = 0;
    var hasUpdateSoundTuningState = false

    fun postSoundTuningState(state : EnumMultichannelStatus){
        if(!hasUpdateSoundTuningState){
            _multichannelStatus.postValue(state)
            hasUpdateSoundTuningState = true
        }
    }
    fun updateUIByCalibrationState(feedbackold: EnumCalibrationFeedback?) {
        val feedback = feedbackold ?: return
        if (feedback.success()) {
            postSoundTuningState(EnumMultichannelStatus.CALIBRATION_SUCCESS)
        } else {

            if (feedback.fail()) {
                postSoundTuningState(EnumMultichannelStatus.CALIBRATION_FAIL)
            } else if (feedback.negative()) {
                calibrationFailTimes++
                if (calibrationFailTimes >= 2) {
                    postSoundTuningState(EnumMultichannelStatus.CALIBRATION_FAIL)
                    calibrationFailTimes = 0
                } else {
                    postSoundTuningState(EnumMultichannelStatus.CALIBRATION_NEGATIVE)
                }

            } else {
                //not implement
            }
        }

    }

    suspend fun loopGetGroupCalibrationState(): EnumCalibrationFeedback? {
        repeat(getCalibrationDuration() / 5) {
            val calibrationState =
                findGO()?.syncGetGroupCalibrationStateWithTimeout(TAG, 0, 5 * 1000)

            val state = calibrationState?.calibration?.state
            val feedback = calibrationState?.calibration?.feedback
            if (state.equals("done",true) && !feedback.isNullOrEmpty()) {
                return EnumCalibrationFeedback.fromValue(feedback)
            }
//            if (feedback?.isCalibrating() == false) {
//                return feedback
//            }
            delay(5 * 1000)
        }
        return EnumCalibrationFeedback.CALI_GROUP_FEEDBACK_SUCCESS

    }

    fun calibrating() {
        findGO()?.wlan0MacWithoutColon()?.also { findGO()?.groupCalibration(it, 0) }
        viewModelScope.launch {
            val groupParam =
                findGO()?.syncGetGroupParameterWithTimeout(TAG, 0, GENERAL_TIMEOUT_MILLS)
            CacheRepository.getInstance()
                .saveGroupInfo(findGO()?.UUID, findGO()?.groupInfoExt?.groupInfo)
            CacheRepository.getInstance().saveGroupParameter(findGO()?.UUID, groupParam)
        }
//        findGO()?.getGroupParameter()
        viewModelScope.launch {
            if (combinationItem.needCalibrationResult()) {
                delay(5 * 1000)
                var state = loopGetGroupCalibrationState()
                updateUIByCalibrationState(state)
            } else {
                delay(getCalibrationDuration().toLong() * 1000)
                postSoundTuningState(EnumMultichannelStatus.CALIBRATION_SUCCESS)

            }

        }
        viewModelScope.launch {
            delay(12 * 1000)
            findGO()?.getGroupParameter()

        }

    }

    fun unregisterAllDeviceListener() {
        getSelectDevice().forEach { item ->
            unregisterDeviceListener(item)

        }
    }

    fun unregisterDeviceListener(device: OneDevice) {
        device.unregisterDeviceListener(deviceListener)

    }

    fun initModelNameAndColors() {
        val list = getSelectDeviceByOrder()
        Logger.d(TAG, "grouping deviceList = $list ")
        modelNameAndColors2 = list.map {
            android.util.Pair.create(
                it.modelName(),
                it.colorId()
            )
        }
        Logger.d(TAG, " list3 = $modelNameAndColors2")
        _modelNameAndColors.value = modelNameAndColors2
    }

    var editable = false
    fun grouping() {
        viewModelScope.launch {

            val union = findGO()?.syncGetDeviceNameWithTimeout(TAG)
            Logger.d(TAG, "grouping union = $union ")
            editable = union?.editable ?: false

            val list = getSelectDeviceByOrder()

            val requests = list.map { item ->
                async { item.syncGetGroupInfoWithTimeout(TAG, 0, 15 * 1000) }
            }
            var groupInfolist = requests.awaitAll()
            val notSingleInfo = groupInfolist.find { item-> item?.isSingle() == false }
            if(notSingleInfo != null){
                Logger.e(TAG, "grouping exist one  notSingleInfo = $notSingleInfo")
                groupFail()
                return@launch
            }
            val hmCastVers = list.map { item -> item.hmCastVer }
            val hmCastMajorVers = list.map { item -> item.hmCastMajorVer() }
            Logger.d(TAG, "grouping groupinfos = $groupInfolist hmCastVers = $hmCastVers hmCastMajorVers = $hmCastMajorVers")
            var cmdStr = makeGroupCommand(list, groupInfolist)
//            Logger.d(TAG, "grouping cmdStr= $cmdStr")

            list.forEach { item -> item.setCastGroup(cmdStr) }
            findGO()?.registerDeviceListener(deviceListener)
            delay(20 * 1000)
            if (loopGetGroupInfo()) {
                groupSuccess()
            } else {
                groupFail()
            }

        }
        viewModelScope.launch { startGroupTimer() }


    }

    private suspend fun startGroupTimer() {
        val repeatTimes = getGroupDuration() * 1000 / 50
        val step = maxProgress / repeatTimes
        repeat(repeatTimes) {
            if (groupFinish) {
                _progress.postValue(maxProgress)
                return
            }
            val current = _progress.value ?: 0
//            Logger.d(
//                TAG,
//                "startGroupTimer current = $current percent = ${current * 100.0 / maxProgress} "
//            )
            var newProgress = step + current
            if (newProgress > maxProgress) {
                newProgress = maxProgress
            }
            _progress.postValue(newProgress)
            delay(50)

        }
    }


    fun groupFail() {
        _multichannelStatus.postValue(com.harman.multichannel.EnumMultichannelStatus.GROUP_FAIL)

    }

    suspend fun loopGetGroupInfo(): Boolean {
        repeat(getGroupDuration() / 10) {
            val groupInfo = findGO()?.syncGetGroupInfoWithTimeout(TAG, 0, 10 * 1000)
            if (groupInfo?.groupMode == GroupMode.GROUP.value) {
                return true
            }
            delay(10 * 1000)
        }
        return false

    }

    fun findGO(): OneDevice? {
        if (combinationItem.combinationType == "2.0" || combinationItem.combinationType == "4.0" || combinationItem.combinationId == "5.0_208c_208c_208c_208c_208c") {
            return entryPoint

        } else {
            return getSelectDevice().firstOrNull() { item -> combinationItem.modelList[0].modelName.contains(item.modelName()) }
        }
    }

    private fun getRandomPwd(): String {
        return generateRandomString("0123456789", 8)
    }

    private fun getGroupId(): String {
        return generateRandomString("abcdef0123456789", 4)
    }

    private fun generateRandomString(allCharacters: String, length: Int): String {
        val randomString = StringBuffer()
        for (i in 0 until length) {
            val randomIndex = (Math.random() * allCharacters.length).toInt()
            if (randomIndex >= 0 && randomIndex < allCharacters.length) {
                randomString.append(allCharacters[randomIndex])
            } else {
                randomString.append("0")
            }
        }
        return randomString.toString()
    }

    private fun getP2pMac(device: OneDevice, infos: List<GetGroupInfoRsp?>): String? {
        return infos.firstOrNull { item ->
            item?.p2pMac?.substring(2) == device.wlan0Mac()?.substring(2)
        }?.p2pMac ?: device.wlan0MacWithoutColon()?.replaceSecondChar('a')

    }

    fun String.replaceSecondChar(newChar: Char): String {
        if (isEmpty() || length < 2) return this
        return this[0] + newChar.toString() + substring(2)
    }

    private fun makeGroupCommand(
        deviceArray: List<OneDevice>, infos: List<GetGroupInfoRsp?>
    ): String {
        val first: OneDevice = deviceArray[0]
        val groupParam = SetCastGroup()
        val groupInfo = GroupInfo()
        groupInfo.disabled = false
        val group = Group()
        group.groupId = getGroupId()
        group.id = first.wlan0MacWithoutColon()
        group.type = "multichannel"
        group.p2pMac = first.groupInfoExt?.groupInfo?.p2pMac ?: getP2pMac(first, infos)  //p2p_mac
        if(!(first.isBLEOnline && combinationItem.isStereo())){
            group.combinationId = combinationItem.combinationId
        }

        val members = ArrayList<Member>()
        for (j in deviceArray.indices) {
            val authDevice = deviceArray[j]
            val member = Member()
//            if(first.isBLEOnline && combinationItem.isStereo()){
//                member.friendlyName = authDevice.modelName()?.replace("JBL ","",true)?.replace("Harman Kardon ","",true)
//            }else{
//                member.friendlyName = authDevice.displayShortDeviceName()
//            }
            member.friendlyName = authDevice.displayShortDeviceName()
            member.id = authDevice.wlan0MacWithoutColon()
            member.colorId = (authDevice.cid?: "1").toIntOrNull() ?: 1
            member.crc = authDevice.UUID
            member.deviceName = authDevice.modelName()

            members.add(member)
        }
        updateMembersChannel(members)

        group.name = getGroupName(first)
        groupInfo.group = group
        groupInfo.members = members
        groupParam.groupInfo = groupInfo
        var cmd = instance.toJson(groupParam)
        Logger.d(TAG, "grouping makeGroupCommand cmd = ${cmd?.length} cmd = $cmd")
//        if (combinationItem.isStereo()) {
//            cmd = cmd?.replace(",\"latency\":0.0,\"volume\":0.0".toRegex(), "")
//                ?.replace(",\"volume\":0.0,\"latency\":0.0".toRegex(), "")
//            Logger.d(TAG, "after replace makeGroupCommand cmd = ${cmd?.length} cmd = $cmd")
//        }
        return cmd!!
    }

    private fun getGroupName(goDevice: OneDevice?): String? {
        var result: String? = null
        if (editable) {
            if (combinationItem.isStereo()) {
                result = STEREO_GROUP_NAME
            } else if (goDevice?.isSoundBar() == true) {
                result = BAR_GROUP_NAME
            } else if (goDevice?.isOneCommander() == true) {
                result = ONE_COMMANDER_GROUP_NAME
            }else {
                result = OTHER_GROUP_NAME
            }
        } else {
            result = goDevice?.deviceName
        }
        return result
    }

    fun getGroupName(): String? {
        return getGroupName(findGO())

    }

    private fun updateMembersChannel(members: List<Member>) {
        val modelList = combinationItem.modelList
        val newModelList: MutableList<ModelItem> = java.util.ArrayList()
        newModelList.addAll(modelList)
        var anchorPointMember: Member? = null
        var leftChannelMember: Member? = null
        for (member in members) {
            val result = getChannelListByConfig(member, newModelList)
            Logger.d(TAG, "member:$member result:$result")
            if (result != null && !result.isEmpty()) {
                member.channel = result
            }
//            if (combinationItem.isStereo()) {
//                member.latency = getLatency(member)
//                member.volume = getVolume(member)
//            }
            if (member.id.equals(findGO()?.wlan0MacWithoutColon(), ignoreCase = true)) {
                anchorPointMember = member
            }
            if (isSameChannel(member.channel, getLeftChannelList())) {
                leftChannelMember = member
            }
        }
        Logger.d(TAG, "anchorPointMember:$anchorPointMember")
        Logger.d(TAG, "leftChannelMember:$leftChannelMember")
        if (anchorPointMember != null && leftChannelMember != null && !anchorPointMember.id.equals(leftChannelMember?.id, ignoreCase = true) && combinationItem.isStereo()) {
            val temp: List<String> = java.util.ArrayList(anchorPointMember.channel)
            anchorPointMember.channel = getLeftChannelList()
            leftChannelMember.channel = temp
            Logger.d(TAG, "after switch anchorPointMember:$anchorPointMember")
            Logger.d(TAG, "after switch leftChannelMember:$leftChannelMember")
        }
    }

    private fun isSameChannel(channel1: List<String>, channel2: List<String>): Boolean {
        return channel1.containsAll(channel2) && channel2.containsAll(channel1)
    }

    private fun getLeftChannelList(): List<String> {
        val list: MutableList<String> = java.util.ArrayList()
        list.add(Channel.Left.channel)
        return list
    }

    private fun getLatency(member: Member): Float {
        val supportModel = getSupportModel(member) ?: return 0F
        val lat = supportModel.latency
        var lat2 = 0f
        try {
            lat2 = lat.toFloat()
        } catch (e: NumberFormatException) {
            e.printStackTrace()
        }
        return lat2
    }

    private fun getVolume(member: Member): Float {
        val supportModel = getSupportModel(member) ?: return 0F
        val vol = supportModel.volume
        var vol2 = 0f
        try {
            vol2 = vol.toFloat()
        } catch (e: NumberFormatException) {
            e.printStackTrace()
        }
        return vol2
    }

    private fun getSupportModel(member: Member): SupportItem? {
        return com.harman.multichannel.HarmancastManager.getInstance().combinationSupportList.filter { item -> item.modelName == member.deviceName }
            .getOrNull(0)
    }

    private fun getChannelListByConfig(
        member: Member, modelList: MutableList<ModelItem>
    ): List<String>? {
        var findIndex = -1
        var result: List<String>? = null
        for (i in modelList.indices) {
            val chModel = modelList[i]
            if (chModel.modelName.contains(member.deviceName)) {
                findIndex = i
                result = chModel.channelOutput
            }
        }
        if (findIndex != -1) {
            modelList.removeAt(findIndex)
        }
        return result
    }

    fun iniSelectDevice() {
        var list = com.harman.multichannel.HarmancastManager.getInstance()
            .getListByCombinationItem(combinationItem, wifiOnlineDevices?.filterVisibleDevice())
            .filter { item -> item != entryPoint }
        _selectedDevices.addAll(list)
    }

    fun List<OneDevice>?.filterVisibleDevice(): List<OneDevice>? {
        val source = this
        return source?.filter { item -> item?.hmCastMajorVer() == entryPoint?.hmCastMajorVer() }
    }

    fun renameGroup(newName: String) {
        Logger.d(TAG, "renameGroup viewmodel newName = $newName")
        findGO()?.renameGroup(newName)

    }

    fun startScan(ctx: Context?) {
        Logger.i(TAG, "startScan() >>> start scan")
        _scanDevices.postValue(DeviceStore.baseDevices.filterDevice())
        DeviceScanner.registerObserver(observer = scanObserver)
    }

    fun stopScan(ctx: Context?) {
        Logger.i(TAG, "stopScan() >>> stop scan")
        DeviceScanner.unregisterObserver(observer = scanObserver)
    }

    private val _scanDevices = MutableLiveData<List<OneDevice>>()
    val scanDevices: LiveData<List<OneDevice>>
        get() = _scanDevices.distinctUntilChanged()

    private val scanObserver = object : IHmDeviceObserver {
        override fun onDevicesUpdate(devices: List<Device>) {
            val filteredDevices = devices.filterDevice()
            _scanDevices.postValue(filteredDevices)
//            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
//
//            }
        }
    }

    fun List<Device>.filterDevice(): List<OneDevice> {
        val source = this
        return source.filter { device ->
            combinationItem.containModelName(device.modelName()) && device != entryPoint
        }.filterIsInstance<OneDevice>().filter { device -> !device.isNetworkConnected }
    }

    private val _checkStereoSuccess = MutableLiveData<Boolean>()
    val checkStereoSuccess: LiveData<Boolean>
        get() = _checkStereoSuccess.distinctUntilChanged()

    fun checkBLEStereo(context: Context) {
        val list = getSelectDeviceByOrder()
        val deviceInfoList = mutableListOf<DeviceInfo>()
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            list.forEach { device ->
                val deviceInfo = connectAndGetDeviceInfo(device, context)
                if (deviceInfo == null) {
                    _checkStereoSuccess.postValue(false)
                    Logger.w(TAG, "checkBLEStereo>>> fail to get device info for ${device.UUID}")
                    return@launch

                } else {
                    deviceInfoList.add(deviceInfo)
                }

            }
            if (isSameHarmanCastVersion(deviceInfoList)) {
                _checkStereoSuccess.postValue(true)
            } else {
                _checkStereoSuccess.postValue(false)
            }


        }

    }

    private suspend fun isSameHarmanCastVersion(list: List<DeviceInfo>): Boolean {
        return withContext(DISPATCHER_DEFAULT) {
            Logger.w(TAG, "isSameHarmanCastVersion>>> $list")
            val map = hashMapOf<String?, DeviceInfo>()
            list.forEach { deviceInfo ->
                val index = deviceInfo.hmCastVer?.indexOf(".")
                deviceInfo.hmCastVer?.indexOf(".")
                    ?.also { map.put(deviceInfo.hmCastVer?.substring(0, it), deviceInfo) }

            }
            map.size == 1

        }
    }

    private suspend fun connectAndGetDeviceInfo(device: OneDevice, context: Context): DeviceInfo? {
        return withContext(DISPATCHER_DEFAULT) {
            if (!device.syncGattConnectWithTimeout(context = context)) {
                Logger.w(TAG, "connectAndGetDeviceInfo>>> fail to connect Gatt")
                null
            }
            device.syncGetDeviceInfoWithTimeout(logTag = TAG)
        }

    }


    companion object {
        private const val TAG = "MultichannelViewModel"
        private const val DEFAULT_MAX_PROGRESS = 2 * 3 * 5 * 7 * 11 * 13 * 17 * 19 * 200
        private const val STEREO_GROUP_NAME = "STEREO"
        private const val BAR_GROUP_NAME = "HOME THEATRE"
        private const val ONE_COMMANDER_GROUP_NAME = "Bar 1300MK2 + One Commander"  //by design and no need translation
        private const val OTHER_GROUP_NAME = "HOME AUDIO SYSTEM"

    }


}
