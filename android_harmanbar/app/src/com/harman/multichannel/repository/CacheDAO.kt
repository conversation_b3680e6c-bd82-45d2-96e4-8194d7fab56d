package com.harman.multichannel.repository

import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.streaming.roon.CampaignResponse

interface CacheDAO {
    fun saveGroupInfo(crc: String, getGroupInfo: GetGroupInfo)

    fun removeGroupInfo(crc: String)

    fun getGroupInfo(crc: String): GetGroupInfo?

    fun getAllGroupInfo(): List<GetGroupInfo>?

    fun saveGroupParameter(crc: String, groupParameterRsp: GetGroupParameterRsp)

    fun removeGroupParameter(crc: String)

    fun getGroupParameter(crc: String): GetGroupParameterRsp?

    fun getAllGroupParameter(): List<GetGroupParameterRsp>?

    fun saveCampaign(crc: String,campaign: CampaignResponse)

    fun removeCampaign(crc: String)

    fun getCampaign(crc: String): CampaignResponse?

    fun saveFlag(key: String,value: Boolean)
    fun getFlag(key: String): <PERSON><PERSON><PERSON>


}