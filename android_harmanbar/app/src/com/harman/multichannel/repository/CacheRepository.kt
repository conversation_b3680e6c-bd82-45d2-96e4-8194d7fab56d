package com.harman.multichannel.repository

import androidx.annotation.WorkerThread
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.streaming.roon.CampaignResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class CacheRepository private constructor(val groupDAO: CacheDAO) {


    suspend fun saveGroupInfo(crc: String?, getGroupInfo: GetGroupInfo?) {
        if (crc.isNullOrBlank() || getGroupInfo == null) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.saveGroupInfo(crc, getGroupInfo)
        }
    }

    suspend fun removeGroupInfo(crc: String?) {
        if (crc.isNullOrBlank()) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.removeGroupInfo(crc)
        }
    }

    suspend fun removeGroupCache(crc: String?) {
        if (crc.isNullOrBlank()) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.removeGroupInfo(crc)
            groupDAO.removeGroupParameter(crc)
        }
    }

    // This is a time cost function.
    @WorkerThread
    suspend fun getGroupInfo(crc: String?): GetGroupInfo? {
        if (crc.isNullOrBlank()) {
            return null
        }
        return withContext(Dispatchers.IO) {
            groupDAO.getGroupInfo(crc)
        }
    }


    suspend fun getAllGroupInfo() =
        withContext(Dispatchers.IO) {
            groupDAO.getAllGroupInfo()?.distinctBy { it.groupInfo?.getValidGroupId() }
        }

    suspend fun saveGroupParameter(crc: String?, groupParameter: GetGroupParameterRsp?) {
        if (crc.isNullOrBlank() || groupParameter == null) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.saveGroupParameter(crc, groupParameter)
        }
    }

    suspend fun removeGroupParameter(crc: String?) {
        if (crc.isNullOrBlank()) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.removeGroupParameter(crc)
        }
    }


    suspend fun getGroupParameter(crc: String?): GetGroupParameterRsp? {
        if (crc.isNullOrBlank()) {
            return null
        }
        return withContext(Dispatchers.IO) {
            groupDAO.getGroupParameter(crc)
        }
    }


    suspend fun getAllGroupParameter() =
        withContext(Dispatchers.IO) {
            groupDAO.getAllGroupParameter()
        }


    suspend fun saveCampaign(crc: String?, campaign: CampaignResponse?) {
        if (crc.isNullOrBlank() || campaign == null) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.saveCampaign(crc, campaign)
        }
    }

    suspend fun removeCampaign(crc: String?) {
        if (crc.isNullOrBlank()) {
            return
        }
        withContext(Dispatchers.IO) {
            groupDAO.removeCampaign(crc)
        }
    }

    suspend fun getCampaign(crc: String?): CampaignResponse? {
        if (crc.isNullOrBlank()) {
            return null
        }
        return withContext(Dispatchers.IO) {
            groupDAO.getCampaign(crc)
        }
    }

    suspend fun getFlag(key: String): Boolean {
        return withContext(Dispatchers.IO) {
            groupDAO.getFlag(key)
        }
    }

    suspend fun saveFlag(key: String, value: Boolean) {
        withContext(Dispatchers.IO) {
            groupDAO.saveFlag(key, value)
        }
    }


    companion object {


        private var instance: CacheRepository? = null


        fun getInstance(): CacheRepository {
            if (instance == null) {
                synchronized(CacheRepository::class.java) {
                    if (instance == null) {
                        instance = CacheRepository(CacheDAOImpl())
                    }
                }
            }
            return instance!!
        }


    }


}