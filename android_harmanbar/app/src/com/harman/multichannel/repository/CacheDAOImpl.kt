package com.harman.multichannel.repository

import com.blankj.utilcode.util.CacheDoubleUtils
import com.google.gson.Gson
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.streaming.roon.Campaign
import com.harman.log.Logger
import com.harman.streaming.roon.CampaignResponse
import java.util.concurrent.CopyOnWriteArrayList

class CacheDAOImpl : CacheDAO {
    private val cacheDoubleUtils: CacheDoubleUtils = CacheDoubleUtils.getInstance()
    private val gson: Gson = Gson()
    override fun saveGroupInfo(crc: String, getGroupInfo: GetGroupInfo) {
        saveCrc(crc)
        cacheDoubleUtils.put(getGroupInfoKey(crc), getGroupInfo)

    }

    override fun removeGroupInfo(crc: String) {
        removeCrc(crc)
        cacheDoubleUtils.remove(getGroupInfoKey(crc))
    }

    override fun getGroupInfo(crc: String): GetGroupInfo? {

        return kotlin.runCatching { cacheDoubleUtils.getSerializable(getGroupInfoKey(crc)) as? GetGroupInfo? }.getOrNull()
    }

    override fun getAllGroupInfo(): List<GetGroupInfo>? {
        val crcList = getCrcList()
//        Logger.i(TAG, "crcList $crcList")
        return crcList?.mapNotNull { crc ->
//            val crcNew = getGroupInfoKey(crc)
//            Logger.i(TAG, "crcNew $crcNew")
            val groupInfo = getGroupInfo(crc)
//            Logger.i(TAG, "crc $crc groupInfo $groupInfo")
            groupInfo
        }
    }

    override fun saveGroupParameter(crc: String, groupParameterRsp: GetGroupParameterRsp) {
        saveCrc(crc)
        cacheDoubleUtils.put(getGroupParameterKey(crc), groupParameterRsp)
    }

    override fun removeGroupParameter(crc: String) {
        removeCrc(crc)
        cacheDoubleUtils.remove(getGroupParameterKey(crc))
    }

    override fun getGroupParameter(crc: String): GetGroupParameterRsp? {

        return kotlin.runCatching { cacheDoubleUtils.getSerializable(getGroupParameterKey(crc)) as? GetGroupParameterRsp?  }
            .getOrNull()
    }

    override fun getAllGroupParameter(): List<GetGroupParameterRsp>? {
        return getCrcList()?.mapNotNull { crc -> getGroupParameter(crc) }
    }

    override fun saveCampaign(crc: String, campaign: CampaignResponse) {
        cacheDoubleUtils.put(getRoonReadyKey(crc), gson.toJson(campaign))
    }

    override fun removeCampaign(crc: String) {
        cacheDoubleUtils.remove(getRoonReadyKey(crc))
    }

    override fun getCampaign(crc: String): CampaignResponse? {
        val str = cacheDoubleUtils.getString(getRoonReadyKey(crc))
        if (str.isNullOrBlank()) {
            return null
        }
        return kotlin.runCatching { gson.fromJson(str, CampaignResponse::class.java) }
            .getOrNull()
    }

    override fun saveFlag(key: String, value: Boolean) {
        cacheDoubleUtils.put(key, value)
    }

    override fun getFlag(key: String): Boolean {
        val str = cacheDoubleUtils.getSerializable(key) ?: return false
        return kotlin.runCatching { str as Boolean }
            .getOrNull() ?: false
    }


    private fun saveCrc(crc: String) {
        val list = getCrcList() ?: CopyOnWriteArrayList()
        if (!list.contains(crc)) {
            list.add(crc)
            cacheDoubleUtils.put(PREFIX_CRC_LIST, list)
        }
    }

    private fun removeCrc(crc: String) {
        val list = getCrcList() ?: CopyOnWriteArrayList()
        if (list.contains(crc)) {
            list.remove(crc)
            cacheDoubleUtils.put(PREFIX_CRC_LIST, list)
        }
    }

    private fun getCrcList(): CopyOnWriteArrayList<String>? {
        val crcList = cacheDoubleUtils.getSerializable(PREFIX_CRC_LIST) ?: return null
        if (crcList is CopyOnWriteArrayList<*>) {
            return crcList as CopyOnWriteArrayList<String>?

        } else if (crcList is ArrayList<*>) {
            val newList = CopyOnWriteArrayList<String>()
            (crcList as ArrayList<String>).forEach { newList.add(it) }
            return newList
        } else {
            return null
        }


    }

    private fun getGroupInfoKey(crc: String): String {
        return PREFIX_GROUP_INFO + crc

    }

    private fun getGroupParameterKey(crc: String): String {
        return PREFIX_GROUP_PARAMETER + crc

    }

    private fun getRoonReadyKey(crc: String): String {
        return PREFIX_ROON_READY + crc

    }

    companion object {
        const val TAG = "GroupDAOImpl"
        const val PREFIX_CRC_LIST = "CRC_LIST__"
        const val PREFIX_GROUP_INFO = "GROUP_INFO__"
        const val PREFIX_GROUP_PARAMETER = "GROUP_PARAMETER__"
        const val PREFIX_ROON_READY = "ROON_READY__"

    }
}