package com.harman.multichannel

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.databinding.FragmentMultichannelTypeListBinding
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.jbl.one.configuration.model.CombinationItem
import kotlinx.coroutines.launch


/**
 * Created by sky on 2024/8/20.
 */
class MultichannelTypeListFragment : com.harman.multichannel.BaseMultiChannelFragment() {

    private val viewModel: MultichannelViewModel by activityViewModels()


    private val _adapter = MutableLiveData<MultichannelTypeAdapter>()
    val adapter: LiveData<MultichannelTypeAdapter>
        get() = _adapter

    private val _itemDecoration = MutableLiveData<RecyclerView.ItemDecoration>()
    val itemDecoration: LiveData<RecyclerView.ItemDecoration>
        get() = _itemDecoration

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentMultichannelTypeListBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this
        binding.fragment = this
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val adapter = MultichannelTypeAdapter(
                datas = viewModel.getMultichannelTypeUIModels(),
                clickListener = clickListener,
                viewModel
            )

            _adapter.value = adapter
            _itemDecoration.value = MultichannelTypeRecyclerViewDecoration(adapter = adapter)
        }


        return binding.root
    }


    private val clickListener = object : IMultichannelTypeClickListener {

        override fun onClick(multichannelTypeUIModel: MultichannelTypeUIModel) {
            Logger.d(TAG, "onClick() >>> $multichannelTypeUIModel")
            if(multichannelTypeUIModel.enable()){
                val combinationItem = multichannelTypeUIModel.data
                combinationItem?.also {
                    viewModel.combinationItem = it
                    viewModel.placementCombinationItem = viewModel.getPlacementCombinationItem(it)
                    viewModel.clearSelectDevice()
                    if (viewModel.onlyHaveDevicesForCombinationItem(it)) {
                        viewModel.iniSelectDevice()
                        if(it.notNeedCalibration()){
                            nextStep()

                        }else{
                            showCalibrationTipDialog()
                        }


                    } else {
                        showSelectDeviceDialog()
                    }
                }

            }

        }
    }

    private fun nextStep(){
        val activity = multiChannelActivity ?: return
        if(viewModel.hasPlacement()){
            activity.setTopFragment(fragmentHelper.multichannelPlacementFragment)
        }else{
            viewModel.initModelNameAndColors()
            activity.setTopFragment(fragmentHelper.multichannelGroupingFragment)
        }
    }


    private var calibrationTipDialog: com.harman.multichannel.CalibrationTipDialog? = null
    private fun showCalibrationTipDialog() {
        val activity = multiChannelActivity ?: return

        calibrationTipDialog?.dismiss()
        calibrationTipDialog = com.harman.multichannel.CalibrationTipDialog(
            context = activity,
            listener = CalibrationTipDialogListener(activity)

        ).apply {
            show()
        }

    }

    private var selectDeviceDialog: SelectDeviceDialog? = null
    private fun showSelectDeviceDialog() {
        val activity = multiChannelActivity ?: return

        selectDeviceDialog?.dismiss()
        selectDeviceDialog = SelectDeviceDialog(
            viewModel,
            context = activity,
            listener = SelectDeviceDialogListener(activity)
        ).apply {
            show()
        }

    }

    private val fragmentHelper = MultichannelFragmentHelper()

    private inner class CalibrationTipDialogListener(
        private val context: Context
    ) : com.harman.multichannel.IDialogEvent {
        override fun onCloseBtnClick() {
            calibrationTipDialog?.dismiss()
        }

        override fun onOKBtnClick() {
            val activity = multiChannelActivity ?: return

            calibrationTipDialog?.dismiss()
            activity.setTopFragment(fragmentHelper.multichannelPlacementFragment)
        }
    }

    private inner class SelectDeviceDialogListener(
        private val context: Context
    ) : com.harman.multichannel.IDialogEvent {
        override fun onCloseBtnClick() {
            selectDeviceDialog?.dismiss()
        }

        override fun onOKBtnClick() {
            selectDeviceDialog?.dismiss()
            viewModel.combinationItem?.also {
                if(it.notNeedCalibration()){
                    nextStep()
                }else{
                    showCalibrationTipDialog()
                }
            }


        }
    }


    companion object {

        private const val TAG = "MultichannelTypeListFragment"


    }
}