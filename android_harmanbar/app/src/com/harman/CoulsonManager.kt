package com.harman

import com.blankj.utilcode.util.StringUtils
import com.harman.log.Logger

class CoulsonManager {

    private var diActionEntry: String? = null

    private var coulsonSetupItem: CoulsonSetupModel? = null

    private var reportTimeMap = HashMap<String, Long>()


    fun recordCoulsonEntryValue(coulsonEntryValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordCoulsonEntryValue $coulsonEntryValue ")
        diActionEntry = coulsonEntryValue
        coulsonSetupItem = CoulsonSetupModel().apply {
            coulsonEntry = coulsonEntryValue
        }
    }

    fun recordLandOnServiceValue(landOnServiceValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordLandOnServiceValue $landOnServiceValue ")
        coulsonSetupItem?.apply {
            landOnService = landOnServiceValue
        }
    }

    fun recordLandOnServiceVaValue(landOnServiceVaValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordLandOnServiceVaValue $landOnServiceVaValue ")
        coulsonSetupItem?.apply {
            landOnServiceVa = landOnServiceVaValue
        }
    }

    fun recordAlexaCancelValue(alexaCancelValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordAlexaCancelValue $alexaCancelValue ")
        coulsonSetupItem?.apply {
            alexaCancel = alexaCancelValue
        }
        initVaTime(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value,false)
        if (StringUtils.equals(EventUtils.Dimension.EnumVAResult.TRUE.value, alexaCancelValue)) {
            reportCoulsonSetupData()
        }
    }

    fun recordGvaCancelValue(gvaCancelValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordGvaCancelValue $gvaCancelValue ")
        coulsonSetupItem?.apply {
            gvaCancel = gvaCancelValue
        }
        initVaTime(EventUtils.Dimension.EnumVaSetupOption.GVA.value,false)
        if (StringUtils.equals(EventUtils.Dimension.EnumVAResult.TRUE.value, gvaCancelValue)) {
            reportCoulsonSetupData()
        }
    }

    fun recordVaSetupOptionValueForce(vaSetupOptionValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValueForce $vaSetupOptionValue landOnServiceVa :${coulsonSetupItem?.landOnServiceVa}")
        coulsonSetupItem?.apply {
            vaSetupOptionValue?.let {
                option = VaOptionItem.findItemByOptionName(it)
                Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValueForce ${option?.optionName} ")
            }
        }
    }

    fun recordVaSetupOptionValue(vaSetupOptionValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValue $vaSetupOptionValue landOnServiceVa :${coulsonSetupItem?.landOnServiceVa}")
        coulsonSetupItem?.apply {
            if ((EventUtils.Dimension.EnumVAResult.TRUE.value.equals(landOnServiceVa, true) && EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value.equals(option?.optionName, true))) {
                return
            }
            vaSetupOptionValue?.let { 
                option = VaOptionItem.findItemByOptionName(it)
                Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValue ${option?.optionName} ")


            }
        }
    }

    fun recordNewSetup(name: String) {
        Logger.i(TAG,"logEvent - EventName : recordNewSetup $name ")
        coulsonSetupItem?.apply {
            newSetup(name)
        }
        initVaTime(name,true)
    }

    private fun initVaTime(name: String, isStart: Boolean) {
        Logger.i(TAG,"logEvent - EventName : initVaTime $name $isStart $coulsonSetupItem")
        coulsonSetupItem?.apply {
            if(EventUtils.Dimension.EnumVaSetupOption.GVA.value == name){
                if(isStart){
                    gvaSetupStart = System.currentTimeMillis()/1000
                }else{
                    gvaSetupEnd = System.currentTimeMillis()/1000
                    gvaSetupDuration = gvaSetupEnd - gvaSetupStart
                }

            }else if(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value  == name){
                if(isStart){
                    alexaSetupStart = System.currentTimeMillis()/1000
                }else{
                    alexaSetupEnd = System.currentTimeMillis()/1000
                    alexaSetupDuration = alexaSetupEnd - alexaSetupStart
                }
            }
            Logger.i(TAG,"logEvent - EventName : initVaTime2 $gvaSetupStart $gvaSetupEnd $gvaSetupDuration")
        }
    }

    fun recordVaSetupResultValue(name: String, isSuccess: Boolean) {
        Logger.i(TAG,"logEvent - EventName : recordVaSetupResultValue $name $isSuccess")
        initVaTime(name,false)
        coulsonSetupItem?.apply {
            setupResult(name, isSuccess)
            val flag = isFlowFinish
            Logger.i(TAG,"logEvent - EventName : isFlowFinish $flag ")
            if (flag) {
                reportCoulsonSetupData()
            }
        }
    }

    fun printStackTrace(thread: Thread) {
        Logger.i(TAG,"logEvent - EventName : printStackTrace start ===============================================================================")
        for (e in thread.stackTrace) {
            Logger.i(TAG,"logEvent - EventName : $e")
        }
        Logger.i(TAG,"logEvent - EventName : printStackTrace end =================================================================================")
    }

    fun reportCoulsonSetupData() {
        if (canReport() == false ) {
            return
        }
        coulsonSetupItem?.let {
            val isDuplicateEvent = isDuplicateReport(it.finalResult)
            if (!isDuplicateEvent) {
                return
            }
        }
        clearData()
    }

    private fun isDuplicateReport(eventName: String): Boolean {
        val preReportTime = reportTimeMap[eventName]
        reportTimeMap[eventName] = System.currentTimeMillis()
        if (preReportTime == null) {
            return true
        }
        val diff = System.currentTimeMillis() - preReportTime
        val isValid = (diff > TIME_THRESHOLD)
        if (!isValid) {
            Logger.i(TAG,"logEvent - EventName : $eventName is not valid since it reported many times in a short period of time")
        }
        return isValid
    }

    private fun canReport(): Boolean? {
        return coulsonSetupItem?.isDataValid
    }

    private fun clearData() {
        coulsonSetupItem?.clear()
    }

    companion object {
        const val TAG = "CoulsonManager"
        val instance = CoulsonManager()
        const val TIME_THRESHOLD = 5000
    }
}