package com.harman.music

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.harman.bar.app.R
import com.harman.buildGlideImgLoadConfig
import com.harman.command.one.bean.EnumSoundscapeV2State
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.info.AudioSource
import com.harman.discover.util.Tools.isNullOrEmpty
import com.harman.discover.util.Tools.roundPercentage
import com.harman.discover.util.Tools.safeResume
import com.harman.log.Logger
import com.harman.moment.EnumMoment
import com.harman.moment.EnumMomentVersion
import com.harman.moment.MomentViewModel
import com.harman.music.service.EnumMusicServiceSource
import com.linkplay.lpmdpkit.LPPlaySourceType
import com.utils.glide.BitmapLoadingListener
import com.utils.glide.GlideMgtUtil
import com.wifiaudio.action.lpmslib.calmradio.LPMSCalmRadioAction
import com.wifiaudio.app.LinkplayApplication
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DlnaPlayerStatus
import com.wifiaudio.model.MenuSlideItem
import com.wifiaudio.model.local_music.MainItem
import com.wifiaudio.model.local_music.MusicSourceConstants
import com.wifiaudio.model.menubar.MenuBar
import com.wifiaudio.model.menubar.MenuBarConstants
import com.wifiaudio.model.menubar.MenuBarConstantsImpl
import com.wifiaudio.service.DlnaServiceProviderPool
import com.wifiaudio.service.WAConstants.IStorageMediumType
import com.wifiaudio.utils.okhttp.HttpRequestUtils
import com.wifiaudio.utils.okhttp.OkHttpResponseItem
import com.wifiaudio.view.alarm.bean.ItemAlarmSource
import com.wifiaudio.view.pagesmsccenter.NewPlayControlFragment
import kotlinx.coroutines.suspendCancellableCoroutine
import org.teleal.cling.support.playqueue.callback.xml.IPlayQueueType
import org.teleal.cling.support.playqueue.callback.xml.IPlayQueueTypeImpl

/**
 * Created by gerrardzhang on 2024/7/2.
 */
internal object Tools {

    fun EnumMusicServiceSource.toMainItem(): MainItem? =
        when (this.tagValue) {
            MusicSourceConstants.SOURCE_IHEART -> MainItem().apply {
                Name = WAApplication.me.getString(R.string.harmanbar_iheartradio_iHeartRadio)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_iheartradio
                Key = MenuBarConstants.STREAM_IHEART
            }
            MusicSourceConstants.SOURCE_TIDAL -> MainItem().apply {
                Name = WAApplication.me.getString(com.wifiaudio.R.string.Tidal)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_tidal
                Key = MenuBarConstants.STREAM_TIDAL
            }
            MusicSourceConstants.SOURCE_VTUNER -> MainItem().apply {
                Name = WAApplication.me.getString(R.string.harmanbar_content_vTuner)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_vtuner
                Key = MenuBarConstants.STREAM_VTUNER
            }
            MusicSourceConstants.SOURCE_NAPSTER -> MainItem().apply {
                Name = WAApplication.me.getString(R.string.harmanbar_content_Napster)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_napster
                Key = MenuBarConstants.STREAM_NAPSTER
            }
            MusicSourceConstants.SOURCE_QOBUZ -> MainItem().apply {
                Name = WAApplication.me.getString(R.string.harmanbar_content_Qobuz)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_qobuz
                Key = MenuBarConstants.STREAM_QOBUZ
            }
            MusicSourceConstants.SOURCE_PRIME_MUSIC -> MainItem().apply {
                Name = WAApplication.me.getString(R.string.harmanbar_primemusic_Amazon_Music)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_amazon_music
                Key = MenuBarConstants.STREAM_PRIME
            }
            MusicSourceConstants.SOURCE_TUNEIN_NEW -> MainItem().apply {
                Name = WAApplication.me.getString(R.string.harmanbar_tunein_TuneIn)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_tunein
                Key = MenuBarConstants.STREAM_TUNEIN_NEW
            }
            MusicSourceConstants.SOURCE_SOUNDMACHINE -> MainItem().apply {
                Name = WAApplication.me.getString(com.wifiaudio.R.string.content_SoundMachine)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_sound_machine
                Key = MenuBarConstants.STREAM__SOUNDMACHINE
            }
            MusicSourceConstants.SOURCE_CALM_RADIO -> MainItem().apply {
                Name = WAApplication.me.getString(com.wifiaudio.R.string.content_Calm_Radio)
                icon_ID = com.wifiaudio.R.drawable.icon_browse_muzo2_calm_radio
                Key = MenuBarConstants.STREAM__CALM_RADIO
            }
            else -> null
        }?.apply {
            bVisible = true // Whether support this audio source.
            bEnable = true // Deprecated.
        }


    fun MainItem.toMenuSlideItem(): MenuSlideItem? = when (Key) {
        MenuBarConstants.STREAM_IHEART -> MenuSlideItem(
            com.wifiaudio.R.drawable.select_icon_menu_iheartradio,
            WAApplication.me.getString(R.string.harmanbar_iheartradio_iHeartRadio),
            MenuSlideItem.ISlideMenuItemType.IHeartRadio
        )
        MenuBarConstants.STREAM_TIDAL -> MenuSlideItem(
            com.wifiaudio.R.drawable.select_icon_menu_tidal,
            WAApplication.me.getString(com.wifiaudio.R.string.Tidal),
            MenuSlideItem.ISlideMenuItemType.TiDal
        )
        MenuBarConstants.STREAM_VTUNER -> MenuSlideItem(
            com.wifiaudio.R.drawable.select_icon_menu_vtuner,
            WAApplication.me.getString(com.wifiaudio.R.string.harmanbar_content_vTuner),
            MenuSlideItem.ISlideMenuItemType.vTuner
        )
        MenuBarConstants.STREAM_NAPSTER -> MenuSlideItem(
            com.wifiaudio.R.drawable.select_icon_menu_rhapsody,
            WAApplication.me.getString(R.string.harmanbar_content_Napster),
            MenuSlideItem.ISlideMenuItemType.Rhapsody
        )
        MenuBarConstants.STREAM_QOBUZ -> MenuSlideItem(
            com.wifiaudio.R.drawable.selector_icon_menu_qobuz,
            WAApplication.me.getString(R.string.harmanbar_content_Qobuz),
            MenuSlideItem.ISlideMenuItemType.Qobuz
        )
        MenuBarConstants.STREAM_PRIME -> MenuSlideItem(
            R.drawable.prime_music_icon,
            WAApplication.me.getString(R.string.harmanbar_primemusic_Amazon_Music),
            MenuSlideItem.ISlideMenuItemType.Prime
        )
        MenuBarConstants.STREAM_TUNEIN_NEW -> MenuSlideItem(
            com.wifiaudio.R.drawable.select_icon_menu_tune,
            WAApplication.me.getString(R.string.harmanbar_tunein_TuneIn),
            MenuSlideItem.ISlideMenuItemType.TuneInNew
        )
        MenuBarConstants.STREAM__SOUNDMACHINE -> MenuSlideItem(
            com.wifiaudio.R.drawable.sourcemanage_sourcehome_049,
            WAApplication.me.getString(com.wifiaudio.R.string.content_SoundMachine),
            MenuSlideItem.ISlideMenuItemType.SoundMachine
        )
        MenuBarConstants.STREAM__CALM_RADIO -> MenuSlideItem(
            com.wifiaudio.R.drawable.sourcemanage_sourcehome_050,
            WAApplication.me.getString(com.wifiaudio.R.string.content_Calm_Radio),
            MenuSlideItem.ISlideMenuItemType.CalmRadio
        )
        else -> null
    }

    fun supportSourceByDeviceCap(device: DeviceItem?, sourceType: Int): Boolean {
        device ?: return false

        when (sourceType) {
            MenuBarConstants.STREAM_ALDI,
            MenuBarConstants.STREAM_LINKPLAY_RADIO -> {
                return true // 该音源固件没有定义所以强制打开
            }
            MenuBarConstants.STREAM_LINKPLAY_SPOTIFY -> {
                return device.servicesCapability?.isSupportLinkplaySpotify ?: false
            }
            MenuBarConstants.STREAM_ROON_READY -> {
                return device.servicesCapability?.isSupportRoonReady ?: false
            }
            MenuBarConstants.STREAM__SOUNDMACHINE -> {
                return device.servicesCapability?.isSupportSoundMachine ?: false
            }
            MenuBarConstants.STREAM__CALM_RADIO -> {
                return device.servicesCapability?.isSupportCalmRadio ?: false
            }
            MenuBarConstants.STREAM_SPOTIFY -> {
                return !"CN".equals(device.devStatus?.region, ignoreCase = true)
            }
        }

        val streams = device.devStatus.streams
        val plmSupport = device.devStatus.plm_support
        val capability = device.devStatus.capability

        //先判断streamAll字段
        val menuBar = MenuBar(
            capability,
            plmSupport,
            if (device.devStatus.streamAll != -1) { // 如果有streamAll字段
                device.devStatus.streamAll
            } else {
                streams
            }
        )

        return MenuBarConstantsImpl.supportStream(menuBar, sourceType)
    }

    fun EnumMusicServiceSource.toItemAlarmSource(device: DeviceItem?): ItemAlarmSource? =
        when (this) {
            EnumMusicServiceSource.IHEART -> ItemAlarmSource(
                WAApplication.me.getString(R.string.harmanbar_iheartradio_iHeartRadio),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.EXTIHeartRadio
            ).apply { sourceIconName = "icon_browse_muzo2_iheartradio" }
            EnumMusicServiceSource.NAPSTER -> ItemAlarmSource(
                WAApplication.me.getString(R.string.harmanbar_content_Napster),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.EXTRHAPSODY
            ).apply { sourceIconName = "icon_browse_muzo2_napster" }
            EnumMusicServiceSource.TIDAL -> ItemAlarmSource(
                WAApplication.me.getString(com.wifiaudio.R.string.Tidal),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.EXTTIDAL
            ).apply { sourceIconName = "icon_browse_muzo2_tidal" }
            EnumMusicServiceSource.TUNEIN_NEW -> ItemAlarmSource(
                WAApplication.me.getString(com.wifiaudio.R.string.harmanbar_tunein_TuneIn),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.NewTuneIn
            ).apply { sourceIconName = "icon_browse_muzo2_tunein" }
            EnumMusicServiceSource.AMAZON_PRIME -> ItemAlarmSource(
                WAApplication.me.getString(com.wifiaudio.R.string.harmanbar_primemusic_Amazon_Music),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.PrimeMusic
            ).apply { sourceIconName = "icon_browse_muzo2_amazon_music" }
            EnumMusicServiceSource.VTUNER -> ItemAlarmSource(
                WAApplication.me.getString(com.wifiaudio.R.string.harmanbar_content_vTuner),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.EXTVTUNER
            ).apply { sourceIconName = "icon_browse_muzo2_vtuner" }
            EnumMusicServiceSource.QOBUZ -> ItemAlarmSource(
                WAApplication.me.getString(com.wifiaudio.R.string.harmanbar_content_Qobuz),
                ItemAlarmSource.SOURCE_MUSIC,
                IPlayQueueType.Qobuz
            ).apply { sourceIconName = "icon_browse_muzo2_qobuz" }
            EnumMusicServiceSource.SOUNDMACHINE -> ItemAlarmSource(
                WAApplication.me.getString(com.wifiaudio.R.string.content_SoundMachine),
                ItemAlarmSource.SOURCE_MUSIC,
                LPPlaySourceType.LP_SOUND_MACHINE
            ).apply { sourceIconName = "icon_browse_muzo2_sound_machine" }
            EnumMusicServiceSource.CALM_RADIO -> {
                if (LPMSCalmRadioAction.isSupportNewQuality(device)) {
                    ItemAlarmSource(
                        WAApplication.me.getString(com.wifiaudio.R.string.content_Calm_Radio),
                        ItemAlarmSource.SOURCE_MUSIC,
                        LPPlaySourceType.LP_CALM_RADIO
                    ).apply { sourceIconName = "icon_browse_muzo2_calm_radio" }
                } else {
                    null
                }
            }
            EnumMusicServiceSource.QPLAY,
            EnumMusicServiceSource.ROON_READY,
            EnumMusicServiceSource.SPOTIFY,
            EnumMusicServiceSource.GOOGLE_CAST,
            EnumMusicServiceSource.AIR_PLAY -> null
        }

    fun Int.toIconResource(): Int? = when (this) {
        EnumMusicServiceSource.TIDAL.keyValue -> R.drawable.icon_music_service_tidal
        EnumMusicServiceSource.AMAZON_PRIME.keyValue -> R.drawable.icon_music_service_amazon
        EnumMusicServiceSource.TUNEIN_NEW.keyValue -> R.drawable.icon_music_service_tunein
        EnumMusicServiceSource.CALM_RADIO.keyValue -> R.drawable.icon_music_service_calm_radio
        EnumMusicServiceSource.NAPSTER.keyValue -> R.drawable.icon_music_service_napster
        EnumMusicServiceSource.QOBUZ.keyValue -> R.drawable.icon_music_service_qobuz
        EnumMusicServiceSource.IHEART.keyValue -> R.drawable.icon_music_service_iheart_radio
        EnumMusicServiceSource.VTUNER.keyValue -> R.drawable.icon_music_service_vtuner
        EnumMusicServiceSource.SOUNDMACHINE.keyValue -> R.drawable.icon_music_service_sound_machine
        else -> null
    }

    fun Int.toSubIconResource(): Int? = when (this) {
        EnumMusicServiceSource.TIDAL.keyValue -> R.drawable.svg_dolby_atmos_image
        EnumMusicServiceSource.AMAZON_PRIME.keyValue -> R.drawable.svg_dolby_atmos_image
        EnumMusicServiceSource.TUNEIN_NEW.keyValue -> null
        EnumMusicServiceSource.CALM_RADIO.keyValue -> null
        EnumMusicServiceSource.NAPSTER.keyValue -> null
        EnumMusicServiceSource.QOBUZ.keyValue -> null
        EnumMusicServiceSource.IHEART.keyValue -> null
        EnumMusicServiceSource.VTUNER.keyValue -> null
        EnumMusicServiceSource.SOUNDMACHINE.keyValue -> null
        else -> null
    }

    fun Int.toDesc(): String? = when (this) {
        EnumMusicServiceSource.TIDAL.keyValue -> WAApplication.me.getString(R.string.tidal_music_service_desc)
        EnumMusicServiceSource.AMAZON_PRIME.keyValue -> WAApplication.me.getString(R.string.amazon_music_service_desc)
        EnumMusicServiceSource.TUNEIN_NEW.keyValue -> WAApplication.me.getString(R.string.Listen_to_free_internet_radio__news__sports_music__audiobooks_and_podcasts)
        EnumMusicServiceSource.CALM_RADIO.keyValue -> WAApplication.me.getString(R.string.calm_radio_music_service_desc)
        EnumMusicServiceSource.NAPSTER.keyValue -> WAApplication.me.getString(R.string.napster_music_service_desc)
        EnumMusicServiceSource.QOBUZ.keyValue -> WAApplication.me.getString(R.string.qobuz_music_service_desc)
        EnumMusicServiceSource.IHEART.keyValue -> WAApplication.me.getString(R.string.iheart_radio_music_service_desc)
        EnumMusicServiceSource.VTUNER.keyValue -> WAApplication.me.getString(R.string.vtuner_desc)
        EnumMusicServiceSource.SOUNDMACHINE.keyValue -> WAApplication.me.getString(R.string.sound_machine_music_service_desc)
        else -> null
    }

    fun Long?.millsToSecs(): Long {
        val mills = this ?: return 0
        return mills / 1000
    }

    fun Long?.formatTimeAsMills(): String {
        this ?: return "0:00"

        val secs = this / 1000
        if (secs <= 0) {
            return "0:00"
        }

        val sec = secs % 60
        val min = secs / 60 % 60
        val hr = secs / 60 / 60

        return if (hr <= 0) {
            "%02d:%02d".format(min, sec)
        } else {
            "%02d:%02d:%02d".format(hr, min, sec)
        }
    }

    fun Int?.formatTimeAsSecs(): String {
        this ?: return "0:00"

        return (this * 1000L).formatTimeAsMills()
    }

    fun mapMomentSubTitle(soundscape: EnumMoment?, musicId: String?, defaultMomentTitle: String?): String? =
        when (soundscape) {
            EnumMoment.MOMENT_FOREST,
            EnumMoment.MOMENT_RAIN,
            EnumMoment.MOMENT_OCEAN,
            EnumMoment.MOMENT_CITY_WALK -> defaultMomentTitle

            EnumMoment.MOMENT_PLAYLIST -> musicId
            else -> null
        }

    fun mapPlayerTitle(songName: String?, inputSource: EnumInputSource?): String? = when (inputSource) {
        EnumInputSource.WIFI,
        EnumInputSource.BT,
        EnumInputSource.MOOD,
        EnumInputSource.USB -> if (!songName.isNullOrBlank()) songName else WAApplication.me.getString(R.string.audio)
        EnumInputSource.AUX,
        EnumInputSource.TV,
        EnumInputSource.HDMI -> WAApplication.me.getString(R.string.audio)
        else -> WAApplication.me.getString(R.string.harmanbar_jbl_No_Music)
    }

    fun mapPlayerSubTitle(artistName: String?, inputSource: EnumInputSource?): String? = when (inputSource) {
        EnumInputSource.WIFI -> if (!artistName.isNullOrBlank()) artistName else WAApplication.me.getString(R.string.wifi)
        EnumInputSource.BT -> if (!artistName.isNullOrBlank()) artistName else WAApplication.me.getString(R.string.bluetooth)
        EnumInputSource.USB -> if (!artistName.isNullOrBlank()) artistName else WAApplication.me.getString(R.string.usb)
        EnumInputSource.MOOD -> if (!artistName.isNullOrBlank()) artistName else WAApplication.me.getString(R.string.audio_source_mood)
        EnumInputSource.AUX -> WAApplication.me.getString(R.string.aux)
        EnumInputSource.TV -> WAApplication.me.getString(R.string.tv)
        EnumInputSource.HDMI -> WAApplication.me.getString(R.string.hdmi)
        else -> null
    }

    /**
     * Only music from BT and USB source support progress.
     */
    fun mapPlayerProgress(source: EnumInputSource?, percentage: Int?): Int =
        when (source) {
            EnumInputSource.WIFI,
            EnumInputSource.BT,
            EnumInputSource.USB -> percentage?.roundPercentage() ?: 0
            else -> 0
        }

    /**
     * [version] -> [MomentViewModel.momentVersion]
     * [momentPlayState] -> [MomentViewModel.soundScapeV2State]
     * [soundScapeId] -> [OneDevice.activeSoundscapeId]
     * [isMusicPlaying] -> [OneDevice.isPlaying]
     */
    fun mapMiniPlayerType(
        version: EnumMomentVersion?,
        momentPlayState: String?,
        soundScapeId: Int?,
        isMusicPlaying: Boolean
    ): com.harman.music.mini.EnumMiniPlayerType? {
        if (isMomentMixWithMusicActive(
                version = version,
                momentPlayState = momentPlayState,
                soundScapeId = soundScapeId,
                isMusicPlaying = isMusicPlaying
        )) { // Mix with music state will act as PLAYER
            return com.harman.music.mini.EnumMiniPlayerType.PLAYER
        }

        if (EnumMomentVersion.V2 == version) {
            // can confirm Moment or Player UI only after get moment play state in V2.
            // avoid UI flash change in Full player page.
            return when (momentPlayState) {
                null -> null
                EnumSoundscapeV2State.PLAYING.value -> com.harman.music.mini.EnumMiniPlayerType.MOMENT
                else -> com.harman.music.mini.EnumMiniPlayerType.PLAYER
            }
        }

        return com.harman.music.mini.EnumMiniPlayerType.PLAYER
    }

    fun OneDevice.isMomentMixWithMusicActive(): Boolean {
        val device = this

        return isMomentMixWithMusicActive(
            version = mapMomentVersion(device = device),
            momentPlayState = device.controlSoundscapeV2Ext?.state,
            soundScapeId = device.activeSoundscapeId,
            isMusicPlaying = device.isPlaying
        )
    }

    /**
     * [version] -> [MomentViewModel.momentVersion]
     * [momentPlayState] -> [MomentViewModel.soundScapeV2State]
     * [soundScapeId] -> [OneDevice.activeSoundscapeId]
     * [isMusicPlaying] -> [OneDevice.isPlaying]
     */
    fun isMomentMixWithMusicActive(
        version: EnumMomentVersion?,
        momentPlayState: String?,
        soundScapeId: Int?,
        isMusicPlaying: Boolean
    ): Boolean {
        val moment = EnumMoment.fromValue(soundScapeId)

        return EnumMomentVersion.V2 == version &&
                EnumSoundscapeV2State.PLAYING.value == momentPlayState &&
                null != moment &&
                isMusicPlaying
    }


    /**
     * Align with logic in [MomentViewModel.getMomentVersion]
     */
    fun mapMomentVersion(device: OneDevice): EnumMomentVersion? {
        val soundscape = device.featSupportExt?.featSupport?.soundscape ?: return null
        if (!soundscape.isSupport) {
            return null
        }

        val smartBtnConfig = device.smartBtnConfigExt?.config
        return if (true == smartBtnConfig?.supportSoundScapeV2) {
            EnumMomentVersion.V2
        } else {
            EnumMomentVersion.V1
        }
    }

    private val specialSources = listOf(
        IStorageMediumType.LINE_IN,
        IStorageMediumType.LINE_IN_2,
        IStorageMediumType.RCA,
        IStorageMediumType.BLUETOOTH,
        IStorageMediumType.EXTERNAL_USB,
        IStorageMediumType.USB_DAC,
        IStorageMediumType.UDISK,
        IStorageMediumType.EXTERNAL_TFCard,
        IStorageMediumType.SONGLIST_LOCAL_TF,
        IStorageMediumType.OPTICAL,
        IStorageMediumType.OPTICAL2,
        IStorageMediumType.CO_AXIAL,
        IStorageMediumType.CO_AXIAL2,
        IStorageMediumType.HDMI,
        IStorageMediumType.TV,
    )

    /**
     * @param medium [IStorageMediumType] [MenuBarConstants]
     * @param track [IPlayQueueType]
     * @param songName
     * @param isPlaying
     */
    fun mapInputSource(
        medium: String?,
        track: String?,
        songName: String?,
        isPlaying: Boolean
    ): EnumInputSource? {
        val musicService = mapMusicServiceSource(medium = medium, track = track)

        val rst = when {
            null != musicService -> {
                if (songName.isNullOrBlank()) {
                    null // regard as no music source if song name is empty
                } else {
                    EnumInputSource.WIFI // display music service as first priority
                }
            }

            IStorageMediumType.BLUETOOTH.equals(medium, true) -> EnumInputSource.BT

            (true == track?.contains(IPlayQueueType.ExtUSBDiskQueue) ||
                    IStorageMediumType.SONGLIST_LOCAL_TF.equals(medium, true) ||
                    IStorageMediumType.UDISK.equals(medium, true) ||
                    IStorageMediumType.USB_DAC.equals(medium, true) ||
                    IStorageMediumType.EXTERNAL_TFCard.equals(medium, true) ||
                    IStorageMediumType.EXTERNAL_USB.equals(medium, true)) -> EnumInputSource.USB

            com.harman.discover.util.Tools.isHDMISource(medium = medium) -> EnumInputSource.HDMI

            com.harman.discover.util.Tools.isTVSource(medium = medium) -> EnumInputSource.TV

            IStorageMediumType.LINE_IN.equals(medium, true) ||
                    IStorageMediumType.LINE_IN_2.equals(medium, true) ||
                    IStorageMediumType.RCA.equals(medium, true) ||
                    IStorageMediumType.CO_AXIAL.equals(medium, true) ||
                    IStorageMediumType.CO_AXIAL2.equals(medium, true) ||
                    MenuBarConstants.MEDIA_TYPE_XLR.equals(medium, true) -> EnumInputSource.AUX

            medium in specialSources || !songName.isNullOrBlank() || isPlaying -> EnumInputSource.WIFI

            else -> null
        }

        return rst
    }

    fun mapPlayTimeVisible(device: Device?, inputSource: EnumInputSource?, musicServiceSource: EnumMusicServiceSource? = null): Boolean =
        when (device) {
            is OneDevice -> when (inputSource) {
                EnumInputSource.WIFI,
                EnumInputSource.BT,
                EnumInputSource.USB -> musicServiceSource != EnumMusicServiceSource.CALM_RADIO
                else -> false
            }
            else -> false
        }

    /**
     * @param medium [IStorageMediumType] [MenuBarConstants]
     * @param track [IPlayQueueType]
     */
    fun mapMusicServiceSource(medium: String?, track: String?): EnumMusicServiceSource? {
        val rst = when {
            !medium.isNullOrBlank() && IStorageMediumType.AIRPLAY.equals(medium, true) -> EnumMusicServiceSource.AIR_PLAY

            IPlayQueueType.CAST.equals(medium, true)  -> EnumMusicServiceSource.GOOGLE_CAST

            (!medium.isNullOrBlank() && medium.trim().equals("SPOTIFY", ignoreCase = true)) ||
                    "Spotify".equals(track, true) -> EnumMusicServiceSource.SPOTIFY

            true == track?.contains(IPlayQueueType.Qobuz, true) -> EnumMusicServiceSource.QOBUZ

            isTidalSource(track = track, medium = medium) -> EnumMusicServiceSource.TIDAL

            IPlayQueueTypeImpl.isExtIHeartRadio(track) -> EnumMusicServiceSource.IHEART

            true == track?.contains(IPlayQueueType.PrimeMusic, true) ||
                    true == track?.equals(IPlayQueueType.AmazonMusic, true) ||
                    true == medium?.equals(IPlayQueueType.EXTALEXA, true) -> EnumMusicServiceSource.AMAZON_PRIME

            IPlayQueueTypeImpl.isExtNewTuneRadio(track) ||
                    IPlayQueueTypeImpl.isExtTuneRadio(track) -> EnumMusicServiceSource.TUNEIN_NEW

            true == track?.contains(LPPlaySourceType.LP_SOUND_MACHINE, true) -> EnumMusicServiceSource.SOUNDMACHINE

            true == track?.contains(LPPlaySourceType.LP_CALM_RADIO, true) -> EnumMusicServiceSource.CALM_RADIO

            IPlayQueueTypeImpl.isvTuner(track) -> EnumMusicServiceSource.VTUNER

            true == track?.contains(IPlayQueueType.EXTRHAPSODY, true) -> EnumMusicServiceSource.NAPSTER

            IPlayQueueTypeImpl.isExtQPlay(track) -> EnumMusicServiceSource.QPLAY

            true == track?.contains(IPlayQueueType.ExtRoonReady, true) -> EnumMusicServiceSource.ROON_READY

            else -> null
        }

        Logger.d(TAG, "mapMusicServiceSource() >>> medium[$medium] track[$track] rst[$rst]")
        return rst
    }

    private fun String?.isValidUrl(): Boolean = !this.isNullOrBlank() &&
            (this.startsWith("http") || this.startsWith("https"))

    suspend fun syncLoadAlbumCover(
        context: Context?,
        albumUrl: String?,
        source: EnumMusicServiceSource?,
        deviceItem: DeviceItem?,
        width: Int,
        height: Int,
        logTag: String
    ): Bitmap? = suspendCancellableCoroutine { continuation ->
        loadAlbumCover(
            context = context,
            albumUrl = albumUrl,
            source = source,
            deviceItem = deviceItem,
            width = width,
            height = height,
            logTag = logTag,
            listener = object : BitmapLoadingListener {
                override fun onSuccess(bitmap: Bitmap?) {
                    continuation.safeResume(bitmap)
                }

                override fun onError() {
                    continuation.safeResume(null)
                }
            }
        )
    }

    fun loadAlbumCover(
        context: Context?,
        listener: BitmapLoadingListener,
        albumUrl: String?,
        source: EnumMusicServiceSource?,
        deviceItem: DeviceItem?,
        width: Int,
        height: Int,
        logTag: String = TAG
    ) {
        if (!albumUrl.isValidUrl()) {
            Logger.d(logTag, "loadAlbumCover() >>> invalid url: $albumUrl")
            listener.onError()
            return
        }

        when (source) {
            EnumMusicServiceSource.AIR_PLAY,
            EnumMusicServiceSource.ROON_READY -> {
                Logger.d(logTag, "loadAlbumCover() >>> start to load airplay/roon. url:$albumUrl")
                val requestUtils: HttpRequestUtils = HttpRequestUtils.getRequestUtils(deviceItem)
                requestUtils.get(albumUrl, object : HttpRequestUtils.ResultCallback<Any?>() {
                    override fun onSuccess(response: Any?) {
                        if (response !is OkHttpResponseItem) {
                            listener.onError()
                            return
                        }

                        val bytes = response.bytes
                        if (bytes.isNullOrEmpty()) {
                            listener.onError()
                            return
                        }

                        Logger.d(logTag, "loadAlbumCover.onSuccess() >>> load url:$albumUrl success.")
                        listener.onSuccess(BitmapFactory.decodeByteArray(bytes, 0, bytes.size))
                    }

                    override fun onFailure(e: Exception) {
                        Logger.e(logTag, "loadAlbumCover.onFailure() >>> load url:$albumUrl fail:$e")
                        listener.onError()
                    }
                })
            }
            else -> {
                Logger.d(logTag, "loadAlbumCover() >>> start to load common. url:$albumUrl")
                GlideMgtUtil.loadBitmap(context, albumUrl, buildGlideImgLoadConfig(width = width, height = height),
                    object : BitmapLoadingListener {
                        override fun onSuccess(bitmap: Bitmap?) {
                            Logger.d(logTag, "onSuccess() >>> load bmp[${bitmap?.byteCount}] complete. url:$albumUrl")
                            listener.onSuccess(bitmap)
                        }

                        override fun onError() {
                            Logger.e(logTag, "onError() >>> fail to load img. url:$albumUrl")
                            listener.onError()
                        }
                    }
                )
            }
        }

    }

    fun String?.isTransitioning(): Boolean
        = DlnaPlayerStatus.IPlayStatus.Transitioning.equals(this, true)

    fun EnumInputSource?.showAlbumCover(): Boolean = when (this) {
        EnumInputSource.BT,
        EnumInputSource.USB,
        EnumInputSource.HDMI,
        EnumInputSource.AUX,
        EnumInputSource.TV -> false

        else -> true
    }

    /**
     * @link
     * [NewPlayControlFragment.updateAlbumInfo]
     * [NewPlayControlFragment.updateBtnEnabledStatus]
     * [NewPlayControlFragment.updateSongListNetworkSourceStatus]
     * [NewPlayControlFragment.updateRadioNetworkSourceStatus]
     * [NewPlayControlFragment.updateStationNetworkSourceStatus]
     *
     * @return null if don't change current visible state.
     */
    fun mapQueueVisible(
        logTag: String, musicService: EnumMusicServiceSource?,
        medium: String?, track: String?
    ): Boolean? {
        Logger.d(logTag, "mapQueueVisible() >>> musicService[$musicService] medium[$medium] track[$track]")
        // [NewPlayControlFragment.updateAlbumInfo]
        if (null == musicService || (medium.isNullOrBlank() && track.isNullOrBlank())) {
            return false
        }

        if (IStorageMediumType.UNKNOWN.equals(medium, true) ||
            IStorageMediumType.NONE.equals(medium, true)) {
            return false
        }

        // [NewPlayControlFragment.updateBtnEnabledStatus]
        return when {
            IStorageMediumType.TV.equals(medium, true) -> false
            IStorageMediumType.AIRPLAY == medium -> false
            IStorageMediumType.SQUEEZELITE == medium -> false
            IStorageMediumType.ROON == medium -> false
            IStorageMediumType.SONGLIST_LOCAL == medium -> true
            IStorageMediumType.SONGLIST_LOCAL_TF == medium -> true
            IStorageMediumType.THIRD_DLNA == medium -> true
            IStorageMediumType.LINE_IN == medium -> false
            IStorageMediumType.BLUETOOTH == medium -> false
            IStorageMediumType.OPTICAL == medium -> false
            IStorageMediumType.OPTICAL2 == medium -> false
            IStorageMediumType.CO_AXIAL == medium -> false
            IStorageMediumType.CO_AXIAL2 == medium -> false
            IStorageMediumType.PHONO == medium -> false
            !medium.isNullOrBlank() && medium.trim().equals("SPOTIFY", true) -> true
            IPlayQueueTypeImpl.isExtQPlay(medium) -> true
            IPlayQueueTypeImpl.isExtALIRPC(medium) -> true
            IStorageMediumType.ALEXA == medium -> false
            IStorageMediumType.ALEXA_PANDORA == medium -> true
            IPlayQueueTypeImpl.isExtTuneRadio(track) -> true
            IPlayQueueTypeImpl.isExtRemoteLocalQueue(track) -> true
            IPlayQueueTypeImpl.isExtTianTianMusic(track) -> true
            isTidalSource(track = track, medium = medium) -> false
            // [NewPlayControlFragment.updateBtnEnabledStatus]
            isSongListNetwork(medium = medium) -> mapQueueBySongListNetwork(medium = medium, track = track)
            // [NewPlayControlFragment.updateRadioNetworkSourceStatus]
            isRadioNetwork(medium = medium) -> mapQueueByRadioNetwork(track = track)
            // [NewPlayControlFragment.updateStationNetworkSourceStatus]
            isStation(medium = medium) -> mapQueueByStation(track = track)
            IStorageMediumType.CAST == medium -> false
            IPlayQueueType.CAST == track -> false
            IStorageMediumType.EXTERNAL_USB == medium -> false
            else -> true
        }
    }

    private fun isTidalSource(track: String?, medium: String?): Boolean {
        return true == track?.contains(IPlayQueueType.EXTTIDAL, true) ||
                "TIDAL_CONNECT".equals(medium, true)
    }

    /**
     * @link
     * [NewPlayControlFragment.updateSongListNetworkSourceStatus]
     */
    private fun isSongListNetwork(medium: String?): Boolean {
        return IStorageMediumType.SONGLIST_NETWORK == medium || "TIDAL_CONNECT".equals(medium, true)
    }

    private fun mapQueueBySongListNetwork(medium: String?, track: String?): Boolean = when {
        track.safeContains(LPPlaySourceType.LP_CALM_RADIO) -> false
        track.safeContains(IPlayQueueType.ExtXimalaya) -> true
        track.safeContains(IPlayQueueType.EXTQingtingFM) -> true
        (track.safeContains(IPlayQueueType.EXTTIDAL)) ||
                "TIDAL_CONNECT".equals(medium, ignoreCase = true) ->
                    !"TIDAL_CONNECT".equals(medium, ignoreCase = true)
        track.safeContains(IPlayQueueType.ExtRemoteLocalSuffix) -> true
        track.safeContains(LPPlaySourceType.LP_SOUND_MACHINE) -> false
        track.safeContains(IPlayQueueType.EXTRHAPSODY) -> true
        track.safeContains(IPlayQueueType.ALDI_LIFE_MUSIK) -> true
        track.safeContains(IPlayQueueType.Qobuz) -> true
        track.safeContains(IPlayQueueType.EXTDEEZER) -> true
        track.safeContains(IPlayQueueType.PrimeMusic) -> true
        track.safeContains(IPlayQueueType.ExtUpnpServer) -> true
        else -> true
    }

    /**
     * @link
     * [NewPlayControlFragment.updateRadioNetworkSourceStatus]
     */
    private fun isRadioNetwork(medium: String?): Boolean {
        return IStorageMediumType.RADIO_NETWORK == medium
    }

    private fun mapQueueByRadioNetwork(track: String?): Boolean? = when {
        track.safeContains("CalmRadio") -> false
        track.safeContains(IPlayQueueType.NewTuneIn) -> false
        track.safeContains(IPlayQueueType.ExtDoubanRadio) -> true
        track.safeContains(IPlayQueueType.ExtPandoraRadio) -> false
        track.safeContains(IPlayQueueType.ExtTuneRadio) -> true
        track.safeContains(IPlayQueueType.EXTVTUNER) -> false
        track.safeContains(IPlayQueueType.EXTIHeartRadio) -> false
        track.safeContains(IPlayQueueType.RADIODE) -> true
        track.safeContains(IPlayQueueType.EXTQingtingFM) -> true
        IPlayQueueTypeImpl.isExtUpnpServer(track) -> true
        track.safeContains(IPlayQueueType.ExtXimalaya) -> true
        track.safeContains(IPlayQueueType.CustomRadio) -> false
        track.safeContains(LPPlaySourceType.LP_CALM_RADIO) -> false
        else -> null
    }

    /**
     * @link
     * [NewPlayControlFragment.updateStationNetworkSourceStatus]
     */
    private fun isStation(medium: String?): Boolean {
        return IStorageMediumType.STATION_NETWORK == medium
    }

    private fun mapQueueByStation(track: String?): Boolean? = when {
        track.safeContains(IPlayQueueType.ExtDoubanRadio) -> true
        track.safeContains(IPlayQueueType.ExtPandoraRadio) -> false
        track.safeContains(IPlayQueueType.EXTIHeartRadio) -> false
        track.safeContains(IPlayQueueType.ExtTuneRadio) -> true
        track.safeContains(IPlayQueueType.EXTQingtingFM) -> true
        track.safeContains(IPlayQueueType.EXTRHAPSODY) -> true
        track.safeContains(IPlayQueueType.ALDI_LIFE_MUSIK) -> true
        track.safeContains(IPlayQueueType.Qobuz) -> true
        track.safeContains(IPlayQueueType.EXTDEEZER) -> false
        track.safeContains(LPPlaySourceType.LP_SOUND_MACHINE) -> false
        else -> null
    }

    private fun String?.safeContains(target: String) = !isNullOrBlank() && contains(target)

    fun updateDeviceItemInApplication(device: Device) {
        LinkplayApplication.me.deviceItem = device.wifiDevice?.deviceItem
        LinkplayApplication.me.CurrentDevice = device.wifiDevice?.deviceItem

        val provider = device.wifiDevice?.deviceItem?.uuid?.let {
            DlnaServiceProviderPool.me().getDlanHelper(it)
        }
        if (provider != null) { // fix bug HOP-27001
            WAApplication.me.SetCurrentServerProvider(provider)
        }

        device.wifiDevice?.deviceItem?.also { // fix bug HOP-26736
            WAApplication.me.deviceItem = device.wifiDevice?.deviceItem
        }
    }

    fun PartyBoxDevice.isRadioStyle(): Boolean {
        if (audioSource != AudioSource.FM_AUDIO &&
            audioSource != AudioSource.DAB_AUDIO) {
            return false
        }

        return when (radioInfo?.currentRadioType) {
            RadioInfo.Type.FMRadio,
            RadioInfo.Type.DABRadio -> true
            else -> false
        }
    }

    fun Device.radioType(): RadioInfo.Type? = (this as? PartyBoxDevice)?.radioInfo?.currentRadioType

    fun PartyBoxDevice.getRadioTitle(): String? {
        val device = this

        return when (device.radioType()) {
            RadioInfo.Type.FMRadio -> device.radioInfo?.currentStation?.frequency?.toString()
            RadioInfo.Type.DABRadio -> device.radioInfo?.currentStation?.stationName
            else -> null
        }
    }

    fun PartyBoxDevice.getRadioSubTitle(): String? {
        val device = this

        return when (device.radioType()) {
            RadioInfo.Type.FMRadio -> WAApplication.me.getString(R.string.fm_radio)
            RadioInfo.Type.DABRadio -> WAApplication.me.getString(R.string.dab_radio)
            else -> null
        }
    }

    fun PartyBoxDevice.isRadioPlay(): Boolean = RadioInfo.State.Playing == radioInfo?.currentStation?.state

    fun OneDevice.musicServiceSource(): EnumMusicServiceSource? =
        mapMusicServiceSource(
            medium = this.mediumSource,
            track = this.trackSource
        )

    fun EnumMusicServiceSource?.isUriConst(): Boolean = when (this) {
        EnumMusicServiceSource.AIR_PLAY,
        EnumMusicServiceSource.ROON_READY -> true
        else -> false
    }

    fun EnumMusicServiceSource?.showQueue(): Boolean = when (this) {
        EnumMusicServiceSource.AMAZON_PRIME,
        EnumMusicServiceSource.NAPSTER,
        EnumMusicServiceSource.QOBUZ,
        EnumMusicServiceSource.TIDAL -> true
        else -> false
    }

    fun EnumMusicServiceSource?.showMore(): Boolean = when (this) {
        EnumMusicServiceSource.AMAZON_PRIME,
        EnumMusicServiceSource.NAPSTER,
        EnumMusicServiceSource.QOBUZ,
        EnumMusicServiceSource.TIDAL,
        EnumMusicServiceSource.TUNEIN_NEW -> true
        else -> false
    }

    private const val TAG = "com.harman.music.Tools"
}