package com.harman.music.mini

import android.graphics.Bitmap
import android.widget.SeekBar
import androidx.core.view.isVisible
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleOwner
import com.blankj.utilcode.util.SizeUtils
import com.harman.bar.app.databinding.ItemMiniMomentBinding
import com.harman.bar.app.databinding.ItemMiniPlayerBinding
import com.harman.BaseMultiAdapter
import com.harman.discover.util.Tools.roundPercentage
import com.harman.bar.app.databinding.ItemMiniRadioBinding
import com.harman.music.player.toVolumeImgRes
import com.harman.log.Logger
import com.harman.music.Tools
import com.utils.glide.BitmapLoadingListener
import com.wifiaudio.app.WAApplication

/**
 * Created by ger<PERSON><PERSON><PERSON><PERSON> on 2024/9/18.
 */
class MiniPlayersAdapter(
    private val data: List<AbsPlayerItem>?,
    private val listener: IMiniPlayerEventListener,
    private val fragment: LifecycleOwner
) : BaseMultiAdapter<AbsPlayerItem>(data) {

    override fun viewType(position: Int): Int =
        getOrNull(position)?.viewType?.ordinal ?: EnumMiniPlayerType.PLAYER.ordinal

    override fun layoutId(viewType: Int): Int =
        (EnumMiniPlayerType.fromValue(original = viewType) ?: EnumMiniPlayerType.PLAYER)
            .getMiniPlayerLayoutId()

    override fun bind(binding: ViewDataBinding, position: Int) {
        val item = getOrNull(position) ?: run {
            Logger.w(TAG, "bind() >>> illegal position[$position]. total[${itemCount}]")
            return
        }

        if (binding is ItemMiniPlayerBinding && item is MusicPlayerItem) {
            decoItemMiniPlayerBinding(binding = binding, item = item, listener = listener, fragment = fragment)
        } else if (binding is ItemMiniMomentBinding && item is MomentPlayerItem) {
            decoItemMiniMomentBinding(binding = binding, item = item, listener = listener, fragment = fragment)
        } else if (binding is ItemMiniRadioBinding && item is RadioPlayerItem) {
            decoItemMiniRadioBinding(binding = binding, item = item, listener = listener, fragment = fragment)
        }
    }

    override fun isSameItem(old: AbsPlayerItem, new: AbsPlayerItem): Boolean =
        if (old is MusicPlayerItem && new is MusicPlayerItem) {
            old.device == new.device
        } else if (old is MomentPlayerItem && new is MomentPlayerItem) {
            old.device == new.device
        } else if (old is RadioPlayerItem && new is RadioPlayerItem) {
            old.device == new.device
        } else {
            false
        }

    override fun isSameContent(old: AbsPlayerItem, new: AbsPlayerItem): Boolean =
        if (old is MusicPlayerItem && new is MusicPlayerItem) {
            old == new
        } else if (old is MomentPlayerItem && new is MomentPlayerItem) {
            old == new
        } else if (old is RadioPlayerItem && new is RadioPlayerItem) {
            old == new
        } else {
            false
        }

    companion object {
        private const val TAG = "MiniPlayersAdapter"

        fun decoItemMiniPlayerBinding(
            binding: ItemMiniPlayerBinding,
            item: MusicPlayerItem?,
            listener: IMiniPlayerEventListener,
            fragment: LifecycleOwner
        ) {
            item ?: return

            Logger.d(TAG, "decoItemMiniPlayerBinding() >>> $item")
            binding.listener = listener
            binding.item = item
            binding.lifecycleOwner = fragment
            binding.sbVolume.progress = item.volumeWithMute
            binding.icVolume.setImageResource(item.volumeWithMute.toVolumeImgRes())
            binding.sbVolume.setOnSeekBarChangeListener(null)
            binding.sbVolume.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {

                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    binding.icVolume.setImageResource(progress.toVolumeImgRes())

                    if (!fromUser) return
                    listener.onVolumeBarSeek(item = item, progress = progress.roundPercentage())
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

                override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit
            })

            if (item.isAlbumCoverVisible) {
                Tools.loadAlbumCover(context = WAApplication.me, albumUrl = item.albumCoverUrl,
                    source = item.musicServiceSource, deviceItem = item.deviceItem,
                    width = 75, height = 75,
                    listener = object : BitmapLoadingListener {
                        override fun onSuccess(bitmap: Bitmap?) {
                            bitmap ?: return

                            binding.ivAlbumCover.isVisible = true
                            binding.ivAlbumCover.setImageBitmap(bitmap)
                        }

                        override fun onError() {
                            binding.ivAlbumCover.isVisible = false
                        }
                    }
                )
            } else {
                binding.ivAlbumCover.isVisible = false
            }
        }

        fun decoItemMiniMomentBinding(
            binding: ItemMiniMomentBinding,
            item: MomentPlayerItem?,
            listener: IMiniPlayerEventListener,
            fragment: LifecycleOwner
        ) {
            item ?: return

            Logger.d(TAG, "decoItemMiniMomentBinding() >>> $item")
            binding.listener = listener
            binding.item = item
            binding.lifecycleOwner = fragment
            binding.sbVolume.progress = item.volumeWithMute
            binding.icVolume.setImageResource(item.volumeWithMute.toVolumeImgRes())
            binding.sbVolume.setOnSeekBarChangeListener(null)
            binding.sbVolume.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    binding.icVolume.setImageResource(progress.toVolumeImgRes())

                    if (!fromUser) return
                    listener.onVolumeBarSeek(item = item, progress = progress.roundPercentage())
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

                override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit
            })
        }

        fun decoItemMiniRadioBinding(
            binding: ItemMiniRadioBinding,
            item: RadioPlayerItem?,
            listener: IMiniPlayerEventListener,
            fragment: LifecycleOwner
        ) {
            item ?: return

            Logger.d(TAG, "decoItemMiniRadioBinding() >>> $item")
            binding.listener = listener
            binding.item = item
            binding.lifecycleOwner = fragment
            binding.sbVolume.progress = item.volumeWithMute
            binding.icVolume.setImageResource(item.volumeWithMute.toVolumeImgRes())
            binding.sbVolume.setOnSeekBarChangeListener(null)
            binding.sbVolume.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    binding.icVolume.setImageResource(progress.toVolumeImgRes())

                    if (!fromUser) return
                    listener.onVolumeBarSeek(item = item, progress = progress.roundPercentage())
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

                override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit
            })
        }
    }
}