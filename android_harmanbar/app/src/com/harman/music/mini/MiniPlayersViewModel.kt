package com.harman.music.mini

import android.bluetooth.BluetoothDevice
import androidx.annotation.IntRange
import androidx.annotation.MainThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.EnumSoundscapeV2Action
import com.harman.command.one.bean.Member
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.connect.asyncGattConnect
import com.harman.discover.DeviceStore
import com.harman.isSub
import com.harman.moment.MomentViewModel
import com.harman.music.player.OnePlayerViewModel
import com.harman.music.player.OnePlayerViewModel.Companion.ACTION_PLAY_PAUSE
import com.harman.music.player.OnePlayerViewModel.Companion.ACTION_VOLUME
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools.addIfAbsent
import com.harman.discover.util.Tools.printList
import com.harman.discover.util.Tools.wlan0MacWithoutColon
import com.harman.getBLEProtocol
import com.harman.isNonePlayableDevice
import com.harman.log.Logger
import com.harman.music.Tools.isRadioPlay
import com.harman.music.Tools.isRadioStyle
import com.harman.music.player.isMediaPlayerSupport
import com.harman.thread.DISPATCHER_DEFAULT
import com.wifiaudio.app.WAApplication
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/9/18.
 */
class MiniPlayersViewModelFactory: ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return MiniPlayersViewModel() as T
    }
}

class MiniPlayersViewModel: ViewModel(), DefaultLifecycleObserver {

    /**
     * Support multiple types of device listeners.
     */
    private val deviceListenerWrappers = mutableListOf<DeviceListenerWrapper>()
    private fun getWrapper(
        target: Device
    ): DeviceListenerWrapper? {
        return deviceListenerWrappers.firstOrNull { wrapper ->
            wrapper.uuid == target.UUID
        }
    }

    /**
     * Mini player source based on [OnePlayerViewModel] and [MomentViewModel]
     */
    private val _miniPlayerItems = MutableLiveData<List<AbsPlayerItem>?>()
    val miniPlayerItems: LiveData<List<AbsPlayerItem>?>
        get() = _miniPlayerItems.distinctUntilChanged()

    /**
     * Generate device bound [AbsPlayerItem] while device is onlined,
     * keep prev view models if device was kept in [_miniPlayerItems],
     * and remove if device was offline.
     */
    @MainThread
    fun updateDevices(devices: List<Device>?) {
        val onlines = devices
            ?.filterMiniPlayerDevices()
            ?.toMutableList()
            ?: mutableListOf()

        // merge and full sort
        val currents = onlines.mapNotNull { device ->
            device.toPlayerItem()?.apply {
                online()
            }
        }

        asyncFetchDeviceInfo(items = currents)
        Logger.d(TAG, "updateDevices() >>> ${currents.printList()}")
        sortAndUpdateItems(items = currents)
        clearOfflineRelatedListenerWrappers(onlines)
    }

    /**
     * Remove [DeviceListenerWrapper] from [deviceListenerWrappers] and [Device] itself
     * if this device in [DeviceListenerWrapper] is no longer exists in [onlines].
     */
    @MainThread
    private fun clearOfflineRelatedListenerWrappers(onlines: List<Device>) {
        val iter = deviceListenerWrappers.iterator()

        while (iter.hasNext()) {
            val wrapper = iter.next()

            if (onlines.none { online ->
                    wrapper.uuid == online.UUID
                }) {
                Logger.d(TAG, "clearOfflineRelatedListenerWrappers() >>> [${wrapper.uuid}]")
                DeviceStore.find(wrapper.uuid)?.unregisterDeviceListener(wrapper)
                iter.remove()
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)

        deviceListenerWrappers.forEach { wrapper ->
            DeviceStore.find(wrapper.uuid)?.unregisterDeviceListener(wrapper)
        }

        deviceListenerWrappers.clear()
    }

    @MainThread
    private fun AbsPlayerItem.online() {
        val device = this.device
        Logger.i(TAG, "online() >>> ${device.UUID}")
        val uuid = device.UUID ?: return

        when (device) {
            is OneDevice -> {
                getOrCreateWrapper(device = device) {
                    val wrapper = OneDeviceStateMaintainer(
                        uuid = uuid,
                        coroutineScope = viewModelScope,
                        updater = ::updatePlayerItem
                    )

                    device.registerDeviceListener(wrapper)
                    deviceListenerWrappers.addIfAbsent(wrapper)
                    wrapper
                }
            }
            is PartyBoxDevice -> {
                getOrCreateWrapper(device = device) {
                    val wrapper = PartyBoxStateMaintainer(
                        uuid = uuid,
                        coroutineScope = viewModelScope,
                        updater = ::updatePlayerItem
                    )

                    device.registerDeviceListener(wrapper)
                    deviceListenerWrappers.addIfAbsent(wrapper)
                    wrapper
                }
            }
        }
    }

    private fun getOrCreateWrapper(
        device: Device,
        creator: (Device) -> DeviceListenerWrapper
    ): DeviceListenerWrapper {
        return deviceListenerWrappers.firstOrNull { wrapper ->
            wrapper.uuid == device.UUID
        } ?: creator.invoke(device)
    }

    /**
     * Try to fetch some feature support params to support functions such as moment after device online.
     */
    @MainThread
    private fun asyncFetchDeviceInfo(items: List<AbsPlayerItem>) {
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            items.forEach { item ->
                when (val device = item.device) {
                    is OneDevice -> {
                        if (!device.isWiFiOnline && device.ableSendCmd) {
                            device.getRemoteVolume()
                        }

                        if (device.needConnectGatt()) {
                            Logger.d(TAG, "asyncFetchDeviceInfo() >>> try to gatt connect [${device.UUID}]")
                            WAApplication.me?.let { context ->
                                device.asyncGattConnect(context = context)
                            }
                        }

                        if (device.ableSendCmd) {
                            checkAndRequest(device = device)
                        }
                    }
                }
            }
        }
    }

    /**
     * Only A2DP connected but WiFi offline devices need to connect BLE.
     */
    private fun OneDevice.needConnectGatt(): Boolean =
        !isWiFiOnline && isA2DPConnected && !isGattConnected

    private fun checkAndRequest(device: OneDevice) {
        // sync with logics in [MomentViewModel.syncDeviceState]
        if (null == device.smartBtnConfigExt) { // get latest smart btn config if didn't existed in device instance
            Logger.d(TAG, "checkAndRequest() >>> try to get smart btn config [${device.UUID}]")
            device.getSmartBtnConfig()
        }

        if (device.supportSoundScapeV2 && null == device.controlSoundscapeV2Ext) { // get latest soundscape v2 control state
            Logger.d(TAG, "checkAndRequest() >>> control soundscape V2 [${device.UUID}]")
            device.getSoundscapeV2State()
        }

        if (device.supportSoundScapeV2 && null == device.soundscapeV2ListExt) { // get latest soundscape v2 custom elements
            Logger.d(TAG, "checkAndRequest() >>> soundscape V2 list [${device.UUID}]")
            device.getSoundscapeV2Config()
        }
    }
    /**
     * @param devices refresh related items with target devices
     */
    @MainThread
    private fun updatePlayerItem(uuid: String) {
        val currents = _miniPlayerItems.value?.toMutableList() ?: return

        val index = currents.indexOfFirst { current ->
            current.device.UUID == uuid
        }

        if (index !in currents.indices) {
            return
        }

        currents[index] = DeviceStore.find(uuid)?.toPlayerItem() ?: return

        Logger.d(TAG, "updatePlayerItem() >>> ${currents.printList()}")
        sortAndUpdateItems(items = currents)
    }

    /**
     * Align with [MomentViewModel.cancelSoundScapeV2]
     */
    @MainThread
    fun cancelSoundScapeV2(item: AbsPlayerItem) {
        val device = item.device as? OneDevice ?: run {
            Logger.w(TAG, "cancelSoundScapeV2() >>> missing OneDevice instance")
            return
        }

        val moment = (item as? MomentPlayerItem)?.moment ?: run {
            Logger.w(TAG, "cancelSoundScapeV2() >>> missing moment instance")
            return
        }

        cancelSoundScapeV2(device = device, soundscapeId = moment.id)
    }

    @MainThread
    fun setVolume(item: AbsPlayerItem, @IntRange(from = 0L, to = 100L) volume: Int) {
        Logger.d(TAG, "setVolume() >>> device[${item.device.UUID}] volume[$volume]")
        when (val device = item.device) {
            is OneDevice -> {
                getWrapper(target = device)?.markAction(ACTION_VOLUME)
                device.setRemoteVolume(value = volume)
            }
            is PartyBoxDevice -> {
                device.setRemoteVolume(protocol = device.getBLEProtocol(), value = volume)
            }
        }
    }

    private var lastPlayPauseClickTime = 0L

    @MainThread
    fun playOrPause(item: AbsPlayerItem) {
        val currentTime = System.currentTimeMillis()
        if (currentTime <= lastPlayPauseClickTime + 1000) {
            Logger.d(TAG, "playOrPause() >>> prevent frequently play pause click")
            return
        }
        lastPlayPauseClickTime = currentTime

        when (val device = item.device) {
            is OneDevice -> {
                getWrapper(target = device)?.markAction(ACTION_PLAY_PAUSE)

                if (device.isPlaying) {
                    Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] pause music")
                    device.pauseMusic()
                    if (device.supportSoundScapeV2) {
                        cancelSoundScapeV2(device = device, soundscapeId = device.activeSoundscapeId)
                        Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] cancel moment")
                    }
                } else {
                    Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] play")
                    device.playMusic()
                }
            }
            is PartyBoxDevice -> {
                if (device.isRadioStyle()) {
                    if (device.isRadioPlay()) {
                        Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] pause radio")
                        device.setRadioInfo(
                            protocol = BluetoothDevice.TRANSPORT_LE,
                            cmd = RadioInfo.Command.FunctionalityStop,
                            cmdBytes = null
                        )
                    } else {
                        Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] play radio")
                        device.setRadioInfo(
                            protocol = BluetoothDevice.TRANSPORT_LE,
                            cmd = RadioInfo.Command.FunctionalityPlay,
                            cmdBytes = null
                        )
                    }
                } else {
                    if (device.isPlaying) {
                        Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] pause music")
                        device.pauseMusic(protocol = device.getBLEProtocol())
                    } else {
                        Logger.d(TAG, "playOrPause() >>> device[${device.UUID}] play music")
                        device.playMusic(protocol = device.getBLEProtocol())
                    }
                }
            }
        }
    }

    @MainThread
    private fun cancelSoundScapeV2(device: OneDevice, soundscapeId: Int?) {
        Logger.i(TAG, "cancelSoundScapeV2() >>> device[${device.UUID}] moment[$soundscapeId]")
        val req = ControlSoundscapeV2(
            actionId = EnumSoundscapeV2Action.PREVIEW_STOP.value,
            soundscapeId = soundscapeId,
            autoResume = true,
            fadeOut = "true",
            keepAlive = null
        )

        device.controlSoundscapeV2(req = req)
    }

    private fun List<Device>.filterMiniPlayerDevices(): List<Device> {
        val source = this
        val members = source.filterMembers()

        return source.filter { device ->
            when (device) {
                is OneDevice -> device.ableJoinMiniPlayer(members)
                is PartyBoxDevice -> device.ableJoinMiniPlayer()
                else -> false
            }
        }
    }

    private fun List<Device>.filterMembers(): List<Member> {
        val source = this

        return source.filterIsInstance<OneDevice>().flatMap { oneDevice ->
            oneDevice.groupInfoExt?.groupInfo?.groupInfo?.getGCMembers() ?: listOf()
        }
    }

    private fun PartyBoxDevice.ableJoinMiniPlayer(): Boolean {
        return isGattConnected || isBrEdrConnected
    }

    private fun OneDevice.ableJoinMiniPlayer(members: List<Member>): Boolean {
        val device = this
        if (device.isNonePlayableDevice() ||
            members.contains(device = device)) { // remove GC member
            return false
        }

        return device.isWiFiOnline || // WiFi Controllable
                device.isMediaPlayerSupport() // BLE Controllable
    }

    private fun List<Member>.contains(device: OneDevice): Boolean {
        val members = this
        return members.any { member ->
            member.crc == device.UUID || member.id == device.wlan0MacWithoutColon()
        }
    }

    private fun sortAndUpdateItems(items: List<AbsPlayerItem>) {
        _miniPlayerItems.value = items.sortedWith(miniPlayerComparator)
        Logger.d(TAG, "sortAndUpdateItems() >>> items[${items.size}]")
    }

    /**
     * Same with [MyProductsFragment.productBeanComparator]
     */
    private val miniPlayerComparator = Comparator<AbsPlayerItem> { o1, o2 ->
        return@Comparator when {
            o1.device.firstTouchTime > o2.device.firstTouchTime -> -1
            o1.device.firstTouchTime < o2.device.firstTouchTime -> 1
            else -> 0
        }
    }

    companion object {
        private const val TAG = "MiniPlayersViewModel"
    }
}