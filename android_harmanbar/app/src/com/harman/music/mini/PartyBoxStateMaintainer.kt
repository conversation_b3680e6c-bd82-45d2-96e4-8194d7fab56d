package com.harman.music.mini

import android.bluetooth.BluetoothDevice
import androidx.annotation.WorkerThread
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.isHorizonCategory
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.discover.DeviceStore
import com.harman.discover.bean.BaseDevice
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.info.AudioSource
import com.harman.log.Logger
import kotlinx.coroutines.CoroutineScope

/**
 * Created by gerrar<PERSON><PERSON><PERSON> on 2024/9/20.
 *
 * Maintain player related info and state round [device]
 */
class PartyBoxStateMaintainer(
    uuid: String,
    coroutineScope: CoroutineScope,
    updater: (String) -> Unit,
) : DeviceListenerWrapper(
    uuid = uuid,
    coroutineScope = coroutineScope,
    updater = updater
), IPartyBoxDeviceListener {

    @WorkerThread
    override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
        Logger.d(TAG, "onGattStatusChanged() >>> status[$status]")
        onConnectStatusChanged(protocol = BluetoothDevice.TRANSPORT_LE, status = status)
    }

    @WorkerThread
    override fun onBrEdrStatusChanged(status: EnumConnectionStatus, session: BaseBrEdrSession<*, *, *>) {
        Logger.d(TAG, "onBrEdrStatusChanged() >>> status[$status]")
        onConnectStatusChanged(protocol = BluetoothDevice.TRANSPORT_BREDR,status = status)
    }

    @WorkerThread
    fun onConnectStatusChanged(protocol: Int, status: EnumConnectionStatus) {
        when (status) {
            EnumConnectionStatus.CONNECTED -> {
                DeviceStore.findPartyBox(uuid = uuid)?.reqPlayerInfo(protocol = protocol)
            }
            else -> {
                // do nothing
            }
        }
    }

    @WorkerThread
    override fun onPlayerInfoUpdate() {
        Logger.d(TAG, "onPlayStatus() >>> [$uuid]")
        updatePlayerItem()
    }

    @WorkerThread
    override fun onDeviceInfoUpdate() {
        val device = DeviceStore.findPartyBox(uuid) ?: return

        if (device.isHorizonCategory()) {
            Logger.d(TAG, "onDeviceInfoUpdate() >>> [${device.UUID}] audioSource[${device.audioSource}]")
            when (device.audioSource) {
                AudioSource.FM_AUDIO,
                AudioSource.DAB_AUDIO -> {
                    device.reqRadioInfo(
                        protocol = BluetoothDevice.TRANSPORT_LE,
                        cmd = ReqRadioInfoCommand(RadioInfo.RequestType.LastStation)
                    )
                }

                else -> Unit
            }
        }
    }

    @WorkerThread
    override fun onVolume(volume: Int) {
        Logger.d(TAG, "onVolume() >>> [$uuid] $volume")
        updatePlayerItem()
    }

    @WorkerThread
    override fun onMute(isMute: Boolean) {
        Logger.d(TAG, "onMute() >>> [$uuid] $isMute")
        updatePlayerItem()
    }

    @WorkerThread
    override fun onRadioInfo(radioInfo: RadioInfo?, functionality: RadioInfo.Command?) {
        Logger.d(TAG, "onRadioInfo() >>> [$uuid] functionality[$functionality] $radioInfo")
        updatePlayerItem()
    }

    companion object {
        private const val TAG = "PartyBoxStateMaintainer"
    }
}