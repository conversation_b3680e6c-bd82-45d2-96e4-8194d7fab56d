package com.harman.music.mini

import androidx.annotation.IntRange
import androidx.annotation.StringRes
import com.harman.bar.app.R
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.moment.EnumMoment
import com.harman.music.EnumInputSource
import com.harman.music.EnumInputSource.Companion.toSmallIconImgRes
import com.harman.music.Tools
import com.harman.discover.bean.OneDevice
import com.harman.music.player.OnePlayerViewModel
import com.harman.moment.MomentViewModel
import com.harman.music.Tools.isMomentMixWithMusicActive
import com.harman.music.player.toEnumInputSource
import com.harman.music.service.EnumMusicServiceSource
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBoxDevice
import com.harman.log.Logger
import com.harman.music.EnumInputSource.Companion.displayAlbumCover
import com.harman.music.Tools.getRadioSubTitle
import com.harman.music.Tools.getRadioTitle
import com.harman.music.Tools.isRadioPlay
import com.harman.music.Tools.isRadioStyle
import com.harman.music.Tools.isTransitioning
import com.harman.music.Tools.radioType
import com.harman.music.Tools.showAlbumCover
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem

/**
 * Created by gerrardzhang on 2024/9/14.
 */
abstract class AbsPlayerItem(
    val device: Device,
    @IntRange(from = 0L, to = 100L)
    val volume: Int,
    val isMute: Boolean,
    val isSubTitleVisible: Boolean,
    val dlnaPlayStatus: String?
) {

    override fun toString(): String {
        return "device[${device.UUID}] volume[$volume] isMute[$isMute] isSubTitleVisible[$isSubTitleVisible]" +
                " dlnaPlayStatus[$dlnaPlayStatus] speakerName[$speakerName]"
    }

    abstract val viewType: EnumMiniPlayerType

    val speakerName: String?
        get() = device.deviceName

    val backgroundRes: Int = R.drawable.radius_medium_fixed_white_10

    val volumeWithMute: Int
        get() = if (isMute) 0 else volume

    val deviceItem: DeviceItem?
        get() = (device as? OneDevice)?.wifiDevice?.deviceItem

    val isTransitioning: Boolean
        get() = dlnaPlayStatus.isTransitioning()

    override fun equals(other: Any?): Boolean {
        if (other !is AbsPlayerItem) {
            return false
        }

        return this.device == other.device &&
                this.volume == other.volume &&
                this.isMute == other.isMute &&
                this.isSubTitleVisible == other.isSubTitleVisible &&
                this.dlnaPlayStatus == other.dlnaPlayStatus &&
                this.speakerName == other.speakerName
    }

    override fun hashCode(): Int {
        var result = device.hashCode()

        result = 31 * result + volume.hashCode()
        result = 31 * result + isMute.hashCode()
        result = 31 * result + isSubTitleVisible.hashCode()
        result = 31 * result + dlnaPlayStatus.hashCode()
        result = 31 * result + speakerName.hashCode()

        return result
    }
}

/**
 * @param title [RadioInfo.Station.frequency] or [RadioInfo.Station.stationName]
 * @param subTitle FM or DAB
 * @param isPlay [RadioInfo.State]
 */
class RadioPlayerItem(
    device: Device,
    volume: Int,
    isMute: Boolean,
    isSubTitleVisible: Boolean,
    dlnaPlayStatus: String?,
    val title: String?,
    val subTitle: String?,
    val isPlay: Boolean
) : AbsPlayerItem(
    device = device, volume = volume, isMute = isMute, isSubTitleVisible = isSubTitleVisible,
    dlnaPlayStatus = dlnaPlayStatus
) {

    override val viewType: EnumMiniPlayerType = EnumMiniPlayerType.RADIO

    val inputSourceImgRes: Int
        get() = when (device.radioType()) {
            RadioInfo.Type.FMRadio -> R.drawable.ic_input_source_fm_small
            RadioInfo.Type.DABRadio -> R.drawable.ic_input_source_dab_small
            else -> R.drawable.ic_input_source_fm_small
        }

    val playStateImgRes: Int = if (isPlay) {
        R.drawable.ic_mini_player_pause
    } else {
        R.drawable.ic_mini_player_play
    }

    override fun toString(): String {
        return "${super.toString()} title[$title] subTitle[$subTitle] isPlay[$isPlay]"
    }

    override fun equals(other: Any?): Boolean {
        if (other !is RadioPlayerItem) {
            return false
        }

        return super.equals(other) &&
                this.title == other.title &&
                this.subTitle == other.subTitle &&
                this.isPlay == other.isPlay
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + title.hashCode()
        result = 31 * result + subTitle.hashCode()
        result = 31 * result + isPlay.hashCode()
        return result
    }
}

/**
 * [inputSource] -> [OnePlayerViewModel.inputSource]
 * [albumCoverUrl] -> [OnePlayerViewModel.albumCoverUrl]
 * [title] -> [Tools.mapPlayerTitle] ([OnePlayerViewModel.songName] + [OnePlayerViewModel.inputSource])
 * [subTitle] -> [Tools.mapPlayerSubTitle] ([OnePlayerViewModel.artistName] + [OnePlayerViewModel.inputSource])
 * [progress] -> [Tools.mapPlayerProgress] ([OnePlayerViewModel.playPercentage] + [OnePlayerViewModel.inputSource])
 * [isPlay] -> [OnePlayerViewModel.isPlaying]
 * [isMomentMixWithMusic] -> MomentV2 + Soundscape.id exists + music playing
 */
class MusicPlayerItem(
    device: Device,
    volume: Int,
    isMute: Boolean,
    isSubTitleVisible: Boolean,
    dlnaPlayStatus: String?,
    val inputSource: EnumInputSource?,
    val musicServiceSource: EnumMusicServiceSource?,
    val albumCoverUrl: String?,
    val title: String?,
    val subTitle: String?,
    val progress: Int?,
    val isPlay: Boolean,
    val isMomentMixWithMusic: Boolean,
    val moment: EnumMoment?
) : AbsPlayerItem(device = device, volume = volume, isMute = isMute, isSubTitleVisible = isSubTitleVisible,
    dlnaPlayStatus = dlnaPlayStatus) {

    override fun toString(): String {
        return "${super.toString()} inputSource[$inputSource] musicServiceSource[$musicServiceSource]" +
                " albumCoverUrl[$albumCoverUrl]" +
                " title[$title] subTitle[$subTitle] progress[$progress] isPlay[$isPlay]" +
                " isMomentMixWithMusic[$isMomentMixWithMusic] moment[$moment]"
    }

    override val viewType: EnumMiniPlayerType = EnumMiniPlayerType.PLAYER

    val inputSourceImgRes: Int = inputSource.toSmallIconImgRes()

    val isAlbumCoverVisible: Boolean = inputSource.displayAlbumCover() && !albumCoverUrl.isNullOrBlank()

    val playStateImgRes: Int = if (isPlay) {
        R.drawable.ic_mini_player_pause
    } else {
        R.drawable.ic_mini_player_play
    }

    val isLayoutPlayerVisible: Boolean = !isTransitioning && when (inputSource) {
        EnumInputSource.WIFI,
        EnumInputSource.BT,
        EnumInputSource.USB -> true
        else -> false
    }

    val mixWithMomentImgRes: Int?
        get() = if (isMomentMixWithMusic) {
            moment?.miniPlayerIcRes
        } else {
            null
        }

    override fun equals(other: Any?): Boolean {
        if (other !is MusicPlayerItem) {
            return false
        }

        return super.equals(other) &&
                this.inputSource == other.inputSource &&
                this.albumCoverUrl == other.albumCoverUrl &&
                this.title == other.title &&
                this.subTitle == other.subTitle &&
                this.progress == other.progress &&
                this.isPlay == other.isPlay &&
                this.isMomentMixWithMusic == other.isMomentMixWithMusic &&
                this.moment == other.moment
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + inputSource.hashCode()
        result = 31 * result + albumCoverUrl.hashCode()
        result = 31 * result + title.hashCode()
        result = 31 * result + subTitle.hashCode()
        result = 31 * result + progress.hashCode()
        result = 31 * result + isPlay.hashCode()
        result = 31 * result + isMomentMixWithMusic.hashCode()
        result = 31 * result + moment.hashCode()
        return result
    }
}

/**
 * [moment] -> [MomentViewModel.activeSoundscape]
 * [albumCoverUrl] -> [MomentViewModel.albumCoverUrl]
 * [musicId] -> [MomentViewModel.musicId]
 * [subTitle] -> [Tools.mapMomentSubTitle] ([MomentViewModel.activeSoundscape], [MomentViewModel.musicId])
 */
class MomentPlayerItem(
    device: OneDevice,
    volume: Int,
    isMute: Boolean,
    isSubTitleVisible: Boolean,
    dlnaPlayStatus: String?,
    val moment: EnumMoment?,
    val albumCoverUrl: String?,
    val musicId: String?,
    val subTitle: String?
) : AbsPlayerItem(device = device, volume = volume, isMute = isMute, isSubTitleVisible = isSubTitleVisible,
    dlnaPlayStatus = dlnaPlayStatus) {

    override fun toString(): String {
        return "${super.toString()} inputSource[$moment] albumCoverUrl[$albumCoverUrl]" +
                " title[$musicId] subTitle[$subTitle]"
    }

    override val viewType: EnumMiniPlayerType = EnumMiniPlayerType.MOMENT

    val momentIconImgRes: Int? = moment?.iconSmallImgRes

    val isMomentIconVisible: Boolean = null != moment && EnumMoment.MOMENT_PLAYLIST != moment

    val isAlbumCoverVisible: Boolean = EnumMoment.MOMENT_PLAYLIST == moment

    @StringRes
    val titleStringRes: Int? = moment?.displayNameStringRes

    override fun equals(other: Any?): Boolean {
        if (other !is MomentPlayerItem) {
            return false
        }

        return super.equals(other) &&
                this.moment == other.moment &&
                this.albumCoverUrl == other.albumCoverUrl &&
                this.musicId == other.musicId &&
                this.subTitle == other.subTitle
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + moment.hashCode()
        result = 31 * result + albumCoverUrl.hashCode()
        result = 31 * result + musicId.hashCode()
        result = 31 * result + subTitle.hashCode()
        return result
    }
}

fun OneDevice.toPlayerItem(): AbsPlayerItem {
    val device = this

    return if (showMomentStyle(device = device)) {
        val activeMoment = EnumMoment.fromValue(device.activeSoundscapeId)

        Logger.d("MiniPlayer", "device[${device.UUID}].Moment " +
                "activeMoment[$activeMoment] " +
                "albumCoverUrl[${device.smartBtnConfigExt?.config?.music?.albumCover}] " +
                "musicId[${device.smartBtnConfigExt?.config?.music?.musicId}] " +
                "dlnaPlayStatus[${device.dlnaPlayStatus}]")

        MomentPlayerItem(
            device = device,
            volume = device.volume,
            isMute = device.isMute,
            isSubTitleVisible = true,
            moment = activeMoment,
            albumCoverUrl = device.smartBtnConfigExt?.config?.music?.albumCover,
            musicId = device.smartBtnConfigExt?.config?.music?.musicId,
            subTitle = Tools.mapMomentSubTitle(
                soundscape = activeMoment,
                musicId = device.smartBtnConfigExt?.config?.music?.musicId,
                defaultMomentTitle = WAApplication.me?.getString(R.string.ambient_audio)
            ),
            dlnaPlayStatus = device.dlnaPlayStatus
        )
    } else {
        val inputSource = Tools.mapInputSource(
            medium = device.mediumSource,
            track = device.trackSource,
            songName = device.songName,
            isPlaying = device.isPlaying
        )

        val musicServiceSource = Tools.mapMusicServiceSource(medium = device.mediumSource, track = device.trackSource)

        val percentage = com.harman.discover.util.Tools.calculatePercentage(
            duration = device.durationMills,
            current =  device.currentMills
        )
        val isMomentMixWithMusic = device.isMomentMixWithMusicActive()
        val activeMoment = EnumMoment.fromValue(device.activeSoundscapeId)

        Logger.d("MiniPlayer", "device[${device.UUID}].Music" +
                " song[${device.songName}] artist[${device.artistName}] trackSource[${device.trackSource}]" +
                " volume[${device.volume}] " +
                " isMute[${device.wifiDevice?.deviceItem?.devInfoExt?.dlnaDesireMute}]" +
                " mediaSource[${device.mediumSource}] inputSource[$inputSource] albumCover[${device.albumCover}]" +
                " percentage[$percentage] isPlay[${device.isPlaying}] current.mills[${device.currentMills}]" +
                " duration.mills[${device.durationMills}] isMomentMixWithMusic[$isMomentMixWithMusic]" +
                " activeMoment[$activeMoment] musicServiceSource[$musicServiceSource]" +
                " dlnaPlayStatus[${device.dlnaPlayStatus}]")

        MusicPlayerItem(
            device = device,
            volume = device.volume,
            isMute = device.isMute,
            isSubTitleVisible = null != inputSource,
            inputSource = inputSource,
            musicServiceSource = musicServiceSource,
            albumCoverUrl = if (inputSource.showAlbumCover()) device.albumCover else null,
            title = Tools.mapPlayerTitle(songName = device.songName, inputSource = inputSource),
            subTitle = Tools.mapPlayerSubTitle(artistName = device.artistName, inputSource = inputSource),
            progress = Tools.mapPlayerProgress(percentage = percentage, source = inputSource),
            isPlay = device.isPlaying,
            isMomentMixWithMusic = isMomentMixWithMusic,
            moment = activeMoment,
            dlnaPlayStatus = device.dlnaPlayStatus
        )
    }
}

private fun showMomentStyle(device: OneDevice): Boolean {
    val momentVersion = Tools.mapMomentVersion(device = device)
    val momentPlayState = device.controlSoundscapeV2Ext?.state

    Logger.d("MiniPlayer", "showMomentStyle() >>> momentVersion[$momentVersion] " +
            "momentPlayState[$momentPlayState] " +
            "soundScapeId[${device.activeSoundscapeId}]" +
            "isMusicPlaying[${device.isPlaying}]")

    return com.harman.music.mini.EnumMiniPlayerType.MOMENT == Tools.mapMiniPlayerType(
        version = momentVersion,
        momentPlayState = momentPlayState,
        soundScapeId = device.activeSoundscapeId,
        isMusicPlaying = device.isPlaying
    )
}

/**
 * Only support One platform and PartyBox currently.
 *
 * @param fakeCurrentMills prefer this time better than [device.currentMills]
 */
fun Device.toPlayerItem(): AbsPlayerItem? =
    when (this) {
        is OneDevice -> toPlayerItem()
        is PartyBoxDevice -> toPlayerItem()
        else -> null
    }

fun PartyBoxDevice.toPlayerItem(): AbsPlayerItem {
    val device = this
    return when {
        device.isRadioStyle() -> mapAsRadio(device = device)
        else -> mapAsNormal(device = device)
    }
}

private fun mapAsRadio(device: PartyBoxDevice): RadioPlayerItem {
    return RadioPlayerItem(
        device = device,
        volume = device.volume,
        isMute = device.isMute,
        isSubTitleVisible = true,
        dlnaPlayStatus = null,
        title = device.getRadioTitle(),
        subTitle = device.getRadioSubTitle(),
        isPlay = device.isRadioPlay()
     )
}

/**
 * Fixed source [EnumInputSource.BT] and music player style.
 */
private fun mapAsNormal(device: PartyBoxDevice): MusicPlayerItem {
    val percentage = com.harman.discover.util.Tools.calculatePercentage(duration = device.durationMills, current = device.currentMills)
    val inputSource = device.audioSource.toEnumInputSource()

    return MusicPlayerItem(
        device = device,
        volume = device.volume,
        isMute = device.isMute,
        isSubTitleVisible = true,
        inputSource = inputSource,
        musicServiceSource = null,
        albumCoverUrl = null,
        title = Tools.mapPlayerTitle(songName = device.songName, inputSource = inputSource),
        subTitle = Tools.mapPlayerSubTitle(artistName = device.artistName, inputSource = inputSource),
        progress = Tools.mapPlayerProgress(percentage = percentage, source = inputSource), // TODO Timer
        isPlay = device.isPlaying,
        isMomentMixWithMusic = false,
        moment = null,
        dlnaPlayStatus = null
    )
}
