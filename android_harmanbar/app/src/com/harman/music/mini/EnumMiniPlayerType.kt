package com.harman.music.mini

import com.harman.bar.app.R

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/9/18.
 */
enum class EnumMiniPlayerType {
    PLAYER,
    MOMENT,
    RADIO;

    companion object {

        fun fromValue(original: Int?) = entries.firstOrNull { type ->
            type.ordinal == original
        }

    }
}

fun EnumMiniPlayerType.getMiniPlayerLayoutId(): Int = when (this) {
    EnumMiniPlayerType.PLAYER -> R.layout.item_mini_player
    EnumMiniPlayerType.MOMENT -> R.layout.item_mini_moment
    EnumMiniPlayerType.RADIO -> R.layout.item_mini_radio
}