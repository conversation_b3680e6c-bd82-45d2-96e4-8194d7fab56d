package com.harman.music.service

import androidx.fragment.app.FragmentActivity
import com.harman.BaseAdapter
import com.harman.discover.bean.OneDevice
import com.harman.webview.HybridOneUiController
import com.harman.webview.models.OHVMEvent
import com.wifiaudio.model.local_music.MainItem

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/3.
 */
class MusicServiceListDialog(
    activity: FragmentActivity,
    device: OneDevice,
    listener: IMusicServiceListListener? = null,
    private val uiController: HybridOneUiController
) : AbsServiceListDialog(device = device, activity = activity, listener = listener) {

    private var doubleConfirmDialog: MusicServiceDoubleConfirmDialog? = null

    override val listAdapter: BaseAdapter<*, *> =
        MusicServiceListAdapter(dialog = this, viewModel = musicServiceListViewModel)

    override fun onItemClick(mainItem: MainItem?) {
        mainItem ?: return

        if (mainItem.bOpen) {
            doubleConfirmDialog?.dismiss()
            doubleConfirmDialog = MusicServiceDoubleConfirmDialog(
                context = context,
                device = device,
                clickListener = object : IDoubleConfirmListener {
                    override fun onRemove() {
                        musicServiceListViewModel.setSourceUsable(
                            context = context,
                            mainItem = mainItem,
                            enable = false
                        )
                    }
                }
            ).apply {
                show()
            }
        } else {
            musicServiceListViewModel.setSourceUsable(context = context, mainItem = mainItem, enable = true)
            uiController.portalMusicServicePage(
                OHVMEvent.PopMusicServiceDialog(
                    source = EnumMusicServiceSource.key2Enum(mainItem.Key)
                )
            )
            dismiss()
        }
    }

    override fun onStop() {
        super.onStop()
        doubleConfirmDialog?.dismiss()
    }
}