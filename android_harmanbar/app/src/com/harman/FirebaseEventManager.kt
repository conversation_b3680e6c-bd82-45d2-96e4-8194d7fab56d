package com.harman

import androidx.core.os.bundleOf
import com.google.firebase.analytics.FirebaseAnalytics
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.util.Tools.printMap
import com.harman.hkone.Util
import com.harman.log.Logger
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.ByteIntConverter

/**
 * start debugview
 * adb shell setprop debug.firebase.analytics.app com.jbl.oneapp.debug
 * or
 * adb shell setprop debug.firebase.analytics.app com.harmankardon.oneapp.debug
 *
 * stop debugview
 * adb shell setprop debug.firebase.analytics.app .none.
 *
 * logcat:
 * adb shell setprop log.tag.FA VERBOSE
 * adb shell setprop log.tag.FA-SVC VERBOSE
 * adb logcat -v time -s FA FA-SVC
 *
 * A py tool to collect logcat into a file
 * python3 ./script/firebaseReportLog.py
 */
object FirebaseEventManager {

    private val firebaseAnalytics by lazy {
        FirebaseAnalytics.getInstance(WAApplication.me.applicationContext)
    }

    /**
     * @param eventName @link [EventUtils.EventName]
     * @param device used for auto fill
     * [EventUtils.Dimension.DI_MODEL_NAME]
     * [EventUtils.Dimension.DI_DEVICE_PID]
     * [EventUtils.Dimension.DI_FIRMWARE_VERSION]
     * [EventUtils.Dimension.DI_DEVICE_BT_MAC]
     * [EventUtils.Dimension.DI_DEVICE_WLAN0_MAC]
     * [EventUtils.Dimension.DI_DEVICE_UID_SHA256]
     * [EventUtils.Dimension.DI_REGION_CODE]
     *
     * @param dimensions custom dimens for each scenario, @link [EventUtils.Dimension]
     *
     * [EventUtils.Dimension.DI_LAUNCH_SEQ] will be auto-filled
     *
     * @Attention null value will be removed from final firebase report bundle
     */
    fun report(eventName: String, device: Device? = null, dimensions: Map<String, Any?>, logTag: String? = null) {
        val collections = mutableMapOf<String, Any?>()

        device?.let {
            collections[EventUtils.Dimension.DI_MODEL_NAME] = device.modelName()
            collections[EventUtils.Dimension.DI_DEVICE_PID] = device.pid
            collections[EventUtils.Dimension.DI_FIRMWARE_VERSION] = device.getFwVersion()
            collections[EventUtils.Dimension.DI_DEVICE_BT_MAC] = device.macAddress?.lowercase()
            collections[EventUtils.Dimension.DI_DEVICE_WLAN0_MAC] = device.wlan0?.lowercase()
            collections[EventUtils.Dimension.DI_DEVICE_UID_SHA256] = device.macAddress?.lowercase()?.let {
                ByteIntConverter.sha256(it)
            }
            collections[EventUtils.Dimension.DI_REGION_CODE] = device.getRegionCode()?.uppercase()
        }

        collections.putAll(dimensions)
        collections[EventUtils.Dimension.DI_LAUNCH_SEQ] = Util.getSeq()

        Logger.d(logTag ?: TAG, "report() >>> event[$eventName] values:\n${collections.printMap()}")

        firebaseAnalytics.logEvent(
            eventName,
            bundleOf(*collections.mapNotNull { collection ->
                if (null == collection.value) { // null value will be removed from final firebase report bundle
                    return@mapNotNull null
                }

                Pair(collection.key, collection.value)
            }.toTypedArray())
        )
    }

    private fun Device.getFwVersion(): String? = when (val device = this) {
        is OneDevice -> device.firmware
        is PartyBoxDevice -> device.firmwareVersion
        is PartyLightDevice -> device.firmwareVersion
        is PartyBandDevice -> device.firmwareVersion
        else -> null
    }

    private fun Device.getRegionCode(): String? = when (val device = this) {
        is OneDevice -> device.regionCode
        else -> null
    }

    private const val TAG = "FirebaseEventManager"
}