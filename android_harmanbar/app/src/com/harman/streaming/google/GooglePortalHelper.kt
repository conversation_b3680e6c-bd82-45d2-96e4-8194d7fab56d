package com.harman.streaming.google

import androidx.annotation.MainThread
import androidx.fragment.app.FragmentActivity
import com.harman.EventUtils
import com.harman.connect.getC4aPermissionStatusWithTimeout
import com.harman.va.VAGoogleActivity
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.va.refactor.VAPortalHelper
import kotlinx.coroutines.withContext

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/1/9.
 */
object GooglePortalHelper {


    /**
     * Do pre detection and display enable dialog if device not enable Google Cast/VA currently.
     */
    @MainThread
    suspend fun portalCastOrVA(device: OneDevice, activity: FragmentActivity, logTag: String, vaEntry: EventUtils.Dimension.EnumCoulsonEntry? = null) {
        if (device.deviceSupportGoogleVA()) {
            Logger.d(logTag, "portalCastOrVA() >>> device[${device.UUID}] pid[${device.pid}] support VA")

            VAPortalHelper.portalGVA(context = activity, device = device, vaEntry)
            return
        }

        portalCast(device = device, activity = activity, logTag = logTag)
    }

    @MainThread
    suspend fun portalCast(device: OneDevice, activity: FragmentActivity, logTag: String) {
        Logger.d(logTag, "portalCast() >>> device[${device.UUID}] pid[${device.pid}] didn't support VA")
        if (true == device.c4aPermissionStatusExt?.status?.enable) {
            Logger.d(logTag, "portalCast() >>> device[${device.UUID}] pid[${device.pid}] is enable c4a by cache.")
            GoogleCastActivity.portal(context = activity, device = device)
            return
        }

        val isEnable = withContext(DISPATCHER_DEFAULT) {
            device.getC4aPermissionStatusWithTimeout(logTag = logTag) ?: false
        }

        Logger.d(logTag, "portalCast() >>> device[${device.UUID}] pid[${device.pid}] is enable c4a currently[$isEnable]")
        if (isEnable) {
            GoogleCastActivity.portal(context = activity, device = device)
            return
        }

        GoogleCastEnableDialog(
            activity = activity,
            device = device,
            listener = object : IGoogleCastEnableDialogListener {
                override fun onGoogleCastLater() {
                    Logger.d(logTag, "portalCast.onGoogleCastLater() >>> cancel enable GoogleCast")
                }

                override fun onChromeCastOpt(optIn: Boolean) {
                    Logger.d(logTag, "portalCast.onChromeCastOpt() >>> ChromeCastOpt[$optIn]")
                    GoogleCastActivity.portal(context = activity, device = device)
                }
            }
        ).apply {
            show()
        }
    }
}