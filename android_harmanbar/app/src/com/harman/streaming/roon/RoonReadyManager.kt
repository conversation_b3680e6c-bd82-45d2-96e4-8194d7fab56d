package com.harman.streaming.roon

import androidx.annotation.AnyThread
import com.harman.bar.app.BuildConfig
import com.harman.connect.syncGetDeviceInfoWithTimeout
import com.harman.modelName
import com.harman.multichannel.repository.CacheRepository
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.safeResume
import com.harman.log.Logger
import com.harman.service.CredentialManager
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_IO
import com.harmanbar.ble.utils.GsonUtil
import java.io.IOException
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Headers.Companion.toHeaders
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response

object RoonReadyManager {

    suspend fun hasClickRoonReadyCard(crc: String?): Boolean {
        return CacheRepository.getInstance().getFlag(ROONREADYCARDFLAG + crc)
    }

    suspend fun hideRoonReadyCard(crc: String?, clickedRoonReadyCard: Boolean) {
        CacheRepository.getInstance().saveFlag(ROONREADYCARDFLAG + crc, clickedRoonReadyCard)
    }


    suspend fun getRemoteCouponCampaign(device: OneDevice): CampaignResponse? {


        val deviceInfo = device.deviceInfoExt?.deviceInfo ?: run {
            device.syncGetDeviceInfoWithTimeout(logTag = TAG)
        }

        if (null == deviceInfo) {
            Logger.e(TAG, "refreshRemoteCampaigns() >>> fail to fetch device info")
            return null
        }
        var productName = device.modelName()
        var serialNumber = device.serialNumber
        var regionCode = deviceInfo.countryCode

//        if (BuildConfig.DEBUG) {
//        productName = "JBL Charge 5 Wi-Fi"
//        serialNumber = "TL200-000001001"
//        regionCode = "EMEA"
//        }

        var campaign = getRemoteCouponCampaign(
            productName,
            serialNumber,
            regionCode
        )
        if (campaign != null) {
            CacheRepository.getInstance().saveCampaign(device.UUID, campaign)
        }
        Logger.i(TAG, "refreshRemoteCampaigns() >>> rsp:$campaign")
        return campaign

    }

    suspend fun getCampaignFromCache(uuid: String?): CampaignResponse? {
        return CacheRepository.getInstance().getCampaign(uuid)

    }

    @AnyThread
    suspend fun getRemoteCouponCampaign(
        productName: String?,
        serialNumber: String?,
        regionCode: String?
    ): CampaignResponse? {
        val url = genReqUrl(
            productName = productName,
            serialNumber = serialNumber,
            regionCode = regionCode
        )
        if (url.isNullOrBlank()) {
            return null
        }

        Logger.i(TAG, "getRemoteCouponCampaign() >>> url: $url")
        val okHttpClient = OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .build()

        val header = hashMapOf<String, String>(
            Pair("x-api-key", CredentialManager.getInstance().accessCredential ?: "")
        )

        val request: Request = Request.Builder()
            .url(url)
            .headers(header.toHeaders())
            .build()

        val call = okHttpClient.newCall(request)

        return withContext(DISPATCHER_API) {
            suspendCancellableCoroutine<CampaignResponse?> { continuation ->
                call.enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Logger.e(TAG, "getRemoteCouponCampaign.onFailure() >>> $e")
                        continuation.safeResume(null)
                    }

                    @Throws(IOException::class)
                    override fun onResponse(call: Call, response: Response) {
                        Logger.d(TAG, "getRemoteCouponCampaign.onResponse() >>> ${response.code}")
                        val bytes = response.body?.bytes() ?: run {
                            Logger.e(TAG, "getRemoteCouponCampaign.onResponse() >>> empty body")
                            return
                        }

                        val jsonStr = String(bytes)
                        Logger.d(TAG, "getRemoteCouponCampaign.onResponse() >>> \n$jsonStr")

                        val bean =
                            GsonUtil.parseJsonToBean(jsonStr, CampaignResponse::class.java) ?: run {
                                Logger.e(
                                    TAG,
                                    "getRemoteCouponCampaign.onResponse() >>> fail to unmarshall to JsonRootBean"
                                )
                                return
                            }

                        continuation.safeResume(bean)
                    }
                })
            }
        }
    }

    private suspend fun genReqUrl(
        productName: String?,
        serialNumber: String?,
        regionCode: String?
    ): String? {

        val urlFormat = BuildConfig.ONE_CLOUDSERVER_HOST +
            "/apps/v1/marketing/campaigns?product-name=%s&serial-number=%s&region-code=%s"

        Logger.d(
            TAG, "genReqUrl() >>> format: $urlFormat\n" +
                "productName[$productName]\n" +
                "serialNumber[$serialNumber]\n" +
                "regionCode[$regionCode]"
        )

        return withContext(DISPATCHER_IO) {
            try {
                urlFormat.format(
                    URLEncoder.encode(
                        if (productName.isNullOrBlank()) "" else productName,
                        "UTF-8"
                    ),
                    URLEncoder.encode(
                        if (serialNumber.isNullOrBlank()) "" else serialNumber,
                        "UTF-8"
                    ),
                    URLEncoder.encode(if (regionCode.isNullOrBlank()) "" else regionCode, "UTF-8")
                )
            } catch (e: UnsupportedEncodingException) {
                Logger.e(TAG, "genReqUrl() >>> exception while formatting req. url:$e")
                null
            }
        }
    }

    private const val ROONREADYCARDFLAG = "ROONREADCARDFLAG"

    const val DEFAULT_ROON_READY_HOME_URL = "https://roon.app/en/"

    const val DEFAULT_ROON_READY_REDEEM_URL = "https://roon.link/redeem?code=%s"

    const val DEFAULT_GOOGLE_PLAY_ROON =
        "https://play.google.com/store/apps/details?id=com.roon.mobile"
    private const val TAG = "RoonReadyManager"
}