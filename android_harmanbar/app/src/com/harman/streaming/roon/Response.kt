package com.harman.streaming.roon

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/31.
 */
@Keep
data class CampaignResponse(
    @SerializedName("campaigns")
    val campaigns: List<Campaign>? = null
) {
    val couponCampaign: Campaign?
        get() = campaigns?.firstOrNull { campaign ->
            "roon-coupon" == campaign.type
        }
}

@Keep
data class Campaign(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("start_date")
    val startDate: String? = null,
    @SerializedName("end_date")
    val endDate: String? = null,
    @SerializedName("priority")
    val priority: Int? = null,
    @SerializedName("impression_limit")
    val impressionLimit: Int? = null,
    @SerializedName("data")
    val data: Data? = null
)

@Keep
data class Data(
    @SerializedName("redeem_link_url")
    val redeemLinkUrl: String? = null,
    @SerializedName("device_coupon_code")
    val deviceCouponCode: String? = null
)