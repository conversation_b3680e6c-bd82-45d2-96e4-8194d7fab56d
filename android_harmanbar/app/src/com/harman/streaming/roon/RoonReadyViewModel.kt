package com.harman.streaming.roon

import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.isActive
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/31.
 */
class RoonReadyViewModelFactory(
    private val device: OneDevice
): ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return RoonReadyViewModel(device = device) as T
    }
}

class RoonReadyViewModel(
    private val device: OneDevice
) : ViewModel() {

    private val _campaign = MutableLiveData<Campaign?>()
    val campaign: LiveData<Campaign?>
        get() = _campaign

    private var reqCampaignsJob: Job? = null

    @MainThread
    fun refreshRemoteCampaigns() {
        if (reqCampaignsJob.isActive()) {
            return
        }

        reqCampaignsJob = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val campaign = RoonReadyManager.getCampaignFromCache(device.UUID)
            Logger.i(TAG, "refreshRemoteCampaigns() >>> rsp:$campaign")
            _campaign.value = campaign?.couponCampaign
        }
    }

    companion object {
        private const val TAG = "RoonReadyViewModel"
    }
}