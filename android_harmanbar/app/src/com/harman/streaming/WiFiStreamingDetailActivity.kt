package com.harman.streaming

import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import com.harman.EventUtils
import com.harman.bar.app.databinding.ActivityWifiStreamingDetailBinding
import com.harman.isCN
import com.harman.parseAsOneDevice
import com.harman.partylight.util.fitSystemBar
import com.harman.portalUrl
import com.harman.safeAddNonNullSources
import com.harman.streaming.amazon.AmazonAlexaActivity
import com.harman.streaming.google.GoogleCastActivity
import com.harman.streaming.google.GooglePortalHelper
import com.harman.streaming.roon.RoonReadyManager
import com.harman.va.VAAlexaActivity
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.deviceSupportAirPlay
import com.harman.discover.bean.deviceSupportAlexa
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.bean.deviceSupportChromeCast
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.discover.bean.deviceSupportQobuz
import com.harman.discover.bean.deviceSupportRoonReady
import com.harman.discover.bean.deviceSupportSpotify
import com.harman.discover.bean.deviceSupportTidal
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.harman.va.refactor.VAPortalHelper
import com.harman.widget.AppCompatBaseActivity
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/8/22.
 */
class WiFiStreamingDetailActivity : AppCompatBaseActivity() {

    private var viewModel: WiFiStreamingViewModel? = null

    private val _device = MutableLiveData<OneDevice>()
    val displayVARelatedUi: LiveData<Boolean> = _device.map { device ->
        !device.isCN()
    }

    private val _googleCastEntranceVisible = MediatorLiveData<Boolean>()
    val googleCastEntranceVisible: LiveData<Boolean>
        get() = _googleCastEntranceVisible

    private val _alexaCastEnableVisible = MutableLiveData<Boolean>()
    val alexaCastEnableVisible: LiveData<Boolean>
        get() = _alexaCastEnableVisible

    val alexaCastForwardVisible: LiveData<Boolean> = alexaCastEnableVisible.map {
        !it
    }

    private val _qobuzEntranceVisible = MutableLiveData<Boolean>()
    val qobuzEntranceVisible: LiveData<Boolean>
        get() = _qobuzEntranceVisible

    private val _roonEntranceVisible = MutableLiveData<Boolean>()
    val roonEntranceVisible: LiveData<Boolean>
        get() = _roonEntranceVisible

    private val _spotifyEntranceVisible = MutableLiveData<Boolean>()
    val spotifyEntranceVisible: LiveData<Boolean>
        get() = _spotifyEntranceVisible

    private val _tidalEntranceVisible = MutableLiveData<Boolean>()
    val tidalEntranceVisible: LiveData<Boolean>
        get() = _tidalEntranceVisible

    private val _airPlayEntranceVisible = MutableLiveData<Boolean>()
    val airPlayEntranceVisible: LiveData<Boolean>
        get() = _airPlayEntranceVisible

    private val _qPlayEntranceVisible = MutableLiveData<Boolean>()
    val qPlayEntranceVisible: LiveData<Boolean>
        get() = _qPlayEntranceVisible

    private val _alexaEntranceVisible = MutableLiveData<Boolean>()
    val alexaEntranceVisible: LiveData<Boolean>
        get() = _alexaEntranceVisible

    private val _topBarTopMargin = MutableLiveData<Int>()
    val topBarTopMargin: LiveData<Int>
        get() = _topBarTopMargin

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Utils.decorateHarmanWindow(this)
        _topBarTopMargin.value = Utils.getStatusBarHeight(this)

        val device = parseAsOneDevice() ?: run {
            finish()
            return
        }

        val viewModel = ViewModelProvider(
            this, WiFiStreamingViewModelFactory(device = device)
        )[WiFiStreamingViewModel::class.java]
        <EMAIL> = viewModel

        _device.value = device

        Logger.d(TAG, "onCreate() >>> device[${device.UUID}] pid[${device.pid}] " +
                "CN[${device.isCN()}] AirPlay[${device.deviceSupportAirPlay()}] " +
                "Roon[${device.deviceSupportRoonReady()}] Spotify[${device.deviceSupportSpotify()}] " +
                "Qobuz[${device.deviceSupportQobuz()}] Tidal[${device.deviceSupportTidal()}] " +
                "Alexa[${device.deviceSupportAlexa()}] AlexaVA[${device.deviceSupportAlexaVA()}] " +
                "ChromeCast[${device.deviceSupportChromeCast()}] GoogleVA[${device.deviceSupportGoogleVA()}]")

        // Universal
        _airPlayEntranceVisible.value = device.deviceSupportAirPlay()
        _roonEntranceVisible.value = device.deviceSupportRoonReady()

        // Non-CN support
        _spotifyEntranceVisible.value = !device.isCN() && device.deviceSupportSpotify()
        _qobuzEntranceVisible.value = !device.isCN() && device.deviceSupportQobuz()
        _tidalEntranceVisible.value = !device.isCN() && device.deviceSupportTidal()
        _alexaEntranceVisible.value = !device.isCN() && (device.deviceSupportAlexa() || device.deviceSupportAlexaVA())

        // CN support
        _qPlayEntranceVisible.value = device.isCN()

        val binding = ActivityWifiStreamingDetailBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this
        binding.activity = this
        binding.tvQplayConnectContent.text = SkinResourcesUtils.getString("从QQ音乐App向音箱投放音乐")
        fitSystemBar()
        setContentView(binding.root)
        bindingData(viewModel = viewModel)
    }

    private fun bindingData(viewModel: WiFiStreamingViewModel) {
        _googleCastEntranceVisible.safeAddNonNullSources(
            displayVARelatedUi,
            viewModel.googleCastEnabled,
            _device
        ) { displayVARelatedUi, googleCastEnabled, device ->
            Logger.d(TAG, "googleCastEntranceVisible >>> googleCastEnabled[$googleCastEnabled]")

            _googleCastEntranceVisible.value =
                ((device?.deviceSupportGoogleVA() == true) || (device?.deviceSupportChromeCast() == true)) &&
                        (displayVARelatedUi ?: false) &&
                        !(googleCastEnabled ?: false)
        }

        viewModel.alexaEnabled.observe(this) {
            _alexaCastEnableVisible.value = !it
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel?.asyncUpdateGoogleCastStatus()
        viewModel?.asyncUpdateAlexaStatus()
    }

    fun onBackBtnClick() {
        finish()
    }

    fun onGoogleCastEnableClick() {
        val device = _device.value ?: return

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            GooglePortalHelper.portalCastOrVA(
                device = device,
                activity = this@WiFiStreamingDetailActivity,
                logTag = TAG,
                vaEntry = EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_WIFI_STREAMING
            )
        }
    }

    fun onLearnMoreClick() {
        portalUrl(
            config = AppConfigurationUtils.getGoogleCastIntroUrl(),
            default = GoogleCastActivity.DEFAULT_GOOGLE_CAST_INTRO_URL
        )
    }

    fun onAlexaCastClick() {
        val device = _device.value ?: return

        if (device.isAlexaEnable(supportAlexaVA = device.deviceSupportAlexaVA())) {
            portalUrl(
                config = AppConfigurationUtils.getAlexaMRMUrl(),
                default = ALEXA_MRM
            )
        }
    }

    fun onAlexaCastEnableClick() {
        val device = _device.value ?: return

        if (device.deviceSupportAlexaVA()) {
            VAPortalHelper.portalAlexa(context = this, device = device, EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_WIFI_STREAMING)
        } else {
            AmazonAlexaActivity.portal(context = this, device = device)
        }
    }

    fun onSpotifyConnectClick() {
        portalUrl(
            config = null,
            default = DEFAULT_STREAM_WITH_SPOTIFY_URL
        )
    }

    fun onQobuzConnectClick() {
        portalUrl(
            config = null,
            default = DEFAULT_STREAM_WITH_QOBUZ_URL
        )
    }

    fun onTidalConnectClick() {
        portalUrl(
            config = AppConfigurationUtils.getTidalConnectUrl(),
            default = TidalConnectActivity.DEFAULT_TIDAL_CONNECT_URL
        )
    }

    fun onRoonConnectClick() {
        portalUrl(
            config = AppConfigurationUtils.getRoonHomeUrl(),
            default = RoonReadyManager.DEFAULT_ROON_READY_HOME_URL
        )
    }

    fun onAirPlayClick() {
        portalUrl(
            config = AppConfigurationUtils.getLearnMoreAboutAirPlayUrl(),
            default = DEFAULT_AIRPLAY_URL
        )
    }

    override val tag: String = TAG

    override val baseControlDevice: Device?
        get() = _device.value

    companion object {
        private const val TAG = "WiFiStreamingDetailActivity"

        private const val ALEXA_MRM = "https://www.amazon.com/b?ie=UTF8&node=17910796011"
        const val DEFAULT_STREAM_WITH_QOBUZ_URL = "https://www.qobuz.com/connect"
        const val DEFAULT_STREAM_WITH_SPOTIFY_URL = "https://connect.spotify.com/howto"
        const val DEFAULT_AIRPLAY_URL = "https://support.apple.com/en-us/HT202809"
    }
}