package com.harman.ota.partylight

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieDrawable
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityPlOtaBinding
import com.harman.bar.app.databinding.LayoutItemPartyligthOtaBinding
import com.harman.partylight.isPlOtaProcessing
import com.harman.partylight.util.fitSystemBar
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.gone
import com.harman.partylight.util.invisible
import com.harman.partylight.util.visible
import com.harman.discover.DeviceScanner
import com.harman.discover.EnumScanPerformance
import com.harman.discover.bean.PartyLightDevice
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.SpaceItemDecoration
import kotlinx.coroutines.launch

class PLOtaActivity : AppCompatActivity() {
    private val vm: PLOtaViewModel by viewModels()
    private val binding by lazy { ActivityPlOtaBinding.inflate(layoutInflater) }
    private fun handleOnBackPressed() {
        if (vm.processStatus.value == ProcessStatus.Processing) {
            ExitPartyLightOtaDialog(this, ::exit).show()
        } else {
            exit()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fitSystemBar()
        window.decorView.keepScreenOn = true
        isPlOtaProcessing = true
        setContentView(binding.root)
        initView()
        observeModel()
        DeviceScanner.startScan(this, EnumScanPerformance.HIGH)
    }

    override fun onResume() {
        super.onResume()
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onPause() {
        super.onPause()
        window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        handleOnBackPressed()
    }

    override fun onDestroy() {
        super.onDestroy()
        window.decorView.keepScreenOn = false
        isPlOtaProcessing = false
    }

    fun initView() {
        binding.appbar.tvTitle.text = getString(R.string.ptl_firmware_upgrade)
        binding.appbar.ivLeading.setOnClickListener {
            handleOnBackPressed()
        }
        initRv()
        binding.tvConfirm.setOnClickListener {
            vm.updateAll()
        }
    }

    private fun observeModel() {
        vm.processStatus.observe(this) {
            buildProcess(it)
        }
        vm.errorMsg.observe(this) {
            ToastUtils.showShort(it)
        }
    }

    private fun exit() {
        lifecycleScope.launch {
            vm.exitOta()
            finish()
        }
    }

    private fun initRv() {
        binding.rv.adapter = RvAdapter(vm, this)
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.addItemDecoration(
            SpaceItemDecoration(
                ConvertUtils.dp2px(10f), SpaceItemDecoration.VERTICAL
            )
        )
        binding.rv.itemAnimator = null
    }

    private fun buildProcess(status: ProcessStatus) {
        binding.gContent.apply {
            if (status == ProcessStatus.Checking) gone() else visible()
        }
        binding.gLoading.apply {
            if (status == ProcessStatus.Checking) visible() else gone()
        }
        //build tvConfirm
        binding.tvConfirm.apply {
            visibility =
                if (status == ProcessStatus.Ready || status == ProcessStatus.CanNotUpdate) View.VISIBLE
                else View.INVISIBLE
            isClickable = status != ProcessStatus.CanNotUpdate
            backgroundTintList =
                ColorStateList.valueOf(ColorUtils.getColor(if (status == ProcessStatus.CanNotUpdate) R.color.bg_on_card else R.color.fg_primary))
            setTextColor(ColorUtils.getColor(if (status == ProcessStatus.CanNotUpdate) R.color.bg_s3 else R.color.fg_inverse))
        }

        //build tvTips
        binding.tvTips.apply {
            visibility = if (status == ProcessStatus.CanNotUpdate) View.INVISIBLE else View.VISIBLE
            text = when (status) {
                ProcessStatus.Ready -> getString(R.string.estimated_update_time, "${vm.getEstimateMins()}")
                ProcessStatus.Processing -> getString(R.string.ptl_update_tip)
                ProcessStatus.AllUpdated -> getString(R.string.ptl_all_updated)
                else -> ""
            }
            setTextColor(
                ContextCompat.getColor(
                    this@PLOtaActivity, if (status == ProcessStatus.AllUpdated) R.color.fg_activate else R.color.fg_secondary
                )
            )
        }
    }
}

/**
 * 设备列表adapter
 */
private class RvAdapter(val viewModel: PLOtaViewModel, lo: LifecycleOwner) : RecyclerView.Adapter<RvAdapter.VH>() {
    val differ = generateSimpleDiffer<OtaItemUiState>()

    init {
        viewModel.bleOtaItems.observe(lo) {
            differ.submitList(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return LayoutInflater
            .from(parent.context)
            .inflate(R.layout.layout_item_partyligth_ota, parent, false)
            .let {
                VH(LayoutItemPartyligthOtaBinding.bind(it))
            }
    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val item = differ.currentList[position]
        val binding = holder.binding
        buildGroupTitle(binding, position)
        Glide.with(binding.ivIcon.context).load(item.devPic).into(binding.ivIcon)
        buildDevName(binding, item)
        buildDesc(binding, item)
        buildRightWidget(binding, item)
        buildBottomLowPowerLayout(binding, position)
    }

    private fun buildDevName(binding: LayoutItemPartyligthOtaBinding, item: OtaItemUiState) {
        binding.tvName.text = item.devName
        if (item.status == BleUpdateStatus.LowPower) {
            binding.tvName.setTextColor(ColorUtils.getColor(R.color.red_1))
        } else {
            binding.tvName.setTextColor(ColorUtils.getColor(android.R.color.white))
        }
    }

    private fun buildGroupTitle(binding: LayoutItemPartyligthOtaBinding, position: Int) {
        val thisItem = differ.currentList[position]
        val prevItem = differ.currentList.getOrNull(position - 1)
        if (position == 0) {
            binding.tvGroup.visible()
            if (thisItem.status != BleUpdateStatus.Success) {
                //第一个item并且不是更新成功的，展示可更新title
                binding.tvGroup.text = StringUtils.getString(R.string.ptl_update_avaliable_with_version, thisItem.newVersion)
            } else {
                //第一个item并且是更新成功的，展示已更新title
                binding.tvGroup.text = StringUtils.getString(R.string.ptl_updated)
            }
        } else if (thisItem.status == BleUpdateStatus.Success && prevItem?.status != BleUpdateStatus.Success) {
            //非第一个item的首个成功更新的item展示已更新title
            binding.tvGroup.visible()
            binding.tvGroup.text = StringUtils.getString(R.string.ptl_updated)
        } else {
            binding.tvGroup.gone()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun buildDesc(binding: LayoutItemPartyligthOtaBinding, item: OtaItemUiState) {
        when (item.status) {
            BleUpdateStatus.Success, BleUpdateStatus.ReadyUpdate, BleUpdateStatus.Updating, BleUpdateStatus.Installing -> {
                binding.tvDesc.text = StringUtils.getString(R.string.format_version, item.fwVersion)
            }

            BleUpdateStatus.LowPower -> {
                binding.tvDesc.text = StringUtils.getString(R.string.ptl_low_battery)
            }

            else -> {
                binding.tvDesc.text = StringUtils.getString(R.string.ptl_update_failed)
            }
        }
        when (item.status) {
            BleUpdateStatus.Success -> binding.tvDesc.setTextColor(ColorUtils.getColor(R.color.fg_activate))
            BleUpdateStatus.Failed, BleUpdateStatus.LowPower -> binding.tvDesc.setTextColor(ColorUtils.getColor(R.color.red_1))

            else -> binding.tvDesc.setTextColor(
                ColorUtils.getColor(R.color.fg_secondary)
            )
        }
    }

    private fun buildRightWidget(binding: LayoutItemPartyligthOtaBinding, item: OtaItemUiState) {
        binding.pb.apply {
            if (item.status == BleUpdateStatus.Updating) {
                visible()
                progress = item.otaProgress.toFloat()
            } else {
                invisible()
            }
        }
        binding.cpi.apply {
            if (item.status == BleUpdateStatus.Installing) {
                visible()
            } else {
                invisible()
            }
        }
        binding.lavReadyUpdate.apply {
            if (item.status == BleUpdateStatus.ReadyUpdate && viewModel.processStatus.value == ProcessStatus.Processing) {
                if (!isVisible) {
                    visible()
                    setAnimation(R.raw.ptl_pending)
                    repeatCount = LottieDrawable.INFINITE
                    playAnimation()
                }
            } else {
                invisible()
            }
        }
        binding.tvRightBtn.apply {
            if (item.status == BleUpdateStatus.Failed) {
                binding.tvRightBtn.visible()
                binding.tvRightBtn.setOnClickListener {
                    onRetryClick(item)
                }
            } else {
                binding.tvRightBtn.gone()
            }
        }
    }

    private fun buildBottomLowPowerLayout(binding: LayoutItemPartyligthOtaBinding, position: Int) {
        val thisItem = differ.currentList[position]
        val nextItem = differ.currentList.getOrNull(position + 1)
        if (thisItem.status == BleUpdateStatus.LowPower && (null == nextItem || nextItem.status == BleUpdateStatus.Success)) {
            //最后一个未更新成功的低电量设备的需要展示提示
            binding.tvLowPowerWarning.visible()
            binding.tvLowPowerWarning.text = binding.root.context.getString(R.string.ptl_low_battery_tip, "${PartyLightDevice.otaSafePower}%")
        } else {
            binding.tvLowPowerWarning.gone()
        }
    }

    private fun onRetryClick(item: OtaItemUiState) {
        viewModel.retry(item)
    }

    data class VH(val binding: LayoutItemPartyligthOtaBinding) : RecyclerView.ViewHolder(binding.root)
}