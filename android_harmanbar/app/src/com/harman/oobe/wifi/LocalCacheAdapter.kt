package com.harman.oobe.wifi

import android.content.Context
import androidx.annotation.AnyThread
import androidx.annotation.WorkerThread
import com.blankj.utilcode.util.Utils
import com.harman.db.DB
import com.harman.db.DeviceCache
import com.harman.permission.UniversalDeviceObserver.loadDiagnosedDeviceList

import com.harman.discover.DeviceStore
import com.harman.discover.bean.BaseDevice
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.info.EnumProductLine
import com.harman.discover.parser.PartyBoxBLEAdvParser
import com.harman.discover.util.Tools.addIfAbsent
import com.harman.discover.util.Tools.overThreshold
import com.harman.discover.util.Tools.setPlaybackDuration
import com.harman.discover.util.Tools.toPid
import com.harman.discover.util.Tools.wlan0Mac
import com.harman.discover.util.crc.CrcHelper.dummyUUID
import com.harman.hkone.deviceoffline.OfflineDeviceHandler
import com.harman.hkone.newmultichannel.Members
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_IO
import com.harman.v5protocol.discover.BatteryInfo
import com.harman.v5protocol.discover.DeviceMiscInfo
import com.harman.v5protocol.discover.PartyInfo
import com.harman.v5protocol.discover.PartyMethodInfo
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DeviceProperty
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.LinkedList
import java.util.concurrent.ConcurrentHashMap

/**
 * Created by gerrardzhang on 2024/7/5.
 *
 * Cause we decided to deprecate file related cache [OfflineDeviceHandler] and use RoomDB as a new one.
 * This class impl. as an adapter cache between [OfflineDeviceHandler] and [DB].
 */
object LocalCacheAdapter {

    /**
     * Memory cache only store auth state for extension function [OneDevice.hadAuthed()]
     */
    private val sMemoryAuthStateMap = ConcurrentHashMap<String, Boolean>()
    private val _sMemoryDevicesCache = ConcurrentHashMap<String, DeviceCache>()
    val sMemoryDevicesCache: Map<String, DeviceCache>
        get() = _sMemoryDevicesCache

    /**
     * To avoid One Device trigger [updateDevice] soon after [deleteDeviceCache] in a short time.
     * Disable and [markAuthed] in a [REMOVE_AUTH_COLD_DOWN_MILLS] gap after [deleteDeviceCache] invoke by UUID
     */
    private val _sLastClearCacheTimestamp = ConcurrentHashMap<String, Long>()

    init {
        GlobalScope.launch(DISPATCHER_IO) {
            val context = WAApplication.me ?: return@launch
            val caches = DB.getAllDeviceCache(context)

            caches?.forEach { cache ->
                if (!sMemoryAuthStateMap.contains(cache.uuid)) {
                    sMemoryAuthStateMap[cache.uuid] = cache.hadAuthed
                }

                _sMemoryDevicesCache[cache.uuid] = cache
            }

            Logger.i(TAG, "LocalCacheAdapter >>> init restore complete")
        }
    }

    /** Define an empty implementation of the init method to make it complete the init block faster */
    fun init() = Unit

    /**
     * Judge by cache.
     */
    @AnyThread
    fun fastHadAuthed(uuid: String?): Boolean {
        if (uuid.isNullOrBlank()) {
            return false
        }

        if (null != OfflineDeviceHandler.get().getDevice(uuid)) {
            Logger.d(TAG, "fastHadAuthed() >>> device[$uuid] hit old cache")
            // exists in old
            return true
        }

        return sMemoryAuthStateMap[uuid] ?: false
    }

    @AnyThread
    fun removeAuthed(uuid: String?) {
        if (uuid.isNullOrBlank()) {
            return
        }

        OfflineDeviceHandler.get().deleteDevice(uuid)
        sMemoryAuthStateMap.remove(uuid)
        _sLastClearCacheTimestamp[uuid] = System.currentTimeMillis()
        Logger.i(TAG, "removeAuthed() >>> uuid[$uuid] remove.ts[${_sLastClearCacheTimestamp[uuid]}]")
    }

    /**
     * Judge by DB.
     */
    @AnyThread
    suspend fun hadAuthed(ctx: Context, uuid: String?): Boolean {
        if (uuid.isNullOrBlank()) {
            return false
        }

        if (null != OfflineDeviceHandler.get().getDevice(uuid)) {
            Logger.d(TAG, "hadAuthed() >>> device[$uuid] hit old cache")
            // exists in old
            return true
        }

        return withContext(DISPATCHER_IO) {
            DB.getTargetDeviceCaches(context = ctx, uuid)
        }?.getOrNull(0)?.hadAuthed ?: false
    }

    /**
     * Mark [PartyBoxDevice] as authed after:
     * # ran OOBE flow.
     */
    @AnyThread
    suspend fun PartyBoxDevice.markAuthed(ctx: Context) {
        val device = this
        val uuid = this.UUID
        if (uuid.isNullOrBlank()) {
            return
        }

        withContext(DISPATCHER_IO) {
            val lastCache = getTargetDeviceCache(context = ctx, uuid = uuid)

            DB.upsertDeviceFull(
                context = ctx,
                DeviceCache(
                    uuid = uuid,
                    hadAuthed = true,
                    pid = device.pid ?: lastCache?.pid,
                    cid = device.cid ?: lastCache?.cid,
                    serialNumber = device.serialNumber ?: lastCache?.serialNumber,
                    deviceName = device.deviceName ?: lastCache?.deviceName,
                    diagnosisReportId = device.customerServiceReportId ?: lastCache?.diagnosisReportId,
                    groupRole = device.role.ordinal,
                    groupId = device.groupID ?: lastCache?.groupId,
                    wlan0Mac = device.macAddress ?: lastCache?.wlan0Mac,
                    firmwareVer = device.firmwareVersion ?: lastCache?.firmwareVer,
                    productLine = EnumProductLine.PARTY_BOX.ordinal,
                    playbackDuration = device.pbDuration ?: lastCache?.playbackDuration
                )
            )
        }

        sMemoryAuthStateMap[uuid] = true
        Logger.d(TAG, "markAuthed() >>> $uuid")
    }

    @AnyThread
    suspend fun PartyBandBTDevice.updateCache(
        hadAuth: Boolean? = null,
        serialNumber: String? = null,
        firmwareVer: String? = null,
        deviceName: String? = null,
    ) {
        val thisDev = this
        withContext(DISPATCHER_IO) {
            var fullCache = _sMemoryDevicesCache[UUID] ?: DeviceCache(
                uuid = thisDev.UUID,
                hadAuthed = false,
                pid = thisDev.pid,
                cid = thisDev.colorID,
                deviceName = thisDev.deviceName,
                firmwareVer = thisDev.firmwareVersion,
                productLine = EnumProductLine.BAND_BOX.ordinal,
                serialNumber = thisDev.serialNumber,
            )
            hadAuth?.also {
                fullCache = fullCache.copy(hadAuthed = it)
            }
            serialNumber?.also {
                fullCache = fullCache.copy(serialNumber = it)
            }
            firmwareVer?.also {
                fullCache = fullCache.copy(firmwareVer = it)
            }
            deviceName?.also {
                fullCache = fullCache.copy(deviceName = it)
            }
            DB.upsertDeviceFull(
                context = Utils.getApp(),
                fullCache,
            )
            _sMemoryDevicesCache[UUID] = fullCache
            sMemoryAuthStateMap[UUID] = fullCache.hadAuthed
        }
    }

    @AnyThread
    suspend fun PartyBandDevice.updateCache(
        hadAuth: Boolean? = null,
        serialNumber: String? = null,
        firmwareVer: String? = null,
        deviceName: String? = null,
    ) = bleDevice?.updateCache(hadAuth, serialNumber, firmwareVer, deviceName)

    /**
     * Mark [OneDevice] as authed after:
     * # ran OOBE flow.
     * # discovered in WiFi channel.
     * # A2DP connected.
     */
    @AnyThread
    suspend fun OneDevice.markAuthed(ctx: Context): Boolean {
        val device = this
        val uuid = this.UUID
        if (uuid.isNullOrBlank()) {
            return false
        }

        if (!device.ableUpdateAuth()) {
            Logger.d(TAG, "markAuthed() >>> block [$uuid] cause still in factory reset cold down interval")
            return false
        }

        withContext(DISPATCHER_IO) {
            val lastCache = getTargetDeviceCache(context = ctx, uuid = uuid)

            DB.upsertDeviceFull(
                context = ctx,
                DeviceCache(
                    uuid = uuid,
                    pid = device.pid ?: lastCache?.pid,
                    cid = (device.cid ?: lastCache?.cid).validCid(),
                    serialNumber = device.serialNumber ?: lastCache?.serialNumber,
                    deviceName = device.deviceName ?: lastCache?.deviceName,
                    hadAuthed = true,
                    diagnosisReportId = device.customerServiceReportId ?: lastCache?.diagnosisReportId,
                    eSSID = device.eSSID ?: lastCache?.eSSID,
                    groupRole = device.role.ordinal,
                    groupId = device.groupId ?: lastCache?.groupId,
                    wlan0Mac = device.wlan0Mac() ?: lastCache?.wlan0Mac,
                    oneOsVer = device.oneOsVer ?: lastCache?.oneOsVer,
                    firmwareVer = device.firmware ?: lastCache?.firmwareVer,
                    productLine = EnumProductLine.ONE.ordinal,
                    playbackDuration = device.pbDuration ?: lastCache?.playbackDuration
                )
            )
        }

        sMemoryAuthStateMap[uuid] = true
        Logger.d(TAG, "markAuthed() >>> $uuid")
        return true
    }

    /**
     * Update full params inside [PartyBoxDevice]
     */
    @AnyThread
    suspend fun PartyBoxDevice.updateDevice(ctx: Context) {
        val device = this
        val uuid = this.UUID
        if (uuid.isNullOrBlank()) {
            return
        }

        withContext(DISPATCHER_IO) {
            val lastCache = getTargetDeviceCache(context = ctx, uuid = uuid)

            DB.upsertDeviceFull(
                context = ctx,
                DeviceCache(
                    uuid = uuid,
                    hadAuthed = lastCache?.hadAuthed ?: false,
                    pid = device.pid ?: lastCache?.pid,
                    cid = device.cid ?: lastCache?.cid,
                    serialNumber = device.serialNumber ?: lastCache?.serialNumber,
                    deviceName = device.deviceName ?: lastCache?.deviceName,
                    diagnosisReportId = device.customerServiceReportId ?: lastCache?.diagnosisReportId,
                    groupRole = device.role.ordinal,
                    groupId = device.groupID ?: lastCache?.groupId,
                    wlan0Mac = device.macAddress ?: lastCache?.wlan0Mac,
                    firmwareVer = device.firmwareVersion ?: lastCache?.firmwareVer,
                    productLine = EnumProductLine.PARTY_BOX.ordinal,
                    playbackDuration = device.pbDuration ?: lastCache?.playbackDuration
                )
            )
        }
    }

    /**
     * Update full params inside [OneDevice].
     */
    @AnyThread
    suspend fun upsert(device: OneDevice, ctx: Context): Boolean {
        val uuid = device.UUID
        if (uuid.isNullOrBlank()) {
            return false
        }

        withContext(DISPATCHER_IO) {
            val lastCache = getTargetDeviceCache(context = ctx, uuid = uuid)

            DB.upsertDeviceFull(
                context = ctx,
                DeviceCache(
                    uuid = uuid,
                    pid = device.pid ?: lastCache?.pid,
                    cid = (device.cid ?: lastCache?.cid).validCid(),
                    serialNumber = device.serialNumber ?: lastCache?.serialNumber,
                    deviceName = device.deviceName ?: lastCache?.deviceName,
                    hadAuthed = lastCache?.hadAuthed ?: false,
                    diagnosisReportId = device.customerServiceReportId ?: lastCache?.diagnosisReportId,
                    eSSID = device.eSSID ?: lastCache?.eSSID,
                    groupRole = device.role.ordinal,
                    groupId = device.groupId ?: lastCache?.groupId,
                    wlan0Mac = device.wlan0Mac() ?: lastCache?.wlan0Mac,
                    oneOsVer = device.oneOsVer ?: lastCache?.oneOsVer,
                    firmwareVer = device.firmware ?: lastCache?.firmwareVer,
                    productLine = EnumProductLine.ONE.ordinal,
                    playbackDuration = device.pbDuration ?: lastCache?.playbackDuration
                )
            )
        }

        Logger.d(TAG, "upsert() >>> $uuid")
        return true
    }

    suspend fun Device.deleteDeviceCache(ctx: Context) {
        val device = this
        val uuid = device.UUID
        if (uuid.isNullOrBlank()) {
            return
        }

        withContext(DISPATCHER_IO) {
            DB.deleteDeviceCache(
                context = ctx,
                DeviceCache(uuid = uuid)
            )
            _sMemoryDevicesCache.remove(uuid)
        }

        removeAuthed(uuid = uuid)
        Logger.d(TAG, "deleteDeviceCache() >>> [$uuid] pid[${device.pid}]")
    }

    @AnyThread
    suspend fun getOneAuthedDeviceList(ctx: Context): List<DeviceCache>? =
        withContext(DISPATCHER_IO) {
            DB.getAllDeviceCache(context = ctx)?.filter { device ->
                device.hadAuthed && EnumProductLine.ONE.ordinal == device.productLine
            }
        }

    @AnyThread
    private suspend fun getDeviceList(ctx: Context, epl: EnumProductLine): List<DeviceCache>? =
        withContext(DISPATCHER_IO) {
            DB.getAllDeviceCache(context = ctx)?.filter { device ->
                epl.ordinal == device.productLine
            }
        }

    fun OneDevice.getMembersDevice(): List<Device> {
        val deviceUUID = this.UUID?.lowercase() ?: return emptyList()

        val ret = LinkedList<Device>()
        OfflineDeviceHandler.get().getGroupParameter(deviceUUID)?.members?.forEach { member ->
            val device = DeviceStore.findOne(member.id) // TODO: it.id can be used as UUID?
                ?: OneDevice(offlineDummy = member.toDeviceItem())

            ret.add(device)
        }
        return ret
    }

    private fun Members.toDeviceItem(): DeviceItem {
        val member = this

        return DeviceItem().apply {
            this.Name = member.device_name
            this.serialNum = member.serial_number
            this.Version = member.one_os_ver
            this.devStatus.release = member.release_date // TODO: is it release date?
            this.devStatus.firmware = member.version
            this.devStatus.mac = StringBuilder().append(member.id.substring(0, 2)).append(":")
                .append(member.id.substring(2, 4)).append(":")
                .append(member.id.substring(4, 6)).append(":")
                .append(member.id.substring(6, 8)).append(":")
                .append(member.id.substring(8, 10)).append(":")
                .append(member.id.substring(10, 12)).toString()
        }
    }

    @WorkerThread
    suspend fun getOfflineDevices(): List<Device> {
        val ret = mutableListOf<Device>()

        restoreOneDevices(ret = ret)
        restorePartyBoxDevices(ret = ret)
        restorePartyBandDevices(ret = ret)

        return ret
    }


    @WorkerThread
    suspend fun getOfflineDeviceCache(context: Context, uuid: String?): DeviceCache? {
        if (uuid.isNullOrBlank()) {
            return null
        }

        return DB.getAllDeviceCache(context = context)?.firstOrNull { cache ->
            cache.uuid == uuid
        }
    }

    @WorkerThread
    suspend fun getOfflineDevice(context: Context, uuid: String?): Device? {
        val cache = getOfflineDeviceCache(context = context, uuid = uuid) ?: return null
        val diagnosedDevices: List<DeviceCache>? = loadDiagnosedDeviceList(ctx = WAApplication.me)

        return when (cache.productLine) {
            EnumProductLine.ONE.ordinal -> cache.mapOneDevice(diagnosedDevices = diagnosedDevices)
            EnumProductLine.PARTY_BOX.ordinal -> cache.mapPartyBoxDevice()
            EnumProductLine.BAND_BOX.ordinal -> PartyBandDevice(null, cache.mapPartyBandBTDevice())
            else -> null
        }
    }

    @WorkerThread
    private fun restorePartyBandDevices(ret: MutableList<Device>) {
        _sMemoryDevicesCache.filter { it.value.productLine == EnumProductLine.BAND_BOX.ordinal }.forEach {
            ret.addIfAbsent(PartyBandDevice(null, it.value.mapPartyBandBTDevice()))
        }
    }

    @WorkerThread
    private suspend fun restorePartyBoxDevices(ret: MutableList<Device>) {
        val partyBoxDevices = getDeviceList(ctx = WAApplication.me, EnumProductLine.PARTY_BOX)

        partyBoxDevices?.forEach { cache ->
            if (!AppConfigurationUtils.isSupportedDevice(cache.pid)) {
                return@forEach
            }

            ret.addIfAbsent(cache.mapPartyBoxDevice())
        }
    }

    /**
     * see [PartyBandDevice.updateCache]
     */
    fun DeviceCache.mapPartyBandBTDevice(): PartyBandBTDevice {
        return PartyBandBTDevice(
            macAddressCrc = uuid.takeLast(4),
            firmwareVersion = firmwareVer ?: "",
            batteryInfo = BatteryInfo(),
            partyMethodInfo = PartyMethodInfo(),
            partyInfo = PartyInfo(),
            miscInfo = DeviceMiscInfo(),
            pid = pid ?: "",
            colorId = cid ?: "",
            vendorId = "",
            deviceName = deviceName ?: "",
            bleAddress = "",
            serialNumber = serialNumber,
        )
    }

    fun DeviceCache.mapPartyBoxBTDevice(): PartyBoxBTDevice {
        val cache = this

        val dummyBTDevice = PartyBoxBTDevice.Builder(
            vendorID = PartyBoxBLEAdvParser.vendorID,
            macAddress = cache.wlan0Mac,
            pid = cache.pid,
            colorID = cache.cid,
            role = cache.groupRole,
            groupId = cache.groupId,
            deviceName = cache.deviceName,
        ).build()

        dummyBTDevice.firmwareVersion = cache.firmwareVer
        dummyBTDevice.serialNumber = cache.serialNumber
        dummyBTDevice.playbackDuration = cache.playbackDuration

        return dummyBTDevice
    }

    private fun DeviceCache.mapPartyBoxDevice(): PartyBoxDevice {
        val cache = this
        val dummyBTDevice = cache.mapPartyBoxBTDevice()

        val dummyDevice = PartyBoxDevice(offlineDummy = dummyBTDevice)
        dummyDevice.customerServiceReportId = cache.diagnosisReportId
        return dummyDevice
    }

    fun DeviceCache.mapDeviceItem(): DeviceItem {
        val cache = this

        return DeviceItem().also { offlineDummy ->
            offlineDummy.uuid = cache.uuid
            offlineDummy.Name = cache.deviceName
            offlineDummy.project = cache.pid?.let { AppConfigurationUtils.getDefaultModelName(it) }
            offlineDummy.serialNum = cache.serialNumber
            offlineDummy.Version = cache.oneOsVer
            offlineDummy.devStatus = (offlineDummy.devStatus ?: DeviceProperty()).also { devStatus ->
                devStatus.firmware = cache.firmwareVer
                devStatus.mac = cache.wlan0Mac
                devStatus.deviceStatus = JSONObject().also { json ->
                    json.put("hm_dev_color", "0x${cache.cid}")
                }.toString()
            }
            offlineDummy.setPlaybackDuration(cache.playbackDuration)
        }
    }

    private fun DeviceCache.mapOneDevice(diagnosedDevices: List<DeviceCache>?): OneDevice {
        val cache = this

        return OneDevice(offlineDummy = cache.mapDeviceItem()).also { oneDevice ->
            diagnosedDevices?.find {
                it.uuid == oneDevice.UUID
            }?.also {
                oneDevice.customerServiceReportId = it.diagnosisReportId
            }
        }
    }

    @WorkerThread
    private suspend fun restoreOneDevices(ret: MutableList<Device>) {
        val diagnosedDevices = loadDiagnosedDeviceList(ctx = WAApplication.me)
        val oneDevices = getOneAuthedDeviceList(ctx = WAApplication.me)

        oneDevices?.forEach { cache ->
            if (!AppConfigurationUtils.isSupportedDevice(cache.pid)) {
                return@forEach
            }

            ret.addIfAbsent(cache.mapOneDevice(diagnosedDevices = diagnosedDevices))
        }

        OfflineDeviceHandler.get().authDeviceList?.forEach { authDev ->
            val pid = AppConfigurationUtils.getPidByModelName(authDev.modelName)
            if (!AppConfigurationUtils.isSupportedDevice(pid)) {
                return@forEach
            }

            val offlineDummy = authDev.wifiDevice ?: return@forEach
            if (!offlineDummy.isValidDummy()) {
                OfflineDeviceHandler.get().deleteDevice(authDev.deviceCrc)
                return@forEach
            }

            OneDevice(offlineDummy = offlineDummy).also { oneDevice ->
                diagnosedDevices?.find {
                    it.uuid == oneDevice.UUID
                }?.also {
                    oneDevice.customerServiceReportId = it.diagnosisReportId
                }
                ret.addIfAbsent(oneDevice)

                /** modify device cache info from [OfflineDeviceHandler] to [DB] */
                upsert(device = oneDevice, ctx = WAApplication.me)
                OfflineDeviceHandler.get().deleteDevice(authDev.deviceCrc)
            }
        }
    }

    /**
     * @link important data binding within [BaseDevice.offlineDummy]
     */
    private fun DeviceItem.isValidDummy(): Boolean {
        val dummy = this
        val uuid = dummy.dummyUUID()

        return !uuid.isNullOrBlank() &&
                !dummy.speakerName.isNullOrBlank() &&
                !dummy.toPid().isNullOrBlank()

    }

    @AnyThread
    suspend fun getAllDeviceCache(context: Context): List<DeviceCache>? =
        withContext(DISPATCHER_IO) {
            DB.getAllDeviceCache(context = context)
        }

    @AnyThread
    suspend fun getTargetDeviceCache(context: Context, uuid: String): DeviceCache? =
        withContext(DISPATCHER_IO) {
            DB.getTargetDeviceCache(context = context, uuid = uuid)
        }

    private fun OneDevice.ableUpdateAuth(): Boolean {
        val uuid = this.UUID
        if (uuid.isNullOrBlank()) {
            return true
        }

        val lastClearCacheTs = _sLastClearCacheTimestamp[uuid] ?: return true

        return lastClearCacheTs.overThreshold(REMOVE_AUTH_COLD_DOWN_MILLS)
    }

    /**
     * Avoid some invalid cid string like "null"
     */
    private fun String?.validCid(): String? = takeIf {
        "null" != it
    }

    private const val TAG = "LocalCacheAdapter"

    private const val REMOVE_AUTH_COLD_DOWN_MILLS = 30 * 1000L
}