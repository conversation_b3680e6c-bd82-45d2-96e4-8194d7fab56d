package com.harman.oobe.wifi

import android.app.Activity
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import androidx.annotation.AnyThread
import androidx.annotation.IntRange
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.SPUtils
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.command.one.bean.APItem
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.BleGetEncryptionKeyResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.EnumBatteryMode
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumOtaStatus
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.OtaStatus
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.syncGetBatteryStatusWithTimeout
import com.harman.connect.syncGetDeviceInfoWithTimeout
import com.harman.getFwCheckRspWithTimeout
import com.harman.oobe.wifi.flow.AuthFlow
import com.harman.oobe.wifi.flow.SelfTuningFlow
import com.harman.oobe.wifi.flow.FirstOTACheckFlow
import com.harman.oobe.wifi.flow.FirstOTACheckFlow.Companion.asBurningPercentage
import com.harman.oobe.wifi.flow.FoundNewProductFlow
import com.harman.oobe.wifi.flow.GoogleCastFlow
import com.harman.oobe.wifi.flow.IOOBEFlow
import com.harman.oobe.wifi.flow.SetupWifiFlow
import com.harman.ota.one.OneOtaActivity
import com.harman.supportBattery
import com.harman.discover.DeviceScanner
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.isActive
import com.harman.discover.util.Tools.isGreaterThan
import com.harman.discover.util.Tools.objectUniID
import com.harman.log.Logger
import com.harman.safeAddSources
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harmanbar.ble.utils.GsonUtil
import com.tencent.mmkv.MMKV
import com.wifiaudio.model.APViewModel
import com.wifiaudio.model.SecurityItem
import com.wifiaudio.utils.WifiResultsUtil
import com.wifiaudio.utils.device.APInfoUtil
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import kotlin.reflect.KClass

/**
 * Created by gerrardzhang on 2024/4/29.
 */
class WiFiOOBEViewModelFactory(
    private val activity: Activity,
    private val mode: EnumMode
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return WiFiOOBEViewModel(
            weakActivity = WeakReference(activity),
            mode = mode
        ) as T
    }
}

class WiFiOOBEViewModel(
    val weakActivity: WeakReference<Activity>,
    val mode: EnumMode
) : ViewModel(), DefaultLifecycleObserver {

    private val _currentFlow = MutableLiveData<IOOBEFlow>()
    val currentFlow: LiveData<IOOBEFlow>
        get() = _currentFlow

    private val _apList = MutableLiveData<List<APItem>>()
    val apList: LiveData<List<APItem>>
        get() = _apList

    @Volatile
    var manualAddedFlag: Boolean = false
        private set

    private val _selectedAPItem = MediatorLiveData<APItem>().also { mediator ->
        mediator.addSource(apList) { apItems ->
            if (manualAddedFlag) { // already had selected APItem
                return@addSource
            }

            val ssidFromPhone = WifiResultsUtil.getWiFiSSID()
            if (ssidFromPhone.isNullOrBlank()) {
                return@addSource
            }

            val matched = apItems.firstOrNull { apItem ->
                apItem.ssidName == ssidFromPhone
            } ?: return@addSource

            manualAddedFlag = true
            mediator.value = matched
            restorePassword(apItem = matched)
        }
    }
    val selectedAPItem: LiveData<APItem>
        get() = _selectedAPItem

    private val _phoneSSID = MutableLiveData<String>()
    val phoneSSID: LiveData<String>
        get() = _phoneSSID

    val customSSID = MutableLiveData<String?>()
    val customSecurity = MutableLiveData<SecurityItem?>()
    val password = MutableLiveData<String?>()

    val otherNetworkContinueEnable = MediatorLiveData<Boolean>().also { mediator ->
        mediator.addSource(customSSID) { ssid ->
            mediator.value = updateOtherNetworkContinueEnable(
                ssid = ssid, security = customSecurity.value, pwd = password.value)
        }

        mediator.addSource(customSecurity) { security ->
            mediator.value = updateOtherNetworkContinueEnable(
                ssid = customSSID.value, security = security, pwd = password.value)
        }

        mediator.addSource(password) { pwd ->
            mediator.value = updateOtherNetworkContinueEnable(
                ssid = customSSID.value, security = customSecurity.value, pwd = pwd)
        }
    }

    private fun updateOtherNetworkContinueEnable(
        ssid: String?,
        security: SecurityItem?,
        pwd: String?
    ): Boolean = when (security?.value) {
        APViewModel.AuthKey.None -> {
            !ssid.isNullOrBlank()
        }
        APViewModel.AuthKey.WEP -> {
            !ssid.isNullOrBlank() && !pwd.isNullOrBlank()
        }
        APViewModel.AuthKey.WPA,
        APViewModel.AuthKey.WPA2,
        APViewModel.AuthKey.WPA3 -> {
            !ssid.isNullOrBlank() && !pwd.isNullOrBlank() && pwd.length >= 8
        }
        else -> false
    }

    val currentAPName = MediatorLiveData<String?>().also { mediator ->
        mediator.safeAddSources(
            _selectedAPItem,
            customSSID,
            _phoneSSID
        ) { apItem, custom, phone ->
            mediator.value = updateAPName(phoneSSID = phone, selectedAPItem = apItem, customSSID = custom)
        }
    }

    val securityName: LiveData<String?> = customSecurity.map { item ->
        item?.value ?: weakActivity.get()?.resources?.getString(R.string.jbl_Security)
    }

    val passLayoutHide: LiveData<Boolean?> = customSecurity.map { item ->
        APViewModel.AuthKey.None.equals(item?.value,true)
    }

    private fun updateAPName(phoneSSID: String?, selectedAPItem: APItem?, customSSID: String?): String? {
        selectedAPItem?.ssidName?.let { ssid ->
            if (ssid.isNotBlank()) {
                return ssid
            }
        }

        if (!customSSID.isNullOrBlank()) {
            return customSSID
        }

        return phoneSSID
    }

    private val _otaStatus = MutableLiveData<OtaStatus>(OtaStatus(status = EnumOtaStatus.UNKNOWN.value))
    val otaStatus: LiveData<EnumOtaStatus> = _otaStatus.map { status ->
        status.enumStatus
    }

    @IntRange(from = 0L, to = 100L)
    val downloadProgress: LiveData<Int> = _otaStatus.map { status ->
        status.progress ?: 0
    }

    val showCloseAllFlag = MMKV.defaultMMKV().decodeBool("SHOW_CLOSE_ALL", "debug" == BuildConfig.BUILD_TYPE && BuildConfig.DEBUG)

    private val _burningProgress = MutableLiveData<Int>()
    val burningProgress: LiveData<Int>
        get() = _burningProgress

    private var installTimer: Job? = null

    // Might experience device offline and online in a short time through OOBE flow.
    // This targetDevice need to be updated if scanner callback with new instance.
    private var targetDevice: OneDevice? = null
        set(value) {
            field?.unregisterDeviceListener(eventsListener)
            value?.registerDeviceListener(eventsListener)
            field = value
        }

    @MainThread
    fun init(dialog: WiFiOOBEDialog, device: OneDevice, mode: EnumMode) {
        Logger.i(TAG, "init() >>> device[${device.UUID}]")
        targetDevice = device
        _otaStatus.value = OtaStatus(status = EnumOtaStatus.UNKNOWN.value)

        _currentFlow.value = when (mode) {
            EnumMode.WIFI_SETUP -> {
                SetupWifiFlow(wifiOOBEDialog = dialog, vm = this@WiFiOOBEViewModel).also { flow ->
                    flow.init()
                }
            }
            EnumMode.FULL -> {
                FoundNewProductFlow(wifiOOBEDialog = dialog, vm = this@WiFiOOBEViewModel).also { flow ->
                    flow.init()
                }
            }
        }

        DeviceScanner.registerObserver(deviceScanner)
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)

        registerNetworkChange(owner = owner)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        targetDevice?.unregisterDeviceListener(eventsListener)
        unregisterNetworkChange(owner = owner)
    }

    @MainThread
    fun onDoubleConfirmResult(quit: Boolean) {
        _currentFlow.value?.onDoubleConfirmResult(quit = quit)
    }

    @MainThread
    fun updateWiFiSSID() {
        val phoneSSID = WifiResultsUtil.getWiFiSSID()
        _phoneSSID.value = phoneSSID
        Logger.d(TAG, "updateWiFiSSID() >>> phoneSSID:$phoneSSID")
    }

    internal fun onLater() {
        _currentFlow.value?.onLater()
    }

    internal fun onConnectBLE() {
        _currentFlow.value?.onConnectBLE()
    }

    @MainThread
    internal fun stopAllBusinesses() {
        Logger.w(TAG, "stopAllBusinesses() >>> ")
        stopLoopGetAPList()
        stopLoopGetOtaStatus()
        DeviceScanner.unregisterObserver(deviceScanner)
    }

    @MainThread
    fun switch(targetKlz: KClass<out IOOBEFlow>, prev: IOOBEFlow) {
        if (prev.dialog !is WiFiOOBEDialog) {
            return
        }

        _currentFlow.value = when (targetKlz) {
            AuthFlow::class -> AuthFlow(wifiOOBEDialog = prev.dialog, vm = prev.vm).also { flow ->
                flow.init(isRetry = false)
            }

            SetupWifiFlow::class -> SetupWifiFlow(wifiOOBEDialog = prev.dialog, vm = prev.vm).also { flow ->
                flow.init()
            }

            FirstOTACheckFlow::class -> FirstOTACheckFlow(dialog = prev.dialog, vm = prev.vm).also { flow ->
                flow.init()
            }

            SelfTuningFlow::class -> SelfTuningFlow(dialog = prev.dialog, vm = prev.vm).also { flow ->
                flow.init()
            }

            GoogleCastFlow::class -> GoogleCastFlow(dialog = prev.dialog, vm = prev.vm)

            else -> {
                return
            }
        }
    }

    private var getAPListLoopJob: Job? = null

    @MainThread
    internal fun startLoopGetAPList() {
        Logger.i(TAG, "startLoopGetAPList() >>> ")
        getAPListLoopJob?.cancel()
        getAPListLoopJob = viewModelScope.launch(DISPATCHER_DEFAULT) {
            while (true) {
                targetDevice?.getAPList()
                delay(5000)
            }
        }
    }

    @MainThread
    internal fun stopLoopGetAPList() {
        Logger.i(TAG, "stopLoopGetAPList() >>> ")
        getAPListLoopJob?.cancel()
        getAPListLoopJob = null
    }

    private var getOtaStatusLoopJob: Job? = null

    @MainThread
    internal fun startLoopGetOtaStatus() {
        if (getOtaStatusLoopJob.isActive()) {
            return
        }

        Logger.i(TAG, "startLoopGetOtaStatus() >>> ")
        getOtaStatusLoopJob?.cancel()
        getOtaStatusLoopJob = viewModelScope.launch(DISPATCHER_DEFAULT) {
            while (true) {
                targetDevice?.getOtaStatus()
                targetDevice?.getDeviceInfo()
                Logger.d(TAG, "startLoopGetOtaStatus() >>> loop")
                delay(5000)
            }
        }
    }

    @MainThread
    internal fun stopLoopGetOtaStatus() {
        Logger.i(TAG, "stopLoopGetOtaStatus() >>> ")
        getOtaStatusLoopJob?.cancel()
        getOtaStatusLoopJob = null
    }

    @MainThread
    internal fun setCurrentAPItem(apItem: APItem?) {
        Logger.d(TAG, "setCurrentAPItem() >>> $apItem")
        manualAddedFlag = true
        _selectedAPItem.value = apItem

        if (null != apItem) {
            restorePassword(apItem = apItem)
        }
    }

    var cacheFeatureSupport: FeatureSupport? = null

    var oobeEncryptionKeyResponse: BleGetEncryptionKeyResponse? = null
    val isSupportOOBEEncryption: Boolean
        get() = true == oobeEncryptionKeyResponse?.key?.isNotBlank()

    val oobeEncryptionKey: String
        get() = oobeEncryptionKeyResponse?.key ?: ""

    private val eventsListener = object : IOneDeviceListener {

        @WorkerThread
        override fun onAPList(apList: List<APItem>?) {
            Logger.d(TAG, "onAPList() >>> ap.size[${apList?.size ?: -1}]")
            val sortedAPList = (apList ?: emptyList()).let { unsorted ->
                unsorted.sortedByDescending { apItem ->
                    apItem.rssi ?: -1
                }
            }

            _apList.postValue(sortedAPList)

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                clearSelectedIfRemoved(aps = sortedAPList)
            }
        }

        @MainThread
        private fun clearSelectedIfRemoved(aps: List<APItem>) {
            if (aps.isEmpty()) {
                // didn't clear selected ap if last ap list is empty to avoid UI flash
                return
            }

            val selected = _selectedAPItem.value ?: return

            if (!aps.contains(selected)) {
                manualAddedFlag = false
                _selectedAPItem.value = null
                password.value = null
            }
        }

        @WorkerThread
        override fun onOtaStatus(status: OtaStatus) {
            handleOtaStatusUpdated(status = status)
        }

        @WorkerThread
        override fun onBatteryStatus(rsp: BatteryStatusResponse) {
            _batteryStatus.postValue(rsp)
        }

        @WorkerThread
        override fun onUpnpOtaStatus(uuid: String, status: OtaStatus) {
            if (uuid != targetDevice?.wifiDevice?.deviceItem?.uuid) {
                return
            }

            handleOtaStatusUpdated(status = status)
        }

        @WorkerThread
        private fun updateOtaProgress(status: OtaStatus) {
            if (!Tools.statusGreaterOrEqual(actual = _otaStatus.value, target = status)) {
                return
            }

            _otaStatus.postValue(status)
        }

        @WorkerThread
        override fun onCalibration(calibration: Calibration) {
            Logger.d(TAG, "onCalibration() >>> $calibration")
        }

        @WorkerThread
        override fun onDeviceInfo(rsp: GetDeviceInfoResponse) {
            handleDeviceInfo(rsp = rsp)
        }

        @WorkerThread
        private fun handleOtaStatusUpdated(status: OtaStatus) {
            Logger.d(TAG, "handleOtaStatusUpdated() >>> [${status.enumStatus.value}] progress[${status.progress}]")
            updateOtaProgress(status = status)

            when (status.status) {
                EnumOtaStatus.NOT_START.value -> {
                    // check whether up to date depends on target version from cloud
                    // and current version from device cmd(getDeviceInfo)
                    viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                        if (isUpToDate()) {
                            checkAndNotifyUpToDate()
                        }
                    }
                }
            }
        }

        @Volatile
        private var lastFwVersion: String? = null

        @WorkerThread
        private fun handleDeviceInfo(rsp: GetDeviceInfoResponse) {
            Logger.d(TAG, "handleDeviceInfo() >>> fw[${rsp.deviceInfo?.firmware}]. baseFw[$lastFwVersion]")
            if (null == lastFwVersion) {
                lastFwVersion = rsp.deviceInfo?.firmware
                Logger.d(TAG, "handleDeviceInfo() >>> mark [$lastFwVersion] as base fw version")
            } else if (true == rsp.deviceInfo?.firmware?.isGreaterThan(lastFwVersion)) {
                Logger.i(TAG, "handleDeviceInfo() >>> fw version changed.")
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    checkAndNotifyUpToDate()
                }
            }
        }

        @MainThread
        private fun checkAndNotifyUpToDate() {
            val currentStatus = _otaStatus.value?.status
            Logger.i(TAG, "handleOtaStatusUpdated() >>> is up to date! currentStatus[$currentStatus]")
            when (currentStatus) {
                EnumOtaStatus.UPDATE_TO_DATE.value,
                EnumOtaStatus.SUCCESS.value,
                EnumOtaStatus.BURNING_FAIL.value,
                EnumOtaStatus.FAIL.value -> {
                    // do nothing cause the UI was in terminal state currently
                }
                else -> {
                    stopLoopGetOtaStatus()
                    _otaStatus.value = OtaStatus(status = EnumOtaStatus.UPDATE_TO_DATE.value)
                }
            }
        }
    }

    private val spDelegate: SPUtils by lazy {
        SPUtils.getInstance(APInfoUtil.AP_INFO_KEY)
    }

    @MainThread
    private fun restorePassword(apItem: APItem) {
        val targetSSID = apItem.ssid
        if (targetSSID.isNullOrBlank()) {
            return
        }

        restorePassword(targetSSID = targetSSID)
    }

    @MainThread
    fun restorePassword(targetSSID: String) {
        if (!password.value.isNullOrBlank()) {
            return
        }

        if (!spDelegate.contains(targetSSID)) {
            return
        }

        val spString = spDelegate.getString(targetSSID)
        if (spString.isNullOrBlank()) {
            return
        }

        val autoSaveBean = GsonUtil.parseJsonToBean(spString, APAutoSaveBean::class.java) ?: return
        val actualSSID = autoSaveBean.apItem?.ssid

        if (!actualSSID.isNullOrBlank() && actualSSID == targetSSID) {
            password.value = autoSaveBean.password
        }
    }

    @MainThread
    fun autoSavePassword() {
        val password = password.value ?: return
        val apItem = _selectedAPItem.value ?: return

        val targetSSID = apItem.ssid
        if (targetSSID.isNullOrBlank()) {
            return
        }

        val bean = APAutoSaveBean(password = password, apItem = apItem)
        Logger.i(TAG, "autoSavePassword() >>> do save pwd of ssid[$targetSSID]")
        spDelegate.put(targetSSID, GsonUtil.parseBeanToJson(bean))
    }

    @MainThread
    fun clearAutoSavePassword() {
        val apItem = _selectedAPItem.value ?: return
        val targetSSID = apItem.ssid
        if (targetSSID.isNullOrBlank()) {
            return
        }

        spDelegate.remove(targetSSID)
    }

    private val _batteryStatus = MutableLiveData<BatteryStatusResponse>()
    val batteryStatus: LiveData<BatteryStatusResponse>
        get() = _batteryStatus

    /**
     * @return whether if battery status is available for current device to handle OTA.
     */
    @MainThread
    fun startBatteryStatusMonitor() {
        if (!targetDevice.supportBattery()) {
            // Available for none battery type of device.
            _batteryStatus.value = BatteryStatusResponse(dcAcMode = EnumBatteryMode.AC.ordinal)
            return
        }

        targetDevice?.batteryStatusExt?.data?.let { batteryStatus ->
            // load battery status from device instance
            _batteryStatus.value = batteryStatus
        }

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            _batteryStatus.value = targetDevice?.syncGetBatteryStatusWithTimeout(logTag = TAG)
        }
    }

    @MainThread
    fun retryFirstOta() {
        Logger.i(TAG, "retryFirstOta() >>> ")
        targetDevice?.requestDeviceOta()
        _otaStatus.value = OtaStatus(status = EnumOtaStatus.UNKNOWN.value)
    }

    private var timeoutCheck: OtaStatusDelegateJob? = null

    /**
     * Delay job which used to check whether [srcStatus] or [progress] changed after [timeoutMills].
     * Auto trigger timeout if that happened.
     *
     * @param isTimeout able do something while timeout happened to determine whether
     * timeout happened or not. return true means regarded as timeout.
     */
    @MainThread
    fun timeoutCheck(
        srcStatus: EnumOtaStatus,
        timeoutMills: Long,
        progress: Int? = null,
        isTimeout: (suspend () -> Boolean)? = null
    ) {
        timeoutCheck?.let { job ->
            if (!job.changed(srcStatus = srcStatus, srcProgress = progress)) {
                // debounce same status events.
                return
            }
        }

        timeoutCheck?.cancel()
        timeoutCheck = OtaStatusDelegateJob(
            delegate = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                delay(timeoutMills)
                if (_otaStatus.value?.enumStatus == srcStatus) {
                    val isTimeoutCheckRst = isTimeout?.invoke() ?: true
                    Logger.w(TAG, "timeoutCheck() >>> timeout and status[${srcStatus.desc}] " +
                            "still didn't change. isTimeoutCheckRst[$isTimeoutCheckRst]")
                    if (isTimeoutCheckRst) {
                        onTimeout()
                    }
                }
            }, status = srcStatus, downloadProgress = progress
        )

        Logger.i(TAG, "timeoutCheck() >>> start monitoring state[${srcStatus.desc}] " +
                "progress[$progress] timeout[${timeoutMills / 1000}]secs")
    }

    @MainThread
    fun cancelTimeoutCheck() {
        Logger.d(TAG, "cancelTimeoutCheck() >>> ")
        timeoutCheck?.cancel()
        timeoutCheck = null
    }

    @MainThread
    private fun onTimeout() {
        Logger.i(TAG, "onTimeout() >>> ")
        stopAllBusinesses()
        _otaStatus.value = OtaStatus(status = EnumOtaStatus.FAIL.value)
    }

    fun setC4aPermissionStatus(status: EnumC4aPermissionStatus) {
        targetDevice?.setC4aPermissionStatus(status = status)
    }

    fun setChromeCastOptIn(optIn: ChromeCastOpt) {
        targetDevice?.setChromeCastOptIn(optIn = optIn)
    }

    private val deviceScanner = object : IHmDeviceObserver {
        @AnyThread
        override fun onDeviceOnlineOrUpdate(device: Device) {
            //Logger.d(TAG, "onDeviceOnlineOrUpdate() >>> uuid[${device.UUID}] WiFi online[${device.isWiFiOnline}]")

            if (device is OneDevice &&
                device.UUID == targetDevice?.UUID &&
                device !== targetDevice) {

                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    Logger.i(
                        TAG, "onDeviceOnlineOrUpdate() >>> [${targetDevice?.UUID}] " +
                            "instance changed from [${targetDevice?.objectUniID()}] to [${device.objectUniID()}]")
                    targetDevice?.clearListeners()
                    targetDevice = device
                }
            }
        }
    }

    @MainThread
    fun startBurningTimer() {
        if (installTimer.isActive()) {
            return
        }

        Logger.i(TAG, "startBurningTimer() >>> exec")
        installTimer = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            var remainMills = FirstOTACheckFlow.BURNING_START_TIMEOUT_MILLS
            _burningProgress.value = 0

            while (remainMills > 0) {
                delay(FirstOTACheckFlow.BURNING_UPDATE_INTERVAL_MILLS)
                remainMills -= FirstOTACheckFlow.BURNING_UPDATE_INTERVAL_MILLS

                val remaining = remainMills.asBurningPercentage()
                Logger.d(TAG, "startBurningTimer() >>> remaining[$remaining]")
                _burningProgress.value = remaining

                if (isOtaSuccessByComparingFwVersion()) {
                    Logger.i(TAG, "startBurningTimer() >>> fw check pass and regard as OTA success")
                    return@launch
                }
            }
        }
    }

    /**
     * Loop check fw version while device burning in case device didn't notify OTA status.
     * [FirstOTACheckFlow.onNextStep] inside it will auto switch to next step if
     * [FirstOTACheckFlow.isOtaSuccessByComparingFwVersion] return true.
     */
    @MainThread
    private suspend fun isOtaSuccessByComparingFwVersion(): Boolean {
        val flow = _currentFlow.value as? FirstOTACheckFlow ?: return false
        return flow.isOtaSuccessByComparingFwVersion()
    }

    @MainThread
    fun cancelBurningTimer() {
        Logger.i(TAG, "cancelBurningTimer() >>> ")
        installTimer?.cancel()
        _burningProgress.value = 0
    }

    @AnyThread
    private suspend fun isUpToDate(): Boolean {
        val targetDevice = targetDevice ?: return false

        val checkFwRsp = withContext(DISPATCHER_API) {
            targetDevice.getFwCheckRspWithTimeout(logTag = TAG)
        } ?: run {
            Logger.w(TAG, "isUpToDate() >>> fail to get remote check fw rsp")
            return false
        }

        val currentVersion = withContext(DISPATCHER_DEFAULT) {
            targetDevice.syncGetDeviceInfoWithTimeout(logTag = TAG)?.firmware
        }
        if (currentVersion.isNullOrBlank()) {
            Logger.w(TAG, "isUpToDate() >>> current version is empty")
            return false
        }

        val targetVersion = checkFwRsp.result?.getOrNull(0)?.version
        Logger.d(TAG, "isUpToDate() >>> target[$targetVersion] current[$currentVersion]")
        val isUpToDate = OneOtaActivity.isUpToDate(
            currentVersion = currentVersion,
            targetVersion = targetVersion
        )

        return isUpToDate
    }

    private fun registerNetworkChange(owner: LifecycleOwner) {
        val ctx = owner as? Context ?: return
        val connectivityManager = ctx.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager ?: return

        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            .build()

        connectivityManager.registerNetworkCallback(networkRequest, networkListener)
    }

    private fun unregisterNetworkChange(owner: LifecycleOwner) {
        val ctx = owner as? Context ?: return
        val connectivityManager = ctx.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager ?: return

        connectivityManager.unregisterNetworkCallback(networkListener)
    }

    private val networkListener = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            currentFlow.value?.onNetworkStatus(available = true)
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                updateWiFiSSID()
            }
        }

        override fun onLosing(network: Network, maxMsToComplete: Int) {
            super.onLosing(network, maxMsToComplete)
            currentFlow.value?.onNetworkStatus(available = false)
        }

        override fun onLost(network: Network) {
            super.onLost(network)
            currentFlow.value?.onNetworkStatus(available = false)
        }

        override fun onUnavailable() {
            super.onUnavailable()
            currentFlow.value?.onNetworkStatus(available = false)
        }
    }

    companion object {
        private const val TAG = "WiFiOOBEViewModel"

        const val OS = "android"
    }
}