package com.harman.oobe.wifi.flow

import android.content.Intent
import android.provider.Settings
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.harman.EventUtils
import com.harman.command.one.bean.APItem
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.WifiSetupEndResponse
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.IConnectReport
import com.harman.connect.disconnectGatt
import com.harman.connect.secureBleGattConnect
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGattSetWifiWithTimeout
import com.harman.connect.syncGetFeatureSupportWithTimeout
import com.harman.connect.syncGetOOBEEncryptionKeyWithTimeout
import com.harman.connect.syncSecureBleGattConnectWithTimeout
import com.harman.discover.DeviceStore
import com.harman.discover.util.Tools.countGapSeconds
import com.harman.log.Logger
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEViewModel
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harmanbar.ble.statistic.HBBLEConnectApResult
import com.harmanbar.ble.utils.HexUtil
import com.harmanbar.ble.utils.RsaEncryptUtil
import com.wifiaudio.model.APViewModel
import com.wifiaudio.model.SecurityItem
import com.wifiaudio.utils.WifiResultsUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by gerrardzhang on 2024/5/6.
 *
 * @param singleStep whether only process current flow, or continue with next flow after current
 * flow success. (@see [checkSSID] and [onSetNetworkSuccess])
 */
class SetupWifiFlow(
    private val wifiOOBEDialog: WiFiOOBEDialog, vm: WiFiOOBEViewModel
) : IOOBEFlow(dialog = wifiOOBEDialog, vm = vm) {

    fun init() {
        updatePreSetupWiFiUI()
        dialog.isPwdVisible.value = null
        vm.updateWiFiSSID() // default ssid
        restorePwd()
    }

    fun showMainUI() {
        vm.startLoopGetAPList()
        dialog.setupWifiUIStatus.value = SetupWifiUIState.MAIN
    }

    /**
     * Seal Gatt and WiFi connect check into a function.
     */
    private fun updatePreSetupWiFiUI() {
        if (!WifiResultsUtil.isCurrentWifiConnected()) {
            Logger.d(TAG, "updatePreSetupWiFiUI() >>> show WiFi setup guide")
            dialog.setupWifiUIStatus.value = SetupWifiUIState.GUIDE
            return
        }

        if (dialog.device.isSecureBleSupport) {
            authDeviceSecureBle()
        } else {
            authDeviceInsecureBle()
        }
    }

    private fun authDeviceSecureBle() {
        val context = vm.weakActivity.get()?: return

        Logger.d(TAG, "authDeviceSecureBle() >>> start Gatt connect on [${dialog.device.UUID}] [${dialog.device.bleAddress}]")
        dialog.setupWifiUIStatus.value = SetupWifiUIState.LOADING

        CoroutineScope(DISPATCHER_DEFAULT).launch {
            dialog.device.secureBleGattConnect(context = context, logTag = TAG, onDisconnected = {
                cancel()
                dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_ERROR_COMMON
                Logger.w(TAG, "authDeviceSecureBle() >>> display network error UI")
            }, onPairFailed = {
                Logger.w(TAG, "authDeviceSecureBle() >>> back to device control screen")
                cancel()
                dialog.device.disconnectGatt()
                <EMAIL>()
            }, onConnected = {
                cancel()
                Logger.i(TAG, "authDeviceSecureBle() >>> PAIRED or CONNECTED")
                showMainUI()
            }, onPairing = {
                Logger.i(TAG, "authDeviceSecureBle() >>> PAIRING")
                dialog.setupWifiUIStatus.value = SetupWifiUIState.READY
            }, iConnectReporter = InnerConnectReporter(connectStartTs = System.currentTimeMillis()))
        }
    }

    private fun authDeviceInsecureBle() {
        if (dialog.device.isGattConnected) {
            Logger.d(TAG, "authDeviceInsecureBle() >>> show main UI directly")
            showMainUI()
            return
        }

        val connectStartTs = System.currentTimeMillis()

        Logger.d(TAG, "authDeviceInsecureBle() >>> start Gatt connect on [${dialog.device.UUID}] [${dialog.device.bleAddress}]")
        dialog.setupWifiUIStatus.value = SetupWifiUIState.LOADING

        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val gattConnectRst = withContext(DISPATCHER_DEFAULT) {
                dialog.device.syncGattConnectWithTimeout(context = vm.weakActivity.get(), repeatTimes = 0)
            }

            Logger.d(TAG, "authDeviceInsecureBle() >>> Gatt connect rst [$gattConnectRst] on [${dialog.device.UUID}] [${dialog.device.bleAddress}]")

            wifiOOBEDialog.reportEventActionOOBE(
                dimensions = mutableMapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_CONNECT_RESULT.value,
                    EventUtils.Dimension.DI_CONNECT_TIME_DURATION to connectStartTs.countGapSeconds(),
                    EventUtils.Dimension.DI_CONNECT_RESULT to if (gattConnectRst) {
                        EventUtils.Dimension.EnumConnectResult.SUCCESS.value
                    } else {
                        EventUtils.Dimension.EnumConnectResult.FAIL.value
                    }
                )
            )

            if (!gattConnectRst) {
                dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_ERROR_COMMON
                Logger.w(TAG, "authDeviceInsecureBle() >>> display network error UI")
                return@launch
            }

            Logger.d(TAG, "authDeviceInsecureBle() >>> connect success, show main UI.")
            showMainUI()
        }
    }

    private fun restorePwd() {
        (dialog as? LifecycleOwner)?.let { owner ->
            vm.phoneSSID.observe(owner) { ssid ->
                if (vm.manualAddedFlag) {
                    return@observe
                }

                vm.restorePassword(targetSSID = HexUtil.str2HexStr(ssid))
            }
        }
    }

    override fun onSwitchAPClick() {
        dialog.hideInputMethod()
        dialog.setupWifiUIStatus.value = SetupWifiUIState.AP_LIST
    }

    override fun onAPItemConnect() {
        val setupStartTs = System.currentTimeMillis()

        dialog.hideInputMethod()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.CONNECT.value
            )
        )

        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            Logger.i(TAG, "onAPItemConnect() >>> dialog.device.isGattConnected ${dialog.device.isGattConnected}")
            dialog.setupWifiUIStatus.value = SetupWifiUIState.LOADING

            Logger.d(TAG, "onAPItemConnect() >>> start connect gatt")
            val gattConnectRet = withContext(DISPATCHER_DEFAULT) {
                if (dialog.device.isSecureBleSupport) {
                    dialog.device.syncSecureBleGattConnectWithTimeout(
                        context = vm.weakActivity.get(),
                        repeatTimes = 2,
                        logTag = TAG
                    ) == EnumConnectionStatus.CONNECTED
                } else {
                    dialog.device.syncGattConnectWithTimeout(
                        context = vm.weakActivity.get(),
                        repeatTimes = 0
                    )
                }
            }

            Logger.d(TAG, "onAPItemConnect() >>> connect gatt ret[$gattConnectRet]")
            if (!gattConnectRet) {
                Logger.e(TAG, "onAPItemConnect() >>> fail to connect gatt")
                onConnectNetworkGeneralError()
                return@launch
            }

            Logger.d(TAG, "onAPItemConnect() >>> start to prepare pwd encrypt key")
            if (!preparePasswordEncryptionKey()) {
                Logger.e(TAG, "onAPItemConnect() >>> fail to prepare pwd encryption key")
                onConnectNetworkGeneralError()
                return@launch
            }

            val req: SetWifiNetworkRequest = assembleRequestByApItem() ?: run {
                Logger.e(TAG, "onAPItemConnect() >>> fail to assemble setup wifi request")
                onConnectNetworkGeneralError()
                return@launch
            }

            Logger.i(TAG, "onAPItemConnect() >>> req:\n$req")
            val rsp = dialog.device.syncGattSetWifiWithTimeout(logTag = TAG, req = req)
            Logger.i(TAG, "onAPItemConnect() >>> rsp:\n$rsp")
            onConnectNetworkResult(rsp = rsp, actualHexSSID = req.ssid, setupStartTs = setupStartTs)
        }
    }

    private fun assembleRequestByApItem(): SetWifiNetworkRequest?  {
        val hexPassword = HexUtil.str2HexStr(vm.password.value ?: "")

        Logger.i(
            TAG,
            "assembleRequestByApItem() >>> supportOOBEEncryption[${vm.isSupportOOBEEncryption}] " +
                    "oobeEncryptionKey[${vm.oobeEncryptionKey}] " +
                    "password[${vm.password.value}] " +
                    "hexPassword[$hexPassword] " +
                    "manualAddedFlag[${vm.manualAddedFlag}]"
        )

        if (vm.manualAddedFlag) {
            val apItem = vm.selectedAPItem.value ?: run {
                Logger.e(TAG, "assembleRequestByApItem() >>> missing selected AP item")
                return null
            }

            return buildSetWifiNetworkRequest(apItem = apItem, hexPassword = hexPassword)
        }

        val ssid = HexUtil.str2HexStr(vm.phoneSSID.value)
        if (ssid.isNullOrBlank()) {
            Logger.e(TAG, "assembleRequestByApItem() >>> missing ssid based on phone")
            return null
        }

        return buildSetWifiNetworkRequest(ssid = ssid, hexPassword = hexPassword)
    }

    private fun buildSetWifiNetworkRequest(apItem: APItem, hexPassword: String) =
        SetWifiNetworkRequest(
            ssid = WifiResultsUtil.trimQuotation(apItem.ssid),
            pass = if (vm.isSupportOOBEEncryption) {
                RsaEncryptUtil.getOOBEEncryptedString(vm.oobeEncryptionKey, vm.password.value?:"")
            } else { hexPassword },
            password = if (vm.isSupportOOBEEncryption) { null } else { hexPassword },
            auth = if (vm.isSupportOOBEEncryption) { null } else { apItem.auth },
            encry = if (vm.isSupportOOBEEncryption) { null } else { apItem.encryption },
            identify = if (vm.isSupportOOBEEncryption) { "" } else { null },
            os = WiFiOOBEViewModel.OS,
            oobeEncryption = if (vm.isSupportOOBEEncryption) { vm.isSupportOOBEEncryption } else { null }
        )

    private fun buildSetWifiNetworkRequest(ssid: String, hexPassword: String) =
        SetWifiNetworkRequest(
            ssid = WifiResultsUtil.trimQuotation(ssid),
            pass = if (vm.isSupportOOBEEncryption) {
                RsaEncryptUtil.getOOBEEncryptedString(vm.oobeEncryptionKey, vm.password.value?:"")
            } else { hexPassword },
            password = if (vm.isSupportOOBEEncryption) { null } else { hexPassword },
            auth = null,
            encry = "",
            identify = "",
            os = WiFiOOBEViewModel.OS,
            oobeEncryption = if (vm.isSupportOOBEEncryption) { vm.isSupportOOBEEncryption } else { null }
        )

    override fun onOtherNetworkConnect() {
        vm.setCurrentAPItem(null) // clear selected ap item to make [vm.currentAPName] display custom SSID
        val setupStartTs = System.currentTimeMillis()

        dialog.hideInputMethod()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.CONTINUE.value
            )
        )

        val ssid = vm.customSSID.value
        if (ssid.isNullOrBlank()) {
            Logger.e(TAG, "onOtherNetworkConnect() >>> ssid is empty")
            onConnectNetworkGeneralError()
            return
        }

        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            Logger.d(TAG, "onOtherNetworkConnect() >>> launch flow")
            dialog.setupWifiUIStatus.value = SetupWifiUIState.LOADING

            Logger.d(TAG, "onOtherNetworkConnect() >>> start connect gatt")
            val gattConnectRet = withContext(DISPATCHER_DEFAULT) {
                dialog.device.syncGattConnectWithTimeout(context = vm.weakActivity.get(), repeatTimes = 0)
            }

            Logger.d(TAG, "onOtherNetworkConnect() >>> connect gatt ret[$gattConnectRet]")
            if (!gattConnectRet) {
                Logger.e(TAG, "onOtherNetworkConnect() >>> fail to connect gatt")
                onConnectNetworkGeneralError()
                return@launch
            }

            Logger.d(TAG, "onOtherNetworkConnect() >>> start to prepare pwd encrypt key")
            if (!preparePasswordEncryptionKey()) {
                Logger.w(TAG, "onOtherNetworkConnect() >>> fail to prepare password encrypt key")
                onConnectNetworkGeneralError()
                return@launch
            }

            val hexSSID = HexUtil.byte2HexStr(ssid.toByteArray())
            val req = assembleRequestByOtherNetwork(hexSSID = hexSSID)

            Logger.i(TAG, "onOtherNetworkConnect() >>> req:\n$req")
            val rsp = dialog.device.syncGattSetWifiWithTimeout(logTag = TAG, req = req)

            Logger.i(TAG, "onOtherNetworkConnect() >>> rsp:\n$rsp")
            onConnectNetworkResult(rsp = rsp, actualHexSSID = hexSSID, setupStartTs = setupStartTs)
        }
    }

    private fun assembleRequestByOtherNetwork(hexSSID: String): SetWifiNetworkRequest {
        val security =  vm.customSecurity.value
        val hexPassword = HexUtil.str2HexStr( vm.password.value ?: "")

        return SetWifiNetworkRequest(
            ssid = WifiResultsUtil.trimQuotation(hexSSID),
            pass = if (vm.isSupportOOBEEncryption) {
                RsaEncryptUtil.getOOBEEncryptedString(vm.oobeEncryptionKey, vm.password.value?:"")
            } else { hexPassword },
            password = if (vm.isSupportOOBEEncryption) { null } else { hexPassword },
            identify = if (vm.isSupportOOBEEncryption) { "" } else { null },
            oobeEncryption = if (vm.isSupportOOBEEncryption) { vm.isSupportOOBEEncryption } else { null },
            auth = if (vm.isSupportOOBEEncryption) {
                null
            } else if (null != security) {
                APViewModel.AuthKey.authMap[security.value]
            } else {
                null
            },
            encry = "",
            os = WiFiOOBEViewModel.OS
        )
    }

    @MainThread
    private suspend fun preparePasswordEncryptionKey(): Boolean {
        Logger.i(TAG, "preparePasswordEncryptionKey() >>> dialog.device.isGattConnected ${dialog.device.isGattConnected}")
        val result = if (!dialog.device.isGattConnected) {
            withContext(DISPATCHER_DEFAULT) {
                dialog.device.syncGattConnectWithTimeout(context = vm.weakActivity.get(), repeatTimes = 2)
            }
        } else {
            true
        }

        Logger.d(TAG, "preparePasswordEncryptionKey() >>> gatt connect result[$result]")
        if (result) {
            withContext(DISPATCHER_DEFAULT) {
                retrievePasswordEncryptionKeyIfNeed()
            }
        } else {
            Logger.e(TAG, "preparePasswordEncryptionKey() >>> fail to connect gatt")
        }

        return result
    }

    private suspend fun retrievePasswordEncryptionKeyIfNeed() {
        if (null == vm.cacheFeatureSupport) {
            vm.cacheFeatureSupport = dialog.device.syncGetFeatureSupportWithTimeout(logTag = TAG)
        }
        Logger.i(TAG, "retrievePasswordEncryptionKeyIfNeed() >>> OOBEEncryption?.isSupport ${vm.cacheFeatureSupport?.OOBEEncryption?.isSupport}")

        if (true == vm.cacheFeatureSupport?.OOBEEncryption?.isSupport && null == vm.oobeEncryptionKeyResponse) {
            vm.oobeEncryptionKeyResponse = dialog.device.syncGetOOBEEncryptionKeyWithTimeout(logTag = TAG)
        }
        Logger.i(TAG, "retrievePasswordEncryptionKeyIfNeed() >>> vm.oobeEncryptionKeyResponse ${vm.oobeEncryptionKeyResponse}")
    }



    override fun onExit() {
        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.LATER.value
            )
        )

        dialog.hideInputMethod()
        dialog.dismissDialog(isInterrupt = true)
    }

    override fun onClose() {
        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.CLOSE.value
            )
        )

        dialog.hideInputMethod()
        dialog.dismissDialog(isInterrupt = true)
    }

    override fun onOpenSettings() {
        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.CONTINUE.value
            )
        )

        vm.weakActivity.get()?.startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
    }

    private fun delayAndPortalToNextStep() {
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            Logger.d(TAG, "delayAndPortalToNextStep() >>> switch to first OTA flow")
            dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_SUCCESS
            // display full anim
            delay(1500)
            // continue with next flow
            vm.switch(targetKlz = FirstOTACheckFlow::class, prev = this@SetupWifiFlow)
        }
    }

    override fun onBackClick() {
        when (dialog.setupWifiUIStatus.value) {
            SetupWifiUIState.AP_LIST -> {
                dialog.setupWifiUIStatus.value = SetupWifiUIState.MAIN
            }
            SetupWifiUIState.OTHER_NETWORK -> {
                dialog.hideInputMethod()

                // clear custom SSID/pwd/Security
                vm.customSSID.value = null
                vm.password.value = null
                vm.customSecurity.value = null

                dialog.setupWifiUIStatus.value = SetupWifiUIState.AP_LIST
            }
            SetupWifiUIState.SECURITY_LIST -> {
                dialog.setupWifiUIStatus.value = SetupWifiUIState.OTHER_NETWORK
            }
            else -> {
                // no impl
            }
        }
    }

    override fun onAPItemClick(apItem: APItem?) {
        if (null != apItem) {
            if (apItem.ssid != vm.selectedAPItem.value?.ssid &&
                apItem.ssid != vm.customSSID.value) {
                vm.password.value = null
            }

            vm.setCurrentAPItem(apItem)
        }

        dialog.setupWifiUIStatus.value = SetupWifiUIState.MAIN
    }

    override fun onOtherNetworkClick() {
        dialog.isPwdVisible.value = null
        dialog.setupWifiUIStatus.value = SetupWifiUIState.OTHER_NETWORK
    }

    override fun onSecurityClick() {
        dialog.hideInputMethod()
        dialog.setupWifiUIStatus.value = SetupWifiUIState.SECURITY_LIST
    }

    override fun onSecurityItemClick(item: SecurityItem?) {
        if (null != item) {
            vm.customSecurity.value = item
        }

        dialog.setupWifiUIStatus.value = SetupWifiUIState.OTHER_NETWORK
    }

    @MainThread
    private fun onConnectNetworkResult(
        rsp: WifiSetupEndResponse?,
        actualHexSSID: String?,
        setupStartTs: Long
    ) {
        Logger.i(TAG, "onConnectNetworkResult() >>> rsp:$rsp\nssid[$actualHexSSID] setupStartTs[$setupStartTs]")
        if (isTargetDeviceWiFiOnline()) {
            Logger.i(TAG, "onConnectNetworkResult() >>> target device already WiFi online")
            onSetNetworkSuccess(
                // both used for check phone is connected to the same network with speaker
                actualHexSSID = actualHexSSID,
                wlan0Mac = actualHexSSID, // DUT must be in same AP env with phone if it's WiFi found.
                setupStartTs = setupStartTs
            )
            return
        }

        when (rsp?.resultCode) {
            HBBLEConnectApResult.CONNECT_AP_SUCCESS.ordinal -> {
                onSetNetworkSuccess(
                    // both used for check phone is connected to the same network with speaker
                    actualHexSSID = actualHexSSID,
                    wlan0Mac = rsp.mac,
                    setupStartTs = setupStartTs
                )
            }
            HBBLEConnectApResult.CONNECT_AP_PASSWORD_ERROR.ordinal -> {
                vm.clearAutoSavePassword()
                vm.stopLoopGetAPList()
                dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_ERROR_PWD
            }
            else -> {
                onConnectNetworkGeneralError()
            }
        }
    }

    private fun isTargetDeviceWiFiOnline(): Boolean {
        val uuid = dialog.device.UUID
        if (uuid.isNullOrBlank()) {
            Logger.w(TAG, "isTargetDeviceWiFiOnline() >>> empty uuid")
            return false
        }

        val targetDevice = DeviceStore.findOne(uuid = uuid) ?: run {
            Logger.w(TAG, "isTargetDeviceWiFiOnline() >>> target[$uuid] cant be scanned")
            return false
        }

        Logger.i(TAG, "isTargetDeviceWiFiOnline() >>> target[$uuid] is WiFi online[${targetDevice.isWiFiOnline}]")
        return targetDevice.isWiFiOnline
    }

    @MainThread
    private fun onConnectNetworkGeneralError() {
        Logger.d(TAG, "onConnectNetworkGeneralError() >>> ")
        vm.clearAutoSavePassword()
        vm.stopLoopGetAPList()
        dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_ERROR_COMMON
    }

    override fun onRetry() {
        Logger.i(TAG, "onRetry() >>> ")
        init()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.TRY_AGAIN.value
            )
        )
    }

    override fun onGetHelpClick() {
        dialog.portalHelpLink()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.GET_HELP.value
            )
        )
    }

    private var actualHexSSID: String? = null
    private var wlan0Mac: String? = null

    @MainThread
    private fun onSetNetworkSuccess(actualHexSSID: String?, wlan0Mac: String?, setupStartTs: Long) {
        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_SUCCESS.value,
                EventUtils.Dimension.DI_WIFI_SETUP_TIME_DURATION to setupStartTs.countGapSeconds()
            )
        )

        dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_SUCCESS
        dialog.onSetupWiFiSuccess()

        dialog.device.inOobe.set(false)
        vm.stopLoopGetAPList()
        vm.autoSavePassword()

        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            delay(1500) // display full anim

            if (sameAPEnv(actualHexSSID = actualHexSSID, wlan0Mac = wlan0Mac)) {
                Logger.d(TAG, "onSetNetworkSuccess() >>> switch to first OTA flow")
                // continue with next flow
                vm.switch(targetKlz = FirstOTACheckFlow::class, prev = this@SetupWifiFlow)
            } else {
                Logger.w(TAG, "onSetNetworkSuccess() >>> not the same AP")
                <EMAIL> = actualHexSSID
                this@SetupWifiFlow.wlan0Mac = wlan0Mac
                dialog.setupWifiUIStatus.value = SetupWifiUIState.SET_NETWORK_SUCCESS_CONFIRM
            }
        }
    }

    override fun done() {
        dialog.setupWifiUIStatus.value = SetupWifiUIState.DIFFERENT_SSID
        checkSSID()
    }

    override fun onChangeNetworkClick() {
        dialog.portalSettingsPage()
    }

    override fun onNotNowClick() {
        dialog.dismissDialog(isInterrupt = true)
    }

    override fun onDoubleConfirmResult(quit: Boolean) {
        if (quit) {
            dialog.dismissDialog(isInterrupt = true)
        }
    }

    override fun onLater() {
        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_WIFI_SETUP.value,
                EventUtils.Dimension.DI_WIFI_SETUP_CONNECT_NOTE to (SetupWifiUIState.GUIDE == dialog.setupWifiUIStatus.value).toString(),
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.LATER.value
            )
        )

        dialog.showDoubleConfirmDialog()
    }

    private fun checkSSID() {
        if (sameAPEnv(actualHexSSID = actualHexSSID, wlan0Mac = wlan0Mac)) {
            // continue with next flow
            vm.switch(targetKlz = FirstOTACheckFlow::class, prev = this@SetupWifiFlow)
        }
    }

    @AnyThread
    override fun onNetworkStatus(available: Boolean) {
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val currentUI = dialog.setupWifiUIStatus.value
            Logger.d(TAG, "onNetworkStatus() >>> currentUI[$currentUI] available[$available]")
            when (currentUI) {
                SetupWifiUIState.GUIDE -> {
                    updatePreSetupWiFiUI()
                }
                SetupWifiUIState.DIFFERENT_SSID -> {
                    checkSSID()
                }
                SetupWifiUIState.MAIN -> {
                    if (sameAPEnv(actualHexSSID = actualHexSSID, wlan0Mac = wlan0Mac)) {
                        Logger.d(TAG, "onNetworkStatus() >>> same App connected")
                        delayAndPortalToNextStep()
                    }
                }
                else -> Unit
            }
        }
    }

    companion object {
        private const val TAG = "SetupWifiFlow"
    }
}

enum class SetupWifiUIState {
    MAIN,
    GUIDE,
    READY,
    AP_LIST,
    OTHER_NETWORK,
    SECURITY_LIST,
    LOADING,
    SET_NETWORK_SUCCESS,
    SET_NETWORK_ERROR_PWD,
    SET_NETWORK_ERROR_COMMON,
    SET_NETWORK_SUCCESS_CONFIRM,
    DIFFERENT_SSID
}