package com.harman.oobe.wifi.flow

import android.content.Context
import androidx.annotation.MainThread
import androidx.lifecycle.viewModelScope
import com.harman.EventUtils
import com.harman.connect.disconnectGatt
import com.harman.connect.secureBleGattConnect
import com.harman.connect.syncGattAuthResultWithTimeout
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.discover.util.Tools.countGapSeconds
import com.harman.log.Logger
import com.harman.oobe.AuthUIState
import com.harman.oobe.wifi.LocalCacheAdapter.markAuthed
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEViewModel
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.wifiaudio.app.WAApplication
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/5/6.
 */
class AuthFlow(
    private val wifiOOBEDialog: WiFiOOBEDialog, vm: WiFiOOBEViewModel
) : IOOBEFlow(dialog = wifiOOBEDialog, vm = vm) {

    private var failTimes = 1

    @MainThread
    fun init(isRetry: Boolean) {
        Logger.d(TAG, "auth() >>> AuthUIState.READY")
        dialog.authUIStatus.value = AuthUIState.READY
        dialog.device.inOobe.set(true)
        auth(vm.weakActivity.get(), isRetry = isRetry)
    }

    @MainThread
    private fun auth(context: Context?, isRetry: Boolean) {
        Logger.d(TAG, "auth() >>> start")
        if (dialog.device.isSecureBleSupport) {
            authDeviceSecureBle(context)
        } else {
            authDeviceInsecureBle(context = context, isRetry = isRetry)
        }
    }

    private fun authDeviceInsecureBle(context: Context?, isRetry: Boolean) {
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val connectStartTs = System.currentTimeMillis()
            var needReportConnectRst = false

            val connectResult = if (!dialog.device.isGattConnected) {
                Logger.d(TAG, "auth() >>> start gatt connect")
                needReportConnectRst = true
                dialog.device.syncGattConnectWithTimeout(context = context, repeatTimes = 2)
            } else {
                true
            }

            if (needReportConnectRst) {
                wifiOOBEDialog.reportEventActionOOBE(
                    dimensions = mutableMapOf(
                        EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_CONNECT_RESULT.value,
                        EventUtils.Dimension.DI_CONNECT_TIME_DURATION to connectStartTs.countGapSeconds(),
                        EventUtils.Dimension.DI_CONNECT_RESULT to if (connectResult) {
                            EventUtils.Dimension.EnumConnectResult.SUCCESS.value
                        } else {
                            EventUtils.Dimension.EnumConnectResult.FAIL.value
                        }
                    )
                )
            }

            if (!connectResult) {
                Logger.e(TAG, "auth() >>> fail to connect gatt. current fail times[$failTimes]")
                dialog.authUIStatus.value = getCountedFailFlag()
                return@launch
            }

            if (!isRetry) {
                wifiOOBEDialog.reportEventActionOOBE(
                    dimensions = mutableMapOf(
                        EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_AUTH.value,
                        EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.AUTH.value
                    )
                )
            }

            Logger.d(TAG, "auth() >>> start to get auth result")

            val authStartTs = System.currentTimeMillis()
            val result = dialog.device.syncGattAuthResultWithTimeout(logTag = TAG)
            Logger.d(TAG, "auth() >>> result: $result")

            wifiOOBEDialog.reportEventActionOOBE(
                dimensions = mutableMapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_AUTH_RESULT.value,
                    EventUtils.Dimension.DI_AUTH_TIME_DURATION to authStartTs.countGapSeconds(),
                    EventUtils.Dimension.DI_AUTH_RESULT to if (result) {
                        EventUtils.Dimension.EnumAuthResult.SUCCESS.value
                    } else {
                        EventUtils.Dimension.EnumAuthResult.FAIL.value
                    }
                )
            )

            vm.viewModelScope.launch(DISPATCHER_DEFAULT) {
                // write cache as authed.
                dialog.device.markAuthed(ctx = WAApplication.me)
            }

            dialog.authUIStatus.value = if (result) {
                Logger.i(TAG, "auth() >>> auth suc")
                countDown()
                AuthUIState.SUCCESS
            } else {
                dialog.device.disconnectGatt()
                Logger.e(TAG, "auth() >>> auth fail. current fail times[$failTimes]")
                getCountedFailFlag()
            }
        }
    }

    private fun authDeviceSecureBle(context: Context?) {
        context?: run {
            return
        }

        vm.viewModelScope.launch(DISPATCHER_DEFAULT) {
            dialog.device.secureBleGattConnect(context = context, logTag = TAG, onDisconnected = {
                cancel()
                dialog.device.disconnectGatt()
                Logger.w(TAG, "authDeviceSecureBle() >>> auth fail. current fail times[$failTimes]")
                getCountedFailFlag()
            }, onPairFailed = {
                cancel()
                Logger.w(TAG, "authDeviceSecureBle() >>> auth PAIRED_FAILED")
                dialog.hideInputMethod()
                dialog.dismissDialog(isInterrupt = true)
            }, onConnected = {
                cancel()
                Logger.i(TAG, "authDeviceSecureBle() >>> auth suc after PAIRED or CONNECTED")
                vm.viewModelScope.launch(DISPATCHER_DEFAULT) {
                    dialog.device.markAuthed(ctx = WAApplication.me)
                }
                countDown()
                dialog.authUIStatus.value = AuthUIState.SUCCESS
            }, onPairing = {
                Logger.w(TAG, "authDeviceSecureBle() >>> auth PAIRING")
                dialog.authUIStatus.value = AuthUIState.READY
            }, iConnectReporter = InnerConnectReporter(connectStartTs = System.currentTimeMillis()))
        }

        Logger.i(TAG, "authDeviceSecureBle() >>> end")
    }

    private fun getCountedFailFlag(): AuthUIState {
        return if (failTimes++ <= 3) {
            AuthUIState.FAIL_LESS_THAN_THREE_TIMES
        } else {
            AuthUIState.FAIL_MORE_THAN_THREE_TIMES
        }
    }

    private fun countDown() {
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            delay(2000L)
            vm.switch(SetupWifiFlow::class, prev = this@AuthFlow)
        }
    }

    @MainThread
    override fun onExit() {
        dialog.showDoubleConfirmDialog()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_AUTH.value,
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.EXIT.value
            )
        )
    }

    @MainThread
    override fun onRetry() {
        init(isRetry = true)

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_AUTH.value,
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.RETRY.value
            )
        )
    }

    @MainThread
    override fun onDoubleConfirmResult(quit: Boolean) {
        if (quit) {
            dialog.dismissDialog(isInterrupt = true)

            // cancel without confirm result
            GlobalScope.launch(DISPATCHER_DEFAULT) {
                dialog.device.cancelAuth()
            }
        }
    }

    companion object {
        private const val TAG = "AuthFlow"
    }
}

