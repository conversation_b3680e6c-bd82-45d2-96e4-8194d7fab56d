package com.harman.oobe.wifi.flow

import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import com.harman.EventUtils
import com.harman.command.one.bean.APItem
import com.harman.connect.IConnectReport
import com.harman.discover.util.Tools.countGapSeconds
import com.harman.oobe.wifi.IOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEViewModel
import com.harman.log.Logger
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harmanbar.ble.utils.HexUtil
import com.wifiaudio.model.SecurityItem
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.WifiResultsUtil

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/4/29.
 *
 * Each concretized flow represents a state in a full OOBE process.
 */
sealed class IOOBEFlow(
    val dialog: IOOBEDialog,
    val vm: WiFiOOBEViewModel
) {

    @MainThread
    open fun onLater() {}

    @MainThread
    open fun onConnectBLE() {}

    @MainThread
    open fun onDoubleConfirmResult(quit: Boolean) {}

    @MainThread
    open fun onExit() {}

    @MainThread
    open fun onClose() {}

    @MainThread
    open fun onRetry() {}

    @MainThread
    open fun onOpenSettings() {}

    @MainThread
    open fun onSwitchAPClick() {}

    @MainThread
    open fun onBackClick() {}

    @MainThread
    open fun onAPItemClick(apItem: APItem?) {}

    @MainThread
    open fun onOtherNetworkClick() {}

    @MainThread
    open fun onSecurityClick() {}

    @MainThread
    open fun onSecurityItemClick(item: SecurityItem?) {}

    @MainThread
    open fun onAPItemConnect() {}

    @MainThread
    open fun onOtherNetworkConnect() {}

    @MainThread
    open fun onGetHelpClick() {}

    @MainThread
    open fun done() {}

    @MainThread
    open fun onChangeNetworkClick() {}

    @MainThread
    open fun onNotNowClick() {}

    @MainThread
    open fun onTuningBtnClick(state: SelfTuningUIState?) {}

    @AnyThread
    open fun onNetworkStatus(available: Boolean) {}

    protected fun sameAPEnv(actualHexSSID: String?, wlan0Mac: String?): Boolean {
        val phoneSSID = WifiResultsUtil.getWiFiSSID() // SSID name without Hex
        val phoneHexSSID = HexUtil.byte2HexStr(phoneSSID?.toByteArray())
        val foundByMac = null != WAUpnpDeviceManager.me().getDeviceItemByMac(wlan0Mac)

        Logger.i(
            TAG, "sameAPEnv() >>> actual[$actualHexSSID] " +
                "target[$phoneSSID].hex[$phoneHexSSID] " +
                "mac[$wlan0Mac].found[$foundByMac]")

        return actualHexSSID.equals(phoneHexSSID, true) ||
                foundByMac
    }

    inner class InnerConnectReporter(
        private val connectStartTs: Long
    ) : IConnectReport {

        override fun onConnectResultReport(result: Boolean) {
            reportSecureConnectResult(connectStartTs = connectStartTs, success = result)
        }

        override fun onStartAuthReport() {
            (dialog as? WiFiOOBEDialog)?.reportEventActionOOBE(
                dimensions = mutableMapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_AUTH.value,
                    EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.AUTH.value
                )
            )
        }

        override fun onAuthResultReport(result: Boolean, authStartTs: Long) {
            (dialog as? WiFiOOBEDialog)?.reportEventActionOOBE(
                dimensions = mutableMapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_AUTH_RESULT.value,
                    EventUtils.Dimension.DI_AUTH_TIME_DURATION to authStartTs.countGapSeconds(),
                    EventUtils.Dimension.DI_AUTH_RESULT to if (result) {
                        EventUtils.Dimension.EnumAuthResult.SUCCESS.value
                    } else {
                        EventUtils.Dimension.EnumAuthResult.FAIL.value
                    }
                )
            )
        }

        private fun reportSecureConnectResult(connectStartTs: Long, success: Boolean) {
            (dialog as? WiFiOOBEDialog)?.reportEventActionOOBE(
                dimensions = mutableMapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_CONNECT_RESULT.value,
                    EventUtils.Dimension.DI_CONNECT_TIME_DURATION to connectStartTs.countGapSeconds(),
                    EventUtils.Dimension.DI_CONNECT_RESULT to success.toString()
                )
            )
        }
    }

    companion object {
        private const val TAG = "IOOBEFlow"
    }
}