package com.harman.oobe.wifi.flow

import androidx.lifecycle.viewModelScope
import com.harman.ableEnterGoogleCastFlow
import com.harman.oobe.wifi.IOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEViewModel
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/5/14.
 */
class SelfTuningFlow(
    dialog: IOOBEDialog,
    vm: WiFiOOBEViewModel
) : IOOBEFlow(dialog = dialog, vm = vm) {

    fun init() {
        dialog.calibrationUIStatus.value = SelfTuningUIState.READY
        dialog.observe(dialog.calibrationUIStatus) { state ->
            when (state) {
                SelfTuningUIState.DOING -> {
                    Logger.d(TAG, "init() >>> set calibration and get state")
                    dialog.device.setCalibration()
                    dialog.device.getCalibrationState()

                    vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                        delay(6 * 1000L)
                        Logger.i(TAG, "init() >>> done calibration by timeout")
                        dialog.calibrationUIStatus.value = SelfTuningUIState.DONE
                    }
                }
                else -> Unit
            }
        }
    }

    override fun onTuningBtnClick(state: SelfTuningUIState?) {
        Logger.d(TAG, "onTuningBtnClick() >>> state[$state]")

        when (state) {
            SelfTuningUIState.READY -> {
                dialog.calibrationUIStatus.value = SelfTuningUIState.DOING
            }
            SelfTuningUIState.DONE -> {
                onNextStep()
            }
            else -> Unit
        }
    }

    private fun onNextStep() {
        if (!dialog.device.ableEnterGoogleCastFlow(tag = TAG)) {
            dialog.dismissDialog(isInterrupt = false)
            return
        }

        Logger.d(TAG, "onNextStep() >>> portal google cast flow")
        vm.switch(targetKlz = GoogleCastFlow::class, prev = this@SelfTuningFlow)
    }

    companion object {
        private const val TAG = "SelfTuningFlow"
    }
}

enum class SelfTuningUIState {
    READY,
    DOING,
    DONE
}
