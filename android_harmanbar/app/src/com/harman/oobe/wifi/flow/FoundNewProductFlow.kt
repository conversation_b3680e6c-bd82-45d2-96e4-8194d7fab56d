package com.harman.oobe.wifi.flow

import android.content.Context
import androidx.annotation.MainThread
import androidx.lifecycle.viewModelScope
import com.harman.EventUtils
import com.harman.connect.secureBleGattConnect
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetFeatureSupportWithTimeout
import com.harman.connect.syncGetOOBEEncryptionKeyWithTimeout
import com.harman.discover.util.Tools.countGapSeconds
import com.harman.log.Logger
import com.harman.oobe.FoundNewProductUIState
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEViewModel
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/5/6.
 */
class FoundNewProductFlow(
    private val wifiOOBEDialog: WiFiOOBEDialog, vm: WiFiOOBEViewModel
) : IOOBEFlow(dialog = wifiOOBEDialog, vm = vm) {

    @MainThread
    fun init() {
        Logger.d(TAG, "init() >>> ")
        dialog.foundNewProductUIStatus.value = FoundNewProductUIState.FOUND_NEW_PRODUCT
    }

    @MainThread
    override fun onConnectBLE() {
        Logger.d(TAG, "onConnectBLE() >>> ")
        execConnectBLE()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_CONNECTION.value,
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.CONNECT.value
            )
        )
    }

    @MainThread
    override fun onLater() {
        Logger.d(TAG, "onLater() >>> ")
        dialog.showDoubleConfirmDialog()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_CONNECTION.value,
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.LATER.value
            )
        )
    }

    @MainThread
    override fun onDoubleConfirmResult(quit: Boolean) {
        if (quit) {
            dialog.dismissDialog(isInterrupt = true)
        }
    }

    @MainThread
    override fun onExit() {
        Logger.d(TAG, "onExit() >>>")
        dialog.dismissDialog(isInterrupt = true)

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_CONNECTION.value,
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.EXIT.value
            )
        )
    }

    @MainThread
    override fun onRetry() {
        execConnectBLE()

        wifiOOBEDialog.reportEventActionOOBE(
            dimensions = mutableMapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_CONNECTION.value,
                EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumOOBEActionItem.RETRY.value
            )
        )
    }

    @MainThread
    private fun execConnectBLE() {
        dialog.foundNewProductUIStatus.value = FoundNewProductUIState.CONNECT
        val context = vm.weakActivity.get()
        connect(context = context)
    }

    @MainThread
    private fun connect(context: Context?) {
        context ?: run {
            Logger.e(TAG, "connect() >>> missing context")
            dialog.foundNewProductUIStatus.value = FoundNewProductUIState.CONNECT_FAIL
            return
        }

        if (dialog.device.isSecureBleSupport) {
            doConnectSecureBle(context)
        } else {
            doConnectInsecureBle(context)
        }
    }

    private fun doConnectSecureBle(context: Context) {
        Logger.d(TAG, "doConnectSecureBle() >>> start to connect gatt with ${dialog.device.bleDevice?.bleAddress}")

        vm.viewModelScope.launch(DISPATCHER_DEFAULT) {
            dialog.device.secureBleGattConnect(context = context, logTag = TAG, onDisconnected = {
                Logger.w(TAG, "doConnectSecureBle() >>> fail to connect gatt")
                cancel()
                dialog.foundNewProductUIStatus.value = FoundNewProductUIState.CONNECT_FAIL
            }, onPairFailed = {
                Logger.w(TAG, "doConnectSecureBle() >>> auth PAIRED_FAILED")
                cancel()
                dialog.foundNewProductUIStatus.value = FoundNewProductUIState.CONNECT_FAIL
            }, onConnected = {
                Logger.i(TAG, "doConnectSecureBle() >>> PAIRED or CONNECTED")
                cancel()
                vm.viewModelScope.launch(DISPATCHER_DEFAULT) {
                    doSecureBleDevicePaired()
                }
            }, onPairing = {
                Logger.w(TAG, "doConnectSecureBle() >>> auth PAIRING")
                dialog.foundNewProductUIStatus.value = FoundNewProductUIState.READY
            }, iConnectReporter = InnerConnectReporter(connectStartTs = System.currentTimeMillis()))
        }
        Logger.d(TAG, "doConnectSecureBle() >>> end")
    }

    private suspend fun doSecureBleDevicePaired() {
        Logger.i(TAG, "doSecureBleDevicePaired() >>> gatt connect success with ${dialog.device.bleDevice?.bleAddress}")
        // Request feature support async for self-tuning support flag. This is an async process.
        vm.cacheFeatureSupport = dialog.device.syncGetFeatureSupportWithTimeout(logTag = TAG)
        Logger.i(TAG, "doSecureBleDevicePaired() >>> OOBEEncryption?.isSupport ${vm.cacheFeatureSupport?.OOBEEncryption?.isSupport}")
        if (true == vm.cacheFeatureSupport?.OOBEEncryption?.isSupport) {
            vm.oobeEncryptionKeyResponse = dialog.device.syncGetOOBEEncryptionKeyWithTimeout(logTag = TAG, 6000)
            Logger.i(TAG, "doSecureBleDevicePaired() >>> vm.oobeEncryptionKeyResponse ${vm.oobeEncryptionKeyResponse}")
        }

        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            vm.switch(AuthFlow::class, prev = this@FoundNewProductFlow)
        }
    }

    private fun doConnectInsecureBle(context: Context?) {
        vm.viewModelScope.launch(DISPATCHER_DEFAULT) {
            Logger.d(TAG, "doConnectInsecureBle() >>> start to connect gatt with ${dialog.device.bleDevice?.bleAddress}")
            val connectStartTs = System.currentTimeMillis()
            var needReportConnectRst = false

            val result = if (!dialog.device.isGattConnected) {
                needReportConnectRst = true
                dialog.device.syncGattConnectWithTimeout(context = context, repeatTimes = 2)
            } else {
                true
            }

            if (needReportConnectRst) {
                wifiOOBEDialog.reportEventActionOOBE(
                    dimensions = mutableMapOf(
                        EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.OOBE_DEVICE_CONNECT_RESULT.value,
                        EventUtils.Dimension.DI_CONNECT_TIME_DURATION to connectStartTs.countGapSeconds(),
                        EventUtils.Dimension.DI_CONNECT_RESULT to if (result) {
                            EventUtils.Dimension.EnumConnectResult.SUCCESS.value
                        } else {
                            EventUtils.Dimension.EnumConnectResult.FAIL.value
                        }
                    )
                )
            }

            if (result) {
                Logger.i(TAG, "doConnectInsecureBle() >>> gatt connect success with ${dialog.device.bleDevice?.bleAddress}")
                // Request feature support async for self-tuning support flag. This is an async process.
                vm.cacheFeatureSupport =  dialog.device.syncGetFeatureSupportWithTimeout(logTag = TAG)
                Logger.i(TAG, "doConnectInsecureBle() >>> OOBEEncryption?.isSupport ${vm.cacheFeatureSupport?.OOBEEncryption?.isSupport}")
                if (true == vm.cacheFeatureSupport?.OOBEEncryption?.isSupport) {
                    vm.oobeEncryptionKeyResponse = dialog.device.syncGetOOBEEncryptionKeyWithTimeout(logTag = TAG, 6000)
                    Logger.i(TAG, "doConnectInsecureBle() >>> vm.oobeEncryptionKeyResponse ${vm.oobeEncryptionKeyResponse}")
                }
                vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    vm.switch(AuthFlow::class, prev = this@FoundNewProductFlow)
                }
            } else {
                Logger.e(TAG, "doConnectInsecureBle() >>> fail to connect gatt")
                vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    dialog.foundNewProductUIStatus.value = FoundNewProductUIState.CONNECT_FAIL
                }
            }
        }
    }

    companion object {
        private const val TAG = "FoundNewProductFlow"
    }
}

