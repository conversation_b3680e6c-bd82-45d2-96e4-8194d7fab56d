package com.harman.oobe.wifi.flow

import androidx.annotation.MainThread
import androidx.lifecycle.viewModelScope
import com.harman.ableEnterGoogleCastFlow
import com.harman.command.one.bean.EnumOtaStatus
import com.harman.connect.syncGetDeviceInfoWithTimeout
import com.harman.discover.DeviceStore
import com.harman.discover.util.Tools
import com.harman.isOneCommander
import com.harman.isSoundBar
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.IOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEViewModel
import com.harman.log.Logger
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/5/10.
 */
class FirstOTACheckFlow(
    dialog: IOOBEDialog,
    vm: WiFiOOBEViewModel
) : IOOBEFlow(dialog = dialog, vm = vm) {

    private val originFw = dialog.device.firmware

    init {
        Logger.i(TAG, "init() >>> UUID[${dialog.device.UUID}] originFw[$originFw]")
    }

    fun init() {
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            dialog.observe(vm.otaStatus) { status ->
                Logger.d(TAG, "otaStatus >>> [$status]")
                when (status) {
                    EnumOtaStatus.UNKNOWN -> {
                        vm.cancelBurningTimer()
                        vm.startLoopGetOtaStatus()
                        vm.startBatteryStatusMonitor()
                        vm.timeoutCheck(
                            srcStatus = status,
                            timeoutMills = DEFAULT_TIMEOUT_MILLS
                        )
                    }
                    EnumOtaStatus.NOT_START,
                    EnumOtaStatus.CHECKING,
                    EnumOtaStatus.NEW_VERSION -> {
                        vm.timeoutCheck(
                            srcStatus = status,
                            timeoutMills = DEFAULT_TIMEOUT_MILLS
                        )
                    }
                    EnumOtaStatus.DOWNLOADING -> {
                        vm.timeoutCheck(
                            srcStatus = status,
                            timeoutMills = DOWNLOADING_TIMEOUT_MILLS,
                            progress = vm.downloadProgress.value
                        )
                    }
                    EnumOtaStatus.UPDATE_TO_DATE,
                    EnumOtaStatus.SUCCESS -> {
                        onNextStep()
                    }
                    EnumOtaStatus.FAIL,
                    EnumOtaStatus.BURNING_FAIL -> {
                        vm.cancelBurningTimer()
                        vm.cancelTimeoutCheck()
                        vm.stopAllBusinesses()
                    }
                    EnumOtaStatus.BURNING_START -> {
                        vm.startBurningTimer()
                        vm.timeoutCheck(
                            srcStatus = status,
                            timeoutMills = BURNING_START_TIMEOUT_MILLS,
                            isTimeout = { !isOtaSuccessByComparingFwVersion() }
                        )
                    }
                    else -> {
                        // no impl
                    }
                }
            }

            dialog.observe(vm.downloadProgress) { progress ->
                Logger.d(TAG, "otaProgress >>> [$progress]")
                val status = vm.otaStatus.value
                if (status != EnumOtaStatus.DOWNLOADING) { // only concern downloading state.
                    return@observe
                }

                vm.timeoutCheck(
                    srcStatus = EnumOtaStatus.DOWNLOADING,
                    timeoutMills = DOWNLOADING_TIMEOUT_MILLS,
                    progress = progress
                )
            }
        }
    }

    /**
     * There might not be UPNP notify after OTA burning completed.
     * Check fw version positively after burning timeout up.
     * @return true if firmware version changed (regarded as OTA success).
     */
    suspend fun isOtaSuccessByComparingFwVersion(): Boolean {
        val uuid = dialog.device.UUID
        if (uuid.isNullOrBlank()) {
            Logger.w(TAG, "isOtaSuccessByComparingFwVersion() >>> empty uuid")
            return false
        }

        val device = DeviceStore.findOne(uuid = uuid) ?: run {
            Logger.w(TAG, "isOtaSuccessByComparingFwVersion() >>> cannot find device instance by uuid[$uuid]")
            return false
        }

        val currentFw = device.firmware
        if (!currentFw.isNullOrBlank() && 0 != Tools.compareVersion(version1 = originFw, version2 = currentFw)) {
            Logger.i(TAG, "isOtaSuccessByComparingFwVersion() >>> firmware version changed while " +
                    "burning. originFw[$originFw] currentFw[$currentFw]. forward next step")
            onNextStep()
            return true
        }

        val deviceInfoFw = device.syncGetDeviceInfoWithTimeout(logTag = TAG)?.firmware

        if (!deviceInfoFw.isNullOrBlank() && 0 != Tools.compareVersion(version1 = originFw, version2 = deviceInfoFw)) {
            Logger.i(TAG, "isOtaSuccessByComparingFwVersion() >>> firmware version changed while " +
                    "burning. originFw[$originFw] DeviceInfo.Fw[$deviceInfoFw]. " +
                    "forward next step")
            onNextStep()
            return true
        }

        Logger.w(TAG, "isOtaSuccessByComparingFwVersion() >>> firmware version didn't change while " +
                "burning. originFw[$originFw] currentFw[$currentFw] " +
                "DeviceInfo.Fw[$deviceInfoFw]")
        return false
    }

    /**
     * @link [WiFiOOBEDialog.dataBinding] [WiFiOOBEDialog.firstOtaUIStatus]
     */
    @MainThread
    private fun forceSwitchUIToUpToData() {
        Logger.d(TAG, "forceSwitchUIToUpToData() >>> ")
        dialog.firstOtaUIStatus.removeSource(vm.batteryStatus)
        dialog.firstOtaUIStatus.removeSource(vm.otaStatus)
        dialog.firstOtaUIStatus.value = FirstOTAUIState.UP_TO_DATE
    }

    private fun onNextStep() {
        vm.cancelBurningTimer()
        vm.cancelTimeoutCheck()
        vm.stopAllBusinesses()

        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            forceSwitchUIToUpToData() // force switch UI to UP_TO_DATE

            delay(3000) // waiting for anim finished

            val isSoundBar = dialog.device.isSoundBar()
            val supportSelfTuning = supportSelfTuning()
            val isOneCommander = dialog.device.isOneCommander()

            Logger.d(TAG, "onNextStep() >>> isSoundBar[$isSoundBar}] " +
                    "cacheFeatureSupport[${vm.cacheFeatureSupport?.selfTuningCalibration?.isSupport}] " +
                    "deviceFeatureSupport[${dialog.device.featSupportExt?.featSupport?.selfTuningCalibration?.isSupport}] " +
                    "supportSelfTuning[$supportSelfTuning] " +
                    "isOneCommander[$isOneCommander]")

            when {
                isOneCommander -> {
                    Logger.d(TAG, "onNextStep() >>> one commander")
                    // your product is up to date.
                    dialog.icUpToDateVisible.value = false
                }
                !isSoundBar && supportSelfTuning -> {
                    Logger.d(TAG, "onNextStep() >>> start self turning")
                    // Use cache param from device which launch fetching while in ConnectDeviceFlow
                    // portal to self tuning
                    vm.switch(targetKlz = SelfTuningFlow::class, prev = this@FirstOTACheckFlow)
                }
                else -> {
                    Logger.d(TAG, "onNextStep() >>> device dont support calibration or is soundbar type or already been oobe")
                    // your product is up to date.
                    dialog.icUpToDateVisible.value = false
                }
            }
        }
    }

    private fun isTerminal() = EnumMode.WIFI_SETUP == vm.mode ||
            !dialog.device.ableEnterGoogleCastFlow(tag = TAG) ||
            dialog.device.isOneCommander()

    override fun onLater() {
        portalNextFlow()
    }

    override fun onRetry() {
        vm.retryFirstOta()
    }

    override fun done() {
        portalNextFlow()
    }

    private fun portalNextFlow() {
        val isTerminal = isTerminal()
        if (isTerminal) {
            dialog.dismissDialog(isInterrupt = false)
            return
        }

        Logger.d(TAG, "portalNextFlow() >>> portal google cast flow")
        vm.switch(targetKlz = GoogleCastFlow::class, prev = this@FirstOTACheckFlow)
    }

    private fun supportSelfTuning(): Boolean {
        vm.cacheFeatureSupport?.let { feat ->
            return true == feat.selfTuningCalibration?.isSupport
        }

        return true == dialog.device.featSupportExt
            ?.featSupport?.selfTuningCalibration?.isSupport
    }

    companion object {
        const val DEFAULT_TIMEOUT_MILLS = 60 * 1000L
        const val DOWNLOADING_TIMEOUT_MILLS = 2 * 60 * 1000L
        const val BURNING_START_TIMEOUT_MILLS = 5 * 60 * 1000L
        const val BURNING_UPDATE_INTERVAL_MILLS = 20 * 1000L

        fun Long.asBurningPercentage(): Int {
            val remainMills = this

            return (((BURNING_START_TIMEOUT_MILLS - remainMills).toFloat() /
                    BURNING_START_TIMEOUT_MILLS
                    ) * 100).toInt()
        }

        private const val TAG = "FirstOTACheckFlow"
    }
}

enum class FirstOTAUIState {
    LOADING,
    BATTERY_TIPS,
    PERCENTAGE,
    UP_TO_DATE,
    FAIL_WITH_RETRY,
    FAIL_WITHOUT_RETRY
}