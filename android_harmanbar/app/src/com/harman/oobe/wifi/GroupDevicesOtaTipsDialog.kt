package com.harman.oobe.wifi

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.harman.BottomPopUpDialog
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogGroupDevicesOtaTipsDialogBinding
import com.harman.log.Logger
import com.jbl.one.configuration.AppConfigurationUtils

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/22.
 */
class GroupDevicesOtaTipsDialog(
    context: Context,
    private val modelName: String?,
    private val deviceName: String?,
    private val cid: Int?
) : BottomPopUpDialog(context = context) {

    val title: String? = context.getString(R.string.will_be_reconnected_back_to_the_system_when_software_update_is_finished)
        .format(deviceName)

    val demoImgUrl: String? = AppConfigurationUtils.getGroupDevicesOtaTipsRenderPath(
        pid = AppConfigurationUtils.getPidByModelName(modelName ?: "") ?: "",
        colorId = cid?.toString()
    )

    init {
        Logger.d(TAG, "() >>> modelName[$modelName] deviceName[$deviceName] cid[$cid]\n" +
                "title[$title] demoImgUrl:$demoImgUrl")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val binding = DialogGroupDevicesOtaTipsDialogBinding.inflate(layoutInflater)
        binding.dialog = this
        binding.lifecycleOwner = this

        setContentView(binding.root)
    }

    fun onBtnOkClick() {
        dismiss()
    }

    companion object {
        private const val TAG = "GroupDevicesOtaTipsDialog"
    }
}