package com.harman.oobe.wifi

import android.content.DialogInterface.OnDismissListener
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.harman.EventUtils
import com.harman.calibration.CalibrationBaseActivity
import com.harman.calibration.CalibrationGuideDialog
import com.harman.calibration.IAudioCalibrationDialogEvent
import com.harman.command.one.bean.Member
import com.harman.connect.GENERAL_TIMEOUT_MILLS
import com.harman.connect.syncGetGroupDevicesOtaStatusWithTimeout
import com.harman.connect.syncGetGroupInfoWithTimeout
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.isCN
import com.harman.isOneCommander
import com.harman.isShowing
import com.harman.isSoundBar
import com.harman.log.Logger
import com.harman.oobe.IOOBEDialogEventListener
import com.harman.supportCalibration
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.va.VAGuideDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by gerrardzhang on 2025/5/21.
 */
class WiFiOOBEDialogHelper(
    private val logTag: String,
    private val activity: FragmentActivity,
    private val device: OneDevice,
    private val mode: EnumMode,
    private val oobeType: EventUtils.Dimension.EnumOOBEType,
    private val oobeDialogDismissListener: OnDismissListener?,
    private val oobeDialogEventListener: IOOBEDialogEventListener?,
    private val calibrationDialogEvent: IAudioCalibrationDialogEvent?,
    private val vaGuideDialogDismissListener: OnDismissListener?
) : IOOBEDialogEventListener, DefaultLifecycleObserver {

    init {
        activity.lifecycle.addObserver(this)
    }

    private var sOOBEDialog: WiFiOOBEDialog? = null
    private var sVAGuideDialog: VAGuideDialog? = null
    private var sCalibrationGuideDialog: CalibrationGuideDialog? = null
    private var sGroupDevicesOtaTipsDialog: GroupDevicesOtaTipsDialog? = null

    fun isAnyDialogShowing(): Boolean =
        sOOBEDialog.isShowing() ||
                sVAGuideDialog.isShowing() ||
                sCalibrationGuideDialog.isShowing() ||
                sGroupDevicesOtaTipsDialog.isShowing()

    fun dismissAllDialogs() {
        sOOBEDialog?.dismiss()
        sVAGuideDialog?.dismiss()
        sCalibrationGuideDialog?.dismiss()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        owner.lifecycle.removeObserver(this)
        sOOBEDialog?.dismiss()
        sVAGuideDialog?.dismiss()
        sCalibrationGuideDialog?.dismiss()
    }

    fun run(): WiFiOOBEDialogHelper = apply {
        sOOBEDialog = WiFiOOBEDialog(
            activity = activity,
            device = device,
            mode = mode,
            oobeType = oobeType,
            listener = this
        ).also { dialog ->
            dialog.show()
            dialog.setOnDismissListener(oobeDialogDismissListener)
        }
    }

    override fun onDebugCloseAllClick() {
        super.onDebugCloseAllClick()
        dismissAllDialogs()
        oobeDialogEventListener?.onDebugCloseAllClick()
    }

    override fun onOOBEFlowEnd(device: OneDevice, isInterrupt: Boolean) {
        super.onOOBEFlowEnd(device, isInterrupt)
        oobeDialogEventListener?.onOOBEFlowEnd(device = device, isInterrupt = isInterrupt)

        if (isInterrupt) {
            return
        }

        Logger.i(logTag, "onOOBEFlowEnd() >>> device[${device.UUID}]" +
                " pid[${device.pid}]" +
                " isCN[${device.isCN()}]" +
                " isSoundBar[${device.isSoundBar()}]" +
                " supportCalibration[${device.supportCalibration()}]" +
                " supportGoogleVA[${device.deviceSupportGoogleVA()}]" +
                " supportAlexaVA[${device.deviceSupportAlexaVA()}]" +
                " isOneCommander[${device.isOneCommander()}]"
        )

        if (device.isSoundBar() && device.supportCalibration()) {
            Logger.i(logTag, "onOOBEFlowEnd() >>> display Calibration Guide dialog for device[${device.UUID}]")
            showCalibrationGuideDialog()
        } else if (!device.isCN() && device.deviceSupportGoogleVA() && device.deviceSupportAlexaVA()) {
            Logger.i(logTag, "onOOBEFlowEnd() >>> display VA Guide dialog for device[${device.UUID}]")
            showVAGuideDialog(device = device)
        } else if (device.isOneCommander()) {
            Logger.i(logTag, "onOOBEFlowEnd() >>> one commander, start checking group ota status")
            handleGroupOtaStatus()
        }
    }

    override fun onOOBEFlowEnd(device: PartyBoxDevice, isInterrupt: Boolean) {
        super.onOOBEFlowEnd(device, isInterrupt)
        oobeDialogEventListener?.onOOBEFlowEnd(device = device, isInterrupt = isInterrupt)
    }

    override fun onOOBEFlowEnd(device: PartyBandDevice, isInterrupt: Boolean) {
        super.onOOBEFlowEnd(device, isInterrupt)
        oobeDialogEventListener?.onOOBEFlowEnd(device = device, isInterrupt = isInterrupt)
    }

    override fun onSetupWiFiSuccess() {
        super.onSetupWiFiSuccess()
        oobeDialogEventListener?.onSetupWiFiSuccess()
    }

    private fun showCalibrationGuideDialog() {
        activity.lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(1000)
            sCalibrationGuideDialog?.dismiss()
            sCalibrationGuideDialog = CalibrationGuideDialog(
                activity = activity,
                device = device,
                listener = object : IAudioCalibrationDialogEvent {
                    override fun onCalibrationBtnClick() {
                        Logger.i(logTag, "CalibrationGuideDialog.onCalibrationBtnClick() >>> go")
                        calibrationDialogEvent?.onCalibrationBtnClick()
                        CalibrationBaseActivity.portal(context = activity, device = device)
                    }

                    override fun onLaterBtnClick() {
                        Logger.i(logTag, "CalibrationGuideDialog.onLaterBtnClick() >>> check OOBE again")
                        calibrationDialogEvent?.onLaterBtnClick()
                    }
                }
            ).apply {
                show()
                Logger.i(logTag, "showCalibrationGuideDialog() >>> pop[${device.UUID}]")
            }
        }
    }

    private fun showVAGuideDialog(device: OneDevice) {
        activity.lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(1000)
            sVAGuideDialog?.dismiss()
            sVAGuideDialog = VAGuideDialog(activity = activity, device = device).apply {
                show()
                setOnDismissListener(vaGuideDialogDismissListener)
                Logger.i(logTag, "showVAGuideDialog() >>> pop[${device.UUID}]")
            }
        }
    }

    private suspend fun OneDevice.retrieveMembersWithCacheFirst(): List<Member>? {
        val groupInfo = device.groupInfoExt?.groupInfo?.groupInfo
            ?: withContext(DISPATCHER_API) {
                device.syncGetGroupInfoWithTimeout(
                    logTag = logTag,
                    repeatTimes = 0,
                    timeoutMills = GENERAL_TIMEOUT_MILLS
                )?.groupInfo
            }

        val members = groupInfo?.members ?: run {
            Logger.w(logTag, "retrieveMembersWithCacheFirst() >>> fail to fetch group info")
            return null
        }

        return members
    }

    private fun handleGroupOtaStatus() = activity.lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
        val rsp = withContext(DISPATCHER_API) {
            device.syncGetGroupDevicesOtaStatusWithTimeout(logTag = logTag)
        }

        Logger.d(logTag, "handleGroupOtaStatus() >>> $rsp")
        val targetOtaStatus = rsp?.devices?.firstOrNull { otaStatus ->
            otaStatus.isGC && otaStatus.isInOTA
        } ?: run {
            Logger.w(logTag, "handleGroupOtaStatus() >>> all GC are not in OTA")
            return@launch
        }
        Logger.d(logTag, "handleGroupOtaStatus() >>> target GC:$targetOtaStatus")

        val members = device.retrieveMembersWithCacheFirst()
        val targetMember = members?.firstOrNull { member ->
            member.id == targetOtaStatus.id
        } ?: run {
            Logger.w(logTag, "handleGroupOtaStatus() >>> fail to find target member by [$targetOtaStatus]")
            return@launch
        }

        Logger.d(logTag, "handleGroupOtaStatus() >>> find target member:$targetMember")

        sGroupDevicesOtaTipsDialog?.dismiss()
        sGroupDevicesOtaTipsDialog = GroupDevicesOtaTipsDialog(
            context = activity,
            modelName = targetMember.deviceName,
            deviceName = targetMember.friendlyName,
            cid = targetMember.colorId
        ).apply {
            show()
            Logger.i(logTag, "handleGroupOtaStatus() >>> pop[${device.UUID}]")
        }
    }
}