package com.harman.oobe.ble.flow

import android.content.Context
import androidx.annotation.MainThread
import androidx.lifecycle.viewModelScope
import com.harman.connect.auth
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.db.DeviceCache
import com.harman.discover.bean.PartyBandDevice
import com.harman.log.Logger
import com.harman.oobe.AuthUIState
import com.harman.oobe.ble.BLEOOBEDialog
import com.harman.oobe.ble.BLEOOBEViewModel
import com.harman.oobe.wifi.LocalCacheAdapter.updateCache
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.v5protocol.bean.devinfofeat.V5AuthenticateMode
import com.harmanbar.ble.statistic.StatisticConstant
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * @Description ble authenticate [PartyBandDevice]
 * <AUTHOR>
 * @Time 2025/3/7
 */
class PartyBandAuthFlow(
    dialog: BLEOOBEDialog, vm: BLEOOBEViewModel
) : IOOBEFlow(dialog = dialog, vm = vm) {

    private var failTimes = 1

    @MainThread
    fun init() {
        dialog.authUIStatus.value = AuthUIState.READY
        auth(vm.weakActivity.get())
    }


    @MainThread
    private fun auth(context: Context?) {
        Logger.d(TAG, "auth() >>> start")
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val device = dialog.device as PartyBandDevice

            if (!gattConnect(context)) {
                dialog.authUIStatus.value = getCountedFailFlag()
                return@launch
            }
            val result = device.auth(BLE_OOBE_TIMEOUT_MILLS)
            if (result) {
                device.updateCache(hadAuth = true)
            }

            dialog.authUIStatus.value = if (result) {
                Logger.i(TAG, "auth() >>> auth suc")
                countDown()
                AuthUIState.SUCCESS
            } else {
                Logger.e(TAG, "auth() >>> auth fail. current fail times[$failTimes]")
                getCountedFailFlag()
            }
        }
    }

    private suspend fun gattConnect(context: Context?): Boolean {
        if (dialog.device.isGattConnected) {
            return true
        }

        context ?: run {
            Logger.e(TAG, "gattConnect() >>> missing context")
            return false
        }

        return dialog.device.syncGattConnectWithTimeout(context = context, repeatTimes = 2)
    }

    private fun getCountedFailFlag(): AuthUIState {
        return if (failTimes++ <= 3) {
            AuthUIState.FAIL_LESS_THAN_THREE_TIMES
        } else {
            AuthUIState.FAIL_MORE_THAN_THREE_TIMES
        }
    }

    private fun countDown() {
        vm.viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            delay(2000L)
            dialog.dismissDialog(isInterrupt = false)
        }
    }

    @MainThread
    override fun onExit() {
        dialog.showDoubleConfirmDialog()
    }

    @MainThread
    override fun onRetry() {
        init()
    }

    @MainThread
    override fun onDoubleConfirmResult(quit: Boolean) {
        if (quit) {
            dialog.dismissDialog(isInterrupt = true)
            val dev = dialog.device as PartyBandDevice
            // cancel without confirm result
            dev.asyncSetDevInfoFeat(V5AuthenticateMode(V5AuthenticateMode.Mode.Exit))
        }
    }

    companion object {
        private const val TAG = "PartyBandAuthFlow"

        private const val BLE_OOBE_TIMEOUT_MILLS = StatisticConstant.CONFIG_SOUND_TIMEOUT * 1000L
    }
}