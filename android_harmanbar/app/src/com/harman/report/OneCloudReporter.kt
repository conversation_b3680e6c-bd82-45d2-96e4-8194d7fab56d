package com.harman.report

import androidx.annotation.WorkerThread
import com.harman.bar.app.BuildConfig
import com.harman.discover.bean.Device
import com.harman.log.Logger
import com.tencent.mmkv.MMKV
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit
import kotlin.math.abs

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/14.
 */
object OneCloudReporter {

    private val url: String
        get() = if (BuildConfig.DEBUG) {
            "https://bluetooth-events.onecloudssodev.com/v1/devices"
        } else {
            "https://bluetooth-events.onecloud.harman.com/v1/devices"
        }

    private val apiKey: String
        get() = if (BuildConfig.DEBUG) {
            "1ccdd857-8803-c032-28d6-52603ea95400"
        } else {
            "187bf0d1-4950-4e2f-94c3-ca534bab5309"
        }

    private const val KEY_NAME = "X-API-Key"

    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(15, TimeUnit.SECONDS)
        .build()

    var isReportEveryTime: Boolean
        get() = MMKV.defaultMMKV().decodeBool(V5_ONE_CLOUD_ANALYTICS_FORCE_REPORT, false)
        set(value) {
            MMKV.defaultMMKV().encode(V5_ONE_CLOUD_ANALYTICS_FORCE_REPORT, value)
        }

    @WorkerThread
    suspend fun upload(reportJStr: String): Boolean {
        val requestBody = reportJStr.toRequestBody("application/json".toMediaType())

        val postRequest = Request.Builder()
            .url(url)
            .addHeader(KEY_NAME, apiKey)
            .post(requestBody)
            .build()

        val request = httpClient.newCall(postRequest)
        val rsp = runCatching {
            request.execute() // block thread.
        }.getOrNull()

        Logger.d(TAG, "upload() >>> rsp:$rsp")
        return rsp?.isSuccessful ?: false
    }

    fun isUploadAvailable(device: Device): Boolean {
        if (BuildConfig.DEBUG && isReportEveryTime) {
            Logger.d(TAG, "isUploadAvailable() >>> report every time")
            return true
        }

        val markKey = device.markKey()
        val currentMins = System.currentTimeMillis() / MINUTE_UNIT
        val lastUploadMin = MMKV.defaultMMKV().decodeLong(markKey, 0L) / MINUTE_UNIT
        val gapMins = abs(lastUploadMin - currentMins)

        Logger.d(TAG, "isUploadAvailable() >>> currentMinutes[$currentMins] lastMinutes[$lastUploadMin]")
        return gapMins >= DEFAULT_UPLOAD_THRESHOLD_MINUTES
    }

    fun markUpload(device: Device) {
        val markKey = device.markKey()
        MMKV.defaultMMKV().encode(markKey, System.currentTimeMillis())
    }

    private fun Device.markKey(): String = "V5OneCloudAnalytics_${this.pid}_${this.UUID}"

    private const val TAG = "OneCloudReporter"

    private const val MINUTE_UNIT = 60 * 1000L

    private const val DEFAULT_UPLOAD_THRESHOLD_MINUTES = 12 * 60

    private const val V5_ONE_CLOUD_ANALYTICS_FORCE_REPORT = "V5_ONE_CLOUD_ANALYTICS_REPORT_EVERYTIME"
}