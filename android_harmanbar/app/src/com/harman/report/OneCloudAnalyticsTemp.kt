package com.harman.report

import com.google.gson.JsonObject
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.bean.PortableDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.firmwareVersion
import com.harman.discover.util.Tools.macAddressMD5
import com.harman.discover.util.Tools.serialNumber
import com.harman.modelName
import com.harman.v5protocol.bean.analytics.IV5AnalyticsValue
import com.harman.v5protocol.bean.analytics.V5BatteryCellConnection
import com.harman.v5protocol.bean.analytics.V5BatteryPackModel
import com.harman.v5protocol.bean.analytics.V5BatterySN
import com.harman.v5protocol.bean.analytics.V5DurationOfAUXSourceInACMode
import com.harman.v5protocol.bean.analytics.V5DurationOfAUXSourceInBatteryMode
import com.harman.v5protocol.bean.analytics.V5DurationOfBTSourceInACMode
import com.harman.v5protocol.bean.analytics.V5DurationOfBTSourceInBatteryMode
import com.harman.v5protocol.bean.analytics.V5DurationOfDutPowerOn
import com.harman.v5protocol.bean.analytics.V5DurationOfEachCH1GuitarVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachCH1MICVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachCH2MICVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachCH3GuitarVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachGuitarPresetID
import com.harman.v5protocol.bean.analytics.V5DurationOfEachInstrumentVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachMasterVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachMusicVolume
import com.harman.v5protocol.bean.analytics.V5DurationOfEachStemMode
import com.harman.v5protocol.bean.analytics.V5DurationOfUACSourceInACMode
import com.harman.v5protocol.bean.analytics.V5DurationOfUACSourceInBatteryMode
import com.harman.v5protocol.bean.analytics.V5DurationOfUSBSourceInACMode
import com.harman.v5protocol.bean.analytics.V5DurationOfUSBSourceInBatteryMode
import com.harman.v5protocol.bean.analytics.V5DurationOfUserCustomizedPreset
import com.harman.v5protocol.bean.analytics.V5TimesOfAuxInstrumentPluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfAuxMusicPluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfBuiltInMICUsed
import com.harman.v5protocol.bean.analytics.V5TimesOfCH1GuitarPluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfCH1MICPluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfCH2InstrumentPluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfCH2MICPluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfCH3PluggedIn
import com.harman.v5protocol.bean.analytics.V5TimesOfDaisyChainOutUsed
import com.harman.v5protocol.bean.analytics.V5TimesOfDrumFeatureTriggered
import com.harman.v5protocol.bean.analytics.V5TimesOfDutPowerOn
import com.harman.v5protocol.bean.analytics.V5TimesOfHeadphoneOutUsed
import com.harman.v5protocol.bean.analytics.V5TimesOfLooperFeatureTriggered
import com.harman.v5protocol.bean.analytics.V5TimesOfMetronomeFeatureTriggered
import com.harman.v5protocol.bean.analytics.V5TimesOfStemON
import com.harman.v5protocol.bean.analytics.V5TimesOfTunerFeatureTriggered
import com.harman.v5protocol.bean.analytics.V5TimesOfUACMultiChannelRecording
import com.harman.v5protocol.bean.analytics.V5TimesOfUACRecording
import com.harman.v5protocol.bean.analytics.V5TimesThatUsersAdjustVolumeThroughAVRCPLEA
import com.wifiaudio.app.LinkplayApplication

/**
 * Created by gerrardzhang on 2025/5/13.
 */
object OneCloudAnalyticsTemp {

    const val PARTITION_KEY = "partition_key"

    const val ANALYTICS_PAYLOAD = "analytics_payload"

    const val GENERAL = "general"

    const val BANDBOX = "bandbox"

    object General {
        const val DEVICE_PRODUCT_ID = "device_product_id"
        const val DEVICE_COLOR_ID = "device_color_id"
        const val DEVICE_MODEL_NAME = "device_model_name"
        const val DEVICE_MAC_ADDRESS = "device_mac_address"
        const val DEVICE_MAC_HASH_ADDRESS = "device_mac_hash_address"
        const val DEVICE_SERIAL_NUMBER = "device_serial_number"
        const val DEVICE_UUID = "device_uuid"
        const val DEVICE_FIRMWARE_VERSION = "device_firmware_version"
        const val MOBILE_PLATFORM = "mobile_platform"
        const val MOBILE_OS_VERSION = "mobile_os_version"
        const val MOBILE_BRAND = "mobile_brand"
        const val MOBILE_MODEL = "mobile_model"
        const val APP_NAME = "app_name"
        const val APP_VERSION = "app_version"
    }

    object BandBox {
        const val PLAYBACK_USAGE = "playback_usage"
        const val BUTTON_USAGE = "button_usage"
        const val VOLUME_USAGE = "volume_usage"
        const val GUITAR_USAGE = "guitar_usage"
        const val UAC_USAGE = "uac_usage"
        const val BATTERY_INFO = "battery_info"
        const val STEM_USAGE = "stem_usage"
    }

    private fun formatV5AnalyticsGeneralJObj(device: Device): JsonObject {
        val macAddressMD5 = device.macAddressMD5()

        val generalJObj = JsonObject().apply {
            addProperty(General.DEVICE_PRODUCT_ID, device.pid)
            addProperty(General.DEVICE_COLOR_ID, device.cid)
            addProperty(General.DEVICE_MODEL_NAME, device.modelName())
            addProperty(General.DEVICE_MAC_ADDRESS, device.macAddress)
            addProperty(General.DEVICE_MAC_HASH_ADDRESS, macAddressMD5)
            addProperty(General.DEVICE_SERIAL_NUMBER, device.serialNumber())
            addProperty(General.DEVICE_UUID, macAddressMD5)
            addProperty(General.DEVICE_FIRMWARE_VERSION, device.firmwareVersion())
            addProperty(General.MOBILE_PLATFORM, "Android")
            addProperty(General.MOBILE_OS_VERSION, android.os.Build.VERSION.RELEASE)
            addProperty(General.MOBILE_BRAND, android.os.Build.BRAND)
            addProperty(General.MOBILE_MODEL, android.os.Build.MODEL)
            addProperty(General.APP_NAME, LinkplayApplication.mResources.getString(R.string.applicationName))
            addProperty(General.APP_VERSION, BuildConfig.VERSION_NAME)
        }

        return generalJObj
    }

    fun formatV5AnalyticsReport(device: Device): JsonObject {
        val generalJObj = formatV5AnalyticsGeneralJObj(device = device)
        val analyticsPayloadJObj = JsonObject()
        analyticsPayloadJObj.add(GENERAL, generalJObj)

        val outputJObj = JsonObject()
        outputJObj.addProperty(PARTITION_KEY, System.currentTimeMillis().toString())
        outputJObj.add(ANALYTICS_PAYLOAD, analyticsPayloadJObj)

        return outputJObj
    }

    fun formatV5AnalyticsReport(
        device: PartyBandDevice,
        values: List<IV5AnalyticsValue<*>>
    ): JsonObject {
        val generalJObj = formatV5AnalyticsGeneralJObj(device = device)

        val bandBoxJObj = JsonObject().also { bandBoxJObj ->
            fun JsonObject.addNumbers(pairs: List<Pair<String, Int>>) {
                pairs.forEach { kv ->
                    addProperty(kv.first, kv.second)
                }
            }

            fun JsonObject.addStrings(pairs: List<Pair<String, String>>) {
                pairs.forEach { kv ->
                    addProperty(kv.first, kv.second)
                }
            }

            fun JsonObject.addNumbers(pairs: List<Pair<String, UInt>>) {
                pairs.forEach { kv ->
                    addProperty(kv.first, kv.second.toLong())
                }
            }

            val playbackUsage = JsonObject()
            val buttonUsage = JsonObject()
            val volumeUsage = JsonObject()
            val guitarUsage = JsonObject()
            val uacUsage = JsonObject()
            val batteryInfo = JsonObject()
            val stemUsage = JsonObject()

            values.forEach { value ->
                when (value) {
                    /** @link [BandBox.PLAYBACK_USAGE] */
                    is V5DurationOfBTSourceInBatteryMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfUSBSourceInBatteryMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfAUXSourceInBatteryMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfUACSourceInBatteryMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfBTSourceInACMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfUSBSourceInACMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfAUXSourceInACMode -> playbackUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfUACSourceInACMode -> playbackUsage.addNumbers(value.cloudKVs)
                    /** @link [BandBox.BUTTON_USAGE] */
                    is V5TimesOfDutPowerOn -> buttonUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfDutPowerOn -> buttonUsage.addNumbers(value.cloudKVs)
                    /** @link [BandBox.VOLUME_USAGE] */
                    is V5TimesThatUsersAdjustVolumeThroughAVRCPLEA -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachMusicVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachCH1MICVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachCH1GuitarVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachInstrumentVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachMasterVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachCH2MICVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachCH3GuitarVolume -> volumeUsage.addNumbers(value.cloudKVs)
                    /** @link [BandBox.GUITAR_USAGE] */
                    is V5DurationOfUserCustomizedPreset -> guitarUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachGuitarPresetID -> guitarUsage.addNumbers(value.cloudKVs)
                    /** @link [BandBox.UAC_USAGE] */
                    is V5TimesOfUACRecording -> uacUsage.addNumbers(value.cloudKVs)
                    is V5TimesOfUACMultiChannelRecording -> uacUsage.addNumbers(value.cloudKVs)
                    /** @link [BandBox.BATTERY_INFO] */
                    is V5BatteryPackModel -> batteryInfo.addStrings(value.cloudKVs)
                    is V5BatteryCellConnection -> batteryInfo.addNumbers(value.cloudKVs)
                    is V5BatterySN -> batteryInfo.addStrings(value.cloudKVs)
                    /** @link [BandBox.STEM_USAGE] */
                    is V5TimesOfStemON -> stemUsage.addNumbers(value.cloudKVs)
                    is V5DurationOfEachStemMode -> stemUsage.addNumbers(value.cloudKVs)
                    /** individuals, put in [bandBoxJObj] */
                    is V5TimesOfDrumFeatureTriggered -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfMetronomeFeatureTriggered -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfTunerFeatureTriggered -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfLooperFeatureTriggered -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfCH1MICPluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfCH1GuitarPluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfCH2MICPluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfCH2InstrumentPluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfCH3PluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfAuxInstrumentPluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfAuxMusicPluggedIn -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfBuiltInMICUsed -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfHeadphoneOutUsed -> bandBoxJObj.addNumbers(value.cloudKVs)
                    is V5TimesOfDaisyChainOutUsed -> bandBoxJObj.addNumbers(value.cloudKVs)
                }
            }

            if (!playbackUsage.isEmpty) {
                bandBoxJObj.add(BandBox.PLAYBACK_USAGE, playbackUsage)
            }

            if (!buttonUsage.isEmpty) {
                bandBoxJObj.add(BandBox.BUTTON_USAGE, buttonUsage)
            }

            if (!volumeUsage.isEmpty) {
                bandBoxJObj.add(BandBox.VOLUME_USAGE, volumeUsage)
            }

            if (!guitarUsage.isEmpty) {
                bandBoxJObj.add(BandBox.GUITAR_USAGE, guitarUsage)
            }

            if (!uacUsage.isEmpty) {
                bandBoxJObj.add(BandBox.UAC_USAGE, uacUsage)
            }

            if (!batteryInfo.isEmpty) {
                bandBoxJObj.add(BandBox.BATTERY_INFO, batteryInfo)
            }

            if (!stemUsage.isEmpty) {
                bandBoxJObj.add(BandBox.STEM_USAGE, stemUsage)
            }
        }

        val analyticsPayloadJObj = JsonObject()
        analyticsPayloadJObj.add(GENERAL, generalJObj)
        analyticsPayloadJObj.add(BANDBOX, bandBoxJObj)

        val outputJObj = JsonObject()
        outputJObj.addProperty(PARTITION_KEY, System.currentTimeMillis().toString())
        outputJObj.add(ANALYTICS_PAYLOAD, analyticsPayloadJObj)

        return outputJObj
    }
}