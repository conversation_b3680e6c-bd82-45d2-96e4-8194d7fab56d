package com.harman.partyband.control

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogToneShifterBinding
import com.harman.BottomPopUpDialogFragment
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.bean.devinfofeat.V5PitchChange
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2024/12/25
 */
class ToneShifterDialog(
    private val context: Context,
    private val device: PartyBandDevice,
) : BottomPopUpDialogFragment(context) {
    private val binding by lazy { DialogToneShifterBinding.inflate(layoutInflater) }
    private val vm by lazy {
        viewModels<ToneShifterViewModel>().value.apply {
            device = <EMAIL>
        }
    }

    override fun getContentView(): View {
        return binding.root
    }

    override fun buildContentView(view: View) {
        binding.btnConfirm.setOnClickListener {
            vm.done()
            dismiss()
        }
        binding.btnReset.setOnClickListener {
            vm.reset()
        }
        binding.hsvValue.apply {
            onValueChange = {
                vm.setValue(it - V5PitchChange.MAX)
            }
        }
    }

    override fun observeModel() {
        vm.value.observe(this) {
            buildTvValue(it)
            buildTvValueDesc(it)
            buildBtnReset(it)
            binding.hsvValue.setValue(it + V5PitchChange.MAX)
        }
        vm.initValue.observe(this) {
            binding.hsvValue.initialValue = it + V5PitchChange.MAX
        }
    }

    override fun prepareData() {
        vm.fetchData()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun buildTvValue(value: Int) {
        binding.tvValue.text = value.toString()
        binding.tvValue.setTextColor(
            context.getColor(
                if (value == 0) {
                    R.color.fg_primary
                } else {
                    R.color.fg_activate
                }
            )
        )
    }

    private fun buildTvValueDesc(value: Int) {
        binding.tvValueDesc.text = getString(
            if (value == 0) {
                R.string.standard
            } else {
                R.string.edited
            }
        )
        binding.tvValueDesc.setTextColor(
            context.getColor(
                if (value == 0) {
                    R.color.fg_primary
                } else {
                    R.color.fg_activate
                }
            )
        )
    }

    private fun buildBtnReset(value: Int) {
        binding.btnReset.apply {
            isEnabled = value != 0
        }
    }
}

class ToneShifterViewModel : ViewModel() {
    val value = MutableLiveData(0)
    val initValue = MutableLiveData<Int>()

    lateinit var device: PartyBandDevice

    fun fetchData() {
        viewModelScope.launch {
            value.value = device.getDevInfoFeat<V5PitchChange>()?.value ?: 0
            initValue.value = value.value
        }
    }

    fun done() {
        device.asyncSetDevInfoFeat(V5PitchChange(value.value!!))
    }

    fun setValue(targetValue: Int) {
        if (targetValue == value.value) {
            return
        }
        value.value = targetValue
    }

    fun reset() {
        value.value = 0
    }
}