package com.harman.partyband.control

import android.content.Context
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ResourceUtils
import com.harman.BottomPopUpDialogFragment
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogPickupBinding
import com.harman.discover.bean.PartyBandDevice
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible
import com.harman.v5protocol.bean.devinfofeat.PickupTypeEnum
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPickup
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPickupQuery
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/5/13
 */
class PickupDialog(
    private val context: Context,
    private val chNum: V5ChannelNumber,
    val device: PartyBandDevice,
) : BottomPopUpDialogFragment(context) {
    private val binding by lazy { DialogPickupBinding.inflate(layoutInflater) }
    private val pickupType = MutableLiveData<PickupTypeEnum?>(null)
    private val expanded = MutableLiveData(false)
    override fun getContentView(): View {
        return binding.root
    }

    override fun buildContentView(view: View) {
        listOf(binding.tvPickupType, binding.ivExpand).forEach {
            it.setOnClickListener {
                toggleExpand()
            }
        }
        binding.tvPickupDesc.text = "${getString(R.string.pickup_desc1)}\n${getString(R.string.pickup_desc2)}"
        binding.tvPassive.apply {
            tag = PickupTypeEnum.Passive
            setOnClickListener { switchType(tag as PickupTypeEnum) }
        }
        binding.tvActive.apply {
            tag = PickupTypeEnum.Active
            setOnClickListener { switchType(tag as PickupTypeEnum) }
        }
        binding.btnConfirm.setOnClickListener {
            dismiss()
        }
    }

    override fun observeModel() {
        pickupType.observe(this) { type ->
            listOf(binding.tvPassive, binding.tvActive).forEach { v ->
                if (v.tag == type) {
                    v.background = ResourceUtils.getDrawable(R.drawable.radius_round_bg_inverse)
                    v.setTextColor(ColorUtils.getColor(R.color.fg_inverse))
                } else {
                    v.background = null
                    v.setTextColor(ColorUtils.getColor(R.color.fg_secondary))
                }
            }
        }
        expanded.observe(this) {
            binding.ivExpand.apply {
                rotation = if (it) 0f else 180f
            }
            binding.tvPickupDesc.apply {
                if (it) visible() else gone()
            }
        }
    }

    override fun prepareData() {
        lifecycleScope.launch {
            device.asyncSetDevInfoFeat(V5GuitarPickupQuery(chNum))
            device.awaitDevInfo<V5GuitarPickup>()?.also {
                pickupType.value = it.type
            }
        }
    }

    private fun toggleExpand() {
        expanded.value = !expanded.value!!
    }

    private fun switchType(type: PickupTypeEnum) {
        device.asyncSetDevInfoFeat(V5GuitarPickup(chNum, type))
        pickupType.value = type
    }
}