package com.harman.partyband.control

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.harman.discover.bean.PartyBandDevice
import com.harman.webview.DeviceControlHybridActivity
import com.harman.webview.models.PBHVMEvent
import com.harman.webview.models.PartyBandHybridViewModel
import kotlinx.coroutines.launch

/**
 * @Description [PartyBandDevice] in [DeviceControlHybridActivity] business UI operations
 * <AUTHOR>
 * @Time 2024/2/14
 */
class HybridPartyBandUiController(private val activity: DeviceControlHybridActivity, private val viewmodel: PartyBandHybridViewModel) :
    DefaultLifecycleObserver {
    init {
        activity.lifecycle.addObserver(this)
        activity.lifecycleScope.launch {
            viewmodel.eventStream.collect {
                onEvent(it)
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        activity.lifecycle.removeObserver(this)
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        activity.lifecycleScope.launch {
            viewmodel.fetchOtaStatus()
        }
    }

    private fun onEvent(e: PBHVMEvent) {
        when (e) {
            is PBHVMEvent.PickUp -> {
                PickupDialog(activity, e.chNum, e.device).show(activity.supportFragmentManager, null)
            }
        }
    }

    companion object {
        private const val TAG = "HybridPartyBandUiController"
    }
}