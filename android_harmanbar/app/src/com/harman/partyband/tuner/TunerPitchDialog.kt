package com.harman.partyband.tuner

import android.content.Context
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogTunerPitchBinding
import com.harman.BottomPopUpDialogFragment
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.bean.devinfofeat.V5TunerMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerString
import com.harman.v5protocol.bean.devinfofeat.V5TunerTempo

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/1/22
 */
class TunerPitchDialog(
    private val context: Context,
    private val initString: V5TunerString.TString,
    private val initValue: V5TunerTempo,
    private val device: PartyBandDevice,
) : BottomPopUpDialogFragment(context) {
    private val binding by lazy { DialogTunerPitchBinding.inflate(layoutInflater) }
    private lateinit var vm: TunerPitchViewModel
    override fun getContentView(): View {
        return binding.root
    }

    override fun buildContentView(view: View) {
        binding.btnConfirm.setOnClickListener {
            vm.done()
            dismiss()
        }
        binding.btnReset.setOnClickListener {
            vm.reset()
        }
        binding.ivAdd.setOnClickListener {
            vm.add()
        }
        binding.ivSub.setOnClickListener {
            vm.sub()
        }
        binding.hsvValue.apply {
            onValueChange = {
                vm.setValue(V5TunerTempo.MIN_HZ + it)
            }
            initialValue = <EMAIL> - V5TunerTempo.MIN_HZ
            onScaleLineLabel = {
                "${(V5TunerTempo.MIN_HZ + it) / 10f}"
            }
        }
    }

    override fun observeModel() {
        vm = viewModels<TunerPitchViewModel>().value.apply {
            device = <EMAIL>
            initString = <EMAIL>
            initValue = <EMAIL>
        }
        vm.valueState.observe(this) {
            buildTvValue(it)
            buildTvDesc(it)
            buildBtnReset(it)
            buildHsv(it)
        }
        vm.load()
    }

    private fun buildHsv(state: TunerPitchValueState) {
        binding.hsvValue.setValue(state.value.hzInt - V5TunerTempo.MIN_HZ)
    }

    private fun buildTvDesc(it: TunerPitchValueState) {
        binding.tvDesc.apply {
            if (it.isChanged) {
                text = getString(R.string.edited)
                setTextColor(requireContext().getColor(R.color.fg_activate))
            } else {
                text = initString.mName
                setTextColor(requireContext().getColor(R.color.fg_primary))
            }
        }
    }

    override fun prepareData() = Unit

    private fun buildTvValue(state: TunerPitchValueState) {
        binding.tvValue.text = state.valueString
        if (state.isChanged) {
            binding.tvValue.setTextColor(context.getColor(R.color.fg_activate))
        } else {
            binding.tvValue.setTextColor(context.getColor(R.color.fg_primary))
        }
    }

    private fun buildBtnReset(state: TunerPitchValueState) {
        binding.btnReset.apply {
            isEnabled = state.isChanged
        }
    }
}

class TunerPitchViewModel : ViewModel() {
    lateinit var device: PartyBandDevice
    lateinit var initString: V5TunerString.TString
    lateinit var initValue: V5TunerTempo
    val valueState = MutableLiveData<TunerPitchValueState>()

    fun load() {
        valueState.value = TunerPitchValueState("${initValue.hzFloat()}Hz", initValue.hzInt != V5TunerTempo.STANDARD_HZ, initValue)
    }

    fun done() {
        valueState.value?.value?.also {
            if (it.hzInt != initValue.hzInt) {
                device.asyncSetDevInfoFeat(it)
            }
        }
    }

    fun setValue(targetValue: Int) {
        if (targetValue > V5TunerTempo.MAX_HZ || targetValue < V5TunerTempo.MIN_HZ) {
            return
        }
        val targetWriteData = V5TunerTempo(targetValue)
        valueState.value = TunerPitchValueState("${targetWriteData.hzFloat()}Hz", targetValue != V5TunerTempo.STANDARD_HZ, targetWriteData)
    }

    fun reset() {
        setValue(V5TunerTempo.STANDARD_HZ)
    }

    fun add() {
        setValue(valueState.value!!.value.hzInt + 1)
    }

    fun sub() {
        setValue(valueState.value!!.value.hzInt - 1)
    }
}