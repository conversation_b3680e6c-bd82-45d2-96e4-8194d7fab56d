package com.harman.partyband.tuner

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.distinctUntilChanged
import com.blankj.utilcode.util.SpanUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.FragmentTunerStringBinding
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible
import com.harman.v5protocol.bean.devinfofeat.V5TunerBassMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerMode

/**
 * @Description This fragment is used to implement different UIs for different instruments
 * <AUTHOR>
 * @Time 2025/1/20
 */
class TunerStringFragment : Fragment() {
    private val binding by lazy { FragmentTunerStringBinding.inflate(layoutInflater) }
    private val vm by activityViewModels<TunerViewModel>()
    private val guitar33Tvs by lazy {
        listOf(
            binding.tv33String6.apply { tag = V5TunerMode.Mode.String6 },
            binding.tv33String5.apply { tag = V5TunerMode.Mode.String5 },
            binding.tv33String4.apply { tag = V5TunerMode.Mode.String4 },
            binding.tv33String3.apply { tag = V5TunerMode.Mode.String3 },
            binding.tv33String2.apply { tag = V5TunerMode.Mode.String2 },
            binding.tv33String1.apply { tag = V5TunerMode.Mode.String1 },
        )
    }

    private val guitar6Tvs by lazy {
        listOf(
            binding.tv6String6.apply { tag = V5TunerMode.Mode.String6 },
            binding.tv6String5.apply { tag = V5TunerMode.Mode.String5 },
            binding.tv6String4.apply { tag = V5TunerMode.Mode.String4 },
            binding.tv6String3.apply { tag = V5TunerMode.Mode.String3 },
            binding.tv6String2.apply { tag = V5TunerMode.Mode.String2 },
            binding.tv6String1.apply { tag = V5TunerMode.Mode.String1 },
        )
    }

    private val bassTvs by lazy {
        listOf(
            binding.tvBassString4.apply { tag = V5TunerMode.Mode.String4 },
            binding.tvBassString3.apply { tag = V5TunerMode.Mode.String3 },
            binding.tvBassString2.apply { tag = V5TunerMode.Mode.String2 },
            binding.tvBassString1.apply { tag = V5TunerMode.Mode.String1 },
        )
    }

    private val ukuleleTvs by lazy {
        listOf(
            binding.tvUkString4.apply { tag = V5TunerMode.Mode.String4 },
            binding.tvUkString3.apply { tag = V5TunerMode.Mode.String3 },
            binding.tvUkString2.apply { tag = V5TunerMode.Mode.String2 },
            binding.tvUkString1.apply { tag = V5TunerMode.Mode.String1 },
        )
    }

    private fun currentTvs(mode: V5TunerBassMode.Mode): List<TextView> {
        return when (mode) {
            V5TunerBassMode.Mode.Guitar33 -> guitar33Tvs
            V5TunerBassMode.Mode.Guitar6InLine -> guitar6Tvs
            V5TunerBassMode.Mode.Bass4String -> bassTvs
            V5TunerBassMode.Mode.Ukulele -> ukuleleTvs
        }
    }

    private val lottieMap = mapOf(
        V5TunerBassMode.Mode.Guitar33 to ViolinLottie(
            "lottie/partyband/guitar33/guitar33.json",
            "lottie/partyband/guitar33/",
            mapOf(
                V5TunerMode.Mode.String1 to 5,
                V5TunerMode.Mode.String2 to 4,
                V5TunerMode.Mode.String3 to 3,
                V5TunerMode.Mode.String4 to 2,
                V5TunerMode.Mode.String5 to 1,
                V5TunerMode.Mode.String6 to 0,
            ),
            6,
        ),
        V5TunerBassMode.Mode.Guitar6InLine to ViolinLottie(
            "lottie/partyband/guitar_6_in_line/guitar_6_in_line.json",
            "lottie/partyband/guitar_6_in_line/",
            mapOf(
                V5TunerMode.Mode.String1 to 5,
                V5TunerMode.Mode.String2 to 4,
                V5TunerMode.Mode.String3 to 3,
                V5TunerMode.Mode.String4 to 2,
                V5TunerMode.Mode.String5 to 1,
                V5TunerMode.Mode.String6 to 0
            ),
            6
        ),

        V5TunerBassMode.Mode.Bass4String to ViolinLottie(
            "lottie/partyband/bass/bass.json",
            "lottie/partyband/bass/",
            mapOf(
                V5TunerMode.Mode.String1 to 3,
                V5TunerMode.Mode.String2 to 2,
                V5TunerMode.Mode.String3 to 1,
                V5TunerMode.Mode.String4 to 0,
            ),
            4
        ),

        V5TunerBassMode.Mode.Ukulele to ViolinLottie(
            "lottie/partyband/ukulele/ukulele.json",
            "lottie/partyband/ukulele/",
            mapOf(
                V5TunerMode.Mode.String1 to 3,
                V5TunerMode.Mode.String2 to 2,
                V5TunerMode.Mode.String3 to 1,
                V5TunerMode.Mode.String4 to 0,
            ),
            4
        )
    )

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        observeModel()
    }

    private fun initView() {
        (guitar33Tvs + guitar6Tvs + bassTvs + ukuleleTvs).forEach { tv ->
            tv.setOnClickListener {
                vm.switchTunerMode(tv.tag as V5TunerMode.Mode, true)
            }
        }
    }

    private fun observeModel() {
        vm.currentTunerBassMode.distinctUntilChanged().observe(viewLifecycleOwner) {
            binding.g33Guitar.gone()
            binding.g6Guitar.gone()
            binding.gBass.gone()
            binding.gUkulele.gone()
            when (it!!) {
                V5TunerBassMode.Mode.Guitar33 -> binding.g33Guitar.visible()
                V5TunerBassMode.Mode.Guitar6InLine -> binding.g6Guitar.visible()
                V5TunerBassMode.Mode.Bass4String -> binding.gBass.visible()
                V5TunerBassMode.Mode.Ukulele -> binding.gUkulele.visible()
            }
            lottieMap[it]?.also { lottie ->
                binding.lav.setAnimation(lottie.lottieJsonFilePath)
                binding.lav.imageAssetsFolder = lottie.imageAssetsFolder
                binding.lav.tag = lottie
                binding.lav.frame = lottie.defaultFrameNumber
            }
        }

        vm.currentActiveString.distinctUntilChanged().observe(viewLifecycleOwner) { tunerMode ->
            vm.currentTunerBassMode.value?.also { bassMode ->
                currentTvs(bassMode).forEach { tv ->
                    tv.setBackgroundResource(if (tv.tag == tunerMode) R.drawable.radius_round_fg_primary else R.drawable.radius_round_bg_card)
                    tv.setTextColor(requireContext().getColor(if (tv.tag == tunerMode) R.color.fg_inverse else R.color.fg_primary))
                }
                (binding.lav.tag as? ViolinLottie)?.also { lottie ->
                    binding.lav.frame = lottie.stringsHighlightFrameNumber[tunerMode] ?: lottie.defaultFrameNumber
                }
            }
        }
        vm.tunerString.observe(viewLifecycleOwner) { string ->
            vm.currentTunerBassMode.value?.also { bassMode ->
                currentTvs(bassMode).forEachIndexed { idx, tv ->
                    tv.text = string.stringNames[idx].let {
                        val span = SpanUtils().append(it.substring(0, 1))
                        if (it.length > 1) {
                            span.append(it.substring(1)).setFontSize(10, true)
                        }
                        span.create()
                    }
                }
            }
        }
    }
}

private data class ViolinLottie(
    val lottieJsonFilePath: String,
    val imageAssetsFolder: String,
    val stringsHighlightFrameNumber: Map<V5TunerMode.Mode, Int>,
    val defaultFrameNumber: Int,
)
