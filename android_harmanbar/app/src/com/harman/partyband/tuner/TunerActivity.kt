package com.harman.partyband.tuner

import android.content.Context
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityTunerBinding
import com.harman.partylight.util.gone
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.discover.DeviceScanner
import com.harman.v5protocol.bean.devinfofeat.V5TunerBassMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerResult
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.random.Random
import kotlin.random.nextInt

/**
 * @Description activity for partyband tuner feature
 * <AUTHOR>
 * @Time 2025/1/6
 */
class TunerActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityTunerBinding.inflate(layoutInflater) }
    private val vm by viewModels<TunerViewModel>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        buildView()
        observeModel()
    }

    private fun observeModel() {
        lifecycleScope.launch {
            vm.soundFlow.collect { sound ->
                vm.tunerString.value?.also { curStrings ->
                    if (sound.hasSignal) {
                        binding.tuner.addPoint(sound, curStrings)
                    }
                }
            }
        }
        vm.isAuto.distinctUntilChanged().observe(this) {
            binding.tvSwitchMode.text = getString(if (it) R.string.auto_mode else R.string.manual_mode)
        }
    }

    private fun buildTvDecision(result: V5TunerResult?) {
        binding.tvDecision.apply {
            if (result?.appraise() == V5TunerResult.Appraise.Perfect) gone() else visible()
            when (result?.appraise()) {
                V5TunerResult.Appraise.Perfect -> Unit
                V5TunerResult.Appraise.ToneUpSlightly,
                V5TunerResult.Appraise.ToneUpSlightly2 -> text = getString(R.string.tone_up_slightly)

                V5TunerResult.Appraise.ToneUp -> text = getString(R.string.tone_up)
                V5TunerResult.Appraise.ToneDownSlightly,
                V5TunerResult.Appraise.ToneDownSlightly2 -> text = getString(R.string.tone_down_slightly)

                V5TunerResult.Appraise.ToneDown -> text = getString(R.string.tone_down)
                null -> text = getString(R.string.pluck_string_to_start_tuning)
            }
        }
    }

    private fun buildView() {
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
        binding.appbar.tvTitle.text = getString(R.string.tuner)
        binding.appbar.ivAction.apply {
            setImageResource(R.drawable.ic_setting)
            setOnClickListener {
                TunerSettingActivity.launch(this@TunerActivity, vm.device?.UUID ?: "")
            }
        }
        binding.tvSwitchMode.setOnClickListener {
            vm.toggleAutoManualMode()
        }
        binding.tuner.keepScreenOn = true
        binding.tuner.onLastPointChanged = {
            buildTvDecision(it)
        }
    }

    companion object {
        fun launch(context: Context, uuid: String) {
            context.push<TunerActivity>(
                bundleOf("uuid" to uuid)
            )
        }
    }
}


