package com.harman.partyband.tuner

import android.content.Context
import android.os.Bundle
import android.view.ViewGroup.LayoutParams
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.children
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityTuningBinding
import com.harman.bar.app.databinding.ItemTuningBinding
import com.harman.dp
import com.harman.partylight.util.push
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.bean.devinfofeat.V5TunerBassMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerString
import com.harman.v5protocol.bean.devinfofeat.V5TunerTempo
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/1/22
 */
class TuningActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityTuningBinding.inflate(layoutInflater) }
    private val vm by viewModels<TuningViewModel>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        buildAppbar()
        observeModel()
    }

    private fun buildAppbar() {
        binding.appbar.ivLeading.setOnClickListener { finish() }
        binding.appbar.tvTitle.text = getString(R.string.tuning)
    }

    private fun observeModel() {
        vm.string.distinctUntilChanged().observe(this) { currentString ->
            binding.tvType.text = getTunerStringTypeName(this, currentString.stringType)
            buildContainer(currentString)
            buildCheckers(currentString)
        }
    }

    private fun buildContainer(currentString: V5TunerString.TString) {
        if (binding.llContainer.childCount == 0) {
            V5TunerString.getTunerStringByStringType(currentString.stringType).forEach { string ->
                ItemTuningBinding.inflate(layoutInflater).apply {
                    tvName.text = string.mName
                    tvChecker.text = string.stringNames.joinToString("")
                    root.tag = string
                    root.setOnClickListener {
                        vm.changeString(string)
                    }
                    binding.llContainer.addView(root, LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, 56.dp()))
                }
            }
        }
    }

    private fun buildCheckers(currentString: V5TunerString.TString) {
        binding.llContainer.children.forEach {
            val itemBinding = ItemTuningBinding.bind(it)
            if (currentString == it.tag) {
                itemBinding.tvChecker.setTextColor(getColor(R.color.fg_activate))
                itemBinding.tvChecker.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.svg_icon_security_selected, 0)
            } else {
                itemBinding.tvChecker.setTextColor(getColor(R.color.fg_disabled))
                itemBinding.tvChecker.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.svg_icon_security_unselected, 0)
            }
        }
    }


    companion object {
        fun launch(context: Context, uuid: String) {
            context.push<TuningActivity>(bundleOf("uuid" to uuid))
        }
    }
}

class TuningViewModel(savedState: SavedStateHandle) : ViewModel() {
    val device = DeviceStore.find(savedState.get<String>("uuid")!!) as? PartyBandDevice
    val string = MutableLiveData<V5TunerString.TString>()

    init {
        viewModelScope.launch {
            device?.getDevInfoFeat<V5TunerString>()?.string?.also {
                string.value = it
            }
        }
    }

    fun changeString(str: V5TunerString.TString) {
        if (str == string.value) {
            return
        }
        device?.asyncSetDevInfoFeat(V5TunerString(str))
        string.value = str
    }
}