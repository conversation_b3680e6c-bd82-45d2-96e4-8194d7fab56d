package com.harman.partyband.tuner

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.drawable.GradientDrawable
import android.media.MediaPlayer
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.animation.doOnEnd
import androidx.core.content.res.ResourcesCompat
import com.blankj.utilcode.util.SpanUtils
import com.harman.bar.app.R
import com.harman.dp
import com.harman.partylight.util.withOpacity
import com.harman.sp
import com.harman.v5protocol.bean.devinfofeat.V5TunerBassMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerResult
import com.harman.v5protocol.bean.devinfofeat.V5TunerString
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.absoluteValue
import kotlin.math.max

/**
 * @Description tuner view for[TunerActivity]
 * <AUTHOR>
 * @Time 2025/1/16
 */
@SuppressLint("UseCompatLoadingForDrawables")
class TunerView : View {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle)

    private val mp by lazy {
        MediaPlayer.create(context, R.raw.tone_success)
    }


    //The pixels that move per second
    private val velocity = 54.dp()
    private val latticeSize = 16.dp()
    private val dotRadius = 2.5f.dp()
    private val bigDotRadius = 16.dp()
    private val coefficientOpacity = 1.3f
    private val stringCircleRadius = 32.7.dp()
    private val pointOffset by lazy {
        (height / 3.5f).toInt()
    }
    private val enableTranslationAnim = false
    private val lastBigPointTranslationDurationMs = 50L
    private var lastBigPointStayTimeMs = 1000L
    private val maskDrawable by lazy {
        GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, intArrayOf(context.getColor(R.color.bg_surface), Color.TRANSPARENT)).apply {
            setSize(width, 56.dp())
        }
    }

    private val widget1 by lazy { context.getDrawable(R.drawable.ic_tuner_widget1)!! }
    private val widget2 by lazy { context.getDrawable(R.drawable.ic_tuner_widget2)!! }
    private val widget3 by lazy { context.getDrawable(R.drawable.icon_check_fg_primary2)!! }

    private val fillPaint = Paint().apply {
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val textPaint = TextPaint(fillPaint).apply {
        textSize = context.resources.getDimensionPixelSize(R.dimen.font_27sp).toFloat()
        typeface = ResourcesCompat.getFont(context, R.font.poppins_extrabold)
    }

    private val shapePaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
        strokeWidth = 2.dp().toFloat()
    }

    private val shapeDashPaint = Paint(shapePaint).apply {
        strokeWidth = 1.dp().toFloat()
        pathEffect = DashPathEffect(floatArrayOf(2.dp().toFloat(), 2.dp().toFloat()), 0f)
    }

    private var coroutineScope: CoroutineScope? = null
    private var deltaH = 0
    private val points = mutableListOf<TunerPoint>()
    private val animator by lazy {
        val totalDistance = run {
            var ret = height
            while (ret % latticeSize != 0) {
                ret++
            }
            ret
        }
        ValueAnimator.ofInt(0, totalDistance).apply {
            duration = (totalDistance / velocity.toFloat() * 1000).toLong()
            interpolator = LinearInterpolator()
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener {
                onAnimFrame(it.animatedValue as Int)
            }
        }
    }
    private var animatorLastPoint: ValueAnimator? = null
    private var lastBigPoint: TunerPoint? = null
    private var lastBigPointTimeoutJob: Job? = null

    var onLastPointChanged: ((result: V5TunerResult?) -> Unit)? = null
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator.removeAllListeners()
        animator.cancel()
        coroutineScope?.cancel()
        mp.release()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        coroutineScope = MainScope()
        post {
            animator.start()
        }
    }

    override fun onDraw(canvas: Canvas) {
        //draw vertical lines
        fillPaint.color = context.getColor(R.color.bg_opacity_10)
        val centerX = width / 2f
        canvas.drawLine(centerX, 0f, centerX, height.toFloat(), fillPaint)
        var distanceToCenterX = latticeSize
        while (distanceToCenterX < centerX) {
            //left
            canvas.drawLine(centerX - distanceToCenterX, 0f, centerX - distanceToCenterX, height.toFloat(), fillPaint)
            //right
            canvas.drawLine(centerX + distanceToCenterX, 0f, centerX + distanceToCenterX, height.toFloat(), fillPaint)
            distanceToCenterX += latticeSize
        }
        //draw horizontal lines
        var distanceToTop = deltaH % latticeSize
        while (distanceToTop <= height) {
            canvas.drawLine(0f, distanceToTop.toFloat(), width.toFloat(), distanceToTop.toFloat(), fillPaint) // first line
            distanceToTop += latticeSize
        }

        //draw widgets
        widget1.setBounds(25.dp(), 62.dp(), 25.dp() + widget1.intrinsicWidth, 62.dp() + widget1.intrinsicHeight)
        widget1.draw(canvas)
        widget2.setBounds(width - widget2.intrinsicWidth - 25.dp(), 62.dp(), width - 25.dp(), 62.dp() + widget2.intrinsicHeight)
        widget2.draw(canvas)
        //draw points
        points.forEach { tunerPoint ->
            when (val appraise = tunerPoint.result.appraise()) {
                null -> Unit
                else -> fillPaint.color = AppraiseColor[appraise]!!
            }
            fillPaint.color =
                fillPaint.color.withOpacity(
                    max(
                        0f,
                        1 - (tunerPoint.yOffset - pointOffset) / (height - pointOffset).toFloat() * coefficientOpacity
                    )
                )
            if (!tunerPoint.isAnimating) {
                canvas.drawCircle(tunerPoint.xOffset.toFloat(), tunerPoint.yOffset.toFloat(), dotRadius.toFloat(), fillPaint)
            }
        }
        //draw last point area
        if (null != lastBigPoint) {
            val appraise = lastBigPoint!!.result.appraise()
            val ry = pointOffset.toFloat()


            //draw Dark Solid circle
            fillPaint.color = context.getColor(R.color.bg_surface)
            canvas.drawCircle(centerX, ry, stringCircleRadius.toFloat(), fillPaint)

            if (V5TunerResult.Appraise.Perfect == appraise) {
                //draw Border of a solid circle
                shapePaint.color = AppraiseColor[appraise]!!
                canvas.drawCircle(centerX, ry, stringCircleRadius.toFloat(), shapePaint)
                val left = centerX.toInt() - widget3.intrinsicWidth / 2
                val top = ry.toInt() - widget3.intrinsicHeight / 2
                widget3.setBounds(left, top, left + widget3.intrinsicWidth, top + widget3.intrinsicHeight)
                widget3.draw(canvas)
            } else {
                //draw Border of a solid circle
                shapeDashPaint.color = when (appraise) {
                    V5TunerResult.Appraise.ToneUpSlightly,
                    V5TunerResult.Appraise.ToneDownSlightly -> AppraiseColor[appraise]!!

                    else -> context.getColor(R.color.fg_disabled)
                }
                canvas.drawCircle(centerX, ry, stringCircleRadius.toFloat(), shapeDashPaint)
                //draw the big point
                when (appraise) {
                    V5TunerResult.Appraise.Perfect,
                    null -> Unit

                    else -> fillPaint.color = AppraiseColor[appraise]!!
                }
                canvas.drawCircle(lastBigPoint!!.xOffset.toFloat(), ry, bigDotRadius.toFloat(), fillPaint)
                //draw The letter of the name of the string
                textPaint.color = context.getColor(R.color.fg_primary)
                val soundStringText = lastBigPoint!!.soundStringText()?.let {
                    val span = SpanUtils().append(it.substring(0, 1))
                    if (it.length > 1) {
                        span.append(it.substring(1)).setFontSize(18.sp())
                    }
                    span.create()
                } ?: ""
                val sl = StaticLayout.Builder
                    .obtain(soundStringText, 0, soundStringText.length, textPaint, width)
                    .setIncludePad(false)
                    .setAlignment(Layout.Alignment.ALIGN_CENTER)
                    .build()
                canvas.save()
                canvas.translate(0f, ry - sl.height / 2)
                sl.draw(canvas)
                canvas.restore()
            }
        }
        //draw mask
        maskDrawable.apply {
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
            setBounds(0, 0, width, maskDrawable.intrinsicHeight)
            draw(canvas)
        }
        maskDrawable.apply {
            orientation = GradientDrawable.Orientation.BOTTOM_TOP
            setBounds(0, height - maskDrawable.intrinsicHeight, width, height)
            draw(canvas)
        }
    }

    fun addPoint(result: V5TunerResult, currentStrings: V5TunerString.TString) {
        post {
            val newPoint = TunerPoint(computePointOffsetX(result), pointOffset, false, System.currentTimeMillis(), result, currentStrings)
            points.add(newPoint)
            val previousLastPint = lastBigPoint
            if (null != previousLastPint && enableTranslationAnim) {
                //animate
                performLastBigPointXTranslationAnimation(previousLastPint, newPoint)
            }
            if (!mp.isPlaying && V5TunerResult.Appraise.Perfect == newPoint.result.appraise()) {
                mp.start()
            }
            if (newPoint.result.appraise() == V5TunerResult.Appraise.Perfect) {
                lastBigPoint = newPoint
                onLastPointChanged?.invoke(newPoint.result)
                lastBigPointStayTimeMs = 2000L
                lastBigPointTimeoutJob?.cancel()
                lastBigPointTimeoutJob = coroutineScope?.launch {
                    delay(lastBigPointStayTimeMs)
                    lastBigPoint = null
                    onLastPointChanged?.invoke(null)
                }
            } else {
                if (lastBigPointTimeoutJob?.isActive != true) {
                    lastBigPoint = newPoint
                    onLastPointChanged?.invoke(newPoint.result)
                    lastBigPointStayTimeMs = 1000L
                    lastBigPointTimeoutJob = coroutineScope?.launch {
                        delay(lastBigPointStayTimeMs)
                        lastBigPoint = null
                        onLastPointChanged?.invoke(null)
                    }
                }
            }
            postInvalidate()
        }
    }

    private fun onAnimFrame(value: Int) {
        deltaH = value
        points.removeIf {
            it.yOffset = pointOffset + ((System.currentTimeMillis() - it.receivedTime) / 1000f * velocity).toInt()
            it.yOffset > height
        }
        postInvalidate()
    }

    private fun performLastBigPointXTranslationAnimation(previous: TunerPoint, newest: TunerPoint) {
        animatorLastPoint?.cancel()
        animatorLastPoint = ValueAnimator.ofInt(previous.xOffset, newest.xOffset).apply {
            duration = lastBigPointTranslationDurationMs
            addUpdateListener {
                newest.xOffset = it.animatedValue as Int
            }
            doOnEnd {
                newest.isAnimating = false
            }
            start()
            newest.isAnimating = true
        }
    }


    private fun computePointOffsetX(result: V5TunerResult): Int {
        val centerX = width / 2
        val ratio = result.result / V5TunerResult.MAX_RESULT
        return if (ratio != 0f) {
            centerX + ratio * centerX
        } else {
            centerX
        }.toInt()
    }

    companion object {
        private const val TAG = "TunerView"
        private val AppraiseColor = mapOf(
            V5TunerResult.Appraise.Perfect to 0xFF30F862.toInt(),
            V5TunerResult.Appraise.ToneUpSlightly to 0xFF92FEC2.toInt(),
            V5TunerResult.Appraise.ToneDownSlightly to 0xFF92FEC2.toInt(),
            V5TunerResult.Appraise.ToneUpSlightly2 to 0xFFF3943D.toInt(),
            V5TunerResult.Appraise.ToneDownSlightly2 to 0xFFF3943D.toInt(),
            V5TunerResult.Appraise.ToneUp to 0xFFF3533D.toInt(),
            V5TunerResult.Appraise.ToneDown to 0xFFF3533D.toInt(),
        )
    }
}

private data class TunerPoint(
    var xOffset: Int,
    var yOffset: Int,
    var isAnimating: Boolean,
    val receivedTime: Long,
    val result: V5TunerResult,
    val currentTunerStrings: V5TunerString.TString,
) {
    fun soundStringText(): String? {
        val stringIdxInCurrentTString = when (result.tunerMode) {
            V5TunerMode.Mode.String1 -> 5
            V5TunerMode.Mode.String2 -> 4
            V5TunerMode.Mode.String3 -> 3
            V5TunerMode.Mode.String4 -> 2
            V5TunerMode.Mode.String5 -> 1
            V5TunerMode.Mode.String6 -> 0
            else -> null
        }
        if (null == stringIdxInCurrentTString) {
            return null
        }
        return currentTunerStrings.stringNames.getOrNull(stringIdxInCurrentTString)
    }
}
