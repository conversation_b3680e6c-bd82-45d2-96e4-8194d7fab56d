package com.harman.partyband.tuner

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5TunerBassMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerMode
import com.harman.v5protocol.bean.devinfofeat.V5TunerResult
import com.harman.v5protocol.bean.devinfofeat.V5TunerStartEnd
import com.harman.v5protocol.bean.devinfofeat.V5TunerString
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * @Description viewmodel for [TunerActivity]
 * <AUTHOR>
 * @Time 2025/1/6
 */
class TunerViewModel(state: SavedStateHandle) : ViewModel() {
    val device = DeviceStore.find(state.get<String>("uuid")!!) as? PartyBandDevice

    val currentTunerBassMode = MutableLiveData<V5TunerBassMode.Mode>()
    val isAuto = MutableLiveData<Boolean>()

    val currentActiveString = MutableLiveData<V5TunerMode.Mode?>(null)
    val tunerString = MutableLiveData<V5TunerString.TString>()
    val soundFlow = MutableSharedFlow<V5TunerResult>(extraBufferCapacity = 1)
    val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            super.onDevFeat(devInfoMap, isNotify)
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.TunerBassMode] as? V5TunerBassMode)?.also {
                    currentTunerBassMode.value = it.mode
                }
                (devInfoMap[V5DevInfoFeatID.TunerMode] as? V5TunerMode)?.also {
                    _currentTunerMode = it.mode
                    isAuto.value = it.mode.isAuto()
                    currentActiveString.value = if (it.mode.isAuto()) null else it.mode
                }
                (devInfoMap[V5DevInfoFeatID.TunerString] as? V5TunerString)?.also {
                    tunerString.value = it.string
                }
                (devInfoMap[V5DevInfoFeatID.TunerResult] as? V5TunerResult)?.also {
                    soundFlow.tryEmit(it)
                    if (isAuto.value == true) {
                        currentActiveString.value = if (it.hasSignal) it.tunerMode else null
                    }
                }
            }
        }
    }
    private var _currentTunerMode: V5TunerMode.Mode? = null

    init {
        device?.registerDeviceListener(deviceListener)
        device?.asyncGetDevInfoFeat(V5DevInfoFeatID.TunerBassMode, V5DevInfoFeatID.TunerMode, V5DevInfoFeatID.TunerString)
        switchTuner(true)
//        viewModelScope.launch {
//            while (isActive) {
//                val value = Random.nextDouble(-150.0, 150.0).toFloat()
//                val string = V5TunerMode.Mode.entries[Random.nextInt(1, 7)]
//                soundFlow.tryEmit(V5TunerResult(0f, string, value, true))
//                delay(100)
//            }
//        }
    }

    override fun onCleared() {
        super.onCleared()
        switchTuner(false)
        device?.unregisterDeviceListener(deviceListener)
    }

    private fun switchTuner(isStart: Boolean) {
        device?.asyncSetDevInfoFeat(V5TunerStartEnd(isStart))
    }

    fun switchTunerMode(mode: V5TunerMode.Mode, refreshNow: Boolean = false) {
        if (_currentTunerMode == mode) {
            return
        }
        device?.asyncSetDevInfoFeat(V5TunerMode(mode))
        if (refreshNow) {
            currentActiveString.value = if (mode.isAuto()) null else mode
        }
    }

    fun toggleAutoManualMode() {
        _currentTunerMode ?: return
        val ctbm = currentTunerBassMode.value ?: return
        when (_currentTunerMode) {
            V5TunerMode.Mode.Auto -> {
                when (ctbm) {
                    V5TunerBassMode.Mode.Guitar33,
                    V5TunerBassMode.Mode.Guitar6InLine -> switchTunerMode(V5TunerMode.Mode.String6)

                    V5TunerBassMode.Mode.Bass4String,
                    V5TunerBassMode.Mode.Ukulele -> switchTunerMode(V5TunerMode.Mode.String4)
                }
            }

            else -> switchTunerMode(V5TunerMode.Mode.Auto)
        }
    }
}