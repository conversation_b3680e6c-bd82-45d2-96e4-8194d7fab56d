package com.harman.partyband.output

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.app.ActivityCompat
import com.blankj.utilcode.util.Utils
import com.harman.discover.bean.PartyBandDevice
import com.harman.jblrecord.JblAudioFormat
import com.harman.jblrecord.JblChannelCount
import com.harman.jblrecord.JblMultiChannelRecord
import com.harman.log.Logger
import com.harman.v5protocol.bean.devinfofeat.V5PrepareOtaPath
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.LinkedBlockingQueue
import kotlin.concurrent.thread
import kotlin.experimental.and

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: AudioRecordUseCase
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/13 9:29
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 9:29
 * @UpdateRemark:
 * @Version: 1.0
 */
class UACRecordUseCase(
    private val context: Context,
    private val device: PartyBandDevice? = null,
) : AbsRecordIF(), JblMultiChannelRecord.AudioCallback {
    /**
     * uac是4channel, 24bit/channel
     */
    companion object {
        val TAG = "UACRecordUseCase"
        private const val TIME_DELAY_PREPARE_UAC = 15 * 1000L
        private const val SAMPLERATE = 44100
        fun waveHead(
            sampleRate: Int = SAMPLERATE,
            channels: Int = 2,
            bitsPerSample: Int = 16,
//            waveFileSize: Int,//pcmfilesize+44
            pcmFileSize: Int,
        ): ByteArray {
            val header = ByteArray(44)

            // ChunkID "RIFF"
            header[0] = 'R'.code.toByte()
            header[1] = 'I'.code.toByte()
            header[2] = 'F'.code.toByte()
            header[3] = 'F'.code.toByte()

            // ChunkSize (文件大小 - 8)
            val waveFileSize = pcmFileSize + 44
            val chunkSize = waveFileSize - 8 //waveFileSize=pcmfilesize+44
            header[4] = (chunkSize and 0xff).toByte()
            header[5] = ((chunkSize shr 8) and 0xff).toByte()
            header[6] = ((chunkSize shr 16) and 0xff).toByte()
            header[7] = ((chunkSize shr 24) and 0xff).toByte()

            // Format "WAVE"
            header[8] = 'W'.code.toByte()
            header[9] = 'A'.code.toByte()
            header[10] = 'V'.code.toByte()
            header[11] = 'E'.code.toByte()

            // Subchunk1ID "fmt "
            header[12] = 'f'.code.toByte()
            header[13] = 'm'.code.toByte()
            header[14] = 't'.code.toByte()
            header[15] = ' '.code.toByte()

            // Subchunk1Size 16
            header[16] = 16
            header[17] = 0
            header[18] = 0
            header[19] = 0

            // AudioFormat 1 (PCM)
            header[20] = 1
            header[21] = 0

            // NumChannels
            header[22] = channels.toByte()
            header[23] = 0

            // SampleRate
            header[24] = (sampleRate and 0xff).toByte()
            header[25] = ((sampleRate shr 8) and 0xff).toByte()
            header[26] = ((sampleRate shr 16) and 0xff).toByte()
            header[27] = ((sampleRate shr 24) and 0xff).toByte()

            // ByteRate
            val byteRate = sampleRate * channels * bitsPerSample / 8
            header[28] = (byteRate and 0xff).toByte()
            header[29] = ((byteRate shr 8) and 0xff).toByte()
            header[30] = ((byteRate shr 16) and 0xff).toByte()
            header[31] = ((byteRate shr 24) and 0xff).toByte()

            // BlockAlign
            val blockAlign = (channels * bitsPerSample / 8).toShort()
            header[32] = (blockAlign and 0xff).toByte()
            header[33] = ((blockAlign.toInt() shr 8) and 0xff).toByte()

            // BitsPerSample
            header[34] = bitsPerSample.toByte()
            header[35] = 0

            // Subchunk2ID "data"
            header[36] = 'd'.code.toByte()
            header[37] = 'a'.code.toByte()
            header[38] = 't'.code.toByte()
            header[39] = 'a'.code.toByte()

            // Subchunk2Size (音频数据大小)
            val subchunk2Size = pcmFileSize
            header[40] = (subchunk2Size and 0xff).toByte()
            header[41] = ((subchunk2Size shr 8) and 0xff).toByte()
            header[42] = ((subchunk2Size shr 16) and 0xff).toByte()
            header[43] = ((subchunk2Size shr 24) and 0xff).toByte()
            return header
        }
    }

    private val mPcmFilePath: String =
        Utils.getApp().cacheDir.absolutePath + File.separator + "audio_record_cache.pcm"
    private var pcmFileOutputStream: FileOutputStream? = null

    private var saveFileJob: Job? = null
    private var recordingTimeJob: Job? = null
    private val audioStore by lazy {
        AudioStore(Utils.getApp())
    }

    private val _audioData = MutableSharedFlow<ByteArray>()
    val audioData: SharedFlow<ByteArray> = _audioData.asSharedFlow()

    private val _audioRecordState = MutableStateFlow<AudioRecordState>(AudioRecordState.Idle)
    val audoiRecordState: SharedFlow<AudioRecordState> = _audioRecordState.asSharedFlow()
    val _recordingTime = MutableStateFlow<Int>(0)

    private var uacRealTimeSaveFileThread: Thread? = null
    private val writePcmQueue = LinkedBlockingQueue<ByteArray>()

    private val sampleRate: Int = 48000
    private var channelCount = JblChannelCount.Channel4
    private val audioFormat = JblAudioFormat.I24
    private val multiChannelRecord = JblMultiChannelRecord()

    private var startTime = System.currentTimeMillis()
    private var totalSize = 0
    private var logTime = System.currentTimeMillis()

    override fun setSeparateIsOpen(isOpen: Boolean) {
        super.setSeparateIsOpen(isOpen)
        channelCount = if (isOpen) JblChannelCount.Channel4 else JblChannelCount.Stereo
    }

    override suspend fun startRecording() {
        try {
            Logger.i(TAG, "startRecording")
            if (ActivityCompat.checkSelfPermission(
                    Utils.getApp(),
                    Manifest.permission.RECORD_AUDIO
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                Logger.e(TAG, "not permission")
                _audioRecordState.value = AudioRecordState.DontConnectDevice
                cancelRecording()
                return
            }
            multiChannelRecord.init(
                context = Utils.getApp(),
                sampleRate = sampleRate,
                channels = JblChannelCount.Channel4.value,
                format = audioFormat.value,
            )

            val v5PrepareOtaPath = device?.getDevInfoFeat<V5PrepareOtaPath>()
            Logger.i(TAG, "startRecording V5PrepareOtaPath=${v5PrepareOtaPath?.enable}")
            if (v5PrepareOtaPath?.enable == true) {//is iap mode
                val status =
                    device?.setDevInfoFeat(V5PrepareOtaPath(false))//switch to uac1
                Logger.i(TAG, "setUsbModel(uac) isSuccess=${status?.isSuccess()}")
                if (status?.isSuccess() != true) {
                    _audioRecordState.value = AudioRecordState.DontConnectDevice
                    cancelRecording()
                    return
                }
                Logger.i(TAG, "Please wait 15 seconds(uac)")
                _audioRecordState.value = AudioRecordState.SwitchUsbModel
                delay(TIME_DELAY_PREPARE_UAC)
            }
            _audioRecordState.value = AudioRecordState.DelayIsOver
            startMultiChanelRecord()
        } catch (e: Exception) {
            e.printStackTrace()
            Logger.e(TAG, e.message)
            cancelRecording()
        }
    }



    override fun onAudioReady(audioData: ByteArray) {
        writePcmQueue.offer(audioData.copyOf())
        totalSize += audioData.size
        val totalTime = System.currentTimeMillis() - startTime
        if (System.currentTimeMillis() - logTime > 5000) {
            logTime = System.currentTimeMillis()
            val speed = (totalSize / (totalTime / 1000f)) / 1024f
            Log.i(
                TAG,
                "onAudioReady=queueSize=${writePcmQueue.size}," +
                        "datasize=${audioData.size}," +
                        "speed=${speed.toInt()}KB/s," +
                        "totalFileSize=${(totalSize/1024f).toInt()}KB}"//${audioData.toHexString()
            )
        }
    }


    private fun startMultiChanelRecord() {
        recordingTime()
        realtimeSaveFileThreadStart()
        startTime = System.currentTimeMillis()
        totalSize = 0
        logTime = System.currentTimeMillis()
        File(mPcmFilePath).apply {
            if (this.exists()) {
                this.delete()
            }
        }
        pcmFileOutputStream = FileOutputStream(mPcmFilePath)
        _audioRecordState.value = AudioRecordState.Recording
        multiChannelRecord.startRecording(this)
    }


    @OptIn(DelicateCoroutinesApi::class)
    private fun realtimeSaveFileThreadStart() {
        uacRealTimeSaveFileThread?.interrupt()
        uacRealTimeSaveFileThread = thread {
            Logger.d(TAG, "realtime save pcm file thread start")
            while (!Thread.currentThread().isInterrupted) {
                try {
                    val bytes = writePcmQueue.take()
                    pcmFileOutputStream?.apply {
                        bytes?.let { data ->
                            this.write(data, 0, data.size)
                            GlobalScope.launch(Dispatchers.IO) {
                                _audioData.emit(data.copyOf(data.size))
                            }
                        }
                    }
                } catch (e: InterruptedException) {
                    Logger.d(TAG, "realtime save pcm file thread is interrupted")
                    break
                } catch (e: Exception) {
                    Logger.d(TAG, "realtime save pcm file thread failed")
                }

            }
            Logger.d(TAG, "realtime save pcm file thread finish")
        }
    }


    override suspend fun cancelRecording() {
        Logger.i(TAG, "cancelRecording")
        stopRecording()
        _audioRecordState.value = AudioRecordState.Idle
    }


    override suspend fun stopRecording() {
        Logger.i(TAG, "stopRecording")
        Logger.i(TAG, "writeQueue size=${writePcmQueue.size}")
        multiChannelRecord.stopRecording()
        recordingTimeJob?.cancel()
        uacRealTimeSaveFileThread?.interrupt()
        uacRealTimeSaveFileThread = null
        runCatching {
            pcmFileOutputStream?.flush()
            pcmFileOutputStream?.close()
            pcmFileOutputStream = null
        }.onFailure {
            it.printStackTrace()
        }
    }

    override suspend fun saveRecordFile(fileName: String?) {
        _audioRecordState.value = AudioRecordState.SaveFileStart
        withContext(Dispatchers.IO) {
            saveFileJob = launch {
                saveRecording(fileName)
            }
        }
    }

    override fun destory() {
        multiChannelRecord.stopRecording()
        recordingTimeJob?.cancel()
        uacRealTimeSaveFileThread?.interrupt()
        uacRealTimeSaveFileThread = null
        runCatching {
            pcmFileOutputStream?.flush()
            pcmFileOutputStream?.close()
            pcmFileOutputStream = null
        }.onFailure {
            it.printStackTrace()
        }
        multiChannelRecord.exitRecord()
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun recordingTime() {
        _recordingTime.value = 0
        recordingTimeJob?.cancel()
        recordingTimeJob = GlobalScope.launch {
            while (isActive) {
                delay(1000)
                _recordingTime.value += 1000
                Logger.d(TAG, "recordingTime=${_recordingTime.value}")
            }
        }
    }


    private suspend fun saveRecording(fileName: String?) {
        runCatching {
            if (fileName.isNullOrEmpty()) {
                Logger.i(TAG, "saveRecording fileName is null =${fileName}")
                return@runCatching
            }
            audioStore.insertAudio(RecordAudioBean(displayName = fileName)).collect { uri ->
                if (uri == null) {
                    Logger.i(TAG, "uri is null =${uri}")
                    return@collect
                }
                Logger.i(TAG, "uri is  =${uri} filename=${fileName}")
                audioStore.getFileOutputStream(Utils.getApp(), uri)?.use { outs ->
                    val pcmFile = File(mPcmFilePath)
                    if (!pcmFile.exists()) {
                        Logger.i(TAG, "record audio cache is not exists")
                        return@collect
                    }
                    try {
                        val buffer = ByteArray(1024 * 1024 * 2)
                        val fin = FileInputStream(mPcmFilePath)
                        Logger.i(TAG, "pcm file total size=${fin.available()/1024}kb")
                        outs.write(
                            waveHead(
                                sampleRate = sampleRate,
                                channels = channelCount.value,
                                bitsPerSample = audioFormat.bitValue,
                                pcmFileSize = fin.available()
                            ), 0, 44
                        )

                        var bytesRead: Int
                        while (fin.read(buffer).also { bytesRead = it } != -1) {
                            val data = buffer.take(bytesRead).toByteArray()
                            outs.write(data, 0, data.size)
                        }
                        Logger.i(TAG, "save file complete")
                        _audioRecordState.value = AudioRecordState.SaveFileFinish
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    _audioRecordState.value = AudioRecordState.Idle
                }
            }
        }.onFailure {
            it.printStackTrace()
            Logger.e(TAG, "$it")
            _audioRecordState.value = AudioRecordState.Idle
        }
    }


}