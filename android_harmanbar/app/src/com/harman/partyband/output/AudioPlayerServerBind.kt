package com.harman.partyband.output

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.net.Uri
import android.os.IBinder
import com.blankj.utilcode.util.Utils

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: AudioPlayerServerBind
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/10 16:55
 * @UpdateUser:
 * @UpdateDate: 2025/1/10 16:55
 * @UpdateRemark:
 * @Version: 1.0
 */
class AudioPlayerServerBind private constructor() : ServiceConnection {

    companion object {
        @Volatile
        private var ins: AudioPlayerServerBind? = null

        fun getIns() =
            ins ?: synchronized(this) {
                ins ?: AudioPlayerServerBind().also { ins = it }
            }
    }

    private var audioServiceOnBind: AudioPlayerServer? = null
    private var isBinder: Boolean = false

    var onServiceConnected: (() -> Unit)? = null

    var onServiceDisconnected: (() -> Unit)? = null


    override fun onServiceDisconnected(name: ComponentName) {
        isBinder = false
        audioServiceOnBind = null
        onServiceDisconnected?.invoke()
    }

    override fun onServiceConnected(name: ComponentName, binder: IBinder) {
        isBinder = true
        audioServiceOnBind = (binder as AudioPlayerServer.AudioBinder).service
        onServiceConnected?.invoke()
    }

    fun play()=audioServiceOnBind?.play()
    fun pause()=audioServiceOnBind?.pause()
    fun seekTo(position: Int) = audioServiceOnBind?.seekTo(position)
    fun stop() = audioServiceOnBind?.stop()
    fun switchMusic(uri: Uri) = audioServiceOnBind?.switchMusic(uri)
    fun currentPosition(): Int? = audioServiceOnBind?.currentPosition()
    fun getDuration(): Int? = audioServiceOnBind?.getDuration()
    fun currentUri(): Uri? = audioServiceOnBind?.currentUri
    fun isPlaying(): Boolean = audioServiceOnBind?.isPlaying() == true


    fun bindAudioPlayServer() {
        val intentService = Intent(
            Utils.getApp(),
            AudioPlayerServer::class.java
        )
        Utils.getApp().bindService(intentService, this, Context.BIND_AUTO_CREATE)
    }

    fun unbindService() {
        runCatching {
            if (isBinder) {
                Utils.getApp().unbindService(this)
            }
        }.onFailure {
            it.printStackTrace()
        }

    }

//    fun startAudioPlayerServer() {
//        val intentService = Intent(
//            Utils.getApp(),
//            AudioPlayerServer::class.java
//        )
//        Utils.getApp().startService(intentService)
//    }
//
//    fun stopAudioPlayerServer() {
//        val intentService = Intent(
//            Utils.getApp(),
//            AudioPlayerServer::class.java
//        )
//        Utils.getApp().stopService(intentService)
//    }
}