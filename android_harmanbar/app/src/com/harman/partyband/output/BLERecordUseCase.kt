package com.harman.partyband.output

import android.content.Context
import android.icu.util.Calendar
import com.harman.discover.bean.PartyBandDevice
import com.harman.log.Logger
import com.harman.partyband.output.UACRecordUseCase.Companion
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.v5protocol.bean.V5Status
import com.harman.v5protocol.bean.devinfofeat.V5OutputRecordingStartStopFeature
import com.harman.v5protocol.bean.devinfofeat.V5SyncTimeFeature
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlin.random.Random
import kotlin.random.nextInt

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: BLERecordUseCase
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/13 10:05
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 10:05
 * @UpdateRemark:
 * @Version: 1.0
 */
class BLERecordUseCase(
    val context: Context,
    var device: PartyBandDevice? = null,
) : AbsRecordIF() {
    companion object {
        const val TAG = "BLERecordUseCase"
    }

    private val _audioRecordState = MutableStateFlow<AudioRecordState>(AudioRecordState.Idle)
    val audoiRecordState: SharedFlow<AudioRecordState> = _audioRecordState.asSharedFlow()

    private val _audioData = MutableSharedFlow<ByteArray>()
    val audioData: SharedFlow<ByteArray> = _audioData.asSharedFlow()

    private var audioDataJob: Job? = null

    val _recordingTime = MutableStateFlow<Int>(0)
    private var recordingTimeJob: Job? = null

    private fun fakeAudioWave() {
        audioDataJob = GlobalScope.launch(DISPATCHER_DEFAULT) {
            while (isActive) {
                delay(50)
                val value = Random.nextInt(IntRange(0, Short.MAX_VALUE.toInt()))
                _audioData.emit(
                    byteArrayOf(
                        (value shr 8).and(0xFF).toByte(),
                        value.and(0xFF).toByte()
                    )
                )
            }
        }
    }

    override suspend fun startRecording() {
        fakeAudioWave()
        val retStatus=syncTime()
        if(retStatus?.isSuccess()==true){
            val status =
                device?.setDevInfoFeat(
                    V5OutputRecordingStartStopFeature(
                        V5OutputRecordingStartStopFeature.Recording.Start
                    )
                )
            if (status?.isSuccess() == true) {
                _audioRecordState.value = AudioRecordState.Recording
                recordingTime()
            } else {
                _audioRecordState.value = AudioRecordState.Error
                cancelRecording()
            }
            Logger.i(TAG, "startRecording status=${status?.isSuccess()}")
        }else{
            cancelRecording()
        }


    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun recordingTime() {
        _recordingTime.value=0
        recordingTimeJob?.cancel()
        recordingTimeJob = GlobalScope.launch {
            while (isActive) {
                delay(1000)
                _recordingTime.value += 1000
                Logger.d(TAG, "recordingTime=${_recordingTime.value}")
            }
        }
    }

    override suspend fun stopRecording() {
        recordingTimeJob?.cancel()
        audioDataJob?.cancel()
        val status =
            device?.setDevInfoFeat(
                V5OutputRecordingStartStopFeature(
                    V5OutputRecordingStartStopFeature.Recording.Stop
                )
            )
        _audioRecordState.value = AudioRecordState.Idle
        Logger.i(TAG, "stopRecording status=${status?.isSuccess()}")
    }
    override suspend fun saveRecordFile(fileName: String?){

    }

    override fun destory() {
        _audioRecordState.value = AudioRecordState.Idle
        audioDataJob?.cancel()
        recordingTimeJob?.cancel()
    }

    override suspend fun cancelRecording() {
        destory()
    }

    private suspend fun syncTime(): V5Status? {
        val calendar: Calendar = Calendar.getInstance()
        val year: Int = calendar.get(Calendar.YEAR)
        val month: Int = calendar.get(Calendar.MONTH) + 1 // 注意月份是从0开始的
        val day: Int = calendar.get(Calendar.DAY_OF_MONTH)
        val hour: Int = calendar.get(Calendar.HOUR_OF_DAY) // 24
        val minute: Int = calendar.get(Calendar.MINUTE)
        val second: Int = calendar.get(Calendar.SECOND)
        //SUNDAY(1), MONDAY(2), TUESDAY(3), WEDNESDAY(4), THURSDAY(5), FRIDAY(6), SATURDAY(7)
        val weekday: Int = calendar.get(Calendar.DAY_OF_WEEK) - 1
        val dayOfWeek = arrayOf(7, 1, 2, 3, 4, 5, 6)[weekday]
        val status = device?.setDevInfoFeat(
            V5SyncTimeFeature(
                second = second,
                minute = minute,
                hour = hour,
                dayOfWeek = dayOfWeek,
                day = day,
                month = month,
                year = year,
                timeFormat = V5SyncTimeFeature.TimeFormat.Hour24
            )
        )
        Logger.i(TAG, "syncTime() isSuccess=${status?.isSuccess()}")
        return status
    }

}