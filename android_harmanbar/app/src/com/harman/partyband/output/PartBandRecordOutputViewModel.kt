package com.harman.partyband.output

import android.content.Intent
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.harman.bar.app.R
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.log.Logger
import com.harman.partyband.looper.combine
import com.harman.partyband.widget.GuitarLooperCircleButton
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5AudioSource
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5LeftDeviceSerialNumberFeature
import com.harman.v5protocol.bean.devinfofeat.V5OutputRecordStorageLocationFeature
import com.harman.v5protocol.bean.devinfofeat.V5OutputSeparateTracksEnableFeature
import com.harman.v5protocol.bean.devinfofeat.V5USBStatusFeature
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: PartBandRecordOutputViewModel
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/13 9:50
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 9:50
 * @UpdateRemark:
 * @Version: 1.0
 */
class PartBandRecordOutputViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {

    companion object {
        val TAG = "PartBandRecordOutputViewModel"
    }

    private val _showToast = MutableStateFlow<String?>(null)
    val showToast = _showToast.asStateFlow()

    private val _audioWaveValues = MutableStateFlow<List<Float>>(listOf(0f))
    val audioWaveValues = _audioWaveValues.asStateFlow()

    val delAudioFlow = MutableSharedFlow<RecordAudioBean?>()


    val device =
        DeviceStore.find(savedStateHandle.get<String>("uuid")!!) as? PartyBandDevice

    private val audioStore by lazy {
        AudioStore(Utils.getApp())
    }
    private val bleRecord by lazy {
        BLERecordUseCase(Utils.getApp(), device)
    }
    private val uacRecord by lazy {
        UACRecordUseCase(Utils.getApp(), device)
    }

    //    private val iapRecord by lazy {
//        IAPRecordUseCase(device)
//    }
    private val usbMonitor by lazy {
        UsbStatusMonitor(device)
    }
    private val waveProcessor by lazy {
        WaveformProcessor()
    }

    private var refreshPlayAudioJob: Job? = null
    private val mAudioPlayerServerBind = AudioPlayerServerBind.getIns()

    private val _loading = MutableStateFlow(false)
    private val _outPutTo = MutableStateFlow(OutputTo.ThisPhone)
    private val _butType = MutableStateFlow(GuitarLooperCircleButton.Type.READY)
    private val _separateOutput = MutableStateFlow(SeparateOutput.OFF)//off=uac on=iap

    //    private val _modelType = MutableStateFlow(DeviceModelType.ModelM)//怎么区分models和modelm？后面有设备了确认
    private val _usbAttached = usbMonitor.usbDeviceAttached
    private val _withSound = MutableStateFlow(false)//wave anim有两种样式 真实的/假的 后期需求确认
    private val _audioFiles = MutableStateFlow<List<RecordAudioBean>>(emptyList())
    private val _audioRecordState = MutableStateFlow<AudioRecordState>(AudioRecordState.Idle)
    private val _recordingTime = MutableStateFlow<Int>(0)

    private val _usbStatus =
        MutableStateFlow(UsbStatus(uacInserted = false, usbFlashDiskInserted = false))
    private val _warningStrBean = MutableStateFlow(WarningStr("", ""))
    val audioDeleteRequest = MutableLiveData<RecordAudioBean>()
    private val _audioDisconnect = MutableLiveData<Boolean>()
    var audioDisconnect: LiveData<Boolean> = _audioDisconnect

    val screenData: StateFlow<ScreenData> = combine(
        _loading,
        _outPutTo,
        _butType,
        _separateOutput,
        _recordingTime,
        _withSound,
        _audioFiles,
        _audioRecordState,
        _warningStrBean,
        _usbStatus,
    ) { loading,
        outPutTo,
        butType,
        separateOutput,
        recordingTime,
        withSound,
        audioFiles,
        audioRecordState,
        warningStr,
        usbStatus ->
        val audioList = if (outPutTo == OutputTo.ThisPhone) {
            audioFiles
        } else {
            emptyList()
        }
        val flashDriverWarning = if (usbStatus.usbFlashDiskInserted) {
            ""
        } else {
            Utils.getApp().getString(R.string.please_connect_your_flash)
        }
        ScreenData.Success(
            loading = loading,
            outPutTo = outPutTo,
            butType = butType,
            separateOutput = separateOutput,
            audioFiles = audioList,
            recordingTime = recordingTime,
            withSound = withSound,
            audioRecordState = audioRecordState,
            warningStr = warningStr.copy(usbFlashDiskWarning = flashDriverWarning),
            usbStatus = usbStatus,
        )

    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5_000), ScreenData.Loading)


    private val deviceListener = object : IV5GattListener {
        //commandid=0x0003 in Child thread
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            devInfoMap[V5DevInfoFeatID.USBStatusFeature]?.run {
                val usbStatus = this as V5USBStatusFeature
                Logger.i(TAG, "notify=${usbStatus}")
                updateUsbStatus(usbStatus)//fixbug JBLPBMS-384
                _audioDisconnect.postValue(usbStatus.status == V5USBStatusFeature.Status.NOT_INSERTED)
            }
            (devInfoMap[V5DevInfoFeatID.OutputSeparateTracksEnableFeature] as? V5OutputSeparateTracksEnableFeature)?.run {
                Logger.i(TAG,"OutputSeparateTracksEnableFeature status = ${this.status}")
                _separateOutput.value = when(status){
                    V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Enable -> SeparateOutput.ON
                    V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Disable -> SeparateOutput.OFF
                }
            }
        }
    }

    init {
        Logger.i(TAG, "device=${device}")
        usbMonitor.startMonitor()
        device?.registerDeviceListener(deviceListener)
        //monitor record audiowavedata
        viewModelScope.launch {
            uacRecord.audioData.collect { recordData ->
                //do wave data
                val waveValue = waveProcessor.processAudioDataToWave(recordData)
                waveDataBack(waveValue, RecordSource.UAC)
            }
        }
        viewModelScope.launch {
            bleRecord.audioData.collect { recordData ->
                //do wave data
                val waveValue = waveProcessor.processAudioDataToWave(recordData)
                waveDataBack(waveValue, RecordSource.BLE)
            }
        }

        //monitor record state
        viewModelScope.launch {
            uacRecord.audoiRecordState.collect {
                Logger.i(TAG, "iapRecord status=${it}")
                processRecordStatus(it, RecordSource.UAC)
            }
        }
        viewModelScope.launch {
            bleRecord.audoiRecordState.collect {
                Logger.i(TAG, "iapRecord status=${it}")
                processRecordStatus(it, RecordSource.BLE)
            }
        }
        //record time
        viewModelScope.launch {
            bleRecord._recordingTime.collect {
                _recordingTime.value = it
            }
        }
        viewModelScope.launch {
            uacRecord._recordingTime.collect {
                _recordingTime.value = it
            }
        }
        //
        viewModelScope.launch {
            _usbAttached.collect { attached ->
                Logger.i(TAG, "usbAttached=${attached}")
                _usbStatus.update {
                    it.copy(uacInserted = attached)
                }
                when (recordSource()) {
                    RecordSource.UAC -> {
                        handEvent(Event.CancelRecordEvent)
                    }

                    RecordSource.BLE -> {}
                }
                if (!attached) {
                    _warningStrBean.update {
                        it.copy(
                            uacWarning = Utils.getApp()
                                .getString(R.string.please_connect_your_phone)
                        )
                    }
                    handEvent(Event.CancelRecordEvent)
                } else {
                    _warningStrBean.update {
                        it.copy(uacWarning = "")
                    }
                }
            }
        }
        _usbAttached.value = usbMonitor.findUsbDevice() == true
        readAudioFiles()
        getInitFeature()
        mAudioPlayerServerBind.bindAudioPlayServer()
        refreshPlayAudioItem()
        Logger.i(TAG, "init end")
    }

    private fun updateUsbStatus(v5UsbStatusFeature: V5USBStatusFeature) {

        when (v5UsbStatusFeature.audioSource) {
            V5AudioSource.USBFlashDisk -> {
                _usbStatus.update {
                    it.copy(usbFlashDiskInserted = v5UsbStatusFeature.status == V5USBStatusFeature.Status.INSERTED)
                }
            }

            V5AudioSource.UAC -> {
                _usbStatus.update {
                    it.copy(uacInserted = v5UsbStatusFeature.status == V5USBStatusFeature.Status.INSERTED)
                }
            }

            else -> {}
        }
    }

    private fun waveDataBack(waveValue: Float, source: RecordSource) {
        //if need realwave anim process here
        //Logger.i(TAG, "waveDataBack=${waveValue} source=$source")
        _audioWaveValues.value = _audioWaveValues.value.toMutableList().apply {
            this.add(waveValue)
        }.takeLast(61)
    }

    private fun processRecordStatus(audioRecordState: AudioRecordState, source: RecordSource) {
        Logger.i(TAG, "processRecordStatus=${audioRecordState} source=$source ${recordSource()}")
        if (recordSource() != source) {
            return
        }
        _audioRecordState.value = audioRecordState
        when (audioRecordState) {
            is AudioRecordState.Idle -> {
                readAudioFiles()
                _butType.value = GuitarLooperCircleButton.Type.READY
                _withSound.value = false
                _audioWaveValues.value = emptyList()
            }

            is AudioRecordState.Error -> {
                Logger.d(TAG, "AudioRecordState.Error=${audioRecordState}")
                _butType.value = GuitarLooperCircleButton.Type.READY
                _withSound.value = false
            }

            is AudioRecordState.Recording -> {
                _butType.value = GuitarLooperCircleButton.Type.RECORDING
                _withSound.value = true
                _warningStrBean.value = WarningStr("", "")
            }

            is AudioRecordState.SaveFileFinish -> {
                readAudioFiles()
            }

            AudioRecordState.DontConnectDevice -> {
                _warningStrBean.update {
                    it.copy(
                        uacWarning = Utils.getApp().getString(R.string.please_connect_your_phone)
                    )
                }
            }

            AudioRecordState.SwitchUsbModel -> {
                _warningStrBean.update {
                    it.copy(
                        uacWarning = Utils.getApp().getString(R.string.changing_output_source_it)
                    )
                }
            }

            AudioRecordState.SnMismatch -> {
                _warningStrBean.update {
                    it.copy(
                        uacWarning = Utils.getApp().getString(R.string.please_connect_this_phone)
                    )
                }
            }

            else -> {
                _butType.value = GuitarLooperCircleButton.Type.READY
                _withSound.value = false
            }
        }
    }


    private fun readAudioFiles() {
        viewModelScope.launch(Dispatchers.IO) {
            runCatching {
                audioStore.getAllAudios().collect {
                    _audioFiles.value = it.map { bean ->
                        bean.onClick = ::clickAudioItem
                        bean.onDelClick = ::delAudio
                        bean.onShareClick = ::shareAudio
                        bean
                    }
                }
            }.onFailure {
                it.printStackTrace()
            }
        }
    }


    private fun delAudio(model: RecordAudioBean) {
        Logger.d(TAG, "delAudio=${model}")
        viewModelScope.launch {
            delAudioFlow.emit(model)
        }
    }

    private fun deleteAudio(audioBean: RecordAudioBean) {
        viewModelScope.launch {
            audioStore.deleteAudio(audioBean).collect {
                if (it.first) {
                    audioBean.uri?.let {
                        mAudioPlayerServerBind.switchMusic(it)
                    }
                    Logger.d(TAG, "delAudio success")
                    readAudioFiles()
                } else {
                    Logger.d(TAG, "delAudio fail")
                    audioDeleteRequest.value = it.second
                }
            }
        }
    }

    private fun shareAudio(model: RecordAudioBean) {
        Logger.d(TAG, "shareAudio=${model}")
        val context = Utils.getApp()
        val shareIntent = Intent(Intent.ACTION_SEND);
        shareIntent.setType("audio/*");
        shareIntent.putExtra(Intent.EXTRA_STREAM, model.uri)
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION) // 授予临时读取权限
        val intent = Intent.createChooser(shareIntent, "Share Music")
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

    private fun clickAudioItem(model: RecordAudioBean) {
        Logger.i(TAG, "clickAudioItem=${model}")
        model.uri?.let {
            when (model.playStatus) {
                PlayStatus.Play -> {
                    mAudioPlayerServerBind.pause()
                    mAudioPlayerServerBind.seekTo(0)
                    _audioFiles.update { list ->
                        list.map { audioItem ->
                            audioItem.copy(playStatus = PlayStatus.Pause)
                        }
                    }
                }

                PlayStatus.Pause -> {
                    mAudioPlayerServerBind.play()
                    _audioFiles.update { list ->
                        list.map { audioItem ->
                            if (audioItem.id == model.id) {
                                audioItem.copy(playStatus = PlayStatus.Play)
                            } else {
                                audioItem
                            }
                        }
                    }
                }

                PlayStatus.Stop -> {
                    mAudioPlayerServerBind.switchMusic(it)
                    _audioFiles.update { list ->
                        list.map { audioItem ->
                            if (audioItem.id == model.id) {
                                audioItem.copy(playStatus = PlayStatus.Play)
                            } else {
                                audioItem
                            }
                        }
                    }
                }
            }
            refreshPlayAudioItem()
        }
    }

    private fun refreshPlayAudioItem() {
        refreshPlayAudioJob?.cancel()
        refreshPlayAudioJob = viewModelScope.launch {
            while (isActive) {
                delay(1000)
                val curPos = mAudioPlayerServerBind.currentPosition()
                val duration = mAudioPlayerServerBind.getDuration()
                val curUri = mAudioPlayerServerBind.currentUri()
                _audioFiles.update { list ->
                    list.map { audioItem ->
                        if (audioItem.uri == curUri) {
                            if (mAudioPlayerServerBind.isPlaying()) {
                                audioItem.copy(
                                    playStatus = PlayStatus.Play,
                                    currentPosition = curPos, duration = duration
                                )
                            } else {
                                audioItem.copy(
                                    currentPosition = curPos, duration = duration
                                )
                            }
                        } else {
                            audioItem.copy(
                                playStatus = PlayStatus.Stop,
                                currentPosition = 0,
                                duration = 0
                            )
                        }
                    }
                }
            }
        }
    }

    private fun switchOutputTo(outputTo: OutputTo) {
        _outPutTo.value = outputTo
        viewModelScope.launch {
            val location = when (outputTo) {
                OutputTo.ThisPhone -> V5OutputRecordStorageLocationFeature.OutputLocation.StoreOnPhone
                OutputTo.FlashDriver -> V5OutputRecordStorageLocationFeature.OutputLocation.StoreOnFlashDriver
            }
            val result = device?.setDevInfoFeat(V5OutputRecordStorageLocationFeature(location))
            Logger.i(TAG, "setOutputLocation ${location}...result isSuccess=${result?.isSuccess()}")
        }
    }

    private fun separateOutput(isChecked: Boolean) {
        viewModelScope.launch {
            _separateOutput.value = if (isChecked) {
                SeparateOutput.ON
            } else {
                SeparateOutput.OFF
            }
            val status = if (isChecked) {
                V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Enable
            } else {
                V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Disable
            }
            device?.setDevInfoFeat(V5OutputSeparateTracksEnableFeature(status))?.apply {
                getSeparateOutput()
                Logger.i(
                    TAG,
                    "separateOutput=${isChecked} status=${status} isSuccess=${this.isSuccess()}"
                )
            }
        }
    }

    private fun getInitFeature() {
        viewModelScope.launch {
            device?.getDevInfoFeat3<V5OutputSeparateTracksEnableFeature, V5OutputRecordStorageLocationFeature, V5USBStatusFeature>()
                .apply {
                    Logger.d(TAG, "getInitFeature result=${this}")
                    this?.first?.let {
                        _separateOutput.value = when (it.status) {
                            V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Enable -> SeparateOutput.ON
                            V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Disable -> SeparateOutput.OFF
                        }
                    }
                    this?.second?.let {
                        when (it.location) {
                            V5OutputRecordStorageLocationFeature.OutputLocation.StoreOnPhone -> _outPutTo.value =
                                OutputTo.ThisPhone

                            V5OutputRecordStorageLocationFeature.OutputLocation.StoreOnFlashDriver -> _outPutTo.value =
                                OutputTo.FlashDriver
                        }
                    }
                    this?.third?.let { usbStatus ->
                        updateUsbStatus(usbStatus)
                    }
                }
            //
//            try {
//                val serialNumber = device?.getDevInfoFeat<V5LeftDeviceSerialNumberFeature>()
//                //Logger.d(IAPRecordUseCase.TAG,"dut serialNumber=${this.partyBandUsbDevice?.serialNumber}")
//                Logger.d(TAG, "ble get sn=${serialNumber}")
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }

        }
    }

    private fun getSeparateOutput() {
        viewModelScope.launch {
            val result = device?.getDevInfoFeat<V5OutputSeparateTracksEnableFeature>()
            Logger.i(TAG, "getSeparateOutput result=${result}")
            result?.apply {
                _separateOutput.value = when (this.status) {
                    V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Enable -> SeparateOutput.ON
                    V5OutputSeparateTracksEnableFeature.SeparateTracksStatus.Disable -> SeparateOutput.OFF
                }
            }
        }
    }

    fun isRecording(): Boolean = _audioRecordState.value == AudioRecordState.Recording

    fun showToast(message: String? = null) {
        _showToast.value = message
    }

    fun recordSource(): RecordSource {
        return when (_outPutTo.value) {
            OutputTo.FlashDriver -> {
                RecordSource.BLE
            }

            OutputTo.ThisPhone -> {
                RecordSource.UAC
            }
        }

    }

    fun handEvent(event: Event) {
        Logger.i(TAG, "start handEvent= $event viewModelScope=${viewModelScope}")
        viewModelScope.launch(Dispatchers.Default) {
            Logger.i(TAG, "is main looper: ${Looper.myLooper() == Looper.getMainLooper()}")
            val sourceRecord = recordSource()
            Logger.i(TAG, "start handEvent sourceRecord=${sourceRecord}")
            val record = when (sourceRecord) {
                RecordSource.BLE -> bleRecord
                RecordSource.UAC -> uacRecord
            }
            when (event) {
                is Event.StartRecordEvent -> {
                    _recordingTime.value = 0
                    when (sourceRecord) {
                        RecordSource.BLE -> {
                            if (_usbStatus.value.usbFlashDiskInserted) {
                                record.setSeparateIsOpen(_separateOutput.value == SeparateOutput.ON)
                                record.startRecording()
                            } else {
                                Logger.i(TAG, "flash driver not Inserted")
                                val tips = Utils.getApp()
                                    .getString(R.string.please_connect_your_flash)
                                _warningStrBean.update {
                                    it.copy(
                                        usbFlashDiskWarning = tips
                                    )
                                }
                            }
                        }

                        RecordSource.UAC -> {
                            if (_usbStatus.value.uacInserted) {
                                record.setSeparateIsOpen(_separateOutput.value == SeparateOutput.ON)
                                record.startRecording()
                            } else {
                                Logger.i(TAG, "uac not plugged")
                                val tips = Utils.getApp()
                                    .getString(R.string.please_connect_this_phone)
                                _warningStrBean.update {
                                    it.copy(
                                        uacWarning = tips
                                    )
                                }
                            }
                        }
                    }
                }

                is Event.StopRecordEvent -> {
                    record.stopRecording()
                }

                is Event.SaveRecordEvent -> {
                    record.saveRecordFile(event.fileName)
                }

                is Event.SwitchOutputToEvent -> {
                    switchOutputTo(event.output)
                }

                is Event.SwitchSeparateOutputEvent -> {
                    separateOutput(event.isChecked)
                }

                is Event.CancelRecordEvent -> {
                    record.cancelRecording()
                }

                is Event.ReloadAudioEvent -> {
                    readAudioFiles()
                }

                is Event.DelAudioEvent -> {
                    deleteAudio(event.audioBean)
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        Logger.i(TAG, "onCleared start")
        refreshPlayAudioJob?.cancel()
        val sourceRecord = recordSource()
        Logger.i(TAG, "cancelRecording sourceRecord=${sourceRecord}")
        mAudioPlayerServerBind.onServiceConnected = null
        mAudioPlayerServerBind.onServiceDisconnected = null
        device?.unregisterDeviceListener(deviceListener)
        usbMonitor.stopMonitor()
        uacRecord.destory()
        bleRecord.destory()
        Logger.i(TAG, "onCleared end")
    }

    fun Long?.formatTimes(): String {
        this ?: return "00:00"

        val secs = this / 1000
        if (secs <= 0) {
            return "00:00"
        }

        val sec = secs % 60
        val min = secs / 60 % 60
        val hr = secs / 60 / 60

        return if (hr <= 0) {
            "%02d:%02d".format(min, sec)
        } else {
            "%02d:%02d:%02d".format(hr, min, sec)
        }
    }

    enum class RecordSource(val value: Int) {
        UAC(1), BLE(3)
    }

    enum class OutputTo(val value: Int) {
        ThisPhone(1), FlashDriver(2)
    }

    enum class SeparateOutput(val value: Int) {
        //Channels will be output as separate tracks
        OFF(0), ON(1)
    }

    enum class DeviceModelType(val value: Int) {
        ModelM(0), ModelS(1)
    }

    data class UsbStatus(val uacInserted: Boolean, val usbFlashDiskInserted: Boolean)
    data class WarningStr(val uacWarning: String, val usbFlashDiskWarning: String)

    sealed interface Event {
        data object StartRecordEvent : Event
        data object CancelRecordEvent : Event
        data object ReloadAudioEvent : Event
        data object StopRecordEvent : Event
        data class SaveRecordEvent(val fileName: String? = null) : Event
        data class SwitchOutputToEvent(val output: OutputTo) : Event
        data class SwitchSeparateOutputEvent(val isChecked: Boolean) : Event
        data class DelAudioEvent(val audioBean: RecordAudioBean) : Event
    }


    sealed interface ScreenData {
        data object Loading : ScreenData
        data class Success(
            val loading: Boolean = false,
            val outPutTo: OutputTo = OutputTo.FlashDriver,
            val butType: GuitarLooperCircleButton.Type = GuitarLooperCircleButton.Type.READY,
            val separateOutput: SeparateOutput = SeparateOutput.OFF,
            val audioFiles: List<RecordAudioBean> = emptyList(),
            val recordingTime: Int = 0,
            val withSound: Boolean = false,
            val audioRecordState: AudioRecordState = AudioRecordState.Idle,
            val warningStr: WarningStr,
            val usbStatus: UsbStatus,
        ) : ScreenData {
            override fun toString(): String {
                return "Success(loading=$loading, outPutTo=$outPutTo, butType=$butType, audioRecordState=$audioRecordState, separateOutput=$separateOutput, audioFiles=${audioFiles.size}, warningStr=$warningStr, withSound=$withSound) usbStatus=${usbStatus}"
            }
        }
    }
}