package com.harman.partyband.output

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: AudioRecordState
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/3/27 10:26
 * @UpdateUser:
 * @UpdateDate: 2025/3/27 10:26
 * @UpdateRemark:
 * @Version: 1.0
 */
sealed class AudioRecordState {
    data object Idle : AudioRecordState()
    data object SaveFileStart : AudioRecordState()
    data object SaveFileFinish : AudioRecordState()
    data object Recording : AudioRecordState()
    data object Error : AudioRecordState()
    data object SnMismatch : AudioRecordState()
    data object DontConnectDevice : AudioRecordState()
    data object SwitchUsbModel : AudioRecordState()
    data object DelayIsOver : AudioRecordState()

}