package com.harman.partyband.output

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: AudioRecordState
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/3/27 10:26
 * @UpdateUser:
 * @UpdateDate: 2025/3/27 10:26
 * @UpdateRemark:
 * @Version: 1.0
 */
interface RecordIF {
    suspend fun startRecording()
    suspend fun stopRecording()
    suspend fun cancelRecording()
    suspend fun saveRecordFile(fileName: String?)
    fun destory()
    fun setSeparateIsOpen(isOpen: Boolean)
}