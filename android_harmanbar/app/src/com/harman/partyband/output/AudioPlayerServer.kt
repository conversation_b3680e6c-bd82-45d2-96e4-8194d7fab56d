package com.harman.partyband.output

import android.app.Service
import android.content.Intent
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.net.Uri
import android.os.Binder
import android.os.IBinder
import com.harman.log.Logger

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: AudioPlayerServer
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/10 13:55
 * @UpdateUser:
 * @UpdateDate: 2025/1/10 13:55
 * @UpdateRemark:
 * @Version: 1.0
 */
class AudioPlayerServer : Service(), OnCompletionListener {
    companion object{
        private const val TAG = "AudioPlayerServer"
    }


    private val binder: IBinder = AudioBinder()
    private var mediaPlayer: MediaPlayer? = null

    var currentUri: Uri? = null

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        Logger.d(TAG, "onStartCommand()")
        return START_STICKY_COMPATIBILITY
    }

    override fun onBind(intent: Intent): IBinder? {
        Logger.d(TAG, "onBind()")
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        Logger.d(TAG, "onCreate()")
    }


    override fun onCompletion(mp: MediaPlayer) {
        Logger.d(TAG, "onCompletion()")
        //stopSelf()
    }

    override fun onDestroy() {
        destroyMediaPlay()
        Logger.d(TAG, "onDestroy()")
    }

    fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying == true
    }

    fun pause() {
        if (isPlaying()) {
            mediaPlayer?.pause()
        }
    }

    fun play() {
        if(!isPlaying()){
            mediaPlayer?.start()
        }
    }

    fun stop() {
        mediaPlayer?.stop()
    }

    fun currentPosition(): Int? {
        return mediaPlayer?.currentPosition
    }

    fun seekTo(position: Int){
        this.mediaPlayer?.seekTo(position)
    }

    fun getDuration(): Int? {
        return mediaPlayer?.duration
    }

    private fun destroyMediaPlay() {
        mediaPlayer?.stop()
        mediaPlayer?.release()
        mediaPlayer = null
    }

    fun switchMusic(uri: Uri) {
        currentUri = uri
        runCatching {
            if (mediaPlayer == null) {
                mediaPlayer = MediaPlayer.create(this, currentUri)
                mediaPlayer?.setOnCompletionListener(this)
                mediaPlayer?.isLooping = true
            }
        }.onFailure {
            it.printStackTrace()
        }

        runCatching {
            mediaPlayer?.stop()
            mediaPlayer?.reset()
            mediaPlayer?.setDataSource(this, currentUri!!)
            mediaPlayer?.prepare()
            mediaPlayer?.start()
            mediaPlayer?.isLooping = true
        }.onFailure {
            it.printStackTrace()
        }
        Logger.i(TAG, "switchMusic=${uri} mediaPlayer=${mediaPlayer}")
    }


    internal inner class AudioBinder : Binder() {
        val service: AudioPlayerServer
            get() = this@AudioPlayerServer
    }

}

