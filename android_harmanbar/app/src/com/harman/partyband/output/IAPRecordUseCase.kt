package com.harman.partyband.output

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbDeviceConnection
import android.hardware.usb.UsbEndpoint
import android.hardware.usb.UsbInterface
import android.hardware.usb.UsbManager
import android.hardware.usb.UsbRequest
import android.os.Build
import androidx.annotation.UiThread
import androidx.annotation.WorkerThread
import com.blankj.utilcode.util.Utils
import com.harman.discover.bean.PartyBandDevice
import com.harman.libusb.LibUsb
import com.harman.log.Logger
import com.harman.v5protocol.bean.devinfofeat.V5LeftDeviceSerialNumberFeature
import com.harman.v5protocol.bean.devinfofeat.V5OutputRecordingStartStopFeature
import com.harman.v5protocol.bean.devinfofeat.V5PrepareOtaPath
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.nio.ByteBuffer
import java.util.concurrent.LinkedBlockingQueue
import kotlin.concurrent.thread


/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: IAPRecordUseCase
 * @Description: refer PartbandOtaTask
 * @Author: mixie
 * @CreateDate: 2025/1/13 9:53
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 9:53
 * @UpdateRemark:
 * @Version: 1.0
 */
class IAPRecordUseCase(
    var device: PartyBandDevice? = null,
    private val usbInterfaceId: Int = 1,
    private val usbInterfaceAlternateSetting: Int = 1,
    private val usbEndpointInAddress: Int = 130,
    private val usbEndpointOutAddress: Int = 2,
) : AbsRecordIF() {

    companion object {
        const val TAG = "IAPRecordUseCase"
        private const val MTU_SIZE = 1512
        private const val TRANSFER_TIMEOUT = 1000L
        private const val TIME_DELAY_PREPARE_IPA = 15 * 1000L
    }

    private val mPcmFilePath: String =
        Utils.getApp().cacheDir.absolutePath + File.separator + "audio_record_cache.pcm"
    private var pcmFileOutputStream: FileOutputStream? = null
    private val _audioRecordState = MutableStateFlow<AudioRecordState>(AudioRecordState.Idle)
    private val audioStore by lazy {
        AudioStore(Utils.getApp())
    }
    private val _audioData = MutableSharedFlow<ByteArray?>()
    val audioData: SharedFlow<ByteArray?> = _audioData.asSharedFlow()
    val audoiRecordState: SharedFlow<AudioRecordState> = _audioRecordState.asSharedFlow()

    val usbDeviceAttached = MutableStateFlow(false)

    val _recordingTime = MutableStateFlow(0)


    private val um by lazy { Utils.getApp().getSystemService(UsbManager::class.java) }
    private var partyBandUsbDevice: UsbDevice? = null
    private var conn: UsbDeviceConnection? = null
    private var usbEndpointIn: UsbEndpoint? = null
    private var usbInterface: UsbInterface? = null


    private var saveFileJob: Job? = null
    private var recordingTimeJob: Job? = null

    private val writePcmQueue = LinkedBlockingQueue<ByteArray>()

    @Volatile
    private var startRecord: Boolean = false

    private val actionUsbPermission = "${Utils.getApp().packageName}.USB_PERMISSION"
    private var usbPermissionReqCompleter: CompletableDeferred<Unit>? = null
    private val usbPermissionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            usbPermissionReqCompleter?.complete(Unit)
        }
    }

    private val usbDeviceDetachedReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                val acion = intent?.action
                when (acion) {
                    UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                        usbDeviceAttached.value = true
                        Logger.d(TAG, "ACTION_USB_DEVICE_ATTACHED")
                    }

                    UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                        usbDeviceAttached.value = false
                        onDeviceDetached()
                        Logger.d(TAG, "ACTION_USB_DEVICE_DETACHED")
                    }

                }
            }
        }
    }

    @UiThread
    private fun onDeviceDetached() {
        //conn?.claimInterface(usbInterface, false)
        conn?.releaseInterface(usbInterface)
        conn?.close()
        conn = null
        LibUsb.close(usbInterfaceId)
    }

    init {
        Logger.d(TAG, "init")
        //The registered device is disconnected and listens to the broadcast
        val filter = IntentFilter()
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Utils.getApp().registerReceiver(
                usbDeviceDetachedReceiver,
                filter,
                Context.RECEIVER_EXPORTED
            )
        } else {
            Utils.getApp().registerReceiver(usbDeviceDetachedReceiver, filter)
        }
        //Register a USB device with permission to listen to broadcasts
        Utils.getApp().registerReceiver(
            usbPermissionReceiver,
            IntentFilter(actionUsbPermission),
            Context.RECEIVER_EXPORTED
        )
    }


    override suspend fun startRecording() {
        Logger.i(TAG, "startRecording")
        val v5PrepareOtaPath = device?.getDevInfoFeat<V5PrepareOtaPath>()
        Logger.i(TAG, "startRecording v5PrepareOtaPath=${v5PrepareOtaPath?.enable}")
        if (v5PrepareOtaPath?.enable != true) {//is uac mode
            val retStatus = device?.setDevInfoFeat(V5PrepareOtaPath(true))
            Logger.d(TAG, "swith to iap model ${retStatus?.isSuccess() == true}")
            if (retStatus?.isSuccess() != true) {
                _audioRecordState.value = AudioRecordState.DontConnectDevice
                cancelRecording()
                return
            }
            _audioRecordState.value = AudioRecordState.SwitchUsbModel
            Logger.d(TAG, "Please wait 15 seconds(iap)")
            delay(TIME_DELAY_PREPARE_IPA)
        }

        _audioRecordState.value = AudioRecordState.DelayIsOver
        record()
    }

    private var iapReadThread: Thread? = null
    private var iapRealTimeSaveFileThread: Thread? = null

    @OptIn(DelicateCoroutinesApi::class)
    private fun realtimeSaveFileThreadStart() {
        iapRealTimeSaveFileThread?.interrupt()
        iapRealTimeSaveFileThread = thread {
            Logger.d(TAG, "realtime save pcm file thread start")
            while (!Thread.currentThread().isInterrupted) {
                try {
                    val bytes = writePcmQueue.take()
                    pcmFileOutputStream?.apply {
                        bytes?.let { data ->
                            this.write(data, 0, data.size)
                            GlobalScope.launch(Dispatchers.IO) {
                                _audioData.emit(data.copyOf(data.size))
                                //Logger.i(TAG, "read=${data.size}")
                            }
                        }
                    }
                } catch (e: InterruptedException) {
                    Logger.d(TAG, "realtime save pcm file thread is interrupted")
                    break
                } catch (e: Exception) {
                    Logger.d(TAG, "realtime save pcm file thread failed")
                }

            }
            Logger.d(TAG, "realtime save pcm file thread finish")
        }
    }

    private suspend fun record() {

        val findDeviceRet = findUsbDevice()
        if (!findDeviceRet) {
            Logger.e(TAG, "Can Not Find Device")
            _audioRecordState.value = AudioRecordState.DontConnectDevice
            cancelRecording()
            return
        }

        val hasPermission = checkAndRequestPermission()
        if (!hasPermission) {
            Logger.e(TAG, "failed,No usb Permission")
            _audioRecordState.value = AudioRecordState.DontConnectDevice
            cancelRecording()
            return
        }

        //TODO 接口还没好暂时注释掉
        val serialNumber = device?.getDevInfoFeat<V5LeftDeviceSerialNumberFeature>()
        Logger.d(TAG, "dut serialNumber=${this.partyBandUsbDevice?.serialNumber}")
        Logger.d(TAG, "ble get sn=${serialNumber}")
        if (serialNumber == null ||
            !this.partyBandUsbDevice?.serialNumber.equals(serialNumber.leftDeivceSerialNum)
        ) {
            _audioRecordState.value = AudioRecordState.SnMismatch
            cancelRecording()
            return
        }


        val openRet = openDevice()
        if (!openRet) {
            Logger.e(TAG, "Open Device Fail")
            _audioRecordState.value = AudioRecordState.DontConnectDevice
            cancelRecording()
            return
        }
        val retStatus = device?.setDevInfoFeat(
            V5OutputRecordingStartStopFeature(
                V5OutputRecordingStartStopFeature.Recording.Start
            )
        )
        Logger.i(TAG, "send startcommand(0x3181) retStatus=${retStatus?.isSuccess() == true}")
        if (retStatus?.isSuccess() != true) {
            Logger.i(TAG, "send startcommand(0x3181) fail")
            _audioRecordState.value = AudioRecordState.DontConnectDevice
            cancelRecording()
            return
        }

        iapReadThread?.interrupt()
        iapReadThread = thread {
            val pcmFile = File(mPcmFilePath)
            if (pcmFile.exists()) {
                pcmFile.delete()
            }
            pcmFileOutputStream = FileOutputStream(mPcmFilePath)
            _audioRecordState.value = AudioRecordState.Recording
            var startReadTime = 0L
            var totalReadLength = 0
            var stepTime = System.currentTimeMillis()
            realtimeSaveFileThreadStart()
            Logger.i(TAG, "start read=${Thread.currentThread().isInterrupted}")
            while (!Thread.currentThread().isInterrupted) {
                /**
                 * TODO
                 * 单独使用LibUsb 读取速度稍慢，有丢包概率 使用官方api 速度快
                 * 但是官方读取api需要libusb初始化才行？？....(后面花时间排查)
                 */
//                val bufferData = readWithLibUsb(conn!!)//
                val bufferData = read()
                bufferData?.let { data ->
                    writePcmQueue.offer(data.copyOf())
                }
                //test code
                if ((bufferData?.size ?: 0) > 0 && startReadTime == 0L) {
                    startReadTime = System.currentTimeMillis()
                    recordingTime()
                }
                totalReadLength += bufferData?.size ?: 0
                val endReadTime = System.currentTimeMillis()
                val time = endReadTime - startReadTime
                if (System.currentTimeMillis() - stepTime > 3000 && time > 0) {
                    Logger.i(
                        TAG,
                        "read speed=${totalReadLength / (time / 1000F)} bufferData=${bufferData?.size} writeQueue=${writePcmQueue.size}"
                    )
                    stepTime = System.currentTimeMillis()
                }
            }
        }
    }


    @OptIn(DelicateCoroutinesApi::class)
    private fun recordingTime() {
        _recordingTime.value = 0
        recordingTimeJob?.cancel()
        recordingTimeJob = GlobalScope.launch {
            while (isActive) {
                delay(1000)
                _recordingTime.value += 1000
                //Logger.d(TAG, "recordingTime=${_recordingTime.value}")
            }
        }
    }


    override suspend fun stopRecording() {
        Logger.i(TAG, "stopRecording()")
        val retStatus = device?.setDevInfoFeat(
            V5OutputRecordingStartStopFeature(
                V5OutputRecordingStartStopFeature.Recording.Stop
            )
        )
        cancelRecording()
        Logger.i(TAG, "stopRecording =${retStatus?.isSuccess() == true}")


    }

    override suspend fun saveRecordFile(fileName: String?) {
        Logger.i(TAG, "saveRecordFile=${fileName}")
        withContext(Dispatchers.IO) {
            saveFileJob = launch {
                saveRecording(fileName)
            }
        }
    }

    override suspend fun cancelRecording() {
        Logger.i(TAG, "cancelRecording()")
//        val retStatus = device?.setDevInfoFeat(
//            V5OutputRecordingStartStopFeature(
//                V5OutputRecordingStartStopFeature.Recording.Stop
//            )
//        )
        iapReadThread?.interrupt()
        iapReadThread = null
        Logger.i(TAG, "writeQueue size=${writePcmQueue.size}")
        iapRealTimeSaveFileThread?.interrupt()
        iapRealTimeSaveFileThread = null
        recordingTimeJob?.cancel()
        recordingTimeJob = null
        runCatching {
            pcmFileOutputStream?.flush()
            pcmFileOutputStream?.close()
            pcmFileOutputStream = null
        }.onFailure {
            it.printStackTrace()
        }
        _audioRecordState.value = AudioRecordState.Idle
        Logger.i(TAG, "cancelRecording end")
    }

    @UiThread
    @OptIn(ExperimentalStdlibApi::class)
    fun findUsbDevice(): Boolean {
        val um = Utils.getApp().getSystemService(UsbManager::class.java)
        val deviceList = um.deviceList
        val partyBandUsbDevice = deviceList.toList().find {
            //Since the bottom layer of USB transmission is the PID and vendor ID stored in big-endian order, so the big-endian order is used as HEX
            val pidString =
                ByteBuffer.allocate(2).putShort(it.second.productId.toShort()).array().toHexString()
            val vendorIdString =
                ByteBuffer.allocate(2).putShort(it.second.vendorId.toShort()).array().toHexString()
            return@find pidString.equals(
                device?.pid,
                true
            ) && vendorIdString.equals(device?.bleDevice?.vendorID, true)
        }?.second
        this.partyBandUsbDevice = partyBandUsbDevice
        this.partyBandUsbDevice?.let {
            val count = it.interfaceCount
            repeat(count) { idx ->
                val usbIntarface = it.getInterface(idx)
                if (usbIntarface.id == usbInterfaceId &&
                    usbIntarface.alternateSetting == usbInterfaceAlternateSetting
                ) {
                    val endpointCount = usbIntarface.endpointCount
                    repeat(endpointCount) { endPointIdx ->
                        val endpoint = usbIntarface.getEndpoint(endPointIdx)
                        if (endpoint.address == usbEndpointInAddress) {
                            usbEndpointIn = endpoint
                            this.usbInterface = usbIntarface
                        }
                    }
                }
            }
            Logger.d(TAG, "device=${this.partyBandUsbDevice?.deviceId}")

//            repeat(count) { interfaceIdx ->
//                val usbIntarface = it.getInterface(interfaceIdx)
//                val endPointCount = usbIntarface.endpointCount
//                repeat(endPointCount) { endPointIdx ->
//                    val endpoint = usbIntarface.getEndpoint(endPointIdx)
//                    if (endpoint.address == usbEndpointInAddress) {
//                        usbEndpoint = endpoint
//                    }
//                }
//            }

        }
        Logger.d(TAG, "usbEndpoint=${usbEndpointIn}")
//        val interfaceUsb=partyBandUsbDevice!!.getInterface(2)
//        Logger.d(TAG,"interfaceUsb=${interfaceUsb}")
        Logger.i(TAG, "partyBandUsbDevice=${partyBandUsbDevice != null} ${this.partyBandUsbDevice}")
        return null != partyBandUsbDevice
    }


    /**
     * Check if the [partyBandUsbDevice] has permissions, and if not, try to request it once,This function can be called separately before calling [startFlow]
     * @return true if [partyBandUsbDevice] has permission
     */
    @UiThread
    private suspend fun checkAndRequestPermission(): Boolean {
        if (!um.hasPermission(partyBandUsbDevice)) {
            val permissionIntent = PendingIntent.getBroadcast(
                Utils.getApp(),
                0,
                Intent(actionUsbPermission),
                PendingIntent.FLAG_IMMUTABLE
            )
            //request permission
            um.requestPermission(partyBandUsbDevice, permissionIntent)
            //wait permission result
            usbPermissionReqCompleter = CompletableDeferred()
            usbPermissionReqCompleter!!.await()
        }
        return um.hasPermission(partyBandUsbDevice)
    }

    private fun openDevice(): Boolean {
        try {
            if (null != conn) {
                return true
            }
            val conn = um.openDevice(partyBandUsbDevice)
            Logger.i(TAG, "openDevice conn=${conn}")
            val initRet = LibUsb.init(
                conn.fileDescriptor,
                usbInterfaceId,
                usbInterfaceAlternateSetting
            )
            if (0 != initRet) {
                Logger.e(TAG, "LibUsb init failed")
            }
            <EMAIL> = conn
            return conn.claimInterface(
                usbInterface,
                true
            )
//            return true
        } catch (e: Exception) {
            Logger.e(TAG, "openDevice fail ${e.stackTraceToString()}")
            return false
        }
    }


    private var logTime = System.currentTimeMillis()
    private fun intervalLog(str: String) {
        if (System.currentTimeMillis() - logTime > 2000) {
            Logger.d(TAG, str)
            logTime = System.currentTimeMillis()
        }
    }

    fun read(): ByteArray? {
        val connection = this.conn
        if (connection == null) {
            intervalLog("read: connection is null")
            return null
        }
        if (partyBandUsbDevice == null) {
            intervalLog("read: partyBandUsbDevice is null")
            return null
        }
        if (usbEndpointIn == null) {
            intervalLog("read: usbEndpoint is null")
            return null
        }


        val request = UsbRequest()
        val initialized = request.initialize(
            connection,
            usbEndpointIn
        )
        if (!initialized) {
            intervalLog("Initialized error")
            return null
        }
        val buffer = ByteBuffer.allocate(2048)
        request.clientData = buffer

//        val queued=request.queue(buffer,buffer.remaining())
        val queued = request.queue(buffer)
        if (!queued) {
            intervalLog("Queue error")
            return null
        }

        val response: UsbRequest = connection.requestWait()
        if (response != request) {
            intervalLog("Request failed")
            return null
        }
        val receivedData = response.clientData as ByteBuffer
        val bytesRead = receivedData.position()
        val data = ByteArray(bytesRead)
        receivedData.rewind()
        receivedData[data, 0, bytesRead]
        return data
    }

    @OptIn(ExperimentalStdlibApi::class)
    @WorkerThread
    private fun readWithLibUsb(conn: UsbDeviceConnection): ByteArray? {
        runCatching {
            val array = ByteArray(MTU_SIZE)
            val ret = LibUsb.bulkTransferRead(
                conn.fileDescriptor,
                this.usbEndpointInAddress, array, array.size, TRANSFER_TIMEOUT.toInt()
            )
            val retArray = if (ret > 0) {
                // array.take(ret).toByteArray()
                array.copyOfRange(0, ret)
            } else null
            //Logger.d(TAG, "readWithLibUsb >>> , read length:$ret retarrysize=${retArray?.size} ${retArray?.toHexString()} ")
            return retArray

        }.onFailure {
            it.printStackTrace()
        }
        return null
    }

    override fun destory() {
        exit()
    }

    fun exit() {
        Logger.i(TAG, "exit()")
        try {
            iapReadThread?.interrupt()
            iapReadThread = null
            iapRealTimeSaveFileThread?.interrupt()
            iapRealTimeSaveFileThread = null
            startRecord = false
            //conn?.claimInterface(usbInterface, false)
            conn?.releaseInterface(usbInterface)
            conn?.close()
            conn = null
        } catch (e: Exception) {
            e.printStackTrace()
        }

        LibUsb.close(usbInterfaceId)
        Utils.getApp().unregisterReceiver(usbPermissionReceiver)
        Utils.getApp().unregisterReceiver(usbDeviceDetachedReceiver)
        Logger.i(TAG, "exit end")
    }


    private suspend fun saveRecording(fileName: String?) {
        runCatching {
            if (fileName.isNullOrEmpty()) {
                Logger.i(UACRecordUseCase.TAG, "saveRecording fileName is null =${fileName}")
                return@runCatching
            }
            audioStore.insertAudio(RecordAudioBean(displayName = fileName)).collect { uri ->
                if (uri == null) {
                    Logger.i(UACRecordUseCase.TAG, "uri is null =${uri}")
                    return@collect
                }
                audioStore.getFileOutputStream(Utils.getApp(), uri)?.let { outs ->
                    val pcmFile = File(mPcmFilePath)
                    if (!pcmFile.exists()) {
                        Logger.i(UACRecordUseCase.TAG, "record audio cache is not exists")
                        return@let
                    }

                    val bufferSize = 1024
                    val buffer = ByteArray(bufferSize)
                    try {
                        val fin = FileInputStream(mPcmFilePath)
                        val pcmFileLength = fin.available()
                        Logger.i(
                            TAG,
                            "Raw pcm file size=${fin.available()}:${pcmFile.length()} /4=${pcmFileLength / 4f}"
                        )
                        outs.write(
                            UACRecordUseCase.waveHead(
                                sampleRate = 48000,//44100,48000,48000
                                channels = 4,//4
                                bitsPerSample = 32,//32
                                pcmFileSize = fin.available()
                            ), 0, 44
                        )
                        Logger.d(TAG, "")

                        var bytesRead: Int
                        while (fin.read(buffer).also { bytesRead = it } != -1) {
                            val data = buffer.take(bytesRead).toByteArray()
                            outs.write(data, 0, data.size)
                        }
                        _audioRecordState.value = AudioRecordState.SaveFileFinish
                    } catch (e: IOException) {
                        e.printStackTrace()
                    } finally {
                        outs.flush()
                        outs.close()
                    }
                    Logger.i(TAG, "save iap record audio file")
                }
                _audioRecordState.value = AudioRecordState.Idle
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

}