package com.harman.partyband.output

import android.annotation.SuppressLint
import android.content.Intent
import android.icu.util.Calendar
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityInputFilenameBinding
import com.harman.partyband.looper.PartBandGuitarLooperDialog
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible
import com.harman.thread.DISPATCHER_IO
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File


/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: InputFileNameActivity
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/3/4 9:20
 * @UpdateUser:
 * @UpdateDate: 2025/3/4 9:20
 * @UpdateRemark:
 * @Version: 1.0
 */
class InputFileNameActivity : AppCompatBaseActivity() {

    companion object {
        const val INPUT_FILENAME_REQUEST_CODE = 3001
        const val FILENAME_KEY = "file_name"
        private const val TAG = "InputFileNameActivity"
//        fun launch(act: Activity, uuid: String) {
//            act.push<InputFileNameActivity>(
//                bundleOf(
//                    "uuid" to uuid,
//                )
//            )
//        }
    }

    private val binding by lazy {
        ActivityInputFilenameBinding.inflate(layoutInflater)
    }
    private val dialog: PartBandGuitarLooperDialog by lazy {
        PartBandGuitarLooperDialog(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.setContentView(binding.root)
        buildAppbar()
        buildEditText()
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        onBack()
    }

    private fun save() {
        val intent = Intent()
        intent.putExtra(FILENAME_KEY, binding.etInputFilename.text.toString())
        setResult(RESULT_OK, intent)
        finish()
    }

    private fun discard() {
        val intent = Intent()
        intent.putExtra(FILENAME_KEY, "")
        setResult(RESULT_OK, intent)
        finish()
    }


    private fun buildAppbar() {
        binding.appbar.tvTitle.text = getString(R.string.title_save_as)
        binding.appbar.ivLeading.setOnClickListener {
            onBack()
        }
    }

    private fun onBack() {
        dialog.setContent(
            getString(R.string.do_you_want_to),
            getString(R.string.discard),
            getString(R.string.jbl_CANCEL)
        )
        dialog.listener = object : PartBandGuitarLooperDialog.DialogIF {
            override fun onDismiss() {
                dialog.dismiss()
            }

            override fun onConfirm() {
                dialog.dismiss()
                save()
            }

        }
        dialog.show()
    }

    @SuppressLint("SetTextI18n")
    private fun buildEditText() {
        val calendar: Calendar = Calendar.getInstance()
        val year: Int = calendar.get(Calendar.YEAR)
        val month: Int = calendar.get(Calendar.MONTH) + 1 // 注意月份是从0开始的
        val day: Int = calendar.get(Calendar.DAY_OF_MONTH)
//        val hour: Int = calendar.get(Calendar.HOUR_OF_DAY) // 24
//        val minute: Int = calendar.get(Calendar.MINUTE)
//        val second: Int = calendar.get(Calendar.SECOND)
//        binding.etInputFilename.setText("${year}.${String.format("%02d",month)}.${String.format("%02d",day)}")
        initFileName("${year}.${String.format("%02d",month)}.${String.format("%02d",day)}")
        binding.etInputFilename.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    if (count > 0) {
                        binding.tvFilename.gone()
                        binding.ivClear.visible()
                    } else {
                        binding.ivClear.gone()
                        binding.tvFilename.visible()
                    }
                }

                override fun afterTextChanged(s: Editable?) {
                }

            }
        )
        binding.ivClear.setOnClickListener {
            binding.etInputFilename.setText("")
        }
        binding.btnSave.setOnClickListener {//save
            save()
        }
        binding.btCancel.setOnClickListener {//discard
            discard()
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        dialog.dismiss()
    }

    private fun initFileName(name: String) {
        lifecycleScope.launch(DISPATCHER_IO) {
            var index = 1
            val dirPath = "${Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)}${AudioStore.relativePath}"
            var fileName = "${name}_${String.format("%02d",index)}"
            runCatching {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
                    index --
                    do {
                        index ++
                        fileName = "${name}_${String.format("%02d",index)}"
                        val use = contentResolver.query(
                            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                            arrayOf(MediaStore.MediaColumns._ID),
                            MediaStore.Audio.Media.DISPLAY_NAME + "=?",
                            arrayOf("${fileName}.wav"),
                            null
                        )?.use {
                            it.moveToFirst()
                        }
                    }while (use == true)

                }else{
                    while (File(dirPath.substringBeforeLast("/"), "$fileName.wav").exists()){
                        index ++
                        fileName = "${name}_${String.format("%02d",index)}"
                    }
                }

                withContext(Dispatchers.Main){
                    binding.etInputFilename.setText(fileName)
                }
            }.onFailure {
                withContext(Dispatchers.Main){
                    binding.etInputFilename.setText(fileName)
                }
            }
        }
    }
}