package com.harman.partyband.output

import android.annotation.SuppressLint
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.bar.app.databinding.RecyclerItemUsbOutputAudiosBinding
import com.harman.BaseItemAdapter
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: RecordAudiosAdapter
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/6 14:43
 * @UpdateUser:
 * @UpdateDate: 2025/1/6 14:43
 * @UpdateRemark:
 * @Version: 1.0
 */
class RecordAudiosAdapter :
    ListAdapter<RecordAudioBean, RecordAudiosAdapter.ViewHolder>(DIFF_CALLBACK) {

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<RecordAudioBean>() {
            override fun areItemsTheSame(
                oldItem: RecordAudioBean,
                newItem: RecordAudioBean
            ): Boolean = (oldItem.displayName == newItem.displayName)

            override fun areContentsTheSame(
                oldItem: RecordAudioBean,
                newItem: RecordAudioBean
            ): Boolean = (oldItem == newItem)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(parent)

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindTo(getItem(position))
    }

    inner class ViewHolder(parent: ViewGroup) : RecyclerView.ViewHolder(
        LayoutInflater.from(parent.context)
            .inflate(R.layout.recycler_item_usb_output_audios, parent, false)
    ) {

        private val binding = RecyclerItemUsbOutputAudiosBinding.bind(itemView)

        fun bindTo(model: RecordAudioBean) {
            binding.tittle.text = "${model.displayName}"
            binding.tvPath.text = "${model.realPath}"
            binding.seekBar.max = model.duration ?: 0
            binding.seekBar.progress = model.currentPosition ?: 0
            binding.ivStatus.setImageResource(
                if (model.playStatus == PlayStatus.Play) {
                    R.drawable.ic_mini_player_pause
                } else {
                    R.drawable.ic_mini_player_play
                }
            )
            when(model.playStatus){
                PlayStatus.Play->binding.seekBar.visible()
                else->binding.seekBar.gone()
            }

            binding.ivStatus.setOnClickListener {
                model.onClick?.invoke(model)
            }
            binding.ivDel.setOnClickListener {
                model.onDelClick?.invoke(model)
            }
            binding.ivShare.setOnClickListener {
                model.onShareClick?.invoke(model)
            }

            Log.i("RecordAudiosAdapter", "==update=${model.displayName}")
        }
    }

}
