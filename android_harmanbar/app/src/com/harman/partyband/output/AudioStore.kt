package com.harman.partyband.output

import android.app.Activity
import android.app.RecoverableSecurityException
import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.content.IntentSender
import android.database.Cursor
import android.media.MediaExtractor
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.provider.Telephony.Mms.Intents
import com.harman.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.io.File
import java.io.OutputStream

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: AudioStore
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/13 9:24
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 9:24
 * @UpdateRemark:
 * @Version: 1.0
 */
class AudioStore(private val context: Context) {
    companion object {
        const val partybandStr="partyband"
        const val relativePath = "/${partybandStr}/"
        const val TAG="AudioStore"
        const val REQUEST_CODE_FOR_DELETE_AUDIO = 1003
    }

    fun getAllAudios(limit: Int = Integer.MAX_VALUE): Flow<List<RecordAudioBean>> = flow {
        val projection = arrayOf(
            MediaStore.MediaColumns._ID,
            MediaStore.MediaColumns.TITLE,
            MediaStore.MediaColumns.DISPLAY_NAME,
            MediaStore.MediaColumns.MIME_TYPE,
            MediaStore.MediaColumns.SIZE,
            MediaStore.MediaColumns.RELATIVE_PATH,
            MediaStore.MediaColumns.DATA,
            MediaStore.MediaColumns.DATE_MODIFIED,
            MediaStore.MediaColumns.DATE_ADDED,
        )

//        val selection = "${MediaStore.Audio.Media.IS_MUSIC} != 0"

        val selection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            "${MediaStore.MediaColumns.RELATIVE_PATH} = ?"
        } else {
            "${MediaStore.MediaColumns.DATA} = ?"
        }

        val selectionArgs = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            arrayOf("${Environment.DIRECTORY_MUSIC}${relativePath}")
        } else {
            arrayOf("${Environment.DIRECTORY_MUSIC}${relativePath}")//
        }

        val sortOrder = "${MediaStore.MediaColumns.DATE_ADDED} DESC"


        val audioList = mutableListOf<RecordAudioBean>()

        context.contentResolver.query(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            selectionArgs,
            sortOrder
        )?.use { cursor ->
            var count = 0
            while (cursor.moveToNext() && count < limit) {
                val audio = cursor.toAudioModel()
                audioList.add(audio)
                count++
            }
        }

        emit(audioList)
    }.flowOn(Dispatchers.IO)


    fun insertAudio(audio: RecordAudioBean): Flow<Uri?> = flow {
        val values = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, audio.displayName)
            put(MediaStore.MediaColumns.MIME_TYPE, "audio/*")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_MUSIC + "/${partybandStr}")
            } else {
                val directory = Environment.getExternalStoragePublicDirectory(
                    Environment.DIRECTORY_MUSIC
                )
                val customDir = File(directory, relativePath.substringBeforeLast('/'))
                if (!customDir.exists()) {
                    customDir.mkdirs()
                }
                audio.displayName?.let {
                    val filePath = File(customDir, audio.displayName).absolutePath
                    put(MediaStore.MediaColumns.DATA, filePath)
                }
            }
        }
        val uri = context.contentResolver.insert(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            values
        )

        emit(uri)
    }.flowOn(Dispatchers.IO)

    fun updateAudio(audio: RecordAudioBean): Flow<Boolean> = flow {
        val values = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, audio.displayName)
        }

        val selection = "${MediaStore.Audio.Media._ID} = ?"
        val selectionArgs = arrayOf(audio.id.toString())

        val count = context.contentResolver.update(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            values,
            selection,
            selectionArgs
        )

        emit(count > 0)
    }.flowOn(Dispatchers.IO)

    fun deleteAudio(recordAudioBean: RecordAudioBean):Flow<Pair<Boolean,RecordAudioBean>> = flow{
        var result = 0
        var intentSender: IntentSender? = null
        kotlin.runCatching {
            recordAudioBean.uri?.run {
                result = context.contentResolver.delete(this, null, null)
            }
        }.onFailure {
            Logger.e(TAG,it.message)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && it is RecoverableSecurityException){
                intentSender = it.userAction.actionIntent.intentSender
            }
        }

        recordAudioBean.intentSender = if (recordAudioBean.intentSender == null)
            intentSender
        else
            null
        emit((result > 0) to recordAudioBean)
    }.flowOn(Dispatchers.IO)

    fun getFileOutputStream(
        context: Context,
        uri: Uri?
    ): OutputStream? {
        uri?.let {
            return context.contentResolver.openOutputStream(uri)
        }
        return null
    }

    private fun Cursor.toAudioModel(): RecordAudioBean {
        val audioId =
            getLong(getColumnIndexOrThrow(MediaStore.MediaColumns._ID))
        val audioUri =
            Uri.withAppendedPath(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, audioId.toString())

        return RecordAudioBean(
            id = audioId,
            displayName = getString(getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME)),
            realPath = getString(getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)),
            uri = audioUri,
        )
    }
}