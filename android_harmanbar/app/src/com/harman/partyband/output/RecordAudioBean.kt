package com.harman.partyband.output

import android.content.IntentSender
import android.net.Uri

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: RecordAudioBean
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/6 15:01
 * @UpdateUser:
 * @UpdateDate: 2025/1/6 15:01
 * @UpdateRemark:
 * @Version: 1.0
 */
data class RecordAudioBean(
    val id: Long = 0,
    val displayName: String? = null,
    val playStatus: PlayStatus = PlayStatus.Stop,
    val uri: Uri? = null,
    val realPath: String? = null,
    val size: String? = null,
    val currentPosition: Int? = 0,
    val duration: Int? = 0,
    var onClick: ((mode: RecordAudioBean) -> Unit)? = null,
    var onDelClick:((mode: RecordAudioBean) -> Unit)? = null,
    var onShareClick:((mode: RecordAudioBean) -> Unit)? = null,
    var intentSender: IntentSender? = null
)

enum class PlayStatus(val value: Int) {
    Play(1), Pause(2), Stop(3)
}