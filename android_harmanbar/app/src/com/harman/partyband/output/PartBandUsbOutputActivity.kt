package com.harman.partyband.output

import android.Manifest
import android.app.Activity
import android.app.ProgressDialog
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore.Audio
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityUsbOutputBinding
import com.harman.log.Logger
import com.harman.music.Tools.formatTimeAsMills
import com.harman.partyband.looper.PartBandGuitarLooperDialog
import com.harman.partyband.widget.GuitarLooperAudioWaveView
import com.harman.partyband.widget.GuitarLooperCircleButton
import com.harman.partyband.widget.dp2px
import com.harman.partylight.util.gone
import com.harman.partylight.util.invisible
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: UsbOutputActivity
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/6 11:30
 * @UpdateUser:
 * @UpdateDate: 2025/1/6 11:30
 * @UpdateRemark:
 * @Version: 1.0
 */
class PartBandUsbOutputActivity : AppCompatBaseActivity() {

    companion object {
        private const val TAG = "PartBandUsbOutputActivity"
        fun launch(act: Activity, uuid: String) {
            act.push<PartBandUsbOutputActivity>(
                bundleOf(
                    "uuid" to uuid,
                )
            )
        }
    }

    private fun testLog(message: String) {
        Log.i(TAG, message)
    }

    private val debug: Boolean = false
    private val binding by lazy { ActivityUsbOutputBinding.inflate(layoutInflater) }

    private val vm by viewModels<PartBandRecordOutputViewModel>()
    private var adapter: RecordAudiosAdapter? = null
    private var currentDeleteAudio: RecordAudioBean? = null

    //    private val inputDialog: InputFileNameDialog by lazy {
//        InputFileNameDialog(this)
//    }
    private lateinit var permissionManager: PermissionManagerImpl

    private val dialog: PartBandGuitarLooperDialog by lazy {
        PartBandGuitarLooperDialog(this)
    }
    private var switchUsbModeProgressDialog: ProgressDialog? = null
    private var saveFileProgressDialog: ProgressDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        permissionManager = PermissionManagerImpl(this)
        this.setContentView(binding.root)
        this.buildAppbar()
        this.buildRecycleListView()
        this.buildRgOutputGroup()
        this.buildSeparateOutput()
        this.buildRecord()
        this.observeModel()
        //缺少权限申请流程和ui
        this.requestPermissionFlow()
        if (debug) {
            PermissionUtils.openManagerAppAllFilesAccessPermission(this)
        }

    }

    private fun requestPermissionFlow() {
        //缺少权限申请流程和ui
        runCatching {
            requestPermissionStore {
                requestPermissionRecord()
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    private fun requestPermissionStore(complete: () -> Unit) {
        //缺少权限申请流程和ui
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.READ_MEDIA_AUDIO
            )
        } else {
            arrayOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
        }
        requestPermissionsX(this.permissionManager, *permission) {
            onGranted {
                vm.handEvent(PartBandRecordOutputViewModel.Event.ReloadAudioEvent)
                testLog("requestPermissionStore权限已授予X")
                complete.invoke()
            }

            onDenied { permissions ->
                testLog("requestPermissionStore权限被拒绝X=${permissions}")
                vm.showToast("need store Permissions")
                complete.invoke()
            }

            onNeverAsk { permissions ->
                testLog("requestPermissionStore用户选择了不再询问X=${permissions}")
                vm.showToast("need store Permissions")
                complete.invoke()
            }
        }
    }

    private fun requestPermissionRecord() {
        //缺少权限申请流程和ui
        requestPermissionsX(
            this.permissionManager,
            Manifest.permission.RECORD_AUDIO,
        ) {
            onGranted {
                testLog("requestPermissionRecord权限已授予X")
            }

            onDenied { permissions ->
                vm.showToast("need record Permissions")
                testLog("requestPermissionRecord权限被拒绝X=${permissions}")
            }

            onNeverAsk { permissions ->
                vm.showToast("need record Permissions")
                testLog("requestPermissionRecord用户选择了不再询问X=${permissions}")
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            if (requestCode == InputFileNameActivity.INPUT_FILENAME_REQUEST_CODE) {
                Logger.d(
                    TAG,
                    "onActivityResult=${data?.getStringExtra(InputFileNameActivity.FILENAME_KEY)} "
                )
                val fileName = data?.getStringExtra(InputFileNameActivity.FILENAME_KEY)
                if (fileName.isNullOrEmpty()) {
                    vm.handEvent(
                        PartBandRecordOutputViewModel.Event.CancelRecordEvent
                    )
                } else {
                    vm.handEvent(
                        PartBandRecordOutputViewModel.Event.SaveRecordEvent(
                            fileName = "${fileName}.wav"
                        )
                    )
                }
            }
            if (requestCode == AudioStore.REQUEST_CODE_FOR_DELETE_AUDIO){
                currentDeleteAudio?.let{
                    vm.handEvent(
                        PartBandRecordOutputViewModel.Event.DelAudioEvent(it)
                    )
                }
            }
        }

    }

    private fun buildAppbar() {
        binding.appbar.tvTitle.text = getString(R.string.title_output)
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
    }

    private fun updateOutputView(output: PartBandRecordOutputViewModel.OutputTo) {
        when (output) {
            PartBandRecordOutputViewModel.OutputTo.ThisPhone -> {
                binding.ibThisPhone.backgroundTintList =
                    ColorStateList.valueOf(getColor(R.color.fg_primary))
                binding.ibThisPhone.setColorFilter(getColor(R.color.fg_inverse))
                binding.tvThisPhone.setTextColor(getColor(R.color.fg_primary))
                binding.tvThisPhone.setTypeface(binding.tvThisPhone.typeface, Typeface.BOLD)

                binding.ibFlashDrive.backgroundTintList =
                    ColorStateList.valueOf(getColor(R.color.transparent))
                binding.ibFlashDrive.setColorFilter(getColor(R.color.fg_secondary))
                binding.tvFlashDrive.setTextColor(getColor(R.color.fg_secondary))
                binding.tvFlashDrive.setTypeface(binding.tvThisPhone.typeface, Typeface.NORMAL)


            }

            PartBandRecordOutputViewModel.OutputTo.FlashDriver -> {
                binding.ibThisPhone.backgroundTintList =
                    ColorStateList.valueOf(getColor(R.color.transparent))
                binding.ibThisPhone.setColorFilter(getColor(R.color.fg_secondary))
                binding.tvThisPhone.setTextColor(getColor(R.color.fg_secondary))
                binding.tvThisPhone.setTypeface(binding.tvThisPhone.typeface, Typeface.NORMAL)

                binding.ibFlashDrive.backgroundTintList =
                    ColorStateList.valueOf(getColor(R.color.fg_primary))
                binding.ibFlashDrive.setColorFilter(getColor(R.color.fg_inverse))
                binding.tvFlashDrive.setTextColor(getColor(R.color.fg_primary))
                binding.tvFlashDrive.setTypeface(binding.tvThisPhone.typeface, Typeface.BOLD)
            }

        }

        if (switchUsbModeProgressDialog?.isShowing == true) {
            binding.ibThisPhone.setImageResource(R.drawable.icon_loading)
        } else {
            binding.ibThisPhone.setImageResource(R.drawable.icon_phone)
        }
    }

    private fun buildRgOutputGroup() {

        binding.ibThisPhone.setOnClickListener {
            if (vm.isRecording()) {
                return@setOnClickListener
            }
            updateOutputView(PartBandRecordOutputViewModel.OutputTo.ThisPhone)
            vm.handEvent(
                PartBandRecordOutputViewModel.Event.SwitchOutputToEvent(
                    PartBandRecordOutputViewModel.OutputTo.ThisPhone
                )
            )
        }
        binding.ibFlashDrive.setOnClickListener {
            if (vm.isRecording()) {
                return@setOnClickListener
            }
            updateOutputView(PartBandRecordOutputViewModel.OutputTo.FlashDriver)
            vm.handEvent(
                PartBandRecordOutputViewModel.Event.SwitchOutputToEvent(
                    PartBandRecordOutputViewModel.OutputTo.FlashDriver
                )
            )
        }
        //Solo doesn’t has separation output toggle
        binding.layoutSeparateOutput.visibility = if(vm.device?.isSolo() == false) View.VISIBLE else View.GONE
    }

    private fun buildSeparateOutput() {
        binding.switchSeparateOutput.setOnCheckedChangeListener { buttonView, isChecked ->
            if (buttonView.isPressed) {
                vm.handEvent(PartBandRecordOutputViewModel.Event.SwitchSeparateOutputEvent(isChecked))
            }
        }
    }

    private fun buildRecycleListView() {
        binding.rcListAudioFiles.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        adapter = RecordAudiosAdapter()
        binding.rcListAudioFiles.adapter = adapter
        binding.rcListAudioFiles.addItemDecoration(SpaceItemVerticalDecoration(8f.dp2px(this)))
        binding.rcListAudioFiles.itemAnimator = null
    }


    private fun buildRecord() {
        binding.btGuitarloopercircleButton.setOnClickListener {
            when (val butType = binding.btGuitarloopercircleButton.getType()) {
                GuitarLooperCircleButton.Type.READY -> {
                    vm.handEvent(PartBandRecordOutputViewModel.Event.StartRecordEvent)
                }

                GuitarLooperCircleButton.Type.RECORDING -> {
                    when (vm.recordSource()) {
                        PartBandRecordOutputViewModel.RecordSource.UAC -> {
                            vm.handEvent(PartBandRecordOutputViewModel.Event.StopRecordEvent)
                            val intent = Intent(this, InputFileNameActivity::class.java)
                            this.startActivityForResult(
                                intent,
                                InputFileNameActivity.INPUT_FILENAME_REQUEST_CODE
                            )
                        }

                        PartBandRecordOutputViewModel.RecordSource.BLE -> {
                            vm.handEvent(PartBandRecordOutputViewModel.Event.StopRecordEvent)
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private fun observeModel() {
        lifecycleScope.launch {
            vm.screenData.collect {
                testLog("screenData=${it}")
                when (it) {
                    is PartBandRecordOutputViewModel.ScreenData.Loading -> {}
                    is PartBandRecordOutputViewModel.ScreenData.Success -> {
                        updateScreen(it)
                    }
                }

            }
        }
        lifecycleScope.launch {
            vm.showToast.collect {
                it?.let {
                    ToastUtils.showLong(it)
                    vm.showToast(null)
                }
            }
        }
        lifecycleScope.launch {
            vm.audioWaveValues.collect {
                binding.viewRealtimeAudiowave.updateWave(it)
            }
        }
        lifecycleScope.launch {
            vm.delAudioFlow.collect { audioMode ->
                Logger.i(TAG, "delAudioFlow collect audioModel=${audioMode}")
                audioMode?.let {
                    dialog.setContent(
                        getString(R.string.do_you_want_to_delete),
                        getString(com.wifiaudio.R.string.global_delete),
                        getString(R.string.jbl_CANCEL)
                    )
                    dialog.listener = object : PartBandGuitarLooperDialog.DialogIF {
                        override fun onDismiss() {
                            dialog.dismiss()
                        }

                        override fun onConfirm() {
                            dialog.dismiss()
                            it.intentSender = null
                            vm.handEvent(
                                PartBandRecordOutputViewModel.Event.DelAudioEvent(it)
                            )

                        }

                    }
                    dialog.show()
                }

            }
        }

        vm.audioDeleteRequest.observe(this){
            currentDeleteAudio = null
            it.intentSender?.run {
                currentDeleteAudio = it
                startIntentSenderForResult(this,AudioStore.REQUEST_CODE_FOR_DELETE_AUDIO,null,0,0,0,null)
            }
        }

        vm.audioDisconnect.observe(this){
            if (binding.btGuitarloopercircleButton.getType() == GuitarLooperCircleButton.Type.RECORDING && it) {
                binding.btGuitarloopercircleButton.performClick()
            }
        }
    }


    private fun hideLoading() {
        switchUsbModeProgressDialog?.dismiss()
        switchUsbModeProgressDialog = null
    }

    private fun showLoading() {
        hideLoading()
        switchUsbModeProgressDialog = ProgressDialog(this)
        switchUsbModeProgressDialog!!.setMessage(getString(R.string.changing_output_source_it))
        switchUsbModeProgressDialog!!.setCancelable(false)
        switchUsbModeProgressDialog!!.show()

        lifecycleScope.launch {
            delay(16000)
            switchUsbModeProgressDialog?.dismiss()
            switchUsbModeProgressDialog = null
            updateScreen(vm.screenData.value as PartBandRecordOutputViewModel.ScreenData.Success)
        }
    }

    private fun hideSaveFileLoading() {
        saveFileProgressDialog?.dismiss()
        saveFileProgressDialog = null
    }

    private fun showSaveFileLoading() {
        hideSaveFileLoading()
        saveFileProgressDialog = ProgressDialog(this)
        saveFileProgressDialog!!.setMessage(getString(R.string.waiting))
        saveFileProgressDialog!!.setCancelable(false)
        saveFileProgressDialog!!.show()
    }

    private fun updateTvWarning(screenData: PartBandRecordOutputViewModel.ScreenData.Success) {

        when (screenData.outPutTo) {
            PartBandRecordOutputViewModel.OutputTo.ThisPhone -> {
                binding.tvWarning.text = screenData.warningStr.uacWarning
            }

            PartBandRecordOutputViewModel.OutputTo.FlashDriver -> {
                binding.tvWarning.text = screenData.warningStr.usbFlashDiskWarning
            }
        }

    }

    private fun updateCircleButton(screenData: PartBandRecordOutputViewModel.ScreenData.Success) {
        binding.btGuitarloopercircleButton.setType(screenData.butType)
        when (screenData.outPutTo) {
            PartBandRecordOutputViewModel.OutputTo.ThisPhone -> {
                binding.btGuitarloopercircleButton.isEnabled = screenData.usbStatus.uacInserted
            }

            PartBandRecordOutputViewModel.OutputTo.FlashDriver -> {
                binding.btGuitarloopercircleButton.isEnabled =
                    screenData.usbStatus.usbFlashDiskInserted
            }
        }
        if (switchUsbModeProgressDialog?.isShowing == true) {
            binding.btGuitarloopercircleButton.isEnabled = false
        }
    }


    private fun updateAudioFilesView(screenData: PartBandRecordOutputViewModel.ScreenData.Success) {
        when (screenData.outPutTo) {
            PartBandRecordOutputViewModel.OutputTo.ThisPhone -> {
                binding.tvRecordfilesTitle.visible()
                binding.rcListAudioFiles.visible()
            }

            PartBandRecordOutputViewModel.OutputTo.FlashDriver -> {
                binding.tvRecordfilesTitle.gone()
                binding.rcListAudioFiles.gone()
            }
        }
    }

    private fun upadteSeparateOutput(screenData: PartBandRecordOutputViewModel.ScreenData.Success) {

        binding.switchSeparateOutput.isChecked =
            screenData.separateOutput == PartBandRecordOutputViewModel.SeparateOutput.ON
        binding.switchSeparateOutput.isEnabled =
            screenData.audioRecordState != AudioRecordState.Recording

        binding.tvSwitchSeparateOutput.setTextColor(
            if (screenData.audioRecordState == AudioRecordState.Idle) {
                getColor(R.color.fg_primary)
            } else {
                getColor(R.color.fg_disabled)
            }
        )
        when (screenData.audioRecordState) {
            AudioRecordState.SwitchUsbModel -> {
                showLoading()
            }

            AudioRecordState.DelayIsOver,
            AudioRecordState.Recording -> {
                hideLoading()
            }

            else -> {

            }
        }

    }

    private fun updateSaveFileDialog(screenData: PartBandRecordOutputViewModel.ScreenData.Success) {

        when (screenData.audioRecordState) {
            AudioRecordState.SaveFileStart -> {
                showSaveFileLoading()
            }

            else -> {
                hideSaveFileLoading()
            }
        }
    }

    private fun updateScreen(screenData: PartBandRecordOutputViewModel.ScreenData.Success) {
        updateOutputView(screenData.outPutTo)
        updateTvWarning(screenData)
        updateCircleButton(screenData)
        updateAudioFilesView(screenData)
        upadteSeparateOutput(screenData)
        updateSaveFileDialog(screenData)

//        binding.viewGuitarlooperAudiowaveView.initAudioWaveAnim()
//        binding.viewGuitarlooperAudiowaveView.withSound(screenData.withSound)
//        binding.viewGuitarlooperAudiowaveView.setAudioWaveStatus(GuitarLooperAudioWaveView.AudioWaveStatus.Ready)
        adapter?.submitList(screenData.audioFiles)
        when (screenData.audioRecordState) {
            AudioRecordState.Idle -> {
                binding.tvRecordTime.invisible()
            }

            else -> {
                binding.tvRecordTime.visible()
                binding.tvRecordTime.text = screenData.recordingTime.toLong().formatTimeAsMills()
            }
        }


    }


    override fun onDestroy() {
        super.onDestroy()
//        inputDialog.dismiss()
        dialog.dismiss()
        hideLoading()
    }


}