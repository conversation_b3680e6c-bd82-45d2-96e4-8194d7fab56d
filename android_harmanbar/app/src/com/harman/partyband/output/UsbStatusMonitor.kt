package com.harman.partyband.output

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.os.Build
import androidx.annotation.UiThread
import com.blankj.utilcode.util.Utils
import com.harman.discover.bean.PartyBandDevice
import com.harman.log.Logger
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.flow.MutableStateFlow
import java.nio.ByteBuffer

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.output
 * @ClassName: UsbStatusMonitor
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/3/27 13:56
 * @UpdateUser:
 * @UpdateDate: 2025/3/27 13:56
 * @UpdateRemark:
 * @Version: 1.0
 */
class UsbStatusMonitor(var device: PartyBandDevice? = null) {

    companion object {
        const val TAG = "UsbStatusMonitor"
    }


    val usbDeviceAttached = MutableStateFlow(false)

    private val actionUsbPermission = "${Utils.getApp().packageName}.USB_PERMISSION"
    private var usbPermissionReqCompleter: CompletableDeferred<Unit>? = null
    private val usbPermissionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            usbPermissionReqCompleter?.complete(Unit)
        }
    }

    private val usbDeviceDetachedReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                val acion = intent?.action
                when (acion) {
                    UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                        usbDeviceAttached.value = true
                        Logger.d(TAG, "ACTION_USB_DEVICE_ATTACHED")
                    }

                    UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                        usbDeviceAttached.value = false
                        Logger.d(TAG, "ACTION_USB_DEVICE_DETACHED")
                    }

                }
            }
        }
    }

    init {
        Logger.d(TAG, "init")
    }

    @UiThread
    @OptIn(ExperimentalStdlibApi::class)
    fun findUsbDevice(): Boolean {
        val um = Utils.getApp().getSystemService(UsbManager::class.java)
        val deviceList = um.deviceList
        val partyBandUsbDevice = deviceList.toList().find {
            //Since the bottom layer of USB transmission is the PID and vendor ID stored in big-endian order, so the big-endian order is used as HEX
            val pidString =
                ByteBuffer.allocate(2).putShort(it.second.productId.toShort()).array().toHexString()
            val vendorIdString =
                ByteBuffer.allocate(2).putShort(it.second.vendorId.toShort()).array().toHexString()
            return@find pidString.equals(
                device?.pid,
                true
            ) && vendorIdString.equals(device?.bleDevice?.vendorID, true)
        }?.second
        return null != partyBandUsbDevice
    }


    fun startMonitor() {
        //The registered device is disconnected and listens to the broadcast
        val filter = IntentFilter()
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Utils.getApp().registerReceiver(
                usbDeviceDetachedReceiver,
                filter,
                Context.RECEIVER_EXPORTED
            )
        } else {
            Utils.getApp().registerReceiver(usbDeviceDetachedReceiver, filter)
        }
        //Register a USB device with permission to listen to broadcasts
        Utils.getApp().registerReceiver(
            usbPermissionReceiver,
            IntentFilter(actionUsbPermission),
            Context.RECEIVER_EXPORTED
        )
        Logger.i(TAG, "startMonitor")
    }

    fun stopMonitor() {
        Utils.getApp().unregisterReceiver(usbPermissionReceiver)
        Utils.getApp().unregisterReceiver(usbDeviceDetachedReceiver)
        Logger.i(TAG, "stopMonitor")
    }
}