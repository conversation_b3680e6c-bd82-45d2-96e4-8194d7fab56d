package com.harman.partyband.debug

import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityPartyBandDebugBinding
import com.harman.ota.PartyOtaChecker
import com.harman.ota.PartyOtaCheckerParams
import com.harman.partyband.drummetronome.DrumMetronomeActivity
import com.harman.partyband.looper.PartyBandGuitarLooperActivity
import com.harman.partyband.ota.PartyBandOtaActivity
import com.harman.partyband.output.PartBandUsbOutputActivity
import com.harman.partyband.control.ToneShifterDialog
import com.harman.discover.DeviceScanner
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.partylight.util.fitSystemBar
import com.harman.partylight.util.push
import com.harman.product.info.ProductInfoActivity
import com.harman.product.setting.activity.BaseProductSettingsActivity
import com.harman.v5protocol.bean.devinfofeat.V5FirmwareVersion
import com.harman.widget.AppCompatBaseActivity
import com.jbl.one.configuration.model.Firmware
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2024/12/6
 */
class PartyBandDebugActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityPartyBandDebugBinding.inflate(layoutInflater) }
    private val device by lazy {
        DeviceStore.find(intent.getStringExtra("uuid")!!) as PartyBandDevice
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        fitSystemBar()
        DeviceScanner.stopScan(this, false)
        build()
        fetchData()
        testGuitarLooper()
        testUsbOutput()
    }

    private fun testUsbOutput() {
        binding.btnUsbOutput.setOnClickListener {
            PartBandUsbOutputActivity.launch(this@PartyBandDebugActivity, device.UUID!!)
        }
    }

    private fun testGuitarLooper() {
        binding.btnGuitarLooper.setOnClickListener {
            PartyBandGuitarLooperActivity.launch(this@PartyBandDebugActivity, device.UUID!!)
        }
    }

    private fun build() {
        binding.fab.setOnClickListener {
            push<PartybandDebugCommunicationActivity>(
                bundleOf("uuid" to device.UUID)
            )
        }
        binding.btnPitch.setOnClickListener {
            ToneShifterDialog(this, device).show(supportFragmentManager, null)
        }
        binding.btnDrum.setOnClickListener {
            DrumMetronomeActivity.launch(this, DrumMetronomeActivity.TYPE_DRUM, device.UUID!!)
        }
        binding.btnMetronome.setOnClickListener {
            DrumMetronomeActivity.launch(this, DrumMetronomeActivity.TYPE_METRONOME, device.UUID!!)
        }
        binding.btnOtaOffline.setOnClickListener {
            val offlineOtaFiles =
                getExternalFilesDir(null)!!.listFiles().filter { it.name.contains("BandBox_OTA") }
            val items = offlineOtaFiles.map { it.name }.toTypedArray()
            val builder = AlertDialog.Builder(this)
            builder.setTitle("choose ota file")
            builder.setItems(items) { dialog, which ->
                dialog.dismiss()
                val selectedFile = offlineOtaFiles[which]
                val version = selectedFile.name.takeLast(18).removeSuffix(".bin")
                PartyBandOtaActivity.launch(
                    this@PartyBandDebugActivity, device.UUID!!,
                    firmware = Firmware(version),
                    selectedFile.absolutePath,
                )
            }
            builder.show()
        }
        binding.btnInfo.setOnClickListener {
            ProductInfoActivity.launchProductInfoPage(this, device)
        }

        binding.btnSetting.setOnClickListener {
            BaseProductSettingsActivity.portal(this, device, 0)
        }
        binding.btnTuner.setOnClickListener {

        }
    }

    private fun fetchData() {
        lifecycleScope.launch {
            device.getDevInfoFeat<V5FirmwareVersion>()?.also {
                binding.tvFwVersion.text =
                    getString(R.string.harmanbar_jbl_Current_version___, it.version)
            }
        }
        lifecycleScope.launch {
            binding.btnOta.isEnabled = false
            binding.btnOta.text = "Checking OTA..."
            val otaCheckRet = PartyOtaChecker.hasUpdate(
                PartyOtaCheckerParams(
                    device.pid!!,
                    device.firmwareVersion,
                )
            )
            if (null == otaCheckRet) {
                binding.btnOta.text = "Check OTA Filed"
            } else {
                binding.btnOta.isEnabled = true
                binding.btnOta.text = "Enter OTA"
                binding.btnOta.setOnClickListener {
                    PartyBandOtaActivity.launch(this@PartyBandDebugActivity, device.UUID!!, otaCheckRet)
                }
            }
        }

    }
}