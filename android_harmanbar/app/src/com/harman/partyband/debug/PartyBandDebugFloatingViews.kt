package com.harman.partyband.debug

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.content.Context
import android.content.res.ColorStateList
import android.os.Bundle
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Choreographer
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnLayoutChangeListener
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.os.bundleOf
import androidx.core.view.children
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.LayoutParams
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ViewPartybandDebugFloatingListBinding
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.dp
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.push
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.V5Util
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.impl.runOnUiThread
import java.text.SimpleDateFormat

class PartyBandDebugFloatingViews : FloatingActionButton {

    private val messages = MutableLiveData(listOf<MessageItem>())
    private val _messages = mutableListOf<MessageItem>()
    private var device: PartyBandDevice? = null
    private val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            super.onDevFeat(devInfoMap, isNotify)
            devInfoMap.forEach {
                add(
                    MessageItem(
                        TimeUtils.getNowString(SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS")),
                        "parse device info feature: [featureId] = ${it.key.name},[payloadJson] = ${GsonUtil.parseBeanToJson(it.value)}"
                    )
                )
            }
            runOnUiThread {
                messages.value = _messages.toList()
            }
        }

        override fun onCharacteristicChanged(data: ByteArray) {
            super.onCharacteristicChanged(data)
            add(
                MessageItem(
                    TimeUtils.getNowString(SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS")),
                    "characteristic changed: ${V5Util.bytes2HexWithSpace(data)}"
                )
            )
            runOnUiThread {
                messages.value = _messages.toList()
            }
        }

        override fun onWriteSuccess(data: ByteArray) {
            super.onWriteSuccess(data)
            add(
                MessageItem(
                    TimeUtils.getNowString(SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS")),
                    "write success: ${V5Util.bytes2HexWithSpace(data)}"
                )
            )
            runOnUiThread {
                messages.value = _messages.toList()
            }
        }
    }
    private val logBinding by lazy { ViewPartybandDebugFloatingListBinding.inflate(LayoutInflater.from(context)) }

    private fun add(item: MessageItem) {
        if (_messages.size >= 200) {
            _messages.removeFirstOrNull()
        }
        _messages.add(item)
    }

    constructor(context: Context) : super(context) {
        init(null, 0)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle) {
        init(attrs, defStyle)
    }

    private fun init(attrs: AttributeSet?, defStyle: Int) {
        backgroundTintList = ColorStateList.valueOf(context.getColor(R.color.green_2))
        size = SIZE_MINI
        setImageResource(R.drawable.ic_bug)
        setOnClickListener {
            toggleRv()
        }
        logBinding.ivClear.setOnClickListener {
            _messages.clear()
            messages.value = _messages.toList()
        }
        (context as? LifecycleOwner)?.also {
            logBinding.rv.adapter = RvAdapter(messages, it)
        }
    }

    private fun monitorDevice(dev: PartyBandDevice) {
        if (device == dev) {
            device!!.unregisterDeviceListener(deviceListener)
        }
        device = dev
        device!!.registerDeviceListener(deviceListener)
    }

    private fun toggleRv() {
        ((context as? Activity)?.findViewById<View>(android.R.id.content) as? FrameLayout)?.also {
            if (null == logBinding.root.parent) {
                val thisIndex = it.children.indexOf(this)
                it.addView(logBinding.root, thisIndex, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
                it.findViewWithTag<View>(TAG1).alpha = 0.2f
                it.findViewWithTag<View>(TAG2).alpha = 0.2f
            } else {
                it.removeView(logBinding.root)
                it.findViewWithTag<View>(TAG1).alpha = 1f
                it.findViewWithTag<View>(TAG2).alpha = 1f
            }
        }
    }

    override fun onDetachedFromWindow() {
        device?.unregisterDeviceListener(deviceListener)
        super.onDetachedFromWindow()
    }


    companion object {
        private const val TAG1 = "PartyBandDebugFloatingViewsTag1"
        private const val TAG2 = "PartyBandDebugFloatingViewsTag2"
        private val lifecycleCallback = object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                var uuid = activity.intent.getStringExtra("Bundle_UUID")
                if (null == uuid) {
                    uuid = activity.intent.getStringExtra("UUID")
                }
                if (null == uuid) {
                    uuid = activity.intent.getStringExtra("uuid")
                }
                uuid?.let {
                    DeviceStore.find(uuid) as? PartyBandDevice
                }?.let { dev ->

                    val btn1 = FloatingActionButton(activity).apply {
                        backgroundTintList = ColorStateList.valueOf(activity.getColor(R.color.purple_2))
                        setImageResource(R.drawable.ic_bug)
                        size = SIZE_MINI
                        tag = TAG1
                        setOnClickListener {
                            activity.push<PartybandDebugCommunicationActivity>(
                                bundleOf("uuid" to uuid)
                            )
                        }
                    }
                    val btn2 = PartyBandDebugFloatingViews(activity).apply {
                        tag = TAG2
                        monitorDevice(dev)
                    }
                    Choreographer.getInstance().postFrameCallback {
                        (activity.findViewById<View>(android.R.id.content) as? FrameLayout)?.also {
                            it.addView(
                                btn1,
                                FrameLayout.LayoutParams(
                                    FrameLayout.LayoutParams.WRAP_CONTENT,
                                    FrameLayout.LayoutParams.WRAP_CONTENT,
                                ).apply {
                                    gravity = Gravity.BOTTOM or Gravity.END
                                    rightMargin = 32.dp()
                                    bottomMargin = 56.dp()
                                }
                            )
                            it.addView(
                                btn2,
                                FrameLayout.LayoutParams(
                                    FrameLayout.LayoutParams.WRAP_CONTENT,
                                    FrameLayout.LayoutParams.WRAP_CONTENT,
                                ).apply {
                                    gravity = Gravity.BOTTOM or Gravity.END
                                    rightMargin = 32.dp()
                                    bottomMargin = 112.dp()
                                }
                            )
                        }
                    }
                }
            }

            override fun onActivityStarted(activity: Activity) = Unit
            override fun onActivityResumed(activity: Activity) = Unit
            override fun onActivityPaused(activity: Activity) = Unit
            override fun onActivityStopped(activity: Activity) = Unit
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) = Unit
            override fun onActivityDestroyed(activity: Activity) = Unit
        }

        fun monitor() {
            Utils.getApp().unregisterActivityLifecycleCallbacks(lifecycleCallback)
            Utils.getApp().registerActivityLifecycleCallbacks(lifecycleCallback)
        }
    }
}

private class RvAdapter(
    private val list: LiveData<List<MessageItem>>,
    private val lifecycleOwner: LifecycleOwner,
) : RecyclerView.Adapter<ViewHolder>() {

    private val differ = generateSimpleDiffer<MessageItem>()
    private var rv: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        rv = recyclerView
    }

    init {
        list.observe(lifecycleOwner) {
            differ.submitList(it)
            if (it.isNotEmpty()) {
                rv?.addOnLayoutChangeListener(object : OnLayoutChangeListener {
                    override fun onLayoutChange(
                        v: View?, left: Int, top: Int, right: Int, bottom: Int, oldLeft: Int, oldTop: Int, oldRight: Int, oldBottom: Int
                    ) {
                        rv?.removeOnLayoutChangeListener(this)
                        rv?.smoothScrollToPosition(differ.currentList.size - 1)
                    }
                })
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return object : ViewHolder(AppCompatTextView(parent.context).apply {
            layoutParams = RecyclerView.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            setTextColor((0xFFFFFFFF).toInt())
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 9f)
            setPadding(0, 10, 0, 0)
            isClickable = false
        }) {}
    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        (holder.itemView as TextView).apply {
            val item = differ.currentList[position]
            text = "${item.time}  ${item.text}"
        }
    }
}

private data class MessageItem(val time: String, val text: String)