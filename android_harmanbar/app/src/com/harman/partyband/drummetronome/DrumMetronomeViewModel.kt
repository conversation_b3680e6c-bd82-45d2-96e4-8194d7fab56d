package com.harman.partyband.drummetronome

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.StringUtils
import com.harman.bar.app.R
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5DrumSequence
import com.harman.v5protocol.bean.devinfofeat.V5DrumStartEnd
import com.harman.v5protocol.bean.devinfofeat.V5DrumTempo
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeSequence
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeStartEnd
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeTempo
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch

/**
 * @Description viewmodel for [DrumMetronomeActivity]
 * <AUTHOR>
 * @Time 2024/12/27
 */
class DrumMetronomeViewModel(savedState: SavedStateHandle) : ViewModel() {
    val device = DeviceStore.find(savedState.get<String>("uuid")!!) as? PartyBandDevice
    private val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.MetronomeSequence] as? V5MetronomeSequence)?.also {
                    currentMetronomeBeat.value = it.beat
                }
                (devInfoMap[V5DevInfoFeatID.DrumTempo] as? V5DrumTempo)?.also {
                    if (isDrum) {
                        bpm.value = it.value
                    }
                }
                (devInfoMap[V5DevInfoFeatID.MetronomeTempo] as? V5MetronomeTempo)?.also {
                    if (!isDrum) {
                        bpm.value = it.value
                    }
                }
                (devInfoMap[V5DevInfoFeatID.DrumSequence] as? V5DrumSequence)?.also {
                    currentDrumType.value = it.type
                }

                (devInfoMap[V5DevInfoFeatID.DrumStartEnd] as? V5DrumStartEnd)?.also {
                    if (isDrum) {
                        isPlaying.value = it.start
                    }
                }
                (devInfoMap[V5DevInfoFeatID.MetronomeStartEnd] as? V5MetronomeStartEnd)?.also {
                    if (!isDrum) {
                        isPlaying.value = it.start
                    }
                }
                devInfoMap[V5DevInfoFeatID.DrumTap]?.run {
                    playDotStream.tryEmit(Unit)
                }
                devInfoMap[V5DevInfoFeatID.MetronomeTap]?.run {
                    playDotStream.tryEmit(Unit)
                }
            }
        }
    }
    val isDrum = DrumMetronomeActivity.TYPE_DRUM == savedState.get<Int>("type")
    val appbarTitle = MutableLiveData(run {
        if (isDrum) StringUtils.getString(R.string.drum)
        else StringUtils.getString(R.string.metronome)
    })
    val bpm = MutableLiveData(0)
    val isPlaying = MutableLiveData(false)
    val currentDrumType = MutableLiveData<V5DrumSequence.Type>()
    val currentMetronomeBeat = MutableLiveData<V5MetronomeSequence.Beat>()
    val isTapping = MutableLiveData(false)
    val playDotStream = MutableSharedFlow<Unit>(extraBufferCapacity = 1)

    init {
        device?.registerDeviceListener(deviceListener)
        if (isDrum) {
            device?.asyncGetDevInfoFeat(V5DevInfoFeatID.DrumSequence, V5DevInfoFeatID.DrumTempo, V5DevInfoFeatID.DrumStartEnd)
        } else {
            device?.asyncGetDevInfoFeat(V5DevInfoFeatID.MetronomeSequence, V5DevInfoFeatID.MetronomeTempo, V5DevInfoFeatID.MetronomeStartEnd)
        }
    }

    override fun onCleared() {
        super.onCleared()
        device?.unregisterDeviceListener(deviceListener)
        exit()
    }

    private fun exit() {
        device?.asyncSetDevInfoFeat(
            if (isDrum) {
                V5DrumStartEnd(false)
            } else {
                V5MetronomeStartEnd(false)
            }
        )
    }

    fun addBpm() {
        setBpm(bpm.value!! + 1)
    }

    fun subBpm() {
        setBpm(bpm.value!! - 1)
    }

    fun setBpm(value: Int) {
        if (value == bpm.value) {
            return
        }
        val minValue = if (isDrum) V5DrumTempo.MIN else V5MetronomeTempo.MIN
        val maxValue = if (isDrum) V5DrumTempo.MAX else V5MetronomeTempo.MAX
        val finalValue = value.coerceIn(minValue, maxValue)
        val write = if (isDrum) V5DrumTempo(finalValue) else V5MetronomeTempo(finalValue)
        device?.asyncSetDevInfoFeat(write)
        bpm.value = finalValue
    }

    fun togglePlayState() {
        val targetValue = !isPlaying.value!!
        val write = if (isDrum) {
            V5DrumStartEnd(targetValue)
        } else {
            V5MetronomeStartEnd(targetValue)
        }
        isPlaying.value = targetValue
        device?.asyncSetDevInfoFeat(write)
    }

    fun changeCurrentDrumType(type: V5DrumSequence.Type) {
        if (type == currentDrumType.value) {
            return
        }
        currentDrumType.value = type
    }

    private var bpmTapTimes = 0
    private var bpmTappingJob: Job? = null
    private var bpmTapFirstTime: Long? = null
    private var bpmTapEndTime: Long? = null

    /** The tapping hand detect  tappings every 2 s and calculate the average interval*/
    fun bpmTap() {
        if (bpmTapTimes == 0) {
            bpmTapFirstTime = System.currentTimeMillis()
        }
        startBpmTappingJob()
        bpmTapTimes++
        bpmTapEndTime = System.currentTimeMillis()
        isTapping.value = true
    }

    private fun startBpmTappingJob() {
        bpmTappingJob?.cancel()
        bpmTappingJob = viewModelScope.launch {
            delay(BPM_TAP_INTERVAL_MILLISECOND)
            if (bpmTapTimes > 1) {
                val tappingDurationMs = bpmTapEndTime!! - bpmTapFirstTime!!
                val tapBpm = (BPM_TAP_COMPUTE_MOLECULE / (tappingDurationMs / (bpmTapTimes - 1).toFloat() / 1000)).toInt()
                setBpm(tapBpm)
            }
            bpmTapTimes = 0
            bpmTapFirstTime = null
            bpmTapEndTime = null
            isTapping.value = false
        }
    }

    fun switchMetronomeBeat(beat: V5MetronomeSequence.Beat) {
        if (currentMetronomeBeat.value == beat) {
            return
        }
        device?.asyncSetDevInfoFeat(V5MetronomeSequence(beat))
        currentMetronomeBeat.value = beat
    }

    companion object {
        private const val BPM_TAP_INTERVAL_MILLISECOND = 2000L
        private const val BPM_TAP_COMPUTE_MOLECULE = 60
    }
}
