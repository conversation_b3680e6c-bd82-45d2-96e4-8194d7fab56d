package com.harman.partyband.drummetronome

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityDrumMetronomeBinding
import com.harman.partylight.util.gone
import com.harman.partylight.util.invisible
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeSequence
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

/**
 * @Description partyband drum and metronome feature activity
 * <AUTHOR>
 * @Time 2024/12/26
 */
class DrumMetronomeActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityDrumMetronomeBinding.inflate(layoutInflater) }
    private val vm by viewModels<DrumMetronomeViewModel>()
    private val rbList by lazy {
        listOf(
            binding.rbBeat44.apply {
                text = V5MetronomeSequence.Beat.B44.beatName
                tag = V5MetronomeSequence.Beat.B44
            },
            binding.rbBeat43.apply {
                text = V5MetronomeSequence.Beat.B34.beatName
                tag = V5MetronomeSequence.Beat.B34
            },
            binding.rbBeat42.apply {
                text = V5MetronomeSequence.Beat.B24.beatName
                tag = V5MetronomeSequence.Beat.B24
            },
            binding.rbBeat68.apply {
                text = V5MetronomeSequence.Beat.B68.beatName
                tag = V5MetronomeSequence.Beat.B68
            },
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        build()
        observeModel()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    private fun build() {
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
        binding.ivAdd.setOnClickListener {
            vm.addBpm()
        }
        binding.ivSub.setOnClickListener {
            vm.subBpm()
        }
        binding.ivPlay.setOnClickListener {
            vm.togglePlayState()
        }
        binding.ivPalm.setOnClickListener {
            vm.bpmTap()
        }
        binding.btnDrum44.setOnClickListener {
            vm.device?.UUID?.also {
                lifecycleScope.launch {
                    DrumTimeSignatureActivity.launch(this@DrumMetronomeActivity, it, vm.currentDrumType.value, vm.isPlaying.value!!)?.also {
                        vm.changeCurrentDrumType(it)
                    }
                }
            }
        }
        rbList.forEach {
            it.setOnClickListener { _ ->
                vm.switchMetronomeBeat(it.tag as V5MetronomeSequence.Beat)
            }
        }
        if (vm.isDrum) {
            binding.rgMetronome.gone()
            binding.btnDrum44.visible()
        } else {
            binding.rgMetronome.visible()
            binding.btnDrum44.gone()
        }
        binding.csvBpm.onValueChange = { value, isDragging ->
            if (!isDragging) {
                vm.setBpm(value)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun observeModel() {
        vm.appbarTitle.observe(this) {
            binding.appbar.tvTitle.text = it
        }
        vm.bpm.distinctUntilChanged().observe(this) {
            binding.tvBpm.text = it.toString()
            binding.csvBpm.setValue(it)
        }
        vm.isPlaying.observe(this) {
            binding.ivPlay.setImageResource(if (it) R.drawable.ic_mini_player_pause else R.drawable.ic_mini_player_play)
        }
        lifecycleScope.launch {
            vm.playDotStream.collect {
                binding.csvBpm.switchRingColor()
            }
        }
        vm.currentDrumType.observe(this) {
            binding.btnDrum44.text = "${it.beat.beatName}\n${getDrumStyleName(this, it)}"
        }
        vm.currentMetronomeBeat.observe(this) { beat ->
            rbList.forEach { rb ->
                rb.setTextColor(getColor(R.color.fg_primary))
                rb.setBackgroundResource(R.drawable.radius_round_bg_card)
            }
            val targetRb = when (beat!!) {
                V5MetronomeSequence.Beat.B44 -> binding.rbBeat44
                V5MetronomeSequence.Beat.B34 -> binding.rbBeat43
                V5MetronomeSequence.Beat.B24 -> binding.rbBeat42
                V5MetronomeSequence.Beat.B68 -> binding.rbBeat68
            }
            targetRb.setTextColor(getColor(R.color.fg_inverse))
            targetRb.setBackgroundResource(R.drawable.radius_round_fg_primary)
        }
        vm.isTapping.distinctUntilChanged().observe(this) {
            if (it) {
                binding.tvBpm.invisible()
                binding.lavTapping.visible()
            } else {
                binding.tvBpm.visible()
                binding.lavTapping.invisible()
            }
        }
    }


    companion object {
        const val TYPE_DRUM = 1
        const val TYPE_METRONOME = 2
        fun launch(context: Context, type: Int, uuid: String) {
            context.push<DrumMetronomeActivity>(
                bundleOf(
                    "type" to type,
                    "uuid" to uuid,
                )
            )
        }
    }
}

