package com.harman.partyband.drummetronome

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.bean.devinfofeat.V5DrumSequence
import com.harman.v5protocol.bean.devinfofeat.V5DrumStartEnd

/**
 * @Description viewmodel for [DrumTimeSignatureActivity]
 * <AUTHOR>
 * @Time 2024/12/30
 */
class DrumTimeSignatureViewModel(state: SavedStateHandle) : ViewModel() {
    private val device = DeviceStore.find(state.get<String>("uuid")!!) as? PartyBandDevice
    var currentType: V5DrumSequence.Type? = state.get<V5DrumSequence.Type>("type")
        private set
    private val isPlaying = state.get<Boolean>("isPlaying") ?: false
    val currentBeat = MutableLiveData(state.get<V5DrumSequence.Type>("type")?.beat)
    val currentBeatTypeStateList = MutableLiveData<List<DrumTimeSignatureItemState>>(genCurrentBeatTypeStateList())
    private fun genCurrentBeatTypeStateList() =
        currentBeat.value?.let {
            V5DrumSequence.beatTypes[it]?.map { beat ->
                DrumTimeSignatureItemState(beat, currentType == beat)
            }
        }

    fun switchBeat(beat: V5DrumSequence.Beat) {
        if (beat == currentBeat.value) {
            return
        }
        currentBeat.value = beat
        currentBeatTypeStateList.value = genCurrentBeatTypeStateList()
    }

    fun switchType(type: V5DrumSequence.Type) {
        if (currentType == type) {
            return
        }
        if (isPlaying) {
            device?.asyncSetDevInfoFeat(V5DrumSequence(type))
        } else {
            device?.asyncSetDevInfoFeat(V5DrumSequence(type), V5DrumStartEnd(true))
        }
        currentType = type
        currentBeatTypeStateList.value = genCurrentBeatTypeStateList()
    }

    override fun onCleared() {
        super.onCleared()
        if (!isPlaying) {
            device?.asyncSetDevInfoFeat(V5DrumStartEnd(false))
        }
    }
}