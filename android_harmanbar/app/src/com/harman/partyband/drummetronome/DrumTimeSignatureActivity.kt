package com.harman.partyband.drummetronome

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.harman.BaseVBViewHolder
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityDrumTimeSignatureBinding
import com.harman.bar.app.databinding.LayoutItemWithCheckboxBinding
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.popResult
import com.harman.partylight.util.syncPush
import com.harman.v5protocol.bean.devinfofeat.V5DrumSequence
import com.harman.widget.AppCompatBaseActivity

/**
 * @Description partyband time signature setting activity
 * <AUTHOR>
 * @Time 2024/12/27
 */
class DrumTimeSignatureActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityDrumTimeSignatureBinding.inflate(layoutInflater) }
    private val vm by viewModels<DrumTimeSignatureViewModel>()
    private val rbList by lazy {
        listOf(
            binding.rbBeat44.apply {
                text = V5DrumSequence.Beat.B44.beatName
                tag = V5DrumSequence.Beat.B44
            },
            binding.rbBeat43.apply {
                text = V5DrumSequence.Beat.B43.beatName
                tag = V5DrumSequence.Beat.B43
            },
            binding.rbBeat42.apply {
                text = V5DrumSequence.Beat.B42.beatName
                tag = V5DrumSequence.Beat.B42
            },
            binding.rbBeat68.apply {
                text = V5DrumSequence.Beat.B68.beatName
                tag = V5DrumSequence.Beat.B68
            },
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initView()
        observeModel()
    }

    private fun initView() {
        binding.appbar.tvTitle.text = getString(R.string.time_signature)
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
        rbList.forEach {
            it.setOnClickListener { _ ->
                vm.switchBeat(it.tag as V5DrumSequence.Beat)
            }
        }
        binding.rvTypes.adapter = DrumTimeSignatureAdapter(vm, this)
        binding.rvTypes.itemAnimator = null
    }

    private fun observeModel() {
        vm.currentBeat.observe(this) { beat ->
            rbList.forEach { rb ->
                rb.setTextColor(getColor(R.color.fg_primary))
                rb.setBackgroundResource(R.drawable.radius_round_bg_card)
            }
            val targetRb = when (beat) {
                V5DrumSequence.Beat.B44 -> binding.rbBeat44
                V5DrumSequence.Beat.B43 -> binding.rbBeat43
                V5DrumSequence.Beat.B42 -> binding.rbBeat42
                V5DrumSequence.Beat.B68 -> binding.rbBeat68
                null -> null
            }
            targetRb?.setTextColor(getColor(R.color.fg_inverse))
            targetRb?.setBackgroundResource(R.drawable.radius_round_fg_primary)
        }
    }

    override fun finish() {
        super.finish()
        popResult(vm.currentType)
    }

    companion object {
        suspend fun launch(context: Context, uuid: String, type: V5DrumSequence.Type?, isPlaying: Boolean) =
            context.syncPush<DrumTimeSignatureActivity, V5DrumSequence.Type>(
                bundleOf(
                    "uuid" to uuid,
                    "type" to type,
                    "isPlaying" to isPlaying,
                )
            )
    }
}

private class DrumTimeSignatureAdapter(
    val vm: DrumTimeSignatureViewModel,
    lifeOwner: LifecycleOwner,
) : RecyclerView.Adapter<BaseVBViewHolder<LayoutItemWithCheckboxBinding>>() {
    private val differ = generateSimpleDiffer<DrumTimeSignatureItemState>()

    init {
        vm.currentBeatTypeStateList.observe(lifeOwner) {
            differ.submitList(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVBViewHolder<LayoutItemWithCheckboxBinding> {
        return BaseVBViewHolder(
            LayoutItemWithCheckboxBinding.bind(
                LayoutInflater.from(parent.context).inflate(R.layout.layout_item_with_checkbox, parent, false)
            )
        )
    }

    override fun getItemCount(): Int = differ.currentList.size

    override fun onBindViewHolder(holder: BaseVBViewHolder<LayoutItemWithCheckboxBinding>, position: Int) {
        val item = differ.currentList[position]
        val binding = holder.binding
        binding.rbSelect.apply {
            if (item.checked) {
                setImageResource(R.drawable.svg_icon_security_selected)
            } else {
                setImageResource(R.drawable.svg_icon_security_unselected)
            }
        }
        binding.tvContent.apply {
            text = getDrumStyleName(context, item.type)
            if (item.checked) {
                setTextColor(context.getColor(R.color.fg_activate))
            } else {
                setTextColor(context.getColor(R.color.fg_primary))
            }
        }
        binding.root.setOnClickListener {
            vm.switchType(item.type)
        }
    }
}



