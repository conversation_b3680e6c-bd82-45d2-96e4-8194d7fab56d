package com.harman.partyband.ota

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.task.partyband.PartyBandOtaError
import com.harman.task.partyband.PartyBandOtaObserver
import com.harman.task.partyband.PartyBandOtaState
import com.harman.task.partyband.PartyBandOtaTask
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5LeftDeviceBatteryStatus
import com.jbl.one.configuration.model.Firmware
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.app.debug.DebugConfigKey
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlin.math.max

/**
 * @Description viewmodel for [PartyBandOtaActivity]
 * <AUTHOR>
 * @Time 2024/12/18
 */
var isBbOtaProcessing = false
    private set

internal class PartyBandOtaViewModel(savedState: SavedStateHandle) : ViewModel() {
    val device = DeviceStore.find(savedState.get<String>("uuid")!!) as? PartyBandDevice
    val firmware = savedState.get<Firmware>("firmware")
    val whatsNewText by lazy {
        firmware?.findWhatsNewByLang(lan = LocaleLanConfigUtil.getLocaleLan())?.points?.let { pts ->
            val sb = StringBuilder()
            pts.forEachIndexed { i, s ->
                sb.append(i + 1).append(". ").append(s)
                if (i < pts.size - 1) {
                    sb.append('\n').append('\n')
                }
            }
            sb.toString()
        } ?: ""
    }
    private val offlinePath = savedState.get<String>("offlinePath")
    private val otaTask by lazy {
        device?.let {
            PartyBandOtaTask(
                it,
                firmware,
                DebugConfigKey.circleUpgrade,
                PartyBandOtaObserver(
                    onDownloadPercentage = { p, r ->
                        currentProcessPercentage.value = ProcessPercentage(p, max((r / 1000 / 60).toInt(), 1))
                    },
                    onTransferPercentage = { p, r ->
                        currentProcessPercentage.value = ProcessPercentage(p, max((r / 1000 / 60).toInt(), 1))
                    },
                    onInstallPercentage = { p, r ->
                        currentProcessPercentage.value = ProcessPercentage(p, max((r / 1000 / 60).toInt(), 1))
                    },
                    onDownloadNetworkConfirm = { isWifi ->
                        if (!isWifi) {
                            val completer = CompletableDeferred<Boolean>()
                            eventStream.tryEmit(PBOVMEvent.NoWifiConfirming { isContinue ->
                                completer.complete(isContinue)
                            })
                            return@PartyBandOtaObserver completer.await()
                        } else {
                            return@PartyBandOtaObserver true
                        }
                    },
                    onStateChanged = { state, error ->
                        if (PartyBandOtaState.Idle == state) {
                            eventStream.tryEmit(if (null == error) PBOVMEvent.Success else PBOVMEvent.Failed(error))
                        } else {
                            eventStream.tryEmit(PBOVMEvent.OnStateChangedWithoutIdle(state))
                        }
                    }
                ),
                offlineOtaFilePath = offlinePath
            )
        }

    }
    private val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            super.onDevFeat(devInfoMap, isNotify)
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.LeftDeviceBatteryStatus] as? V5LeftDeviceBatteryStatus)?.also {
                    if (currentEvent is PBOVMEvent.LowPower || currentEvent is PBOVMEvent.Initialized) {
                        init2()
                    }
                }
            }
        }
    }

    val eventStream = MutableSharedFlow<PBOVMEvent>(replay = 1, extraBufferCapacity = 1)
    val currentProcessPercentage = MutableLiveData<ProcessPercentage>()
    private var currentEvent: PBOVMEvent? = null

    init {
        isBbOtaProcessing = true
        init2()
        viewModelScope.launch {
            eventStream.collect { ev ->
                currentEvent = ev
            }
        }
        device?.registerDeviceListener(deviceListener)
    }

    override fun onCleared() {
        super.onCleared()
        isBbOtaProcessing = false
        device?.unregisterDeviceListener(deviceListener)
    }

    private fun init2() {
        if (null == device) {
            return
        }
        if (!powerCheck()) {
            eventStream.tryEmit(PBOVMEvent.LowPower)
        } else {
            eventStream.tryEmit(PBOVMEvent.Initialized)
        }
    }

    private fun powerCheck(): Boolean {
        return device?.let { dev ->
            if (dev.isTrio() && !dev.isCharging && dev.batteryLevel < MIN_POWER_LEVEL_WITHOUT_AC_TRIO) {
                return@let false
            }
            if (dev.isSolo() && dev.batteryLevel < MIN_POWER_LEVEL_WITHOUT_AC_SOLO) {
                return@let false
            }
            return@let true
        } ?: false
    }

    fun resetState() {
        init2()
    }

    fun exit() {
        otaTask?.exit()
    }

    fun startOtaFlow() {
        eventStream.tryEmit(PBOVMEvent.Initialized)
        otaTask?.startFlow()
    }

    companion object {
        const val MIN_POWER_LEVEL_WITHOUT_AC_TRIO = 30
        const val MIN_POWER_LEVEL_WITHOUT_AC_SOLO = 50
    }
}

data class ProcessPercentage(val percentage: Int, val remainingMinutes: Int?)
internal sealed class PBOVMEvent {
    data object Initialized : PBOVMEvent()
    data object LowPower : PBOVMEvent()
    data class NoWifiConfirming(val onConfirm: (isContinue: Boolean) -> Unit) : PBOVMEvent()
    data class OnStateChangedWithoutIdle(val state: PartyBandOtaState) : PBOVMEvent()
    data class Failed(val error: PartyBandOtaError) : PBOVMEvent()
    data object Success : PBOVMEvent()
}