package com.harman.partyband.ota

import android.app.Activity
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityPartybandOtaBinding
import com.harman.deviceImgForce
import com.harman.deviceImgPath
import com.harman.formatProgress
import com.harman.home.HomePagesActivity
import com.harman.ota.WhatsNewDialog
import com.harman.partylight.util.gone
import com.harman.partylight.util.invisible
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.setEnable
import com.harman.whatsNewTitle
import com.harman.discover.bean.PartyBandDevice
import com.harman.task.partyband.PartyBandOtaError
import com.harman.task.partyband.PartyBandOtaState
import com.harman.widget.AppCompatBaseActivity
import com.harman.widget.ComponentGuideDialog
import com.jbl.one.configuration.model.Firmware
import com.wifiaudio.view.component.ComponentConfig
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

/**
 * @Description ota activity for [PartyBandDevice]
 * <AUTHOR>
 * @Time 2024/12/18
 */
class PartyBandOtaActivity : AppCompatBaseActivity() {
    private val vm by viewModels<PartyBandOtaViewModel>()
    private val binding by lazy { ActivityPartybandOtaBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.decorView.keepScreenOn = true
        setContentView(binding.root)
        buildAppbar()
        observeModel()
    }

    override fun onBackPressed() {
        if (binding.appbar.ivLeading.isVisible) {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        vm.exit()
        super.onDestroy()
    }

    private fun observeModel() {
        // ui state
        lifecycleScope.launch {
            vm.eventStream.distinctUntilChanged().collect {
                buildAppbarLeadingVisibility(it)
                buildImgDeviceArea(it)
                buildNewVersion(it)
                buildTvMainTitle(it)
                buildTvMainDesc(it)
                buildControlTip(it)
                buildBtnMain(it)
                buildBtnSecond(it)
                buildProgressVisible(it)
            }
        }
        vm.currentProcessPercentage.observe(this) {
            buildProgressAndRemainingText(it)
        }
        //event
        lifecycleScope.launch {
            vm.eventStream.collect {
                when (it) {
                    is PBOVMEvent.Failed -> handleErrorEvent(it.error)
                    is PBOVMEvent.NoWifiConfirming -> handleNoWifiEvent(it)
                    else -> Unit
                }
            }
        }
    }

    private suspend fun handleNoWifiEvent(noWifiEvent: PBOVMEvent.NoWifiConfirming) {
        val ret = ComponentGuideDialog(
            this,
            getString(R.string.continue_update),
            getString(R.string.current_wifi_connection_not_detected_would_you_like_to_continue_downloading_the_update_package_with_your_cellular_data),
            confirmText = getString(R.string.jbl_CONTINUE),
        ).syncShow()
        noWifiEvent.onConfirm.invoke(ret ?: false)
    }

    private fun handleErrorEvent(error: PartyBandOtaError) {
        when (error) {
            PartyBandOtaError.CanNotFindUsbDevice -> {
                lifecycleScope.launch {
                    ComponentGuideDialog(
                        this@PartyBandOtaActivity,
                        getString(R.string.plug_in_to_start_updating),
                        getString(R.string.plug_one_side_of_the_cable_type_c_to_type_c_to_the_speaker_and_the_other_side_to_your_phone_to_start_the_updating_process),
                        getDrawable(R.drawable.ic_plug_cable),
                        confirmText = getString(R.string.jbl_CONTINUE),
                    ).syncShow()
                    vm.resetState()
                }
            }

            PartyBandOtaError.BleConnctFail,
            PartyBandOtaError.PrepareOtaPathFail,
            PartyBandOtaError.NoUsbPermission,
            PartyBandOtaError.OpenUsbDeviceFail,
            PartyBandOtaError.ConfirmNetworkFail -> vm.resetState()

            else -> Unit
        }
    }

    private fun buildAppbarLeadingVisibility(event: PBOVMEvent) {
        binding.appbar.ivLeading.apply {
            when (event) {
                PBOVMEvent.Initialized,
                PBOVMEvent.LowPower,
                is PBOVMEvent.NoWifiConfirming -> visible()

                is PBOVMEvent.OnStateChangedWithoutIdle -> {
                    when (event.state) {
                        PartyBandOtaState.ModeSwitching,
                        PartyBandOtaState.USBPreparing,
                        PartyBandOtaState.BleConnChecking,
                        PartyBandOtaState.NetWorkChecking -> visible()

                        PartyBandOtaState.Downloading,
                        PartyBandOtaState.Transferring,
                        PartyBandOtaState.Installing,
                        PartyBandOtaState.BleReconnecting -> invisible()

                        PartyBandOtaState.Idle -> Unit
                    }
                }

                PBOVMEvent.Success -> invisible()
                is PBOVMEvent.Failed -> {
                    when (event.error) {
                        PartyBandOtaError.DownloadFail,
                        PartyBandOtaError.TransferFail,
                        PartyBandOtaError.InstallFail,
                        PartyBandOtaError.ReconnectFail -> invisible()

                        else -> visible()
                    }
                }
            }
        }
    }

    private fun buildProgressVisible(event: PBOVMEvent) {
        binding.pb.apply {
            when (event) {
                is PBOVMEvent.OnStateChangedWithoutIdle -> {
                    when (event.state) {
                        PartyBandOtaState.Downloading,
                        PartyBandOtaState.Transferring,
                        PartyBandOtaState.Installing,
                        PartyBandOtaState.BleReconnecting -> visible()

                        else -> gone()
                    }
                }

                else -> gone()
            }
        }
    }

    private fun buildProgressAndRemainingText(percentage: ProcessPercentage) {
        binding.pb.apply {
            if (isVisible) {
                formatProgress(this, percentage.percentage)
            }
        }
        binding.tvMainDesc.apply {
            text = getString(R.string.min_remaining).format(percentage.remainingMinutes ?: 10)
        }
    }

    private fun buildTvMainDesc(event: PBOVMEvent) {
        binding.tvMainDesc.apply {
            text = when (event) {
                is PBOVMEvent.Failed -> {
                    when (event.error) {
                        PartyBandOtaError.InstallFail -> getString(R.string.sorry_the_update_was_not_complete_no_changes_were_made)
                        PartyBandOtaError.ReconnectFail -> getString(R.string.sorry_the_speaker_is_currently_offline)
                        else -> ""
                    }
                }

                PBOVMEvent.Success -> getString(R.string.harmanbar_jbl_Current_version___).format(vm.firmware?.version)
                else -> ""
            }
        }
    }

    private fun buildBtnSecond(event: PBOVMEvent) {
        binding.btnSecond.apply {
            when (event) {
                is PBOVMEvent.Failed -> {
                    when (event.error) {
                        PartyBandOtaError.DownloadFail,
                        PartyBandOtaError.TransferFail,
                        PartyBandOtaError.InstallFail -> {
                            visible()
                            setBtnText(getString(R.string.jbl_UPDATE_LATER))
                            setOnClickListener {
                                finish()
                            }
                        }

                        else -> gone()
                    }
                }

                else -> gone()
            }
        }
    }

    private fun buildBtnMain(event: PBOVMEvent) {
        binding.btnMain.apply {
            when (event) {
                PBOVMEvent.Initialized -> {
                    setBtnText(getString(R.string.jbl_UPDATE))
                    setBtnEnabled(true)
                    visible()
                    setOnClickListener { vm.startOtaFlow() }
                }

                PBOVMEvent.LowPower -> {
                    setBtnEnabled(false)
                    setOnClickListener(null)
                }

                is PBOVMEvent.NoWifiConfirming -> Unit
                is PBOVMEvent.OnStateChangedWithoutIdle -> {
                    when (event.state) {
                        PartyBandOtaState.BleConnChecking,
                        PartyBandOtaState.ModeSwitching,
                        PartyBandOtaState.USBPreparing,
                        PartyBandOtaState.NetWorkChecking -> {
                            setBtnEnabled(true)
                            setBtnType(ComponentConfig.BTN_LOADING)
                        }

                        else -> gone()
                    }
                }

                is PBOVMEvent.Failed -> {
                    visible()
                    setBtnType(ComponentConfig.BTN_REGULAR)
                    when (event.error) {
                        PartyBandOtaError.ReconnectFail -> {
                            setBtnText(getString(R.string.jbl_BACK_TO_DASHBOARD))
                            setOnClickListener { HomePagesActivity.resumeDashboardWithoutRecreate(this@PartyBandOtaActivity) }
                        }

                        PartyBandOtaError.DownloadFail,
                        PartyBandOtaError.TransferFail,
                        PartyBandOtaError.InstallFail -> {
                            setBtnText(getString(R.string.jbl_TRY_AGAIN))
                        }

                        else -> Unit
                    }
                }

                PBOVMEvent.Success -> {
                    visible()
                    setBtnType(ComponentConfig.BTN_REGULAR)
                    setBtnText(getString(R.string.harmanbar_jbl_DONE))
                    setOnClickListener { finish() }
                }
            }
        }
    }

    private fun buildControlTip(event: PBOVMEvent) {
        binding.tvControlTip.apply {
            when (event) {
                PBOVMEvent.Initialized -> gone()
                PBOVMEvent.LowPower -> {
                    visible()
                    text = if (vm.device?.isSolo() == true) getString(
                        R.string.you_need_to_have_at_least_percent_battery_to_continue,
                        "${PartyBandOtaViewModel.MIN_POWER_LEVEL_WITHOUT_AC_SOLO}%"
                    )
                    else getString(R.string.harmanbar_jbl_Plug_in_power_to_continue)
                }

                is PBOVMEvent.NoWifiConfirming -> Unit
                is PBOVMEvent.OnStateChangedWithoutIdle -> {
                    when (event.state) {
                        PartyBandOtaState.Downloading,
                        PartyBandOtaState.Transferring -> {
                            visible()
                            text = getString(R.string.do_not_turn_off_product_or_unplug_power_cable)
                        }

                        PartyBandOtaState.Installing,
                        PartyBandOtaState.BleReconnecting -> {
                            visible()
                            text = getString(R.string.jbl_Getting_ready__please_wait___)
                        }

                        else -> Unit
                    }
                }

                is PBOVMEvent.Failed,
                PBOVMEvent.Success -> gone()
            }
        }
    }


    private fun buildImgDeviceArea(event: PBOVMEvent) {
        binding.imgDevice.apply {
            deviceImgForce(this, vm.device)
            alpha = when (event) {
                is PBOVMEvent.Failed -> {
                    when (event.error) {
                        PartyBandOtaError.DownloadFail,
                        PartyBandOtaError.TransferFail,
                        PartyBandOtaError.InstallFail,
                        PartyBandOtaError.ReconnectFail -> 0.5f

                        else -> 1f
                    }
                }

                else -> 1f
            }
        }
        binding.ivAlert.apply {
            if (binding.imgDevice.alpha < 1f) visible()
            else gone()
        }
    }


    private fun buildNewVersion(event: PBOVMEvent) {
        binding.gNewVersion.apply {
            when (event) {
                PBOVMEvent.Initialized,
                PBOVMEvent.LowPower -> visible()

                is PBOVMEvent.OnStateChangedWithoutIdle -> {
                    when (event.state) {
                        PartyBandOtaState.Downloading -> gone()
                        else -> Unit
                    }
                }

                else -> Unit
            }
        }
        if (binding.gNewVersion.isVisible) {
            whatsNewTitle(binding.tvNewVersion, vm.firmware?.version)
            binding.tvNewVersionDesc.text = vm.whatsNewText
        }

        binding.tvNewVersionDescLearnMore.apply {
            if (binding.gNewVersion.isVisible) {
                binding.tvNewVersionDesc.post {
                    if (binding.tvNewVersionDesc.lineCount >= binding.tvNewVersionDesc.maxLines) {
                        visible()
                        binding.tvNewVersionDescLearnMore.setOnClickListener {
                            WhatsNewDialog(
                                activity = this@PartyBandOtaActivity,
                                whatsNewContents = vm.whatsNewText,
                                vm.device.deviceImgPath(),
                                vm.firmware?.version,
                            ).show()
                        }
                    } else {
                        invisible()
                    }
                }
            } else {
                invisible()
            }
        }
    }

    private fun buildTvMainTitle(event: PBOVMEvent) {
        binding.tvMainTitle.apply {
            text = when (event) {
                PBOVMEvent.Initialized,
                PBOVMEvent.LowPower,
                is PBOVMEvent.NoWifiConfirming -> ""

                is PBOVMEvent.OnStateChangedWithoutIdle -> {
                    when (event.state) {
                        PartyBandOtaState.Downloading -> getString(R.string.jbl_Downloading)
                        PartyBandOtaState.Transferring -> getString(R.string.transferring)
                        PartyBandOtaState.Installing,
                        PartyBandOtaState.BleReconnecting -> getString(R.string.jbl_Installing)

                        else -> ""
                    }
                }

                PBOVMEvent.Success -> getString(R.string.jbl_You_Are_All_Set)

                is PBOVMEvent.Failed -> when (event.error) {
                    PartyBandOtaError.DownloadFail -> getString(R.string.jbl_Download_Unsuccessful)
                    PartyBandOtaError.TransferFail -> getString(R.string.transfer_unsuccessful)
                    PartyBandOtaError.InstallFail -> getString(R.string.update_unsuccessful)
                    PartyBandOtaError.ReconnectFail -> getString(R.string.reconnect_to_continue)
                    else -> ""
                }
            }
        }
    }

    private fun buildAppbar() {
        binding.appbar.tvTitle.text = getString(R.string.jbl_Software_Update)
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
    }

    companion object {
        private const val TAG = "PartyBandOtaActivity"
        fun launch(act: Activity, uuid: String, firmware: Firmware?, offlinePath: String? = null) {
            act.push<PartyBandOtaActivity>(
                bundleOf(
                    "uuid" to uuid,
                    "firmware" to firmware,
                    "offlinePath" to offlinePath,
                )
            )
        }
    }
}