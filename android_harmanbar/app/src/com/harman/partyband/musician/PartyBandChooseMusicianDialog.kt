package com.harman.partyband.musician

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updatePadding
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.harman.BottomPopUpDialog
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogPartybandChooseMusicianBinding
import com.harman.bar.app.databinding.ItemPartybandChooseMusicianBinding
import com.harman.discover.bean.PartyBandDevice
import com.harman.partyband.widget.AlphaPagerTransformer
import com.harman.v5protocol.bean.devinfofeat.SoloMusicianEnum
import com.harman.v5protocol.bean.devinfofeat.V5DeviceOOBE
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2024/12/23
 */
class PartyBandChooseMusicianDialog(
    context: Context,
    private val device: PartyBandDevice,
    private val currentMusician: V5DeviceOOBE? = null,
) :
    BottomPopUpDialog(context) {
    private val binding by lazy { DialogPartybandChooseMusicianBinding.inflate(layoutInflater) }
    private val data = listOf(
        MusicianData(R.mipmap.pic_partyband_musician1, R.string.guitarist, R.string.hello_what_type_of_musician_you_are, SoloMusicianEnum.GUITARIST),
        MusicianData(
            R.mipmap.pic_partyband_musician2,
            R.string.singing_guitarist,
            R.string.hello_what_type_of_musician_you_are,
            SoloMusicianEnum.GUITAR_VOCALIST
        ),
        MusicianData(R.mipmap.pic_partyband_musician3, R.string.singer, R.string.hello_what_type_of_musician_you_are, SoloMusicianEnum.SINGER),
    )
    private val currentIndex = MutableLiveData(0)
    private val resultCompleter = CompletableDeferred<V5DeviceOOBE>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        setContentView(binding.root)
    }

    override fun onStart() {
        super.onStart()
        build()
        observeState()
    }

    suspend fun syncShow(): V5DeviceOOBE {
        show()
        return resultCompleter.await()
    }

    private fun observeState() {
        currentIndex.observe(this) {
            binding.indicator.currentIndex = it
            binding.tvDesc.text = context.getString(data[it].textRes2)
        }
    }

    private fun build() {
        binding.vp2.also {
            it.adapter = PagerAdapter(data, context)
            it.registerOnPageChangeCallback(object : OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    currentIndex.value = position
                }
            })
            (it.getChildAt(0) as? RecyclerView)?.apply {
                updatePadding(left = 220, right = 220)
                clipToPadding = false
            }
            it.offscreenPageLimit = data.size / 2
            it.setPageTransformer(AlphaPagerTransformer())
            it.currentItem = data.indexOfFirst { each -> currentMusician?.musician == each.musician }
        }
        binding.indicator.apply {
            totalIndex = data.size
        }
        binding.btnConfirm.setOnClickListener {
            lifecycleScope.launch {
                V5DeviceOOBE(data[currentIndex.value!!].musician).also {
                    device.setDevInfoFeat(it)
                    //Delay to ensure that the device switch is successful, and then optimize later
                    delay(150)
                    dismiss()
                    resultCompleter.complete(it)
                }
            }
        }
    }
}

private class PagerAdapter(val data: List<MusicianData>, val context: Context) : RecyclerView.Adapter<PagerAdapter.VH>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(
            ItemPartybandChooseMusicianBinding.bind(
                LayoutInflater.from(context).inflate(R.layout.item_partyband_choose_musician, parent, false)
            )
        )
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val item = data[position]
        holder.binding.tv.text = context.getString(item.textRes)
        holder.binding.iv.setImageResource(item.picRes)
    }

    class VH(val binding: ItemPartybandChooseMusicianBinding) : ViewHolder(binding.root)
}

private data class MusicianData(val picRes: Int, val textRes: Int, val textRes2: Int, val musician: SoloMusicianEnum)


