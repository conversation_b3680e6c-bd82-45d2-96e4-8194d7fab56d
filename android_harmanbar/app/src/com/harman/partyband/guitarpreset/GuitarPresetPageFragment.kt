package com.harman.partyband.guitarpreset

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.FragmentGuitarPresetPageBinding
import com.harman.partyband.guitarpreset.widget.SwipeGestureListener
import com.harman.partylight.util.invisible
import com.harman.partylight.util.visible
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/25
 */
class GuitarPresetPageFragment : Fragment() {
    private val pPresetId by lazy { arguments?.getInt("presetId")!! }
    private val rootVm by activityViewModels<GuitarPresetViewModel>()
    private val vm by lazy {
        ViewModelProvider(this, GuitarPresetPageViewModelFactory(rootVm, pPresetId))[GuitarPresetPageViewModel::class.java]
    }
    private val binding by lazy { FragmentGuitarPresetPageBinding.inflate(layoutInflater) }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        build()
        observeModel()
        (requireActivity() as GuitarPresetActivity).guideView.checkGuide()
    }

    private fun build() {
        binding.tvSave.setOnClickListener {
            rootVm.saveCurrent()
        }
        binding.tvSaveAs.setOnClickListener {
            rootVm.currentUiPreset()?.also {
                lifecycleScope.launch {
                    DuplicateSaveActivity.launch(requireContext(), PageType.Save, it)?.also { ret ->
                        rootVm.saveAsCurrent(ret.name, ret.genre)
                    }
                }
            }
        }
    }

    private fun observeModel() {
        vm.slotNoiseGate.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivNoiseGate, binding.tvNoiseGate)
        }
        vm.slotCompressor.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivCompressor, binding.tvCompressor)
        }
        vm.slotWah.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivWah, binding.tvWah)
        }
        vm.slotAmp.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivAmp, binding.tvAmp, R.drawable.slot_inactive3)
        }
        vm.slotCab.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivCab, binding.tvCab, R.drawable.slot_inactive2)
        }
        vm.slotMod.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivMod, binding.tvMod)
        }
        vm.slotDelay.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivDelay, binding.tvDelay)
        }
        vm.slotReverb.observe(viewLifecycleOwner) {
            buildSlot(it, binding.ivReverb, binding.tvReverb)
        }
        vm.anySlotChanged.observe(viewLifecycleOwner) {
            binding.gSaveArea.apply {
                if (it) visible() else invisible()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        vm.fetchPreset()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun buildSlot(item: UiMiniSlotItem, iv: ImageView, tv: TextView, disableImgResId: Int = R.drawable.slot_inactive1) {
        iv.apply {
            if (item.isBypass || null == item.resIds) {
                setImageResource(disableImgResId)
            } else {
                setImageResource(item.resIds.imgWithKnob)
            }
            GestureDetector(
                requireContext(),
                SwipeGestureListener(
                    onSwipeUp = {
                        vm.setSlotByPass(item.source, true)
                    },
                    onSwipeDown = {
                        vm.setSlotByPass(item.source, false)
                    },
                    onTap = {
                        SlotDetailActivity.launch(
                            requireContext(), rootVm.device?.UUID!!, rootVm.chNum, pPresetId, item.source, vm.findSavedSlotItem(item.source.slotId)
                        )
                    },
                    onScroll = {
                        parent.requestDisallowInterceptTouchEvent(true)
                    },
                )
            ).also {
                setOnTouchListener { _, event -> it.onTouchEvent(event!!) }
            }
        }
        tv.apply {
            setTextColor(requireContext().getColor(if (item.isEdited) R.color.fg_activate else R.color.fg_primary))
            item.resIds?.also { text = getString(it.name) }
        }
    }

    companion object {
        private const val TAG = "GuitarPresetPageFragment"
        fun newInstance(presetId: Int) = GuitarPresetPageFragment().apply { arguments = bundleOf("presetId" to presetId) }
    }
}

