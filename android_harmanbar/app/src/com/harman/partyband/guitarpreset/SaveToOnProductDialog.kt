package com.harman.partyband.guitarpreset

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.blankj.utilcode.util.ResourceUtils
import com.harman.BottomPopUpDialogFragment
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogSaveToOnProductBinding
import com.harman.bar.app.databinding.ItemSaveToOnProductBinding
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/26
 */
class SaveToOnProductDialog(private val activity: GuitarPresetActivity) : BottomPopUpDialogFragment(activity) {
    private val binding by lazy { DialogSaveToOnProductBinding.inflate(layoutInflater) }
    val vm by viewModels<SaveToOnProductViewModel>()
    override fun getContentView() = binding.root

    override fun buildContentView(view: View) {
        vm.init(activity.vm.uiPresetSimpleList.value ?: listOf(), activity.vm.currentUiPreset.value)
        binding.rv.adapter = MyAdapter(this)
        binding.rv.itemAnimator = null
        binding.tvDone.setOnClickListener {
            activity.vm.saveToOnProductCurrent(vm.checkedPresetId())
            dismiss()
        }

        binding.tvCancel.setOnClickListener {
            dismiss()
        }
    }

    override fun observeModel() {}

    override fun prepareData() {}

}

private class MyAdapter(private val dialog: SaveToOnProductDialog) :
    RecyclerView.Adapter<MyAdapter.VH>() {
    private val differ = generateSimpleDiffer<SaveToOnProductItem>()

    init {
        dialog.vm.items.observe(dialog) {
            differ.submitList(it)
        }
    }

    class VH(val binding: ItemSaveToOnProductBinding) : ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return LayoutInflater.from(parent.context).inflate(R.layout.item_save_to_on_product, parent, false).let {
            VH(ItemSaveToOnProductBinding.bind(it))
        }
    }

    override fun getItemCount() = differ.currentList.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        val item = differ.currentList[position]
        holder.binding.tvName.apply {
            if (null == item.uiPresetSimple) {
                setCompoundDrawablesRelativeWithIntrinsicBounds(ResourceUtils.getDrawable(R.drawable.ic_add), null, null, null)
                text = dialog.getString(R.string.harmanbar_NewPreset_Empty)
            } else {
                setCompoundDrawablesRelativeWithIntrinsicBounds(ResourceUtils.getDrawable(R.drawable.ic_speaker), null, null, null)
                text = item.uiPresetSimple.presetName
            }
        }
        holder.binding.tvName2.apply {
            if (item.isChecked && null != item.uiPresetSimple) {
                visible()
                text = dialog.vm.current?.presetName ?: ""
            } else {
                gone()
            }
        }
        holder.binding.ivChecker.apply {
            setImageResource(if (item.isChecked) R.drawable.svg_icon_security_selected else R.drawable.svg_icon_security_unselected)
        }

        holder.binding.root.setOnClickListener {
            dialog.vm.check(position)
        }
    }
}

class SaveToOnProductViewModel : ViewModel() {
    val items = MutableLiveData<List<SaveToOnProductItem>>()
    private var checkedIndex = -1
    var current: UiPresetSimpleItem? = null
        private set

    fun init(presetSimpleList: List<UiPresetSimpleItem>, current: UiPresetSimpleItem?) {
        this.current = current
        val list = presetSimpleList.filter { it.isOnProduct }.map { SaveToOnProductItem(it) }.toMutableList()
        if (list.size < 6) {
            checkedIndex = list.size
            while (list.size < 6) {
                list.add(SaveToOnProductItem())
            }
        } else {
            checkedIndex = 0
        }
        list[checkedIndex].isChecked = true
        items.value = list
    }

    fun check(index: Int) {
        val newList = items.value?.toMutableList() ?: mutableListOf()
        newList[checkedIndex].copy(isChecked = false).also { newList[checkedIndex] = it }
        newList[index].copy(isChecked = true).also { newList[index] = it }
        checkedIndex = index
        items.value = newList
    }

    fun checkedPresetId() = items.value!![checkedIndex].uiPresetSimple?.presetId
}

data class SaveToOnProductItem(val uiPresetSimple: UiPresetSimpleItem? = null, var isChecked: Boolean = false)