package com.harman.partyband.guitarpreset.widget

import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import kotlin.math.abs

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/31
 */
class SwipeGestureListener(
    private val onSwipeUp: () -> Unit,
    private val onSwipeDown: () -> Unit,
    private val onTap: (() -> Unit)? = null,
    private val onScroll: (() -> Unit)? = null,
) : SimpleOnGestureListener() {
    override fun onDown(e: MotionEvent): Boolean {
        return true
    }

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        val diffY = e2.y - e1!!.y
        val diffX = e2.x - e1.x
        if (abs(diffY.toDouble()) > abs(diffX.toDouble())) {
            if (abs(diffY.toDouble()) > SWIPE_THRESHOLD && abs(velocityY.toDouble()) > SWIPE_VELOCITY_THRESHOLD) {
                if (diffY > 0) {
                    onSwipeDown()
                } else {
                    onSwipeUp()
                }
            }
        }
        return true
    }

    override fun onSingleTapUp(e: MotionEvent): Boolean {
        if (null != onTap) {
            onTap.invoke()
            return true
        }
        return super.onSingleTapUp(e)
    }

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        onScroll?.invoke()
        return true
    }

    private fun onSwipeUp() = onSwipeUp.invoke()
    private fun onSwipeDown() = onSwipeDown.invoke()

    companion object {
        private const val SWIPE_THRESHOLD = 100
        private const val SWIPE_VELOCITY_THRESHOLD = 100
    }
}