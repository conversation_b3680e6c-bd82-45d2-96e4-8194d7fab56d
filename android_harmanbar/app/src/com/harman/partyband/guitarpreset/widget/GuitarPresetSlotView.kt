package com.harman.partyband.guitarpreset.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ViewGuitarPresetSlotBinding
import com.harman.dp
import com.harman.partyband.guitarpreset.resIds
import com.harman.partyband.widget.CircularRingProgressView
import com.harman.partylight.util.gone
import com.harman.partylight.util.visible
import com.harman.v5protocol.bean.devinfofeat.Algorithm
import com.harman.v5protocol.bean.devinfofeat.SlotValue
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.ratio0To100ValueToFloat
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.ratioTo0100Value
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.timeTo0100Value
import com.harman.v5protocol.bean.devinfofeat.SlotValueTypeEnum

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/20
 */
class GuitarPresetSlotView : ConstraintLayout, IGuitarPresetSlotView {
    private lateinit var binding: ViewGuitarPresetSlotBinding
    private val crpvs by lazy { listOf(binding.crpv1, binding.crpv2, binding.crpv3, binding.crpv4, binding.crpv5) }
    private val tvNames by lazy { listOf(binding.tvName1, binding.tvName2, binding.tvName3, binding.tvName4, binding.tvName5) }
    private var algorithm: Algorithm? = null
    var onRouteToSetValue: ((value: UiSlotValue) -> Unit)? = null

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }


    private fun init(context: Context, attrs: AttributeSet?) {
        binding = LayoutInflater.from(context).inflate(R.layout.view_guitar_preset_slot, this, true).let {
            ViewGuitarPresetSlotBinding.bind(it)
        }
    }

    private fun buildIvMaterial() {
        binding.ivMaterial.apply {
            if (null == algorithm) {
                setImageResource(R.drawable.slot_inactive1)
            } else {
                setImageResource(algorithm!!.resIds().img)
            }
        }
    }

    override fun setAlgorithm(id: Algorithm?) {
        if (id == algorithm) {
            return
        }
        algorithm = id
        buildIvMaterial()
    }

    override fun setValues(values: List<UiSlotValue>) {
        build(values, crpvs, tvNames)
        when (values.size) {
            1, 2 -> binding.gl1.setGuidelineBegin(72.dp())
            else -> binding.gl1.setGuidelineBegin(24.dp())
        }
        crpvs.forEachIndexed { idx, v ->
            v.setOnClickListener {
                onRouteToSetValue?.invoke(values[idx])
            }
        }
    }

    companion object {
        fun build(values: List<UiSlotValue>, crpvs: List<CircularRingProgressView>, tvNames: List<TextView>) {
            crpvs.forEach { it.tag = null }
            tvNames.forEach { it.tag = null }

            values.forEachIndexed { index, slotValue ->
                crpvs.getOrNull(index)?.tag = slotValue
                tvNames.getOrNull(index)?.tag = slotValue
            }
            crpvs.forEach { crpv ->
                (crpv.tag as? UiSlotValue)?.also { value ->
                    crpv.visible()
                    crpv.setMaxvalue(
                        when (value.value.type) {
                            SlotValueTypeEnum.Time -> 98
                            SlotValueTypeEnum.Shape -> 4
                            SlotValueTypeEnum.Ratio -> 100
                            else -> 100
                        }
                    )
                    crpv.setValue(
                        when (value.value.type) {
                            SlotValueTypeEnum.Time -> value.value.value.timeTo0100Value()
                            SlotValueTypeEnum.Shape -> value.value.value.toInt()
                            SlotValueTypeEnum.Ratio -> value.value.value.ratioTo0100Value()
                            else -> value.value.value.toInt()
                        },
                    )
                    crpv.valueFormatter = {
                        when (value.value.type) {
                            SlotValueTypeEnum.Time -> "${(it + 2) * 10}.0"
                            SlotValueTypeEnum.Shape -> "$it.0"
                            SlotValueTypeEnum.Ratio -> it.ratio0To100ValueToFloat().toString()
                            else -> "$it.0"
                        }
                    }
                    crpv.setTextColor(crpv.context.getColor(if (value.isEdited) R.color.fg_activate else R.color.fg_primary))
                } ?: run {
                    crpv.gone()
                }
            }
            tvNames.forEach { tv ->
                (tv.tag as? UiSlotValue)?.also { value ->
                    tv.visible()
                    tv.text = value.value.type.name
                    tv.setTextColor(tv.context.getColor(if (value.isEdited) R.color.fg_activate else R.color.fg_primary))
                } ?: run {
                    tv.gone()
                }
            }
        }
    }
}

interface IGuitarPresetSlotView {
    fun setAlgorithm(id: Algorithm?)
    fun setValues(values: List<UiSlotValue>)
}

data class UiSlotValue(val value: SlotValue, val isEdited: Boolean = false)