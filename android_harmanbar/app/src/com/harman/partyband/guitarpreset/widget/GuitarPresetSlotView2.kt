package com.harman.partyband.guitarpreset.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ViewGuitarPresetSlot2Binding
import com.harman.dp
import com.harman.partyband.guitarpreset.resIds
import com.harman.partyband.guitarpreset.specialGradientColors
import com.harman.v5protocol.bean.devinfofeat.Algorithm
import com.harman.v5protocol.bean.devinfofeat.SlotValue
import io.mockk.InternalPlatformDsl.toArray

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/20
 */
class GuitarPresetSlotView2 : ConstraintLayout, IGuitarPresetSlotView {
    private lateinit var binding: ViewGuitarPresetSlot2Binding
    private val crpvs by lazy { listOf(binding.crpv1, binding.crpv2, binding.crpv3, binding.crpv4, binding.crpv5) }
    private val tvNames by lazy { listOf(binding.tvName1, binding.tvName2, binding.tvName3, binding.tvName4, binding.tvName5) }
    private var algorithm: Algorithm? = null
    var onRouteToSetValue: ((value: UiSlotValue) -> Unit)? = null

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        binding = LayoutInflater.from(context).inflate(R.layout.view_guitar_preset_slot2, this, true).let {
            ViewGuitarPresetSlot2Binding.bind(it)
        }
        binding.ivMaterial.updateLayoutParams<ViewGroup.LayoutParams> {
            height = 238.dp()
            width = 9999.dp()
        }
    }

    private fun buildIvMaterial() {
        binding.ivMaterial.apply {
            if (null == algorithm) {
                setImageResource(R.drawable.slot_inactive3)
            } else {
                setImageResource(algorithm!!.resIds().img)
            }
        }
    }

    override fun setAlgorithm(id: Algorithm?) {
        if (id == algorithm) {
            return
        }
        algorithm = id
        buildIvMaterial()
    }

    override fun setValues(values: List<UiSlotValue>) {
        crpvs.forEachIndexed { idx, v ->
            v.middleGradientColors = algorithm?.specialGradientColors()?.toIntArray()
            v.setOnClickListener {
                onRouteToSetValue?.invoke(values[idx])
            }

        }
        GuitarPresetSlotView.build(values, crpvs, tvNames)
    }
}