package com.harman.partyband.guitarpreset.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.harman.bar.app.R
import com.harman.partyband.guitarpreset.resIds
import com.harman.v5protocol.bean.devinfofeat.Algorithm
import com.harman.v5protocol.bean.devinfofeat.SlotValue

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/20
 */
class GuitarPresetSlotView3 : AppCompatImageView, IGuitarPresetSlotView {
    private var algorithm: Algorithm? = null

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        adjustViewBounds = true
        setImageResource(R.drawable.slot_inactive2)
    }

    private fun build() {
        if (null == algorithm) {
            setImageResource(R.drawable.slot_inactive2)
        } else {
            setImageResource(algorithm!!.resIds().img)
        }
    }

    override fun setAlgorithm(id: Algorithm?) {
        if (id == algorithm) {
            return
        }
        algorithm = id
        build()
    }

    override fun setValues(values: List<UiSlotValue>) = Unit
}