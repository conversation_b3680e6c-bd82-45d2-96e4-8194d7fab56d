package com.harman.partyband.guitarpreset

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.partyband.guitarpreset.widget.UiSlotValue
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.Algorithm
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.SlotChangeEnum
import com.harman.v5protocol.bean.devinfofeat.SlotValue
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSlotInfo
import com.harman.v5protocol.bean.devinfofeat.V5SlotItem
import com.wifiaudio.view.pagesmsccontent.showDebugToast
import kotlinx.coroutines.launch

/**
 * @Description viewmodel for [SlotDetailActivity]
 * <AUTHOR>
 * @Time 2025/4/1
 */
class SlotDetailViewModel(savedState: SavedStateHandle) : ViewModel() {
    private val device = DeviceStore.find(savedState.get<String>("uuid")!!) as? PartyBandDevice
    private var slotItem = savedState.get<V5SlotItem>("slotItem")!!
    private var savedSlotItem = savedState.get<V5SlotItem>("savedSlotItem")!!
    private val chNum = savedState.get<V5ChannelNumber>("chNum")!!
    private val presetId = savedState.get<Int>("presetId")!!
    private val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            super.onDevFeat(devInfoMap, isNotify)
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.GuitarPresetSlotInfo] as? V5GuitarPresetSlotInfo)?.also {
                    handleGuitarPresetSlotInfo(it)
                }
            }
        }
    }

    val slotDetailItems = MutableLiveData<List<SlotDetailItem>>()

    init {
        slotDetailItems.value = slotItem.slotId.itsAlgorithms().map { algorithm ->
            if (algorithm == slotItem.algorithm) {
                SlotDetailItem(genUiSlotValueList(slotItem.valueList ?: listOf(), algorithm), algorithm, true)
            } else {
                SlotDetailItem(listOf(), algorithm, false)
            }
        }
        device?.registerDeviceListener(deviceListener)
    }

    private fun genUiSlotValueList(allSlotValues: List<SlotValue>, algorithm: Algorithm): List<UiSlotValue> {
        return allSlotValues.map { eachSlotValue ->
            UiSlotValue(
                eachSlotValue,
                if (algorithm != savedSlotItem.algorithm) false
                else {
                    savedSlotItem.valueList?.find { v -> v.type == eachSlotValue.type }?.value?.let {
                        it != eachSlotValue.value
                    } ?: false
                }
            )
        }
    }


    override fun onCleared() {
        super.onCleared()
        device?.unregisterDeviceListener(deviceListener)
    }

    fun uiType() = slotItem.algorithm?.uiType() ?: UiAlgorithmType.Classic

    private fun handleGuitarPresetSlotInfo(slotInfo: V5GuitarPresetSlotInfo) {
        if (slotInfo.presetId != presetId || slotInfo.slotItem.slotId != slotItem.slotId) {
            return
        }
        slotItem = slotItem.copyWith(slotInfo.slotItem)
        runCatching {
            when (slotInfo.change) {
                SlotChangeEnum.AllInfo -> {
                    slotDetailItems.value!!.forEach { slotDetailItem ->
                        slotDetailItem.isSelect = slotInfo.slotItem.algorithm == slotDetailItem.id
                        slotDetailItem.uiValues =
                            if (slotDetailItem.isSelect) genUiSlotValueList(
                                slotInfo.slotItem.valueList ?: listOf(),
                                slotInfo.slotItem.algorithm!!
                            ) else listOf()
                    }
                    slotDetailItems.value = slotDetailItems.value!!.toList()
                }

                SlotChangeEnum.UserControlInfo -> {
                    slotDetailItems.value!!.forEach { slotDetailItem ->
                        slotDetailItem.isSelect = slotInfo.slotItem.algorithm == slotDetailItem.id
                        if (slotDetailItem.isSelect) {
                            //设备过来的slot info是当前选中的
                            val newValueList = slotDetailItem.uiValues.map { it.value }.toMutableList().apply {
                                replaceAll { existsValue ->
                                    slotInfo.slotItem.valueList?.find { it.type == existsValue.type } ?: existsValue
                                }
                            }
                            slotDetailItem.uiValues = genUiSlotValueList(newValueList, slotInfo.slotItem.algorithm!!)
                        }
                    }
                    slotDetailItems.value = slotDetailItems.value!!.toList()
                }

                else -> Unit
            }
        }.onFailure {
            showDebugToast("handle guitar preset slot info error $slotInfo; $it")
        }
    }

    private fun selectedItem() = slotDetailItems.value?.find { it.isSelect }

    fun switch(targetItem: SlotDetailItem) {
        val selected = selectedItem()
        if (targetItem == selected || null == selected) {
            showDebugToast("there is no selected algorithm $targetItem")
            return
        }
        selected.isSelect = false
        targetItem.isSelect = true
        slotDetailItems.value = slotDetailItems.value!!.toList()
        device?.asyncSetDevInfoFeat(
            V5GuitarPresetSlotInfo(
                chNum,
                presetId,
                when (slotItem.algorithm?.uiType()) {
                    UiAlgorithmType.Amp,
                    UiAlgorithmType.Cab -> SlotChangeEnum.ConfigId

                    else -> SlotChangeEnum.AlgorithmId
                },
                V5SlotItem(slotItem.slotId, algorithm = targetItem.id)
            )
        )
    }

    fun onSlotValueAdjust(value: SlotValue) {
        if (slotItem.valueList?.find { it.type == value.type }?.value == value.value) {
            return
        }
        device?.asyncSetDevInfoFeat(
            V5GuitarPresetSlotInfo(chNum, presetId, SlotChangeEnum.UserControlInfo, slotItem.copy(valueList = listOf(value)))
        )
    }
}

data class SlotDetailItem(var uiValues: List<UiSlotValue>, val id: Algorithm, var isSelect: Boolean)