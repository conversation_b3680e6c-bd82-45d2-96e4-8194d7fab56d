package com.harman.partyband.guitarpreset

import android.os.Bundle
import com.harman.bar.app.databinding.ActivityAdjustSlotValueBinding
import com.harman.partylight.util.popResult
import com.harman.v5protocol.bean.devinfofeat.SlotValue
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.ratio0To100ValueToFloat
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.ratioTo0100Value
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.timeTo0100Value
import com.harman.v5protocol.bean.devinfofeat.SlotValueTypeEnum
import com.harman.widget.AppCompatBaseActivity

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/4/2
 */
class AdjustSlotValueActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityAdjustSlotValueBinding.inflate(layoutInflater) }
    private val slotValue by lazy { intent.extras?.get("slotValue") as SlotValue }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.tvValueName.text = slotValue.type.name
        binding.tvValue.text = slotValue.valueString()

        binding.vsv.apply {
            maxValue = when (slotValue.type) {
                SlotValueTypeEnum.Time -> 98
                SlotValueTypeEnum.Shape -> 4
                SlotValueTypeEnum.Ratio -> 100
                else -> 100
            }
            setValue(
                when (slotValue.type) {
                    SlotValueTypeEnum.Time -> slotValue.value.timeTo0100Value()
                    SlotValueTypeEnum.Shape -> slotValue.value.toInt()
                    SlotValueTypeEnum.Ratio -> slotValue.value.ratioTo0100Value()
                    else -> slotValue.value.toInt()
                },
            )
            onValueChange = { value, max, isActive, isMoving ->
                val convertValue = when (slotValue.type) {
                    SlotValueTypeEnum.Time -> (value + 2) * 10
                    SlotValueTypeEnum.Shape -> value
                    SlotValueTypeEnum.Ratio -> value.ratio0To100ValueToFloat()
                    else -> value
                }
                slotValue.value = convertValue.toFloat()
                binding.tvValue.text = slotValue.valueString()
                if (isActive && !isMoving) {
                    finish()
                }
            }
        }
    }

    override fun finish() {
        super.finish()
        popResult(slotValue)
    }
}