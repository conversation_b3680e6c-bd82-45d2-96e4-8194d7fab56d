package com.harman.partyband.guitarpreset

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivitySlotDetailBinding
import com.harman.bar.app.databinding.ItemSlotDetailBinding
import com.harman.dp
import com.harman.partyband.micsensitivity.SpaceItemDecoration
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.invisible
import com.harman.partylight.util.push
import com.harman.partylight.util.syncPush
import com.harman.partylight.util.visible
import com.harman.v5protocol.bean.devinfofeat.SlotValue
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.v5protocol.bean.devinfofeat.V5SlotItem
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/4/1
 */
class SlotDetailActivity : AppCompatBaseActivity() {
    val vm by viewModels<SlotDetailViewModel>()
    val binding by lazy { ActivitySlotDetailBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.appbar.ivLeading.setOnClickListener { finish() }
        buildRv()
        buildLine()
        observeModel()
    }

    private fun buildLine() {
        binding.vLine.apply {
            when (vm.uiType()) {
                UiAlgorithmType.Classic,
                UiAlgorithmType.Cab -> visible()

                UiAlgorithmType.Amp -> invisible()
            }
        }
    }

    private fun buildRv() {
        binding.rvSlotList.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        binding.rvSlotList.adapter = SlotDetailAdapter(this)
        binding.rvSlotList.itemAnimator = null
        binding.rvSlotList.addItemDecoration(SpaceItemDecoration(12.dp()))
    }


    private fun observeModel() {
        vm.slotDetailItems.observe(this) { list ->
            list.find { it.isSelect }?.also {
                val uiAlgorithmType = it.id.uiType()
                binding.gpsvClassic.apply {
                    if (uiAlgorithmType == UiAlgorithmType.Classic) {
                        visible()
                        buildGpsvClassic(it)
                    } else invisible()
                }
                binding.gpsvAmp.apply {
                    if (uiAlgorithmType == UiAlgorithmType.Amp) {
                        visible()
                        buildGpsvAmp(it)
                    } else invisible()
                }
                binding.gpsvCab.apply {
                    if (uiAlgorithmType == UiAlgorithmType.Cab) {
                        visible()
                        buildGpsvCab(it)
                    } else invisible()
                }
                binding.appbar.tvTitle.text = getString(it.id.resIds().name)
            }
        }
    }

    private fun buildGpsvClassic(item: SlotDetailItem) {
        binding.gpsvClassic.apply {
            setAlgorithm(item.id)
            setValues(item.uiValues)
            onRouteToSetValue = { value ->
                lifecycleScope.launch {
                    syncPush<AdjustSlotValueActivity, SlotValue>(bundleOf("slotValue" to value.value))?.also {
                        vm.onSlotValueAdjust(it)
                    }
                }
            }
        }
    }

    private fun buildGpsvAmp(item: SlotDetailItem) {
        binding.gpsvAmp.apply {
            setAlgorithm(item.id)
            setValues(item.uiValues)
            onRouteToSetValue = {
                lifecycleScope.launch {
                    syncPush<AdjustSlotValueActivity, SlotValue>(bundleOf("slotValue" to it.value))?.also {
                        vm.onSlotValueAdjust(it)
                    }
                }
            }
        }
    }

    private fun buildGpsvCab(item: SlotDetailItem) {
        binding.gpsvCab.apply {
            setAlgorithm(item.id)
        }
    }

    companion object {
        fun launch(context: Context, uuid: String, chNum: V5ChannelNumber, presetId: Int, slotItem: V5SlotItem?, savedSlotItem: V5SlotItem?) {
            if (null == slotItem?.algorithm || null == savedSlotItem?.algorithm) {
                return
            }
            context.push<SlotDetailActivity>(
                bundleOf(
                    "uuid" to uuid,
                    "chNum" to chNum,
                    "presetId" to presetId,
                    "slotItem" to slotItem,
                    "savedSlotItem" to savedSlotItem,
                )
            )
        }
    }
}

private class SlotDetailAdapter(private val activity: SlotDetailActivity) :
    RecyclerView.Adapter<SlotDetailAdapter.VH>() {
    private val differ = generateSimpleDiffer<SlotDetailItem>()
    private var needJumpToSelectedPosition = true

    init {
        activity.vm.slotDetailItems.observe(activity) { list ->
            differ.submitList(list)
            handleAutoJump(list)
        }
    }

    private fun handleAutoJump(list: List<SlotDetailItem>) {
        if (needJumpToSelectedPosition) {
            needJumpToSelectedPosition = false
            val selectedIndex = list.indexOfFirst { it.isSelect }
            if (selectedIndex < 0) return
            activity.binding.rvSlotList.also {
                it.post {
                    it.smoothScrollToPosition(selectedIndex)
                }
            }
        }
    }

    class VH(val binding: ItemSlotDetailBinding) : ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return LayoutInflater.from(parent.context).inflate(R.layout.item_slot_detail, parent, false).let {
            it.updateLayoutParams<RecyclerView.LayoutParams> {
                width = when (activity.vm.uiType()) {
                    UiAlgorithmType.Classic -> {
                        83.dp()
                    }

                    UiAlgorithmType.Amp -> {
                        139.dp()
                    }

                    UiAlgorithmType.Cab -> {
                        83.dp()
                    }
                }
            }
            VH(ItemSlotDetailBinding.bind(it))
        }
    }

    override fun getItemCount() = differ.currentList.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        val item = differ.currentList[position]
        val resIds = item.id.resIds()
        holder.binding.ivSlot.apply {
            setImageResource(resIds.imgWithKnob)
        }
        holder.binding.tvAlgorithmName.apply {
            text = activity.getString(resIds.name)
            setTextColor(activity.getColor(if (item.isSelect) R.color.fg_inverse else R.color.fg_primary))
        }
        holder.binding.root.apply {
            background = if (item.isSelect) activity.getDrawable(R.drawable.radius_8dp_fg_primary) else null
            setOnClickListener {
                activity.vm.switch(item)
            }
        }
    }
}