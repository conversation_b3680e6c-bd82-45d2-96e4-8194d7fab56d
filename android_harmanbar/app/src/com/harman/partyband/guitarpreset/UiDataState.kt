package com.harman.partyband.guitarpreset

import com.blankj.utilcode.util.ColorUtils
import com.harman.bar.app.R
import com.harman.v5protocol.bean.devinfofeat.Algorithm
import com.harman.v5protocol.bean.devinfofeat.SlotId
import com.harman.v5protocol.bean.devinfofeat.V5MusicGenreEnum
import com.harman.v5protocol.bean.devinfofeat.V5SlotItem

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/25
 */
data class UiMiniSlotItem(
    val source: V5SlotItem,
    val isBypass: Boolean,
    val isEdited: Boolean,
    val resIds: UiAlgorithmResIds? = null,
)

data class UiAlgorithmResIds(
    val img: Int,
    val imgWithKnob: Int,
    val name: Int,
)

enum class UiAlgorithmType {
    Classic,
    Amp,
    Cab
}

fun Algorithm.resIds() = when (this) {
    Algorithm.FxNoiseGate -> UiAlgorithmResIds(R.drawable.slot_noise_gate, R.drawable.slot_noise_gate_knob, R.string.noise_gate)
    Algorithm.FxExpander -> UiAlgorithmResIds(R.drawable.slot_noise_gate2, R.drawable.slot_noise_gate2_knob, R.string.expander_gate)
    Algorithm.FxCompressor -> UiAlgorithmResIds(R.drawable.slot_compressor, R.drawable.slot_compressor_knob, R.string.compressor)
    Algorithm.FxWah -> UiAlgorithmResIds(R.drawable.slot_wah2, R.drawable.slot_wah2_knob, R.string.wah)
    Algorithm.FxAutoWah -> UiAlgorithmResIds(R.drawable.slot_wah1, R.drawable.slot_wah1_knob, R.string.auto_wah)
    Algorithm.AmpBlackface -> UiAlgorithmResIds(R.drawable.slot_amp1, R.drawable.slot_amp1_knob, R.string.blackface)
    Algorithm.AmpJazzSound -> UiAlgorithmResIds(R.drawable.slot_amp2, R.drawable.slot_amp2_knob, R.string.jazz_sound)
    Algorithm.AmpYellowTweed -> UiAlgorithmResIds(R.drawable.slot_amp3, R.drawable.slot_amp3_knob, R.string.yellow_tweed)
    Algorithm.AmpUKCrunch -> UiAlgorithmResIds(R.drawable.slot_amp4, R.drawable.slot_amp4_knob, R.string.uk_crunch)
    Algorithm.AmpBritishLead -> UiAlgorithmResIds(R.drawable.slot_amp5, R.drawable.slot_amp5_knob, R.string.british_lead)
    Algorithm.AmpDriveJM45 -> UiAlgorithmResIds(R.drawable.slot_amp6, R.drawable.slot_amp6_knob, R.string.drive_jm45)
    Algorithm.AmpJump6507 -> UiAlgorithmResIds(R.drawable.slot_amp7, R.drawable.slot_amp7_knob, R.string.jump_6507)


    Algorithm.AmpUSMetal -> UiAlgorithmResIds(R.drawable.slot_amp8, R.drawable.slot_amp8_knob, R.string.us_metal)
    Algorithm.CabIrChampion1x8 -> UiAlgorithmResIds(R.drawable.slot_cab1, R.drawable.slot_cab1, R.string.IR_Champion_1x8)
    Algorithm.CabIrVoks2x12 -> UiAlgorithmResIds(R.drawable.slot_cab2, R.drawable.slot_cab2, R.string.IR_Voks_2x12)
    Algorithm.CabIrTR652x12 -> UiAlgorithmResIds(R.drawable.slot_cab3, R.drawable.slot_cab3, R.string.IR_TR65_2x12)
    Algorithm.CabIrUK25502x12 -> UiAlgorithmResIds(R.drawable.slot_cab4, R.drawable.slot_cab4, R.string.IR_UK_2550_2x12)
    Algorithm.CabIrBM594x10 -> UiAlgorithmResIds(R.drawable.slot_cab5, R.drawable.slot_cab5, R.string.IR_BM59_4x10)
    Algorithm.CabIrTanger4x12 -> UiAlgorithmResIds(R.drawable.slot_cab6, R.drawable.slot_cab6, R.string.IR_Tanger_4x12)
    Algorithm.CabIrUK19604x12 -> UiAlgorithmResIds(R.drawable.slot_cab7, R.drawable.slot_cab7, R.string.IR_UK_1960_4x12)
    Algorithm.CabIr65054x12 -> UiAlgorithmResIds(R.drawable.slot_cab8, R.drawable.slot_cab8, R.string.IR_6505_4x12)
    Algorithm.CabIrMebo4x12 -> UiAlgorithmResIds(R.drawable.slot_cab9, R.drawable.slot_cab9, R.string.IR_Mebo_4x12)
    Algorithm.CabIrMBass2x10 -> UiAlgorithmResIds(R.drawable.slot_cab10, R.drawable.slot_cab10, R.string.IR_M_Bass_2x10)
    Algorithm.CabIrABass8x10 -> UiAlgorithmResIds(R.drawable.slot_cab11, R.drawable.slot_cab11, R.string.IR_A_Bass_8x10)

    Algorithm.ModMxrPhase90 -> UiAlgorithmResIds(R.drawable.slot_mod1, R.drawable.slot_mod1_knob, R.string.MXR_Phase_90)
    Algorithm.ModGreenPhaser -> UiAlgorithmResIds(R.drawable.slot_mod2, R.drawable.slot_mod2_knob, R.string.Green_Phaser)
    Algorithm.ModNotchPhaser -> UiAlgorithmResIds(R.drawable.slot_mod3, R.drawable.slot_mod3_knob, R.string.Notch_Phaser)
    Algorithm.ModeMinivibe -> UiAlgorithmResIds(R.drawable.slot_mod4, R.drawable.slot_mod4_knob, R.string.Minivibe)
    Algorithm.ModTremolo -> UiAlgorithmResIds(R.drawable.slot_mod5, R.drawable.slot_mod5_knob, R.string.Tremolo)
    Algorithm.ModSimpleChorus -> UiAlgorithmResIds(R.drawable.slot_mod6, R.drawable.slot_mod6_knob, R.string.Chorus)
    Algorithm.ModShiverVibrato -> UiAlgorithmResIds(R.drawable.slot_mod7, R.drawable.slot_mod7_knob, R.string.Shiver_Vibrato)
    Algorithm.ModJetterFlanger -> UiAlgorithmResIds(R.drawable.slot_mod8, R.drawable.slot_mod8_knob, R.string.Jetter_Flanger)
    Algorithm.ModGrandVibrato -> UiAlgorithmResIds(R.drawable.slot_mod9, R.drawable.slot_mod9_knob, R.string.Grand_Vibrato)
    Algorithm.DelayPureEko -> UiAlgorithmResIds(R.drawable.slot_delay1, R.drawable.slot_delay1_knob, R.string.Pure_Echo)
    Algorithm.DelayAnalogEko -> UiAlgorithmResIds(R.drawable.slot_delay2, R.drawable.slot_delay2_knob, R.string.Analog_Echo)
    Algorithm.DelayEkopress900 -> UiAlgorithmResIds(R.drawable.slot_delay5, R.drawable.slot_delay5_knob, R.string.Echopress_900)
    Algorithm.DelaySweepEko -> UiAlgorithmResIds(R.drawable.slot_delay3, R.drawable.slot_delay3_knob, R.string.Sweep_Echo)
    Algorithm.DelaySweetie -> UiAlgorithmResIds(R.drawable.slot_delay4, R.drawable.slot_delay4_knob, R.string.Sweet_Echo)
    Algorithm.ReverbDattorro -> UiAlgorithmResIds(R.drawable.slot_reverb, R.drawable.slot_reverb_knob, R.string.Reverb_Dattorro)
}

fun Algorithm.controlNumber() = when (this) {
    Algorithm.FxNoiseGate,
    Algorithm.FxExpander -> 2

    Algorithm.FxCompressor -> 5
    Algorithm.FxWah -> 5
    Algorithm.FxAutoWah -> 4
    Algorithm.AmpBlackface,
    Algorithm.AmpJazzSound,
    Algorithm.AmpYellowTweed,
    Algorithm.AmpUKCrunch,
    Algorithm.AmpDriveJM45,
    Algorithm.AmpJump6507,
    Algorithm.AmpUSMetal -> 5

    Algorithm.AmpBritishLead -> 4
    Algorithm.ModMxrPhase90 -> 1
    Algorithm.ModGreenPhaser -> 3
    Algorithm.ModNotchPhaser -> 4
    Algorithm.ModeMinivibe -> 3
    Algorithm.ModTremolo -> 5
    Algorithm.ModSimpleChorus -> 5
    Algorithm.ModShiverVibrato -> 2
    Algorithm.ModJetterFlanger -> 4
    Algorithm.ModGrandVibrato -> 3
    Algorithm.DelayPureEko -> 3
    Algorithm.DelayAnalogEko -> 3
    Algorithm.DelayEkopress900 -> 3
    Algorithm.DelaySweetie -> 3
    Algorithm.DelaySweepEko -> 5
    Algorithm.ReverbDattorro -> 5
    else -> 0
}

fun V5SlotItem.toUiMiniSlotItem(isEdited: Boolean = false) = UiMiniSlotItem(
    this,
    isBypass ?: false,
    isEdited,
    algorithm?.resIds(),
)

fun V5MusicGenreEnum.nameResId() = when (this) {
    V5MusicGenreEnum.General -> R.string.General
    V5MusicGenreEnum.Pop -> R.string.Pop
    V5MusicGenreEnum.Blues -> R.string.Blues
    V5MusicGenreEnum.Rock -> R.string.Rock
    V5MusicGenreEnum.Metal -> R.string.Metal
    V5MusicGenreEnum.Alternative -> R.string.Alternative
}

fun SlotId.itsAlgorithms() = run {
    when (this) {
        SlotId.Fx1 -> listOf(Algorithm.FxNoiseGate, Algorithm.FxExpander)
        SlotId.Fx2 -> listOf(Algorithm.FxCompressor)
        SlotId.Fx3 -> listOf(Algorithm.FxWah, Algorithm.FxAutoWah)
        SlotId.Amp -> listOf(
            Algorithm.AmpBlackface,
            Algorithm.AmpJazzSound,
            Algorithm.AmpYellowTweed,
            Algorithm.AmpUKCrunch,
            Algorithm.AmpBritishLead,
            Algorithm.AmpDriveJM45,
            Algorithm.AmpJump6507,
            Algorithm.AmpUSMetal,
        )

        SlotId.Cab -> listOf(
            Algorithm.CabIrChampion1x8,
            Algorithm.CabIrVoks2x12,
            Algorithm.CabIrTR652x12,
            Algorithm.CabIrUK25502x12,
            Algorithm.CabIrBM594x10,
            Algorithm.CabIrTanger4x12,
            Algorithm.CabIrUK19604x12,
            Algorithm.CabIr65054x12,
            Algorithm.CabIrMebo4x12,
            Algorithm.CabIrMBass2x10,
            Algorithm.CabIrABass8x10,
        )

        SlotId.Mod -> listOf(
            Algorithm.ModMxrPhase90,
            Algorithm.ModGreenPhaser,
            Algorithm.ModNotchPhaser,
            Algorithm.ModeMinivibe,
            Algorithm.ModTremolo,
            Algorithm.ModSimpleChorus,
            Algorithm.ModShiverVibrato,
            Algorithm.ModJetterFlanger,
            Algorithm.ModGrandVibrato,
        )

        SlotId.Delay -> listOf(
            Algorithm.DelayPureEko,
            Algorithm.DelayAnalogEko,
            Algorithm.DelayEkopress900,
            Algorithm.DelaySweepEko,
            Algorithm.DelaySweetie,
        )

        SlotId.Reverb -> listOf(Algorithm.ReverbDattorro)
    }
}

fun Algorithm.uiType() = run {
    when (this) {
        Algorithm.AmpBlackface,
        Algorithm.AmpJazzSound,
        Algorithm.AmpYellowTweed,
        Algorithm.AmpUKCrunch,
        Algorithm.AmpBritishLead,
        Algorithm.AmpDriveJM45,
        Algorithm.AmpJump6507,
        Algorithm.AmpUSMetal -> UiAlgorithmType.Amp

        Algorithm.CabIrChampion1x8,
        Algorithm.CabIrVoks2x12,
        Algorithm.CabIrTR652x12,
        Algorithm.CabIrUK25502x12,
        Algorithm.CabIrBM594x10,
        Algorithm.CabIrTanger4x12,
        Algorithm.CabIrUK19604x12,
        Algorithm.CabIr65054x12,
        Algorithm.CabIrMebo4x12,
        Algorithm.CabIrMBass2x10,
        Algorithm.CabIrABass8x10 -> UiAlgorithmType.Cab

        else -> UiAlgorithmType.Classic
    }
}

fun Algorithm.specialGradientColors() = when (this) {
    Algorithm.AmpJazzSound -> {
        listOf(0xFFE2E2E2.toInt(), 0xFF6E6E6E.toInt())
    }

    Algorithm.AmpUKCrunch -> {
        listOf(0xFFDBCD87.toInt(), 0xFFB27D00.toInt())
    }

    Algorithm.AmpBritishLead -> {
        listOf(0xFFE2E2E2.toInt(), 0xFF6E6E6E.toInt())
    }

    Algorithm.AmpDriveJM45 -> {
        listOf(0xFFDBCD87.toInt(), 0xFFB27D00.toInt())
    }

    else -> null
}