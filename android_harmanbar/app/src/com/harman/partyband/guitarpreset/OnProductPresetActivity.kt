package com.harman.partyband.guitarpreset

import android.os.Bundle
import androidx.activity.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemDragListener
import com.chad.library.adapter.base.module.DraggableModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityOnProductPresetBinding
import com.harman.bar.app.databinding.ViewItemOnProductPresetBinding
import com.harman.deviceImgForce
import com.harman.partylight.util.invisible
import com.harman.partylight.util.visible
import com.harman.widget.AppCompatBaseActivity
import com.harman.partyband.guitarpreset.UiOnProductPresetItem.Type as Type

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/4/8
 */
class OnProductPresetActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityOnProductPresetBinding.inflate(layoutInflater) }
    val vm by viewModels<OnProductPresetViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.ivLeading.setOnClickListener { finish() }
        binding.tvTitle.text = getString(R.string.on_product_preset)
        deviceImgForce(binding.ivDevImg, vm.device)
        binding.rv.adapter = OnProductPresetAdapter(this)
        observeModel()
    }

    private fun observeModel() {
        vm.isEditing.observe(this) {
            buildAction(it)
        }
    }

    private fun buildAction(isEditing: Boolean) {
        binding.csbAction.apply {
            text = getString(if (isEditing) R.string.jbl_SAVE else R.string.jbl_edit)
            setOnClickListener { vm.toggleEditSave() }
        }
    }
}

private class OnProductPresetAdapter(val act: OnProductPresetActivity) :
    BaseQuickAdapter<UiOnProductPresetItem, BaseViewHolder>(R.layout.view_item_on_product_preset, mutableListOf()),
    DraggableModule {

    init {
        act.vm.items.observe(act) {
            setList(it)
        }
        act.vm.isEditing.observe(act) {
            draggableModule.isDragEnabled = !it
            draggableModule.setOnItemDragListener(object : OnItemDragListener {
                override fun onItemDragStart(viewHolder: RecyclerView.ViewHolder?, pos: Int) {}

                override fun onItemDragMoving(source: RecyclerView.ViewHolder?, from: Int, target: RecyclerView.ViewHolder?, to: Int) {
                    act.vm.onReSorting(from, to)
                }

                override fun onItemDragEnd(viewHolder: RecyclerView.ViewHolder?, pos: Int) {
                    act.vm.onSortDone()
                }
            })
        }
    }


    override fun convert(holder: BaseViewHolder, item: UiOnProductPresetItem) {
        val binding = ViewItemOnProductPresetBinding.bind(holder.itemView)
        binding.tv.apply {
            text = when (item.type) {
                Type.Empty -> act.getString(R.string.harmanbar_NewPreset_Empty)
                else -> item.name ?: ""
            }
            setCompoundDrawablesRelativeWithIntrinsicBounds(
                act.getDrawable(if (item.type == Type.Empty) R.drawable.ic_round_empty else R.drawable.ic_speaker),
                null,
                null,
                null
            )
            setTextColor(act.getColor(if (item.type == Type.Empty) R.color.fg_disabled else R.color.fg_primary))
        }
        binding.ivAction.apply {
            if (item.type == Type.Empty) invisible() else visible()
            setImageResource(
                when (item.type) {
                    Type.Normal -> R.drawable.ic_sort_bar
                    Type.Editing -> R.drawable.ic_round_remove_red_1
                    Type.Empty -> 0
                }
            )
            setOnClickListener {
                if (item.type == Type.Editing) {
                    act.vm.delete(item.presetId!!)
                }
            }
        }
    }

}

