package com.harman.partyband.guitarpreset

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.deepCopy
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.Algorithm
import com.harman.v5protocol.bean.devinfofeat.GuitarPresetSimpleInfo
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.SlotChangeEnum
import com.harman.v5protocol.bean.devinfofeat.SlotId
import com.harman.v5protocol.bean.devinfofeat.SlotValue
import com.harman.v5protocol.bean.devinfofeat.SlotValueTypeEnum
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetAllSimpleInfoList
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetInfo
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetInfoQuery
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetOnProductList
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetOperation
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetOperationEnum
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSelect
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSelectQuery
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSlotInfo
import com.harman.v5protocol.bean.devinfofeat.V5MusicGenreEnum
import com.harman.v5protocol.bean.devinfofeat.V5SlotItem
import com.wifiaudio.view.pagesmsccontent.showDebugToast
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import java.io.Serializable

/**
 * @Description viewmodel for [GuitarPresetActivity]
 * <AUTHOR>
 * @Time 2025/3/25
 */
class GuitarPresetViewModel(savedState: SavedStateHandle) : ViewModel() {
    val device = DeviceStore.find(savedState.get<String>("uuid")!!) as? PartyBandDevice
    val chNum = savedState.get<V5ChannelNumber>("chNum")!!
    val uiPresetSimpleList = MutableLiveData<List<UiPresetSimpleItem>>()
    val currentUiPresetIndex = MutableLiveData<Int>()
    val currentUiPreset = MutableLiveData<UiPresetSimpleItem>()

    private val fetchInitPresetInfoCompleters = mutableMapOf<Int, CompletableDeferred<V5GuitarPresetInfo>>()
    private var onProductPresetIds = mutableListOf<Int>()

    /** 和[uiPresetSimpleList]同步 */
    private var presetSimpleList = mutableListOf<GuitarPresetSimpleInfo>()
    val savedPresetInfos = mutableMapOf<Int, V5GuitarPresetInfo>()
    val previewPresetInfos = mutableMapOf<Int, V5GuitarPresetInfo>()
    val eventStream = MutableSharedFlow<GPVMEvent>(extraBufferCapacity = 1)
    private val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            /**The code here must be executed after, [presetSimpleList], and [onProductPresetIds] all have data*/
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.GuitarPresetAllSimpleInfo] as? V5GuitarPresetAllSimpleInfoList)?.also {
                    handleGuitarPresetAllSimpleInfo(it)
                }
                (devInfoMap[V5DevInfoFeatID.GuitarPresetInfo] as? V5GuitarPresetInfo)?.also {
                    handleGuitarPresetInfo(it)
                }
                (devInfoMap[V5DevInfoFeatID.GuitarPresetOperation] as? V5GuitarPresetOperation)?.also {
                    handleGuitarPresetOperation(it)
                }
                (devInfoMap[V5DevInfoFeatID.GuitarPresetOnProductList] as? V5GuitarPresetOnProductList)?.also {
                    handleGuitarPresetOnProductList(it)
                }
                (devInfoMap[V5DevInfoFeatID.GuitarPresetSlotInfo] as? V5GuitarPresetSlotInfo)?.also {
                    handleGuitarPresetSlotInfo(it)
                }
                (devInfoMap[V5DevInfoFeatID.GuitarPresetSelect] as? V5GuitarPresetSelect)?.also {
                    onSelectedPresetChanged(it.presetId)
                }
            }
        }
    }

    private fun handleGuitarPresetOperation(data: V5GuitarPresetOperation) {
        try {
            when (data.operation) {
                V5GuitarPresetOperationEnum.Duplicate,
                V5GuitarPresetOperationEnum.SaveAs -> {
                    presetSimpleList.add(GuitarPresetSimpleInfo(data.newPresetId!!, data.genre!!, data.name!!))
                    updateUiPresetSimpleList()
                    switchPreset(data.newPresetId!!)
                }

                V5GuitarPresetOperationEnum.ResetToDefault -> {
                    viewModelScope.launch {
                        device?.setDevInfoFeat(V5GuitarPresetInfoQuery(data.sourcePresetId!!))?.also {
                            device.awaitDevInfo<V5GuitarPresetInfo>()?.also { newPreset ->
                                handleGuitarPresetInfo(newPreset, true)
                                presetSimpleList.find { it.presetId == newPreset.presetId }?.also { oldPresetSimple ->
                                    oldPresetSimple.apply {
                                        genre = newPreset.genre
                                        name = newPreset.name
                                    }
                                }
                                updateUiPresetSimpleList()
                            }
                        }
                    }
                }

                V5GuitarPresetOperationEnum.Rename -> {
                    presetSimpleList.find { it.presetId == data.sourcePresetId }?.also {
                        it.name = data.name!!
                    }
                    updateUiPresetSimpleList()
                    savedPresetInfos[data.sourcePresetId]?.apply {
                        name = data.name!!
                        eventStream.tryEmit(GPVMEvent.InitPresetUpdate(data.sourcePresetId!!))
                    }
                    previewPresetInfos[data.sourcePresetId]?.apply {
                        name = data.name!!
                        eventStream.tryEmit(GPVMEvent.PreviewPresetUpdate(data.sourcePresetId!!))
                    }
                }

                V5GuitarPresetOperationEnum.SavePreset -> {
                    savedPresetInfos[data.sourcePresetId!!] = previewPresetInfos[data.sourcePresetId!!]!!.deepCopy()
                    eventStream.tryEmit(GPVMEvent.InitPresetUpdate(data.sourcePresetId!!))
                }

                V5GuitarPresetOperationEnum.Delete -> {
                    val removeIndex = presetSimpleList.indexOfFirst { it.presetId == data.sourcePresetId }
                    val size = presetSimpleList.size
                    if (removeIndex >= 0) {
                        presetSimpleList.removeAt(removeIndex)
                        savedPresetInfos.remove(data.sourcePresetId)
                        previewPresetInfos.remove(data.sourcePresetId)
                        val newIndex = if (size <= 1) {
                            null
                        } else {
                            when (removeIndex) {
                                size - 1 -> removeIndex - 1//the last
                                0 -> 0//first
                                else -> removeIndex
                            }
                        }
                        updateUiPresetSimpleList()
                        newIndex?.also { switchPresetByIndex(it) }
                    }
                }

                V5GuitarPresetOperationEnum.ExitPreview -> {
                    previewPresetInfos[data.sourcePresetId!!] = savedPresetInfos[data.sourcePresetId!!]!!.deepCopy()
                    eventStream.tryEmit(GPVMEvent.PreviewPresetUpdate(data.sourcePresetId!!))
                }

                else -> Unit
            }
        } catch (e: Exception) {
            e.printStackTrace()
            showDebugToast("Operation notify data exception $e")
        }
    }

    private fun handleGuitarPresetSlotInfo(data: V5GuitarPresetSlotInfo) {
        if (chNum != data.chNum) {
            return
        }
        previewPresetInfos[data.presetId]?.also { preset ->
            preset.slots.replaceAll { oldSlot ->
                val newSlotItem = data.slotItem
                if (newSlotItem.slotId == oldSlot.slotId) {
                    when (data.change) {
                        SlotChangeEnum.AllInfo -> newSlotItem
                        else -> oldSlot.copyWith(newSlotItem)
                    }
                } else {
                    oldSlot
                }
            }
            eventStream.tryEmit(GPVMEvent.PreviewPresetUpdate(preset.presetId))
        }
    }

    private fun handleGuitarPresetOnProductList(data: V5GuitarPresetOnProductList) {
        onProductPresetIds = data.presetIds
        updateUiPresetSimpleList()
    }

    /** 设备只会返回已落库的preset info，不会返回没有保存的preview preset info */
    private fun handleGuitarPresetInfo(data: V5GuitarPresetInfo, isReset: Boolean = false) {
        val existInitPreset = savedPresetInfos[data.presetId]
        savedPresetInfos[data.presetId] = data
        val existPreviewPreset = previewPresetInfos[data.presetId]
        if (null == existPreviewPreset || isReset) {
            previewPresetInfos[data.presetId] = data.deepCopy()
            if (isReset) {
                eventStream.tryEmit(GPVMEvent.PreviewPresetUpdate(data.presetId))
            }
        }
        fetchInitPresetInfoCompleters.remove(data.presetId)?.complete(data)
        if (null != existInitPreset) {
            eventStream.tryEmit(GPVMEvent.InitPresetUpdate(data.presetId))
        }
    }

    private fun handleGuitarPresetAllSimpleInfo(data: V5GuitarPresetAllSimpleInfoList) {
        presetSimpleList.apply {
            clear()
            addAll(data.list)
        }
        updateUiPresetSimpleList()
    }

    private fun updateUiPresetSimpleList() = run {
        uiPresetSimpleList.value = presetSimpleList.mapIndexed { idx, item ->
            val ret = UiPresetSimpleItem(item.presetId, item.name, item.genre, onProductPresetIds.contains(item.presetId))
            if (ret.presetId == currentUiPreset.value?.presetId) {
                currentUiPreset.value = ret
            }
            ret
        }
    }


    init {
        viewModelScope.launch {
            runCatching {
                device?.getDevInfoFeat2<V5GuitarPresetAllSimpleInfoList, V5GuitarPresetOnProductList>()?.also { pair ->
                    device.asyncSetDevInfoFeat(V5GuitarPresetSelectQuery(chNum))
                    device.awaitDevInfo<V5GuitarPresetSelect>()?.presetId?.also { presetId ->
                        presetSimpleList = pair.first!!.list
                        onProductPresetIds = pair.second?.presetIds ?: mutableListOf()
                        updateUiPresetSimpleList()
                        onSelectedPresetChanged(presetId)
                        device.registerDeviceListener(deviceListener)
                    }
                }
            }.onFailure {
                showDebugToast("fetch preset data error $it")
            }
        }
    }

    /**
     * fetch preset info
     * @return first is init preset , second is preview preset
     */
    suspend fun fetchPreset(presetId: Int): Pair<V5GuitarPresetInfo, V5GuitarPresetInfo>? {
        return runCatching {
            return Pair(savedPresetInfos[presetId]!!, previewPresetInfos[presetId]!!)
        }.onFailure {
            if (null == fetchInitPresetInfoCompleters[presetId]) {
                fetchInitPresetInfoCompleters[presetId] = CompletableDeferred()
            }
            device?.asyncSetDevInfoFeat(V5GuitarPresetInfoQuery(presetId))
            return fetchInitPresetInfoCompleters[presetId]?.await()?.let {
                Pair(it, previewPresetInfos[presetId]!!)
            }
        }.getOrNull()
    }

    override fun onCleared() {
        super.onCleared()
        fetchInitPresetInfoCompleters.clear()
        device?.unregisterDeviceListener(deviceListener)
        exitCurrentPreviewIfAvailable()
    }

    fun onSelectedPresetChanged(presetId: Int) {
        runCatching {
            //改变当前使用的preset前重置当前preview preset
            currentUiPreset.value?.presetId?.also { id ->
                previewPresetInfos[id] = savedPresetInfos[id]!!.deepCopy()
                eventStream.tryEmit(GPVMEvent.PreviewPresetUpdate(id))
            }
        }
        runCatching {
            uiPresetSimpleList.value!!.also {
                val idx = it.indexOfFirst { e -> e.presetId == presetId }
                currentUiPreset.value = it[idx]
                currentUiPresetIndex.value = idx
            }
        }.onFailure {
            showDebugToast("can not find preset in preset simple list $presetId,app will select first preset")
            switchPresetByIndex(0)
        }
    }

    fun switchPreset(presetId: Int) {
        if (currentUiPreset.value?.presetId == presetId) {
            return
        }
        device?.asyncSetDevInfoFeat(V5GuitarPresetSelect(chNum, presetId))
    }

    fun switchPresetByIndex(index: Int) {
        uiPresetSimpleList.value?.getOrNull(index)?.presetId?.also {
            device?.asyncSetDevInfoFeat(V5GuitarPresetSelect(chNum, it))
        }
    }

    private fun exitCurrentPreviewIfAvailable() {
        currentUiPreset()?.presetId?.also {
            if (savedPresetInfos[it] != previewPresetInfos[it]) {
                device?.asyncSetDevInfoFeat(V5GuitarPresetOperation(chNum, V5GuitarPresetOperationEnum.ExitPreview, it))
            }
        }
    }

    fun currentUiPreset() = uiPresetSimpleList.value?.getOrNull(currentUiPresetIndex.value ?: -1)

    fun uiPresetSize() = uiPresetSimpleList.value?.size ?: 0

    fun duplicateCurrent(name: String, genre: V5MusicGenreEnum) {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(
                V5GuitarPresetOperation(
                    chNum,
                    V5GuitarPresetOperationEnum.Duplicate,
                    it,
                    name = name,
                    genre = genre
                ),
            )
        }
    }

    fun saveAsCurrent(name: String, genre: V5MusicGenreEnum) {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(
                V5GuitarPresetOperation(
                    chNum,
                    V5GuitarPresetOperationEnum.SaveAs,
                    it,
                    name = name,
                    genre = genre
                )
            )
        }
    }

    fun saveCurrent() {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(V5GuitarPresetOperation(chNum, V5GuitarPresetOperationEnum.SavePreset, it))
        }
    }

    fun resetToDefaultCurrent() {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(V5GuitarPresetOperation(chNum, V5GuitarPresetOperationEnum.ResetToDefault, it))
        }
    }

    fun removeFromOnProductCurrent() {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(V5GuitarPresetOperation(chNum, V5GuitarPresetOperationEnum.RemoveFromOnProductList, it))
        }
    }

    fun saveToOnProductCurrent(originPresetId: Int?) {
        if (null == originPresetId) {
            //add current preset to on product list
            currentUiPreset()?.presetId?.also {
                val targetIds = onProductPresetIds.toMutableList().apply { add(it) }
                device?.asyncSetDevInfoFeat(V5GuitarPresetOnProductList(targetIds))
            }
        } else {
            //replace origin preset
            currentUiPreset()?.presetId?.also {
                val targetIds = onProductPresetIds.toMutableList().apply {
                    replaceAll { e ->
                        if (e == originPresetId) it else e
                    }
                }
                device?.asyncSetDevInfoFeat(V5GuitarPresetOnProductList(targetIds))
            }
        }
    }

    fun deleteCurrent() {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(V5GuitarPresetOperation(chNum, V5GuitarPresetOperationEnum.Delete, it))
        }
    }

    fun renamePresetCurrent(newName: String) {
        currentUiPreset()?.presetId?.also {
            device?.asyncSetDevInfoFeat(V5GuitarPresetOperation(chNum, V5GuitarPresetOperationEnum.Rename, it, name = newName))
        }
    }
}

data class UiPresetSimpleItem(val presetId: Int, val presetName: String, val genre: V5MusicGenreEnum, val isOnProduct: Boolean) : Serializable

sealed class GPVMEvent {
    data class PreviewPresetUpdate(val presetId: Int) : GPVMEvent()
    data class InitPresetUpdate(val presetId: Int) : GPVMEvent()
}


private fun mockPresetInfo(presetId: Int) = run {
    V5GuitarPresetInfo(
        presetId,
        V5MusicGenreEnum.entries.random(),
        "preset$presetId",
        mutableListOf(
            V5SlotItem(
                SlotId.Fx1, Algorithm.FxNoiseGate, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.Threshold),
                    SlotValue(1, 12f, SlotValueTypeEnum.Release)
                )
            ),
            V5SlotItem(
                SlotId.Fx2, Algorithm.FxCompressor, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.Threshold),
                    SlotValue(1, 1.98f, SlotValueTypeEnum.Ratio),
                    SlotValue(2, 12f, SlotValueTypeEnum.Attack),
                    SlotValue(3, 12f, SlotValueTypeEnum.Release),
                    SlotValue(4, 12f, SlotValueTypeEnum.OutputLevel),
                )
            ),
            V5SlotItem(
                SlotId.Fx3, Algorithm.FxWah, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.Mix),
                    SlotValue(1, 12f, SlotValueTypeEnum.Rate),
                    SlotValue(2, 12f, SlotValueTypeEnum.Freq),
                    SlotValue(3, 12f, SlotValueTypeEnum.Depth),
                    SlotValue(4, 12f, SlotValueTypeEnum.Reso),
                )
            ),
            V5SlotItem(
                SlotId.Amp, Algorithm.AmpUKCrunch, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.Drive),
                    SlotValue(1, 12f, SlotValueTypeEnum.Bass),
                    SlotValue(2, 12f, SlotValueTypeEnum.Mid),
                    SlotValue(3, 12f, SlotValueTypeEnum.Treble),
                    SlotValue(4, 12f, SlotValueTypeEnum.OutVol),
                )
            ),
            V5SlotItem(SlotId.Cab, Algorithm.CabIr65054x12, false, listOf()),
            V5SlotItem(
                SlotId.Mod, Algorithm.ModGreenPhaser, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.Speed),
                    SlotValue(1, 12f, SlotValueTypeEnum.Depth),
                    SlotValue(2, 12f, SlotValueTypeEnum.Feedback),
                )
            ),
            V5SlotItem(
                SlotId.Delay, Algorithm.DelaySweetie, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.Mix),
                    SlotValue(1, 500f, SlotValueTypeEnum.Time),
                    SlotValue(2, 12f, SlotValueTypeEnum.Feedback),
                )
            ),
            V5SlotItem(
                SlotId.Reverb, Algorithm.ReverbDattorro, false, listOf(
                    SlotValue(0, 12f, SlotValueTypeEnum.PreDelay),
                    SlotValue(1, 12f, SlotValueTypeEnum.PreFilter),
                    SlotValue(2, 12f, SlotValueTypeEnum.Decay),
                    SlotValue(3, 12f, SlotValueTypeEnum.Damp),
                    SlotValue(4, 12f, SlotValueTypeEnum.Amount),
                )
            ),
        )
    )
}

private fun mockPresetSimpleList() = mutableListOf(
    GuitarPresetSimpleInfo(1, V5MusicGenreEnum.Pop, "preset1"),
    GuitarPresetSimpleInfo(2, V5MusicGenreEnum.Rock, "preset2"),
    GuitarPresetSimpleInfo(3, V5MusicGenreEnum.Alternative, "preset3"),
    GuitarPresetSimpleInfo(4, V5MusicGenreEnum.Pop, "preset4"),
    GuitarPresetSimpleInfo(5, V5MusicGenreEnum.Blues, "preset5"),
    GuitarPresetSimpleInfo(6, V5MusicGenreEnum.Metal, "preset6"),
    GuitarPresetSimpleInfo(7, V5MusicGenreEnum.General, "preset7"),
    GuitarPresetSimpleInfo(8, V5MusicGenreEnum.Blues, "preset8"),
    GuitarPresetSimpleInfo(9, V5MusicGenreEnum.Pop, "preset9"),
    GuitarPresetSimpleInfo(10, V5MusicGenreEnum.Blues, "preset10"),
)