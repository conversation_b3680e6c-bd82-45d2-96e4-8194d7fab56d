package com.harman.partyband.guitarpreset

import android.view.View
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import com.harman.BottomPopUpDialogFragment
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogOperationBinding
import com.harman.partylight.util.gone
import com.harman.partylight.util.syncPush
import com.harman.partylight.util.visible
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/26
 */
class OperationDialog(private val activity: GuitarPresetActivity) : BottomPopUpDialogFragment(activity) {
    private val binding by lazy { DialogOperationBinding.inflate(layoutInflater) }
    private val rootVm = activity.vm
    override fun getContentView() = binding.root
    override fun buildContentView(view: View) {
        binding.tvDuplicate.setOnClickListener {
            dismiss()
            activity.lifecycleScope.launch {
                rootVm.currentUiPreset()?.also {
                    DuplicateSaveActivity.launch(activity, PageType.Duplicate, it)?.also { ret ->
                        rootVm.duplicateCurrent(ret.name, ret.genre)
                    }
                }
            }
        }
        binding.tvResetToDefault.setOnClickListener {
            rootVm.resetToDefaultCurrent()
            dismiss()
        }
        binding.tvRename.setOnClickListener {
            dismiss()
            activity.lifecycleScope.launch {
                rootVm.currentUiPreset()?.also {
                    val renameRet = activity.syncPush<RenamePresetActivity, String>(bundleOf("presetName" to it.presetName))
                    if (renameRet.isNullOrBlank()) {
                        return@launch
                    }
                    rootVm.renamePresetCurrent(renameRet)
                }
            }
        }
        binding.tvRemoveFrom.setOnClickListener {
            rootVm.removeFromOnProductCurrent()
            dismiss()
        }
        binding.tvSaveTo.setOnClickListener {
            dismiss()
            SaveToOnProductDialog(activity).show(activity.supportFragmentManager, null)
        }
        binding.tvDeletePreset.setOnClickListener {
            rootVm.deleteCurrent()
            dismiss()
        }
        binding.tvDeletePreset.apply {
            if (rootVm.uiPresetSize() == 1) gone() else visible()
        }
        rootVm.currentUiPreset()?.also {
            binding.tvRemoveFrom.apply {
                if (rootVm.device?.isSolo() == true && it.isOnProduct) visible() else gone()
            }
            binding.tvSaveTo.apply {
                if (rootVm.device?.isSolo() == true && !it.isOnProduct) visible() else gone()
            }
            binding.tvPresetName.apply {
                text = it.presetName
                setCompoundDrawablesRelativeWithIntrinsicBounds(
                    if (it.isOnProduct) activity.getDrawable(R.drawable.ic_speaker) else null,
                    null,
                    null,
                    null
                )
            }
        }
    }

    override fun observeModel() {}

    override fun prepareData() = Unit
}