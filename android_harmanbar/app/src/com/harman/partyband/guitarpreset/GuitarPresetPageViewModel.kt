package com.harman.partyband.guitarpreset

import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.v5protocol.bean.devinfofeat.SlotId
import com.harman.v5protocol.bean.devinfofeat.SlotChangeEnum
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSlotInfo
import com.harman.v5protocol.bean.devinfofeat.V5SlotItem
import kotlinx.coroutines.launch

/**
 * @Description viewmodel for [GuitarPresetPageFragment]
 * <AUTHOR>
 * @Time 2025/3/25
 */
class GuitarPresetPageViewModelFactory(
    private val rootVm: GuitarPresetViewModel,
    private val presetId: Int,
) : AbstractSavedStateViewModelFactory() {
    override fun <T : ViewModel> create(key: String, modelClass: Class<T>, handle: SavedStateHandle): T {
        return GuitarPresetPageViewModel(rootVm, presetId, handle) as T
    }
}

class GuitarPresetPageViewModel(
    private val rootVm: GuitarPresetViewModel,
    private val presetId: Int,
    private val savedState: SavedStateHandle,
) :
    ViewModel() {
    private val savedPresetInfo
        get() = rootVm.savedPresetInfos[presetId]
    private val previewPresetInfo
        get() = rootVm.previewPresetInfos[presetId]
    val slotNoiseGate = MutableLiveData<UiMiniSlotItem>()
    val slotCompressor = MutableLiveData<UiMiniSlotItem>()
    val slotWah = MutableLiveData<UiMiniSlotItem>()
    val slotAmp = MutableLiveData<UiMiniSlotItem>()
    val slotCab = MutableLiveData<UiMiniSlotItem>()
    val slotMod = MutableLiveData<UiMiniSlotItem>()
    val slotDelay = MutableLiveData<UiMiniSlotItem>()
    val slotReverb = MutableLiveData<UiMiniSlotItem>()
    val anySlotChanged = MutableLiveData(false)


    init {
        viewModelScope.launch {
            rootVm.eventStream.collect {
                when (it) {
                    is GPVMEvent.InitPresetUpdate -> it.presetId
                    is GPVMEvent.PreviewPresetUpdate -> it.presetId
                    else -> null
                }?.also { updatePresetId ->
                    if (presetId == updatePresetId) {
                        updatePresetInfo()
                    }
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
    }

    fun fetchPreset() {
        viewModelScope.launch {
            rootVm.fetchPreset(presetId)?.also {
                updatePresetInfo()
            }
        }
    }

    private fun updatePresetInfo() {
        previewPresetInfo?.slots?.forEach {
            updateUiSlotItem(it)
        }
        anySlotChanged.value = previewPresetInfo != savedPresetInfo
    }

    private fun updateUiSlotItem(intactSlotItem: V5SlotItem) {
        val targetUpdateUiSlotIdLiveData = when (intactSlotItem.slotId) {
            SlotId.Fx1 -> slotNoiseGate
            SlotId.Fx2 -> slotCompressor
            SlotId.Fx3 -> slotWah
            SlotId.Amp -> slotAmp
            SlotId.Cab -> slotCab
            SlotId.Mod -> slotMod
            SlotId.Delay -> slotDelay
            SlotId.Reverb -> slotReverb
        }
        targetUpdateUiSlotIdLiveData.value =
            intactSlotItem.toUiMiniSlotItem(isEdited = deviceSlot(intactSlotItem.slotId) != intactSlotItem)
    }

    private fun deviceSlot(id: SlotId) = savedPresetInfo?.slots?.find { it.slotId == id }
    private fun previewSlot(id: SlotId) = previewPresetInfo?.slots?.find { it.slotId == id }

    fun findSavedSlotItem(slotId: SlotId) = run {
        savedPresetInfo?.slots?.find { it.slotId == slotId }
    }


    fun setSlotByPass(slotItem: V5SlotItem, isBypass: Boolean) {
        if (null == slotItem.algorithm || slotItem.isBypass == isBypass) {
            return
        }
        V5GuitarPresetSlotInfo(rootVm.chNum, presetId, SlotChangeEnum.BypassStatus, slotItem.copy(isBypass = isBypass)).also {
            rootVm.device?.asyncSetDevInfoFeat(it)
        }
    }
}