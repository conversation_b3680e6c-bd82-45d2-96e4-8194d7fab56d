package com.harman.partyband.guitarpreset

import android.content.Context
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityGuitarPresetBinding
import com.harman.discover.bean.PartyBandDevice
import com.harman.partyband.widget.AlphaPagerTransformer
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.gone
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.widget.AppCompatBaseActivity
import com.harman.widget.BtnType
import kotlinx.coroutines.launch

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/19
 */
class GuitarPresetActivity : AppCompatBaseActivity() {
    val binding by lazy { ActivityGuitarPresetBinding.inflate(layoutInflater) }
    val vm by viewModels<GuitarPresetViewModel>()
    val guideView by lazy { GuitarPresetActivityGuideView(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        buildAppbar()
        buildVp2()
        observeModel()
        guideView
    }


    private fun observeModel() {
        vm.currentUiPresetIndex.observe(this) {
            binding.vp2.post {
                binding.vp2.setCurrentItem(it, false)
            }
        }
        vm.currentUiPreset.observe(this) {
            buildCurPreset(it)
            buildIvMore(it)
        }
    }

    private fun buildIvMore(preset: UiPresetSimpleItem?) {
        binding.ivMore.apply {
            if (null == preset) {
                gone()
            } else {
                visible()
                setOnClickListener {
                    OperationDialog(this@GuitarPresetActivity).show(supportFragmentManager, null)
                }
            }
        }
    }

    private fun buildVp2() {
        binding.vp2.apply {
            if (null == adapter) {
                adapter = PageAdapter(this@GuitarPresetActivity, this)
            }
            if (null == tag) {
                registerOnPageChangeCallback(object : OnPageChangeCallback() {
                    private var isManualDragging = false
                    override fun onPageSelected(position: Int) {
                        if (isManualDragging) {
                            //忽略掉非手动操作的情况
                            vm.switchPresetByIndex(position)
                        } else if (position != vm.currentUiPresetIndex.value) {
                            binding.vp2.setCurrentItem(vm.currentUiPresetIndex.value ?: 0, false)
                        }
                    }

                    override fun onPageScrollStateChanged(state: Int) {
                        super.onPageScrollStateChanged(state)
                        when (state) {
                            ViewPager2.SCROLL_STATE_IDLE -> isManualDragging = false
                            ViewPager2.SCROLL_STATE_DRAGGING -> isManualDragging = true
                            ViewPager2.SCROLL_STATE_SETTLING -> Unit
                        }
                    }
                })
                setPageTransformer(AlphaPagerTransformer(0.3f))
                tag = true
            }
        }
    }

    private fun buildCurPreset(preset: UiPresetSimpleItem?) {
        binding.llCurPreset.apply {
            if (null == preset) {
                gone()
                return
            } else {
                visible()
                setOnClickListener {
                    lifecycleScope.launch {
                        PresetSimpleListDialog(this@GuitarPresetActivity).syncShow(supportFragmentManager)?.also {
                            vm.switchPreset(it)
                        }
                        binding.ivAllPreset.rotation = 0f
                    }
                    binding.ivAllPreset.rotation = 180f
                }
            }
        }
        binding.tvCurPreset.apply {
            text = preset!!.presetName
        }
        binding.ivPresetOnProduct.apply {
            if (preset!!.isOnProduct && true == vm.device?.isSolo()) visible() else gone()
        }
    }

    private fun buildAppbar() {
        binding.appbar.tvTitle.text = getString(R.string.harmanbar_content_Preset)
        binding.appbar.ivLeading.setOnClickListener {
//            push<OnProductPresetActivity>(bundleOf("uuid" to vm.device!!.UUID))
            finish()
        }
        binding.appbar.csbAction.apply {
            text = getString(R.string.guide)
            btnType(BtnType.Flat)
            setOnClickListener {
                guideView.show()
            }
        }
    }

    companion object {
        fun launch(context: Context, device: PartyBandDevice, chNum: V5ChannelNumber) {
            context.push<GuitarPresetActivity>(
                bundleOf(
                    "uuid" to device.UUID,
                    "chNum" to chNum,
                )
            )
        }
    }
}

private class PageAdapter(val activity: GuitarPresetActivity, val vp: ViewPager2) : FragmentStateAdapter(activity) {
    private val differ = generateSimpleDiffer<UiPresetSimpleItem>()

    init {
        activity.vm.uiPresetSimpleList.observe(activity) {
            differ.submitList(it)
        }
    }

    override fun getItemCount() = differ.currentList.size
    override fun createFragment(position: Int) =
        GuitarPresetPageFragment.newInstance(differ.currentList[position].presetId)

    override fun getItemId(position: Int): Long {
        return differ.currentList[position].presetId.toLong()
    }

    override fun containsItem(itemId: Long): Boolean {
        return null != differ.currentList.find { it.presetId.toLong() == itemId }
    }
}
