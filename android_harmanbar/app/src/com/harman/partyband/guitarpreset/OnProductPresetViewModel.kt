package com.harman.partyband.guitarpreset

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.harman.bar.app.R
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.GuitarPresetSimpleInfo
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetAllSimpleInfoList
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetOnProductList
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSelect
import com.harman.v5protocol.bean.devinfofeat.V5GuitarPresetSelectQuery
import com.wifiaudio.view.pagesmsccontent.showToast
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import com.harman.partyband.guitarpreset.UiOnProductPresetItem.Type as Type

/**
 * @Description viewmodel for [OnProductPresetActivity]
 * <AUTHOR>
 * @Time 2025/4/8
 */
class OnProductPresetViewModel(savedState: SavedStateHandle) : ViewModel() {
    val device = DeviceStore.find(savedState.get<String>("uuid")!!) as? PartyBandDevice
    val isEditing = MutableLiveData(false)
    val items = MutableLiveData<List<UiOnProductPresetItem>>(listOf())
    private var onProductPresetIds = mutableListOf<Int>()
    private var presets = mutableListOf<GuitarPresetSimpleInfo>()
    private val temporaryDeleteIds = mutableListOf<Int>()
    private var selectedPresetId: Int? = null
    private val deviceListener = object : IV5GattListener {
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            super.onDevFeat(devInfoMap, isNotify)
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.GuitarPresetSelect] as? V5GuitarPresetSelect)?.also {
                    selectedPresetId = it.presetId
                }
            }
        }
    }

    init {
        viewModelScope.launch {
            device?.getDevInfoFeat2<V5GuitarPresetAllSimpleInfoList, V5GuitarPresetOnProductList>()?.also { pair ->
                pair.first?.list?.also { allList ->
                    pair.second?.presetIds?.also { onProductIds ->
                        presets = allList
                        onProductPresetIds = onProductIds
                        loadList()
                    }
                }
            }
            device?.asyncSetDevInfoFeat(V5GuitarPresetSelectQuery(V5ChannelNumber.CH1))
        }
        device?.registerDeviceListener(deviceListener)
    }

    override fun onCleared() {
        super.onCleared()
        device?.unregisterDeviceListener(deviceListener)
    }

    private fun loadList() {
        val ret = mutableListOf<UiOnProductPresetItem>()
        onProductPresetIds.forEach { eachOnProductId ->
            if (isEditing.value!! && temporaryDeleteIds.contains(eachOnProductId)) {
                return@forEach
            }
            presets.find { it.presetId == eachOnProductId }?.also {
                ret.add(UiOnProductPresetItem(if (isEditing.value!!) Type.Editing else Type.Normal, it.name, eachOnProductId))
            }
        }
        if (isEditing.value!!) {
            while (ret.size < 6) {
                ret.add(UiOnProductPresetItem(Type.Empty, null, null))
            }
        }
        items.value = ret
    }

    fun toggleEditSave() {
        val targetIsEditing = !isEditing.value!!
        isEditing.value = targetIsEditing
        if (!targetIsEditing) {
            //save
            val newOnProductPresetIds = onProductPresetIds.filter { !temporaryDeleteIds.contains(it) }.toMutableList()
            device?.asyncSetDevInfoFeat(V5GuitarPresetOnProductList(newOnProductPresetIds))
            onProductPresetIds = newOnProductPresetIds
            temporaryDeleteIds.clear()
        }
        loadList()
    }

    fun delete(presetId: Int) {
        if (temporaryDeleteIds.size == onProductPresetIds.size - 1) {
            showToast(Utils.getApp().getString(R.string.the_minimum_number_of_preset_is_one))
            return
        }
        if (presetId == selectedPresetId) {
            showToast(Utils.getApp().getString(R.string.this_preset_is_in))
            return
        }
        temporaryDeleteIds.add(presetId)
        loadList()
    }

    fun onReSorting(from: Int, to: Int) {
        val fromId = onProductPresetIds[from]
        val toId = onProductPresetIds[to]
        onProductPresetIds[from] = toId
        onProductPresetIds[to] = fromId
    }

    fun onSortDone() {
        loadList()
        device?.asyncSetDevInfoFeat(V5GuitarPresetOnProductList(onProductPresetIds))
    }
}

data class UiOnProductPresetItem(var type: Type, val name: String?, val presetId: Int?) {
    enum class Type {
        Normal,
        Editing,
        Empty
    }
}