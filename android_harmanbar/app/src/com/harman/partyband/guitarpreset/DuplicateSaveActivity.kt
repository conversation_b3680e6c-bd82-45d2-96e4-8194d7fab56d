package com.harman.partyband.guitarpreset

import android.content.Context
import android.os.Bundle
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.os.bundleOf
import androidx.core.view.children
import androidx.lifecycle.MutableLiveData
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityDuplicateSaveBinding
import com.harman.bar.app.databinding.ViewTextWithCheckBinding
import com.harman.dp
import com.harman.partylight.util.popResult
import com.harman.partylight.util.syncPush
import com.harman.partylight.widget.CommBottomSheet
import com.harman.v5protocol.bean.devinfofeat.GuitarPresetSimpleInfo
import com.harman.v5protocol.bean.devinfofeat.V5MusicGenreEnum
import com.harman.widget.AppCompatBaseActivity
import com.wifiaudio.view.pagesmsccontent.showDebugToast
import java.io.Serializable

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/27
 */
class DuplicateSaveActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityDuplicateSaveBinding.inflate(layoutInflater) }
    private val pageType by lazy { intent.extras?.get("pageType") as PageType }
    private val preset by lazy { intent.extras?.get("uiPreset") as UiPresetSimpleItem }
    private val currentGenreIndex by lazy { MutableLiveData(V5MusicGenreEnum.entries.indexOf(preset.genre)) }
    private var result: DuplicateSaveResult? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.et.setText(preset.presetName)
        buildAppbar()
        currentGenreIndex.observe(this) {
            buildGenre(it)
        }
    }

    private fun buildAppbar() {
        binding.ivClose.setOnClickListener { onBackPressed() }
        binding.tvTitle.text = getString(
            when (pageType) {
                PageType.Duplicate -> R.string.Duplicate
                PageType.Save -> R.string.title_save_as
            }
        )
        binding.csbSave.setOnClickListener {
            runCatching {
                result = DuplicateSaveResult(binding.et.text.toString(), V5MusicGenreEnum.entries[currentGenreIndex.value!!])
                finish()
            }.onFailure {
                showDebugToast("there is no selected genre $it")
            }
        }
    }

    private fun buildGenre(activeIndex: Int) {
        if (binding.llMusicGenreContainer.childCount == 0) {
            V5MusicGenreEnum.entries.forEachIndexed { index, v5MusicGenreEnum ->
                ViewTextWithCheckBinding.inflate(layoutInflater).apply {
                    tv.text = getString(v5MusicGenreEnum.nameResId())
                    ivCheck.setImageResource(R.drawable.svg_icon_security_unselected)
                    root.setOnClickListener {
                        currentGenreIndex.value = index
                    }
                }.also {
                    binding.llMusicGenreContainer.addView(it.root, LinearLayout.LayoutParams(-1, 56.dp()))
                }
            }
        }
        binding.llMusicGenreContainer.children.forEachIndexed { i, v ->
            ViewTextWithCheckBinding.bind(v).apply {
                tv.setTextColor(getColor(if (activeIndex == i) R.color.fg_activate else R.color.fg_primary))
                ivCheck.setImageResource(if (activeIndex == i) R.drawable.svg_icon_security_selected else R.drawable.svg_icon_security_unselected)
            }
        }
    }

    override fun onBackPressed() {
        when (pageType) {
            PageType.Duplicate -> CommBottomSheet(
                this,
                getString(R.string.do_you_want_to_discard_the_unsaved_preset),
                action1Text = getString(R.string.discard),
                action2Text = getString(R.string.jbl_CANCEL),
                onAction1Tap = { super.onBackPressed() }
            ).show()

            PageType.Save -> super.onBackPressed()
        }
    }

    override fun finish() {
        super.finish()
        popResult(result)
    }

    companion object {
        suspend fun launch(context: Context, pageType: PageType, uiPreset: UiPresetSimpleItem): DuplicateSaveResult? {
            return context.syncPush<DuplicateSaveActivity, DuplicateSaveResult>(
                bundleOf(
                    "uiPreset" to uiPreset,
                    "pageType" to pageType
                )
            )
        }
    }
}

enum class PageType {
    Duplicate,
    Save
}

data class DuplicateSaveResult(val name: String, val genre: V5MusicGenreEnum) : Serializable