package com.harman.partyband.guitarpreset

import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import com.blankj.utilcode.util.SPUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ViewGuitarPresetGuide1Binding
import com.harman.bar.app.databinding.ViewGuitarPresetGuide2Binding
import com.harman.bar.app.databinding.ViewGuitarPresetGuide3Binding
import com.harman.dp
import com.harman.partylight.util.gone
import com.harman.partylight.util.invisible
import com.harman.partylight.util.visible

/**
 * @Description Detach the guide view layer from [GuitarPresetActivity]
 * <AUTHOR>
 * @Time 2025/4/11
 */
class GuitarPresetActivityGuideView(val activity: GuitarPresetActivity) {
    private val binding = activity.binding
    private val showGuide = MutableLiveData<Boolean>()

    init {
        observeGuide()
    }

    fun checkGuide() {
        showGuide.value = SPUtils.getInstance().getBoolean(guideSpKey, true)
    }

    fun show() {
        showGuide.value = true
    }

    private fun observeGuide() {
        showGuide.distinctUntilChanged().observe(activity) {
            if (it) {
                binding.clGuide.visible()
                binding.appbarGuide.tvTitle.text = activity.getString(R.string.harmanbar_content_Preset)
                binding.appbarGuide.ivLeading.setOnClickListener {
                    showGuide.value = false
                }
                binding.vpGuide.apply {
                    if (null == tag) {
                        val guidePageSize = 3
                        binding.vpGuide.adapter = object : PagerAdapter() {
                            override fun getCount() = guidePageSize
                            override fun isViewFromObject(view: View, `object`: Any) = view == `object`

                            override fun instantiateItem(container: ViewGroup, position: Int): Any {
                                val v = when (position) {
                                    0 -> ViewGuitarPresetGuide1Binding.inflate(activity.layoutInflater).root
                                    1 -> ViewGuitarPresetGuide2Binding.inflate(activity.layoutInflater).let { binding ->
                                        binding.root.post {
                                            val vAnchor1 = activity.window.findViewById<View>(R.id.ivNoiseGate)
                                            val vAnchor2 = activity.window.findViewById<View>(R.id.ivMod)
                                            binding.iv.updateLayoutParams {
                                                this as MarginLayoutParams
                                                leftMargin = vAnchor1.left - 16.dp()
                                                topMargin = vAnchor1.top - 26.dp()
                                            }
                                            binding.iv2.updateLayoutParams {
                                                this as MarginLayoutParams
                                                leftMargin = vAnchor2.left - 16.dp()
                                                bottomMargin = (vAnchor2.parent as ViewGroup).height - vAnchor2.bottom - 29.dp()
                                            }
                                        }
                                        binding.root
                                    }

                                    else -> ViewGuitarPresetGuide3Binding.inflate(activity.layoutInflater).let { binding ->
                                        binding.root.post {
                                            val vAnchor = activity.window.findViewById<View>(R.id.ivNoiseGate)
                                            binding.iv.updateLayoutParams {
                                                this as MarginLayoutParams
                                                leftMargin = vAnchor.left - 16.dp()
                                                topMargin = vAnchor.top - 9.dp()
                                            }
                                        }
                                        binding.root
                                    }
                                }
                                container.addView(v)
                                return v
                            }

                            override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
                                container.removeView(`object` as View)
                            }
                        }
                        binding.vpGuide.addOnPageChangeListener(object : OnPageChangeListener {
                            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) = Unit
                            override fun onPageSelected(position: Int) {
                                binding.indicatorGuide.currentIndex = position
                                if (position == guidePageSize - 1) {
                                    binding.tvGuideGotIt.visible()
                                } else {
                                    binding.tvGuideGotIt.invisible()
                                }
                            }

                            override fun onPageScrollStateChanged(state: Int) = Unit
                        })
                        tag = true
                    }
                }
                binding.tvGuideGotIt.setOnClickListener {
                    showGuide.value = false
                    SPUtils.getInstance().put(guideSpKey, false)
                }
            } else {
                binding.clGuide.gone()
                binding.vpGuide.currentItem = 0
            }
        }
    }

    companion object {
        private const val guideSpKey = "needBandBoxGuitarPresetGuide"
    }
}