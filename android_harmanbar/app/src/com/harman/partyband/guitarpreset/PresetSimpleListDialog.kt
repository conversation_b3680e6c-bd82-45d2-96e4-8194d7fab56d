package com.harman.partyband.guitarpreset

import android.content.DialogInterface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.updatePadding
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ResourceUtils
import com.blankj.utilcode.util.StringUtils
import com.harman.BottomPopUpDialogFragment
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogPresetSimpleListBinding
import com.harman.bar.app.databinding.ViewTextWithCheckBinding
import com.harman.dp
import com.harman.partyband.micsensitivity.SpaceItemDecoration
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.v5protocol.bean.devinfofeat.V5MusicGenreEnum
import kotlinx.coroutines.CompletableDeferred

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/26
 */
class PresetSimpleListDialog(private val activity: GuitarPresetActivity) : BottomPopUpDialogFragment(activity) {
    private val binding by lazy { DialogPresetSimpleListBinding.inflate(layoutInflater) }
    private var result: CompletableDeferred<Int?>? = null

    val rootVm by activityViewModels<GuitarPresetViewModel>()
    val vm by lazy {
        ViewModelProvider(this, PresetSimpleListViewModelFactory(rootVm))[PresetSimpleListViewModel::class.java]
    }

    override fun getContentView() = binding.root

    override fun buildContentView(view: View) {
        binding.ivConfirm.setOnClickListener {
            result?.complete(vm.selectedPresetId())
            dismiss()
        }
        binding.rvGenreType.apply {
            layoutManager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
            adapter = GenreAdapter(this@PresetSimpleListDialog)
            addItemDecoration(SpaceItemDecoration(8.dp()))
        }
        binding.rvPresetList.apply {
            layoutManager = LinearLayoutManager(activity)
            adapter = PresetListAdapter(this@PresetSimpleListDialog)
        }
    }

    override fun observeModel() {}

    override fun prepareData() {}

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        result?.complete(null)
    }

    suspend fun syncShow(fm: FragmentManager): Int? {
        show(fm, null)
        result = CompletableDeferred()
        return result?.await()
    }
}

private class GenreAdapter(val dialog: PresetSimpleListDialog) : RecyclerView.Adapter<GenreAdapter.VH>() {
    private val differ = generateSimpleDiffer<UiGenreItem>()

    init {
        dialog.vm.genreList.observe(dialog.viewLifecycleOwner) {
            differ.submitList(it)
        }
    }

    class VH(val tv: TextView) : ViewHolder(tv.parent as ViewGroup)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        val tv = TextView(parent.context).apply {
            setTextAppearance(R.style.Text_Body_Medium)
            gravity = Gravity.CENTER
            minWidth = 65.dp()
            updatePadding(left = 12.dp(), right = 12.dp())
        }
        val box = FrameLayout(parent.context)
        box.addView(tv, FrameLayout.LayoutParams(-2, 37.dp()).apply {
            gravity = Gravity.CENTER
        })
        box.layoutParams = RecyclerView.LayoutParams(-2, -1)
        return VH(tv)
    }

    override fun getItemCount() = differ.currentList.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        val item = differ.currentList[position]
        holder.tv.apply {
            text = StringUtils.getString(item.genre?.nameResId() ?: R.string.harmanbar_newtidal_All)
            setTextColor(ColorUtils.getColor(if (item.isSelect) R.color.fg_inverse else R.color.fg_secondary))
            background = if (item.isSelect) ResourceUtils.getDrawable(R.drawable.radius_round_fg_primary) else null
            setOnClickListener {
                dialog.vm.switchGenre(item)
            }
        }
    }
}

private class PresetListAdapter(val dialog: PresetSimpleListDialog) : RecyclerView.Adapter<PresetListAdapter.VH>() {
    private val differ = generateSimpleDiffer<UiPresetItem>()

    init {
        dialog.vm.currentGenrePresetList.observe(dialog.viewLifecycleOwner) {
            differ.submitList(it)
        }
    }

    class VH(val binding: ViewTextWithCheckBinding) : ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(LayoutInflater.from(parent.context).inflate(R.layout.view_text_with_check, parent, false).let {
            ViewTextWithCheckBinding.bind(it)
        })
    }

    override fun getItemCount() = differ.currentList.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        val item = differ.currentList[position]
        holder.binding.tv.apply {
            text = item.preset.presetName
        }
        holder.binding.ivCheck.apply {
            setImageResource(if (item.isSelect) R.drawable.svg_icon_security_selected else R.drawable.svg_icon_security_unselected)
        }
        holder.binding.root.setOnClickListener {
            dialog.vm.switchPreset(item)
        }
    }
}

class PresetSimpleListViewModelFactory(private val rootVm: GuitarPresetViewModel) : AbstractSavedStateViewModelFactory() {
    override fun <T : ViewModel> create(key: String, modelClass: Class<T>, handle: SavedStateHandle): T {
        return PresetSimpleListViewModel(rootVm) as T
    }
}

class PresetSimpleListViewModel(val rootVm: GuitarPresetViewModel) : ViewModel() {

    private val allPresetList: List<UiPresetItem> = rootVm.uiPresetSimpleList.value?.map {
        UiPresetItem(it, it.presetId == rootVm.currentUiPreset.value?.presetId)
    } ?: listOf()
    val currentGenrePresetList = MutableLiveData(allPresetList)

    val genreList = MutableLiveData(listOf(UiGenreItem(null, true)) + V5MusicGenreEnum.entries.map { UiGenreItem(it, false) })
    fun selectedPresetId() = allPresetList.find { it.isSelect }?.preset?.presetId

    fun switchGenre(item: UiGenreItem) {
        val selected = genreList.value?.find { it.isSelect }
        if (null == selected || selected.genre == item.genre) {
            return
        }
        selected.isSelect = false
        item.isSelect = true
        genreList.value = genreList.value!!.toList()
        if (null == item.genre) {
            currentGenrePresetList.value = allPresetList
        } else {
            currentGenrePresetList.value = allPresetList.filter { it.preset.genre == item.genre }
        }
    }

    fun switchPreset(item: UiPresetItem) {
        val selected = allPresetList.find { it.isSelect }
        if (null == selected || selected.preset == item.preset) {
            return
        }
        selected.isSelect = false
        item.isSelect = true
        currentGenrePresetList.value = currentGenrePresetList.value?.toList() ?: listOf()
    }
}

data class UiPresetItem(val preset: UiPresetSimpleItem, var isSelect: Boolean)

/**
 * @param genre null means all type
 */
data class UiGenreItem(val genre: V5MusicGenreEnum?, var isSelect: Boolean)