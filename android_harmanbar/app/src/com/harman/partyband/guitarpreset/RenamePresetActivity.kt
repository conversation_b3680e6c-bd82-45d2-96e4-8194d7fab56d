package com.harman.partyband.guitarpreset

import android.os.Bundle
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityRenamePresetBinding
import com.harman.partylight.util.popResult
import com.harman.partylight.widget.CommBottomSheet
import com.harman.widget.AppCompatBaseActivity

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/3/26
 */
class RenamePresetActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityRenamePresetBinding.inflate(layoutInflater) }
    private val presetName by lazy { intent.extras?.getString("presetName") ?: "" }
    private var result: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.appbar.tvTitle.text = getString(R.string.rename_text)
        binding.appbar.ivLeading.setOnClickListener {
            onBackPressed()
        }
        binding.et.setText(presetName)
        binding.cbSave.setOnClickListener {
            result = binding.et.text.toString()
            finish()
        }
    }

    override fun onBackPressed() {
        CommBottomSheet(
            this,
            getString(R.string.do_you_want_to_discard_the_unsaved_preset),
            action1Text = getString(R.string.discard),
            action2Text = getString(R.string.jbl_CANCEL),
            onAction1Tap = {
                super.onBackPressed()
            }
        ).show()
    }

    override fun finish() {
        super.finish()
        popResult(result)
    }
}