package com.harman.partyband.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import com.harman.bar.app.R
import com.harman.dpToPx
import com.harman.log.Logger
import com.harman.partyband.looper.PartBandGuitarLooperViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.abs
import kotlin.random.Random
import kotlin.random.nextInt

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.widget
 * @ClassName: GuitarLooperAudioWaveView
 * @Description:
 * @Author: mixie
 * @CreateDate: 2024/12/27
 * @UpdateUser:
 * @UpdateDate: 2024/12/27
 * @UpdateRemark:
 * @Version: 1.0
 */
class GuitarLooperAudioWaveView : View {

    companion object {
        const val TAG = "GuitarLooperAudioWaveView"
        const val MAX_COUNT = 61//60s
        const val RECONDING_SEEKLINE = "record"
        const val LEFT_SEEK_LINE = "left"
        const val RIGHT_SEEK_LINE = "right"
        const val MINTIME = "0.00s"
        const val MAXTIME = "60.00s"
    }

    constructor(context: Context) : super(context) {
        this.init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        this.init(context, attrs)
    }

    private fun log(msg: String) {
        Logger.i(TAG, "${msg}")
    }

    private val paintDebug = Paint().apply {
        this.isAntiAlias = true
        this.style = Paint.Style.STROKE
    }
    private val paintAudioWave = Paint().apply {
        this.isAntiAlias = true
        this.style = Paint.Style.FILL
        this.strokeCap = Paint.Cap.ROUND
    }
    private val paintSeek = Paint().apply {
        this.isAntiAlias = true
        this.style = Paint.Style.FILL
        this.strokeCap = Paint.Cap.ROUND
    }
    private val paintPoint = Paint().apply {
        this.isAntiAlias = true
        this.style = Paint.Style.FILL
        this.strokeCap = Paint.Cap.ROUND
    }
    private val paintTriangle = Paint().apply {
        this.isAntiAlias = true
        this.style = Paint.Style.FILL
        this.strokeCap = Paint.Cap.ROUND
    }

    private val paintTime = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
        color = context.getColor(R.color.fg_secondary)
        textSize = 12f.dp2px(context)
    }

    private val drumReactPaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        color = context.getColor(R.color.fg_secondary)
        strokeCap = Paint.Cap.ROUND
        strokeWidth = 2f.dp2px(context)
    }


    private val valueList = MutableStateFlow<List<Float>>(emptyList())
    private val audioWaveAnimItemList = CopyOnWriteArrayList<AudioWaveAnimItemDrawer>()
    private var audioWaveSubItemWidth: Float = 0f
    private var horizontalSpace: Float = 0f
    private var viewPortWidth: Float = 0f
    private var audioWaveHeight: Float = 0f
    private var totalItemCount: Int = 0
    private var debug: Boolean = false
    private var audioWaveItemColor: Int = 0
    private var leftRightSpace: Float = 0f
    private var trimTimeTextSize: Float = 0f
    private var trimRectMaxHeight: Float = 0f
    private var defaultSeekColor: Int = 0

    private val pathTriangle: Path = Path()
    private var triangleWidth: Float = 0f
    private var triangleHeight: Float = 0f
    private var recordingTotalTime: Int = 0

    //
    private var seekLineCircleRadius: Float = 0f
    private var seekLineColorRecording: Int = 0
    private var seekLineColorTrim: Int = 0
    private var seekLineWidth: Float = 0f
    private var seekLineRecordingHeight = 0f
    private var seekLineTrimHeight = 0f
    private var seekLineRecordDrawer: SeekLineDrawable? = null
    private var seekLineTrimLeftDrawer: SeekLineDrawable? = null
    private var seekLineTrimRightDrawer: SeekLineDrawable? = null
    private var currentStatus: AudioWaveStatus = AudioWaveStatus.Ready
    private var playProgress: Int = 0//s
    private var withSound: Boolean = true
    private var drumRectHeight = 0f
    private var drumBorderWidth = 0f
    private var drumType = PartBandGuitarLooperViewModel.DrumType.FREEHAND
    private var drumBarNum = 1


    private var currentTrimDragDrawer: SeekLineDrawable? = null
    private var downX: Float = 0f
    private var downY: Float = 0f
    private var audioWaveStartY = 0f

    private var supportDragProgressArea: RectF = RectF(0f, 0f, 0f, 0f)
    private var progressDragRectF: RectF = RectF(0f, 0f, 0f, 0f)
    private var progressDebug: Boolean = false
    private var dragProgress: Boolean = false

    var onAudioWaveStatusChange: ((status: AudioWaveStatus, waveItemSize: Int) -> Unit)? = null
    var onAudioTrimStatusChange: ((startTime: Int, endTime: Int) -> Unit)? = null
    var onAudioPlayed: (() -> Unit)? = null
    private val recordDeque = ArrayDeque<AudioRecordData>()
    private var overdubStartX = 0f

    /**
     * @param eventAction MotionEvent.ACTION_DOWN/MotionEvent.ACTION_UP
     */
    var onTouchEvent: ((status: AudioWaveStatus, eventAction: Int) -> Unit)? = null

    /**
     *
     */
    var onSeekTo: ((progress: Int, seekEnd: Boolean) -> Unit)? = null


    private fun init(context: Context, attrs: AttributeSet?) {
        audioWaveSubItemWidth = 3.0f.dp2px(context)
        audioWaveHeight = 35f.dp2px(context)
        seekLineCircleRadius = 6f.dp2px(context)
        seekLineWidth = 4f.dp2px(context)
        seekLineRecordingHeight = 110f.dp2px(context)
        seekLineTrimHeight = 170f.dp2px(context)
        leftRightSpace = 10f.dp2px(context)
        trimTimeTextSize = 12f.dp2px(context)
        triangleWidth = 10f.dp2px(context)
        triangleHeight = 7f.dp2px(context)
        trimRectMaxHeight = 83f.dp2px(context)
        seekLineColorRecording = context.getColor(R.color.red_1)
        seekLineColorTrim = context.getColor(R.color.green_2)
        audioWaveItemColor = context.getColor(R.color.green_2)
        seekLineRecordDrawer = SeekLineDrawable(RECONDING_SEEKLINE)
        seekLineTrimLeftDrawer = SeekLineDrawable(LEFT_SEEK_LINE)
        seekLineTrimRightDrawer = SeekLineDrawable(RIGHT_SEEK_LINE)
        paintPoint.color = context.getColor(R.color.color_33D9D9D9)
        totalItemCount = MAX_COUNT
        defaultSeekColor = context.getColor(R.color.bg_on_card)
        overdubStartX = leftRightSpace
        drumRectHeight = 45f.dp2px(context)
        drumBorderWidth = 2f.dp2px(context)
    }


    private fun initData() {
        audioWaveSubItemWidth = 3.0f.dp2px(context)
        viewPortWidth = width.toFloat() - leftRightSpace * 2
        totalItemCount = MAX_COUNT
        val totalItemsWidth = totalItemCount * audioWaveSubItemWidth
        horizontalSpace = (viewPortWidth - totalItemsWidth) / totalItemCount
        log("initData viewPortWidth=${viewPortWidth} totalItemCount=${totalItemCount} ")
        seekLineTrimLeftDrawer!!.bitmap = svgToBitmap(context, R.drawable.icon_trim_normal_left)
        seekLineTrimRightDrawer!!.bitmap = svgToBitmap(context, R.drawable.icon_trim_normal_right)
        audioWaveStartY = (height - audioWaveHeight) / 2f
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        initData()
    }

    fun initAudioWaveAnim() {
        if (audioWaveAnimItemList.isEmpty()) {
            repeat(totalItemCount) {
                val value = Random.nextInt(IntRange(10, 100)) / 100f
                val newValue = value.coerceIn(0f, 1f)
                val item = AudioWaveAnimItemDrawer()
                item.hvalue = value
                item.w = audioWaveSubItemWidth
                item.h = audioWaveHeight * newValue
                audioWaveAnimItemList.add(item)
            }
        }
    }


    fun updateValueList(list: List<Float>) {
        valueList.value = list.reversed()
        onAudioWaveStatusChange?.invoke(currentStatus, valueList.value.size)
        invalidate()
    }

    fun setOriginalValue(list: List<Float>, time: Int) {
        recordDeque.addFirst(AudioRecordData(list, time))
    }

    fun getRecordData(): AudioRecordData? {
        if (recordDeque.isNotEmpty()) {
            return recordDeque.first()
        }
        return null
    }

    fun undo() {
        if (recordDeque.isNotEmpty()) {
            recordDeque.removeFirst()
        }
        overdubStartX = leftRightSpace
    }

    fun clearRecordData() {
        if (recordDeque.isNotEmpty()) {
            recordDeque.clear()
        }
        overdubStartX = leftRightSpace
    }

    fun setAudioWaveStatus(status: AudioWaveStatus) {
        this.currentStatus = status
        onAudioWaveStatusChange?.invoke(status, valueList.value.size)
        invalidate()
    }

    fun updateTrimSeekLineInitX() {
        seekLineTrimLeftDrawer?.seekLineInitX = getAudioWaveItemX(0)
        seekLineTrimRightDrawer?.seekLineInitX = getAudioWaveItemX(valueList.value.size)
        invalidate()
    }

    fun setRecordingTime(time: Int) {//ms
        this.recordingTotalTime = time
        invalidate()
    }

    fun reset(invalidata: Boolean = true) {
        recordingTotalTime = 0
        valueList.value = emptyList()
        seekLineTrimLeftDrawer?.reset()
        seekLineTrimRightDrawer?.reset()
        seekLineRecordDrawer?.reset()
        drumBarNum = 1
        drumType = PartBandGuitarLooperViewModel.DrumType.FREEHAND
        initData()
        if (invalidata) {
            invalidate()
        }
    }

    fun withSound(withSound: Boolean) {
        this.withSound = withSound
        invalidate()
    }

    fun isWithSound() = this.withSound


    private fun notifyTrimTime() {
        val pair = trimTimeRange()
        onAudioTrimStatusChange?.invoke(pair.first, pair.second)
    }


    //audioWaveItemList[idx]
    private fun getAudioWaveItemX(idx: Int): Float {
        return leftRightSpace + (idx) * (audioWaveSubItemWidth + horizontalSpace)
    }

    private fun seekLineXTransToIndex(seeklineX: Float): Int {
        val index = (seeklineX - leftRightSpace) / (audioWaveSubItemWidth + horizontalSpace)
        return index.toInt()
    }

    fun currentStatus(): AudioWaveStatus {
        return currentStatus
    }


    private fun trimTimeRange(): Pair<Int, Int> {//s
        val trimStartX = getAudioWaveItemX(0)
        val trimEndX = getAudioWaveItemX(valueList.value.size)
        val trimWidth = trimEndX - trimStartX
        val scaleRecord = recordingTotalTime / trimWidth
        val leftTime = scaleRecord * abs(seekLineTrimLeftDrawer!!.x - trimStartX)
        val rightTime = scaleRecord * abs(seekLineTrimRightDrawer!!.x - trimStartX)
        return leftTime.toInt() to rightTime.toInt()
    }

    private fun audioDuration(): Int {
        val pair = trimTimeRange()
        val audioDuration = pair.second - pair.first
        return audioDuration
    }

    fun setPlayProgress(progress: Int) {
        kotlin.runCatching {
            val audioDuration = audioDuration()
            val newProgression = progress.coerceIn(0, audioDuration)
            this.playProgress = newProgression
        }.onFailure {
            it.printStackTrace()
        }

        postInvalidate()
    }

    fun setCurrentBarNumber(currentBar: Int) {
        this.drumBarNum = currentBar
    }

    fun setDrumMode(model: PartBandGuitarLooperViewModel.DrumType) {
        this.drumType = model
    }

    fun initDrumPlayRange() {
        audioWaveSubItemWidth =
            if (drumBarNum > 16) 1.0f.dp2px(context) / 2 else if(drumBarNum > 8) 1.5f.dp2px(context) else 3.0f.dp2px(context)
        updateTrimSeekLineInitX()
        seekLineTrimRightDrawer?.run { x = seekLineInitX }
    }


    enum class AudioWaveStatus(val code: Int) {
        Ready(1),
        Recording(2),
        RecordingFinish(3),
        Triming(4),
        Playing(5),
        Pause(6),
        Excessive(7),
        Overdub(8),
        DrumPlaying(9),
        CountDown(10),
        DrumRecord(11)
    }


    private var moveX: Float = 0f

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        if (event.action == MotionEvent.ACTION_DOWN ||
            event.action == MotionEvent.ACTION_UP ||
            event.action == MotionEvent.ACTION_CANCEL
        ) {
            log("onTouchEvent currentStatus=${currentStatus} action=${event.action} x=${x} y=${y}")
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                downX = x
                downY = y
                moveX = downX
                debug = false
                progressDebug = true
                currentTrimDragDrawer = when {
                    seekLineTrimLeftDrawer!!.dragRectF.contains(
                        downX,
                        downY
                    ) -> seekLineTrimLeftDrawer

                    seekLineTrimRightDrawer!!.dragRectF.contains(
                        downX,
                        downY
                    ) -> seekLineTrimRightDrawer

                    else -> null
                }
                onTouchEvent?.invoke(currentStatus, MotionEvent.ACTION_DOWN)
                dragProgress =
                    supportDragProgressArea.contains(x, y) && currentTrimDragDrawer == null
                log("onTouchEvent=$currentTrimDragDrawer dragProgress=$dragProgress")
            }

            MotionEvent.ACTION_MOVE -> {
                seekLineTrimLeftDrawer!!.bitmap =
                    svgToBitmap(context, R.drawable.icon_trim_press_left)
                seekLineTrimRightDrawer!!.bitmap =
                    svgToBitmap(context, R.drawable.icon_trim_press_right)
                defaultSeekColor = context.getColor(R.color.fg_activate)
                val offsetX = x - downX
                if (currentStatus != AudioWaveStatus.Playing) {
                    currentTrimDragDrawer?.let { drawer ->
                        val limitMinX: Float
                        val limitMaxX: Float
                        when (drawer.name) {
                            LEFT_SEEK_LINE -> {
                                limitMinX = getAudioWaveItemX(0)
                                limitMaxX =
                                    seekLineTrimRightDrawer!!.x - seekLineTrimRightDrawer!!.dragRectF.width()
                            }

                            RIGHT_SEEK_LINE -> {
                                limitMinX =
                                    seekLineTrimLeftDrawer!!.x + seekLineTrimLeftDrawer!!.dragRectF.width()
                                limitMaxX = getAudioWaveItemX(valueList.value.size)
                            }

                            else -> return@let
                        }

                        val newPosition = drawer.seekLineInitX + drawer.seekLineTrimX + offsetX
                        drawer.offsetX = when {
                            newPosition < limitMinX -> limitMinX - drawer.seekLineTrimX - drawer.seekLineInitX
                            newPosition > limitMaxX -> limitMaxX - drawer.seekLineTrimX - drawer.seekLineInitX
                            else -> offsetX
                        }

                    }
                }
                if (dragProgress) {
                    val px = playProgress2X(playProgress) + (x - moveX)
                    moveX = x
                    val progress = x2PlayProgress(px)
                    onSeekTo?.invoke(progress.toInt(), false)
                    onTouchEvent?.invoke(currentStatus, MotionEvent.ACTION_MOVE)
                }
                invalidate()
                Logger.i(TAG, "Touch action_up $dragProgress")
                return true
            }

            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_CANCEL -> {
                seekLineTrimLeftDrawer!!.bitmap =
                    svgToBitmap(context, R.drawable.icon_trim_normal_left)
                seekLineTrimRightDrawer!!.bitmap =
                    svgToBitmap(context, R.drawable.icon_trim_normal_right)
                defaultSeekColor = context.getColor(R.color.bg_on_card)
                log("onTouchEvent=ACTION_UP，ACTION_CANCEL")
                if (currentStatus in setOf(
                        AudioWaveStatus.Triming,
                        AudioWaveStatus.Playing,
                        AudioWaveStatus.Pause
                    )
                ) {

                    when {
                        currentTrimDragDrawer != null -> {
                            currentTrimDragDrawer!!.seekLineTrimX += currentTrimDragDrawer!!.offsetX
                            currentTrimDragDrawer!!.offsetX = 0f
                            notifyTrimTime()
                        }

                        dragProgress -> {
                            val progress = if (abs(x - downX) <= 1) {//tap to edit the play progress
                                x2PlayProgress(downX)
                            } else {
                                val px = this.playProgress2X(this.playProgress) + (x - moveX)
                                x2PlayProgress(px)
                            }
                            onSeekTo?.invoke(progress.toInt(), true)
                        }

                        else -> {
                            onTouchEvent?.invoke(currentStatus, MotionEvent.ACTION_UP)
                        }
                    }
                }

                currentTrimDragDrawer = null
                progressDebug = false
                dragProgress = false
                debug = false
                downX = 0f
                downY = 0f
                moveX = 0f
                invalidate()
            }
        }

        return true
    }

    @SuppressLint("DefaultLocale")
    private fun formatTime(time: Int): String {//ms
//        return String.format("00:%02d", time)
        return String.format("%.2fs", time / 1000f)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        //draw point
//        if (currentStatus != AudioWaveStatus.Excessive && currentStatus != AudioWaveStatus.Overdub) {
//            drawPoint(canvas)
//        }
        var maxTime = MAXTIME
        //draw seekline
        when (currentStatus) {
            AudioWaveStatus.Ready -> {
                drawPoint(canvas)
//                if (withSound) drawAduioWaveAnim(canvas)
            }

            AudioWaveStatus.Recording,
            AudioWaveStatus.RecordingFinish -> {
                drawPoint(canvas)
                val endPoint = drawRecodingWave(canvas, false)
                drawSeekLine(canvas, endPoint.x)
            }

            AudioWaveStatus.DrumPlaying,
            AudioWaveStatus.Pause,
            AudioWaveStatus.Playing,
            AudioWaveStatus.Triming -> {
                if (currentStatus == AudioWaveStatus.Playing ||
                    currentStatus == AudioWaveStatus.Pause ||
                    currentStatus == AudioWaveStatus.DrumPlaying
                ) {
                    drwaPlayProgress(canvas,0,currentStatus != AudioWaveStatus.DrumPlaying)
                }
                horizontalSpace =
                    (width - leftRightSpace * 2 - valueList.value.size * audioWaveSubItemWidth) / valueList.value.size
                seekLineTrimRightDrawer?.seekLineInitX = getAudioWaveItemX(valueList.value.size)
                drawRecodingWave(canvas, true)

                if (drumType == PartBandGuitarLooperViewModel.DrumType.FREEHAND) {
                    drawTrimSeekLine(canvas)
                }

                maxTime = formatTime(recordingTotalTime)
            }

            AudioWaveStatus.Excessive -> {
                moveDown(canvas)
            }

            AudioWaveStatus.Overdub -> {
                val endPoint = drawRecodingWave2(canvas)
                drawSeekLine(canvas, endPoint.x)
                maxTime =
                    formatTime(recordingTotalTime.coerceAtLeast(getRecordData()?.recordTime ?: 0))
            }

            AudioWaveStatus.CountDown -> {
                onDrawCountDown(canvas)
                drwaPlayProgress(canvas,drumRectHeight.toInt() / 2,false)
                drawSeekLine(canvas, leftRightSpace - drumBorderWidth)
                maxTime = formatTime(recordingTotalTime)
            }

            AudioWaveStatus.DrumRecord -> {
                val onDrawDrumRecord = onDrawDrumRecord(canvas)
                onDrawCountDown(canvas)
                drawSeekLine(canvas, onDrawDrumRecord.x)
                maxTime = formatTime(recordingTotalTime)
            }
        }
        drawMinAndMaxTime(maxTime, canvas)

        if (debug) {
            with(paintDebug) {
                color = Color.GREEN
                canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), this)
                textSize = 22f
                strokeWidth = 1f
                canvas.drawText(
                    "${currentStatus}",
                    width / 2f,
                    height - textSize,
                    this
                )
            }
        }
    }


    internal class AudioWaveAnimItemDrawer {
        var x: Float = 0f
        var y: Float = 0f
        var w: Float = 0f
        var h: Float = 0f
        var animH: Float = 0f
        var hvalue: Float = 0f
        var rectF: RectF = RectF(0f, 0f, 0f, 0f)
        var time: Int = 0 // s
        fun draw(canvas: Canvas, paint: Paint) {
            rectF.set(x, y, x + w, y + animH)
            canvas.drawRoundRect(rectF, w, w, paint)
        }
    }

    internal class SeekLineDrawable(val name: String) {
        var x: Float = 0f
        var y: Float = 0f
        var w: Float = 0f
        var h: Float = 0f
        var circleRadius: Float = 0f
        var rectF: RectF = RectF(0f, 0f, 0f, 0f)
        var dragRectF: RectF = RectF(0f, 0f, 0f, 0f)
        var seekLineTrimX: Float = 0f
        var seekLineInitX: Float = 0f
        var offsetX: Float = 0f
        var bitmap: Bitmap? = null

        fun reset() {
            seekLineTrimX = 0f
            seekLineInitX = 0f
            offsetX = 0f
        }

        fun updateRectF(x: Float, y: Float, w: Float, h: Float) {
            rectF.set(x, y, x + w, y + h)
        }

        fun draw(canvas: Canvas, paint: Paint, hasTopCircle: Boolean = true) {
            canvas.drawRoundRect(rectF, 0f, 0f, paint)
            val centerx = rectF.left + (w / 2f)
            if (hasTopCircle) {
                val startCenterY = rectF.top
                canvas.drawCircle(centerx, startCenterY, circleRadius, paint)
            }
            val endCenterY = rectF.top + h
            canvas.drawCircle(centerx, endCenterY, circleRadius, paint)
            dragRectF.set(x - circleRadius * 3, y, x + w + circleRadius * 3, y + h + circleRadius)
        }
    }

    private fun drawRecodingWave(canvas: Canvas, stateAble: Boolean): PointF {
        var startX = leftRightSpace
        val startY = (height - audioWaveHeight) / 2f
        paintAudioWave.strokeWidth = 1f
        paintAudioWave.color = when (currentStatus) {
            AudioWaveStatus.RecordingFinish -> {
                audioWaveItemColor
            }

            AudioWaveStatus.Triming -> {
                Color.WHITE
            }

            else -> Color.WHITE
        }
        var endAudioWaveItemX = 0f
        var endAudioWaveItemY = 0f
        val progressMaxWidth = seekLineTrimRightDrawer!!.x - seekLineTrimLeftDrawer!!.x
        val audioDuration = audioDuration()
        val speedWidth = playProgress * (progressMaxWidth / audioDuration)

        valueList.value.forEachIndexed { index, value ->
            val itemH = audioWaveHeight * value
            val itemW = audioWaveSubItemWidth
            val drawX = startX
            val drawY = startY + (audioWaveHeight - itemH) / 2
            paintAudioWave.color = Color.WHITE
            if (stateAble) {
                paintAudioWave.color =
                    if ((seekLineTrimLeftDrawer!!.x + speedWidth) > drawX && drawX >= seekLineTrimLeftDrawer!!.x && drawX <= seekLineTrimRightDrawer!!.x) {
                        overdubStartX = drawX + itemW + horizontalSpace
                        Color.WHITE
                    } else
                        context.getColor(R.color.color_33D9D9D9)
            }
            canvas.drawRoundRect(
                drawX,
                drawY,
                drawX + itemW,
                drawY + itemH,
                itemW,
                itemW,
                paintAudioWave
            )

            getRecordData()?.let {
                if (index <= it.data.size - 1) {
                    if (stateAble) {
                        paintAudioWave.color =
                            if ((seekLineTrimLeftDrawer!!.x + speedWidth) > drawX && drawX >= seekLineTrimLeftDrawer!!.x && drawX <= seekLineTrimRightDrawer!!.x)
                                context.getColor(R.color.red_2)
                            else
                                context.getColor(R.color.color_33D9D9D9)
                    }
                    val originalItemH = audioWaveHeight * it.data[index]
                    val originalY = startY + (audioWaveHeight - originalItemH) / 2

                    canvas.drawRoundRect(
                        drawX,
                        originalY,
                        drawX + itemW,
                        originalY + originalItemH,
                        itemW,
                        itemW,
                        paintAudioWave
                    )
                }
            }


            startX += (itemW + horizontalSpace)
            if (index <= MAX_COUNT) {
                endAudioWaveItemX = drawX
                endAudioWaveItemY = drawY
            }
        }
        if (drumType != PartBandGuitarLooperViewModel.DrumType.FREEHAND) {
            drawDrumSession(startY + (audioWaveHeight - drumRectHeight) / 2, speedWidth, canvas)
        }
        return PointF(endAudioWaveItemX, endAudioWaveItemY)
    }

    private fun drawDrumSession(startY: Float, speedWidth: Float, canvas: Canvas) {
        var startX = leftRightSpace - drumBorderWidth
        val itemW = (width - leftRightSpace * 2) / drumBarNum
        repeat(drumBarNum) {
            var drawX = startX
            var drawY = startY

            drumReactPaint.color = if (speedWidth >= drawX)
                context.getColor(R.color.fg_secondary)
            else
                context.getColor(R.color.fg_disabled)

            canvas.drawRoundRect(
                drawX,
                drawY,
                startX + itemW,
                drawY + drumRectHeight,
                seekLineCircleRadius,
                seekLineCircleRadius,
                drumReactPaint
            )

            startX += itemW + drumBorderWidth
        }
    }

    private fun drawRecodingWave2(canvas: Canvas): PointF {
        val startY = (height / 2 - audioWaveHeight)
        paintAudioWave.strokeWidth = 1f
//        var endAudioWaveItemX = 0f
//        var endAudioWaveItemY = 0f
        val originalSize = getRecordData()?.data?.size ?: 0
        val max = originalSize.coerceAtLeast(valueList.value.size)
        val horizontalSpace = (width - leftRightSpace * 2 - max * audioWaveSubItemWidth) / max

        val leftSize = (overdubStartX - leftRightSpace) / (audioWaveSubItemWidth + horizontalSpace)
        var startX = leftRightSpace
//        if (valueList.value.size > originalSize) {
//            leftRightSpace
//        } else {
//            if (valueList.value.size > originalSize - leftSize.toInt())
//                width - (valueList.value.size * (audioWaveSubItemWidth + horizontalSpace))
//            else
//                overdubStartX
//        }
        var point = PointF(startX, 0f)
        val leftX = startX
        valueList.value.forEachIndexed { index, value ->
            paintAudioWave.color = context.getColor(R.color.red_2)
            val itemH = audioWaveHeight * value
            val itemW = audioWaveSubItemWidth
            val drawX = startX
            val drawY = startY + (audioWaveHeight - itemH) / 2
            canvas.drawRoundRect(
                drawX,
                drawY,
                drawX + itemW,
                drawY + itemH,
                itemW,
                itemW,
                paintAudioWave
            )
            startX += (itemW + horizontalSpace)

            point =
                if (drawX >= width.toFloat() - leftRightSpace || valueList.value.size >= (getRecordData()?.data?.size
                        ?: 0)
                )
                    PointF(width.toFloat() - leftRightSpace, drawY)
                else
                    PointF(drawX, drawY)
        }

        var oriStartX = leftRightSpace
        val originalSpace =
            (width - leftRightSpace * 2 - originalSize * audioWaveSubItemWidth) / originalSize
        getRecordData()?.data?.forEachIndexed { index, value ->
            paintAudioWave.color =
                if ((oriStartX >= leftX && oriStartX + originalSpace + audioWaveSubItemWidth <= startX) || valueList.value.size > (getRecordData()?.data?.size
                        ?: 0)
                )
                    Color.WHITE
                else
                    context.getColor(R.color.color_33D9D9D9)
            val itemH = audioWaveHeight * value
            val itemW = audioWaveSubItemWidth
            val drawX = oriStartX
            val drawY = (audioWaveHeight - itemH) / 2 + height / 2
            canvas.drawRoundRect(
                drawX,
                drawY,
                drawX + itemW,
                drawY + itemH,
                itemW,
                itemW,
                paintAudioWave
            )
            oriStartX += (itemW + originalSpace)

        }
        if (drumType != PartBandGuitarLooperViewModel.DrumType.FREEHAND) {
            drawDrumSession((audioWaveHeight - drumRectHeight) / 2 + height / 2 + (drumRectHeight - audioWaveHeight) /2, startX, canvas)
        }
        return point
    }

    private fun moveDown(canvas: Canvas) {
        var oriStartX = leftRightSpace
        val originalSpace =
            (width - leftRightSpace * 2 - (getRecordData()?.data?.size
                ?: 0) * audioWaveSubItemWidth) / (getRecordData()?.data?.size ?: 1)
        getRecordData()?.data?.forEachIndexed { index, value ->
            paintAudioWave.color = context.getColor(R.color.color_33D9D9D9)
            val itemH = audioWaveHeight * value
            val itemW = audioWaveSubItemWidth
            val drawX = oriStartX
            val drawY = audioWaveStartY + (audioWaveHeight - itemH) / 2
            canvas.drawRoundRect(
                drawX,
                drawY,
                drawX + itemW,
                drawY + itemH,
                itemW,
                itemW,
                paintAudioWave
            )
            oriStartX += (itemW + originalSpace)
        }

//        if (drumType != PartBandGuitarLooperViewModel.DrumType.FREEHAND) {
//            drawDrumSession(audioWaveStartY, overdubStartX, canvas)
//            drwaPlayProgress(canvas)
//        }

    }

    private fun onDrawDrumRecord(canvas: Canvas): PointF {
        val progressMaxWidth = seekLineTrimRightDrawer!!.x - seekLineTrimLeftDrawer!!.x
        val audioDuration = audioDuration()
        val speedWidth = playProgress * (progressMaxWidth / audioDuration)

        val startY = (height / 2 - audioWaveHeight)
        var startX = leftRightSpace

        valueList.value.forEachIndexed { index, value ->
            paintAudioWave.color = context.getColor(R.color.red_2)
            val itemH = audioWaveHeight * value
            val itemW = audioWaveSubItemWidth
            val drawX = startX
            val drawY = startY + (audioWaveHeight - itemH) / 2
            if (drawX + itemW > speedWidth){
                return PointF(speedWidth,0f)
            }
            canvas.drawRoundRect(
                drawX,
                drawY,
                drawX + itemW,
                drawY + itemH,
                itemW,
                itemW,
                paintAudioWave
            )
            startX += (itemW + horizontalSpace)

        }
        return PointF(speedWidth,0f)
    }

    private fun onDrawCountDown(canvas: Canvas) {
        val progressMaxWidth = seekLineTrimRightDrawer!!.x - seekLineTrimLeftDrawer!!.x
        val audioDuration = audioDuration()
        val speedWidth = playProgress * (progressMaxWidth / audioDuration)
        var originalStartX = leftRightSpace
        val originalSize = getRecordData()?.data?.size ?: 0
        val originalSpace =
            (width - leftRightSpace * 2 - originalSize * audioWaveSubItemWidth) / originalSize

        getRecordData()?.data?.forEachIndexed { index, value ->
            val itemH = audioWaveHeight * value
            val itemW = audioWaveSubItemWidth
            val drawX = originalStartX
            val drawY = (audioWaveHeight - itemH) / 2 + height / 2 + (drumRectHeight - audioWaveHeight) /2

            paintAudioWave.color =
                if ((seekLineTrimLeftDrawer!!.x + speedWidth) > drawX && drawX >= seekLineTrimLeftDrawer!!.x && drawX <= seekLineTrimRightDrawer!!.x) {
                    overdubStartX = drawX + itemW + horizontalSpace
                    Color.WHITE
                } else
                    context.getColor(R.color.color_33D9D9D9)

            canvas.drawRoundRect(
                drawX,
                drawY,
                drawX + itemW,
                drawY + itemH,
                itemW,
                itemW,
                paintAudioWave
            )
            originalStartX += (itemW + originalSpace)

        }
        if (drumType != PartBandGuitarLooperViewModel.DrumType.FREEHAND) {
            drawDrumSession((audioWaveHeight - drumRectHeight) / 2 + height / 2 + (drumRectHeight - audioWaveHeight) /2, speedWidth, canvas)
        }

    }


    /**
     * withSound
     */
    private fun drawAduioWaveAnim(canvas: Canvas) {
        paintAudioWave.strokeWidth = 1f
        paintAudioWave.color = Color.WHITE
        var startX = leftRightSpace
        val startY = (height - audioWaveHeight) / 2f
        audioWaveAnimItemList.forEachIndexed { _, audioWaveItemDrawer ->
            val value = Random.nextInt(IntRange(10, 100)) / 100f
            audioWaveItemDrawer.h = audioWaveHeight * value
            audioWaveItemDrawer.animH = audioWaveItemDrawer.h
            val drawX = startX
            val drawY = startY + (audioWaveHeight - audioWaveItemDrawer.animH) / 2
            audioWaveItemDrawer.x = drawX
            audioWaveItemDrawer.y = drawY
            audioWaveItemDrawer.draw(canvas, paintAudioWave)
            startX += (audioWaveItemDrawer.w + horizontalSpace)
        }
        kotlin.runCatching {
            Thread.sleep(100)
        }.onFailure {
            it.printStackTrace()
        }
        invalidate()
    }


    private fun drawSeekLine(canvas: Canvas, x: Float) {
        paintSeek.color = seekLineColorRecording
        paintSeek.strokeWidth = seekLineWidth
        paintSeek.style = Paint.Style.FILL
        val startX = x
        val startY = (height - seekLineRecordingHeight) / 2f
        seekLineRecordDrawer?.apply {
            this.w = seekLineWidth
            this.h = seekLineRecordingHeight
            this.x = startX
            this.y = startY
            this.updateRectF(this.x, this.y, this.w, this.h)
            this.circleRadius = seekLineCircleRadius
            this.draw(canvas, paintSeek)
        }
    }

    private fun drawTrimSeekLine(canvas: Canvas) {
        paintSeek.color = defaultSeekColor
        paintSeek.strokeWidth = seekLineWidth
        paintSeek.style = Paint.Style.FILL
        var left = 0f
        var right = 0f
        var top = (height - trimRectMaxHeight) / 2 - 2f.dp2px(context)
        var bottom = (height - trimRectMaxHeight) / 2 + trimRectMaxHeight + 2f.dp2px(context)

        //left
        seekLineTrimLeftDrawer?.apply {
            this.w = triangleWidth
            this.circleRadius = seekLineCircleRadius
            this.h = bottom - top
            val drawX = this.seekLineInitX + this.seekLineTrimX + this.offsetX
            val drawY = top
            this.x = drawX
            this.y = drawY
            this.updateRectF(this.x - triangleWidth, this.y, w, h)
//            this.draw(canvas, paintSeek, false)
            bitmap?.run {
                canvas.drawBitmap(this, Rect(0, 0, width, height), rectF, paintSeek)
            }
            dragRectF.set(x - circleRadius * 3, y, x + w + circleRadius * 3, y + h + circleRadius)
            left = drawX + w - triangleWidth
        }
        //right
        seekLineTrimRightDrawer?.apply {
            this.w = triangleWidth
            this.circleRadius = seekLineCircleRadius
            this.h = bottom - top
            val drawX = this.seekLineInitX + this.seekLineTrimX + this.offsetX
            val drawY = top
            this.x = drawX
            this.y = drawY
            this.updateRectF(this.x, this.y, w, h)
//            this.draw(canvas, paintSeek, false)
            bitmap?.run {
                canvas.drawBitmap(this, Rect(0, 0, width, height), rectF, paintSeek)
            }
            dragRectF.set(x - circleRadius * 3, y, x + w + circleRadius * 3, y + h + circleRadius)
            right = drawX
        }

        canvas.drawRect(
            RectF(
                left,
                (height - trimRectMaxHeight) / 2 - 2f.dp2px(context),
                right,
                (height - trimRectMaxHeight) / 2
            ), paintSeek
        )
        canvas.drawRect(
            RectF(
                left,
                (height - trimRectMaxHeight) / 2 + trimRectMaxHeight,
                right,
                (height - trimRectMaxHeight) / 2 + trimRectMaxHeight + 2f.dp2px(context)
            ), paintSeek
        )


        //mask rect
        paintSeek.alpha = (255f * 0.5f).toInt()
        val rectH = trimRectMaxHeight
        val rectx = seekLineTrimLeftDrawer!!.x
        val recty = (height - rectH) / 2
        val rectw = abs(seekLineTrimLeftDrawer!!.x - seekLineTrimRightDrawer!!.x)
//        canvas.drawRect(rectx, recty, rectx + rectw, recty + rectH, paintSeek)
        paintSeek.alpha = 255

        //text
//        paintSeek.textSize = trimTimeTextSize
//        val trimTimePair = trimTimeRange()
//        seekLineTrimLeftDrawer?.apply {
//            paintSeek.textAlign = Paint.Align.LEFT
//            canvas.drawText(
//                formatTime(trimTimePair.first),
//                this.x + trimTimeTextSize,
//                this.y + trimTimeTextSize,
//                paintSeek
//            )
//        }
//        seekLineTrimRightDrawer?.apply {
//            paintSeek.textAlign = Paint.Align.RIGHT
//            canvas.drawText(
//                formatTime(trimTimePair.second),
//                this.x - trimTimeTextSize,
//                this.y + trimTimeTextSize,
//                paintSeek
//            )
//        }


        //debug
        if (debug) {
            paintSeek.color = Color.RED
            paintSeek.style = Paint.Style.STROKE
            paintSeek.strokeWidth = 1f
            canvas.drawRoundRect(seekLineTrimLeftDrawer!!.dragRectF, 0f, 0f, paintSeek)
            canvas.drawRoundRect(seekLineTrimRightDrawer!!.dragRectF, 0f, 0f, paintSeek)
            currentTrimDragDrawer?.let {
                paintSeek.color = Color.WHITE
                canvas.drawRoundRect(it.dragRectF, 0f, 0f, paintSeek)
            }
        }
    }

    private fun drawMinAndMaxTime(maxTime: String, canvas: Canvas) {
        val minStartX = leftRightSpace + 8f.dp2px(context)
        val maxStartX = viewPortWidth - maxTime.length - 8f.dp2px(context) - leftRightSpace
        val startY =
            (height - seekLineRecordingHeight) / 2 + seekLineRecordingHeight + seekLineCircleRadius * 2

        paintTime.color = context.getColor(R.color.fg_secondary)
        canvas.drawText(MINTIME, minStartX, startY, paintTime)
        paintTime.color = when (currentStatus) {
            AudioWaveStatus.Playing,
            AudioWaveStatus.Pause,
            AudioWaveStatus.Triming -> {
                if (maxTime.substringBefore("s").toFloat() >= 55)
                    context.getColor(R.color.red_2)
                else
                    context.getColor(R.color.fg_secondary)
            }
            else -> context.getColor(R.color.fg_secondary)
        }
        canvas.drawText(maxTime, maxStartX, startY, paintTime)
    }

    private fun drawPoint(canvas: Canvas) {
        val circleRadius = audioWaveSubItemWidth / 2
        var startX = leftRightSpace + circleRadius
        val startY = (height - audioWaveSubItemWidth) / 2
        paintAudioWave.strokeWidth = 1f
        repeat(totalItemCount) {
            val drawX = startX
            if (it >= valueList.value.size) {
                canvas.drawCircle(drawX, startY, circleRadius, paintPoint)
            }
            startX += (circleRadius * 2 + horizontalSpace)
        }
    }

    private fun playProgress2X(playProgress: Int): Float {
        val progressMaxWidth = seekLineTrimRightDrawer!!.x - seekLineTrimLeftDrawer!!.x
        val audioDuration = audioDuration()
        val startX = seekLineTrimLeftDrawer!!.x + playProgress * (progressMaxWidth / audioDuration)
        return startX
    }

    private fun x2PlayProgress(x: Float): Float {
        val progressMaxWidth = seekLineTrimRightDrawer!!.x - seekLineTrimLeftDrawer!!.x
        val audioDuration = audioDuration()
        val playProgress = (x - seekLineTrimLeftDrawer!!.x) / (progressMaxWidth / audioDuration)
        return playProgress
    }


    private fun drwaPlayProgress(canvas: Canvas,offsetY: Int,isInvoke: Boolean) {
        paintTriangle.color = Color.WHITE
        paintTriangle.style = Paint.Style.FILL
        paintTriangle.strokeCap = Paint.Cap.ROUND
        val startX = playProgress2X(playProgress)
        val startY = (height - trimRectMaxHeight) / 2 + trimRectMaxHeight + triangleHeight
//        drawTriangle(
//            canvas,
//            x = startX,
//            y = startY,
//            w = triangleWidth,
//            h = triangleHeight,
//            paintTriangle
//        )

        //drwa text
//        val showText = formatTime(playProgress)
//        paintTriangle.textSize = trimTimeTextSize
//        val textOffset = if (startX > width / 2) {
//            -paintTriangle.measureText(showText) - triangleWidth * 2
//        } else {
//            0f
//        }
//        canvas.drawText(
//            showText,
//            startX + triangleWidth + textOffset,
//            startY + triangleHeight,
//            paintTriangle
//        )
        //draw rect
        val progressMaxWidth = seekLineTrimRightDrawer!!.x - seekLineTrimLeftDrawer!!.x
        val audioDuration = audioDuration()
        paintTriangle.alpha = (255f * 0.5f).toInt()
        val rectH = trimRectMaxHeight
        val rectx = seekLineTrimLeftDrawer!!.x
        val recty = (height - rectH) / 2 + offsetY
        val rectw = playProgress * (progressMaxWidth / audioDuration)
//        canvas.drawRect(rectx, recty, rectx + rectw, recty + rectH, paintTriangle)
        paintAudioWave.strokeWidth = 1f
        paintAudioWave.color = Color.WHITE
        canvas.drawRoundRect(
            rectx + rectw,
            recty,
            rectx + rectw + 3.0f.dp2px(context),
            recty + rectH,
            audioWaveSubItemWidth,
            audioWaveSubItemWidth,
            paintAudioWave
        )
        if (playProgress >= audioDuration && isInvoke) {
            overdubStartX = leftRightSpace
            onAudioPlayed?.invoke()
        }
        paintTriangle.alpha = 255


        //
        var beginPosition =
            ((rectx - leftRightSpace) / (audioWaveSubItemWidth + horizontalSpace)).toInt()

//        for (index in beginPosition + 1..<valueList.value.size){
//            var beginX = leftRightSpace + audioWaveSubItemWidth * index + horizontalSpace * index
//            val itemH = audioWaveHeight * valueList.value[index]
//            val itemW = audioWaveSubItemWidth
//            val drawX =
//                leftRightSpace + audioWaveSubItemWidth * index + horizontalSpace * index
//            val drawY = (height - audioWaveHeight) / 2f + (audioWaveHeight - itemH) / 2
//
//            paintAudioWave.color = if (rectx + rectw >= beginX)
//                Color.WHITE
//            else
//                context.getColor(R.color.color_33D9D9D9)
//
//            canvas.drawRoundRect(
//                drawX,
//                drawY,
//                drawX + itemW,
//                drawY + itemH,
//                itemW,
//                itemW,
//                paintAudioWave
//            )
//            beginX += itemW + horizontalSpace
//        }


        //do drag space
        paintTriangle.color = Color.RED
        paintTriangle.style = Paint.Style.STROKE
        val left = startX - triangleWidth
        val top = (height - rectH) / 2
        val right = startX + triangleWidth
        val bottom = top + trimRectMaxHeight
        progressDragRectF.set(left, top, right, bottom)
        //support drag area
        val areaLeft = seekLineTrimLeftDrawer!!.dragRectF.right
        val areaTop = recty
        val areaRight = seekLineTrimRightDrawer!!.dragRectF.left
        val areaBottom = trimRectMaxHeight
        supportDragProgressArea.set(areaLeft, areaTop, areaRight, areaTop + areaBottom)
        //draw debug
        if (progressDebug) {
//            canvas.drawRect(progressDragRectF, paintTriangle)
        }
        paintTriangle.color = Color.BLUE
//        canvas.drawRect(supportDragProgressArea, paintTriangle)
    }

    private fun drawTriangle(canvas: Canvas, x: Float, y: Float, w: Float, h: Float, paint: Paint) {
        pathTriangle.moveTo(x, y)//top point
        pathTriangle.lineTo(x - w / 2f, y + h)//left point
        pathTriangle.lineTo(x + w / 2f, y + h)
        pathTriangle.close()
        canvas.drawPath(pathTriangle, paint)
        pathTriangle.reset()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        this.reset(invalidata = false)
    }

    fun svgToBitmap(context: Context, svgResId: Int): Bitmap {
        val drawable: Drawable? =
            VectorDrawableCompat.create(context.resources, svgResId, context.theme)
        val bitmap = Bitmap.createBitmap(
            drawable!!.intrinsicWidth,
            drawable.intrinsicHeight,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)
        return bitmap
    }

    public data class AudioRecordData(val data: List<Float> = emptyList(), val recordTime: Int = 0)
}