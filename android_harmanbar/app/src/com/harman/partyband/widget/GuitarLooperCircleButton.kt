package com.harman.partyband.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import com.harman.bar.app.R
import kotlin.math.min

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.widget
 * @ClassName: GuitarLooperCircleButton
 * @Description:
 * @Author: mixie
 * @CreateDate: 2024/12/27
 * @UpdateUser:
 * @UpdateDate: 2024/12/27
 * @UpdateRemark:
 * @Version: 1.0
 */
class GuitarLooperCircleButton : View {
    constructor(context: Context) : super(context) {

        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    val paint = Paint().apply {
        this.isAntiAlias = true
        this.style = Paint.Style.STROKE
    }

    private var centerX: Float = 0f
    private var centerY: Float = 0f
    private var radius: Float = 0f
    private var middleRadius: Float = 0f

    private var middleRecordIngRectWh: Float = 0f
    private var middleRecordIngRectConnerRadius: Float = 0f

    private var circularRingColor: Int = 0
    private var circularRingBorderWidth: Float = 0f

    private var middleReadyColor: Int = 0
    private var middlePlayColor: Int = 0
    private var middlePauseColor: Int = 0
    private var middleRecordColor: Int = 0
    private var dissableColor: Int = 0


    private var middleRecordFinishDrawable: Drawable? = null
    private var middleRecordFinishDrawableColor: Int = 0
    private var middleRecordFinishWidth: Float = 0f
    private var middleRecordFinishHeight: Float = 0f

    private var middlePauseDrawable: Drawable? = null
    private var middlePauseDrawableColor: Int = 0
    private var middlePauseWidth: Float = 0f
    private var middlePauseHeight: Float = 0f

    private var type: Type = Type.READY
    private var debug: Boolean = false


    @SuppressLint("UseCompatLoadingForDrawables")
    private fun init(context: Context, attrs: AttributeSet?) {

        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.GuitarLooperCircleButton)
        val typeInt = typeArray.getInt(R.styleable.GuitarLooperCircleButton_glcb_type, 0)
        kotlin.runCatching {
            type = Type.entries.find {
                it.value == typeInt
            }!!
        }.onFailure {
            it.printStackTrace()
        }
        debug = typeArray.getBoolean(R.styleable.GuitarLooperCircleButton_glcb_debug, true)
        typeArray.recycle()

        circularRingColor = context.getColor(R.color.fg_secondary)
        middleReadyColor = context.getColor(R.color.red_1)
        middleRecordFinishDrawableColor = context.getColor(R.color.green_2)
        middlePlayColor = context.getColor(R.color.green_2)
        middlePauseColor = context.getColor(R.color.purple_1)
        middlePauseDrawableColor = context.getColor(R.color.green_2)
        middleRecordColor = context.getColor(R.color.red_1)
        dissableColor=context.getColor(R.color.button_disable)

        middleRecordFinishDrawable = context.getDrawable(R.drawable.ic_looper_record_finish)
        middleRecordFinishDrawable?.setTint(middleRecordFinishDrawableColor)
        middlePauseDrawable = context.getDrawable(R.drawable.ic_looper_record_pause)
        middlePauseDrawable?.setTint(middlePauseDrawableColor)

        circularRingBorderWidth = 4f.dp2px(context)
    }

    enum class Type(val value: Int) {
        READY(0), RECORDING(1), PLAY(2), RECORDFINISH(3), PAUSE(4), STOP(5),OVERDUB(6),COUNTDOWN(7)
    }

    private fun initData() {
        val wh = min(width, height)
        radius = wh / 2f - circularRingBorderWidth
        middleRadius = radius * 0.75f
        middleRecordIngRectWh = wh * 0.4f
        middleRecordIngRectConnerRadius = wh * 0.072f
        centerX = width / 2f
        centerY = height / 2f
        //
        middleRecordFinishWidth = (55f / 160f * wh)
        middleRecordFinishHeight = (40f / 160f * wh)

        middlePauseWidth = (31f / 160f * wh)
        middlePauseHeight = (44f / 160f * wh)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        initData()
    }

    fun setType(type: Type) {
        this.type = type
        postInvalidate()
    }

    fun getType(): Type {
        return this.type
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        //draw circle ring
        paint.strokeWidth = circularRingBorderWidth
        paint.color = circularRingColor
        paint.style=Paint.Style.STROKE
        canvas.drawCircle(centerX, centerY, radius, paint)
        when (type) {
            Type.READY -> {
                drawReady(canvas)
            }

            Type.RECORDING -> {
                drawRecording(canvas)
            }

            Type.RECORDFINISH -> {
                drawRecordFinish(canvas)
            }

            Type.PLAY -> {
                drawPlay(canvas)
            }

            Type.PAUSE -> {
                drawPause(canvas)
            }
            Type.OVERDUB -> {
                drawReady(canvas)
            }
            Type.COUNTDOWN -> {
                drawReady(canvas)
            }

            else -> {

            }
        }

        if (debug) {
            paint.color = Color.GREEN
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 1f
            paint.textAlign = Paint.Align.CENTER
            canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)
            canvas.drawText("${type}", centerX, height - paint.textSize * 2, paint)
        }


    }

    //READY,RECORDING,PLAY, FINISH, PAUSE, STOP
    private fun drawReady(canvas: Canvas) {
        paint.color = if(isEnabled){middleReadyColor}else{dissableColor}
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, middleRadius, paint)
    }

    private fun drawRecording(canvas: Canvas) {
        paint.color =if(isEnabled){middleRecordColor}else{dissableColor}
        paint.style = Paint.Style.FILL
        val startx = (width - middleRecordIngRectWh) / 2f
        val starty = (height - middleRecordIngRectWh) / 2f
        val w = middleRecordIngRectWh
        val h = middleRecordIngRectWh
        canvas.drawRoundRect(
            startx, starty, startx + w, starty + h,
            middleRecordIngRectConnerRadius, middleRecordIngRectConnerRadius, paint
        )
    }

    private fun drawRecordFinish(canvas: Canvas) {
        val w = middleRecordFinishWidth
        val h = middleRecordFinishHeight
        val startx = (width - w) / 2f
        val starty = (height - h) / 2f
        middleRecordFinishDrawable?.setBounds(
            startx.toInt(),
            starty.toInt(),
            (startx + w).toInt(),
            (starty + h).toInt()
        )
        middleRecordFinishDrawable?.draw(canvas)

    }

    private fun drawPlay(canvas: Canvas) {
        paint.color = middlePlayColor
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, middleRadius, paint)
    }

    private fun drawPause(canvas: Canvas) {
        paint.color = middlePauseColor
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, middleRadius, paint)
        //
        val w = middlePauseWidth
        val h = middlePauseHeight
        val startx = (width - w) / 2f
        val starty = (height - h) / 2f
        middlePauseDrawable?.setBounds(
            startx.toInt(),
            starty.toInt(),
            (startx + w).toInt(),
            (starty + h).toInt()
        )
        middlePauseDrawable?.draw(canvas)
    }


}