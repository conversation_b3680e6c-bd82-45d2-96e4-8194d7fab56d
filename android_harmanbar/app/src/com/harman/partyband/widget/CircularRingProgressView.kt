package com.harman.partyband.widget

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.content.res.ResourcesCompat
import com.harman.bar.app.R
import com.harman.dp
import com.harman.utils.ViewUtils
import kotlin.math.abs
import kotlin.math.absoluteValue
import kotlin.math.min

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.widget
 * @ClassName: CircleProgressView
 * @Description:
 * @Author: mixie
 * @CreateDate: 2024/12/18
 * @UpdateUser:
 * @UpdateDate: 2024/12/18
 * @UpdateRemark:
 * @Version: 1.0
 */
class CircularRingProgressView : View {

    companion object {
        const val TAG = "CircularRingProgressView"
        const val MAX_VALUE_DEFAULT = 100
        const val SWEEP_ANGLE = 270F
        const val START_ANGLE = 135F
    }

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    data class ColorProperties(
        val ringColor: Int,
        val ringBackgroundColor: Int,
        val middleCircleColor: Int,
        val middleCircleBorderColor: Int,
        val textColor: Int,
        val longPointColor: Int,
        val middleCircleShadowColor: Int,
    )

    private var maxValue: Int = MAX_VALUE_DEFAULT
    private var value: Int = 0
    private var realValue: Int = 0
    private var debug: Boolean = false
    private var textSize: Float = 0f
    private var centerX: Float = 0f
    private var centerY: Float = 0f

    private lateinit var currentProperties: ColorProperties

    var ringWidth: Float = 0f
        set(value) {
            field = value
            postInvalidate()
        }
    private var ringRadius: Float = 0f
    private var middleCircleRadius: Float = 0f
    var middleCircleBorderWidth: Float = 0f
        set(value) {
            field = value
            postInvalidate()
        }
    var longPointHeight: Float = 0f
        set(value) {
            field = value
            postInvalidate()
        }
    var longPointWidth: Float = 0f
        set(value) {
            field = value
            postInvalidate()
        }
    private var shadowRadius: Float = 0f

    private val paintMiddleCircle = Paint()
    private val paintRing = Paint()
    private val textPaint = Paint()
    private val paintLongPoint = Paint()
    private val debugPaint = Paint()

    private var onClickListener: OnClickListener? = null
    private var animator: ValueAnimator? = null
    var valueFormatter: ((value: Int) -> String)? = null

    private fun init(context: Context, attrs: AttributeSet?) {
        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.CircularRingProgressView)
        maxValue =
            typeArray.getInt(R.styleable.CircularRingProgressView_crpv_max, MAX_VALUE_DEFAULT)
        value = typeArray.getInt(R.styleable.CircularRingProgressView_crpv_value, 0)
        //
        ringWidth =
            typeArray.getDimension(
                R.styleable.CircularRingProgressView_crpv_ring_width,
                4f.dp2px(context)
            )

        middleCircleBorderWidth = typeArray.getDimension(
            R.styleable.CircularRingProgressView_crpv_middle_circle_border_width,
            2f.dp2px(context)
        )
        longPointWidth = typeArray.getDimension(
            R.styleable.CircularRingProgressView_crpv_long_point_width,
            2.5f.dp2px(context)
        )
        longPointHeight = typeArray.getDimension(
            R.styleable.CircularRingProgressView_crpv_long_point_height,
            5f.dp2px(context)
        )
        debug = typeArray.getBoolean(R.styleable.CircularRingProgressView_crpv_debug, false)
        realValue = value
        typeArray.recycle()

        currentProperties = ColorProperties(
            ringColor = context.getColor(R.color.common_white),
            ringBackgroundColor = context.getColor(R.color.common_black),
            middleCircleColor = context.getColor(R.color.color_1E1E1E),//color_1E1E1E
            middleCircleBorderColor = context.getColor(R.color.common_black),
            textColor = context.getColor(R.color.fg_primary),
            longPointColor = context.getColor(R.color.common_white),
            middleCircleShadowColor = context.getColor(R.color.color_80000000)
        )
    }


    override fun setOnClickListener(l: OnClickListener?) {
        onClickListener = l
    }

    fun setTextColor(color: Int) {
        currentProperties = currentProperties.copy(textColor = color)
        postInvalidate()
    }


    fun setValue(valueSet: Int, anim: Boolean = false) {
        val newValue = valueSet.coerceIn(0, maxValue)
        realValue = newValue
        if (anim) {
            animator?.cancel()
            animator = ValueAnimator.ofInt(value, newValue)
            val duration = ((abs(value - newValue) / maxValue.toFloat()) * 200).toLong()
            animator?.setDuration(duration)
            animator?.addUpdateListener {
                this.value = it.animatedValue as Int
                postInvalidate()
            }
            animator?.start()
        } else {
            this.value = newValue
            postInvalidate()
        }

    }

    fun getValue(): Int {
        return this.realValue
    }

    fun setMaxvalue(max: Int) {
        this.maxValue = max
        postInvalidate()
    }

    fun getMaxValue(): Int {
        return this.maxValue
    }

    private fun initValues() {
        val diameter = min(width, height)
        val radius = diameter / 2f
        textSize = radius * 0.35f
        middleCircleRadius = radius * 0.5f
        shadowRadius = (15f / 114f) * diameter//middle circle shadow
        ringRadius = radius - shadowRadius
        centerX = width / 2f
        centerY = height / 2f
        if (debug) {
            Log.i(
                TAG,
                "maxValue=${maxValue} value=${value} size=${width}x${height} radius=${radius} diameter=${diameter} space=${((15f / 114f) * diameter)} currentProperties=${currentProperties} "
            )
        }

    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        initValues()
        //Log.i(TAG, "onLayout=${width}x${height}")
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                ViewUtils.doPerformHapticFeedback(this)
            }

            MotionEvent.ACTION_MOVE -> {
                //do....
            }

            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_CANCEL -> {
                onClickListener?.onClick(this)
            }

        }
        postInvalidate()
        return true
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawMiddleCircle(canvas)
        drawRing(canvas)
        drawLongPoint(canvas)
        drawValueText(canvas)
        if (debug) {
            drawDebug(canvas)
        }
    }

    private fun drawDebug(canvas: Canvas) {
        debugPaint.style = Paint.Style.STROKE
        debugPaint.color = Color.GREEN
        debugPaint.strokeWidth = 10f
        canvas.drawRect(RectF(0f, 0f, width.toFloat(), height.toFloat()), debugPaint)
    }

    private fun drawValueText(canvas: Canvas) {
        //drawtext
        textPaint.color = currentProperties.textColor
        textPaint.textSize = textSize
        textPaint.textAlign = Paint.Align.CENTER
        textPaint.typeface = ResourcesCompat.getFont(context, R.font.poppins_regular)
        val fontMetrics = textPaint.fontMetrics
        val baseline = (fontMetrics.ascent.absoluteValue + fontMetrics.descent.absoluteValue) / 2 - fontMetrics.descent.absoluteValue
        canvas.drawText(valueFormatter?.invoke(value) ?: "$value", centerX, centerY + baseline, textPaint)
    }

    var middleGradientColors: IntArray? = null
        set(value) {
            field = value
            middleGradientDrawable = GradientDrawable().apply {
                colors = value
                shape = GradientDrawable.OVAL
                gradientType = GradientDrawable.SWEEP_GRADIENT
            }
            postInvalidate()
        }
    private var middleGradientDrawable: GradientDrawable? = null
    private fun drawMiddleCircle(canvas: Canvas) {
        paintMiddleCircle.style = Paint.Style.FILL
        paintRing.isAntiAlias = true

        //drawShadow
        paintMiddleCircle.setMaskFilter(BlurMaskFilter(shadowRadius, BlurMaskFilter.Blur.NORMAL))
        paintMiddleCircle.color = currentProperties.middleCircleShadowColor
        canvas.drawCircle(centerX, centerY, middleCircleRadius, paintMiddleCircle)
        paintMiddleCircle.setMaskFilter(null)

        //border
        if (null == middleGradientColors) {
            paintMiddleCircle.color = currentProperties.middleCircleBorderColor
            canvas.drawCircle(centerX, centerY, middleCircleRadius, paintMiddleCircle)
        } else {
            paintMiddleCircle.color = middleGradientColors!![0]
            canvas.drawCircle(centerX, centerY, middleCircleRadius, paintMiddleCircle)
        }
        //middle circle
        val insideRadius = middleCircleRadius - middleCircleBorderWidth
        if (null == middleGradientDrawable) {
            paintMiddleCircle.color = currentProperties.middleCircleColor
            canvas.drawCircle(centerX, centerY, insideRadius, paintMiddleCircle)
        } else {
            val currentAngle = (value.toFloat() / maxValue.toFloat()) * SWEEP_ANGLE + START_ANGLE
            middleGradientDrawable!!.setBounds(
                (centerX - insideRadius).toInt(),
                (centerY - insideRadius).toInt(),
                (centerX + insideRadius).toInt(),
                (centerY + insideRadius).toInt()
            )
            canvas.save()
            canvas.rotate(currentAngle, centerX, centerY)
            middleGradientDrawable!!.draw(canvas)
            canvas.restore()
        }
    }


    private fun drawRing(canvas: Canvas) {
        paintRing.style = Paint.Style.STROKE
        paintRing.isAntiAlias = true
        paintRing.strokeWidth = ringWidth
        paintRing.strokeCap = Paint.Cap.ROUND
        paintRing.color = currentProperties.ringBackgroundColor
        val centerX = width / 2
        val centerY = height / 2
        val oval = RectF(
            (centerX - ringRadius),//left
            (centerY - ringRadius),//top
            (centerX + ringRadius),//right
            (centerY + ringRadius),//bottom
        )
        //ring background
        canvas.drawArc(oval, START_ANGLE, SWEEP_ANGLE, false, paintRing)//
        //Log.i(TAG, "oval=$oval raingWidth=$raingWidth")

        //ring slider
        paintRing.color = currentProperties.ringColor
        val currentAngle = (value.toFloat() / maxValue.toFloat()) * SWEEP_ANGLE

        //shadow
        paintRing.setMaskFilter(BlurMaskFilter(ringWidth * 1f, BlurMaskFilter.Blur.NORMAL))
        canvas.drawArc(oval, START_ANGLE, currentAngle, false, paintRing)//
        //
        paintRing.setMaskFilter(BlurMaskFilter(ringWidth * 0.01f, BlurMaskFilter.Blur.NORMAL))
        paintRing.strokeWidth = ringWidth * 1.1f
        canvas.drawArc(oval, START_ANGLE, currentAngle, false, paintRing)//
        paintRing.reset()
        //Log.i(TAG, "oval=$oval currentAngle=$currentAngle")
    }

    private fun drawLongPoint(canvas: Canvas) {
        paintLongPoint.style = Paint.Style.STROKE
        paintLongPoint.color = currentProperties.longPointColor
        paintLongPoint.strokeWidth = longPointWidth
        paintLongPoint.strokeCap = Paint.Cap.ROUND
        val currentAngle = (value.toFloat() / maxValue.toFloat()) * SWEEP_ANGLE + START_ANGLE
        canvas.save()
        canvas.rotate(currentAngle - SWEEP_ANGLE, centerX, centerY)
        val lineLength = 5.dp()
        val startY = centerY - middleCircleRadius + longPointWidth
        canvas.drawLine(centerX, startY, centerX, startY + lineLength, paintLongPoint)
        canvas.restore()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator?.cancel()
    }
}