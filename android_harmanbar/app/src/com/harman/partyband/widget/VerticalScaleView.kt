package com.harman.partyband.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.harman.bar.app.R
import com.harman.utils.ViewUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext
import kotlin.math.abs

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.widget
 * @ClassName: VerticalScaleView
 * @Description: Degree of completion : 80%  //TODO
 * @Author: mixie
 * @CreateDate: 2024/12/23
 * @UpdateUser:
 * @UpdateDate: 2024/12/23
 * @UpdateRemark:
 * @Version: 1.0
 */
class VerticalScaleView : View, CoroutineScope {

    companion object {
        const val TAG = "VerticalScaleView"
        const val MAX_VALUE_DEFAULT = 50
    }

    private val viewJob = Job()

    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + viewJob

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet) {
        init(context, attributeSet)
    }

    private var centerX: Float = 0f
    private var centerY: Float = 0f
    private var currentValue: Int = 0
    private var minValue: Int = 0
    var maxValue: Int = 0
        set(value) {
            field = value
            postInvalidate()
        }
    var reverse: Boolean = true
        set(value) {
            field = value
            postInvalidate()
        }
    private var first = true
    private var debug = true
    private var itemViewHeight: Float = 0f
    private var itemMinHeight: Float = 0f
    private var itemMaxHeight: Float = 0f
    private var itemMinWidth: Float = 0f
    private var itemMaxWidth: Float = 0f
    private var spaceHeight: Float = 0f
    private var referLineY: Float = 0f

    private var downX: Float = 0f
    private var downY: Float = 0f
    private var offsetY: Float = 0f
    private var actionDownTime = System.currentTimeMillis()
    private var flingJob: Job? = null

//    private var dispatchDownX: Float = 0f
//    private var dispatchDownY: Float = 0f

    private var fastScrollTopArea = RectF(0f, 0f, 0f, 0f)
    private var fastScrollBottomArea = RectF(0f, 0f, 0f, 0f)
    private var fastScrollAreaHeight = 0f


    @Volatile
    private var inOperation: Boolean = false
    private var enableFling = false

    //private var gestureDetector: GestureDetector? = null
    //private var scroller: OverScroller? = null
    // private var velocity: Float = 0f

    private val paint = Paint().apply {
        this.style = Paint.Style.FILL
        this.isAntiAlias = true
        this.strokeCap = Paint.Cap.ROUND
    }
    var onValueChange: ((value: Int, max: Int, isActive: Boolean, isMoving: Boolean) -> Unit)? = null
    //var onValueChangeLastTime:((value: Int, max: Int) -> Unit)? = null //when touch event.up/cancel call the method

    private fun init(context: Context, attrs: AttributeSet?) {
        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.VerticalScaleView)
        maxValue = typeArray.getInt(R.styleable.VerticalScaleView_vsv_max, MAX_VALUE_DEFAULT)
        debug = typeArray.getBoolean(R.styleable.VerticalScaleView_vsv_debug, false)
        currentValue = typeArray.getInt(R.styleable.VerticalScaleView_vsv_value, minValue)
        typeArray.recycle()
        spaceHeight = 10f.dp2px(context)
        itemViewHeight = 19f.dp2px(context)
        fastScrollAreaHeight = 60f.dp2px(this.context)
        if (debug) {
            Log.i(TAG, "init value=${currentValue} max=${maxValue} min=${minValue}")
        }

    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        initData()
    }


    private fun initData() {
        centerX = width / 2f
        centerY = height / 2f
        itemMinHeight = itemViewHeight * 0.30f//0.4f
        itemMaxHeight = itemViewHeight
        itemMinWidth = width * 0.4f
        itemMaxWidth = width * 0.85f
        referLineY = centerY - itemMaxHeight * 1.5f  //
        fastScrollTopArea = RectF(0f, 0f, width.toFloat(), fastScrollAreaHeight)
        fastScrollBottomArea = RectF(0f, height - fastScrollAreaHeight, width.toFloat(), height.toFloat())
    }

    fun setValue(value: Int) {
        post {
            val realValue = if (reverse) {
                maxValue - value
            } else value
            this.currentValue = realValue.coerceIn(minValue, maxValue)
            if (!inOperation) {
                val newOffsetY = valueToOffsetY(this.currentValue)
                offsetY = -newOffsetY
            }
            notifyOnValueChange(false, false)
            //Logger.i(TAG, "setValue($value) offsetY=${offsetY}")
            invalidate()
        }
    }

    private fun notifyOnValueChange(isActive: Boolean, isMoving: Boolean) {
        onValueChange?.invoke(if (reverse) maxValue - currentValue else currentValue, maxValue, isActive, isMoving)
    }


    fun getValue(): Int = this.currentValue

    /**
     * value trans to scroll`s offsetY
     */
    private fun valueToOffsetY(value: Int): Float {
        var startY = referLineY
        var totalOffsetY = 0f
        //**滑动过程中总宽高，item宽高都是变化的
        for (index in 1..value) {
            val drawY = startY + itemMaxHeight
            val distanceToRefreY = abs(drawY - referLineY)
            val pair = caculatorItemWidthHeight(distanceToRefreY, isTopRegion = true)
            startY += (pair.second + spaceHeight)
            totalOffsetY += (pair.second + spaceHeight)
            //Log.i(TAG,"valueToOffsetY itemmaxHeight=${itemMaxHeight} realHeight=${pair.second} idx=$index distanceToRefreY=$distanceToRefreY")
        }
        //Logger.i(TAG, "valueToOffsetY=${totalOffsetY} value=${value}")
        return totalOffsetY
    }

    private fun offsetYToValue(offsetY: Float): Int {
        var value = 0
        do {
            if (-valueToOffsetY(value) > offsetY) {
                value++
            } else {
                break
            }
        } while (value < maxValue)

        //Logger.i(TAG, "offsetYToValue($offsetY) value=${value}")
        return value
    }


    private fun startFlingIfAvailable(downy: Float, upy: Float) {
        if (!enableFling) {
            return
        }
        val distanceY = upy - downy
        val time = System.currentTimeMillis() - actionDownTime
        val vY = distanceY / time
        val velocity = vY * 1000L
        if (abs(velocity) > 1500 && currentValue != minValue && currentValue != maxValue) {
            flingJob?.cancel()
            flingJob = launch {
                val animy = velocity / 10
                var t = 1
                while (isActive) {
                    delay(50)
                    t += 2
                    val newOffsetY = animy / t
                    offsetY += (newOffsetY.toInt())
                    <EMAIL> = offsetYToValue(offsetY)
                    if (<EMAIL> != lastValue) {
                        ViewUtils.doPerformHapticFeedback(this@VerticalScaleView)
                    }
                    lastValue = <EMAIL>
                    setValue(<EMAIL>)
                    notifyOnValueChange(true, true)
                    invalidate()
                    if (abs(newOffsetY.toInt()) <= 8) {
                        cancel()
                        break
                    }
//                    Logger.i(TAG,"fling=${animy} newOffsetY=${newOffsetY} currentValue=${<EMAIL>}")
                }
            }
        } else {
            setValue(this.currentValue)
            notifyOnValueChange(true, false)
            invalidate()
        }
    }

    private var lastY: Float = 0f
    private var lastValue: Int = 0

    private var startStayCheckJob: Job? = null

    private var inTopAreaStartTime = 0L
    private var inTopArea: Boolean = false

    private var inBottomAreaStartTime = 0L
    private var inBottomArea: Boolean = false


    private fun startStayCheck() {
        startStayCheckJob?.cancel()
        startStayCheckJob = launch {
            val delayTime = 16L
            val step = 30
            while (isActive) {
                delay(delayTime)
                if (inTopArea) {
                    inTopAreaStartTime += delayTime
                    if (inTopAreaStartTime > 1000) {
                        if (currentValue < maxValue) {
                            stayScroll(-step)
                        }
                    }
                } else if (inBottomArea) {
                    inBottomAreaStartTime += delayTime
                    if (inBottomAreaStartTime > 1000) {
                        if (currentValue > minValue) {
                            stayScroll(step)
                        }
                    }
                }

            }
        }
    }

    private fun stayScroll(moveY: Int) {
        val newValue = offsetYToValue(offsetY + moveY)
        if (newValue in minValue..maxValue) {
            offsetY += moveY
        }
        currentValue = newValue.coerceIn(minValue, maxValue)
        if (currentValue != lastValue) {
            ViewUtils.doPerformHapticFeedback(this)
        }
        lastValue = currentValue
        notifyOnValueChange(true, true)
        invalidate()
    }

    private fun cancelStayCheck() {
        startStayCheckJob?.cancel()
        inTopArea = false
        inTopAreaStartTime = 0L
        inBottomArea = false
        inBottomAreaStartTime = 0L
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                flingJob?.cancel()
                inOperation = true
                lastY = event.y
                downX = x
                downY = y
                actionDownTime = System.currentTimeMillis()
                cancelStayCheck()
                startStayCheck()
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                inOperation = true
                val moveY = y - lastY
                if (fastScrollTopArea.contains(x, y)) {
                    inTopArea = true
                    return true
                } else {
                    inTopArea = false
                }
                if (fastScrollBottomArea.contains(x, y)) {
                    inBottomArea = true
                    return true
                } else {
                    inBottomArea = false
                }

                val newValue = this.offsetYToValue(offsetY + moveY)
                if (lastValue == newValue && (lastValue == minValue || lastValue == maxValue)) {
                    return true
                }
                if (newValue in minValue..maxValue) {
                    offsetY += moveY
                }
                this.currentValue = newValue.coerceIn(minValue, maxValue)
                if (this.currentValue != lastValue) {
                    ViewUtils.doPerformHapticFeedback(this)
                }
                lastValue = this.currentValue
                lastY = event.y
                notifyOnValueChange(true, true)
                invalidate()
                return true
            }

            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_CANCEL -> {
                inOperation = false
                startFlingIfAvailable(downy = downY, upy = y)
                lastY = 0f
                downX = 0f
                downY = 0f
                cancelStayCheck()
                notifyOnValueChange(true, false)
                return true
            }
        }
        return super.onTouchEvent(event)
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawNewItems(canvas)
        //debug
        if (debug) {
            paint.color = Color.GREEN
            paint.style = Paint.Style.STROKE
            canvas.drawRect(0f, 0f + scrollY, width.toFloat(), scrollY + height.toFloat(), paint)
            canvas.drawRect(fastScrollTopArea, paint)
            canvas.drawRect(fastScrollBottomArea, paint)
            paint.color = Color.GREEN
            paint.style = Paint.Style.FILL
            paint.strokeWidth = 4f.dp2px(context)
            canvas.drawPoint(centerX, referLineY, paint)
            paint.textAlign = Paint.Align.LEFT
            canvas.drawText(
                "${centerY.toInt()},${currentValue}",
                centerX + paint.strokeWidth,
                centerY + scrollY,
                paint
            )
            paint.color = Color.RED
            paint.strokeWidth = 2f
            canvas.drawLine(0f, referLineY, width.toFloat(), referLineY, paint)
        }
    }

    private fun caculatorItemWidthHeight(
        distanceToRefreY: Float,
        isTopRegion: Boolean
    ): Pair<Float, Float> {
        val widthScale = if (isTopRegion) {
            1.6f
        } else {
            1.5f
        }
        val heightScale = if (isTopRegion) {
            0.15f
        } else {
            0.1f
        }
        val itemW =
            (itemMaxWidth - distanceToRefreY * widthScale).coerceIn(itemMinWidth, itemMaxWidth)
        val itemH = (itemMaxHeight - (distanceToRefreY * heightScale)).coerceIn(
            itemMinHeight,
            itemMaxHeight
        )
        return itemW to itemH
    }

    private fun drawNewItems(canvas: Canvas) {
        val maxItemCount = maxValue
        var startY = referLineY
        val itemColor = Color.WHITE
        paint.color = itemColor
        paint.isAntiAlias = true
        paint.style = Paint.Style.FILL
        for (index in 0..maxItemCount) {
            val drawY = startY + offsetY
            val isTopRegion = drawY - referLineY < 0
            val distanceToReferLineY = abs(drawY - referLineY)
            val widthHeightPair = caculatorItemWidthHeight(distanceToReferLineY, isTopRegion)
            val itemW = widthHeightPair.first
            val itemH = widthHeightPair.second
            val itemY = drawY
            val itemX = (width - itemW) / 2
            //alpha
            val alpha255 = (distanceToReferLineY / (height / 2f) * 255F).toInt()
            val realAlpha = if (isTopRegion) {
                (280 - alpha255).coerceIn(0, 255)
            } else {
                (340 - alpha255).coerceIn(0, 255)
            }
            paint.color = Color.argb(
                realAlpha,
                itemColor.red,
                itemColor.green,
                itemColor.blue
            )
            //float left, float top, float right, float bottom
            if (itemY >= 0 && itemY <= height) {
                canvas.drawRoundRect(
                    itemX, itemY, itemX + itemW, itemY + itemH, itemH, itemH,
                    paint
                )
            }

//            if (debug) {
//                paint.color = Color.RED
//                paint.textSize = 12f.dp2px(context)
//                canvas.drawText("${index}", itemX, itemY + itemH, paint)
//            }
            startY += (itemH + spaceHeight)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cancelStayCheck()
        flingJob?.cancel()
        viewJob.cancel()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
    }


}