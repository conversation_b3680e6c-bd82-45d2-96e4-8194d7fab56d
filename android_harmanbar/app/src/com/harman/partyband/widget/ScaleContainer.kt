package com.harman.partyband.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import com.harman.bar.app.R

/**
 * @Description A container that supports zooming of subviews can have only one child view
 * <AUTHOR>
 * @Time 2025/3/21
 */
class ScaleContainer : ViewGroup {
    private var scaleRatio = 1f


    constructor(context: Context) : super(context) {
        init(null, 0)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle) {
        init(attrs, defStyle)
    }

    private fun init(attrs: AttributeSet?, defStyle: Int) {
        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.ScaleContainer)
        scaleRatio = typeArray.getFloat(R.styleable.ScaleContainer_sc_scale_factor, 1f)
        typeArray.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val child = getChildAt(0)
        measureChild(child, widthMeasureSpec, heightMeasureSpec)
        val measuredWidth = (child.measuredWidth * scaleRatio).toInt()
        val measuredHeight = (child.measuredHeight * scaleRatio).toInt()
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        val child = getChildAt(0)
        val childWidth = child.measuredWidth
        val childHeight = child.measuredHeight
        child.layout(0, 0, childWidth, childHeight)
        child.pivotX = 0f
        child.pivotY = 0f
        child.scaleX = scaleRatio
        child.scaleY = scaleRatio
    }
}