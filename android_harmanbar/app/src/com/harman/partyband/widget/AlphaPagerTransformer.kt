package com.harman.partyband.widget

import android.view.View
import androidx.viewpager2.widget.ViewPager2

/**
 * @Description
 * <AUTHOR>
 * @Time 2025/4/9
 */
class AlphaPagerTransformer(private val minAlpha: Float = 0.5f) : ViewPager2.PageTransformer {
    override fun transformPage(page: View, position: Float) {
        if (position < -1 || position > 1) {
            page.alpha = minAlpha
        } else {
            if (position < 0) {//[0,-1]
                page.alpha = minAlpha + (1 + position) * (1 - minAlpha)
            } else {//[1,0]
                page.alpha = minAlpha + (1 - position) * (1 - minAlpha)
            }
        }
    }
}