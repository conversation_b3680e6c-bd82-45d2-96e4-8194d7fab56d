package com.harman.partyband.looper

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.harman.BaseVBViewHolder
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ItemLooperBarsLayoutBinding
import com.harman.partylight.util.generateSimpleDiffer
import kotlinx.coroutines.flow.StateFlow

class LooperBarsAdapter(val context: Context): RecyclerView.Adapter<BaseVBViewHolder<ItemLooperBarsLayoutBinding>>() {
    private val differ = generateSimpleDiffer<LooperBars>()
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseVBViewHolder<ItemLooperBarsLayoutBinding> {
        return BaseVBViewHolder(ItemLooperBarsLayoutBinding.inflate(LayoutInflater.from(parent.context)))
    }

    fun refresh(list: List<LooperBars>){
        differ.submitList(list)
    }

    override fun getItemCount() = differ.currentList.size

    override fun onBindViewHolder(
        holder: BaseVBViewHolder<ItemLooperBarsLayoutBinding>,
        position: Int
    ) {
        val looperBars = differ.currentList[position]
        with(holder.binding.tvValue){
            text = looperBars.value.toString()
            isSelected = looperBars.isSelected
            isEnabled = looperBars.isEnable
        }

        holder.binding.tvValue.setTextColor(if (looperBars.isEnable){
            holder.binding.tvValue.setOnClickListener{
                looperBars.onClick?.invoke(looperBars.value)
            }
            if (looperBars.isSelected)
                context.getColor(R.color.fg_inverse)
            else{
                context.getColor(R.color.fg_secondary)
            }
        }else{
            context.getColor(R.color.bg_opacity_10)
        })
    }
}