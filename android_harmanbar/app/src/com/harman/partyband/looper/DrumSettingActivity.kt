package com.harman.partyband.looper

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.distinctUntilChanged
import androidx.recyclerview.widget.RecyclerView
import com.harman.BaseVBViewHolder
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityDrumSettingBinding
import com.harman.bar.app.databinding.LayoutItemWithCheckboxBinding
import com.harman.partyband.drummetronome.DrumTimeSignatureItemState
import com.harman.partyband.drummetronome.getDrumStyleName
import com.harman.partylight.util.generateSimpleDiffer
import com.harman.partylight.util.popResult
import com.harman.partylight.util.syncPush
import com.harman.v5protocol.bean.devinfofeat.V5DrumSequence
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeSequence
import com.harman.widget.AppCompatBaseActivity

class DrumSettingActivity : AppCompatBaseActivity(), View.OnClickListener {
    private val binding by lazy { ActivityDrumSettingBinding.inflate(layoutInflater) }
    private val viewModel by viewModels<DrumSettingViewModel>()

    private val rbList by lazy {
        listOf(
            binding.rbBeat44.apply {
                text =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B44.beatName else V5MetronomeSequence.Beat.B44.beatName
                tag =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B44 else V5MetronomeSequence.Beat.B44
            },
            binding.rbBeat43.apply {
                text =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B43.beatName else V5MetronomeSequence.Beat.B34.beatName
                tag =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B43 else V5MetronomeSequence.Beat.B34
            },
            binding.rbBeat42.apply {
                text =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B42.beatName else V5MetronomeSequence.Beat.B24.beatName
                tag =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B42 else V5MetronomeSequence.Beat.B24
            },
            binding.rbBeat68.apply {
                text =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B68.beatName else V5MetronomeSequence.Beat.B68.beatName
                tag =
                    if (viewModel.isDrum) V5DrumSequence.Beat.B68 else V5MetronomeSequence.Beat.B68
            },
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initView()
        observeModel()
    }

    private fun initView() {
//        binding.tvBpm.text = viewModel.bpm.value?.toString()
        binding.ivLeading.setOnClickListener(this)
        binding.tvAdd.setOnClickListener(this)
        binding.ivSub.setOnClickListener(this)
        binding.ivAdd.setOnClickListener(this)


        rbList.forEach {
            it.setOnClickListener { _ ->
                if (viewModel.isDrum)
                    viewModel.switchBeat(it.tag as V5DrumSequence.Beat)
                else
                    viewModel.switchMetronomeBeat(it.tag as V5MetronomeSequence.Beat)
            }
        }
        binding.rvTypes.adapter = DrumTimeSignatureAdapter(viewModel, this)

        binding.tvRhythmTitle.text = if (viewModel.isDrum){
            binding.tvTitle.text = getString(R.string.drum_setting)
            getString(R.string.styles)
        }else{
            binding.tvTitle.text = getString(R.string.metronome_settings)
            binding.tvTrackSetting.visibility = View.GONE
            binding.tvMetronomeTips.visibility = View.VISIBLE
            binding.rvTypes.visibility = View.GONE
            getString(R.string.time_signature)
        }
    }

    private fun observeModel() {
        viewModel.currentBeat.observe(this@DrumSettingActivity) { beat ->
            beat?.let {
                updateBeatView(it)
            }
        }

        viewModel.currentMetronomeBeat.observe(this) { beat ->
            updateBeatView(beat)
        }

        viewModel.bpm.distinctUntilChanged().observe(this) {
            binding.tvBpm.text = it.toString()
        }

    }

    private fun updateBeatView(beat: Any) {
        rbList.forEach { rb ->
            rb.setTextColor(getColor(R.color.fg_primary))
            rb.setBackgroundResource(R.drawable.radius_round_bg_card)
        }
        val targetRb = when (beat) {
            V5DrumSequence.Beat.B44 -> binding.rbBeat44
            V5DrumSequence.Beat.B43 -> binding.rbBeat43
            V5DrumSequence.Beat.B42 -> binding.rbBeat42
            V5DrumSequence.Beat.B68 -> binding.rbBeat68

            V5MetronomeSequence.Beat.B44 -> binding.rbBeat44
            V5MetronomeSequence.Beat.B34 -> binding.rbBeat43
            V5MetronomeSequence.Beat.B24 -> binding.rbBeat42
            V5MetronomeSequence.Beat.B68 -> binding.rbBeat68

            else -> null
        }
        targetRb?.setTextColor(getColor(R.color.fg_inverse))
        targetRb?.setBackgroundResource(R.drawable.radius_round_fg_primary)
    }

    companion object {
        suspend fun launch(
            context: Context,
            uuid: String,
            type: V5DrumSequence.Type?,
            isDrum: Boolean
        ): LooperDrumData? {
            return context.syncPush<DrumSettingActivity, LooperDrumData>(
                bundleOf(
                    "uuid" to uuid,
                    "type" to type,
                    "idDrum" to isDrum
                )
            )
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.ivLeading -> finish()
            R.id.tv_add -> {
                this.popResult(LooperDrumData(viewModel.bpm.value,viewModel.currentType,viewModel.currentMetronomeBeat.value))
                finish()
            }

            R.id.ivSub -> {
                viewModel.subBpm()
            }

            R.id.ivAdd -> {
                viewModel.addBpm()
            }

        }
    }
}

private class DrumTimeSignatureAdapter(
    val viewModel: DrumSettingViewModel,
    lifeOwner: LifecycleOwner,
) : RecyclerView.Adapter<BaseVBViewHolder<LayoutItemWithCheckboxBinding>>() {
    private val differ = generateSimpleDiffer<DrumTimeSignatureItemState>()

    init {
        viewModel.currentBeatTypeStateList.observe(lifeOwner) {
            differ.submitList(it)
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseVBViewHolder<LayoutItemWithCheckboxBinding> {
        return BaseVBViewHolder(
            LayoutItemWithCheckboxBinding.bind(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.layout_item_with_checkbox, parent, false)
            )
        )
    }

    override fun getItemCount(): Int = differ.currentList.size

    override fun onBindViewHolder(
        holder: BaseVBViewHolder<LayoutItemWithCheckboxBinding>,
        position: Int
    ) {
        val item = differ.currentList[position]
        val binding = holder.binding
        binding.rbSelect.apply {
            if (item.checked) {
                setImageResource(R.drawable.svg_icon_security_selected)
            } else {
                setImageResource(R.drawable.svg_icon_security_unselected)
            }
        }
        binding.tvContent.apply {
            text = getDrumStyleName(context, item.type)
            if (item.checked) {
                setTextColor(context.getColor(R.color.fg_activate))
            } else {
                setTextColor(context.getColor(R.color.fg_primary))
            }
        }
        binding.root.setOnClickListener {
            viewModel.switchType(item.type)
        }
    }
}