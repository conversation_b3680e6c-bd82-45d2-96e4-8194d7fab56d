package com.harman.partyband.looper

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.partyband.widget.GuitarLooperAudioWaveView
import com.harman.partyband.widget.GuitarLooperCircleButton
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.log.Logger
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5AudioDataTrimmingFeature
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5DrumSequence
import com.harman.v5protocol.bean.devinfofeat.V5DrumStartEnd
import com.harman.v5protocol.bean.devinfofeat.V5LooperMusicSignalFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperMusicSignalReportIntervalFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperNumberOfBarsFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperRecordPlayFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperRecordPlayJumpFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperRecordTimeLengthFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperRecordingStartStopFeature
import com.harman.v5protocol.bean.devinfofeat.V5LooperRecordingStartStopFeature.LooperRecordingStartStopFeature
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.math.roundToInt
import kotlin.random.Random
import kotlin.random.nextInt

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.looper
 * @ClassName: PartBandGuitarLooperViewModel
 * @Description:
 * @Author: mixie
 * @CreateDate: 2024/12/26
 * @UpdateUser:
 * @UpdateDate: 2024/12/26
 * @UpdateRemark:
 * @Version: 1.0
 */
class PartBandGuitarLooperViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {
    companion object {
        const val TAG = "PartBandGuitarLooperViewModel"
        const val MAX_RECORD_TIME = 60000//
    }

    val device = DeviceStore.find(savedStateHandle.get<String>("uuid")!!) as? PartyBandDevice

    private val deviceListener = object : IV5GattListener {
        //commandid=0x0003 in Child thread
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            devInfoMap[V5DevInfoFeatID.LooperMusicSignalFeature]?.run {
                val musicSignalFeature = this as V5LooperMusicSignalFeature //0x3142
                val totalValueSize = _audioWaveValueList.value.size
                testLog("notify=${musicSignalFeature.signal} totalValueSize=${totalValueSize}")
                _withSound.value = true
                if (_audioWaveStatus.value == GuitarLooperAudioWaveView.AudioWaveStatus.Recording
                    || _audioWaveStatus.value == GuitarLooperAudioWaveView.AudioWaveStatus.Overdub) {
                    val totalTime = getRecordingTime()
                    if (totalTime != _recordMaxTime.value) {
                        val value = Random.nextInt(IntRange(10, 100)) / 100f
                        val newList = listOf(value) + _audioWaveValueList.value
                        _audioWaveValueList.value = newList
                    }
                    _recordMaxTime.value = totalTime
                }
            }
            (devInfoMap[V5DevInfoFeatID.DrumSequence] as? V5DrumSequence)?.also {
                currentDrumType.postValue(it.type)
            }

            (devInfoMap[V5DevInfoFeatID.DrumStartEnd] as? V5DrumStartEnd)?.also {
                isPlaying.postValue(it.start)
            }

            (devInfoMap[V5DevInfoFeatID.LooperNumberOfBars] as? V5LooperNumberOfBarsFeature)?.also {
                _currentBar.value = it.number
            }

        }
    }

    private var recordAutoFinish: Boolean = false//when record>=60s
    private var startRecordTime = System.currentTimeMillis()
    private var countDownJob: Job? = null

    private val _audioWaveValueList = MutableStateFlow<List<Float>>(emptyList())
    private val _loading = MutableStateFlow(false)
    private val _audioPlayProgress = MutableStateFlow(0)
    private val _recordMaxTime = MutableStateFlow(0)
    private val _trimData = MutableStateFlow(TrimData(0, 0))
    private val _audioWaveStatus =
        MutableStateFlow(GuitarLooperAudioWaveView.AudioWaveStatus.Ready)
    private val _butType = MutableStateFlow(GuitarLooperCircleButton.Type.READY)
    private val _withSound = MutableStateFlow(false)


    private val whileSubscribed = SharingStarted.WhileSubscribed(5_000)
    private var playTrimingAudioProgressJob: Job? = null

    val currentDrumType = MutableLiveData<V5DrumSequence.Type>()
    val isPlaying = MutableLiveData(false)
    val undoOrDiscardAudio = MutableLiveData<Int>()
    val currentAudioLayer = MutableStateFlow(0)

    private val _barNumberList = MutableStateFlow<List<LooperBars>>(listOf())
    val barNumberList = _barNumberList.asStateFlow()
    private val _currentBar = MutableStateFlow(1)
    val currentBar = _currentBar.asStateFlow()
    private val _drumValue = MutableStateFlow(1 to 1)
    val drumValue = _drumValue.asStateFlow()
    private val _drumBpm = MutableStateFlow(1)
    val drumBpm = _drumBpm.asStateFlow()
    private var playDrumProgressJob: Job? = null
    private val _drumType = MutableStateFlow(PartBandGuitarLooperViewModel.DrumType.FREEHAND)
    val drumType = _drumType.asStateFlow()
    val isCountDown = MutableStateFlow(false)

    val screenData: StateFlow<ScreenData> = combine(
        _loading,
        _recordMaxTime,
        _trimData,
        _audioWaveStatus,
        _butType,
        _audioPlayProgress,
        _audioWaveValueList,
        _withSound,
        _currentBar
    ) { loading,
        maxTime,
        trimData,
        audioWaveStatus,
        butType,
        audioPlayProgress,
        audioWaveValueList,
        withSound ,
        barValue ->
        ScreenData.Success(
            loading = loading,
            recordMaxTime = maxTime,
            trimData = trimData,
            butType = butType,
            audioWaveStatus = audioWaveStatus,
            audioPlayProgress = audioPlayProgress,
            audioWaveValueList = audioWaveValueList,
            withSound = withSound,
            barValue = barValue

        )
    }.stateIn(viewModelScope, whileSubscribed, ScreenData.Loading)


    init {
        device?.registerDeviceListener(deviceListener)
        device?.asyncGetDevInfoFeat(V5DevInfoFeatID.DrumSequence,V5DevInfoFeatID.DrumTempo,V5DevInfoFeatID.DrumStartEnd,V5DevInfoFeatID.LooperNumberOfBars)
        viewModelScope.launch {
            delay(500)
            _withSound.value = true//fake data
        }
    }

    private fun testLog(message: String) {
        Logger.i(TAG, message)
    }

    fun reset() {
        currentAudioLayer.value = 0
        _audioWaveValueList.value = emptyList()
        _butType.value = GuitarLooperCircleButton.Type.READY
        _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Ready
        _loading.value = false
        _recordMaxTime.value = 0
        _trimData.value = TrimData(0, 0)
        cancelAudioPlayProgress()
        _audioPlayProgress.value = 0
        _withSound.value = false
        _currentBar.value = 1
        _drumType.value = DrumType.FREEHAND
        viewModelScope.launch {
            delay(500)
            _withSound.value = true
        }
    }

    private fun setReportInterval(time: Int) {
        viewModelScope.launch {
            val devInfoFeat = device?.setDevInfoFeat(V5LooperMusicSignalReportIntervalFeature(time))
            devInfoFeat?.isSuccess()
        }
    }

    private fun startRecord(block: ((isSuccess: Boolean) -> Unit)? = null){
        viewModelScope.launch {
            val devInfoFeat =
                device?.setDevInfoFeat(V5LooperRecordingStartStopFeature(_drumType.value.type))
            devInfoFeat?.takeIf {
                it.isSuccess()
            }?.run {
                currentAudioLayer.value ++
                block?.invoke(isSuccess())
            }

        }
    }

    private fun startFreeHandRecord() {
        startRecord{
            _butType.value = GuitarLooperCircleButton.Type.RECORDING
            _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Recording
            startRecordTime = System.currentTimeMillis()
            recordAutoFinish = false
            recordCountDown()
        }
//        viewModelScope.launch {
//            val status =
//                device?.setDevInfoFeat(V5LooperRecordingStartStopFeature(V5LooperRecordingStartStopFeature.LooperRecordingStartStopFeature.Start1st))
//            if (status?.isSuccess() == true) {
//                currentAudioLayer.value ++
//                _butType.value = GuitarLooperCircleButton.Type.RECORDING
//                _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Recording
//                startRecordTime = System.currentTimeMillis()
//                recordAutoFinish = false
//                recordCountDown()
//            }
//            testLog("startRecord isSuccess=${status?.isSuccess()}")
//        }
    }


    private fun stopRecord() {
        viewModelScope.launch {
            val status =
                device?.setDevInfoFeat(V5LooperRecordingStartStopFeature(V5LooperRecordingStartStopFeature.LooperRecordingStartStopFeature.Stop))
            if (status?.isSuccess() == true) {
                countDownJob?.cancel()
                device?.getDevInfoFeat<V5LooperRecordTimeLengthFeature>()?.apply {
                    var time = 0
                    if (_drumType.value == DrumType.FREEHAND) {
                        Logger.i(TAG, "stopRecord timeLength=${this.timeLength}")
                        val totalCount = (this.timeLength / 1000f).roundToInt()
                        if (_audioWaveValueList.value.size < totalCount) {//fakedata
                            val subcount = totalCount - _audioWaveValueList.value.size
                            val compensationList: MutableList<Float> = mutableListOf()
                            repeat(subcount + 1) {
                                val value = Random.nextInt(IntRange(10, 100)) / 100f
                                compensationList.add(value)
                            }
                            _audioWaveValueList.value = compensationList + _audioWaveValueList.value
                        }
                        time = this.timeLength
                        if (recordAutoFinish && this.timeLength < MAX_RECORD_TIME) {
                            time = MAX_RECORD_TIME
                        }
                    }else{
                        time = getDrumMaxTime(_currentBar.value)
                    }
                    changeToRecordingFinish(time)
                }
            }
            testLog("stopRecord isSuccess=${status?.isSuccess()}")
        }
    }

    private fun getRecordingTime(): Int {//ms
        val time = System.currentTimeMillis() - startRecordTime
        return time.toInt()
    }

    private fun recordCountDown() {
        countDownJob?.cancel()
        countDownJob = viewModelScope.launch {
            delay(61_000)
            if (!recordAutoFinish) {
                recordAutoFinish = true
                handEvent(Event.StopRecord)
            }
        }
    }

    private fun changeToRecordingFinish(time: Int) {
        _recordMaxTime.value = time
        _trimData.value = TrimData(0, _recordMaxTime.value)
        _butType.value = GuitarLooperCircleButton.Type.OVERDUB
        _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Triming
        playTrimAudio()
    }

    private fun trimmingAudio(startTime: Int, endTime: Int) {
        viewModelScope.launch {
            cancelAudioPlayProgress()
            val status =
                device?.setDevInfoFeat(V5AudioDataTrimmingFeature(startTime * 1000, endTime * 1000))
            if (status?.isSuccess() == true) {
                _trimData.value = TrimData(startTime = startTime, endTime = endTime)
                _audioPlayProgress.value = 0
                handEvent(Event.PlayAudio)
            }
            testLog("trimmingAudio =[$startTime,$endTime] status=${status} playprogress=${_audioPlayProgress.value}")
        }
    }

    private fun playTrimAudio() {
        viewModelScope.launch {
            val retStatus =
                device?.setDevInfoFeat(V5LooperRecordPlayFeature(V5LooperRecordPlayFeature.LooperRecordPlayStatus.Start))
            if (retStatus?.isSuccess() == true) {
                _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Playing
//                _butType.value = GuitarLooperCircleButton.Type.PAUSE
                audioPlayProgress()
            }
            testLog("playTrimAudio isSuccess=${retStatus?.isSuccess()}")
        }
    }

    private fun pauseTrimAudio() {
        viewModelScope.launch {
            val retStatus =
                device?.setDevInfoFeat(V5LooperRecordPlayFeature(V5LooperRecordPlayFeature.LooperRecordPlayStatus.Pause))
            if (retStatus?.isSuccess() == true) {
                _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Pause
//                _butType.value = GuitarLooperCircleButton.Type.PLAY
                cancelAudioPlayProgress()
            }
            testLog("pauseTrimAudio isSuccess=${retStatus?.isSuccess()}")
        }
    }

    private fun stopTrimAudio() {
        viewModelScope.launch {
            val retStatus =
                device?.setDevInfoFeat(V5LooperRecordPlayFeature(V5LooperRecordPlayFeature.LooperRecordPlayStatus.Stop))
            if (retStatus?.isSuccess() == true) {
                _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Triming
//                _butType.value = GuitarLooperCircleButton.Type.OVERDUB
                cancelAudioPlayProgress()
                _audioPlayProgress.value = 0
            }
            testLog("pauseTrimAudio isSuccess=${retStatus?.isSuccess()}")
        }
    }
    private fun seekAudio(currentPos: Int,sendCommand:Boolean){
        viewModelScope.launch {
            _audioPlayProgress.value=currentPos
            testLog("seekAudio=${currentPos} sendCommand=${sendCommand}")
            if(sendCommand){
                val retStatus=device?.setDevInfoFeat(V5LooperRecordPlayJumpFeature(currentPos))
                if(retStatus?.isSuccess()==true){
                    handEvent(Event.PlayAudio)
                }
                testLog("seekAudio=${retStatus}")
            }
        }
    }

    private fun startOverdub(){
        startRecord {
            _audioWaveValueList.value = emptyList()
            _loading.value = false
            _recordMaxTime.value = 0
            _trimData.value = TrimData(0, 0)
            cancelAudioPlayProgress()
            _audioPlayProgress.value = 0

//            _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Excessive
//            delay(500)
            _butType.value = GuitarLooperCircleButton.Type.RECORDING
            _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Overdub
            startRecordTime = System.currentTimeMillis()
            recordAutoFinish = false
            recordCountDown()
        }
    }

    private fun undoAudio(time: Int, data: List<Float>) {
        viewModelScope.launch {
            val devInfoFeat = device?.setDevInfoFeat(
                V5LooperRecordingStartStopFeature(V5LooperRecordingStartStopFeature.LooperRecordingStartStopFeature.Undo)
            )
            devInfoFeat?.takeIf {
                it.isSuccess()
            }?.run {
                undoOrDiscardAudio.value = 1
                currentAudioLayer.value --
                if (data.isNotEmpty()){
                    _audioWaveValueList.value = data
                }
                _recordMaxTime.value = time
                _trimData.value = TrimData(0, time)
                cancelAudioPlayProgress()
                _audioPlayProgress.value = 0
                _butType.value = GuitarLooperCircleButton.Type.OVERDUB
                _audioWaveStatus.value = if (_drumType.value != DrumType.FREEHAND && currentAudioLayer.value <= 1){
                    startDrum()
                    GuitarLooperAudioWaveView.AudioWaveStatus.DrumPlaying
                }else{
                    GuitarLooperAudioWaveView.AudioWaveStatus.Playing
                }
            }
        }
    }

    private fun disCardAudio(){
        viewModelScope.launch {
            val devInfoFeat = device?.setDevInfoFeat(
                V5LooperRecordingStartStopFeature(V5LooperRecordingStartStopFeature.LooperRecordingStartStopFeature.Discard)
            )
            devInfoFeat?.takeIf {
                it.isSuccess()
            }?.run {
                reset()
                undoOrDiscardAudio.value = 0
            }
        }
    }

    private fun startDrum(){
        cancelAudioPlayProgress()
        _audioPlayProgress.value = 0
        updateBars()
        val duration = getDrumMaxTime(_currentBar.value)
        _recordMaxTime.value = duration
        _audioWaveValueList.value = getDrumRecordData()
        _butType.value = GuitarLooperCircleButton.Type.OVERDUB
        _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.DrumPlaying
        playDrumProgressJob = viewModelScope.launch {
            while (isActive) {
                delay(100)
                _audioPlayProgress.value+=100
                if (_audioPlayProgress.value > duration) {
                    _audioPlayProgress.value = 0
                    if (isCountDown.value) {
                        isCountDown.value = false
                        drumRecord()
                        cancel()
                    }
                }
            }
        }
    }

    private fun countDown(){
        isCountDown.value = true
        _butType.value = GuitarLooperCircleButton.Type.COUNTDOWN
        _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.CountDown
    }

    private fun drumRecord(){
        startRecord {
            _butType.value = GuitarLooperCircleButton.Type.RECORDING
            _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.DrumRecord
            cancelAudioPlayProgress()
            _audioPlayProgress.value = 0
            val duration = getDrumMaxTime(_currentBar.value)
            _recordMaxTime.value = duration
            _audioWaveValueList.value = getDrumRecordData()
            playDrumProgressJob = viewModelScope.launch {
                while (isActive) {
                    delay(100)
                    _audioPlayProgress.value+=100
                    if (_audioPlayProgress.value > duration) {
                        _audioPlayProgress.value = 0
                        stopRecord()
                        cancel()
                    }
                }
            }
        }

    }

    private fun updateBars(){
        _barNumberList.value = LooperBars.bars.map {
            LooperBars(
                value = it,
                isSelected = (it == _currentBar.value),
                isEnable = getDrumMaxTime(it) <= MAX_RECORD_TIME,
                onClick = ::onBarClick
            )
        }
    }



    private fun onBarClick(value: Int){
//        viewModelScope.launch {
//            val status = device?.setDevInfoFeat(V5LooperNumberOfBarsFeature(value))
//            status?.takeIf {
//                it.isSuccess()
//            }?.run {
                _currentBar.value = value
                _barNumberList.update { list ->
                    list.map {
                        it.copy(isSelected = (it.value == value))
                    }
                }
                startDrum()
//            }
//        }
    }

    private fun getDrumMaxTime(barValue: Int): Int{
        return ((60 / _drumBpm.value.toFloat()) *( 4 / _drumValue.value.second.toFloat()) * _drumValue.value.first * barValue * 1000).toInt()
    }

    private fun getDrumRecordData(): List<Float>{
        val count = if (_currentBar.value >16) 128 else if (_currentBar.value > 8) 64 else 32
        return arrayOfNulls<Float>(count).toMutableList().map {
            Random.nextInt(IntRange(10, 100)) / 100f
        }
    }


    private fun cancelAudioPlayProgress() {
        playTrimingAudioProgressJob?.cancel()
        playDrumProgressJob?.cancel()
    }

    /**
     * play audio
     */
    private fun audioPlayProgress() {
        cancelAudioPlayProgress()
        val duration = _trimData.value.endTime - _trimData.value.startTime
        playTrimingAudioProgressJob = viewModelScope.launch {
            while (isActive) {
                delay(100)
                _audioPlayProgress.value+=100
                if (_audioPlayProgress.value > duration) {
                    _audioWaveStatus.value = GuitarLooperAudioWaveView.AudioWaveStatus.Playing
                    _audioPlayProgress.value = 0
                    if (isCountDown.value){
                        isCountDown.value = false
//                        stopTrimAudio()
                        startOverdub()
                        cancel()
                    }
                }
            }
        }
    }

    fun changeCurrentDrumType(type: V5DrumSequence.Type) {
        if (type == currentDrumType.value) {
            return
        }
        currentDrumType.value = type
    }

    fun setDrumValue(value: String){
        kotlin.runCatching {
            _drumValue.value = value.substringBefore("/").toInt() to value.substringAfter("/").toInt()
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun setBpm(bpm: Int){
        _drumBpm.value = bpm
    }

    fun setDrumType(type: DrumType){
        this._drumType.value = type
    }

    fun addAudioLayer(){
        currentAudioLayer.value ++
    }

    fun togglePlayState() {
        val targetValue = !isPlaying.value!!
        val write = V5DrumStartEnd(targetValue)
        isPlaying.value = targetValue
        device?.asyncSetDevInfoFeat(write)
    }


    override fun onCleared() {
        super.onCleared()
        device?.unregisterDeviceListener(deviceListener)
        cancelAudioPlayProgress()
        countDownJob?.cancel()
        LooperRecordManager.instance.saveRecordData(GuitarLooperAudioWaveView.AudioRecordData(_audioWaveValueList.value,_recordMaxTime.value))
    }

    fun handEvent(event: Event) {
        testLog("handEvent(${event})")
        when (event) {
            is Event.InitRecordedAudio -> initAudio(event.time,event.data)
            is Event.AudioReportInterval -> setReportInterval(event.time)
            is Event.StartRecord -> startFreeHandRecord()
            is Event.StopRecord -> stopRecord()
            is Event.PlayAudio -> playTrimAudio()
            is Event.PauseAudio -> pauseTrimAudio()
            is Event.StopAudio -> stopTrimAudio()
            is Event.TrimAudio -> trimmingAudio(
                startTime = event.startTime,
                endTime = event.endTime
            )
            is Event.SeekAudio->{
                seekAudio(event.currentPos,event.sendCommand)
            }
            is Event.OverDub -> {
                startOverdub()
            }
            is Event.UndoAudio -> undoAudio(event.time,event.data)
            is Event.DiscardAudio -> disCardAudio()
            is Event.DrumReading -> startDrum()
            is Event.CountDown -> countDown()
            is Event.DrumRecord -> drumRecord()


            else -> {}
        }
    }

    private fun initAudio(time: Int, data: List<Float>) {
        _audioWaveValueList.value = data
        changeToRecordingFinish(time)
    }


    sealed interface Event {
        data class  InitRecordedAudio(var time: Int,val data: List<Float>): Event
        data class  AudioReportInterval(val time: Int): Event
        data object StartRecord : Event
        data object StopRecord : Event
        data class TrimAudio(val startTime: Int, val endTime: Int) : Event
        data object PlayAudio : Event
        data object PauseAudio : Event
        data object StopAudio : Event
        data object OverDub: Event
        data class SeekAudio(val currentPos:Int,val sendCommand:Boolean):Event
        data class UndoAudio(var time: Int,val data: List<Float>): Event
        data object DiscardAudio: Event
        data object DrumReading: Event
        data object CountDown: Event
        data object DrumRecord: Event
    }

    sealed interface ScreenData {
        data object Loading : ScreenData
        data class Success(
            val loading: Boolean = true,
            val recordMaxTime: Int = 60_000,
            val trimData: TrimData? = null,
            val butType: GuitarLooperCircleButton.Type = GuitarLooperCircleButton.Type.READY,
            val audioWaveStatus: GuitarLooperAudioWaveView.AudioWaveStatus = GuitarLooperAudioWaveView.AudioWaveStatus.Ready,
            val audioPlayProgress: Int = 0,
            val audioWaveValueList: List<Float> = emptyList(),
            val withSound: Boolean = false,
            var barValue: Int = 1
        ) : ScreenData {
            override fun toString(): String {
                return "Success(loading=$loading, recordMaxTime=$recordMaxTime, trimData=$trimData, butType=$butType, audioWaveStatus=$audioWaveStatus, audioPlayProgress=$audioPlayProgress, audioWaveValueList=${audioWaveValueList.size})"
            }
        }
    }

    /**
     * drag to trim audio starttime-endtime
     */
    data class TrimData(val startTime: Int, val endTime: Int)


    enum class DrumType(val type: LooperRecordingStartStopFeature){
        FREEHAND (LooperRecordingStartStopFeature.Start1st),DRUM(LooperRecordingStartStopFeature.Start2nd),METRONOME(LooperRecordingStartStopFeature.Start3rd)
    }

}