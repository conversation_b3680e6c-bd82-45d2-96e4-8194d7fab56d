package com.harman.partyband.looper

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.partyband.drummetronome.DrumMetronomeActivity
import com.harman.partyband.drummetronome.DrumTimeSignatureItemState
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5DrumSequence
import com.harman.v5protocol.bean.devinfofeat.V5DrumTempo
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeSequence
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeTempo
import kotlinx.coroutines.launch

class DrumSettingViewModel(state: SavedStateHandle) : ViewModel() {
    private val device = DeviceStore.find(state.get<String>("uuid")!!) as? PartyBandDevice
    private val deviceListener = object : IV5GattListener {
        //commandid=0x0003 in Child thread
        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
//            (devInfoMap[V5DevInfoFeatID.DrumSequence] as? V5DrumSequence)?.also {
//                viewModelScope.launch {
//                    currentType =it.type
//                    currentBeat.value = currentType?.beat
//                    currentBeatTypeStateList.value = genCurrentBeatTypeStateList()
//                }
//            }
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.DrumTempo] as? V5DrumTempo)?.also {
                    if (isDrum)
                    bpm.value = it.value
                }

                (devInfoMap[V5DevInfoFeatID.MetronomeSequence] as? V5MetronomeSequence)?.also {
                    if (!isDrum)
                    currentMetronomeBeat.value = it.beat
                }

                (devInfoMap[V5DevInfoFeatID.MetronomeTempo] as? V5MetronomeTempo)?.also {
                    if (!isDrum)
                    bpm.value = it.value
                }
            }
        }
    }

    val isDrum = state.get<Boolean>("idDrum")?: true
    var currentType: V5DrumSequence.Type? = state.get<V5DrumSequence.Type>("type")
    val currentBeat = MutableLiveData(state.get<V5DrumSequence.Type>("type")?.beat)
    val currentBeatTypeStateList =
        MutableLiveData<List<DrumTimeSignatureItemState>>(genCurrentBeatTypeStateList())

    val currentMetronomeBeat = MutableLiveData<V5MetronomeSequence.Beat>()

    private fun genCurrentBeatTypeStateList() =
        currentBeat.value?.let {
            V5DrumSequence.beatTypes[it]?.map { beat ->
                DrumTimeSignatureItemState(beat, currentType == beat)
            }
        }

    val bpm = MutableLiveData<Int>(0)


    init {
        device?.registerDeviceListener(deviceListener)
        if (isDrum)
            device?.asyncGetDevInfoFeat(V5DevInfoFeatID.DrumTempo)
        else
            device?.asyncGetDevInfoFeat(V5DevInfoFeatID.MetronomeSequence, V5DevInfoFeatID.MetronomeTempo)

    }

    fun switchBeat(beat: V5DrumSequence.Beat) {
        if (beat == currentBeat.value) {
            return
        }
        currentBeat.value = beat
        currentBeatTypeStateList.value = genCurrentBeatTypeStateList()
    }

    fun switchType(type: V5DrumSequence.Type) {
        if (currentType == type) {
            return
        }
        device?.asyncSetDevInfoFeat(V5DrumSequence(type))
        currentType = type
        currentBeatTypeStateList.value = genCurrentBeatTypeStateList()
    }

    fun switchMetronomeBeat(beat: V5MetronomeSequence.Beat) {
        if (currentMetronomeBeat.value == beat) {
            return
        }
        device?.asyncSetDevInfoFeat(V5MetronomeSequence(beat))
        currentMetronomeBeat.value = beat
    }

    /*************************************/

    fun addBpm() {
        setBpm(bpm.value!! + 1)
    }

    fun subBpm() {
        setBpm(bpm.value!! - 1)
    }

    fun setBpm(value: Int) {
        if (value == bpm.value) {
            return
        }
        val minValue = V5DrumTempo.MIN
        val maxValue = V5DrumTempo.MAX
        val finalValue = value.coerceIn(minValue, maxValue)
        val write = if (isDrum) V5DrumTempo(finalValue) else V5MetronomeTempo(finalValue)
        device?.asyncSetDevInfoFeat(write)
        bpm.value = finalValue
    }

    override fun onCleared() {
        super.onCleared()
        device?.unregisterDeviceListener(deviceListener)
    }

}