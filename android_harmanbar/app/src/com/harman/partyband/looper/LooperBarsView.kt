package com.harman.partyband.looper

import android.content.AttributionSource
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.harman.bar.app.R
import com.harman.bar.app.databinding.LayoutLooperBarsBinding

class LooperBarsView: FrameLayout{
    private lateinit var looperBarsAdapter: LooperBarsAdapter
    private lateinit var binding: LayoutLooperBarsBinding
    private val spanCount = LooperBars.bars.size

    constructor(context: Context): super(context)

    constructor(context: Context,attributeSet: AttributeSet?): super(context,attributeSet){
        binding = LayoutLooperBarsBinding.inflate(LayoutInflater.from(context))
        addView(binding.root)
        initView()
    }


    private fun initView() {
        val gridLayoutManager = GridLayoutManager(context, spanCount)
        binding.rvList.layoutManager = gridLayoutManager
        looperBarsAdapter = LooperBarsAdapter(context)
        binding.rvList.adapter = looperBarsAdapter
    }

    fun setData(list: List<LooperBars>){
        looperBarsAdapter.refresh(list)
    }
}