package com.harman.partyband.looper

import com.harman.partyband.widget.GuitarLooperAudioWaveView
import kotlinx.coroutines.flow.MutableStateFlow

class LooperRecordManager {
    private var recordData: MutableStateFlow<GuitarLooperAudioWaveView.AudioRecordData?> = MutableStateFlow(GuitarLooperAudioWaveView.AudioRecordData())

    companion object {
        @JvmStatic
        val instance: LooperRecordManager by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            LooperRecordManager()
        }
    }

    fun saveRecordData(recordData: GuitarLooperAudioWaveView.AudioRecordData?){
        this.recordData.value = recordData
    }

    fun getRecordData(): GuitarLooperAudioWaveView.AudioRecordData?{
        return recordData.value
    }
}