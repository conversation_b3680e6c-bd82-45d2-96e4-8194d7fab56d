package com.harman.partyband.looper

data class LooperBars(
    var value: Int,
    var isSelected: <PERSON>olean,
    var isEnable: <PERSON>olean,
    var onClick: ((value: Int) -> Unit)? = null
){
    companion object{
        var bars: MutableList<Int> = mutableListOf()
            get() {
            if (field.isEmpty()){
                field = mutableListOf(1,2,4,8,16,32)
            }
            return field
        }
    }
}

