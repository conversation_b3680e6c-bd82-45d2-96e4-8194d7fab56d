package com.harman.partyband.looper

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Typeface
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityPartbandGuitarlooperBinding
import com.harman.partyband.widget.GuitarLooperAudioWaveView
import com.harman.partyband.widget.GuitarLooperCircleButton
import com.harman.partylight.util.push
import com.harman.log.Logger
import com.harman.partyband.drummetronome.getDrumStyleName
import com.harman.widget.AppCompatBaseActivity
import com.wifiaudio.action.tuneIn.TunInListenerImpl
import com.wifiaudio.utils.ClickHelper
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.util.Locale

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.looper
 * @ClassName: GuitarLooperActivity
 * @Description:
 * @Author: mixie
 * @CreateDate: 2024/12/26
 * @UpdateUser:
 * @UpdateDate: 2024/12/26
 * @UpdateRemark:
 * @Version: 1.0
 */
class PartyBandGuitarLooperActivity : AppCompatBaseActivity(), View.OnClickListener {

    companion object {
        private const val TAG = "PartyBandGuitarLooperActivity"
        fun launch(act: Activity, uuid: String) {
            act.push<PartyBandGuitarLooperActivity>(
                bundleOf(
                    "uuid" to uuid,
                )
            )
        }
    }

    private fun testLog(message: String) {
        Logger.i(TAG, message)
    }

    private val binding by lazy { ActivityPartbandGuitarlooperBinding.inflate(layoutInflater) }
    private val vm by viewModels<PartBandGuitarLooperViewModel>()
    private val dialog: PartBandGuitarLooperDialog by lazy {
        PartBandGuitarLooperDialog(this)
    }
    private val partBandGuitarLooperDialog: PartBandGuitarLooperDialog by lazy {
        PartBandGuitarLooperDialog(this)
    }
    private val originalList = MutableStateFlow<List<Float>>(emptyList())
    private var originalTime = 0
    private var currentAudioRecordLayer = 0
    private var isDrum = true
//    private var isDrumModeReading = MutableStateFlow(false)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.setContentView(binding.root)
        this.buildDialog()
        this.buildAppbar()
        this.buildAudioWaveView()
        this.buildCircleButton()
        this.observeModel()
        initBottomController()
//        initRecordAudio()
    }

    private fun initRecordAudio() {
        LooperRecordManager.instance.getRecordData()?.run {
            takeIf {
                data.isNotEmpty() && recordTime > 0
            }?.also {
                vm.handEvent(PartBandGuitarLooperViewModel.Event.InitRecordedAudio(recordTime,data))
            }
        }
    }

    private fun buildDialog() {
        dialog.listener = object : PartBandGuitarLooperDialog.DialogIF {
            override fun onDismiss() {
                dialog.dismiss()
            }

            override fun onConfirm() {
                vm.handEvent(PartBandGuitarLooperViewModel.Event.DiscardAudio)
                dialog.dismiss()
            }



        }

        partBandGuitarLooperDialog.setContent(getString(R.string.looper_undo_remind),
            getString(R.string.UNDO),getString(R.string.jbl_CANCEL))

        partBandGuitarLooperDialog.listener = object: PartBandGuitarLooperDialog.DialogIF{
            override fun onDismiss() {
                binding.viewGuitarlooperAudiowaveView.performClick()
                partBandGuitarLooperDialog.dismiss()
            }

            override fun onConfirm() {
                binding.llUndo.performClick()
                partBandGuitarLooperDialog.dismiss()
            }

        }
    }

    private fun buildAppbar() {
        binding.appbar.tvTitle.text = getString(R.string.title_guitar_looper)
        binding.appbar.ivAction.setImageResource(R.drawable.ic_trashcan_delete)
        binding.appbar.ivLeading.setOnClickListener {
            binding.viewGuitarlooperAudiowaveView.getRecordData()?.let {
                showUndoDialog()
            } ?: finish()

        }
        binding.appbar.ivAction.setOnClickListener {
            if (dialog.isShowing) {
                dialog.dismiss()
            }
            dialog.show()
        }
        binding.appbar.ivAction.visibility = View.INVISIBLE
//        vm.handEvent(PartBandGuitarLooperViewModel.Event.AudioReportInterval(600))
        binding.tvAdd.setOnClickListener(this)
        binding.btnDrum.setOnClickListener(this)
        binding.btnMetronome.setOnClickListener(this)
    }

    private fun showUndoDialog() {
        if (partBandGuitarLooperDialog.isShowing){
            partBandGuitarLooperDialog.dismiss()
        }
        partBandGuitarLooperDialog.show()

    }

    private fun buildCircleButton() {
        binding.btGuitarloopercircleButton.setOnClickListener {
            if(ClickHelper.isFastClick()){
                return@setOnClickListener
            }

//            if (binding.viewGuitarlooperAudiowaveView.currentStatus() == GuitarLooperAudioWaveView.AudioWaveStatus.DrumPlaying){
//                binding.viewGuitarlooperAudiowaveView.setOriginalValue(
//                    this.originalList.value,
//                    originalTime
//                )
//                vm.handEvent(PartBandGuitarLooperViewModel.Event.CountDown)
//                return@setOnClickListener
//            }

            val butType = binding.btGuitarloopercircleButton.getType()
            testLog("click circle but=${butType}")
            when (butType) {
                GuitarLooperCircleButton.Type.READY -> {
                    vm.handEvent(PartBandGuitarLooperViewModel.Event.StartRecord)
                }

                GuitarLooperCircleButton.Type.RECORDING -> {
                    vm.handEvent(PartBandGuitarLooperViewModel.Event.StopRecord)
                }
                GuitarLooperCircleButton.Type.OVERDUB -> {
                    binding.viewGuitarlooperAudiowaveView.setOriginalValue(
                        this.originalList.value,
                        originalTime
                    )
                    if (!vm.isCountDown.value)
                        vm.handEvent(PartBandGuitarLooperViewModel.Event.CountDown)
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.StopAudio)
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.OverDub)
                }

                else -> {
                }
            }

        }
    }

    private fun buildAudioWaveView() {
        binding.viewGuitarlooperAudiowaveView.onAudioTrimStatusChange = { startTime, endTime ->
            testLog("audio trim=[${startTime},${endTime}]")
            vm.handEvent(
                PartBandGuitarLooperViewModel.Event.TrimAudio(
                    startTime = startTime,
                    endTime = endTime
                )
            )
        }
        binding.viewGuitarlooperAudiowaveView.onTouchEvent = { status, eventAction ->
            testLog("audioview onTouchEvent=[${status},${eventAction}]")
            if ((eventAction == MotionEvent.ACTION_DOWN ||
                        eventAction == MotionEvent.ACTION_MOVE) &&
                status == GuitarLooperAudioWaveView.AudioWaveStatus.Playing
            ) {
                vm.handEvent(PartBandGuitarLooperViewModel.Event.PauseAudio)
            } else if ((eventAction == MotionEvent.ACTION_UP
                        || eventAction == MotionEvent.ACTION_CANCEL) &&
                status == GuitarLooperAudioWaveView.AudioWaveStatus.Pause
            ) {
                vm.handEvent(PartBandGuitarLooperViewModel.Event.PlayAudio)
            }
        }

        binding.viewGuitarlooperAudiowaveView.onSeekTo = { progress: Int, seekEnd: Boolean ->
            vm.handEvent(
                PartBandGuitarLooperViewModel.Event.SeekAudio(
                    currentPos = progress,
                    sendCommand = seekEnd
                )
            )
        }

        binding.viewGuitarlooperAudiowaveView.onAudioPlayed = {
//                if (vm.isCountDown.value){
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.StopAudio)
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.OverDub)
//                }else{
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.PlayAudio)
//                }
//            else{
//                if (isDrumModeReading.value){
//                    isDrumModeReading.value =false
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.DrumRecord)
//                }else{
//                    vm.handEvent(PartBandGuitarLooperViewModel.Event.DrumReading)
//                }
//            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun observeModel() {
        lifecycleScope.launch {
            vm.screenData.collect {
                //vm.testLog("collect screenData=${it}")
                when (it) {
                    is PartBandGuitarLooperViewModel.ScreenData.Loading -> {

                    }

                    is PartBandGuitarLooperViewModel.ScreenData.Success -> {
                        updateScreen(it)
                    }
                }
            }
        }

        lifecycleScope.launch {
            vm.currentAudioLayer.collect {
                currentAudioRecordLayer = it
            }
        }

        vm.currentDrumType.observe(this) {
            binding.tvLooperDrum.text = "${getDrumStyleName(this, it)}"
        }

        vm.isPlaying.observe(this) {
//            binding.ivDrumPlay.setImageResource(if (it) R.drawable.ic_mini_player_pause else R.drawable.ic_mini_player_play)
        }

        vm.undoOrDiscardAudio.observe(this) {
            if (it == 0) {
                binding.viewGuitarlooperAudiowaveView.reset()
                binding.viewGuitarlooperAudiowaveView.clearRecordData()
            } else if (it == 1) {
                binding.viewGuitarlooperAudiowaveView.undo()
            }
        }

        lifecycleScope.launch {
            vm.barNumberList.collect{
                binding.looperBarsView.setData(it)
            }
        }

    }

    private fun initBottomController() {
        binding.llUndo.setOnClickListener(this)
        binding.llPlay.setOnClickListener(this)
        binding.llDelete.setOnClickListener(this)
    }

    private fun updateScreen(screenData: PartBandGuitarLooperViewModel.ScreenData.Success) {
        binding.viewGuitarlooperAudiowaveView.setRecordingTime(screenData.recordMaxTime)
        binding.btGuitarloopercircleButton.setType(screenData.butType)
        binding.viewGuitarlooperAudiowaveView.setAudioWaveStatus(screenData.audioWaveStatus)
        binding.viewGuitarlooperAudiowaveView.withSound(screenData.withSound)
        binding.viewGuitarlooperAudiowaveView.setDrumMode(vm.drumType.value)
        binding.viewGuitarlooperAudiowaveView.setCurrentBarNumber(screenData.barValue)
        binding.tvRecordTime.text =
            String.format(Locale.ROOT, "%.2f", screenData.recordMaxTime / 1000f)
        binding.looperBarsView.visibility = View.GONE
        binding.clDrum.visibility = View.GONE
        updateBottomControllerView(screenData.butType)
        when (screenData.audioWaveStatus) {
            GuitarLooperAudioWaveView.AudioWaveStatus.Ready -> {
                binding.clDrum.visibility = View.VISIBLE
//                binding.viewGuitarlooperAudiowaveView.initAudioWaveAnim()
            }

            GuitarLooperAudioWaveView.AudioWaveStatus.Playing,
            GuitarLooperAudioWaveView.AudioWaveStatus.Triming,
            GuitarLooperAudioWaveView.AudioWaveStatus.Pause,
            GuitarLooperAudioWaveView.AudioWaveStatus.Overdub,
            GuitarLooperAudioWaveView.AudioWaveStatus.Recording -> {
                if (screenData.audioWaveStatus == GuitarLooperAudioWaveView.AudioWaveStatus.Recording) {
//                    binding.viewGuitarlooperAudiowaveView.setOriginalValue(screenData.audioWaveValueList,screenData.recordMaxTime)
                }
                binding.viewGuitarlooperAudiowaveView.setPlayProgress(screenData.audioPlayProgress)
                if (screenData.audioWaveStatus == GuitarLooperAudioWaveView.AudioWaveStatus.Triming) {
                    binding.viewGuitarlooperAudiowaveView.updateTrimSeekLineInitX()
                }
                //
                if (screenData.audioWaveStatus != GuitarLooperAudioWaveView.AudioWaveStatus.Overdub) {
                    this.originalList.value = screenData.audioWaveValueList
                    this.originalTime = screenData.recordMaxTime
                }


                binding.viewGuitarlooperAudiowaveView.updateValueList(screenData.audioWaveValueList)
                if (screenData.audioWaveStatus == GuitarLooperAudioWaveView.AudioWaveStatus.Playing) {
                    binding.ivRecordPlay.setImageResource(R.drawable.ic_mini_player_pause)
                    binding.tvLoopPlay.text = getString(R.string.pause)
                } else {
                    binding.ivRecordPlay.setImageResource(R.drawable.ic_mini_player_play)
                    binding.tvLoopPlay.text = getString(R.string.play)
                }
            }

            GuitarLooperAudioWaveView.AudioWaveStatus.Excessive -> {
                binding.viewGuitarlooperAudiowaveView.reset()
            }

            GuitarLooperAudioWaveView.AudioWaveStatus.DrumPlaying -> {
                binding.viewGuitarlooperAudiowaveView.updateValueList(screenData.audioWaveValueList)
                binding.viewGuitarlooperAudiowaveView.initDrumPlayRange()
                binding.viewGuitarlooperAudiowaveView.setPlayProgress(screenData.audioPlayProgress)
                this.originalList.value = screenData.audioWaveValueList
                this.originalTime = screenData.recordMaxTime
                binding.looperBarsView.visibility = View.VISIBLE
                binding.llControls.visibility = View.VISIBLE
            }

            GuitarLooperAudioWaveView.AudioWaveStatus.DrumRecord,
            GuitarLooperAudioWaveView.AudioWaveStatus.CountDown -> {
                binding.viewGuitarlooperAudiowaveView.setPlayProgress(screenData.audioPlayProgress)
            }

            else -> {}
        }

        binding.tvRecordTime.visibility = when (screenData.audioWaveStatus) {
            GuitarLooperAudioWaveView.AudioWaveStatus.Ready,
            GuitarLooperAudioWaveView.AudioWaveStatus.Recording,
            GuitarLooperAudioWaveView.AudioWaveStatus.RecordingFinish -> {
                View.VISIBLE
            }

            else -> {
                View.INVISIBLE
            }
        }
        val circleButTypeName = when (screenData.butType) {
            GuitarLooperCircleButton.Type.READY -> getString(R.string.record)
            GuitarLooperCircleButton.Type.RECORDING -> getString(R.string.stop)
            GuitarLooperCircleButton.Type.PLAY,
            GuitarLooperCircleButton.Type.RECORDFINISH,
            GuitarLooperCircleButton.Type.PAUSE,
            GuitarLooperCircleButton.Type.STOP,
            GuitarLooperCircleButton.Type.OVERDUB -> getString(R.string.overdub)
            GuitarLooperCircleButton.Type.COUNTDOWN -> getString(R.string.countdown)
        }
        binding.tvCircleButType.text = circleButTypeName
    }

    private fun updateBottomControllerView(type: GuitarLooperCircleButton.Type){
        binding.llControls.visibility = when (type) {
            GuitarLooperCircleButton.Type.PLAY,
            GuitarLooperCircleButton.Type.RECORDFINISH,
            GuitarLooperCircleButton.Type.PAUSE,
            GuitarLooperCircleButton.Type.STOP,
            GuitarLooperCircleButton.Type.OVERDUB
                -> {
                binding.llUndo.visibility =
                    if (currentAudioRecordLayer >= 2) View.VISIBLE else View.GONE
                View.VISIBLE
            }

            else -> {
                binding.llUndo.visibility = View.GONE
                View.GONE
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (dialog.isShowing) {
            dialog.dismiss()
        }
        if (partBandGuitarLooperDialog.isShowing){
            partBandGuitarLooperDialog.dismiss()
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.ll_undo -> {
                binding.viewGuitarlooperAudiowaveView.getRecordData()?.run {
                    vm.handEvent(PartBandGuitarLooperViewModel.Event.UndoAudio(recordTime, data))
                }
            }

            R.id.ll_play -> {
                when (binding.viewGuitarlooperAudiowaveView.currentStatus()) {
                    GuitarLooperAudioWaveView.AudioWaveStatus.Playing -> {
                        vm.handEvent(PartBandGuitarLooperViewModel.Event.PauseAudio)
                    }

                    GuitarLooperAudioWaveView.AudioWaveStatus.Triming,
                    GuitarLooperAudioWaveView.AudioWaveStatus.Pause -> {
                        vm.handEvent(PartBandGuitarLooperViewModel.Event.PlayAudio)
                    }

                    else -> {}
                }
            }

            R.id.ll_delete -> {
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
                dialog.show()
            }
            R.id.tv_add -> jumpToDrumSetting()
            R.id.btn_drum -> updateDrumView(PartBandGuitarLooperViewModel.DrumType.DRUM)
            R.id.btn_metronome -> updateDrumView(PartBandGuitarLooperViewModel.DrumType.METRONOME)
        }
    }
    
    private fun updateDrumView(type: PartBandGuitarLooperViewModel.DrumType) {
        when (type) {
            PartBandGuitarLooperViewModel.DrumType.DRUM -> {
                isDrum = true
                binding.tvLooperDrum.visibility = View.VISIBLE
                binding.btnDrum.setBackgroundResource(R.drawable.radius_round_bg_inverse)
                binding.btnDrum.setTextColor(getColor(R.color.fg_inverse))
                binding.btnDrum.setTypeface(binding.btnDrum.typeface, Typeface.BOLD)

                binding.btnMetronome.background = null
                binding.btnMetronome.setTextColor(getColor(R.color.fg_secondary))
                binding.btnMetronome.setTypeface(binding.btnDrum.typeface, Typeface.NORMAL)
            }

            PartBandGuitarLooperViewModel.DrumType.METRONOME -> {
                isDrum =false
                binding.tvLooperDrum.visibility = View.GONE
                binding.btnDrum.background = null
                binding.btnDrum.setTextColor(getColor(R.color.fg_secondary))
                binding.btnDrum.setTypeface(binding.btnDrum.typeface, Typeface.NORMAL)

                binding.btnMetronome.setBackgroundResource(R.drawable.radius_round_bg_inverse)
                binding.btnMetronome.setTextColor(getColor(R.color.fg_inverse))
                binding.btnMetronome.setTypeface(binding.btnDrum.typeface, Typeface.BOLD)
            }
            else ->{}
        }
    }

    private fun jumpToDrumSetting(){
        val type = if (isDrum) vm.currentDrumType.value else null
        vm.device?.UUID?.let {
            lifecycleScope.launch {
                DrumSettingActivity.launch(
                    this@PartyBandGuitarLooperActivity,
                    it,
                    type,
                    isDrum
                ).also {
                    it?.run {
                        dealDrum(this)
                    }
                }
            }
        }
    }

    private fun dealDrum(looperDrumData: LooperDrumData) {
        vm.setBpm(looperDrumData.bpm?: 0)

        looperDrumData.drumSequence?.let {
            vm.setDrumType(PartBandGuitarLooperViewModel.DrumType.DRUM)
            vm.changeCurrentDrumType(it)
            vm.setDrumValue(it.beat.beatName)
        }

        looperDrumData.metronomeSequence?.run {
            vm.setDrumType(PartBandGuitarLooperViewModel.DrumType.METRONOME)
            vm.setDrumValue(beatName)
        }

        vm.addAudioLayer()
        binding.clDrum.visibility = View.VISIBLE
        vm.handEvent(PartBandGuitarLooperViewModel.Event.DrumReading)
    }
}