package com.harman.partyband.micsensitivity

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBandDevice
import com.harman.log.Logger
import com.harman.partyband.looper.combine
import com.harman.v5protocol.bean.devinfofeat.V5Mic1SensitivityIndBFeature
import com.harman.v5protocol.bean.devinfofeat.V5Mic2SensitivityIndBFeature
import com.jbl.one.configuration.AppConfigurationUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.micsensitivity
 * @ClassName: MicSensitivityViewModel
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/13 17:51
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 17:51
 * @UpdateRemark:
 * @Version: 1.0
 */
class MicSensitivityViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {

    companion object {
        const val TAG = "MicSensitivityViewModel"
        const val DBDEFAULT: Float = -40F
        const val MIC1 = 0x01
        const val MIC2 = 0x02
    }

    private val _showToast = MutableStateFlow<String?>(null)
    val showToast = _showToast.asStateFlow()

    private val device =
        DeviceStore.find(savedStateHandle.get<String>("uuid")!!) as? PartyBandDevice
    private val micType = savedStateHandle.get<Int>("micType") ?: MIC1

    private val _micType = MutableStateFlow(MicType.MIC1)
    private val _micclassify = MutableStateFlow(MicClassify.Customization.value)
    private val _sensitivityinlevel = MutableStateFlow(MicSensitivityInLevel.Mid)
    private val _dbvalue = MutableStateFlow<Float>(DBDEFAULT)
    private val _classifyList = MutableStateFlow<List<MicclassifyBean>>(emptyList())
    private val _sensitivityinLevelList =
        MutableStateFlow<List<MicSensitivityInLevelBean>>(emptyList())
    private val _otherModelsList =
        MutableStateFlow<List<MicSensitivityDbBean>>(emptyList())

    val screenData: StateFlow<ScreenData> =
        combine(
            _micclassify,
            _sensitivityinlevel,
            _dbvalue,
            _classifyList,
            _sensitivityinLevelList,
            _otherModelsList
        ) { micclassify,
            sensitivityinlevel,
            dbvalue,
            classifyList,
            sensitivityinLevelList,
            otherModelsList ->
            ScreenData.Success(
                micclassify = micclassify,
                sensitivityinlevel = sensitivityinlevel,
                dbvalue = dbvalue,
                classifyList = classifyList,
                sensitivityinLevelList = sensitivityinLevelList,
                otherModelsList = otherModelsList,
            )
        }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5_000), ScreenData.Loading)


    init {
        _micType.value = MicType.entries.find {
            it.value == micType
        } ?: MicType.MIC1
        Logger.i(TAG, "micType=${_micType.value}")
        createClassifyData()
        createMicSensitivityLevelData()
        createOthersModels()
        getLevelAndDb()
    }

    infix fun Float.until(to: Float): ClosedFloatingPointRange<Float> {
        return this..(to - Float.MIN_VALUE)
    }

    private fun getLevelAndDb() {
        viewModelScope.launch {
            when (_micType.value) {
                MicType.MIC1 -> {
                    device?.getDevInfoFeat<V5Mic1SensitivityIndBFeature>()
                        .apply {
                            Logger.i(TAG, "getMic1LevelAndDb=${this}")
                            _dbvalue.value = this?.db ?: DBDEFAULT
                            _sensitivityinlevel.value = dbValue2Level(_dbvalue.value)
                            Logger.i(
                                TAG,
                                "getMic1LevelAndDb level=${_sensitivityinlevel.value} db=${_dbvalue.value}"
                            )
                        }
                }

                MicType.MIC2 -> {
                    device?.getDevInfoFeat<V5Mic2SensitivityIndBFeature>()
                        .apply {
                            Logger.i(TAG, "getMic2LevelAndDb=${this}")
                            _dbvalue.value = this?.db ?: DBDEFAULT
                            _sensitivityinlevel.value = dbValue2Level(_dbvalue.value)
                            Logger.i(
                                TAG,
                                "getMic2LevelAndDb level=${_sensitivityinlevel.value} db=${_dbvalue.value}"
                            )
                        }
                }
            }

        }
    }

    private fun dbValue2Level(dbValue: Float): MicSensitivityInLevel {
        //[-40, -30) [-30, -10] (-10, 0]
        return if (dbValue >= -40f && dbValue < -30f) {
            MicSensitivityInLevel.Low
        } else if (dbValue >= -30f && dbValue <= -10f) {
            MicSensitivityInLevel.Mid
        } else if (dbValue > -10f && dbValue <= 0f) {
            MicSensitivityInLevel.High
        } else {
            MicSensitivityInLevel.Low
        }
    }

    private fun level2DbValue(level: MicSensitivityInLevel): Float {
        //40、-20、0 low mid high
        return when (level) {
            MicSensitivityInLevel.Low -> -40f
            MicSensitivityInLevel.Mid -> -20f
            MicSensitivityInLevel.High -> 0f
        }
    }

    private fun createClassifyData() {//tab
        val clickFun = ::micClassifyClick
        val totalTabList = mutableListOf<MicclassifyBean>()
        //
        val finalTab = MicClassify.entries.map {
            MicclassifyBean(
                showName = it.desc,
                checked = it.value == _micclassify.value,
                classifyValue = it.value,
                onClickItem = clickFun,
            )
        }
        totalTabList += finalTab
        AppConfigurationUtils.getBandBoxMicModels()?.brandList?.mapIndexed { _, brand ->
            val brandId = brand.id
            MicclassifyBean(
                showName = brand.name,
                checked = brandId == _micclassify.value,
                classifyValue = brandId,
                onClickItem = clickFun,
            )
        }?.let {
            totalTabList += it
        }
        Logger.i(TAG, "tabList=${totalTabList}")
        _classifyList.value = totalTabList
    }

    private fun createMicSensitivityLevelData() {//default low mid high
        _sensitivityinLevelList.value = MicSensitivityInLevel.entries.map {
            MicSensitivityInLevelBean(
                showName = it.desc,
                checked = it == _sensitivityinlevel.value,
                level = it,
                onClickItem = ::micSensitivityLevelClick,
            )
        }
    }

    private fun createOthersModels() {
        _otherModelsList.value = AppConfigurationUtils.getBandBoxMicModels()?.brandList
            ?.find { it.id.toInt() == _micclassify.value }
            ?.let { brand ->
                Logger.i(TAG, "${brand.id},${brand.name},${brand.models.size}")
                brand.models.map { model ->
                    Logger.i(TAG, "--------${model.id},${model.name},${model.db}")
                    MicSensitivityDbBean(
                        showName = model.name,
                        checked = _dbvalue.value == model.db,
                        db = model.db,
                        onClickItem = ::micSensitivityOtherModelsClick,
                        id = model.id
                    )
                }
            } ?: emptyList()
    }

    private fun micClassifyClick(mode: MicclassifyBean) {
        _classifyList.update { list ->
            list.map {
                if (it.classifyValue == mode.classifyValue) {
                    it.copy(checked = true)
                } else {
                    it.copy(checked = false)
                }
            }
        }
        _micclassify.value = mode.classifyValue
        when (mode.classifyValue) {
            MicClassify.Customization.value -> {
                createMicSensitivityLevelData()
            }

            else -> {
                createOthersModels()
            }
        }
        Logger.i(TAG, "micClassifyClick=${mode}")
    }

    private fun micSensitivityLevelClick(mode: MicSensitivityInLevelBean) {
        _sensitivityinLevelList.update { list ->
            list.map {
                if (it.level == mode.level) {
                    _sensitivityinlevel.value = mode.level
                    it.copy(checked = true)
                } else {
                    it.copy(checked = false)
                }
            }
        }
        handEvent(Event.SetDefaultLevel(mode.level))
        Logger.i(TAG, "micClassifyClick=${mode}")
    }

    private fun micSensitivityOtherModelsClick(mode: MicSensitivityDbBean) {
        _otherModelsList.update { list ->
            list.map {
                if (it.id == mode.id) {
                    it.copy(checked = true)
                } else {
                    it.copy(checked = false)
                }
            }
        }
        handEvent(
            Event.SetDbValueSendCommand(mode.db, isViewValue = false)
        )
    }


    fun handEvent(event: Event) {
        viewModelScope.launch {
            when (event) {
                is Event.SetDefaultLevel -> {
                    val setValue = level2DbValue(event.level)
                    setMicSensitivityDb(setValue)
                }

                is Event.SetDbValueNotSendCommand -> {
                    _dbvalue.value = viewValue2Db(event.dbViewValue)
                }

                is Event.SetDbValueSendCommand -> {
                    _dbvalue.value = if (event.isViewValue) {
                        viewValue2Db(event.dbValue)
                    } else {
                        event.dbValue
                    }
                    setMicSensitivityDb(_dbvalue.value)
                }

                else -> {}
            }
        }
    }

    private suspend fun setMicSensitivityDb(db: Float) {
        when (_micType.value) {
            MicType.MIC1 -> {
                val status =
                    device?.setDevInfoFeat(V5Mic1SensitivityIndBFeature(db))
                if (status?.isSuccess() == true) {
                    Logger.i(TAG, "setMic1SensitivityDb=${db} success = ${status}")
                    _dbvalue.value = db
                    _sensitivityinlevel.value = dbValue2Level(db)
                } else {
                    showToast("SetMic1DbValue fail =${db}")
                }
            }

            MicType.MIC2 -> {
                val status =
                    device?.setDevInfoFeat(V5Mic2SensitivityIndBFeature(db))
                if (status?.isSuccess() == true) {
                    Logger.i(TAG, "setMic2SensitivityDb=${db} success = ${status}")
                    _dbvalue.value = db
                    _sensitivityinlevel.value = dbValue2Level(db)
                } else {
                    showToast("SetMic2DbValue fail =${db}")
                }
            }
        }
    }

    fun showToast(message: String? = null) {
        _showToast.value = message
    }

    fun db2ViewValue(): Float {//[0,40]
        //_dbvalue [-40,0]
        return _dbvalue.value + 40F
    }

    fun viewValue2Db(viewValue: Float): Float {
        //viewValue [0,40]
        return viewValue - 40f
    }

    enum class MicType(val value: Int) {
        MIC1(MicSensitivityViewModel.MIC1), MIC2(MicSensitivityViewModel.MIC2)
    }


    sealed interface Event {
        data class SetDefaultLevel(val level: MicSensitivityInLevel) : Event
        data class SetDbValueNotSendCommand(val dbViewValue: Float) : Event
        data class SetDbValueSendCommand(val dbValue: Float, val isViewValue: Boolean = true) :
            Event
    }


    sealed interface ScreenData {
        data object Loading : ScreenData
        data class Success(
            val micclassify: Int = MicClassify.Customization.value,
            val sensitivityinlevel: MicSensitivityInLevel = MicSensitivityInLevel.Mid,
            val dbvalue: Float = 0F,
            val classifyList: List<MicclassifyBean> = emptyList(),
            val sensitivityinLevelList: List<MicSensitivityInLevelBean> = emptyList(),
            val otherModelsList: List<MicSensitivityDbBean> = emptyList(),
        ) : ScreenData
    }


}