package com.harman.partyband.micsensitivity

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.micsensitivity
 * @ClassName: SpaceItemDecoration
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/14 15:47
 * @UpdateUser:
 * @UpdateDate: 2025/1/14 15:47
 * @UpdateRemark:
 * @Version: 1.0
 */
class SpaceItemDecoration(
    private val space: Int,
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        if (position == 0) {
            outRect.left = 0
        } else {
            outRect.left = space
        }
        val itemCount = parent.adapter?.itemCount ?: -1
        if (position == itemCount - 1) {
            outRect.right = 0
        } else {
            outRect.right = space
        }

    }
}