package com.harman.partyband.micsensitivity

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.micsensitivity
 * @ClassName: MicSensitivityActivity
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/14 10:57
 * @UpdateUser:
 * @UpdateDate: 2025/1/14 10:57
 * @UpdateRemark:
 * @Version: 1.0
 */
data class MicclassifyBean(
    val showName: String,
    val checked: Boolean = false,
    val classifyValue:Int,
    val onClickItem: (model: MicclassifyBean) -> Unit,
)

enum class MicClassify(val value: Int, val desc: String) {
    Customization(0x9999, "Customization"),
//    Basic(0x10000, "Basic"),
//    DBVPa(0x10001, "dBV/Pa"),
//    Shure(0x02, "Shure"),
//    <PERSON><PERSON><PERSON><PERSON>(0x03,"<PERSON>nhei<PERSON>"),
//    <PERSON><PERSON>(0x04,"<PERSON><PERSON>"),
}

