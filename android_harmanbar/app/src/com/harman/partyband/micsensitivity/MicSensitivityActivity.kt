package com.harman.partyband.micsensitivity

import android.app.Activity
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityMicSensitivityBinding
import com.harman.log.Logger
import com.harman.partyband.widget.dp2px
import com.harman.partylight.util.gone
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.launch

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.partyband.micsensitivity
 * @ClassName: MicSensitivityActivity
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/1/13 17:51
 * @UpdateUser:
 * @UpdateDate: 2025/1/13 17:51
 * @UpdateRemark:
 * @Version: 1.0
 */
class MicSensitivityActivity : AppCompatBaseActivity() {

    companion object {
        private const val TAG = "MicSensitivityActivity"
        const val MIC1 = MicSensitivityViewModel.MIC1
        const val MIC2 = MicSensitivityViewModel.MIC2
        fun launch(act: Activity, uuid: String, micType: Int = MIC1) {
            act.push<MicSensitivityActivity>(
                bundleOf(
                    "uuid" to uuid,
                    "micType" to micType,
                )
            )
        }
    }

    private val debug: Boolean = false
    private val binding by lazy { ActivityMicSensitivityBinding.inflate(layoutInflater) }

    private val vm by viewModels<MicSensitivityViewModel>()
    private val classifyAdapter by lazy {
        MicclassifyAdapter()
    }
    private val micSensitivityInLevelAdapter by lazy {
        MicSensitivityInLevelAdapter()
    }
    private val otherModelsAdapter by lazy {
        MicSensitivityDbAdapter()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.setContentView(binding.root)
        this.buildAppbar()
        this.buildRecycleListView()
        this.buildDbView()
        this.observeModel()
    }

    private fun buildAppbar() {
        binding.appbar.tvTitle.text = getString(R.string.mic_sensitivity_title)
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
    }

    private fun buildRecycleListView() {
        binding.rcListClassify.layoutManager =
            LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
        binding.rcListClassify.addItemDecoration(SpaceItemDecoration(6f.dp2px(this).toInt()))
        binding.rcListClassify.adapter = classifyAdapter
        binding.rcListClassify.itemAnimator = null
        //
        binding.rcListSensitivityInLevel.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rcListSensitivityInLevel.adapter = micSensitivityInLevelAdapter
        //
        binding.rcListModels.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rcListModels.adapter = otherModelsAdapter

    }

    private fun buildDbView() {
        binding.ccsView.onValueChange = {
            vm.handEvent(MicSensitivityViewModel.Event.SetDbValueNotSendCommand(it.toFloat()))
        }
        binding.ccsView.onValueChangeLastTime = {
            vm.handEvent(MicSensitivityViewModel.Event.SetDbValueSendCommand(it.toFloat()))
        }
    }

    private fun observeModel() {
        lifecycleScope.launch {
            vm.screenData.collect {
                Logger.i(TAG, "screenData=${it}")
                when (it) {
                    is MicSensitivityViewModel.ScreenData.Loading -> {}
                    is MicSensitivityViewModel.ScreenData.Success -> {
                        updateScreen(it)
                    }
                }

            }
        }
        lifecycleScope.launch {
            vm.showToast.collect {
                it?.let {
                    ToastUtils.showShort(it)
                    vm.showToast(null)
                }
            }
        }
    }

    private fun updateScreen(screenData: MicSensitivityViewModel.ScreenData.Success) {
        this.classifyAdapter.submitList(screenData.classifyList)
        this.micSensitivityInLevelAdapter.submitList(screenData.sensitivityinLevelList)
        this.otherModelsAdapter.submitList(screenData.otherModelsList)

        when (screenData.micclassify) {
            MicClassify.Customization.value -> {
                binding.tvContentTitle.text = getString(R.string.levels)
                binding.layoutSensitivityInLevel.gone()
                binding.layoutMicSensitivityInDbv.visible()
                binding.layoutOtherModels.gone()
            }

            else -> {
                binding.tvContentTitle.text = getString(R.string.models)
                binding.layoutSensitivityInLevel.gone()
                binding.layoutMicSensitivityInDbv.gone()
                binding.layoutOtherModels.visible()
            }
        }
//        binding.tvDbValue.text = "${screenData.dbvalue.toInt()}"
        //-40,0
        //[0,40]
        binding.ccsView.setCurrentValue(vm.db2ViewValue().toInt())
    }


}