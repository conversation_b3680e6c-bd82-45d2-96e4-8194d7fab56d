package com.harman.partyband.micsensitivity

data class MicSensitivityInLevelBean(
    val showName: String,
    val level: MicSensitivityInLevel = MicSensitivityInLevel.Mid,
    val checked: Boolean = false,
    val onClickItem: ((model: MicSensitivityInLevelBean) -> Unit)?=null,
)

data class MicSensitivityDbBean(
    val showName: String,
    val db: Float,
    val checked: Boolean = false,
    val onClickItem: ((model: MicSensitivityDbBean) -> Unit)?=null,
    val id: Int
)

/**
 *
 * 0x0001
 * 0x00: Off
 * 0x01: Low
 * 0x02: Mid
 * 0x03: High
 *
 *
 */
enum class MicSensitivityInLevel(val value: Int, val desc: String) {
//    Default(0x00, "Default"),//off
    Low(0x01, "Low"),
    Mid(0x02, "Mid"),
    High(0x03, "High"),
}
