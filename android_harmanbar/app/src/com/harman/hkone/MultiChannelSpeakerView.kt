package com.harman.hkone

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import com.harman.bar.app.R
import com.harman.colorId
import com.harman.modelName
import com.harman.discover.bean.Device
import com.jbl.one.configuration.AppConfigurationUtils

class MultiChannelSpeakerView : LinearLayout {
    private var mContext: Context? = null
    private var preList: List<android.util.Pair<String?,String?>?>?  = null

    private val FIRST_MARGIN_START = 0

    private val NORMAL_MARGIN_START = 16f

    constructor(context: Context?) : super(context) {
        init(context)
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context)
    }

    constructor(
        context: Context?,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        init(context)
    }


    fun init(context: Context?) {
        this.mContext = context
    }

    fun setDeviceImagesByDeviceList(devices: List<Device>) {
        var modelNameAndColors =  devices.map { android.util.Pair.create(it.modelName(),it.colorId()) }
        setDeviceImagesByPairList(modelNameAndColors)
    }

    fun setDeviceImagesByPairList(modelNameAndColors: List<android.util.Pair<String?,String?>?>?) {
        modelNameAndColors?:return
        if(preList == modelNameAndColors){
            return
        }
        preList = modelNameAndColors
        removeAllViews()
        for (i in modelNameAndColors.indices) {
            if (i >= 4) break
            val iv = ImageView(mContext)
            val modelNameAndColor = modelNameAndColors[i]
            modelNameAndColor?:continue
            var color = modelNameAndColor.second?:"01"
            modelNameAndColor.first?.also {
                var deviceImage = AppConfigurationUtils.getModelRenderPathByModelName(it,color)
                DeviceImageUtil.loadAsBitmap(iv,deviceImage,null,null, scaleType = ImageView.ScaleType.CENTER_INSIDE)

                iv.setBackgroundResource(R.drawable.round_feature_sp3_new)
                val w = resources.getDimension(R.dimen.gc_view_width2).toInt()
                val lp = LayoutParams(w, w)

                if (i == 0) {
                    lp.marginStart = FIRST_MARGIN_START
                } else {
                    lp.marginStart = -ScreenUtil.dip2px(mContext, NORMAL_MARGIN_START)
                }
                addView(iv, lp)
            }
        }
    }


    fun setDeviceImagesByModelNameList(modelNames: List<String>?) {
        modelNames?:return
        var modelNameAndColors =  modelNames.map { android.util.Pair.create(it,"01") }
        setDeviceImagesByPairList(modelNameAndColors)

    }

}
