package com.harman.partylight.util

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import androidx.annotation.FloatRange
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.isHKOneApp
import kotlinx.coroutines.CompletableDeferred
import java.lang.reflect.Modifier
import java.io.Serializable

/**
 * @Description Some commonly used extensions
 * <AUTHOR>
 * @Time 2024/7/29
 */

//Restrict the content of an activity to a safe area (cannot be extended to the status bar, navigation bar)
fun Activity.fitSystemBar() {
    window.statusBarColor = getColor(R.color.bg_surface)
    window.navigationBarColor = getColor(R.color.bg_surface)
    WindowCompat.setDecorFitsSystemWindows(window, true)
    WindowCompat.getInsetsController(window, window.decorView).apply {
        isAppearanceLightStatusBars = isHKOneApp()
        isAppearanceLightNavigationBars = isHKOneApp()
    }
}
//The content of the activity can be extended to (status bar, navigation bar)
fun Activity.immersive(
    statusBarColor: Int = getColor(R.color.transparent),
    navBarColor: Int = getColor(R.color.transparent)
) {
    window.statusBarColor = statusBarColor
    window.navigationBarColor = navBarColor
    WindowCompat.setDecorFitsSystemWindows(window, false)
    WindowCompat.getInsetsController(window, window.decorView).apply {
        isAppearanceLightStatusBars = isHKOneApp()
        isAppearanceLightNavigationBars = isHKOneApp()
    }
}

fun View.gone() {
    visibility = View.GONE
}

fun View.visible() {
    visibility = View.VISIBLE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.doubleClick(invoke: ((v: View) -> Unit)? = null) {
    if (null == invoke) {
        setOnClickListener(null)
    } else {
        setOnClickListener(
            object : CustomCountClickListener(2) {
                override fun onCountClick(v: View) {
                    invoke.invoke(v)
                }
            },
        )
    }
}

fun View.debounceClick(invoke: (v: View) -> Unit) {
    setOnClickListener(object : DebounceClickListener() {
        override fun onDebounceClick(v: View) {
            invoke.invoke(v)
        }
    })
}

fun <T> RecyclerView.Adapter<*>.generateSimpleDiffer(
    isSameItem: ((oldItem: T, newItem: T) -> Boolean)? = null,
    isSameContent: ((oldItem: T, newItem: T) -> Boolean)? = null
): AsyncListDiffer<T> {
    return AsyncListDiffer<T>(this, object : DiffUtil.ItemCallback<T>() {

        override fun areItemsTheSame(oldItem: T & Any, newItem: T & Any): Boolean {
            if (null != isSameItem) {
                return isSameItem.invoke(oldItem, newItem)
            }

            return oldItem === newItem
        }

        override fun areContentsTheSame(oldItem: T & Any, newItem: T & Any): Boolean {
            return isSameContent?.invoke(oldItem, newItem) ?: false
        }
    })
}

/**
 * 检查对象中的所有属性字段是否为 null.(将跳过静态字段)
 */
inline fun <reified T> T.isAllFieldNull(): Boolean {
    val clz = T::class.java
    return clz.declaredFields.all {
        val isStaticField = Modifier.isStatic(it.modifiers)
        if (isStaticField) {
            //如果是静态属性就跳过
            return@all true
        }
        it.isAccessible = true
        val value = it.get(this)
        return@all null == value
    }
}

fun Int.toRgbList(): List<Int> = listOf(red, green, blue)
fun List<Int>.toColorInt(): Int? = try {
    Color.rgb(get(0), get(1), get(2))
} catch (e: Exception) {
    null
}

fun Int.withOpacity(@FloatRange(from = 0.0, to = 1.0) opacity: Float): Int {
    return Color.argb((255 * opacity).toInt(), red, green, blue)
}

inline fun <reified T> Context.push(params: Bundle? = null) {
    startActivity(Intent(this, T::class.java).apply {
        params?.run {
            putExtras(this)
        }
    })
}

/**
 * Wait for the target activity to finish and receive the result,
 * and the target activity needs to broadcast the execution result by [LocalBroadcastManager.sendBroadcast] when finishing
 * see also [routeResultKey],[routeResultAction],[popResult]
 */
suspend inline fun <reified Act, Result : Serializable> Context.syncPush(params: Bundle? = null): Result? {
    val completer = CompletableDeferred<Result?>()
    val action = Act::class.java.name + ".routeResultAction"
    val resultKey = Act::class.java.name + ".routeResult"
    val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == action) {
                LocalBroadcastManager.getInstance(this@syncPush).unregisterReceiver(this)
                completer.complete(intent.extras?.get(resultKey) as? Result)
            }
        }
    }
    LocalBroadcastManager.getInstance(this).registerReceiver(receiver, IntentFilter(action))
    startActivity(Intent(this, Act::class.java).apply {
        params?.run {
            putExtras(this)
        }
    })
    return completer.await()
}

private fun Activity.routeResultKey() = this.javaClass.name + ".routeResult"
private fun Activity.routeResultAction() = this.javaClass.name + ".routeResultAction"
fun Activity.popResult(result: Serializable?) {
    LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(routeResultAction()).putExtra(routeResultKey(), result))
}