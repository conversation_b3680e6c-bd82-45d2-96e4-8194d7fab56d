package com.harman.partylight.productinfo

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityPlProdInfoBinding
import com.harman.ota.partylight.PLOtaActivity
import com.harman.partylight.quickStartGuideUrl
import com.harman.partylight.util.doubleClick
import com.harman.partylight.util.fitSystemBar
import com.harman.partylight.util.gone
import com.harman.partylight.util.push
import com.harman.partylight.util.visible
import com.harman.log.Logger
import com.harman.utils.Utils
import com.harman.webview.AddNewProductHybridActivity
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity

/**
 * @Description 设备信息页
 * <AUTHOR>
 * @Time 2024/7/25
 */
class PLProdInfoActivity : AppCompatActivity() {
    companion object {
        const val TAG = "PLProdInfoActivity"
    }

    private val vm by viewModels<PLProdInfoViewModel>()

    private val binding by lazy {
        ActivityPlProdInfoBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
//        fitSystemBar()
        setContentView(binding.root)
        if (!vm.isDataValid()) {
            finish()
            return
        }
        buildView()
        vm.fetchData()
        observeVM()
    }

    private fun buildView() {
        binding.appbar.tvTitle.text = getString(R.string.product_information)
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
        binding.appbar.tvTitle.doubleClick {
            vm.fetchUsage()
        }
        binding.reset.root.background = null
        binding.qStart.root.background = null
        binding.sn.tvMain.text = getString(R.string.ptl_serial_number)
        binding.update.root.setOnClickListener {
            onUpdateClick()
        }
        binding.update.tvMain.text = getString(R.string.ptl_firmware_upgrade)
        binding.usage.tvMain.text = getString(R.string.ptl_total_power_time)
        binding.reset.root.setOnClickListener {
            onResetClick()
        }
        binding.reset.tvMain.text = getString(R.string.ptl_reset_product)
        binding.qStart.root.setOnClickListener {
            onQuickStartClick()
        }
        binding.qStart.tvMain.text = getString(R.string.ptl_start_guide)
        binding.qStart.ivRight.setImageResource(R.drawable.icon_arrow_outward)
        binding.qStart.ivRight.imageTintList = null

        updateSupportEntrance()
    }

    private fun updateSupportEntrance() {
        val displaySupportEntrance = !vm.mainDev?.pid?.let {
            AppConfigurationUtils.getModelConfig(it)?.urls?.urlSupport
        }.isNullOrBlank()

        if (displaySupportEntrance) {
            binding.productSupport.root.visible()
            binding.productSupport.tvMain.text = getString(R.string.jbl_Product_Support)
            binding.productSupport.ivRight.setImageResource(R.drawable.icon_arrow_outward)
            binding.productSupport.root.setOnClickListener {
                onProductSupportClick()
            }
        } else {
            binding.productSupport.root.gone()
        }
    }

    private fun observeVM() {
        vm.snState.observe(this) {
            binding.sn.run {
                if (null == it) {
                    root.gone()
                } else {
                    root.visible()
                    tvSec.text = it
                }
            }
        }
        vm.usageMinutesState.observe(this) {
            if (null == it) {
                binding.llUsage.gone()
            } else {
                binding.llUsage.visible()
                binding.usage.tvSec.text = formatUsageTime(it)
            }
        }
        vm.hasNewVersion.observe(this) {
            binding.update.vIndicator.run {
                if (it) visible() else gone()
            }
        }
    }

    private fun formatUsageTime(minutes: Int): String {
        val text = StringBuilder()
        val hours = minutes / 60
        if (hours > 0) {
            text.append(getString(R.string.hours_format,"$hours")).append(" ")
        }
        text.append("${minutes % 60}").append(" ").append(getString(R.string.ptl_minute))
        return text.toString()
    }

    private fun onUpdateClick() {
        push<PLOtaActivity>(bundleOf("uid" to vm.mainDev!!.UUID))
    }

    private fun onResetClick() {
//        push<PLProdResetActivity>(bundleOf("pid" to vm.mainDev?.pid))
        vm.mainDev?.pid?.also {  AddNewProductHybridActivity.launchSetupProduct(activity = this, pid = it)}

    }

    private fun onQuickStartClick() {
        push<QuickStartGuideWebActivity>(bundleOf("quick_start_guide_url" to quickStartGuideUrl(vm.mainDev?.pid ?: "")))
    }

    private fun onProductSupportClick() {
        val pid = vm.mainDev?.pid ?: run {
            Logger.e(TAG, "onProductSupportClick() >>> missing mainDev pid")
            return
        }

        val faqUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlFaq
        if (faqUrl.isNullOrBlank()) {
            Logger.e(TAG, "onProductSupportClick() >>> empty faq url for PID[$pid]")
            return
        }

        loadingUrlInBrowser(linkUrl = faqUrl)
    }

    private fun loadingUrlInBrowser(linkUrl: String) {
        Logger.d(TAG, "loadingUrlInBrowser Url: $linkUrl")
        runCatching {
            val intent = Intent()
            intent.action = "android.intent.action.VIEW"
            intent.data = Uri.parse(linkUrl)
            startActivity(intent)
        }.onFailure { e ->
            Logger.e(TAG,"", e)
        }
    }
}


//@Preview
//@Composable
//fun BuildPage() {
//    CompositionLocalProvider(LocalTheme provides LocalTheme.current) {
//        MaterialTheme() {
//            Column(
//                Modifier
//                    .fillMaxSize()
//                    .background(HMTheme.bgL1)
//            ) {
//                BuildAppBar(
//                    ::onBackClick,
//                    titleText = stringResource(id = R.string.ptl_product_info_title),
//                    onTitleDoubleClick = ::onTitleDoubleClick
//                )
//                BuildBody()
//            }
//        }
//    }
//}
//
//@Composable
//fun BuildBody() {
//    Column(
//        modifier = Modifier
//            .fillMaxSize()
//            .padding(16.dp),
//        verticalArrangement = Arrangement.spacedBy(16.dp)
//    ) {
//        //sn
//        BuildSn()
//        //update
//        BuildRow(
//            text = stringResource(id = R.string.ptl_firmware_upgrade),
//            hasIndicator = hasNewVersion,
//            onClick = this@PLProdInfoActivity::onUpdateClick,
//        )
//        //usage
//        BuildProductUsage()
//        //learn more
//        BuildLearMore()
//    }
//}
//
//@Composable
//fun BuildLearMore() {
//    Column {
//        Text(
//            text = stringResource(id = R.string.ptl_lear_more),
//            style = HMTStyle.Body_Strong.fg1(),
//            modifier = Modifier.padding(vertical = 8.dp),
//        )
//        BuildRow(
//            text = stringResource(id = R.string.ptl_reset_product),
//            shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
//            onClick = this@PLProdInfoActivity::onResetClick,
//        )
//        BuildRow(
//            text = stringResource(id = R.string.quick_start_guide),
//            shape = RoundedCornerShape(bottomStart = 12.dp, bottomEnd = 12.dp),
//            onClick = this@PLProdInfoActivity::onQuickStartClick,
//            rightWidget = {
//                Image(
//                    painter = painterResource(id = R.drawable.ptl_ic_forward),
//                    contentDescription = "",
//                )
//            }
//        )
//    }
//}
//
//@Composable
//fun BuildRow(
//    text: String,
//    hasIndicator: Boolean = false,
//    shape: Shape = RoundedCornerShape(12.dp),
//    onClick: (() -> Unit)? = null,
//    rightWidget: (@Composable () -> Unit)? = null,
//) {
//    Row(
//        modifier = Modifier
//            .clip(shape)
//            .background(HMTheme.bgM1)
//            .fillMaxWidth()
//            .clickable(null != onClick, onClick = { onClick?.invoke() })
//            .padding(16.dp),
//        verticalAlignment = Alignment.CenterVertically,
//    ) {
//        Text(
//            text = text,
//            style = HMTStyle.body_1_strong.fg1(),
//        )
//        if (hasIndicator) {
//            Spacer(
//                modifier = Modifier
//                    .padding(8.dp)
//                    .clip(CircleShape)
//                    .size(6.dp)
//                    .background(HMTheme.sp2)
//            )
//        }
//        Spacer(Modifier.weight(1f))
//        rightWidget?.invoke() ?: Image(
//            painter = painterResource(id = R.drawable.right_arrow),
//            contentDescription = "",
//            modifier = Modifier.size(24.dp),
//            colorFilter = ColorFilter.tint(HMTheme.fg1),
//            contentScale = ContentScale.Inside
//        )
//    }
//}
//
//@Composable
//fun BuildSn() {
//    remember { snState }.value?.also {
//        BuildInfoRow(mainText = stringResource(id = R.string.ptl_serial_number), secondText = it)
//    }
//}
//
//@Composable
//fun BuildProductUsage() {
//    remember { productUsageState }.value?.also {
//        Column {
//            Text(
//                text = stringResource(id = R.string.ptl_product_usage),
//                style = HMTStyle.Body_Strong.fg1(),
//                modifier = Modifier.padding(vertical = 8.dp),
//            )
//            BuildInfoRow(
//                mainText = stringResource(id = R.string.ptl_total_power_time),
//                secondText = it
//            )
//        }
//    }
//}
//
//@Composable
//fun BuildInfoRow(mainText: String, secondText: String) {
//    Column(
//        modifier = Modifier
//            .clip(RoundedCornerShape(12.dp))
//            .background(HMTheme.bgM1)
//            .fillMaxWidth()
//            .padding(16.dp),
//        horizontalAlignment = Alignment.Start,
//    ) {
//        Text(
//            text = mainText,
//            style = HMTStyle.body_1_strong.fg1(),
//        )
//        VSpace(dp = 4.dp)
//        Text(
//            text = secondText,
//            style = HMTStyle.caption.fg2()
//        )
//    }
//}
//@Composable
//fun BuildAppBar(
//    onLeadingClick: () -> Unit,
//    titleText: String,
//    onTitleDoubleClick: (() -> Unit)? = null
//) {
//    ConstraintLayout(
//        Modifier
//            .fillMaxWidth()
//            .height(44.dp + statusBarHeightDp())
//    ) {
//        val (leading, title) = createRefs()
//        val safeTop = createGuidelineFromTop(statusBarHeightDp())
//        IconButton(
//            onClick = onLeadingClick,
//            Modifier
//                .constrainAs(leading) {
//                    start.linkTo(parent.start)
//                    top.linkTo(safeTop)
//                    bottom.linkTo(parent.bottom)
//                }
//                .wrapContentSize(),
//        ) {
//            Image(painter = painterResource(id = R.drawable.ic_back), contentDescription = "")
//        }
//        Text(
//            text = titleText,
//            modifier = Modifier
//                .constrainAs(title) {
//                    start.linkTo(parent.start)
//                    end.linkTo(parent.end)
//                    top.linkTo(safeTop)
//                    bottom.linkTo(parent.bottom)
//                }
//                .doubleClick {
//                    onTitleDoubleClick?.invoke()
//                },
//            textAlign = TextAlign.Justify,
//            style = HMTStyle.title_2.fg1(),
//        )
//    }
//}