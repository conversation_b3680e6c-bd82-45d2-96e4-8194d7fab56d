package com.harman.radio

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.harman.BottomPopUpDialog
import com.harman.bar.app.databinding.DialogAdjustFrequencyBinding
import com.harman.command.partybox.gatt.radio.RadioInfo
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.NumberFormat

class AdjustFrequencyDialog(
    context: Context,
    private val radioInfo: RadioInfo?
) : BottomPopUpDialog(context = context) {

    private lateinit var binding: DialogAdjustFrequencyBinding
    private var mIEventListener: IEventListener? = null
    private val _frequencyNum = MutableLiveData<String>()
    val frequencyNum: LiveData<String>
        get() = _frequencyNum

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCancelable(true)

        binding = DialogAdjustFrequencyBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this@AdjustFrequencyDialog
        binding.dialog = this@AdjustFrequencyDialog
        dataBinding(binding)

        setContentView(binding.root)
    }

    private fun dataBinding(binding: DialogAdjustFrequencyBinding) {
        radioInfo?.let { itRadioInfo ->
            itRadioInfo.setting?.let { itSetting ->
                val scaleValueStep = 0.5f
                itSetting.manualSeekStep?.let { itManualSeekStep ->
                    val scaleItemSize = scaleValueStep / itManualSeekStep
                    binding.loopScaleView.setScaleItemSize(scaleItemSize.toInt())
                }

                itSetting.startFrequency?.let {
                    binding.loopScaleView.setMinScaleValue(it)
                }

                itSetting.endFrequency?.let {
                    binding.loopScaleView.setMaxScaleValue(it)
                }
            }

            itRadioInfo.currentStation?.let { itStation ->
                itStation.frequency?.let { itFrequency ->
                    _frequencyNum.value = formatFrequency(itFrequency)
                    binding.loopScaleView.setCurrentScale(itFrequency)
                }
            }

            binding.loopScaleView.setOnValueChangeListener(object : LoopScaleView.OnValueChangeListener {
                override fun onValueChange(newValue: Float) {
                    val bd = BigDecimal(newValue.toDouble())
                    val toFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                    val radioInfoTemp = itRadioInfo.deepCopy()
                    radioInfoTemp.currentStation?.let { itStation ->
                        itStation.frequency = toFloat
                        _frequencyNum.value = formatFrequency(toFloat)
                        mIEventListener?.onScaleSlide(itStation)
                    }

                }

            })

        }
    }

    fun setOnEventListener(listener: IEventListener) {
        mIEventListener = listener
    }

    fun onFreqHandyMinusClick() {
        radioInfo?.let { itRadioInfo ->
            val radioInfoTemp = itRadioInfo.deepCopy()
            radioInfoTemp.currentStation?.let { itStation ->
                val frequencyStr = binding.tvFrequencyNumber.text.toString()
                val startFrequency = radioInfoTemp.setting?.startFrequency!!
                val endFrequency = radioInfoTemp.setting?.endFrequency!!
                val manualSeekStep = radioInfoTemp.setting?.manualSeekStep!!

                if (frequencyStr.isNotBlank()) {
                    val bd = BigDecimal(frequencyStr)
                    var frToFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                    if (frToFloat <= startFrequency) {
                        frToFloat = endFrequency + manualSeekStep
                    }
                    val adjustedFrequency = frToFloat - manualSeekStep
                    val bigDecimal = BigDecimal(adjustedFrequency.toDouble())
                    val toFloat = bigDecimal.setScale(2, RoundingMode.HALF_UP).toFloat()
                    itStation.frequency = toFloat
                    _frequencyNum.value = formatFrequency(toFloat)
                    binding.loopScaleView.setCurrentScale(toFloat)
                    mIEventListener?.onMinusClick(itStation)
                }
            }
        }

    }

    fun onFreqHandyPlusClick() {
        radioInfo?.let { itRadioInfo ->
            val radioInfoTemp = itRadioInfo.deepCopy()
            radioInfoTemp.currentStation?.let { itStation ->
                val frequencyStr = binding.tvFrequencyNumber.text.toString()
                val startFrequency = radioInfoTemp.setting?.startFrequency!!
                val endFrequency = radioInfoTemp.setting?.endFrequency!!
                val manualSeekStep = radioInfoTemp.setting?.manualSeekStep!!

                if (frequencyStr.isNotBlank()) {
                    val bd = BigDecimal(frequencyStr)
                    var frToFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                    if (frToFloat >= endFrequency) {
                        frToFloat = startFrequency - manualSeekStep
                    }
                    val adjustedFrequency = frToFloat + manualSeekStep
                    val bigDecimal = BigDecimal(adjustedFrequency.toDouble())
                    val toFloat = bigDecimal.setScale(2, RoundingMode.HALF_UP).toFloat()
                    itStation.frequency = toFloat
                    _frequencyNum.value = formatFrequency(toFloat)
                    binding.loopScaleView.setCurrentScale(toFloat)
                    mIEventListener?.onPlusClick(itStation)
                }
            }
        }

    }

    fun onOkClick() {
        dismiss()
    }

    private fun formatFrequency(frequency: Float): String {
        val defaultLocaleFormatter = NumberFormat.getNumberInstance()
        defaultLocaleFormatter.minimumFractionDigits = 2
        defaultLocaleFormatter.maximumFractionDigits = 2
        val formattedString = defaultLocaleFormatter.format(frequency)
        return formattedString
    }

    interface IEventListener {
        fun onScaleSlide(newStation: RadioInfo.Station)

        fun onMinusClick(newStation: RadioInfo.Station)

        fun onPlusClick(newStation: RadioInfo.Station)
    }

    companion object {
        private const val TAG = "AdjustFrequencyDialog"
    }

}