package com.harman.radio

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ItemPresetStationBinding
import com.harman.command.partybox.gatt.radio.RadioInfo
import java.text.NumberFormat


class SetPresetStationsAdapter(
    private val context: Context,
    private var dataList: MutableList<RadioInfo.Station>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val ITEM_1 = 1
    private var mOnItemClickListener: OnItemClickListener? = null

    //选择的位置(-1则代表默认没有选中)
    private var selectedPosition = -1

    //临时记录上次选择的位置
    private var temp = -1

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.mOnItemClickListener = listener
    }

    fun updateSelectedPosition(selectedType: RadioInfo.Station?) {
        val elementToIndexMap = dataList.withIndex().associateBy({ it.value }, { it.index })
        selectedPosition = dataList.indexOf(selectedType)
        notifyDataSetChanged()
    }

    fun updateDataList(dataList: MutableList<RadioInfo.Station>) {
        this.dataList = dataList
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when (viewType) {
            ITEM_1 -> {
                val inflateView = LayoutInflater.from(context).inflate(R.layout.item_preset_station, parent, false)
                return ViewHolder1(inflateView)

            }

            else -> {
                val inflateView = LayoutInflater.from(context).inflate(R.layout.item_preset_station, parent, false)
                return ViewHolder1(inflateView)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ViewHolder1 -> {
                updateViewHolder1(holder, position)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        super.getItemViewType(position)
        return ITEM_1
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    private fun updateViewHolder1(holder: ViewHolder1, position: Int) {

        holder.binding?.tvIndex?.text = (position + 1).toString()
        dataList.elementAtOrNull(position)?.let { itStation ->
            holder.binding?.tvStationName?.setTextColor(ContextCompat.getColor(context, R.color.fg_primary))

            if (isInvalidStation(itStation)) {
                holder.binding?.tvStationName?.text = holder.binding?.tvStationName?.resources?.getString(R.string.no_preset)
                holder.binding?.tvStationName?.setTextColor(ContextCompat.getColor(context, R.color.fg_disabled))
                holder.binding?.tvRadioType?.visibility = View.GONE
            } else {
                when (itStation.radioType) {
                    RadioInfo.Type.FMRadio -> {
                        itStation.frequency?.let { itFrequency ->
                            val defaultLocaleFormatter = NumberFormat.getNumberInstance()
                            defaultLocaleFormatter.minimumFractionDigits = 2
                            defaultLocaleFormatter.maximumFractionDigits = 2
                            val formattedString = defaultLocaleFormatter.format(itFrequency)
                            holder.binding?.tvStationName?.text = formattedString
                        }
                        holder.binding?.tvRadioType?.visibility = View.VISIBLE
                        holder.binding?.tvRadioType?.text = holder.binding?.tvRadioType?.resources?.getString(R.string.fm)
                    }

                    RadioInfo.Type.DABRadio -> {
                        itStation.stationName?.let { stationName ->
                            holder.binding?.tvStationName?.text = itStation.stationName ?: itStation.stationID?.toString()
                        }
                        holder.binding?.tvRadioType?.visibility = View.VISIBLE
                        holder.binding?.tvRadioType?.text = holder.binding?.tvRadioType?.resources?.getString(R.string.dab)
                    }

                    else -> {
                        itStation.frequency?.let { itFrequency ->
                            val defaultLocaleFormatter = NumberFormat.getNumberInstance()
                            defaultLocaleFormatter.minimumFractionDigits = 2
                            defaultLocaleFormatter.maximumFractionDigits = 2
                            val formattedString = defaultLocaleFormatter.format(itFrequency)
                            holder.binding?.tvStationName?.text = formattedString
                        }
                        holder.binding?.tvRadioType?.visibility = View.GONE
                    }
                }
            }

        } ?: let {
            holder.binding?.tvStationName?.text = holder.binding?.tvStationName?.resources?.getString(R.string.no_preset)
            holder.binding?.tvStationName?.setTextColor(ContextCompat.getColor(context, R.color.fg_disabled))
            holder.binding?.tvRadioType?.visibility = View.GONE
        }

        holder.itemView.isSelected = holder.layoutPosition == selectedPosition
        holder.binding?.rbSelect?.isSelected = holder.layoutPosition == selectedPosition

        holder.binding?.itemRootLayout?.setOnClickListener {
            holder.binding?.rbSelect?.isSelected = true
            //将旧的位置保存下来，用于后面把旧的位置颜色变回来
            temp = selectedPosition
            //设置新的位置
            selectedPosition = holder.layoutPosition
            //更新旧位置
            notifyItemChanged(temp)
            mOnItemClickListener?.onItemClick(it, holder.layoutPosition, dataList.elementAt(holder.layoutPosition))
        }

        holder.binding?.rbSelect?.setOnClickListener {
            holder.binding?.rbSelect?.isSelected = true
            //将旧的位置保存下来，用于后面把旧的位置颜色变回来
            temp = selectedPosition
            //设置新的位置
            selectedPosition = holder.layoutPosition
            //更新旧位置
            notifyItemChanged(temp)
            mOnItemClickListener?.onItemClick(it, holder.layoutPosition, dataList.elementAt(holder.layoutPosition))
        }

    }

    private fun isInvalidStation(station: RadioInfo.Station): Boolean {
        val isIDNull = station.stationID == null
        val isNameNull = station.stationName == null
        val isFrequencyNull = station.frequency == null
        val isStateNull = station.state == null
        val isRadioTypeNull = station.radioType == null
        val isScanStatusNull = station.stationScanStatus == null
        val isInvalidStation = isIDNull && isNameNull && isFrequencyNull &&
                isStateNull && isRadioTypeNull && isScanStatusNull

        return isInvalidStation
    }

    inner class ViewHolder1(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var binding: ItemPresetStationBinding? = DataBindingUtil.bind(itemView)
    }

    interface OnItemClickListener {
        fun onItemClick(view: View, position: Int, type: RadioInfo.Station)
    }

    companion object {
        val TAG = SetPresetStationsAdapter::class.simpleName
    }

}