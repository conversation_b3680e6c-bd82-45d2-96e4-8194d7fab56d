package com.harman.radio

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Shader
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.Vibrator
import android.os.VibratorManager
import android.util.AttributeSet
import android.util.TypedValue
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import android.view.View
import android.view.ViewParent
import android.widget.ScrollView
import android.widget.Scroller
import androidx.core.widget.NestedScrollView
import com.harman.bar.app.R
import com.harman.log.Logger
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.NumberFormat
import java.util.concurrent.ScheduledExecutorService
import kotlin.math.abs
import kotlin.math.max


class LoopScaleView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    var available = true
    private var mOnValueChangeListener: OnValueChangeListener? = null
    private var mOnAddOrMinusScaleListener: OnAddOrMinusScaleListener? = null
    private var mScrollView: ViewParent? = null

    //中间游标的画笔
    private lateinit var cursorLinePaint: Paint

    //刻度线的画笔
    private lateinit var scaleLinePaint: Paint

    //刻度文字的画笔
    private lateinit var scaleTextPaint: Paint
    private lateinit var unitTextPaint: Paint

    //尺子控件总宽度
    private var viewWidth = 0f

    //尺子控件总高度
    private var viewHeight = 0f

    //中间的标识图片
    private var cursorBitmap: Bitmap? = null

    //标签的位置
    private var cursorLocation = 0f

    //未设置标识图片时默认绘制一条线作为标尺的线的颜色
    private var cursorColor = Color.parseColor("#92F1BE")

    //大刻度线宽，默认为3
    private val cursorWidth = dip2px(3f)
    private val cursorHeight = dip2px(54f)
    private lateinit var cursorRectF: RectF // 刻度区域
    private var cursorRadius = dip2px(10f) // 光标圆角

    //刻度区域顶部的Y
    private val scaleRectFTopY = dip2px(16f)
    private val scaleRectFBottomY = dip2px(56f)
    private val gradientRectFWidth = dip2px(56f)

    //刻度高度，默认值为40
    private val scaleHeight = scaleRectFBottomY - scaleRectFTopY

    //小刻度线宽，默认为1
    private val shortScaleWidth = dip2px(1f)
    private val longScaleWidth = dip2px(2f)
    private val shortScaleHeight = dip2px(24f)
    private val longScaleHeight = dip2px(40f)

    //设置屏幕宽度内最多显示的大刻度数，默认为5个
    private var showItemSize = 5

    //标尺开始位置
    private var currLocation = 0f
    private var currentScale = 0f

    //刻度表的最大值，默认为200
    private var maxScaleValue = 108f

    //刻度表的最小值，默认为0
    private var minScaleValue = 87.5f

    //scale value 之间的步长，比如 87.50-88.00，则 valueStep = 0.5
    private var scaleValueStep = 0.5f

    // 刻度之间的个数。比如87.5-88.0之间是10个刻度还是5个
    private var scaleItemSize = 10

    //一个刻度表示的值的大小
    private var oneScaleValue: Float = scaleValueStep / scaleItemSize

    // 刻度尺的刻度间隔
    private var scaleSpace = dip2px(5f)

    // 刻度尺的刻度数量
    private var scaleCount = ((maxScaleValue - minScaleValue) / oneScaleValue).toInt()

    //刻度的颜色刻度色，默认为灰色
    private var scaleColor = Color.parseColor("#92939B")
    private var unitTextColor = Color.parseColor("#92939B")

    //刻度文字的颜色，默认为灰色
    private var scaleTextColor = Color.parseColor("#D4D4D7")

    //刻度文字的大小,默认为24px
    private val scaleTextSize = 24

    //手势解析器
    private var mGestureDetector: GestureDetector? = null

    //处理惯性滚动
    private var mScroller: Scroller? = null

    //惯性滑动时用于查询位置状态
    private var mScheduler: ScheduledExecutorService? = null

    //scroller 滚动的最大值
    private var maxX = 0

    //scroller 滚动的最小值
    private var minX = 0

    private var minViewHeight = dip2px(84f)
    private var marginTop = dip2px(16f)

    private var minusIconBitmap: Bitmap? = null // 减号图片
    private var addIconBitmap: Bitmap? = null // 加号图片

    private lateinit var scaleRectF: RectF // 刻度区域
    private lateinit var minusIconBitmapRectF: RectF // 减号图片区域
    private lateinit var minusIconBitmapPaint: Paint // 减号图片区域画笔
    private lateinit var addIconBitmapRectF: RectF // 加号图片区域
    private lateinit var addIconBitmapPaint: Paint // 加号图片区域画笔

    private lateinit var scaleTextRectF: RectF // 刻度文字区域
    private lateinit var unitTextRectF: RectF // 刻度单位区域

    private lateinit var leftGradientRectF: RectF // 左侧刻度渐变区域
    private lateinit var rightGradientRectF: RectF // 右侧刻度渐变区域
    private lateinit var leftGradientRectFPaint: Paint //左侧刻度渐变区域画笔
    private lateinit var rightGradientRectFPaint: Paint //右侧刻度渐变区域画笔

    private lateinit var vibrator: Vibrator //震动反馈
    private var handler: Handler = Handler(Looper.getMainLooper())
    private val LONG_PRESS_TIME: Long = 1000 // 1秒
    private val isLongPress = false
    private val PRESS_LEFT = "press_left"
    private val PRESS_RIGHT = "press_right"
    private var pressDirection = PRESS_LEFT
    private var pressDownTime: Long = 0 // 记录按下的时间
    private lateinit var longPressRunnable: Runnable
    private val SCROLL_TIMEOUT: Long = 300 // 300ms 内没有滚动则认为滚动停止
    private var scrollStoppedRunnable: Runnable? = null

    /**
     * 滑动手势处理
     */
    private val gestureListener: SimpleOnGestureListener = object : SimpleOnGestureListener() {
        override fun onDown(e: MotionEvent): Boolean {
            Logger.d(TAG, "onGestureEvent onDown---: ${e.action}")
            return true
        }

        /**
         * MotionEvent.ACTION_DOWN 0
         * MotionEvent.ACTION_UP 1
         * MotionEvent.ACTION_MOVE 2
         * MotionEvent.ACTION_CANCEL 3
         * @param e1
         * @param e2
         * @param distanceX
         * @param distanceY
         * @return
         */
        override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
            Logger.i(TAG, "onGestureEvent onScroll---e1:${e1?.action}, e2:${e2.action}")
            Logger.d(TAG, "onGestureEvent onScroll---distanceX: $distanceX")
            scrollView(distanceX)
            return true
        }

        /**
         * 用户按下触摸屏、快速移动后松开
         * MotionEvent.ACTION_DOWN 0
         * MotionEvent.ACTION_UP 1
         * MotionEvent.ACTION_MOVE 2
         * MotionEvent.ACTION_CANCEL 3
         * @param e1 第1个ACTION_DOWN MotionEvent
         * @param e2 最后一个ACTION_MOVE MotionEvent
         * @param velocityX X轴上的移动速度，像素/秒
         * @param velocityY Y轴上的移动速度，像素/秒
         * @return
         */
        override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
            Logger.d(TAG, "onGestureEvent onFling---e1:${e1?.action}, e2:${e2.action}")
            if (!mScroller!!.computeScrollOffset()) {
                mScroller?.fling(currLocation.toInt(), 0, (-velocityX / 1.5).toInt(), 0, minX, maxX, 0, 0)
                setNextMessage(0)
            }
            return true
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            Logger.d(TAG, "onGestureEvent onSingleTapUp---: ${e.action}")
            return super.onSingleTapUp(e)
        }

        override fun onLongPress(e: MotionEvent) {
            Logger.d(TAG, "onGestureEvent onLongPress---: ${e.action}")
            super.onLongPress(e)
        }
    }

    init {
        minusIconBitmap = BitmapFactory.decodeResource(resources, R.drawable.ic_frequency_handy_minus)
        addIconBitmap = BitmapFactory.decodeResource(resources, R.drawable.ic_frequency_handy_plus)

        scaleLinePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = scaleColor
            strokeWidth = shortScaleWidth
            strokeCap = Paint.Cap.ROUND
        }

        scaleTextPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = scaleColor
            textSize = dip2px(12f)
            textAlign = Paint.Align.CENTER
        }

        unitTextPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = unitTextColor
            textSize = dip2px(14f)
            textAlign = Paint.Align.CENTER
        }

        cursorLinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        cursorLinePaint.strokeWidth = cursorWidth
        cursorLinePaint.strokeCap = Paint.Cap.ROUND
        cursorLinePaint.setColor(cursorColor)

        leftGradientRectFPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        minusIconBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

        rightGradientRectFPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        addIconBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

        cursorRectF = RectF()
        leftGradientRectF = RectF()
        rightGradientRectF = RectF()
        minusIconBitmapRectF = RectF()
        addIconBitmapRectF = RectF()

        val ta = context.obtainStyledAttributes(attrs, R.styleable.LoopScaleView)
        showItemSize = ta.getInteger(R.styleable.LoopScaleView_maxShowItem, showItemSize)
        maxScaleValue = ta.getFloat(R.styleable.LoopScaleView_maxScaleValue, maxScaleValue)
        minScaleValue = ta.getFloat(R.styleable.LoopScaleView_minScaleValue, minScaleValue)
        scaleValueStep = ta.getFloat(R.styleable.LoopScaleView_scaleValueStep, scaleValueStep)
        scaleItemSize = ta.getInteger(R.styleable.LoopScaleView_scaleItemSize, scaleItemSize)
        scaleSpace = ta.getFloat(R.styleable.LoopScaleView_scaleSpace, scaleSpace)
        oneScaleValue = ta.getFloat(R.styleable.LoopScaleView_oneScaleValue, oneScaleValue)
        scaleColor = ta.getColor(R.styleable.LoopScaleView_scaleColor, scaleColor)
        scaleTextColor = ta.getColor(R.styleable.LoopScaleView_scaleTextColor, scaleTextColor)
        cursorColor = ta.getColor(R.styleable.LoopScaleView_cursorColor, cursorColor)
        val cursorBitMapId = ta.getResourceId(R.styleable.LoopScaleView_cursorBitmap, -1)
        if (cursorBitMapId != -1) {
            cursorBitmap = BitmapFactory.decodeResource(resources, cursorBitMapId)
        }
        ta.recycle()

        oneScaleValue = scaleValueStep / scaleItemSize
        scaleCount = ((maxScaleValue - minScaleValue) / oneScaleValue).toInt()
        scaleSpace = when (scaleItemSize) {
            10 -> dip2px(5f)
            5 -> dip2px(9f)
            else -> dip2px(6f)
        }

        mScroller = Scroller(context)

//        mScheduler = Executors.newScheduledThreadPool(2)
        mGestureDetector = GestureDetector(context, gestureListener)

        vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            @Suppress("DEPRECATION") context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }

        longPressRunnable = Runnable {
            // 执行长按后的操作
            onPress(pressDirection, needAddListener = false)
            handler.postDelayed(longPressRunnable, 50)
        }

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val minimumWidth = suggestedMinimumWidth
        val minimumHeight = suggestedMinimumHeight
        val width: Int = measureWidth(minimumWidth, widthMeasureSpec)
        val height: Int = measureHeight(minimumHeight, heightMeasureSpec)
        viewWidth = width.toFloat()
        viewHeight = MeasureSpec.getSize(heightMeasureSpec).toFloat()
        viewHeight = height.toFloat().coerceAtLeast(minViewHeight) //Math.max
//        viewHeight = height.toFloat().coerceAtMost(minViewHeight) //Math.min
        cursorLocation = viewWidth / 2f
        maxX = (scaleCount * scaleSpace).toInt()
        minX = -maxX

    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w.toFloat()
        viewHeight = h.toFloat()
        cursorLocation = viewWidth / 2f
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制反向的一个刻度尺,右--->左
        for (i in 0 until scaleCount) {
            drawScale(canvas, i, -1)
        }
        //绘制正向的一个刻度尺，左--->右
        for (i in 0 until scaleCount) {
            drawScale(canvas, i, 1)
        }

        drawIcons(canvas)
//        drawUnitText(canvas)
        drawCursor(canvas)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        var vp = parent
        do {
            if (vp is NestedScrollView) {
                mScrollView = vp
                break
            }
            vp = vp!!.parent
        } while (vp != null)

        if (!available) {
            Logger.d(TAG, "can not seek on parent scroll")
            //防止出现在本view和父View同时点击，longPressRunnable未移除导致onPress不断调用的问题
            handler.removeCallbacks(longPressRunnable)
            mScrollView?.requestDisallowInterceptTouchEvent(false)
            return false
        } else {
            Logger.d(TAG, "requestDisallowInterceptTouchEvent true")
            mScrollView?.requestDisallowInterceptTouchEvent(true)
        }

        when (event!!.action) {
            MotionEvent.ACTION_DOWN -> {
                pressDownTime = System.currentTimeMillis()
                val isLeft = isClickOnLeftIcon(event.x, event.y)
                val isRight = isClickOnRightIcon(event.x, event.y)

                if (isLeft || isRight) {
                    Logger.i(TAG, "onTouchEvent ACTION_DOWN----${if (isLeft) "ClickOnLeftIcon" else "ClickOnRightIcon"}")
                    pressDirection = if (isLeft) PRESS_LEFT else PRESS_RIGHT
                    handler.postDelayed(longPressRunnable, LONG_PRESS_TIME)
                }

            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                val currentTime = System.currentTimeMillis()
                val isShortPress = currentTime - pressDownTime < LONG_PRESS_TIME

                when {
                    isClickOnLeftIcon(event.x, event.y) -> {
                        Logger.i(TAG, "onTouchEvent ACTION_UP----ClickOnLeftIcon")
                        pressDirection = PRESS_LEFT
                        if (isShortPress) onPress(PRESS_LEFT, needAddListener = true)
                    }

                    isClickOnRightIcon(event.x, event.y) -> {
                        Logger.i(TAG, "onTouchEvent ACTION_UP----ClickOnRightIcon")
                        pressDirection = PRESS_RIGHT
                        if (isShortPress) onPress(PRESS_RIGHT, needAddListener = true)
                    }

                    else -> getIntegerPosition()
                }

                Logger.d(TAG, "onTouchEvent isShortPress: $isShortPress, currentScale: $currentScale")

                handler.removeCallbacks(longPressRunnable)
                if (!isShortPress) {
                    onLongPressFinish(pressDirection)
                }

            }

        }
        return mGestureDetector?.onTouchEvent(event)!!
    }

    private fun onPress(pressDirection: String, needAddListener: Boolean) {
        when (pressDirection) {
            PRESS_LEFT -> {
                Logger.i(TAG, "onPress---halfScaleValue PRESS_LEFT pre currentScale:$currentScale")
                currentScale -= oneScaleValue
                if (currentScale < minScaleValue) {
                    currentScale = maxScaleValue
                }
                Logger.i(TAG, "onPress---halfScaleValue PRESS_LEFT after currentScale:$currentScale")
                setCurrentScale(currentScale)
                val bd = BigDecimal(currentScale.toDouble())
                val toFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                if (needAddListener) {
                    mOnAddOrMinusScaleListener?.onMinus(toFloat)
                }
            }

            PRESS_RIGHT -> {
                Logger.i(TAG, "onPress---halfScaleValue PRESS_RIGHT pre currentScale:$currentScale")
                currentScale += oneScaleValue
                if (currentScale > maxScaleValue) {
                    currentScale = minScaleValue
                }
                Logger.i(TAG, "onPress---halfScaleValue PRESS_RIGHT after currentScale:$currentScale")
                setCurrentScale(currentScale)
                val bd = BigDecimal(currentScale.toDouble())
                val toFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                if (needAddListener) {
                    mOnAddOrMinusScaleListener?.onAdd(toFloat)
                }
            }
        }
    }

    private fun onLongPressFinish(pressDirection: String) {
        when (pressDirection) {
            PRESS_LEFT -> {
                if (currentScale == minScaleValue) {
                    currentScale = maxScaleValue
                }
                Logger.i(TAG, "onPress---halfScaleValue PRESS_LEFT currentScale:$currentScale")
                setCurrentScale(currentScale)
                val bd = BigDecimal(currentScale.toDouble())
                val toFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                mOnAddOrMinusScaleListener?.onMinus(toFloat)
            }

            PRESS_RIGHT -> {
                if (currentScale == maxScaleValue) {
                    currentScale = minScaleValue
                }
                Logger.i(TAG, "onPress---halfScaleValue PRESS_RIGHT currentScale:$currentScale")
                setCurrentScale(currentScale)
                val bd = BigDecimal(currentScale.toDouble())
                val toFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                mOnAddOrMinusScaleListener?.onAdd(toFloat)
            }
        }
    }

    /**
     * 滑动View
     *
     * @param distance 滑动的距离
     */
    private fun scrollView(distance: Float) {
        currLocation += distance
        //设置新的位置
        Logger.d(TAG, "scrollView---currLocation: $currLocation")
        setCurrLocation(currLocation)
        // 每次滚动时，移除之前的回调
        scrollStoppedRunnable?.let {
            handler.removeCallbacks(it)
        }
        // 设置新的回调
        scrollStoppedRunnable = Runnable {
            onScrollStopped()
        }
        // 延迟执行回调
        scrollStoppedRunnable?.let {
            handler.postDelayed(it, SCROLL_TIMEOUT)
        }
    }

    /**
     * 获取当前位置最近的整数刻度
     */
    private fun getIntegerPosition() {
        var currentItem: Int = (currLocation / scaleSpace).toInt()
        Logger.d(TAG, "getIntegerPosition---currentItem1: $currentItem")
        if (currentItem < 0) {
            if (abs(currentItem) in 0..scaleItemSize) {
                val halfCurrentItem = scaleItemSize / 2
                currentItem = if (abs(currentItem) >= halfCurrentItem) {
                    -scaleItemSize
                } else {
                    0
                }
            }
        }

        val fraction: Float = currLocation - currentItem * scaleSpace
        Logger.i(TAG, "getIntegerPosition---currentItem2: $currentItem")
        Logger.d(TAG, "getIntegerPosition---fraction: $fraction")
        currLocation = if (abs(fraction.toDouble()) > 0.5 * scaleSpace) {
            if (currentItem == 0 || currentItem == -scaleItemSize) {
                (currentItem * scaleSpace).toFloat()
            } else {
                if (fraction < 0) {
                    ((currentItem - 1) * scaleSpace).toFloat()
                } else {
                    ((currentItem + 1) * scaleSpace).toFloat()
                }
            }

        } else {
            (currentItem * scaleSpace).toFloat()
        }
        Logger.i(TAG, "getIntegerPosition---currLocation: $currLocation")
        setCurrLocation(currLocation)
    }

    /**
     * 设置当前游标所在的值
     *
     * @param currLocation 当前游标所在的值
     */
    private fun setCurrLocation(currLocation: Float) {
        this.currLocation = currLocation
        var currentScaleAmount: Int = (currLocation / scaleSpace).toInt()
        var currentScale: Float = (currLocation / scaleSpace).toInt() * oneScaleValue

        val bd = BigDecimal(currentScale.toDouble())
        val toFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()

        Logger.i(TAG, "setCurrLocation---currentScaleAmount: $currentScaleAmount, currentScale1: $toFloat")
        if (currentScale < 0) {
            currentScale = (maxScaleValue - Math.abs(currentScale) + 0.5).toFloat()
        } else {
            currentScale = minScaleValue + Math.abs(currentScale)
        }
        Logger.d(TAG, "setCurrLocation---currentScaleAmount: $currentScaleAmount, currentScaleUI: $currentScale")

//        mOnValueChangeListener?.onValueChange(currentScale)
        val halfScaleValue = (maxScaleValue + (maxScaleValue - oneScaleValue + 0.5)) / 2

        val halfBD = BigDecimal(halfScaleValue.toDouble())
        val halfToFloat = halfBD.setScale(2, RoundingMode.HALF_UP).toFloat()
        Logger.i(TAG, "setCurrLocation---halfScaleValue: $halfToFloat, currentScaleUI1: $currentScale")
        if (currentScale > maxScaleValue) {
            handler.postDelayed({
                if (currentScale > halfToFloat) {
                    currentScale = minScaleValue
                } else {
                    currentScale = maxScaleValue
                }

//                setCurrentScale(currentScale)
            }, 500)

        }
        this.currentScale = currentScale
        Logger.d(TAG, "setCurrLocation---halfScaleValue: $halfToFloat, currentScaleUI2: $currentScale")
        invalidate()
    }

    private fun drawScale(canvas: Canvas, value: Int, type: Int) {
        cursorLocation = viewWidth / 2
//        Logger.d("drawScale---type: $type, currLocation:$currLocation, cursorLocation: $cursorLocation, viewWidth:$viewWidth")
        if (currLocation + cursorLocation >= scaleCount * scaleSpace) {
            currLocation = -cursorLocation

            val speed = mScroller?.currVelocity
            mScroller?.fling(currLocation.toInt(), 0, speed!!.toInt(), 0, minX, maxX, 0, 0)
            setNextMessage(0)

        } else if (currLocation - cursorLocation <= -scaleCount * scaleSpace) {
            currLocation = cursorLocation

            val speed = mScroller?.currVelocity
            mScroller?.fling(currLocation.toInt(), 0, speed!!.toInt(), 0, minX, maxX, 0, 0)
            setNextMessage(0)

        }

        val location: Float = cursorLocation - currLocation + value * scaleSpace * type
        if (value % scaleItemSize == 0) {
            val longScaleLineY1 = scaleRectFTopY
            val longScaleLineY2 = longScaleLineY1 + longScaleHeight
            scaleLinePaint.strokeWidth = longScaleWidth
            canvas.drawLine(location, longScaleLineY1, location, longScaleLineY2, scaleLinePaint)
            var scaleTextValue = 0f
            if (type < 0) {
                scaleTextValue = maxScaleValue - value * oneScaleValue
                if (value.toFloat() == maxScaleValue) { //左闭右开区间，不取最大值
                    scaleTextValue = minScaleValue
                }
            } else {
                scaleTextValue = minScaleValue + value * oneScaleValue
            }


            if (scaleTextValue != maxScaleValue) {
                if (type < 0) {
                    scaleTextValue += 0.5f
                }
                val defaultLocaleFormatter = NumberFormat.getNumberInstance()
                defaultLocaleFormatter.minimumFractionDigits = 2
                defaultLocaleFormatter.maximumFractionDigits = 2
                val defaultFormattedString = defaultLocaleFormatter.format(scaleTextValue)
                canvas.drawText(defaultFormattedString, location, height.toFloat(), scaleTextPaint)
            }

        } else {
            val shortScaleLineY1 = scaleRectFTopY + (longScaleHeight - shortScaleHeight) / 2
            val shortScaleLineY2 = shortScaleLineY1 + shortScaleHeight
            scaleLinePaint.strokeWidth = shortScaleWidth
            canvas.drawLine(location, shortScaleLineY1, location, shortScaleLineY2, scaleLinePaint)
        }
    }

    /**
     * 绘制指示标签
     *
     * @param canvas 绘制控件的画布
     */
    private fun drawCursor(canvas: Canvas) {
        cursorLocation = (width / 2).toFloat() //屏幕显示Item 数的中间位置

        cursorRectF.left = cursorLocation - cursorWidth / 2
        cursorRectF.top = scaleRectFTopY - (cursorHeight - scaleHeight) / 2
        cursorRectF.right = cursorLocation + cursorWidth / 2
        cursorRectF.bottom = cursorRectF.top + cursorHeight

        if (cursorBitmap == null) { //绘制一条竖线
//            canvas.drawRoundRect(cursorRectF, cursorRadius, cursorRadius, cursorLinePaint)
            val x1 = cursorLocation
            val y1 = scaleRectFTopY - (cursorHeight - scaleHeight) / 2
            val x2 = cursorLocation
            val y2 = y1 + cursorHeight
            canvas.drawLine(x1, y1, x2, y2, cursorLinePaint)
        } else { //绘制标识图片
            val left = cursorLocation - cursorBitmap!!.width / 2
            val top = (scaleRectFTopY - (cursorBitmap!!.height - scaleHeight) / 2).toFloat()
            val right = cursorLocation + cursorBitmap!!.width / 2
            val bottom = top + cursorBitmap!!.height
            val rectF = RectF(left, top, right, bottom)
            canvas.drawBitmap(cursorBitmap!!, null, rectF, cursorLinePaint)
        }
    }

    /**
     * 绘制加减图标
     *
     * @param canvas
     */
    private fun drawIcons(canvas: Canvas) {
        leftGradientRectF.left = 0f
        leftGradientRectF.top = 0f
        leftGradientRectF.right = gradientRectFWidth
        leftGradientRectF.bottom = viewHeight

        // 定义左侧渐变颜色数组
        val leftColors = intArrayOf(
            Color.parseColor("#2A2B2E"), // 起始颜色
            Color.parseColor("#2A2B2E"), // 中间颜色（保持不变）
            Color.parseColor("#2A2B2E"), // 中间颜色（保持不变）
            Color.TRANSPARENT // 结束颜色（透明）
        )

        // 定义左侧渐变位置数组
        val leftPositions = floatArrayOf(
            0f, // 起始位置
            0.4f, // 中间位置
            0.6f, // 中间位置
            1f // 结束位置
        )

        // 创建一个LinearGradient对象，定义渐变的起始和结束颜色
        val leftShader = LinearGradient(
            0f, // 渐变开始的X坐标
            0f, // 渐变开始的Y坐标
            leftGradientRectF.right, // 渐变结束的X坐标
            0f, // 渐变结束的Y坐标
            leftColors, leftPositions, // 与颜色数组对应的相对位置数组
            Shader.TileMode.CLAMP // Shader的平铺模式
        )

        // 将Shader设置到Paint对象上
        leftGradientRectFPaint.shader = leftShader
        // 在Canvas上绘制矩形，应用渐变效果
        canvas.drawRect(leftGradientRectF, leftGradientRectFPaint)

        rightGradientRectF.left = viewWidth - gradientRectFWidth
        rightGradientRectF.top = 0f
        rightGradientRectF.right = viewWidth
        rightGradientRectF.bottom = viewHeight

        // 定义右侧渐变颜色数组
        val rightColors = intArrayOf(
            Color.TRANSPARENT, // 起始颜色
            Color.parseColor("#2A2B2E"), // 中间颜色（保持不变）
            Color.parseColor("#2A2B2E"), // 中间颜色（保持不变）
            Color.parseColor("#2A2B2E") // 结束颜色（透明）
        )

        // 定义右侧渐变位置数组
        val rightPositions = floatArrayOf(
            0f, // 起始位置
            0.4f, // 中间位置
            0.6f, // 中间位置
            1f // 结束位置
        )

        val rightShader = LinearGradient(
            rightGradientRectF.left, // 渐变开始的X坐标
            0f, // 渐变开始的Y坐标
            rightGradientRectF.right, // 渐变结束的X坐标
            0f, // 渐变结束的Y坐标
            rightColors, rightPositions, // 与颜色数组对应的相对位置数组
            Shader.TileMode.CLAMP // Shader的平铺模式
        )
        rightGradientRectFPaint.shader = rightShader
        canvas.drawRect(rightGradientRectF, rightGradientRectFPaint)

        /*minusIconBitmapRectF.left = dip2px(16f)
        minusIconBitmapRectF.top = scaleRectFTopY
        minusIconBitmapRectF.right = minusIconBitmapRectF.left + dip2px(40f)
        minusIconBitmapRectF.bottom = scaleRectFBottomY
        minusIconBitmap?.let {
            canvas.drawBitmap(it, null, minusIconBitmapRectF, minusIconBitmapPaint)
        }*/

        /*addIconBitmapRectF.left = viewWidth - dip2px(16f) - dip2px(40f)
        addIconBitmapRectF.top = scaleRectFTopY
        addIconBitmapRectF.right = addIconBitmapRectF.left + dip2px(40f)
        addIconBitmapRectF.bottom = scaleRectFBottomY
        addIconBitmap?.let {
            canvas.drawBitmap(it, null, addIconBitmapRectF, addIconBitmapPaint)
        }*/

    }

    private fun drawUnitText(canvas: Canvas) {
        canvas.drawText("MHz", viewWidth - dip2px(16f) - dip2px(20f), height.toFloat(), unitTextPaint)
    }

    fun setMaxScaleValue(maxScaleValue: Float) {
        this.maxScaleValue = maxScaleValue
        invalidate()
    }

    fun setMinScaleValue(minScaleValue: Float) {
        this.minScaleValue = minScaleValue
        invalidate()
    }

    fun setScaleValueRange(minScaleValue: Float, maxScaleValue: Float) {
        this.minScaleValue = minScaleValue
        this.maxScaleValue = maxScaleValue
        invalidate()
    }

    fun setScaleItemSize(scaleItemSize: Int) {
        if (scaleItemSize != 0) {
            this.scaleItemSize = scaleItemSize
            oneScaleValue = scaleValueStep / scaleItemSize
            scaleSpace = when (scaleItemSize) {
                10 -> dip2px(5f)
                5 -> dip2px(9f)
                else -> dip2px(6f)
            }
            invalidate()
        }
    }

    /**
     * 设置当前刻度的位置
     *
     * @param currValue 当前刻度位置，小于最小值时取最小刻度值,大于最大值时取最大值刻度值
     */
    fun setCurrentScale(currValue: Float) {
        val scaleCount = ((maxScaleValue - minScaleValue) / oneScaleValue).toInt()
        val tempValue = currValue.coerceIn(minScaleValue, maxScaleValue)
        currentScale = tempValue
        val halfScaleValue = (maxScaleValue + minScaleValue) / 2
        val type = if (tempValue > halfScaleValue) -1 else 1
        val currentScaleCount = if (tempValue > halfScaleValue) {
            ((maxScaleValue - tempValue + 0.5) / oneScaleValue).toFloat()
        } else {
            (tempValue - minScaleValue) / oneScaleValue
        }

        Logger.i(TAG, "setCurrentScale---halfScaleValue:$halfScaleValue, currValue:$currValue")
        Logger.i(TAG, "setCurrentScale---scaleCount:$scaleCount, currentScaleCount:$currentScaleCount")
        Logger.i(TAG, "setCurrentScale---total scale distance:${scaleCount * scaleSpace}")
        Logger.i(TAG, "setCurrentScale---current scale distance:${currentScaleCount * scaleSpace}")

        currLocation = currentScaleCount * type * scaleSpace
        Logger.i(TAG, "setCurrentScale---currLocation2:$currLocation")
//        mOnValueChangeListener?.onValueChange(currentScale)
        invalidate()
    }

    fun getCurrentScale(): Float {
        return currentScale
    }

    /**
     * 获取一共有多少个刻度
     *
     * @return 总刻度数
     */
    fun getScalesCount(): Int {
        return ((maxScaleValue - minScaleValue) / oneScaleValue).toInt()
    }

    private fun measureWidth(defaultWidth: Int, measureSpec: Int): Int {
        var width = defaultWidth
        val specMode = MeasureSpec.getMode(measureSpec)
        val specSize = MeasureSpec.getSize(measureSpec)
        Logger.d(TAG, "measureWidth---speSize: $specSize")
        when (specMode) {
            MeasureSpec.AT_MOST -> {
                width = specSize
                Logger.i(TAG, "measureWidth----speMode = AT_MOST, defaultWidth: $width")
            }

            MeasureSpec.EXACTLY -> {
                Logger.i(TAG, "measureWidth----speMode = EXACTLY, defaultWidth: $width")
                width = specSize
            }

            MeasureSpec.UNSPECIFIED -> {
                Logger.i(TAG, "measureWidth----speMode = UNSPECIFIED, defaultWidth:$width")
                width = max(defaultWidth.toDouble(), specSize.toDouble()).toInt()
            }
        }
        return width
    }

    private fun measureHeight(defaultHeight: Int, measureSpec: Int): Int {
        var height = defaultHeight
        val specMode = MeasureSpec.getMode(measureSpec)
        val specSize = MeasureSpec.getSize(measureSpec)
        Logger.i(TAG, "measureHeight---speSize: $specSize")
        when (specMode) {
            MeasureSpec.AT_MOST -> {
                height = specSize
                Logger.i(TAG, "measureHeight----speMode = AT_MOST, defaultHeight: $height")
            }

            MeasureSpec.EXACTLY -> {
                height = specSize
                Logger.i(TAG, "measureHeight----speMode = EXACTLY, defaultHeight: $height")
            }

            MeasureSpec.UNSPECIFIED -> {
                height = max(defaultHeight.toDouble(), specSize.toDouble()).toInt()
                Logger.i(TAG, "measureHeight----speMode = UNSPECIFIED, defaultHeight: $height")
            }
        }
        return height
    }

    private fun isClickOnLeftIcon(x: Float, y: Float): Boolean {
        return x in leftGradientRectF.left..leftGradientRectF.right &&
                y in leftGradientRectF.top..leftGradientRectF.bottom
    }

    private fun isClickOnRightIcon(x: Float, y: Float): Boolean {
        return x in rightGradientRectF.left..rightGradientRectF.right &&
                y in rightGradientRectF.top..rightGradientRectF.bottom
    }

    /**
     * dp转px
     */
    private fun dp2px(context: Context, dp: Float): Int {
        val density = context.resources.displayMetrics.density
        return (dp * density + 0.5f).toInt()
    }

    /**
     * px转dp
     */
    private fun px2dp(context: Context, px: Float): Int {
        val density = context.resources.displayMetrics.density
        return (px / density + 0.5f).toInt()
    }

    private fun dip2px(dipValue: Float): Float {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dipValue, resources.displayMetrics)
    }

    private fun setNextMessage(message: Int) {
        animationHandler.removeMessages(0)
        animationHandler.sendEmptyMessage(message)
    }

    // 动画处理
    private val animationHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            mScroller!!.computeScrollOffset()
            val currX = mScroller!!.currX
            val delta = currX - currLocation
            if (delta != 0f) {
                scrollView(delta)
            }
            // 滚动还没有完成
//            if (!mScroller!!.isFinished) {
            if (mScroller?.isFinished == false) {
                sendEmptyMessage(msg.what)
            } else {
                //到整数刻度
                getIntegerPosition()
            }
        }
    }

    fun onScrollStopped() {
        mOnValueChangeListener?.onValueChange(currentScale)
    }

    fun setOnValueChangeListener(onValueChangeListener: OnValueChangeListener) {
        mOnValueChangeListener = onValueChangeListener
    }

    fun setOnAddOrMinusScaleListener(onAddOrMinusScaleListener: OnAddOrMinusScaleListener) {
        mOnAddOrMinusScaleListener = onAddOrMinusScaleListener
    }

    interface OnValueChangeListener {
        fun onValueChange(newValue: Float)
    }

    interface OnAddOrMinusScaleListener {
        fun onAdd(value: Float)
        fun onMinus(value: Float)
    }

    companion object {
        private const val TAG = "LoopScaleView"
    }

}