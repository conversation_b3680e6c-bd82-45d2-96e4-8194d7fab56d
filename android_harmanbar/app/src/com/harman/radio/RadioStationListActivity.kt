package com.harman.radio

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.harman.BaseMultiAdapter
import com.harman.bar.app.databinding.ActivityRadioStationListBinding
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBoxDevice
import com.harman.log.Logger
import com.harman.partylight.util.fitSystemBar
import com.harman.radio.bean.ScanStationListBean
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.widget.AppCompatBaseActivity
import com.wifiaudio.utils.ClickHelper
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList


class RadioStationListActivity : AppCompatBaseActivity() {

    private val handler = Handler(Looper.getMainLooper())
    private var viewModel: RadioViewModel? = null
    private var progress = 0
    private lateinit var binding: ActivityRadioStationListBinding
    private var job: Job? = null

    private val _device = MutableLiveData<PartyBoxDevice>()

    private val _pageStyle = MutableLiveData<EnumPageStyle>()
    val pageStyle: LiveData<EnumPageStyle>
        get() = _pageStyle

    private val _isScanCompleted = MutableLiveData<Boolean>()
    val isScanCompleted: LiveData<Boolean>
        get() = _isScanCompleted

    private var scanAllStationsDialog: RadioScanAllStationsDialog? = null

    private val _selectedStation = MutableLiveData<RadioInfo.Station>()
    val selectedStation: LiveData<RadioInfo.Station>
        get() = _selectedStation

    private val _adapter = MutableLiveData<BaseMultiAdapter<*>>()
    val adapter: LiveData<BaseMultiAdapter<*>>
        get() = _adapter


    private val _stations = MutableLiveData<List<ScanStationListBean>>()
    val stations: LiveData<List<ScanStationListBean>>
        get() = _stations

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val device = parseBundle() ?: run {
            Logger.e(TAG, "onCreate() >>> fail to find device by uuid")
            finish()
            return
        }
        _device.value = device
        val viewModel = ViewModelProvider(
            this,
            RadioViewModelFactory(device = device)
        )[RadioViewModel::class.java]
        lifecycle.addObserver(viewModel)

        <EMAIL> = viewModel

        val binding = ActivityRadioStationListBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this
        binding.activity = this
        <EMAIL> = binding

        dataBindingRadioInfo(binding, device, viewModel)
        fitSystemBar()
        setContentView(binding.root)

    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        onBackBtnClick()
    }

    fun onBackBtnClick() {
        viewModel?.stopRadioScan()
        viewModel?.clearScannedStationList()
        handler.removeCallbacksAndMessages(null)
        job?.cancel()
        finish()
    }

    val clickListener = object : ScanStationAdapter.ScanStationClickListener {
        override fun onClick(stationBean: ScanStationListBean) {
            _device.value?.let { device ->
                device.radioInfo?.let { itRadioInfo ->
                    val assembleScanStations = assembleScanStations(itRadioInfo.scannedStationList, _selectedStation.value)
                    _stations.value = assembleScanStations
                }
                _selectedStation.value = stationBean.station
                viewModel?.activeStation(station = stationBean.station)
            }

        }

    }

    private fun dataBindingRadioInfo(
        binding: ActivityRadioStationListBinding,
        device: PartyBoxDevice,
        viewModel: RadioViewModel
    ) {

        startJob(isScanCompleted = false, EnumPageStyle.Scanning)

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(300)
            device.radioInfo?.let { itRadioInfo ->
                itRadioInfo.currentStation?.let { itStation ->
                    if (itStation.stationScanStatus == 0 || itStation.stationScanStatus == 2) {
                        viewModel.startRadioScan(itStation.radioType)
                    } else {
                        viewModel.getCachedScanStations(itStation)
                    }
                } ?: let {
                    viewModel.startRadioScan(RadioInfo.Type.FMRadio)
                }
            }
        }

        val adapter = ScanStationAdapter(
            stations = assembleScanStations(device.radioInfo?.scannedStationList, _selectedStation.value),
            lifecycleOwner = this,
            clickListener = clickListener
        )
        _adapter.value = adapter

        viewModel.radioInfo.observe(this) { radioInfo ->

            radioInfo?.let { itRadioInfo ->
                if (itRadioInfo.requestType == RadioInfo.RequestType.DeviceNotify) {
                    val scanResult = itRadioInfo.functionality == RadioInfo.Command.FunctionalityScanResult
                    val activeStation = itRadioInfo.functionality == RadioInfo.Command.FunctionalityActiveStation
                    if (scanResult || activeStation) {
                        itRadioInfo.scannedStationList?.let { itScannedStations ->
                            _stations.value = assembleScanStations(itScannedStations, _selectedStation.value)
                            if (itScannedStations.size > 0) {
                                startJob(isScanCompleted = true, EnumPageStyle.NotEmpty)
                            } else {
                                startJob(isScanCompleted = true, EnumPageStyle.Empty)
                            }

                        }
                    }
                }

            }
        }

    }

    private fun assembleScanStations(
        stationList: MutableList<RadioInfo.Station>?,
        selectedStation: RadioInfo.Station?
    ): MutableList<ScanStationListBean> {
        val tempList = stationList?.let { CopyOnWriteArrayList<RadioInfo.Station>(it) }
        val list = mutableListOf<ScanStationListBean>()

        tempList?.let { itStationList ->
            itStationList.forEach { item ->
                val scanStationListBean = ScanStationListBean(item, false)
                list.add(scanStationListBean)
            }
        }

        val iterator = list.iterator()
        while (iterator.hasNext()) {
            val item = iterator.next()
            selectedStation?.let { itStation ->
                when (itStation.radioType) {
                    RadioInfo.Type.DABRadio -> {
                        if (itStation.stationName == item.station.stationName) {
                            item.isSelected = true
                        }
                    }

                    else -> {
                        if (itStation.frequency == item.station.frequency) {
                            item.isSelected = true
                        }
                    }
                }
            }
        }

        return list
    }

    private fun startJob(isScanCompleted: Boolean, pageStyle: EnumPageStyle) {
        val maxTime = 90 * 1000L
        val maxProgress = 100
        val updateInterval = maxTime / maxProgress
        job?.cancel()
        _pageStyle.value = pageStyle

        if (isScanCompleted) {
            binding.progressBar.progress = maxProgress
            binding.tvScanningTips.visibility = View.GONE
            binding.tvNoStationTips.visibility = View.GONE
            fadeOut(binding.progressBar)
            return
        } else {
            binding.progressBar.visibility = View.VISIBLE
        }

        job = lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            while (isActive) {
                if (progress < maxProgress) {
                    progress++
                    binding.progressBar.progress = progress
                } else {
                    progress = maxProgress
                    binding.progressBar.progress = progress
                    fadeOut(binding.progressBar)
                    _pageStyle.value = EnumPageStyle.Empty
                    job?.cancel()
                }
                delay(updateInterval)
            }
        }
    }

    private fun updateProgressBar(isScanCompleted: Boolean, pageStyle: EnumPageStyle) {
        val maxTime = 90 * 1000L
        val maxProgress = 100
        val updateInterval = maxTime / maxProgress
        _pageStyle.value = pageStyle
        if (isScanCompleted) {
            binding.progressBar.progress = maxProgress
            fadeOut(binding.progressBar)
            handler.removeCallbacksAndMessages(null)
            return
        } else {
            binding.progressBar.visibility = View.VISIBLE
        }

        handler.postDelayed(object : Runnable {
            override fun run() {
                if (isScanCompleted) {
                    binding.progressBar.progress = maxProgress
                    fadeOut(binding.progressBar)
                    handler.removeCallbacksAndMessages(null)
                } else {
                    if (progress < maxProgress) {
                        progress++
                        binding.progressBar.progress = progress
                        handler.postDelayed(this, updateInterval)
                    } else {
                        binding.progressBar.progress = maxProgress
                        progress = maxProgress
                        fadeOut(binding.progressBar)
                        _pageStyle.value = EnumPageStyle.Empty
                        handler.removeCallbacksAndMessages(null)
                    }
                }
            }

        }, updateInterval)

    }

    fun onSyncBtnClick() {
        if (ClickHelper.isFastClick()) return
        _device.value?.let { device ->
            scanAllStationsDialog?.dismiss()
            scanAllStationsDialog = RadioScanAllStationsDialog(this,
                object : RadioScanAllStationsDialog.IEventListener {
                    override fun onScanClick() {
                        scanAllStationsDialog?.dismiss()
                        viewModel?.clearScannedStationList()
                        _stations.value = mutableListOf<ScanStationListBean>()
                        _adapter.value?.notifyDataSetChanged()
                        progress = 0
                        binding.progressBar.progress = progress
                        startJob(isScanCompleted = false, EnumPageStyle.Scanning)
                        viewModel?.startRadioScan(device.radioInfo?.currentRadioType)
                    }

                    override fun onNotNowClick() {
                        scanAllStationsDialog?.dismiss()
                    }

                })
            scanAllStationsDialog?.show()
        }

    }

    private fun parseBundle(): PartyBoxDevice? {
        val uuid = intent.getStringExtra(BUNDLE_UUID)
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "parseBundle() >>> missing uuid")
            return null
        }

        val device = DeviceStore.findPartyBox(uuid = uuid)
        if (null == device) {
            Logger.e(TAG, "parseBundle() >>> can't find one device based on uuid:$uuid")
        }

        Logger.d(TAG, "parseBundle() >>> init with device[${device?.UUID}]")
        return device
    }

    enum class EnumPageStyle {
        Scanning,
        NotEmpty,
        Empty
    }

    private fun fadeIn(view: View) {
        fadeIn(view, 0f, 1f, 500)
        view.isEnabled = true
    }

    private fun fadeOut(view: View) {
        fadeOut(view, 1f, 0f, 1000)
        view.isEnabled = false
    }

    private fun fadeIn(view: View, startAlpha: Float, endAlpha: Float, duration: Long) {
        if (view.visibility == View.VISIBLE) return
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.duration = duration
        view.startAnimation(animation)
        view.visibility = View.VISIBLE
    }

    private fun fadeOut(view: View, startAlpha: Float, endAlpha: Float, duration: Long) {
        if (view.visibility == View.GONE) return
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.duration = duration
        view.startAnimation(animation)
        view.visibility = View.GONE
    }

    companion object {
        fun portal(context: Context?, device: PartyBoxDevice?): Boolean {
            context ?: return false

            val uuid = device?.UUID
            if (uuid.isNullOrBlank()) {
                return false
            }

            val intent = Intent(context, RadioStationListActivity::class.java)
            intent.putExtra(BUNDLE_UUID, uuid)
            context.startActivity(intent)
            return true
        }

        private const val BUNDLE_UUID = "UUID"

        private const val TAG = "RadioStationListActivity"

    }
}