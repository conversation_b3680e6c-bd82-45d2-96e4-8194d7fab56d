package com.harman.radio

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.RecyclerView
import com.harman.BottomPopUpDialog
import com.harman.bar.app.databinding.DialogPresetStationBinding
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.wifiaudio.utils.WrapContentLinearLayoutManager
import com.wifiaudio.view.component.ComponentConfig


class PresetStationDialog(
    context: Context,
    private val presetStationList: MutableList<RadioInfo.Station>,
    private val currentStation: RadioInfo.Station?
) : BottomPopUpDialog(context = context) {

    private var mButtonClickListener: ButtonClickListener? = null
    private var toSetPresetStation: RadioInfo.Station? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCancelable(true)

        val binding = DialogPresetStationBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this@PresetStationDialog
        binding.dialog = this@PresetStationDialog
        dataBindingPreset(binding)

        setContentView(binding.root)
    }

    private fun dataBindingPreset(binding: DialogPresetStationBinding) {
        val stationsManager = WrapContentLinearLayoutManager(context, RecyclerView.VERTICAL, false)
        val adapter = SetPresetStationsAdapter(context, presetStationList)
        binding.presetStationRecycleView.layoutManager = stationsManager
        binding.presetStationRecycleView.adapter = adapter
        (binding.presetStationRecycleView.itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false
        adapter.setOnItemClickListener(object : SetPresetStationsAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int, type: RadioInfo.Station) {
                currentStation?.let { itStation ->
                    toSetPresetStation = itStation.deepCopy()
                    toSetPresetStation?.stationIndex = position + 1
                    toSetPresetStation?.isPreset = true
                }
                binding.btnDone.setBtnType(ComponentConfig.BTN_REGULAR)
                binding.btnDone.isEnabled = true
            }

        })
    }

    fun setOnButtonClickListener(listener: ButtonClickListener) {
        this.mButtonClickListener = listener
    }

    interface ButtonClickListener {
        fun onDoneClick(station: RadioInfo.Station?)
        fun onCancelClick()
    }

    fun onDoneClick() {
        mButtonClickListener?.onDoneClick(toSetPresetStation)
    }

    fun onCancelClick() {
        mButtonClickListener?.onCancelClick()
    }

}