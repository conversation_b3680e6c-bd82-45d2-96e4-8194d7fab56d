package com.harman.radio

import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleOwner
import com.harman.BaseMultiAdapter
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ItemScanStationBinding
import com.harman.radio.bean.ScanStationListBean


class ScanStationAdapter(
    stations: List<ScanStationListBean>,
    private val lifecycleOwner: LifecycleOwner,
    private val clickListener: ScanStationClickListener?
) : BaseMultiAdapter<ScanStationListBean>(data = stations) {

    override fun viewType(position: Int): Int = 0

    override fun layoutId(viewType: Int): Int = R.layout.item_scan_station

    override fun bind(binding: ViewDataBinding, position: Int) {
        val bean = getOrNull(position) ?: return
        val viewBinding = binding as? ItemScanStationBinding ?: return
        viewBinding.bean = bean
        viewBinding.listener = clickListener
        viewBinding.lifecycleOwner = lifecycleOwner
    }

    override fun isSameItem(old: ScanStationListBean, new: ScanStationListBean): Boolean {
        return old.station == new.station
    }

    override fun isSameContent(old: ScanStationListBean, new: ScanStationListBean): Boolean {
        return old.stationName == new.stationName && old.textColor == new.textColor
    }

    interface ScanStationClickListener {
        fun onClick(stationBean: ScanStationListBean)
    }

}