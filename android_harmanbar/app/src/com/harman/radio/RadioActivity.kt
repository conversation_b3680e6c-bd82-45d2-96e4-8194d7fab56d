package com.harman.radio

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.SeekBar
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityRadioBinding
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools.roundPercentage
import com.harman.log.Logger
import com.harman.music.player.PlayerViewModel
import com.harman.music.player.toVolumeImgRes
import com.harman.partylight.util.fitSystemBar
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.NumberFormat


class RadioActivity : AppCompatBaseActivity() {

    private val _device = MutableLiveData<PartyBoxDevice>()

    var targetDevice: Device? = null
        private set

    private val _switchingSource = MutableLiveData<Boolean>()
    val switchingSource: LiveData<Boolean>
        get() = _switchingSource

    private val _pageStyle = MutableLiveData<EnumPageStyle>()
    val pageStyle: LiveData<EnumPageStyle>
        get() = _pageStyle

    val deviceName = _device.map { device ->
        device.deviceName
    }

    private val _playStopImgRes = MutableLiveData<Int>()
    val playStopImgRes: LiveData<Int>
        get() = _playStopImgRes

    private val _ableControl = MutableLiveData<Boolean>()
    val ableControl: LiveData<Boolean>
        get() = _ableControl

    private val _isPreset = MutableLiveData<Boolean>()
    val isPreset: LiveData<Boolean>
        get() = _isPreset

    private val _presetIndex = MutableLiveData<String>()
    val presetIndex: LiveData<String>
        get() = _presetIndex

    private val _isScanning = MutableLiveData<Boolean>()
    val isScanning: LiveData<Boolean>
        get() = _isScanning

    private val _frequencyNum = MutableLiveData<String>()
    val frequencyNum: LiveData<String>
        get() = _frequencyNum

    private val _ableSwitchRadioSource = MutableLiveData<Boolean>()
    val ableSwitchRadioSource: LiveData<Boolean>
        get() = _ableSwitchRadioSource

    private val _dabNeedScan = MutableLiveData<Boolean>()
    val dabNeedScan: LiveData<Boolean>
        get() = _dabNeedScan

    private val _dabTitle = MutableLiveData<String>()
    val dabTitle: LiveData<String>
        get() = _dabTitle

    // TODO
    private val _dabSubTitle = MutableLiveData<String>()
    val dabSubTitle: LiveData<String>
        get() = _dabSubTitle

    private var viewModel: RadioViewModel? = null
    private var playerViewModel: PlayerViewModel<*>? = null

    private var viewBinding: ActivityRadioBinding? = null

    private var presetStationDialog: PresetStationDialog? = null
    private var scanAllStationsDialog: RadioScanAllStationsDialog? = null
    private var adjustFrequencyDialog: AdjustFrequencyDialog? = null

    private var loadingDialog: LoadingDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Utils.decorateHarmanWindow(this, R.color.bg_music)
        window?.navigationBarColor = ContextCompat.getColor(this, R.color.sp_player)

        val device = parseBundle() ?: run {
            Logger.e(TAG, "onCreate() >>> fail to find device by uuid")
            finish()
            return
        }

        targetDevice = device
        _device.value = device
        _pageStyle.value = EnumPageStyle.LOADING

        val viewModel = ViewModelProvider(
            this,
            RadioViewModelFactory(device = device)
        )[RadioViewModel::class.java]
        lifecycle.addObserver(viewModel)

        <EMAIL> = viewModel

        /*val playerViewModel = ViewModelProvider(
            this,
            PartyBoxPlayerViewModelFactory(device = device)
        )[PartyBoxPlayerViewModel::class.java]
        this.playerViewModel = playerViewModel
        lifecycle.addObserver(playerViewModel)*/

        val binding = ActivityRadioBinding.inflate(layoutInflater)
        binding.activity = this@RadioActivity
        binding.lifecycleOwner = this@RadioActivity
//        fitSystemBar()
        <EMAIL> = binding

        dataBindingVolume(binding = binding, device = device, viewModel = viewModel)

        dataBindingRadioInfo(binding = binding, device = device, viewModel = viewModel)

        setContentView(binding.root)
    }

    override val baseControlDevice: Device?
        get() = targetDevice

    private fun dataBindingRadioInfo(
        binding: ActivityRadioBinding,
        device: PartyBoxDevice,
        viewModel: RadioViewModel
    ) {
        viewModel.getRadioInfo()
        _ableSwitchRadioSource.value = viewModel.isDABSupport()

        device.radioInfo?.let { radioInfo ->
            updateCurrentStationView(radioInfo)
        }

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(200)
            device.radioInfo?.let { radioInfo ->
                viewModel.switchRadioType(radioInfo.currentRadioType)
            }
        }

        viewModel.radioInfo.observe(this) { radioInfo ->
            radioInfo?.let {
                updateCurrentStationView(it)
            }
        }

    }

    private fun updateCurrentStationView(radioInfo: RadioInfo) {
        isDABNeedScan(radioInfo)
        Logger.d(TAG, "updateCurrentStationView >>> radioInfo: $radioInfo")
        radioInfo.currentStation?.let { itStation ->
            _isScanning.value = false
            when (itStation.radioType) {
                RadioInfo.Type.FMRadio -> {
                    _pageStyle.value = EnumPageStyle.FM
                    itStation.frequency?.let {
                        _ableControl.value = true
                        _frequencyNum.value = formatFrequency(it)
                    } ?: let {
                        _ableControl.value = false
                    }

                }

                RadioInfo.Type.DABRadio -> {
                    _pageStyle.value = EnumPageStyle.DAB
                    itStation.stationName?.let {
//                        _ableControl.value = it.isNotBlank()
                        _ableControl.value = true
                        _dabTitle.value = it
                    } ?: let {
                        _ableControl.value = false
                    }
                }

                else -> {}
            }

            itStation.state?.let { itState ->
                when (itState) {
                    RadioInfo.State.Playing -> {
                        _playStopImgRes.value = R.drawable.ic_round_pause_2
                    }

                    RadioInfo.State.Stopped -> {
                        _playStopImgRes.value = R.drawable.ic_round_play
                    }
                }
            } ?: let {
                _playStopImgRes.value = R.drawable.ic_round_play
                _ableControl.value = false
            }

            if (itStation.isPreset == true) {
                itStation.stationIndex?.let { itIndex ->
                    _presetIndex.value = itIndex.toString()
                }
            }

            _isPreset.value = itStation.isPreset

        }
    }

    private fun isDABNeedScan(radioInfo: RadioInfo) {
        radioInfo.currentStation?.let { itStation ->
            if (itStation.radioType == RadioInfo.Type.DABRadio) {
                val scanStatus = itStation.stationScanStatus == 0 || itStation.stationScanStatus == 2
                _dabNeedScan.value = itStation.stationName.isNullOrBlank() && scanStatus
            }
        }
    }

    private fun dataBindingVolume(
        binding: ActivityRadioBinding,
        device: PartyBoxDevice,
        viewModel: RadioViewModel
    ) {

        /*binding.layoutRadioFm.sbVolume.thumbOffset = -10
        binding.layoutRadioDab.sbVolume.thumbOffset = -10*/

        val volumeWithMute = device.volumeWithMute

        binding.layoutRadioFm.sbVolume.progress = volumeWithMute
        binding.layoutRadioFm.icVolume.setImageResource(volumeWithMute.toVolumeImgRes())

        binding.layoutRadioDab.sbVolume.progress = volumeWithMute
        binding.layoutRadioDab.icVolume.setImageResource(volumeWithMute.toVolumeImgRes())

        binding.layoutRadioFm.sbVolume.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {
            private var last: Int = 0

            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                Logger.d(TAG, "onProgressChanged() >>> progress[$progress] fromUser[$fromUser]")
                if (!fromUser) return
                last = progress
                binding.layoutRadioFm.icVolume.setImageResource(progress.toVolumeImgRes())
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // no impl
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val volume = last.roundPercentage()
                Logger.d(TAG, "onStopTrackingTouch() >>> $volume")
                viewModel.setVolume(volume = volume)
            }
        })

        binding.layoutRadioDab.sbVolume.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {
            private var last: Int = 0
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                Logger.d(TAG, "onProgressChanged() >>> progress[$progress] fromUser[$fromUser]")
                if (!fromUser) return
                last = progress
                binding.layoutRadioDab.icVolume.setImageResource(progress.toVolumeImgRes())
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // no impl
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val volume = last.roundPercentage()
                Logger.d(TAG, "onStopTrackingTouch() >>> $volume")
                viewModel.setVolume(volume = volume)
            }

        })

        viewModel.volume.observe(this) { volume ->
            binding.layoutRadioFm.sbVolume.progress = volume
            binding.layoutRadioFm.icVolume.setImageResource(volume.toVolumeImgRes())
            binding.layoutRadioDab.sbVolume.progress = volume
            binding.layoutRadioDab.icVolume.setImageResource(volume.toVolumeImgRes())
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel?.let {
            lifecycle.removeObserver(it)
        }
    }

    private fun showLoadingDialog() {
        if (loadingDialog == null) {
            loadingDialog = LoadingDialog()
        }
        if (loadingDialog?.isAdded == false) {
            loadingDialog?.show(supportFragmentManager)
        }
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(2000)
            loadingDialog?.dismissAllowingStateLoss()
        }
    }

    fun onBackBtnClick() {
        finish()
    }

    fun onPlayStopClick() {
        showLoadingDialog()
        _device.value?.radioInfo?.let { itRadioInfo ->
            itRadioInfo.currentStation?.let { itStation ->
                itStation.state?.let { itState ->
                    when (itState) {
                        RadioInfo.State.Playing -> {
                            viewModel?.stopStation()
                        }

                        RadioInfo.State.Stopped -> {
                            viewModel?.playStation()
                        }
                    }
                }
            }
        }
    }

    fun onPreviousClick() {
        _device.value?.let { device ->
            device.radioInfo?.let {
                viewModel?.switchToPrevStation()
                _ableControl.value = false
                _isScanning.value = true
            }
        }
    }

    fun onNextClick() {
        _device.value?.let { device ->
            device.radioInfo?.let {
                viewModel?.switchToNextStation()
                _ableControl.value = false
                _isScanning.value = true
            }
        }
    }

    fun onFavouriteClick(favouriteNow: Boolean) {
        presetStationDialog?.dismiss()
        _device.value?.radioInfo?.let { itRadioInfo ->
            if (favouriteNow) {
                viewModel?.removePresetStation(itRadioInfo.currentStation)
            } else {
                viewModel?.assemblePresetStations(itRadioInfo.presetStationList)?.let {
                    presetStationDialog = PresetStationDialog(context = this, it, itRadioInfo.currentStation)
                    presetStationDialog?.setOnButtonClickListener(object : PresetStationDialog.ButtonClickListener {
                        override fun onDoneClick(station: RadioInfo.Station?) {
                            viewModel?.addPresetStation(station)
                            presetStationDialog?.dismiss()
                        }


                        override fun onCancelClick() {
                            presetStationDialog?.dismiss()
                        }

                    })
                    presetStationDialog?.show()
                }

            }
        }

    }

    fun onStationListClick() {
        _device.value?.let { device ->
            device.radioInfo?.let { itRadioInfo ->
                itRadioInfo.currentStation?.let { itStation ->
                    if (itStation.stationScanStatus == 0 || itStation.stationScanStatus == 2) {
                        scanAllStationsDialog?.dismiss()
                        scanAllStationsDialog = RadioScanAllStationsDialog(this,
                            object : RadioScanAllStationsDialog.IEventListener {
                                override fun onScanClick() {
                                    scanAllStationsDialog?.dismiss()
//                                    viewModel?.startRadioScan(itStation.radioType)
                                    RadioStationListActivity.portal(this@RadioActivity, device = device)
                                }

                                override fun onNotNowClick() {
                                    scanAllStationsDialog?.dismiss()
                                }

                            })
                        scanAllStationsDialog?.show()
                    } else {
//                        viewModel?.getCachedScanStations(itStation)
                        RadioStationListActivity.portal(this@RadioActivity, device = device)
                    }
                }

            }
        }
    }

    fun onFrequencyClick() {
        _device.value?.let { device ->
            device.radioInfo?.let { itRadioInfo ->
                adjustFrequencyDialog?.dismiss()
                adjustFrequencyDialog = AdjustFrequencyDialog(this, itRadioInfo)
                adjustFrequencyDialog?.show()
                adjustFrequencyDialog?.setOnEventListener(object : AdjustFrequencyDialog.IEventListener {
                    override fun onScaleSlide(newStation: RadioInfo.Station) {
                        viewModel?.activeStation(newStation)
                    }

                    override fun onMinusClick(newStation: RadioInfo.Station) {
                        viewModel?.activeStation(newStation)
                    }

                    override fun onPlusClick(newStation: RadioInfo.Station) {
                        viewModel?.activeStation(newStation)
                    }

                })
            }
        }

    }

    fun onFreqHandyMinusClick() {
        _device.value?.radioInfo?.let { itRadioInfo ->
            val radioInfoTemp = itRadioInfo.deepCopy()
            radioInfoTemp.currentStation?.let { itStation ->
                val frequencyStr = viewBinding?.layoutRadioFm?.tvFrequencyNumber?.text?.toString()
                frequencyStr?.let { itFrequency ->
                    val startFrequency = radioInfoTemp.setting?.startFrequency!!
                    val endFrequency = radioInfoTemp.setting?.endFrequency!!
                    val manualSeekStep = radioInfoTemp.setting?.manualSeekStep!!
                    val bd = BigDecimal(itFrequency)
                    var frToFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                    if (frToFloat <= startFrequency) {
                        frToFloat = endFrequency + manualSeekStep
                    }
                    val adjustedFrequency = frToFloat - manualSeekStep
                    val bigDecimal = BigDecimal(adjustedFrequency.toDouble())
                    val toFloat = bigDecimal.setScale(2, RoundingMode.HALF_UP).toFloat()
                    itStation.frequency = toFloat
                    viewModel?.activeStation(itStation)

                }
            }
        }

    }

    fun onFreqHandyPlusClick() {
        _device.value?.radioInfo?.let { itRadioInfo ->
            val radioInfoTemp = itRadioInfo.deepCopy()
            radioInfoTemp.currentStation?.let { itStation ->
                val frequencyStr = viewBinding?.layoutRadioFm?.tvFrequencyNumber?.text?.toString()
                frequencyStr?.let { itFrequency ->
                    val startFrequency = radioInfoTemp.setting?.startFrequency!!
                    val endFrequency = radioInfoTemp.setting?.endFrequency!!
                    val manualSeekStep = radioInfoTemp.setting?.manualSeekStep!!
                    val bd = BigDecimal(itFrequency)
                    var frToFloat = bd.setScale(2, RoundingMode.HALF_UP).toFloat()
                    if (frToFloat >= endFrequency) {
                        frToFloat = startFrequency - manualSeekStep
                    }
                    val adjustedFrequency = frToFloat + manualSeekStep
                    val bigDecimal = BigDecimal(adjustedFrequency.toDouble())
                    val toFloat = bigDecimal.setScale(2, RoundingMode.HALF_UP).toFloat()
                    itStation.frequency = toFloat
                    viewModel?.activeStation(itStation)

                }
            }
        }

    }

    fun onSwitchRadioSourceClick() {
        showLoadingDialog()
        _device.value?.radioInfo?.let { itRadioInfo ->
            itRadioInfo.currentRadioType?.let { radioType ->
                when (radioType) {
                    RadioInfo.Type.FMRadio -> {
                        viewModel?.switchRadioType(RadioInfo.Type.DABRadio)
                    }

                    RadioInfo.Type.DABRadio -> {
                        viewModel?.switchRadioType(RadioInfo.Type.FMRadio)
                    }
                }
            }
        }
    }

    fun onDABScanBtnClick() {
        _device.value?.let { device ->
            scanAllStationsDialog?.dismiss()
            scanAllStationsDialog = RadioScanAllStationsDialog(this,
                object : RadioScanAllStationsDialog.IEventListener {
                    override fun onScanClick() {
                        scanAllStationsDialog?.dismiss()
                        RadioStationListActivity.portal(this@RadioActivity, device = device)
                    }

                    override fun onNotNowClick() {
                        scanAllStationsDialog?.dismiss()
                    }

                })
            scanAllStationsDialog?.show()
        }

    }

    private fun parseBundle(): PartyBoxDevice? {
        val uuid = intent.getStringExtra(BUNDLE_UUID)
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "parseBundle() >>> missing uuid")
            return null
        }

        val device = DeviceStore.findPartyBox(uuid = uuid)
        if (null == device) {
            Logger.e(TAG, "parseBundle() >>> can't find one device based on uuid:$uuid")
        }

        Logger.d(TAG, "parseBundle() >>> init with device[${device?.UUID}]")
        return device
    }

    enum class EnumPageStyle {
        LOADING,
        FM,
        DAB
    }

    private fun formatFrequency(frequency: Float): String {
        val defaultLocaleFormatter = NumberFormat.getNumberInstance()
        defaultLocaleFormatter.minimumFractionDigits = 2
        defaultLocaleFormatter.maximumFractionDigits = 2
        val formattedString = defaultLocaleFormatter.format(frequency)
        return formattedString
    }

    companion object {

        fun portal(context: Context?, device: PartyBoxDevice?): Boolean {
            context ?: return false

            val uuid = device?.UUID
            if (uuid.isNullOrBlank()) {
                return false
            }

            val intent = Intent(context, RadioActivity::class.java)
            intent.putExtra(BUNDLE_UUID, uuid)
            context.startActivity(intent)
            return true
        }

        private const val BUNDLE_UUID = "UUID"

        private const val TAG = "RadioActivity"
    }
}