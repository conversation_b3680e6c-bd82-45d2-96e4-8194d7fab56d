package com.harman.radio

import android.bluetooth.BluetoothDevice
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.syncActiveStationWithTimeout
import com.harman.connect.syncAddFavouriteWithTimeout
import com.harman.connect.syncGetLastStationWithTimeout
import com.harman.connect.syncGetPresetRadioListWithTimeout
import com.harman.connect.syncGetRadioSettingsWithTimeout
import com.harman.connect.syncNextStationWithTimeout
import com.harman.connect.syncNotifyRadioListNotifyWithTimeout
import com.harman.connect.syncPlayStationWithTimeout
import com.harman.connect.syncPrevStationWithTimeout
import com.harman.connect.syncStartRadioScanWithTimeout
import com.harman.connect.syncStopRadioScanWithTimeout
import com.harman.connect.syncStopStationWithTimeout
import com.harman.connect.syncSwitchRadioTypeWithTimeout
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.PartyBoxDevFeat
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.wifiaudio.view.pagesdevcenter.SingleLiveData
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList


class RadioViewModelFactory(
    private val device: PartyBoxDevice
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return RadioViewModel(device = device) as T
    }
}

class RadioViewModel(
    private val device: PartyBoxDevice
) : ViewModel(), DefaultLifecycleObserver {

    private val _volume = MutableLiveData<Int>(device.volumeWithMute)
    val volume: LiveData<Int>
        get() = _volume

    private val _radioInfo = MutableLiveData<RadioInfo>()
    val radioInfo: LiveData<RadioInfo>
        get() = _radioInfo

    private val _switchRadioTypeLiveData = SingleLiveData<Boolean>()
    val switchRadioType: LiveData<Boolean>
        get() = _switchRadioTypeLiveData

    fun setVolume(volume: Int) {
        device.setRemoteVolume(protocol = BluetoothDevice.TRANSPORT_LE, value = volume)
    }

    fun assemblePresetStations(stationList: MutableList<RadioInfo.Station>?): MutableList<RadioInfo.Station> {
        val presetStations = MutableList(5) { RadioInfo.Station() }
        stationList?.let { itStationList ->
            for (station in itStationList) {
                if (station.isPreset == true) {
                    station.stationIndex?.let { itStationIndex ->
                        if (itStationIndex <= 5 && itStationIndex - 1 >= 0) {
                            presetStations[itStationIndex - 1] = station
                        }
                    }
                }
            }
        }
        Logger.i("RadioViewModel", "assemblePresetStations:$presetStations")
        return presetStations
    }

    fun isDABSupport(): Boolean {
        if (!device.isOnline) return false
        val supportRadio = device.deviceFeature?.supportRadio
        return when (supportRadio) {
            PartyBoxDevFeat.SupportRadio.FMDAB, PartyBoxDevFeat.SupportRadio.DAB -> {
                true
            }

            else -> {
                false
            }
        }
    }

    fun getRadioInfo() {
        if (!device.isOnline) return
        device.reqRadioInfo(
            protocol = device.getBLEProtocol(),
            cmd = ReqRadioInfoCommand(RadioInfo.RequestType.LastStation)
        )

        device.reqRadioInfo(
            protocol = device.getBLEProtocol(),
            cmd = ReqRadioInfoCommand(RadioInfo.RequestType.RadioSetting)
        )

        device.reqRadioInfo(
            protocol = device.getBLEProtocol(),
            cmd = ReqRadioInfoCommand(RadioInfo.RequestType.AllPreset)
        )
    }

    fun getRadioSettings() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncGetRadioSettingsWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun getAllPreset() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncGetPresetRadioListWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun getLastStation() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncGetLastStationWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun switchRadioType(radioType: RadioInfo.Type?) {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            radioType?.let { itRadioType ->
                val switchResult = device.syncSwitchRadioTypeWithTimeout(radioType = itRadioType, device.getBLEProtocol(), logTag = TAG)
                _switchRadioTypeLiveData.postValue(switchResult)
            }

        }
    }

    fun playStation() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncPlayStationWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun stopStation() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncStopStationWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun switchToPrevStation() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncPrevStationWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun switchToNextStation() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncNextStationWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun activeStation(station: RadioInfo.Station?) {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            station?.let { itStation ->
                device.syncActiveStationWithTimeout(device.getBLEProtocol(), logTag = TAG, station = itStation)
            }

        }
    }

    fun startRadioScan(radioType: RadioInfo.Type?) {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            radioType?.let { itRadioType ->
                device.syncStartRadioScanWithTimeout(radioType = itRadioType, device.getBLEProtocol(), logTag = TAG)
            }
        }

    }

    fun stopRadioScan() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.syncStopRadioScanWithTimeout(device.getBLEProtocol(), logTag = TAG)
        }
    }

    fun addPresetStation(station: RadioInfo.Station?) {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            station?.let { itStation ->
                device.syncAddFavouriteWithTimeout(device.getBLEProtocol(), logTag = TAG, station = itStation)
            }
        }
    }

    fun removePresetStation(station: RadioInfo.Station?) {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            station?.let { itStation ->
                itStation.isPreset = false
                device.radioInfo?.let { itRadioInfo ->
                    itRadioInfo.presetStationList?.let { itPresetStations ->
                        val tempPresetStations = CopyOnWriteArrayList(itPresetStations)
                        val filterList = tempPresetStations.filter { it.stationIndex != station.stationIndex }.toMutableList()
                        device.radioInfo?.presetStationList = filterList
                    }
                }
                device.syncAddFavouriteWithTimeout(device.getBLEProtocol(), logTag = TAG, station = itStation)
                delay(200)
                getAllPreset()
            }

        }
    }

    fun getCachedScanStations(station: RadioInfo.Station?) {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            station?.let { itStation ->
                itStation.radioType?.let { itRadioType ->
                    device.syncNotifyRadioListNotifyWithTimeout(radioType = itRadioType, device.getBLEProtocol(), logTag = TAG)
                }
            }
        }
    }

    fun clearScannedStationList() {
        if (!device.isOnline) return
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            device.radioInfo?.scannedStationList = mutableListOf()
        }
    }

    val deviceListener = object : IPartyBoxDeviceListener {

        override fun onVolume(volume: Int) {
            super.onVolume(volume)
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _volume.value = device.volumeWithMute
            }
        }

        override fun onRadioInfo(radioInfo: RadioInfo?, functionality: RadioInfo.Command?) {
            super.onRadioInfo(radioInfo, functionality)
            Logger.i(TAG, "onRadioInfo radioInfo:$radioInfo")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                radioInfo?.let {
                    _radioInfo.postValue(it)
                }
            }
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        Logger.i(TAG, "onCreate")
//        device.clearListeners()
        device.registerDeviceListener(deviceListener)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        Logger.i(TAG, "onDestroy")
        device.unregisterDeviceListener(deviceListener)
    }

    companion object {
        val TAG: String = RadioViewModel::class.java.simpleName
    }
}