package com.harman.radio

import android.app.Dialog
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.app.ActivityCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogLoadingBinding

class LoadingDialog : DialogFragment() {
    private lateinit var binding: DialogLoadingBinding

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireActivity())
        val window = dialog.window
        window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        //内容是否扩展到导航栏和状态栏,这里需要设置，否则布局不能侵入系统状态栏，无法沉浸式
        window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_PANEL)
        window?.setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS, WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
        setWindowStyle(window)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        isCancelable = false
        return dialog
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.dialog_loading, container, false)
        return binding.root
    }

    /**
     * 设置当前window的样式
     *
     * @param window 可以通过该window设置dialog window
     */
    private fun setWindowStyle(window: Window?) {
        if (window == null || context == null) {
            return
        }
        window.setBackgroundDrawable(ColorDrawable(ActivityCompat.getColor(requireContext(), R.color.transparent)))
        window.decorView.setPadding(0, 0, 0, 0)
        window.setGravity(Gravity.CENTER)
//        window.attributes.windowAnimations = R.style.BottomDialogAnimation
        // NOTE: 千万不要设置WindowManager.LayoutParams.FLAG_DIM_BEHIND，否则布局不能侵入系统状态栏，无法沉浸式
        window.attributes.dimAmount = 0.0f
        window.attributes = getLayoutParams(window.attributes)
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
    }

    /**
     * 设置dialog布局参数
     *
     * @param params 通过该参数设置相应的属性
     */
    private fun getLayoutParams(params: WindowManager.LayoutParams?): WindowManager.LayoutParams? {
        if (params == null) {
            return WindowManager.LayoutParams()
        }
        params.width = ViewGroup.LayoutParams.MATCH_PARENT
        params.height = ViewGroup.LayoutParams.MATCH_PARENT
        params.gravity = Gravity.BOTTOM
        return params
    }

    fun show(fragmentManager: FragmentManager) {
        this.show(fragmentManager, LoadingDialog::class.java.simpleName)
    }

}