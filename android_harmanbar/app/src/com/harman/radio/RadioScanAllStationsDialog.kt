package com.harman.radio

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.harman.BottomPopUpDialog
import com.harman.bar.app.databinding.DialogRadioScanAllStationsBinding


class RadioScanAllStationsDialog(
    activity: FragmentActivity,
    private val eventListener: IEventListener? = null
) : BottomPopUpDialog(activity) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCancelable(true)

        val binding = DialogRadioScanAllStationsBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this@RadioScanAllStationsDialog
        binding.dialog = this@RadioScanAllStationsDialog

        setContentView(binding.root)
    }

    fun onNotNowClick() {
        eventListener?.onNotNowClick()
    }

    fun onScanClick() {
        eventListener?.onScanClick()
    }

    interface IEventListener {

        fun onScanClick()

        fun onNotNowClick()

    }
}