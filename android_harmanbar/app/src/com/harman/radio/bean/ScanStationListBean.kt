package com.harman.radio.bean

import com.harman.bar.app.R
import com.harman.command.partybox.gatt.radio.RadioInfo
import java.text.NumberFormat


data class ScanStationListBean(
    val station: RadioInfo.Station,
    var isSelected: Boolean
) {

    val textColor: Int
        get() = if (isSelected) R.color.fg_activate else R.color.fg_primary

    val stationName: String?
        get() = station.radioType?.let { itRadioType ->
            if (itRadioType == RadioInfo.Type.DABRadio) {
                station.stationName
            } else {
                station.frequency.formatFrequency()
            }
        }?: let {
            station.frequency.formatFrequency()
        }

    val backgroundColor: Int
        get() = if (isSelected) R.color.bg_card else R.color.bg_surface

    companion object {

        fun Float?.toFMString(): String? = this?.let { f ->
            "%.2f".format(f)
        }

        fun Float?.formatFrequency(): String {
            val defaultLocaleFormatter = NumberFormat.getNumberInstance()
            defaultLocaleFormatter.minimumFractionDigits = 2
            defaultLocaleFormatter.maximumFractionDigits = 2
            val formattedString = defaultLocaleFormatter.format(this)
            return formattedString
        }

    }
}