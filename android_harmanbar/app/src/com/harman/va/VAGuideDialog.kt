package com.harman.va

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogVaGuideBinding
import com.harman.BottomPopUpDialog
import com.harman.EventUtils
import com.harman.discover.bean.OneDevice
import com.harman.log.Logger
import com.harman.va.refactor.CoulsonFirebaseManager
import com.harman.va.refactor.VAPortalHelper
import java.util.LinkedList
import java.util.Queue

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/11/28.
 */
class VAGuideDialog(
    private val activity: ComponentActivity,
    private val device: OneDevice,
    private val isFromOOBE: Boolean = true
) : BottomPopUpDialog(
    activity, R.style.SlideDialogAnim
) {

    private val _pager = MutableLiveData<EnumVAGuidePager>(EnumVAGuidePager.ONE)
    val pager: LiveData<EnumVAGuidePager>
        get() = _pager

    private val _selection = MutableLiveData<EnumVAGuideSelection>(EnumVAGuideSelection.BOTH)
    val selection: LiveData<EnumVAGuideSelection>
        get() = _selection

    private val _upperImgRes = MutableLiveData<Int>()
    val upperImgRes: LiveData<Int>
        get() = _upperImgRes

    private val _lowerImgRes = MutableLiveData<Int>()
    val lowerImgRes: LiveData<Int>
        get() = _lowerImgRes

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val binding = DialogVaGuideBinding.inflate(layoutInflater)
        binding.dialog = this
        binding.lifecycleOwner = this

        setContentView(binding.root)

        initAlexaAndGoogleVAIcon()

        CoulsonFirebaseManager.recordLandOnServiceValue(EventUtils.Dimension.EnumVAResult.TRUE.value)
    }

    private fun initAlexaAndGoogleVAIcon() {
        if (0 == (0..1).random() % 2) {
            _upperImgRes.value = R.drawable.ic_google_va
            _lowerImgRes.value = R.drawable.ic_alexa_va
        } else {
            _upperImgRes.value = R.drawable.ic_alexa_va
            _lowerImgRes.value = R.drawable.ic_google_va
        }
    }

    // ------------ EnumVAGuidePager.ONE control ----------
    fun onLearnMoreClick() {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        val url = Uri.parse(LEARN_MORE_LINK)
        intent.data = url
        activity.startActivity(intent)
    }

    fun onSetupBtnClick() {
        _pager.value = EnumVAGuidePager.TWO
        CoulsonFirebaseManager.recordLandOnServiceVaValue(EventUtils.Dimension.EnumVAResult.TRUE.value)
    }

    fun onLaterBtnClick() {
        dismiss()
    }

    // ------------ EnumVAGuidePager.TWO control ----------
    fun onBackBtnClick() {
        _pager.value = EnumVAGuidePager.ONE
    }

    fun onSelect(select: EnumVAGuideSelection) {
        _selection.value = select
    }

    private val queue: Queue<EnumVAGuideSelection> = LinkedList<EnumVAGuideSelection>()

    fun onContinueBtnClick() {
        when (_selection.value) {
            EnumVAGuideSelection.BOTH -> {
                Logger.i(TAG, "onContinueBtnClick() >>> portal Alexa VA and store Google VA")
                queue.offer(EnumVAGuideSelection.GOOGLE_VA)
                VAPortalHelper.portalGVAAndAlexa(context = this.activity, device = device, if(isFromOOBE) EventUtils.Dimension.EnumCoulsonEntry.OOBE else EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_BANNER_GVA_ALEXA)
            }
            EnumVAGuideSelection.ALEXA -> {
                Logger.i(TAG, "onContinueBtnClick() >>> portal Alexa VA")
                VAPortalHelper.portalAlexa(context = this.activity, device = device, if(isFromOOBE) EventUtils.Dimension.EnumCoulsonEntry.OOBE else EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_BANNER_ALEXA)
            }
            EnumVAGuideSelection.GOOGLE_VA -> {
                VAPortalHelper.portalGVA(context = this.activity, device = device, if(isFromOOBE) EventUtils.Dimension.EnumCoulsonEntry.OOBE else EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_BANNER_GVA)
            }
            else -> {
                // no impl
            }
        }
        dismiss()
    }

    companion object {
        private const val TAG = "VAGuideDialog"

        private const val LEARN_MORE_LINK = "https://www.jbl.com/mae"
    }
}

enum class EnumVAGuidePager {
    ONE,
    TWO
}

enum class EnumVAGuideSelection {
    BOTH,
    ALEXA,
    GOOGLE_VA
}