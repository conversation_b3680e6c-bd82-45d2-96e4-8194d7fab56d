package com.harman.va

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amazon.identity.auth.device.AuthError
import com.amazon.identity.auth.device.api.authorization.AuthCancellation
import com.amazon.identity.auth.device.api.authorization.AuthorizationManager
import com.amazon.identity.auth.device.api.authorization.AuthorizeListener
import com.amazon.identity.auth.device.api.authorization.AuthorizeRequest
import com.amazon.identity.auth.device.api.authorization.AuthorizeResult
import com.amazon.identity.auth.device.api.authorization.ProfileScope
import com.amazon.identity.auth.device.api.authorization.ScopeFactory
import com.amazon.identity.auth.device.api.workflow.RequestContext
import com.harman.bar.app.databinding.ActivityVaAlexaBinding
import com.harman.command.one.bean.EnumLWAStatus
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.partylight.util.fitSystemBar
import com.harman.portalUrl
import com.harman.discover.DeviceStore
import com.harman.discover.bean.OneDevice
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.harman.va.refactor.VAActivity
import com.harman.widget.CollapsingToolBar
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.UIUtils
import com.wifiaudio.view.dlg.AlexaLogoutDialog
import com.wifiaudio.view.oobe.SetupGVANormalDialogFragment
import com.wifiaudio.view.oobe.SetupGVAOOBEDialogFragment
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import com.wifiaudio.view.pagesdevcenter.devicesetting.calibration.ChromecastBuiltInAvailableDlgView
import java.util.concurrent.atomic.AtomicBoolean
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by sky on 2024/7/25.
 */
class VAAlexaActivity : AppCompatActivity() {


    private var viewModel: VAAlexaViewModel? = null

    private val _viewStyle = MutableLiveData<EnumViewStyle>()
    val viewStyle: LiveData<EnumViewStyle>
        get() = _viewStyle

    private val _viewStyleLoading = MutableLiveData<EnumViewStyle>()
    val viewStyleLoading: LiveData<EnumViewStyle>
        get() = _viewStyleLoading

    private val _viewStyleDetail = MutableLiveData<EnumViewStyle>()
    val viewStyleDetail: LiveData<EnumViewStyle>
        get() = _viewStyleDetail
    var requestContext: RequestContext? = null

    private val _startToneConfig = MutableLiveData<Boolean>()
    val startToneConfig: LiveData<Boolean>
        get() = _startToneConfig

    private val _endToneConfig = MutableLiveData<Boolean>()
    val endToneConfig: LiveData<Boolean>
        get() = _endToneConfig

    private val _voiceLanguage = MutableLiveData<String>()
    val voiceLanguage: LiveData<String>
        get() = _voiceLanguage
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Utils.decorateHarmanWindow(this@VAAlexaActivity)

        val device = parseBundle() ?: run {
            setResult(RESULT_CODE)
            finish()
            return
        }

        val binding = ActivityVaAlexaBinding.inflate(layoutInflater)
        binding.activity = this@VAAlexaActivity
        binding.lifecycleOwner = this@VAAlexaActivity
        fitSystemBar()
        setContentView(binding.root)

        bindingData(device = device)
        binding.collapsingToolbar?.setExpanded(false)
        binding.collapsingToolbar?.setTitleText(SkinResourcesUtils.getString("jbl_Amazon_Alexa"))
        binding.collapsingToolbar?.setNavigationListener(object : CollapsingToolBar.NavigationListener {
            override fun onBack() {
                setResult(RESULT_CODE)
                finish()
            }

            override fun onNext() {
            }
        })

        doInitLWAFlow()
        refreshToneConfig()
    }

    open fun onStartToneClick() {
        viewModel?.switchStartToneConfig()
    }

    open fun onEndToneClick() {
        viewModel?.switchEndToneConfig()
    }

    private fun refreshToneConfig() {
        viewModel?.voiceLanguage?.observe(this) { config ->
            _voiceLanguage.value = config
        }
        viewModel?.refreshVoiceLanguageConfig()

        viewModel?.startToneConfig?.observe(this) { config ->
            _startToneConfig.value = config.isEnable()
        }
        viewModel?.refreshStartToneConfig()

        viewModel?.endToneConfig?.observe(this) { config ->
            _endToneConfig.value = config.isEnable()
        }
        viewModel?.refreshEndToneConfig()
    }

    override fun onResume() {
        super.onResume()

        setupLwaState?.also {
            if (!it) {
                requestContext?.onResume()

                lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                    delay(500)
                    getLWAState()
                }
            }
        }


    }

    private fun getLWAState() {
        if (getLWAStateCount >= MAX_RETRY) {
            navigateToPrevPage()
            return
        }
        getLWAStateCount++
        _viewStyleLoading.value = EnumViewStyle.SHOW
        _viewStyleDetail.value = EnumViewStyle.HIDE
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            var response = viewModel?.getLWAState()
            Logger.i(TAG, "getLWAState() $response")
            if (true == response?.success()) {

                if (response.state == EnumLWAStatus.NOT_LOGIN.value) {
//                    response?.meta?.also {
//                        productID = it.productId
//                        serialNumber = it.serialNumber
//                    }
                    if (lwaSate == null) {
                        lwaSate = response
                        getLWAStateCount = 0
                        doLWAAuthorize()
                    }

                } else if (response.state == EnumLWAStatus.LOGGING.value) {
                    delay(2000)
                    getLWAState()


                } else if (response.state == EnumLWAStatus.LOGGED.value) {
                    getLWAStateCount = 0
                    setupLwaState = true
                    goAfterLoginSuccess()
                }


            }
        }

    }

    private fun doLWAAuthorize() {

        val scopeData = JSONObject()
        val productInstanceAttributes = JSONObject()
        try {
            productInstanceAttributes.put("deviceSerialNumber", lwaSate?.meta?.serialNumber) // TODO:
            scopeData.put("productInstanceAttributes", productInstanceAttributes);
            scopeData.put("productID", lwaSate?.meta?.productId)
        } catch (e: JSONException) {
            throw RuntimeException(e)
        }
        LogsUtil.d(TAG, "doLWAAuthorize start: lwaSate: $lwaSate scopeData:${scopeData}")
        if (requestContext == null) {
            requestContext = RequestContext.create(this)
            requestContext?.registerListener(authorizeListener)
        }
        try {
            AuthorizationManager.authorize(AuthorizeRequest.Builder(requestContext) // App Login
                .addScopes(ProfileScope.profile(), ProfileScope.postalCode()) // TODO: Authorize an AVS Device Through a Companion App.
                .addScopes(ScopeFactory.scopeNamed("alexa:voice_service:pre_auth"), ScopeFactory.scopeNamed("alexa:all", scopeData))
                .forGrantType(AuthorizeRequest.GrantType.AUTHORIZATION_CODE).withProofKeyParameters(lwaSate?.meta?.codeChallenge, "S256")
                .build())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun doInitLWAFlow() {
        requestContext = RequestContext.create(this)
        requestContext?.registerListener(authorizeListener)
    }

    private var hasAuthorizeResult = AtomicBoolean(false)
    private var authorizeListener = object : AuthorizeListener() {


        override fun onSuccess(result: AuthorizeResult?) {


            if (!hasAuthorizeResult.getAndSet(true)) {
                var authorizationCode = result?.authorizationCode
                var clientId = result?.clientId
                var redirectURI = result?.redirectURI
                Logger.i(TAG, "authorizeListener：success:${authorizationCode},${clientId},${redirectURI}")
                //get device info
//                requestLanguageFromAmazon()
//                wait for voice language from amazon server
                setupLWAState(LWAInfo(authorizationCode, clientId, redirectURI))
            }
        }

        override fun onError(p0: AuthError?) {
            Logger.i(TAG, "authorizeListener：onError:${p0?.message}")
            navigateToPrevPage()
        }

        override fun onCancel(p0: AuthCancellation?) {
            Logger.i(TAG, "authorizeListener：onCancel:${p0?.description}")
            //if concurrent setup select alexa and google
            navigateToPrevPage()

//            if (enterFrom == EnterFromType.TYPE_COMPOSE && alexaOnly == false) {
//                uihd.post { showCancelConfirmDialog() }
//            } else {
//                showLoading(true)
//                navigateToPrevPage()
//            }
        }

    }

    private var setupLwaState: Boolean? = false
    private fun setupLWAState(lwaInfo: LWAInfo) {
        if (lwaInfo == null || lwaInfo.isEmpty()) {
            Logger.i(TAG, "setupLWAState：fail:${lwaInfo}")
            return
        }
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            var response = viewModel?.setLWAAuthCode(lwaInfo)
            Logger.i(TAG, "setLWAAuthCode：response:${response}")
            if (true == response?.success()) {


            }
        }


    }


    private var serialNumber: String? = ""
    private var productID: String? = ""
    private var getLWAStateCount = 0
    private var lwaSate: LwaStateResponse? = null
    fun goAfterLoginSuccess() {
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            _viewStyleLoading.value = EnumViewStyle.HIDE
            _viewStyleDetail.value = EnumViewStyle.SHOW

        }

    }


    private fun navigateToPrevPage() {
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            setResult(RESULT_CODE)
            finish()
        }

    }


    private var availableView: ChromecastBuiltInAvailableDlgView? = null
    private fun showChromecastSetupDone() {
        if (availableView != null) {
            availableView = if (availableView!!.isShowing()!!) return else {
                availableView!!.dismissDialog()
                null
            }
        }
        availableView = ChromecastBuiltInAvailableDlgView(this, 2)

        availableView?.show()
    }


    private var setupChromecastDialogFragment: SetupGVANormalDialogFragment? = null
    private var setupGoogleAssistantDialogFragment: SetupGVAOOBEDialogFragment? = null
    private var isClickChromecastSetupButton = false
    private fun showGoogleSetupDialog() {
        if (setupGoogleAssistantDialogFragment == null) {
            setupGoogleAssistantDialogFragment = SetupGVAOOBEDialogFragment()
            setupGoogleAssistantDialogFragment?.onCloseListener = object : SetupGVAOOBEDialogFragment.OnCloseListener {
                override fun onClose() {
                    setResult(RESULT_CODE)
                    finish()
                }
            }
        }

//        if (setupGoogleAssistantDialogFragment?.isAdded == false) {
//            setupGoogleAssistantDialogFragment?.show(supportFragmentManager, SetupGoogleAssistantDialogFragment::class.java.simpleName)
//        }
//
//        if (setupChromecastDialogFragment == null) {
//            setupChromecastDialogFragment = SetupGoogleAssistantAndChromecastDialogFragment()
//        }
//        if (setupChromecastDialogFragment?.isAdded == false) {
//            setupChromecastDialogFragment?.onCloseListener = object : SetupGoogleAssistantAndChromecastDialogFragment.OnCloseListener {
//                override fun onClose() {
//                    finish()
//                }
//            }
//            setupChromecastDialogFragment?.setOnSetupListener(object : SetupGoogleAssistantAndChromecastDialogFragment.OnSetupListener {
//                override fun onSetup() {
//                    isClickChromecastSetupButton = true
//                }
//
//            })
//            setupChromecastDialogFragment?.show(supportFragmentManager, SetupGoogleAssistantAndChromecastDialogFragment::class.java.simpleName)
//        }
    }

    override fun onDestroy() {
        super.onDestroy()
//        googleCastEnableDialog?.dismiss()
    }

    private fun parseBundle(): OneDevice? {
        val uuid = intent.getStringExtra(BUNDLE_UUID)
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "parseBundle() >>> missing uuid")
            return null
        }

        val device = DeviceStore.findOne(uuid = uuid)
        if (null == device) {
            Logger.e(TAG, "parseBundle() >>> can't find one device based on uuid:$uuid")
        }

        return device
    }

    private fun bindingData(device: OneDevice) {
        val viewModel = ViewModelProvider(
            this,
            VAAlexaViewModelFactory(device = device)
        )[VAAlexaViewModel::class.java]

        <EMAIL> = viewModel
        lifecycle.addObserver(viewModel)
    }

    fun clickLogout() {
        showLogoutLWADlg()
    }

    fun clickMusicStreamingWithAlexaCast() {
        portalUrl(
            config = AppConfigurationUtils.getMusicStreamingWithAlexaCastUrl(),
            default = music_streaming_with_alexacast
        )
//        UIUtils.openWebUrl(this, "https://music.amazon.com/help?nodeId=G202196760")
    }

    fun clickMultiRoomMusic() {
        portalUrl(
            config = AppConfigurationUtils.getMultiroomMusicAlexaUrl(),
            default = multiroom_music_alexa
        )
//        UIUtils.openWebUrl(this, "https://www.amazon.com/gp/help/customer/display.html?nodeId=GZ5U38E9GGBBWEM8")
    }

    fun clickVoiceLanguage() {
        var finalDeepLink =  AppConfigurationUtils.getVoiceLanguageUrl()?: voice_language
//        finalDeepLink = "https://alexa.amazon.com/spa/index.html#v2/devices-channel/control-panel/all"
        UIUtils.openAppPage(this, UIUtils.PACKAGE_NAME_ALEXA_APP, finalDeepLink)
    }

    fun clickGoToAmazonAlexaApp() {
        UIUtils.openApp(this, UIUtils.PACKAGE_NAME_ALEXA_APP)
    }

    private var logoutDialog: AlexaLogoutDialog? = null
    private fun showLogoutLWADlg() {
        if (logoutDialog != null && logoutDialog?.isShowing() == true) {
            Log.d(TAG, "showGoogleLogoutDlg: return")
            return
        }
        logoutDialog = this?.let { AlexaLogoutDialog(it) }
        logoutDialog?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
            override fun onConfirm(any: Any?) {
                logoutLWA()
            }

            override fun onCancel() {
            }

        })
        logoutDialog?.show()
    }

    private fun logoutLWA() {

        WAApplication.me.showProgDlg(this, true, "")
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val flag = viewModel?.alexaLwaLogoutWithTimeout()
            Logger.i(TAG, "logoutGoogle() >>> check result[$flag]")
            delay(5000)
            WAApplication.me.showProgDlg(this@VAAlexaActivity, false, "")
            finishAfterTransition()
        }


    }


    companion object {

        fun portal(activity: Activity?, device: OneDevice?): Boolean {
            if (VAActivity.REFACTOR_VA) {
                VAActivity.portalAlexa(activity, device)
            } else {
                activity ?: return false
                device ?: return false

                val intent = Intent(activity, VAAlexaActivity::class.java)
                intent.putExtra(BUNDLE_UUID, device.UUID)
                activity.startActivity(intent)
            }


            return true
        }

        fun portalGVAAndAlexa(
            launcher: ActivityResultLauncher<Intent>,
            activity: Activity?,
            device: OneDevice?
        ): Boolean {
            if (VAActivity.REFACTOR_VA) {
                VAActivity.portalGVAAndAlexa(activity, device)
            } else {
                activity ?: return false
                device ?: return false

                val intent = Intent(activity, VAAlexaActivity::class.java)
                intent.putExtra(BUNDLE_UUID, device.UUID)
                launcher.launch(intent)
            }


            return true
        }

        fun portalAlexa(
            launcher: ActivityResultLauncher<Intent>,
            activity: Activity?,
            device: OneDevice?
        ): Boolean {
            if (VAActivity.REFACTOR_VA) {
                VAActivity.portalAlexa(activity, device)
            } else {
                activity ?: return false
                device ?: return false

                val intent = Intent(activity, VAAlexaActivity::class.java)
                intent.putExtra(BUNDLE_UUID, device.UUID)
                launcher.launch(intent)
            }


            return true
        }

        private const val BUNDLE_UUID = "Bundle_UUID"

        private const val TAG = "VAAlexaActivity"

        private const val music_streaming_with_alexacast = "https://music.amazon.com/help?nodeId=G202196760"

        private const val multiroom_music_alexa = "https://www.amazon.com/gp/help/customer/display.html?nodeId=GZ5U38E9GGBBWEM8"

        private const val voice_language = "https://alexa.amazon.com/spa/index.html#v2/devices-channel/control-panel/all"
        const val MAX_RETRY = 6

        const val RESULT_CODE = 22
    }

}