package com.harman.va.refactor

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.distinctUntilChanged
import com.harman.EventUtils
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.databinding.ActivityVaBinding
import com.harman.discover.DeviceStore
import com.harman.discover.bean.OneDevice
import com.harman.discover.info.EnumProductLine
import com.harman.log.Logger
import com.harman.partylight.util.fitSystemBar
import com.harman.utils.Utils
import com.harman.va.EnumViewStyle
import com.wifiaudio.view.dlg.AlexaSetupExitDialog
import com.wifiaudio.view.oobe.AlexaSetupReadyDialogFragment
import com.wifiaudio.view.oobe.VaAreReadyDialogFragment
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import com.wifiaudio.view.pagesdevcenter.devicesetting.calibration.ChromecastBuiltInAvailableDlgView

/**
 * Created by sky on 2025/5/07.
 */
class VAActivity : AppCompatActivity() {

    private val viewModel: VAViewModel by viewModels()

    private val _topFragment = MediatorLiveData<Fragment?>()
    val topFragment: LiveData<Fragment?>
        get() = _topFragment.distinctUntilChanged()

    private val fragmentHelper = VAFragmentHelper()

    var coulsonEntry: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Utils.decorateHarmanWindow(this@VAActivity)

        val binding = ActivityVaBinding.inflate(layoutInflater)
        binding.lifecycleOwner = this
        binding.activity = this
        fitSystemBar()
        setContentView(binding.root)
        initFragment(intent)
        viewModel?.vaResult?.observe(this) { result ->
            when (result) {
                EnumViewStyle.ALEXA_SUCCESS -> {
                    showAlexaSetupOkDialog()
                    CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value,true)
                }
                EnumViewStyle.ALEXA_FAIL -> {
                    CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value,false)
                    finishAfterTransition()
                }
                EnumViewStyle.ALEXA_CANCEL -> {
                    CoulsonFirebaseManager.recordAlexaCancelValue(EventUtils.Dimension.EnumVAResult.TRUE.value)
                    finishAfterTransition()
                }
                EnumViewStyle.CONCURRENT_ALEXA_SUCCESS -> {
                    CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value,true)
                    _topFragment.value = fragmentHelper.vaGoogleFragment
                }

                EnumViewStyle.CONCURRENT_ALEXA_FAIL -> {
                    CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value,false)
                    showCancelConfirmDialog()
                }
                EnumViewStyle.CONCURRENT_ALEXA_CANCEL -> {
                    CoulsonFirebaseManager.recordAlexaCancelValue(EventUtils.Dimension.EnumVAResult.TRUE.value)
                    showCancelConfirmDialog()

                }
                EnumViewStyle.BOTH_VA_ENABLE -> {
                    showBothVAEnable()
                }
                EnumViewStyle.ONLY_GVA_ENABLE -> {
//                    showOnlyGVAEnable()
                }
                else -> {

                }

            }
        }

    }

    private var alexaSetupReadyDialogFragment: AlexaSetupReadyDialogFragment? = null
    private fun showAlexaSetupOkDialog() {
        if (alexaSetupReadyDialogFragment == null) {
            alexaSetupReadyDialogFragment = AlexaSetupReadyDialogFragment()
            alexaSetupReadyDialogFragment?.onCloseListener =
                object : AlexaSetupReadyDialogFragment.OnCloseListener {
                    override fun onClose() {
                        alexaSetupReadyDialogFragment?.dismiss()

                    }
                }
        }
        if (alexaSetupReadyDialogFragment?.isAdded == true) return

        supportFragmentManager?.let {
            alexaSetupReadyDialogFragment?.show(
                it,
                AlexaSetupReadyDialogFragment::class.java.simpleName
            )
        }

    }

    private var alexaSetupExitDialog: AlexaSetupExitDialog? = null
    private fun showCancelConfirmDialog() {
        if (alexaSetupExitDialog != null && alexaSetupExitDialog?.isShowing() == true) {
            Logger.d(TAG, "showCancelConfirmDialog: return")
            return
        }
        alexaSetupExitDialog = AlexaSetupExitDialog(this)
        alexaSetupExitDialog?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
            override fun onConfirm(any: Any?) {
                alexaSetupExitDialog = null
                _topFragment.value = fragmentHelper.vaGoogleFragment
                CoulsonFirebaseManager.reportCoulsonSetupData()


            }

            override fun onCancel() {

                alexaSetupExitDialog = null
                finishAfterTransition()

            }
        })
        alexaSetupExitDialog?.show()
    }

    private var vaAreReadyDialogFragment: VaAreReadyDialogFragment? = null
    private fun showBothVAEnable() {
        if (vaAreReadyDialogFragment == null) {
            vaAreReadyDialogFragment = VaAreReadyDialogFragment()
        }

        if (vaAreReadyDialogFragment?.isAdded == false) {
            vaAreReadyDialogFragment?.onCloseListener =
                object : VaAreReadyDialogFragment.OnCloseListener {
                    override fun onClose() {

                    }
                }
            supportFragmentManager?.let {
                vaAreReadyDialogFragment?.show(it, VaAreReadyDialogFragment::class.java.simpleName)
            }
        }
    }

    private var availableView: ChromecastBuiltInAvailableDlgView? = null
    private fun showOnlyGVAEnable() {
        if (availableView != null && availableView?.isShowing() == true) {
            Logger.d(TAG, "showCancelConfirmDialog: return")
            return
        }
        availableView = ChromecastBuiltInAvailableDlgView(this, 2)
        availableView?.show()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        initFragment(intent)
    }

    private fun initFragment(intent: Intent?) {
        val uuid = intent?.getStringExtra(BUNDLE_UUID)
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "parseAsAnyDevice() >>> fail to parse uuid")
            return
        }

        val device =
            DeviceStore.find(uuid = uuid, productLine = EnumProductLine.ONE) as? OneDevice ?: run {
                Logger.e(
                    TAG,
                    "parseAsAnyDevice() >>> can't find target device based on uuid[$uuid]"
                )
                return
            }

        viewModel.device = device

        val vaSetupOption = intent?.getStringExtra(EventUtils.Dimension.DI_VA_SETUP_OPTION)
        if (vaSetupOption.isNullOrBlank()) {
            Logger.e(TAG, "initFragment() >>> fail to parse vaSetupOption")
            return
        }
        viewModel.vaSetupOption = vaSetupOption
        coulsonEntry = intent?.getStringExtra(EventUtils.Dimension.DI_COULSON_ENTRY)
        CoulsonFirebaseManager.recordCoulsonEntryValue(coulsonEntry,device)
        CoulsonFirebaseManager.recordVaSetupOptionValue(vaSetupOption)

        when (viewModel.vaSetupOption) {
            EventUtils.Dimension.EnumVaSetupOption.ALEXA.value -> {
                _topFragment.value = fragmentHelper.vaAlexaFragment
            }

            EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value -> {
                _topFragment.value = fragmentHelper.vaAlexaFragment
            }

            EventUtils.Dimension.EnumVaSetupOption.GVA.value -> {
                _topFragment.value = fragmentHelper.vaGoogleFragment
            }


        }

    }

    fun onBackBtnClick() {
        finish()
    }


    companion object {

        private fun portalVA(
            context: Context?, device: OneDevice?, vaSetupOption: String?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry?
        ): Boolean {
            context ?: return false
            device ?: return false

            val intent = Intent(context, VAActivity::class.java)
            intent.putExtra(BUNDLE_UUID, device.UUID)

            intent.putExtra(EventUtils.Dimension.DI_VA_SETUP_OPTION, vaSetupOption)
            intent.putExtra(EventUtils.Dimension.DI_COULSON_ENTRY, coulsonEntry?.value)
            context.startActivity(intent)
            return true
        }

        fun portalGVA(context: Context?, device: OneDevice?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry? = null): Boolean {
            return portalVA(context, device, EventUtils.Dimension.EnumVaSetupOption.GVA.value, coulsonEntry)
        }

        fun portalGVAAndAlexa(context: Context?, device: OneDevice?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry? = null): Boolean {
            return portalVA(context, device, EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value, coulsonEntry)
        }

        fun portalAlexa(context: Context?, device: OneDevice?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry? = null): Boolean {
            return portalVA(context, device, EventUtils.Dimension.EnumVaSetupOption.ALEXA.value, coulsonEntry)
        }


        private const val BUNDLE_UUID = "Bundle_UUID"

        private const val TAG = "VAActivity"

        const val REFACTOR_VA = BuildConfig.ENABLE_LOCAL_FLAG && !BuildConfig.IS_RELEASE

    }
}