package com.harman.va.refactor

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.databinding.FragmentVaAlexaBinding
import com.harman.log.Logger
import com.harman.portalUrl
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.UIUtils
import com.wifiaudio.view.dlg.AlexaLogoutDialog
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 * Created by sky.
 */
class VAAlexaFragment : Fragment() {

    private val viewModel: VAViewModel by activityViewModels()

    private val _startToneConfig = MutableLiveData<Boolean>()
    val startToneConfig: LiveData<Boolean>
        get() = _startToneConfig

    private val _endToneConfig = MutableLiveData<Boolean>()
    val endToneConfig: LiveData<Boolean>
        get() = _endToneConfig

    private val _voiceLanguage = MutableLiveData<String>()
    val voiceLanguage: LiveData<String>
        get() = _voiceLanguage

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel.activity = activity
        val binding = FragmentVaAlexaBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this
        binding.fragment = this
        binding.viewModel = viewModel

        refreshToneConfig()
        viewModel.registerAuthorizeListener()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        viewModel.getAlexaStatus()
    }

    private fun refreshToneConfig() {
        viewModel?.voiceLanguage?.observe(viewLifecycleOwner) { config ->
            Logger.i(TAG, "onGetVoiceLanguage:$config")
            _voiceLanguage.value = config
        }
        viewModel?.startToneConfig?.observe(viewLifecycleOwner) { config ->
            Logger.i(TAG, "onGetVoiceStart:$config")
            _startToneConfig.value = config.isEnable()
        }
        viewModel?.endToneConfig?.observe(viewLifecycleOwner) { config ->
            Logger.i(TAG, "onGetVoiceEnd:$config")
            _endToneConfig.value = config.isEnable()
        }

        viewModel?.initVoice()
        viewModel?.registerDeviceListenerForAlexa()
        viewModel?.refreshVoiceLanguageConfig()
        viewModel?.refreshStartToneConfig()
        viewModel?.refreshEndToneConfig()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel?.unregisterDeviceListenerForAlexa()
    }

    fun onBackBtnClick() {
        activity?.finish()
    }

    fun clickMusicStreamingWithAlexaCast() {
        portalUrl(
            config = AppConfigurationUtils.getMusicStreamingWithAlexaCastUrl(),
            default = music_streaming_with_alexacast
        )
    }

    fun clickMultiRoomMusic() {
        portalUrl(
            config = AppConfigurationUtils.getMultiroomMusicAlexaUrl(),
            default = multiroom_music_alexa
        )
    }

    fun clickVoiceLanguage() {
        var finalDeepLink =  AppConfigurationUtils.getVoiceLanguageUrl()?: voice_language
        UIUtils.openAppPage(activity, UIUtils.PACKAGE_NAME_ALEXA_APP, finalDeepLink)
    }

    fun clickGoToAmazonAlexaApp() {
        UIUtils.openApp(activity, UIUtils.PACKAGE_NAME_ALEXA_APP)
    }

    fun clickLogout() {
        showLogoutLWADlg()
    }
    private var logoutDialog: AlexaLogoutDialog? = null
    private fun showLogoutLWADlg() {
        if (logoutDialog != null && logoutDialog?.isShowing() == true) {
            Log.d(TAG, "showLogoutLWADlg: return")
            return
        }
        logoutDialog = activity?.let { AlexaLogoutDialog(it) }
        logoutDialog?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
            override fun onConfirm(any: Any?) {
                logoutLWA()
            }

            override fun onCancel() {
            }

        })
        logoutDialog?.show()
    }

    private fun logoutLWA() {
        WAApplication.me.showProgDlg(activity, true, "")
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val flag = viewModel?.alexaLwaLogoutWithTimeout()
            Logger.i(TAG, "logoutGoogle() >>> check result[$flag]")
            delay(8000)
            WAApplication.me.showProgDlg(activity, false, "")
            activity?.finishAfterTransition()
        }
    }

    companion object {
        private const val TAG = "VAAlexaFragment"
        private const val music_streaming_with_alexacast = "https://music.amazon.com/help?nodeId=G202196760"
        private const val multiroom_music_alexa = "https://www.amazon.com/gp/help/customer/display.html?nodeId=GZ5U38E9GGBBWEM8"
        private const val voice_language = "https://alexa.amazon.com/spa/index.html#v2/devices-channel/control-panel/all"
    }
}