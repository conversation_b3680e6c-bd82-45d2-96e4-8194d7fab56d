package com.harman.va.refactor

import com.blankj.utilcode.util.StringUtils
import com.harman.CoulsonSetupModel
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.VaOptionItem
import com.harman.discover.bean.Device
import com.harman.log.Logger

object CoulsonFirebaseManager {

    private var diActionEntry: String? = null

    private var coulsonSetupItem: CoulsonSetupModel? = null

    private var reportTimeMap = HashMap<String, Long>()

    private var targetDevice : Device? = null


    fun recordCoulsonEntryValue(coulsonEntryValue: String?, device : Device?) {
        Logger.i(TAG,"logEvent - EventName : recordCoulsonEntryValue $coulsonEntryValue ")
        diActionEntry = coulsonEntryValue
        targetDevice = device
        if(coulsonSetupItem == null){
            coulsonSetupItem = CoulsonSetupModel()
        }
        coulsonSetupItem?.apply {
            coulsonEntry = coulsonEntryValue
            coulsonSeq = (System.currentTimeMillis()/1000).toString()
        }
    }

    fun recordLandOnServiceValue(landOnServiceValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordLandOnServiceValue $landOnServiceValue ")
        if(coulsonSetupItem == null){
            coulsonSetupItem = CoulsonSetupModel()
        }
        coulsonSetupItem?.apply {
            landOnService = landOnServiceValue
        }
    }

    fun recordLandOnServiceVaValue(landOnServiceVaValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordLandOnServiceVaValue $landOnServiceVaValue ")
        if(coulsonSetupItem == null){
            coulsonSetupItem = CoulsonSetupModel()
        }
        coulsonSetupItem?.apply {
            landOnServiceVa = landOnServiceVaValue
        }
    }

    fun recordAlexaCancelValue(alexaCancelValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordAlexaCancelValue $alexaCancelValue ")
        coulsonSetupItem?.apply {
            alexaCancel = alexaCancelValue
        }
        initVaTime(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value,false)
        if (StringUtils.equals(EventUtils.Dimension.EnumVAResult.TRUE.value, alexaCancelValue) && coulsonSetupItem?.option == VaOptionItem.ALEX) {
            reportCoulsonSetupData()
        }
    }

    fun recordGvaCancelValue(gvaCancelValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordGvaCancelValue $gvaCancelValue ")
        coulsonSetupItem?.apply {
            gvaCancel = gvaCancelValue
        }
        initVaTime(EventUtils.Dimension.EnumVaSetupOption.GVA.value,false)
        if (StringUtils.equals(EventUtils.Dimension.EnumVAResult.TRUE.value, gvaCancelValue)) {
            reportCoulsonSetupData()
        }
    }

//    fun recordVaSetupOptionValueForce(vaSetupOptionValue: String?) {
//        Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValueForce $vaSetupOptionValue landOnServiceVa :${coulsonSetupItem?.landOnServiceVa}")
//        coulsonSetupItem?.apply {
//            vaSetupOptionValue?.let {
//                option = VaOptionItem.findItemByOptionName(it)
//                Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValueForce ${option?.optionName} ")
//            }
//        }
//    }

    fun recordVaSetupOptionValue(vaSetupOptionValue: String?) {
        Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValue $vaSetupOptionValue landOnServiceVa :${coulsonSetupItem?.landOnServiceVa}")
        coulsonSetupItem?.apply {
            if ((EventUtils.Dimension.EnumVAResult.TRUE.value.equals(landOnServiceVa, true) && EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value.equals(option?.optionName, true))) {
                return
            }
            vaSetupOptionValue?.let {
                option = VaOptionItem.findItemByOptionName(it)
                Logger.i(TAG,"logEvent - EventName : recordVaSetupOptionValue ${option?.optionName} ")


            }
        }
    }

    fun recordNewSetup(name: String) {
        Logger.i(TAG,"logEvent - EventName : recordNewSetup $name ")
        coulsonSetupItem?.apply {
            newSetup(name)
        }
        initVaTime(name,true)
    }

    private fun initVaTime(name: String, isStart: Boolean) {
        Logger.i(TAG,"logEvent - EventName : initVaTime $name $isStart $coulsonSetupItem")
        coulsonSetupItem?.apply {
            if(EventUtils.Dimension.EnumVaSetupOption.GVA.value == name){
                if(isStart){
                    gvaSetupStart = System.currentTimeMillis()/1000
                }else{
                    if(gvaSetupStart != CoulsonSetupModel.DEFAULT_VA_SETUP_TIME){
                        gvaSetupEnd = System.currentTimeMillis()/1000
                        gvaSetupDuration = gvaSetupEnd - gvaSetupStart
                    }

                }
                Logger.i(TAG,"logEvent - EventName : initVaTime2 $gvaSetupStart $gvaSetupEnd $gvaSetupDuration")

            }else if(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value == name){
                if(isStart){
                    alexaSetupStart = System.currentTimeMillis()/1000
                }else{

                    if(alexaSetupStart != CoulsonSetupModel.DEFAULT_VA_SETUP_TIME){
                        alexaSetupEnd = System.currentTimeMillis()/1000
                        alexaSetupDuration = alexaSetupEnd - alexaSetupStart
                    }

                }
                Logger.i(TAG,"logEvent - EventName : initVaTime2 $alexaSetupStart $alexaSetupEnd $alexaSetupDuration")
            }

        }
    }

    fun recordVaSetupResultValue(name: String, isSuccess: Boolean) {
        Logger.i(TAG,"logEvent - EventName : recordVaSetupResultValue $name $isSuccess")
        initVaTime(name,false)
        coulsonSetupItem?.apply {
            setupResult(name, isSuccess)
            val flag = isFlowFinish
            Logger.i(TAG,"logEvent - EventName : isFlowFinish $flag ")
            if (flag) {
                reportCoulsonSetupData()
            }
        }
    }

    fun printStackTrace(thread: Thread) {
        Logger.i(TAG,"logEvent - EventName : printStackTrace start ===============================================================================")
        for (e in thread.stackTrace) {
            Logger.i(TAG,"logEvent - EventName : $e")
        }
        Logger.i(TAG,"logEvent - EventName : printStackTrace end =================================================================================")
    }

     fun reportCoulsonSetupData() {
        if (canReport() == false ) {
            return
        }
        coulsonSetupItem?.let {
            val isDuplicateEvent = isDuplicateReport(it.finalResult)
            if (!isDuplicateEvent) {
                return
            }
            FirebaseEventManager.report(
                eventName = EventUtils.EventName.EVENT_ACTION,
                device = targetDevice,
                dimensions = mapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.ACTION_COULSON_SETUP.value,
                    EventUtils.Dimension.DI_COULSON_SEQ to it.coulsonSeq,
                    EventUtils.Dimension.DI_COULSON_ENTRY to it.coulsonEntry,
                    EventUtils.Dimension.DI_LAND_ON_SERVICE to it.landOnService,
                    EventUtils.Dimension.DI_LAND_ON_SERVICE_VA to it.landOnServiceVa,
                    EventUtils.Dimension.DI_ALEXA_CANCEL to it.alexaCancel,
                    EventUtils.Dimension.DI_GVA_CANCEL to it.gvaCancel,
                    EventUtils.Dimension.DI_VA_SETUP_OPTION to it.option?.optionName,
                    EventUtils.Dimension.DI_VA_SETUP_RESULT to it.finalResult,
                    EventUtils.Dimension.DI_GVA_SETUP_START to it.gvaSetupStart,
                    EventUtils.Dimension.DI_GVA_SETUP_END to it.gvaSetupEnd,
                    EventUtils.Dimension.DI_GVA_SETUP_DURATION to it.gvaSetupDuration,
                    EventUtils.Dimension.DI_ALEXA_SETUP_START to it.alexaSetupStart,
                    EventUtils.Dimension.DI_ALEXA_SETUP_END to it.alexaSetupEnd,
                    EventUtils.Dimension.DI_ALEXA_SETUP_DURATION to it.alexaSetupDuration,

                ), TAG
            )
        }


        clearData()
    }

    private fun isDuplicateReport(eventName: String): Boolean {
        val preReportTime = reportTimeMap[eventName]
        reportTimeMap[eventName] = System.currentTimeMillis()
        if (preReportTime == null) {
            return true
        }
        val diff = System.currentTimeMillis() - preReportTime
        val isValid = (diff > TIME_THRESHOLD)
        if (!isValid) {
            Logger.i(TAG,"logEvent - EventName : $eventName is not valid since it reported many times in a short period of time")
        }
        return isValid
    }

    private fun canReport(): Boolean? {
        return coulsonSetupItem?.isDataValid
    }

    private fun clearData() {
        coulsonSetupItem?.clear()
        coulsonSetupItem = null
    }

    const val TAG = "CoulsonFirebaseManager"
    private const val TIME_THRESHOLD = 5000
}