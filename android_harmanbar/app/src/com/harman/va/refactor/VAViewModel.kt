package com.harman.va.refactor

import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amazon.identity.auth.device.AuthError
import com.amazon.identity.auth.device.api.authorization.AuthCancellation
import com.amazon.identity.auth.device.api.authorization.AuthorizationManager
import com.amazon.identity.auth.device.api.authorization.AuthorizeListener
import com.amazon.identity.auth.device.api.authorization.AuthorizeRequest
import com.amazon.identity.auth.device.api.authorization.AuthorizeResult
import com.amazon.identity.auth.device.api.authorization.ProfileScope
import com.amazon.identity.auth.device.api.authorization.ScopeFactory
import com.amazon.identity.auth.device.api.workflow.RequestContext
import com.harman.EventUtils
import com.harman.bean.VoiceLanguage
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.EnumLWAStatus
import com.harman.command.one.bean.EnumVoiceSource
import com.harman.command.one.bean.EnumVoiceToneStatus
import com.harman.command.one.bean.EnumVoiceToneStatus.Companion.fromValue
import com.harman.command.one.bean.GetVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceToneResponse
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.SetVoiceToneRequest
import com.harman.connect.alexaLwaLogoutWithTimeout
import com.harman.connect.getC4aPermissionStatusWithTimeout
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.requestGoogleLogoutWithTimeout
import com.harman.discover.bean.OneDevice
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.va.EnumViewStyle
import kotlin.coroutines.resume
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by sky
 */


class VAViewModel : ViewModel(), DefaultLifecycleObserver {
     var device: OneDevice? = null
    lateinit var vaSetupOption: String


    private val _voiceLanguage = MutableLiveData<String>()
    val voiceLanguage: LiveData<String>
        get() = _voiceLanguage

    private val _startToneConfig = MutableLiveData<EnumVoiceToneStatus>()
    val startToneConfig: LiveData<EnumVoiceToneStatus>
        get() = _startToneConfig

    private val _endToneConfig = MutableLiveData<EnumVoiceToneStatus>()
    val endToneConfig: LiveData<EnumVoiceToneStatus>
        get() = _endToneConfig

    private val defaultSource = EnumVoiceSource.ALEXA

    private val _viewStyleLoadingAlexa = MutableLiveData<EnumViewStyle>()
    val viewStyleLoadingAlexa: LiveData<EnumViewStyle>
        get() = _viewStyleLoadingAlexa

    private val _viewStyleDetailAlexa = MutableLiveData<EnumViewStyle>()
    val viewStyleDetailAlexa: LiveData<EnumViewStyle>
        get() = _viewStyleDetailAlexa


    private val _viewStyleGVA = MutableLiveData<EnumViewStyle>()
    val viewStyleGVA: LiveData<EnumViewStyle>
        get() = _viewStyleGVA

    private val _viewStyleLoadingGVA = MutableLiveData<EnumViewStyle>()
    val viewStyleLoadingGVA: LiveData<EnumViewStyle>
        get() = _viewStyleLoadingGVA

    private val _viewStyleDetailGVA = MutableLiveData<EnumViewStyle>()
    val viewStyleDetailGVA: LiveData<EnumViewStyle>
        get() = _viewStyleDetailGVA

    private val _vaResult= MutableLiveData<EnumViewStyle>()
    val vaResult: LiveData<EnumViewStyle>
        get() = _vaResult

     var isNewSetupGVA = false

    fun initVoice(){
        device?.voiceLanguageResponseExt?.voiceLanguageResponse?.locale?.let { config ->
            var voiceLanguage = VoiceLanguage(config)
            _voiceLanguage.value = voiceLanguage.language + "(${voiceLanguage.countries})"
        }

        device?.startVoiceToneResponseExt?.let { config ->
            _startToneConfig.value = config.voiceToneResponse.status.fromValue()
        }

        device?.endVoiceToneResponseExt?.let { config ->
            _endToneConfig.value = config.voiceToneResponse.status.fromValue()
        }
    }

    @AnyThread
    suspend fun checkGoogleCastEnable(): Boolean = withContext(DISPATCHER_DEFAULT) {
        device?.getC4aPermissionStatusWithTimeout(logTag = TAG) ?: false
    }

    @AnyThread
    suspend fun googleLogoutWithTimeout(): Boolean = withContext(DISPATCHER_DEFAULT) {
        device?.requestGoogleLogoutWithTimeout(logTag = TAG) ?: false
    }

    @MainThread
    suspend fun getGVAStatus() {
        if(isNewSetupGVA){
            delay(1000)
        }
        _viewStyleLoadingGVA.value = EnumViewStyle.SHOW
        _viewStyleDetailGVA.value = EnumViewStyle.HIDE
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val isEnable = checkGoogleCastEnable()
            Logger.d(TAG, "getGVAStatus() >>> check result[$isEnable]")
            _viewStyleLoadingGVA.value = EnumViewStyle.HIDE
            if (isEnable) {
                _viewStyleDetailGVA.value = EnumViewStyle.SHOW
                if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value) {
//                    _viewStyleGVA.value = EnumViewStyle.BOTH_VA_ENABLE
                    if(isNewSetupGVA){
                        _vaResult.value = EnumViewStyle.BOTH_VA_ENABLE
                        CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.GVA.value,true)
                    }

                } else if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA.value) {
//                    _viewStyleGVA.value = EnumViewStyle.ONLY_GVA_ENABLE
                    if(isNewSetupGVA){
                        _vaResult.value = EnumViewStyle.ONLY_GVA_ENABLE
                        CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.GVA.value,true)
                    }

                }

            } else {
                _viewStyleDetailGVA.value = EnumViewStyle.HIDE
                if(isNewSetupGVA){
                    CoulsonFirebaseManager.recordVaSetupResultValue(EventUtils.Dimension.EnumVaSetupOption.GVA.value,false)
                }
                if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value) {
                    _viewStyleGVA.value = EnumViewStyle.GVA_OOBE_SETUP
                } else if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA.value) {
                    _viewStyleGVA.value = EnumViewStyle.GVA_NORMAL_SETUP
                }
            }
        }
    }

     fun registerDeviceListenerForAlexa() {
        device?.registerDeviceListener(oneDeviceListener)
    }

     fun unregisterDeviceListenerForAlexa() {
        device?.unregisterDeviceListener(oneDeviceListener)
    }

    private var hasOpenedAmazonWeb: Boolean = false

    fun getAlexaStatus(){
        requestContext?.onResume()
        viewModelScope.launch(DISPATCHER_FAST_MAIN){
            _viewStyleLoadingAlexa.value = EnumViewStyle.SHOW
            _viewStyleDetailAlexa.value = EnumViewStyle.HIDE
            if(hasOpenedAmazonWeb){
                Logger.d(TAG, "getAlexaStatus delay 1s")
                delay(1000)
            }
            val lwaState = loopGetLWAState()
            _viewStyleLoadingAlexa.value = EnumViewStyle.HIDE
            if (lwaState == null) {
                Logger.d(TAG, "getAlexaStatus setupAlexaFail")
                setupAlexaFail()
            } else if (lwaState.state == EnumLWAStatus.LOGGED.value) {
                _viewStyleDetailAlexa.value = EnumViewStyle.SHOW
                if(hasOpenedAmazonWeb){
                    Logger.d(TAG, "getAlexaStatus setupAlexaSuccess after login amzone")
                    setupAlexaSuccess()
                }else{
                    Logger.d(TAG, "getAlexaStatus already setup success before,just query lwa ")
                }

            } else if (lwaState.state == EnumLWAStatus.NOT_LOGIN.value) {
                if(!hasOpenedAmazonWeb){
                    hasOpenedAmazonWeb = true
                    Logger.d(TAG, "getAlexaStatus first open amazon login")
                    doLWAAuthorize(lwaState)
                }else{
                    Logger.d(TAG, "getAlexaStatus retur to app without amazon login")
                    setupAlexaFail()
                }


            }
        }
    }

    private fun doLWAAuthorize(lwaSate: LwaStateResponse?) {
        CoulsonFirebaseManager.recordNewSetup(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value)
        val scopeData = JSONObject()
        val productInstanceAttributes = JSONObject()
        try {
            productInstanceAttributes.put(
                "deviceSerialNumber",
                lwaSate?.meta?.serialNumber
            ) // TODO:
            scopeData.put("productInstanceAttributes", productInstanceAttributes);
            scopeData.put("productID", lwaSate?.meta?.productId)
        } catch (e: JSONException) {
            throw RuntimeException(e)
        }
        Logger.d(TAG, "doLWAAuthorize start: lwaSate: $lwaSate scopeData:${scopeData}")
        registerAuthorizeListener()

        try {
            AuthorizationManager.authorize(
                AuthorizeRequest.Builder(requestContext) // App Login
                    .addScopes(
                        ProfileScope.profile(),
                        ProfileScope.postalCode()
                    ) // TODO: Authorize an AVS Device Through a Companion App.
                    .addScopes(
                        ScopeFactory.scopeNamed("alexa:voice_service:pre_auth"),
                        ScopeFactory.scopeNamed("alexa:all", scopeData)
                    )
                    .forGrantType(AuthorizeRequest.GrantType.AUTHORIZATION_CODE)
                    .withProofKeyParameters(lwaSate?.meta?.codeChallenge, "S256")
                    .build()
            )
        } catch (e: Exception) {
            Logger.d(TAG, "doLWAAuthorize error:$e")
            e.printStackTrace()
        }
    }

    private val oneDeviceListener = object : IOneDeviceListener {

        override fun onGetVoiceLanguage(rsp: GetVoiceLanguageResponse) {
            Logger.d(TAG, "onGetVoiceLanguage:$rsp")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                rsp.locale?.let { config ->
                    var voiceLanguage = VoiceLanguage(config)
                    _voiceLanguage.value = voiceLanguage.language + "(${voiceLanguage.countries})"
                }
            }
        }

        override fun onGetVoiceRequestStartTone(rsp: GetVoiceToneResponse) {
            Logger.d(TAG, "onGetVoiceRequestStartTone:$rsp")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _startToneConfig.value = rsp.status.fromValue()
            }
        }

        override fun onGetVoiceRequestEndTone(rsp: GetVoiceToneResponse) {
            Logger.d(TAG, "onGetVoiceRequestEndTone:$rsp")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _endToneConfig.value = rsp.status.fromValue()
            }
        }
    }

    @MainThread
    fun refreshVoiceLanguageConfig() {
        device?.getVoiceLanguage(defaultSource)
    }

    @MainThread
    fun refreshStartToneConfig() {
        device?.getVoiceRequestStartTone(defaultSource)
    }

    @MainThread
    fun refreshEndToneConfig() {
        device?.getVoiceRequestEndTone(defaultSource)
    }

    @MainThread
    fun setStartToneConfig(config: EnumVoiceToneStatus) {
        device?.setVoiceRequestStartTone(SetVoiceToneRequest(defaultSource.value, config.value))
        _startToneConfig.value = config
    }

    @MainThread
    fun switchStartToneConfig() {
        val target = when (_startToneConfig.value) {
            EnumVoiceToneStatus.DISABLE -> EnumVoiceToneStatus.ENABLE
            EnumVoiceToneStatus.ENABLE -> EnumVoiceToneStatus.DISABLE
            else -> EnumVoiceToneStatus.ENABLE
        }

        setStartToneConfig(config = target)
    }

    @MainThread
    fun setEndToneConfig(config: EnumVoiceToneStatus) {
        device?.setVoiceRequestEndTone(SetVoiceToneRequest(defaultSource.value, config.value))
        _endToneConfig.value = config
    }

    @MainThread
    fun switchEndToneConfig() {
        val target = when (_endToneConfig.value) {
            EnumVoiceToneStatus.DISABLE -> EnumVoiceToneStatus.ENABLE
            EnumVoiceToneStatus.ENABLE -> EnumVoiceToneStatus.DISABLE
            else -> EnumVoiceToneStatus.ENABLE
        }

        setEndToneConfig(config = target)
    }


    @AnyThread
    suspend fun alexaLwaLogoutWithTimeout(): Boolean = withContext(DISPATCHER_DEFAULT) {
        device?.alexaLwaLogoutWithTimeout(logTag = TAG) ?: false
    }

    private suspend fun loopGetLWAState(): LwaStateResponse? {

        repeat(12) {
            val response = getLWAState()
            Logger.d(TAG, "loopGetLWAState:$response times index : $it")
            if (response.success() && (response.state == EnumLWAStatus.NOT_LOGIN.value || response.state == EnumLWAStatus.LOGGED.value)) {
                return response

            }
            delay(2 * 1000)
        }
        return null
    }


    suspend fun getLWAState(): LwaStateResponse =
        suspendCancellableCoroutine { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onGetLWAState(response: LwaStateResponse) {
                    device?.unregisterDeviceListener(this)
                    continuation.resume(response)
                    Logger.d(TAG, "getLWAState：response:$response")
                }
            }

            continuation.invokeOnCancellation {
                device?.unregisterDeviceListener(listener)
            }
            device?.registerDeviceListener(listener)
            device?.getLWAState()
        }
    var activity: FragmentActivity? = null
    private var requestContext: RequestContext? = null

     fun registerAuthorizeListener() {
        if (requestContext == null) {
            requestContext = RequestContext.create(activity)
            requestContext?.registerListener(authorizeListener)

        }
    }

    private var authorizeListener = object : AuthorizeListener() {

        override fun onSuccess(result: AuthorizeResult?) {

            var authorizationCode = result?.authorizationCode
            var clientId = result?.clientId
            var redirectURI = result?.redirectURI
            Logger.d(
                TAG,
                "authorizeListener：success:${authorizationCode},${clientId},${redirectURI}"
            )
            setLWAAuthCode(LWAInfo(authorizationCode, clientId, redirectURI))

        }

        override fun onError(p0: AuthError?) {
            Logger.d(TAG, "authorizeListener：onError:${p0?.message}")
            setupAlexaFail()
        }

        override fun onCancel(p0: AuthCancellation?) {
            Logger.d(TAG, "authorizeListener：onCancel:${p0?.description}")
            cancelSetupAlexa()
        }

    }

    private fun setupAlexaSuccess() {
        if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value) {
            _vaResult.postValue(EnumViewStyle.CONCURRENT_ALEXA_SUCCESS)
            Logger.d(TAG, "CONCURRENT_ALEXA_SUCCESS")

        }else if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.ALEXA.value) {
            _vaResult.postValue(EnumViewStyle.ALEXA_SUCCESS)
            Logger.d(TAG, "ALEXA_SUCCESS")
        }
    }

    private fun setupAlexaFail() {
        if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value) {
            Logger.d(TAG, "CONCURRENT_ALEXA_FAIL")
            _vaResult.postValue(EnumViewStyle.CONCURRENT_ALEXA_FAIL)

        }else if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.ALEXA.value) {
            _vaResult.postValue(EnumViewStyle.ALEXA_FAIL)
            Logger.d(TAG, "ALEXA_FAIL")
        }

    }

    private fun cancelSetupAlexa() {

        if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value) {
            _vaResult.postValue(EnumViewStyle.CONCURRENT_ALEXA_CANCEL)
            Logger.d(TAG, "CONCURRENT_ALEXA_CANCEL")

        }else if (vaSetupOption == EventUtils.Dimension.EnumVaSetupOption.ALEXA.value) {
            _vaResult.postValue(EnumViewStyle.ALEXA_CANCEL)
            Logger.d(TAG, "ALEXA_CANCEL")
        }
    }

    private fun setLWAAuthCode(lwaInfo: LWAInfo) {
        if (lwaInfo.isEmpty()) {
            Logger.d(TAG, "setLWAAuthCode：fail:${lwaInfo}")
            return
        }
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            var response = setLWAAuthCodeSuspend(lwaInfo)
            Logger.d(TAG, "setLWAAuthCode：response:${response}")
        }
    }

    suspend fun setLWAAuthCodeSuspend(lwaInfo: LWAInfo): BasicResponse =
        suspendCancellableCoroutine { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onSetLWAAuthCode(response: BasicResponse) {
                    device?.unregisterDeviceListener(this)
                    continuation.resume(response)
                }
            }

            continuation.invokeOnCancellation {
                device?.unregisterDeviceListener(listener)
            }
            device?.registerDeviceListener(listener)
            device?.setLWAAuthCode(lwaInfo)
        }


    companion object {
        private const val TAG = "VAViewModel"
    }


}
