package com.harman.va.refactor

import android.content.Context
import androidx.annotation.MainThread
import com.harman.EventUtils
import com.harman.discover.bean.OneDevice

/**
 * Created by SKY.
 */
object VAPortalHelper {
    @MainThread
    fun portalGVAAndAlexa(context: Context?, device: OneDevice?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry?) {
        VAActivity.portalGVAAndAlexa(context, device, coulsonEntry)
    }

    fun portalGVA(context: Context?, device: OneDevice?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry?) {
        VAActivity.portalGVA(context, device, coulsonEntry)
    }

    fun portalAlexa(context: Context?, device: OneDevice?, coulsonEntry: EventUtils.Dimension.EnumCoulsonEntry?) {
        VAActivity.portalAlexa(context, device, coulsonEntry)
    }


}