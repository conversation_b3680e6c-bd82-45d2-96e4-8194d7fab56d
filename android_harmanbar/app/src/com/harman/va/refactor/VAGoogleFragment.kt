package com.harman.va.refactor

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.harman.EventUtils
import com.harman.bar.app.databinding.FragmentVaGoogleBinding
import com.harman.log.Logger
import com.harman.portalUrl
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.va.EnumViewStyle
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.UIUtils
import com.wifiaudio.view.dlg.GoogleLogoutDialog
import com.wifiaudio.view.oobe.SetupGVANormalDialogFragment
import com.wifiaudio.view.oobe.SetupGVAOOBEDialogFragment
import com.wifiaudio.view.oobe.VaAreReadyDialogFragment
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevBaseDlgView
import com.wifiaudio.view.pagesdevcenter.devicesetting.calibration.ChromecastBuiltInAvailableDlgView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 * Created by sky.
 */
class VAGoogleFragment : Fragment() {

    private val viewModel: VAViewModel by activityViewModels()
    private val _viewStyle = MutableLiveData<EnumViewStyle>()
    val viewStyle: LiveData<EnumViewStyle>
        get() = _viewStyle

    private val _viewStyleLoading = MutableLiveData<EnumViewStyle>()
    val viewStyleLoading: LiveData<EnumViewStyle>
        get() = _viewStyleLoading

    private val _viewStyleDetail = MutableLiveData<EnumViewStyle>()
    val viewStyleDetail: LiveData<EnumViewStyle>
        get() = _viewStyleDetail


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentVaGoogleBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this
        binding.fragment = this
        observe()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch {
            viewModel.getGVAStatus()
        }

    }

    private fun observe(){
        viewModel.viewStyleLoadingGVA.observe(viewLifecycleOwner) { item ->
            Logger.d(TAG, "viewStyleLoadingGVA item = $item")
            _viewStyleLoading.value = item
        }

        viewModel.viewStyleDetailGVA.observe(viewLifecycleOwner) { item ->
            Logger.d(TAG, "viewStyleDetailGVA item = $item")
            _viewStyleDetail.value = item
        }

        viewModel.viewStyleGVA.observe(viewLifecycleOwner) { item ->
            Logger.d(TAG, "viewStyleGVA item = $item")
            _viewStyle.value = item
            if(item == EnumViewStyle.BOTH_VA_ENABLE){
                showBothVAEnable()
            }else if(item == EnumViewStyle.ONLY_GVA_ENABLE){
//                showOnlyGVAEnable()

            }else if(item == EnumViewStyle.GVA_NORMAL_SETUP){
                showSetupGVANormalDialogFragment()

            }else if(item == EnumViewStyle.GVA_OOBE_SETUP){
                showSetupGVAOOBEDialogFragment()

            }
        }
    }

    private var vaAreReadyDialogFragment: VaAreReadyDialogFragment? = null
    private fun showBothVAEnable() {
        if (vaAreReadyDialogFragment == null) {
            vaAreReadyDialogFragment = VaAreReadyDialogFragment()
        }

        if (vaAreReadyDialogFragment?.isAdded == false) {
            vaAreReadyDialogFragment?.onCloseListener = object : VaAreReadyDialogFragment.OnCloseListener {
                override fun onClose() {

                }
            }
            fragmentManager?.let {
                vaAreReadyDialogFragment?.show(it, VaAreReadyDialogFragment::class.java.simpleName)
            }
        }
    }

    private fun showOnlyGVAEnable() {
        if (availableView != null) {
            availableView = if (availableView!!.isShowing()!!) return else {
                availableView!!.dismissDialog()
                null
            }
        }
        activity?.let {
            availableView = ChromecastBuiltInAvailableDlgView(it, 2)

            availableView?.show()
        }

    }

    private var setupGVANormalDialogFragment: SetupGVANormalDialogFragment? = null
    private fun showSetupGVANormalDialogFragment() {
        if (setupGVANormalDialogFragment == null) {
            setupGVANormalDialogFragment = SetupGVANormalDialogFragment()
        }
        if (setupGVANormalDialogFragment?.isAdded == false) {
            setupGVANormalDialogFragment?.onCloseListener = object : SetupGVANormalDialogFragment.OnCloseListener {
                override fun onClose() {
                    activity?.finishAfterTransition()
                    CoulsonFirebaseManager.recordGvaCancelValue(EventUtils.Dimension.EnumVAResult.TRUE.value)

                }
            }
            setupGVANormalDialogFragment?.setOnSetupListener(object : SetupGVANormalDialogFragment.OnSetupListener {
                override fun onSetup() {
                    viewModel.isNewSetupGVA = true
                    CoulsonFirebaseManager.recordNewSetup(EventUtils.Dimension.EnumVaSetupOption.GVA.value)
                }

            })
            fragmentManager?.also {
                setupGVANormalDialogFragment?.show(it, SetupGVANormalDialogFragment::class.java.simpleName)
            }
        }
    }

    private var setupGVAOOBEDialogFragment: SetupGVAOOBEDialogFragment? = null
    private fun showSetupGVAOOBEDialogFragment() {
        if (setupGVAOOBEDialogFragment == null) {
            setupGVAOOBEDialogFragment = SetupGVAOOBEDialogFragment()
        }
        if (setupGVAOOBEDialogFragment?.isAdded == false) {
            setupGVAOOBEDialogFragment?.onCloseListener = object : SetupGVAOOBEDialogFragment.OnCloseListener {
                override fun onClose() {
                    activity?.finishAfterTransition()
                    CoulsonFirebaseManager.recordGvaCancelValue(EventUtils.Dimension.EnumVAResult.TRUE.value)
                }
            }

            setupGVAOOBEDialogFragment?.setOnSetupListener(object : SetupGVAOOBEDialogFragment.OnSetupListener {
                override fun onSetup() {
                    viewModel.isNewSetupGVA = true
                    CoulsonFirebaseManager.recordNewSetup(EventUtils.Dimension.EnumVaSetupOption.GVA.value)
                }

            })
            fragmentManager?.also {
                setupGVAOOBEDialogFragment?.show(it, SetupGVAOOBEDialogFragment::class.java.simpleName)
            }
        }
    }

    private var availableView: ChromecastBuiltInAvailableDlgView? = null


    fun onBackBtnClick() {
        activity?.finish()
    }

    fun clickChromecastBuildIn() {
        portalUrl(
            config = AppConfigurationUtils.getChromecastBuiltInUrl(),
            default = chromecast_built_in
        )
    }
    fun clickMultiRoomMusic() {
        portalUrl(
            config = AppConfigurationUtils.getMultiroomMusicGoogleUrl(),
            default = multiroom_music_google
        )
    }
    fun clickMyAssistantActivity() {
        portalUrl(
            config = AppConfigurationUtils.getMyAssistantActivity(),
            default = my_assistant_activity
        )
    }

    fun clickGoToGoogleApp() {
        UIUtils.openAppPage(activity, UIUtils.PACKAGE_NAME_GOOGLE_HOME_APP, "googlehome://devices")
    }
    fun clickLogout() {
        showGoogleLogoutDlg()
    }

    private var logoutDialog: GoogleLogoutDialog? = null
    private fun showGoogleLogoutDlg() {
        if (logoutDialog != null && logoutDialog?.isShowing() == true) {
            Log.d(TAG, "showGoogleLogoutDlg: return")
            return
        }
        logoutDialog = this?.activity?.let { GoogleLogoutDialog(it) }
        logoutDialog?.setDlgClickListener(object : DevBaseDlgView.IOnDlgBtnClickListener {
            override fun onConfirm(any: Any?) {
                logoutGoogle()
            }

            override fun onCancel() {
            }

        })
        logoutDialog?.show()
    }

    private fun logoutGoogle() {

        WAApplication.me.showProgDlg(activity, true, "")
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val flag = viewModel?.googleLogoutWithTimeout()
            Logger.i(TAG, "logoutGoogle() >>> check result[$flag]")
            delay(5000)
            WAApplication.me.showProgDlg(activity, false, "")

            activity?.finishAfterTransition()
        }


    }



    companion object {
        private const val TAG = "VAGoogleFragment"

        private const val chromecast_built_in = "https://www.google.com/chromecast/built-in/audio/"

        private const val multiroom_music_google = "https://support.google.com/assistant/answer/9210727?hl"

        private const val my_assistant_activity = "https://myactivity.google.com/product/assistant?utm_source=assistant-3p&utm_medium=jblauthentics"
    }
}