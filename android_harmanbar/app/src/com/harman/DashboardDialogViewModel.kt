package com.harman

import android.app.Activity
import android.content.Context
import androidx.annotation.AnyThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.harman.command.HttpResponseCastException
import com.harman.command.TimeoutException
import com.harman.command.WriteBytesCharacteristicException
import com.harman.command.WriteCmdCharacteristicException
import com.harman.command.common.IGeneralCommand
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.GetDeviceInfoAttachOneCommand
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.FeatureSupportResponse
import com.harman.command.one.bean.GetOtaAccessPointResponse
import com.harman.command.partybox.gatt.auth.EnumAuthAction
import com.harman.command.partybox.spp.rsp.BaseSppRspCommand
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.BaseSppSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.PartyBoxGattSession
import com.harman.connect.PartyBoxSppSession
import com.harman.connect.listener.IBaseSPPListener
import com.harman.connect.listener.IGattListener
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.listener.IPartyBoxSppListener
import com.harman.connect.listener.IPartyLightDeviceListener
import com.harman.connect.listener.IPortableDeviceListener
import com.harman.connect.session.OneBusiness
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetFirmwareVersionWithTimeout
import com.harman.eq.EQExts.toEQSettings
import com.harman.ota.PartyOtaFetchTask
import com.harman.ota.RemoteUpdateModel
import com.harman.ota.partybox.PartyBoxOtaActivity
import com.harman.ota.partylight.PartyLightOtaActivity
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.bean.PortableDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.EnumProductLine
import com.harman.discover.util.Tools.asString
import com.harman.discover.util.Tools.isPartyBoxCategory
import com.harman.discover.util.Tools.repeatWithTimeout
import com.harman.log.Logger
import com.harman.task.partybox.EnumPartyBoxDfuStatus
import com.harman.task.partybox.IPartyBoxDfuTaskListener
import com.harman.task.partybox.PartyBoxOtaTask
import com.harman.task.partylight.EnumPartyLightDfuStatus
import com.harman.task.partylight.IPartyLightDfuTaskListener
import com.harman.task.partylight.PartyLightOtaTask
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harmanbar.ble.entity.BLEResponse
import com.harmanbar.ble.model.HBApInfo
import com.harmanbar.ble.model.HBBLEDeviceInfo
import com.harmanbar.ble.utils.GsonUtil
import com.harmanbar.ble.utils.HexUtil
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.LinkedList
import java.util.Queue
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.math.roundToInt

/**
 * Created by gerrardzhang on 2024/2/27.
 */
class DashboardDialogViewModel : ViewModel() {

    private val _baseDevice = MutableLiveData<Device>()
    val baseDevice: LiveData<Device>
        get() = _baseDevice

    val dashboardStyle: LiveData<EnumProductLine> = _baseDevice.map { device ->
        device.productLine
    }

    private val _bleConnectionStatus = MutableLiveData<EnumConnectionStatus>(EnumConnectionStatus.DISCONNECTED)
    val bleConnectionStatus: LiveData<EnumConnectionStatus>
        get() = _bleConnectionStatus

    private val _sppConnectionStatus = MutableLiveData<EnumConnectionStatus>(EnumConnectionStatus.DISCONNECTED)
    val sppConnectionStatus: LiveData<EnumConnectionStatus>
        get() = _sppConnectionStatus

    private val _mtuSize = MutableLiveData<String>("-1")
    val mtuSize: LiveData<String>
        get() = _mtuSize

    private val _receivedPayloads = MutableLiveData<Queue<String>>()
    val receivedPayloads: LiveData<Queue<String>>
        get() = _receivedPayloads

    private val _deviceInfo = MutableLiveData<String>()
    val deviceInfo: LiveData<String>
        get() = _deviceInfo

    private val _commandSendStatusDisplay = MutableLiveData<String>("IDLE")
    val commandSendStatusDisplay: LiveData<String>
        get() = _commandSendStatusDisplay

    var sessionPriority: EnumSessionPriority = EnumSessionPriority.WIFI

    private val _otaModel = MutableLiveData<RemoteUpdateModel?>()

    private var lastPartyBoxOtaTask: PartyBoxOtaTask? = null
    private var lastPartyLightOtaTask: PartyLightOtaTask? = null

    private val _volume = MutableLiveData<Int>(0)
    val volume: LiveData<String>
        get() = _volume.map { v -> "Vol[$v]" }

    private val _isMute = MutableLiveData<Boolean>(false)
    val isMute: LiveData<String>
        get() = _isMute.map { v -> "isMute[$v]" }

    private val _isPlay = MutableLiveData<Boolean>(false)
    val isPlay: LiveData<String>
        get() = _isPlay.map { v -> "isPlay[$v]" }

    private val _songName = MutableLiveData<String?>(null)
    val songName: LiveData<String?>
        get() = _songName

    private val _artistName = MutableLiveData<String?>(null)
    val artistName: LiveData<String?>
        get() = _artistName

    private val _isGoogleCastEnable = MutableLiveData<Boolean>()
    val isGoogleCastEnable: LiveData<Boolean>
        get() = _isGoogleCastEnable

    private val getOtaAccessPointRsp = MutableLiveData<GetOtaAccessPointResponse?>()

    fun updateDevice(device: Device) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            _baseDevice.value = device
            _deviceInfo.value = device.toString()
            _bleConnectionStatus.value = device.gattSession?.state
            device.registerDeviceListener(deviceListener)

            if (device is OneDevice && device.ableSendCmd) {
                device.getFeatureSupport()
                device.getC4aPermissionStatus()
                _isGoogleCastEnable.value = device.isChromeCastEnabled
            }
        }
    }

    fun onDestroy() {
        val hmDevice = _baseDevice.value ?: return
        hmDevice.unregisterDeviceListener(deviceListener)
    }

    fun clearRecentPayloads() {
        _receivedPayloads.value = LinkedList()
    }

    fun connectBLE(context: Context) {
        val device = _baseDevice.value ?: return
        val gattSession = device.gattSession ?: return

        _mtuSize.value = "${gattSession.mtuSize}"

        val listener = deviceListener
        _deviceListener = listener
        device.registerDeviceListener(listener)
        gattSession.connect(context)
    }

    fun disconnectBLE() {
        _receivedPayloads.value = LinkedList()
        _mtuSize.value = "-1"
        _bleConnectionStatus.value = EnumConnectionStatus.DISCONNECTED

        _deviceListener?.let { listener ->
            _baseDevice.value?.unregisterDeviceListener(listener)
        }

        _baseDevice.value?.gattSession?.disconnect()
    }

    fun connectSPP(context: Context) {
        val sppSession = _baseDevice.value?.bleDevice?.sppSession ?: return
        sppSession.registerConnectionListener(sppSessionListener)
        sppSession.connect(context)
    }

    fun disconnectSPP() {
        _receivedPayloads.value = LinkedList()
        _baseDevice.value?.bleDevice?.sppSession?.disconnect()
    }

    private val jblOneSpecChannelBusiness: OneBusiness?
        get() {
            val hmDevice = _baseDevice.value as? OneDevice ?: return null

            return when (sessionPriority) {
                EnumSessionPriority.WIFI -> hmDevice.wifiSession
                EnumSessionPriority.BLE -> hmDevice.gattBusiness
                EnumSessionPriority.AUTO -> hmDevice.adaptBusiness
            }
        }

    private val sppSessionListener: IBaseSPPListener
        get() {
            val hmDevice = _baseDevice.value ?: return baseSppSessionListener

            return when (hmDevice.productLine) {
                EnumProductLine.PARTY_BOX -> partyBoxSppSessionListener
                else -> baseSppSessionListener
            }
        }

    fun getPartyBoxDevInfo() {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        _commandSendStatusDisplay.value = "SENDING"
        device.reqDeviceInfo(protocol = device.getBLEProtocol())
    }

    fun setPartyBoxDevInfo(contentBytes: ByteArray) {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        _commandSendStatusDisplay.value = "SENDING"
        device.setDeviceInfo(protocol = device.getBLEProtocol(), contentBytes = contentBytes)
    }

    fun getPartyBoxDfuInfo() {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        _commandSendStatusDisplay.value = "SENDING"
        device.getDfuInfo()
    }

    fun getPortableDevInfo() {
        val device = _baseDevice.value as? PortableDevice ?: return
        _commandSendStatusDisplay.value = "SENDING"
        device.reqDeviceInfo()
    }

    fun setPortableDevInfo(contentBytes: ByteArray) {
        val device = _baseDevice.value as? PortableDevice ?: return
        _commandSendStatusDisplay.value = "SENDING"
        device.setDeviceInfo(contentBytes)
    }

    fun getJBLOneDevInfo() {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.sendAttachCommand(GetDeviceInfoAttachOneCommand())
    }

    fun setJBLOneDevName(name: String) {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.sendSetDeviceName(name)
    }

    fun getJBLOneAPList() {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.getAPList()
    }

    fun setJBLOneCastGroup() {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.setCastGroup(demoSetCastGroupJson)
    }

    fun getJBLOneVolume() {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.getRemoteVolume()
    }

    fun getJBLOneFeatSupport() {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.getFeatureSupport()
    }

    fun getJBLOneEQList() {
        val business = jblOneSpecChannelBusiness ?: return
        _commandSendStatusDisplay.value = "SENDING"
        business.getEQList()
    }

    fun playMusic() {
        val musicController = jblOneSpecChannelBusiness ?: return
        musicController.playMusic()
    }

    fun pauseMusic() {
        val musicController = jblOneSpecChannelBusiness ?: return
        musicController.pauseMusic()
    }

    fun prevMusic() {
        val musicController = jblOneSpecChannelBusiness ?: return
        musicController.prevMusic()
    }

    fun nextMusic() {
        val musicController = jblOneSpecChannelBusiness ?: return
        musicController.nextMusic()
    }

    fun volumeUp() {
        val musicController = jblOneSpecChannelBusiness ?: return
        val device = baseDevice.value ?: return
        val currentVol = device.volume
        musicController.setRemoteVolume(currentVol + 5)
    }

    fun volumeDown() {
        val musicController = jblOneSpecChannelBusiness ?: return
        val device = baseDevice.value ?: return
        val currentVol = device.volume
        musicController.setRemoteVolume(currentVol - 5)
    }

    fun fetchPartyBoxRemoteOtaConfigAndDownload(context: Context) {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        val btDevice = device.bleDevice ?: return

        val pid = btDevice.pid
        if (pid.isNullOrBlank()) {
            return
        }

        val firmwareVer = btDevice.firmwareVersion ?: return
        executeDownload(context = context, pid = pid, firmwareVer = firmwareVer)
    }

    fun executePartyLightOtaFlow(context: Context) = viewModelScope.launch(DISPATCHER_FAST_MAIN) {
        val device = _baseDevice.value as? PartyLightDevice ?: return@launch
        val btDevice = device.bleDevice ?: return@launch

        val pid = btDevice.pid
        if (pid.isNullOrBlank()) {
            return@launch
        }

        val firmwareVer = btDevice.firmwareVersion ?: return@launch

        val model = withContext(DISPATCHER_API) {
            repeatWithTimeout(timeoutMills = 60 * 1000) {
                suspendCoroutine { continuation ->
                    PartyOtaFetchTask(
                        pid = pid,
                        appVersion = "99.99.99",
                        deviceFirmwareVer = firmwareVer,
                    ) { model ->
                        continuation.resume(model)
                    }.execute(context = context)
                }
            }
        }

        if (null == model || !model.isUpdateAvailable) {
            _commandSendStatusDisplay.value = "OTA Unavailable"
            return@launch
        }

        _otaModel.value = model
        _receivedPayloads.postValue(model.toString())
        _commandSendStatusDisplay.value = "OTA Available"

        val remoteFirmwareVersion = model.releaseVersion
        if (remoteFirmwareVersion.isNullOrBlank()) {
            return@launch
        }

        val otaFilePath = model.localFilePath
        if (otaFilePath.isNullOrBlank()) {
            return@launch
        }

        val newTask = PartyLightOtaTask(
            device = device,
            remoteFirmwareVersion = remoteFirmwareVersion,
            otaFilePath = otaFilePath
        ).also { task ->
            task.registerListener(partyLightDfuFlowListener)
            lastPartyLightOtaTask = task
        }

        viewModelScope.launch(DISPATCHER_DEFAULT) {
            val otaSuccess = newTask.prepareAndStartOtaFlow(context = context, platform = btDevice.platform)

            newTask.unregisterListener(partyLightDfuFlowListener)
        }
    }

    fun cancelPartyLightDfu() {
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            lastPartyLightOtaTask?.let { task ->
                task.cancelOta()
                task.unregisterListener(partyLightDfuFlowListener)
            }
        }
    }

    private fun executeDownload(context: Context, pid: String, firmwareVer: String) {
        val task = PartyOtaFetchTask(
            pid = pid,
            appVersion = "99.99.99",
            deviceFirmwareVer = firmwareVer,
            listener = remotePartyBoxOtaConfigListener
        )

        viewModelScope.launch(DISPATCHER_API) {
            task.execute(context = context)
        }
    }

    private val remotePartyBoxOtaConfigListener =
        _root_ide_package_.com.harman.ota.FirmwareUpdateStatus { model ->
            _otaModel.postValue(model)
            _receivedPayloads.postValue(model.toString())
            _commandSendStatusDisplay.postValue(
                if (model?.isUpdateAvailable == true) {
                    "OTA Available"
                } else {
                    "OTA Unavailable"
                }
            )
        }

    fun startOrResumePartyBoxDfu() {
        val otaModel = _otaModel.value ?: return
        val remoteFirmwareVersion = otaModel.releaseVersion
        if (remoteFirmwareVersion.isNullOrBlank()) {
            return
        }

        val otaFilePath = otaModel.localFilePath
        if (otaFilePath.isNullOrBlank()) {
            return
        }

        val device = _baseDevice.value as? PartyBoxDevice ?: return
        val sppSession = device.sppSession as? PartyBoxSppSession ?: run {
            return
        }

        val newTask = PartyBoxOtaTask(
            sppSession = sppSession,
            gattSession = device.gattSession as? PartyBoxGattSession
        ).also { task ->
            task.registerListener(partyBoxDfuFlowListener)
            lastPartyBoxOtaTask = task
        }

        viewModelScope.launch(DISPATCHER_DEFAULT) {
            val otaSuccess = newTask.startOrResumeDfuFlow(
                platform = device.bleDevice?.platform,
                remoteFirmwareVersion = remoteFirmwareVersion,
                otaFilePath = otaFilePath
            )

            newTask.unregisterListener(partyBoxDfuFlowListener)
        }
    }

    fun cancelPartyBoxDfu() {
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            lastPartyBoxOtaTask?.let { task ->
                task.cancelOta()
                task.unregisterListener(partyBoxDfuFlowListener)
            }
        }
    }

    private val partyBoxDfuFlowListener = object : IPartyBoxDfuTaskListener {
        override fun onDfuStatusChanged(status: EnumPartyBoxDfuStatus, e: Exception?) {
            val info = "Dfu [${status}]${e ?: ""}"

            _commandSendStatusDisplay.postValue(info)
        }

        override fun onProgress(current: Int, total: Int, progress: Float) {
            _commandSendStatusDisplay.postValue("Dfu trans: ${(progress * 100).roundToInt()}")
        }
    }

    private val partyLightDfuFlowListener = object : IPartyLightDfuTaskListener {
        override fun onDfuStatusChanged(task: PartyLightOtaTask, status: EnumPartyLightDfuStatus, e: Exception?) {
            val info = "Dfu [${status}]${e ?: ""}"

            _commandSendStatusDisplay.postValue(info)
        }

        override fun onProgress(task: PartyLightOtaTask, current: Int, total: Int, progress: Float) {
            _commandSendStatusDisplay.postValue("Dfu trans: ${(progress * 100).roundToInt()}")
        }

        override fun onBLEAdvReceived(task: PartyLightOtaTask, match: Boolean) {
            // no impl
        }
    }

    private var _deviceListener: IGattListener? = null

    private val deviceListener: IGattListener?
        get() {
            val device = baseDevice.value ?: return null

            return when (device.productLine) {
                EnumProductLine.ONE -> jblOneListener
                EnumProductLine.PARTY_BOX -> partyBoxListener
                EnumProductLine.PORTABLE -> portableListener
                EnumProductLine.PARTY_LIGHT -> partyLightListener
                else -> null
            }
        }

    private val partyLightListener = object : IPartyLightDeviceListener {
        @WorkerThread
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            handleGattStatusChange(status = status, session = session)
        }

        @WorkerThread
        override fun onGattMtuChanged(size: Int, session: BaseGattSession<*, *, *>) {
            handleMTUChanged(size)
        }

        @WorkerThread
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            _commandSendStatusDisplay.postValue("RECEIVED")
            _receivedPayloads.postValue(HexUtil.byte2HexStr(receivedCommand.payload))
        }

        @WorkerThread
        override fun onGattException(e: Exception, session: BaseGattSession<*, *, *>) {
            handleException(e)
        }
    }

    private val partyBoxListener = object : IPartyBoxDeviceListener {
        @WorkerThread
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            handleGattStatusChange(status = status, session = session)
        }

        @WorkerThread
        override fun onGattMtuChanged(size: Int, session: BaseGattSession<*, *, *>) {
            handleMTUChanged(size)
        }

        @WorkerThread
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            _commandSendStatusDisplay.postValue("RECEIVED")
            _receivedPayloads.postValue(HexUtil.byte2HexStr(receivedCommand.payload))
        }

        @WorkerThread
        override fun onGattException(e: Exception, session: BaseGattSession<*, *, *>) {
            handleException(e)
        }

        @WorkerThread
        override fun onVolume(volume: Int) {
            _volume.postValue(volume)
        }

        @WorkerThread
        override fun onMute(isMute: Boolean) {
            _isMute.postValue(isMute)
        }
    }

    private val portableListener = object : IPortableDeviceListener {
        @WorkerThread
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            handleGattStatusChange(status = status, session = session)
        }

        @WorkerThread
        override fun onGattMtuChanged(size: Int, session: BaseGattSession<*, *, *>) {
            handleMTUChanged(size)
        }

        @WorkerThread
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            _commandSendStatusDisplay.postValue("RECEIVED")
            _receivedPayloads.postValue(HexUtil.byte2HexStr(receivedCommand.payload))
        }

        @WorkerThread
        override fun onGattException(e: Exception, session: BaseGattSession<*, *, *>) {
            handleException(e)
        }

        @WorkerThread
        override fun onVolume(volume: Int) {
            _volume.postValue(volume)
        }

        @WorkerThread
        override fun onMute(isMute: Boolean) {
            _isMute.postValue(isMute)
        }
    }

    private val jblOneListener = object : IOneDeviceListener {

        @WorkerThread
        override fun onAttachCommandReceived(rsp: Any) {
            _commandSendStatusDisplay.postValue("RECEIVED")
            _receivedPayloads.postValue(rsp.toString())
        }

        @WorkerThread
        override fun onRspSuccess(cmd: String?, content: String?, port: String?) {
            _commandSendStatusDisplay.postValue("RECEIVED")
            _receivedPayloads.postValue(content ?: "")
        }

        @WorkerThread
        override fun onRspFailure(cmd: String?, e: Exception, port: String?) {
            handleException(e)
        }

        @WorkerThread
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            handleGattStatusChange(status = status, session = session)
        }

        @WorkerThread
        override fun onGattMtuChanged(size: Int, session: BaseGattSession<*, *, *>) {
            handleMTUChanged(size)
        }

        @WorkerThread
        override fun onCharacteristicWriteSuccess(bytes: ByteArray) {
            _commandSendStatusDisplay.postValue("WRITE SUCCESS")
        }

        @WorkerThread
        override fun onGattException(e: Exception, session: BaseGattSession<*, *, *>) {
            handleException(e)
        }

        @WorkerThread
        override fun onResponseReceived(response: BLEResponse) {
            val strData = response.responseData ?: return

            handleJBLOneJsonBody(code = response.responsedCode, strData = strData)
        }

        @WorkerThread
        override fun onVolume(volume: Int) {
            _volume.postValue(volume)
        }

        override fun onPlayStatus(isPlay: Boolean) {
            _isPlay.postValue(isPlay)
        }

        @WorkerThread
        override fun onMute(isMute: Boolean) {
            _isMute.postValue(isMute)
        }

        @WorkerThread
        override fun onFeatureSupport(featureSupport: FeatureSupport) {
            _receivedPayloads.postValue(GsonUtil.parseBeanToJson(featureSupport))
        }

        @WorkerThread
        override fun onEQList(eqListResponse: EQListResponse) {
            _receivedPayloads.postValue(GsonUtil.parseBeanToJson(eqListResponse))
        }

        @WorkerThread
        override fun receivedUPNPNotify(body: String, uuid: String) {
            Logger.d(TAG, "receivedUPNPNotify() >>> uuid[$uuid] body:$body")
        }

        @WorkerThread
        override fun onSongName(songName: String?) {
            _songName.postValue(songName)
        }

        @WorkerThread
        override fun onArtistName(artistName: String?) {
            _artistName.postValue(artistName)
        }

        @WorkerThread
        override fun onGetOTAAccessPoint(rsp: GetOtaAccessPointResponse) {
            _receivedPayloads.postValue(GsonUtil.parseBeanToJson(rsp))
            getOtaAccessPointRsp.postValue(rsp)
        }

        @WorkerThread
        override fun onC4aPermissionStatus(rsp: C4aPermissionStatusResponse) {
            if (rsp.success()) {
                _isGoogleCastEnable.postValue(rsp.enable)
            }
        }

        @WorkerThread
        override fun onRequestGoogleLogout(rsp: BasicResponse) {
            if (rsp.success()) {
                _isGoogleCastEnable.postValue(false)
            }
        }
    }

    @AnyThread
    private fun handleGattStatusChange(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
        when (status) {
            EnumConnectionStatus.DISCONNECTED -> {
                _mtuSize.postValue("-1")
            }

            EnumConnectionStatus.CONNECTED -> {
                val device = when (session) {
                    is BaseGattSession<*, *, *> -> session.device
                    is BaseBrEdrSession<*, *, *> -> session.device
                    else -> return
                }

                if (device is OneBTDevice) {
                    device.getFeatureSupport()
                }
            }

            else -> {}
        }

        _bleConnectionStatus.postValue(status)
    }

    @AnyThread
    private fun handleMTUChanged(size: Int) {
        _mtuSize.postValue("$size")
    }

    @AnyThread
    private fun handleException(e: Exception) {
        _commandSendStatusDisplay.postValue(
            when (e) {
                is TimeoutException -> "TIMEOUT"
                is WriteCmdCharacteristicException -> "WRITE CMD ERR"
                is WriteBytesCharacteristicException -> "WRITE BYTES ERR"
                is HttpResponseCastException -> "HTTP ERR"
                else -> "UNKNOWN"
            }
        )
    }

    @Synchronized
    private fun MutableLiveData<Queue<String>>.postValue(newValue: String) {
        val queue = value ?: EvictingQueue<String>(5)
        queue.offer(newValue)
        postValue(queue)
    }

    @AnyThread
    private fun handleJBLOneJsonBody(code: Int, strData: String) {
        when (code) {
            EnumCommandMapping.GET_DEVICE_INFO.bleCmd -> {
                val devInfo =
                    GsonUtil.parseJsonToBean(strData, HBBLEDeviceInfo::class.java) ?: return

                _receivedPayloads.postValue(devInfo.toString())
            }

            EnumCommandMapping.LP_GET_AP_LIST_CMD.bleCmd -> {
                val apInfo =
                    GsonUtil.parseJsonToBean<HBApInfo>(strData, HBApInfo::class.java) ?: return

                _receivedPayloads.postValue(apInfo.asString())
            }

            EnumCommandMapping.GET_FEATURE_SUPPORT.bleCmd -> {
                val featSupport =
                    GsonUtil.parseJsonToBean<FeatureSupportResponse>(strData, FeatureSupportResponse::class.java) ?: return

                _receivedPayloads.postValue(GsonUtil.parseBeanToJson(featSupport))
            }

            EnumCommandMapping.GET_EQ_LIST.bleCmd -> {
                val eqListResponse =
                    GsonUtil.parseJsonToBean<EQListResponse>(strData, EQListResponse::class.java) ?: return

                _receivedPayloads.postValue(GsonUtil.parseBeanToJson(eqListResponse))
            }
        }
    }

    private val partyBoxSppSessionListener = object : IPartyBoxSppListener {
        override fun onCommandReceived(device: BaseBTDevice<*, *>, receivedCommand: BaseSppRspCommand) {
            _receivedPayloads.postValue(receivedCommand.toString())
        }

        override fun onStatusChanged(status: EnumConnectionStatus, session: BaseSppSession) {
            handleSppStatusChange(status = status, session = session, listener = this)
        }

        override fun onReceived(bytes: ByteArray) {
            // no impl
        }
    }

    private val baseSppSessionListener = object : IBaseSPPListener {
        override fun onStatusChanged(status: EnumConnectionStatus, session: BaseSppSession) {
            handleSppStatusChange(status = status, session = session, listener = this)
        }

        override fun onReceived(bytes: ByteArray) {
            // no impl
        }
    }

    @AnyThread
    private fun handleSppStatusChange(
        status: EnumConnectionStatus,
        session: BaseSppSession,
        listener: IBaseSPPListener
    ) {
        when (status) {
            EnumConnectionStatus.DISCONNECTED -> {
                session.unregisterConnectionListener(listener)
            }

            else -> {}
        }

        _sppConnectionStatus.postValue(status)
    }

    private val demoSetCastGroupJson = """
        {
          "group_info": {
            "disabled": false,
            "group": {
              "group_id": "7c49",
              "id": "78669d1c7d92",
              "name": "JBL Charge 5 Wi-Fi",
              "p2p_mac": "7a:66:9d:1c:7d:93",
              "type": "multichannel"
            },
            "members": [
              {
                "channel": [
                  "front_right"
                ],
                "color_id": 1,
                "crc": "278d",
                "device_name": "JBL Charge 5 Wi-Fi",
                "friendly_name": "JBL Charge 5 Wi-Fi",
                "id": "78669d1c7d92"
              },
              {
                "channel": [
                  "front_left"
                ],
                "color_id": 1,
                "crc": "7c5e",
                "device_name": "JBL Charge 5 Wi-Fi",
                "friendly_name": "Stereo",
                "id": "287e8016a118"
              }
            ]
          },
          "password": "98769593"
        }
    """.trimIndent()

    internal fun launchOta(
        device: PartyBoxDevice,
        activity: Activity
    ) {
        val pid = device.pid
        if (pid.isNullOrBlank()) {
            return
        }

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val connect = device.syncGattConnectWithTimeout(context = activity)
            if (!connect) {
                return@launch
            }

            var deviceFirmwareVersion = device.firmwareVersion
            if (deviceFirmwareVersion.isNullOrBlank()) {
                val success = device.syncGetFirmwareVersionWithTimeout(protocol = device.getBLEProtocol())
                if (!success) {
                    return@launch
                }
            }

            deviceFirmwareVersion = device.firmwareVersion
            if (deviceFirmwareVersion.isNullOrBlank()) {
                return@launch
            }

            val model = withContext(DISPATCHER_API) {
                repeatWithTimeout(timeoutMills = 60 * 1000) {
                    suspendCoroutine { continuation ->
                        PartyOtaFetchTask(
                            pid = pid,
                            appVersion = "99.99.99",
                            deviceFirmwareVer = deviceFirmwareVersion,
                        ) { model ->
                            continuation.resume(model)
                        }.execute(context = activity)
                    }
                }
            }

            if (null == model || !model.isUpdateAvailable) {
                _commandSendStatusDisplay.value = "OTA Unavailable"
                return@launch
            }

            PartyBoxOtaActivity.launchOtaPage(activity = activity, launcher = null, device = device, model = model)
        }
    }

    internal fun launchOta(
        device: PartyLightDevice,
        activity: Activity
    ) {
        val pid = device.pid
        if (pid.isNullOrBlank()) {
            return
        }

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val deviceFirmwareVersion = device.firmwareVersion
            if (deviceFirmwareVersion.isNullOrBlank()) {
                return@launch
            }

            val model = withContext(DISPATCHER_API) {
                repeatWithTimeout(timeoutMills = 60 * 1000) {
                    suspendCoroutine { continuation ->
                        PartyOtaFetchTask(
                            pid = pid,
                            appVersion = "99.99.99",
                            deviceFirmwareVer = deviceFirmwareVersion,
                        ) { model ->
                            continuation.resume(model)
                        }.execute(context = activity)
                    }
                }
            }

            if (null == model || !model.isUpdateAvailable) {
                _commandSendStatusDisplay.value = "OTA Unavailable"
                return@launch
            }

            PartyLightOtaActivity.launchOtaPage(
                activity = activity,
                pid = pid,
                model = model
            )
        }
    }

    internal fun getJBLOtaAccessPoint() {
        val device = _baseDevice.value as? OneDevice ?: return
        device.getOtaAccessPoint()
    }

    internal suspend fun getJBLCheckFwVersion(): CheckFWResp? {
        val device = _baseDevice.value as? OneDevice ?: return null
        val otaAccessPoint = getOtaAccessPointRsp.value ?: return null

        val rsp = withContext(DISPATCHER_API) {
            otaAccessPoint.checkFwVersion(tag = TAG, oneDevice = device)
        }

        if (null != rsp) {
            _receivedPayloads.postValue(GsonUtil.parseBeanToJson(rsp))
        } else {
            _receivedPayloads.postValue("Fail to check Fw version.")
        }

        return rsp
    }

    fun getPartyBoxAdvancedEQ() {
        val device = _baseDevice.value as? PartyBoxDevice ?: return

        device.reqAdvancedEQ(protocol = device.getBLEProtocol())
    }

    fun setPartyBoxAdvancedEQ(context: Context?) {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        val eqSettings = device.toEQSettings(context = context) ?: return

        if (isPartyBoxCategory(device.pid)) {
            device.setAdvancedEQBE(protocol = device.getBLEProtocol(), eqSettings = eqSettings)
        } else {
            device.setAdvancedEQLE(protocol = device.getBLEProtocol(), eqSettings = eqSettings)
        }
    }

    fun unGroupPartyBox() {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        device.unGroup(protocol = device.getBLEProtocol(), secondaryDevice = null)
    }

    fun getPartyBoxDeviceFeatureInfo() {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        device.reqDeviceFeatureInfo(protocol = device.getBLEProtocol())
    }

    fun setPartyBoxAuth() {
        val device = _baseDevice.value as? PartyBoxDevice ?: return
        device.setAuth(protocol = device.getBLEProtocol(), action = EnumAuthAction.PLAY, durationSeconds = 30)
    }

    companion object {
        private const val TAG = "DashboardDialogViewModel"
    }
}