package com.harman

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.RadioGroup
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogDebugDashboardBinding
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumSoundscapeV2Action
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SmartBtnConfigSoundscapeV2
import com.harman.connect.isGen2
import com.harman.moment.EnumMoment
import com.harman.moment.MomentActivity
import com.harman.music.service.MusicServiceListDialog
import com.harman.ota.one.OneOtaActivity
import com.harman.partybox.EnumTwsMode
import com.harman.partybox.PartyBoxTwsActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harmanbar.ble.utils.HexUtil
import com.linkplay.amazonmusic_library.utils.ToastUtils
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/2/27.
 */
class DebugDashboardDialog : AppCompatDialogFragment() {

    private lateinit var viewModel : DashboardDialogViewModel

    private var device: Device? = null

    private val customSetDeviceInfoDialog by lazy {
        CustomSetDeviceInfoDialog()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel = ViewModelProvider(requireActivity())[DashboardDialogViewModel::class.java]
        device?.let {
            viewModel.updateDevice(it)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.decoOnCreateView()

        val binding = DialogDebugDashboardBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this@DebugDashboardDialog
        binding.dialog = this@DebugDashboardDialog
        binding.viewModel = viewModel

        return binding.root
    }

    override fun onStart() {
        super.onStart()

        dialog?.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT));
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.onDestroy()
    }

    private fun Dialog.decoOnCreateView() {
        window?.requestFeature(Window.FEATURE_NO_TITLE);
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.setGravity(Gravity.CENTER)
        window?.attributes?.windowAnimations = R.style.BottomDialogAnimation
        window?.attributes?.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND
        window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window?.attributes?.dimAmount = 0.4f
        setCanceledOnTouchOutside(true)
        setCancelable(true)
        setOnCancelListener {
            dismissAllowingStateLoss()
        }
    }

    fun updateDevice(device: Device) {
        <EMAIL> = device
    }

    fun onClickClearRecentPayloads() {
        viewModel.clearRecentPayloads()
    }

    fun onClickBLEConnect() {
        val context = <EMAIL> ?: return
        val bleDevice = viewModel.baseDevice.value?.bleDevice
        if (null == bleDevice || bleDevice.bleAddress.isNullOrBlank()) {
            ToastUtils.showToast(context, "Didn't have BLE address")
            return
        }

        viewModel.connectBLE(context = context)
    }

    fun onClickBLEDisconnect() {
        viewModel.disconnectBLE()
    }

    fun onClickSPPConnect() {
        val context = <EMAIL> ?: return
        val bleDevice = viewModel.baseDevice.value?.bleDevice
        if (null == bleDevice || bleDevice.macAddress.isNullOrBlank()) {
            ToastUtils.showToast(context, "Didn't have MAC address")
            return
        }

        viewModel.connectSPP(context = context)
    }

    fun onClickSPPDisconnect() {
        viewModel.disconnectSPP()
    }

    fun onClickPartyBoxGetDevInfo() {
        viewModel.getPartyBoxDevInfo()
    }

    fun onClickPartyBoxGetDfuInfo() {
        viewModel.getPartyBoxDfuInfo()
    }

    fun onClickPartyBoxGetAdvancedEQ() {
        viewModel.getPartyBoxAdvancedEQ()
    }

    fun onClickPartyBoxSetAdvancedEQ() {
        viewModel.setPartyBoxAdvancedEQ(context)
    }

    fun onClickPartyBoxUnGroup() {
        viewModel.unGroupPartyBox()
    }

    fun onClickPartyBoxGetDeviceFeature() {
        viewModel.getPartyBoxDeviceFeatureInfo()
    }

    fun onClickPartyBoxSetAuth() {
        viewModel.setPartyBoxAuth()
    }

    fun onClickPreparePartyBoxOta() {
        val ctx = context ?: return
        viewModel.fetchPartyBoxRemoteOtaConfigAndDownload(context = ctx)
    }

    fun onClickStartOrResumePartyBoxOta() {
        viewModel.startOrResumePartyBoxDfu()
    }

    fun onClickCancelPartyBoxOta() {
        viewModel.cancelPartyBoxDfu()
    }

    fun onClickPartyBoxPortalOta() {
        val activity = (activity as? AppCompatActivity) ?: return
        val device = (device as? PartyBoxDevice) ?: return

        viewModel.launchOta(activity = activity, device = device)
    }

    fun portalGen2Tws() {
        val activity = (activity as? AppCompatActivity) ?: return
        val device = (device as? PartyBoxDevice) ?: return

        if (!device.isGen2()) {
            return
        }

        PartyBoxTwsActivity.launchTwsPage(
            activity = activity,
            device = device,
            mode = EnumTwsMode.GEN2
        )
    }

    fun onClickStartPartyLightOta() {
        val ctx = context ?: return
        viewModel.executePartyLightOtaFlow(context = ctx)
    }

    fun onClickCancelPartyLightOta() {
        viewModel.cancelPartyLightDfu()
    }

    fun onClickPartyLightPortalOta() {
        val activity = (activity as? AppCompatActivity) ?: return
        val device = (device as? PartyLightDevice) ?: return

        viewModel.launchOta(activity = activity, device = device)
    }

    fun onClickPortableGetDevInfo() {
        viewModel.getPortableDevInfo()
    }

    fun onClickJBLOneGetDevInfo() {
        viewModel.getJBLOneDevInfo()
    }

    fun onClickJBLOneGetAPList() {
        viewModel.getJBLOneAPList()
    }

    fun onClickJBLOneSetCastGroup() {
        viewModel.setJBLOneCastGroup()
    }

    fun onClickJBLOneGetVolume() {
        viewModel.getJBLOneVolume()
    }

    fun onClickJBLOneGetFeatSupport() {
        viewModel.getJBLOneFeatSupport()
    }

    fun onClickJBLOneEQList() {
        viewModel.getJBLOneEQList()
    }

    fun onClickJBLOneGetOtaAccessPoint() {
        viewModel.getJBLOtaAccessPoint()
    }

    fun onClickJBLOneCheckFwVersion() {
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val checkFwRsp = viewModel.getJBLCheckFwVersion() ?: return@launch
            val activity = <EMAIL> ?: return@launch
            val device = viewModel.baseDevice.value as? OneDevice ?: return@launch

            OneOtaActivity.launchOtaPage(
                activity = activity,
                launcher = null,
                device = device,
                checkFwRsp = checkFwRsp
            )
        }
    }

    fun onClickPlayMusic() {
        viewModel.playMusic()
    }

    fun onClickPauseMusic() {
        viewModel.pauseMusic()
    }

    fun onClickPrevMusic() {
        viewModel.prevMusic()
    }

    fun onClickNextMusic() {
        viewModel.nextMusic()
    }

    fun onClickVolumeUp() {
        viewModel.volumeUp()
    }

    fun onClickVolumeDown() {
        viewModel.volumeDown()
    }

    fun onClickPartyBoxSetDevInfo() {
        customSetDeviceInfoDialog.customSetDeviceInfoListener = object :
            ICustomSetDeviceInfoObserver {
            override fun onContent(contentHex: String?) {
                if (contentHex.isNullOrBlank()) {
                    return
                }

                viewModel.setPartyBoxDevInfo(HexUtil.hexStr2Bytes(contentHex.uppercase()))
            }
        }
        customSetDeviceInfoDialog.show(
            childFragmentManager.beginTransaction().remove(customSetDeviceInfoDialog), TAG
        )
    }

    fun onClickPortableSetDevInfo() {
        customSetDeviceInfoDialog.customSetDeviceInfoListener = object :
            ICustomSetDeviceInfoObserver {
            override fun onContent(contentHex: String?) {
                if (contentHex.isNullOrBlank()) {
                    return
                }

                viewModel.setPortableDevInfo(HexUtil.hexStr2Bytes(contentHex.uppercase()))
            }
        }
        customSetDeviceInfoDialog.show(
            childFragmentManager.beginTransaction().remove(customSetDeviceInfoDialog), TAG
        )
    }

    fun onClickJBLOneSetDevInfo() {
        customSetDeviceInfoDialog.customSetDeviceInfoListener = object :
            ICustomSetDeviceInfoObserver {
            override fun onContent(contentHex: String?) {
                if (contentHex.isNullOrBlank()) {
                    return
                }

                viewModel.setJBLOneDevName(contentHex)
            }
        }
        customSetDeviceInfoDialog.show(
            childFragmentManager.beginTransaction().remove(customSetDeviceInfoDialog), TAG
        )
    }

    fun onSessionPriChanged(group: RadioGroup, @IdRes checkedId: Int) {
        when (checkedId) {
            R.id.rb_wifi -> {
                viewModel.sessionPriority = EnumSessionPriority.WIFI
            }
            R.id.rb_ble -> {
                viewModel.sessionPriority = EnumSessionPriority.BLE
            }
            R.id.rb_auto -> {
                viewModel.sessionPriority = EnumSessionPriority.AUTO
            }
        }
    }

    fun onHybridDashboardClick() {
        val uuid = device?.UUID ?: return
        val activity = this.activity ?: return
        val pid = device?.pid ?: return

        DeviceControlHybridActivity.portalAsOnline(
            activity = activity,
            pid = pid,
            uuid = uuid
        )
    }

    fun onClickJBLOneGetSmartBtnConfig() {
        val oneDevice = device as? OneDevice ?: return
        oneDevice.getSmartBtnConfig()
    }

    fun onClickJBLOneSetSmartBtnConfig() {
        val oneDevice = device as? OneDevice ?: return
        val config = oneDevice.smartBtnConfigExt?.config ?: return

        oneDevice.setSmartBtnConfig(config = config)
    }

    fun onClickJBLOnePortalMoment() {
        val oneDevice = device as? OneDevice ?: return
        MomentActivity.portal(context = activity, device = oneDevice)
    }

    fun onClickJBLOneGetSoundscapeV2Config() {
        val oneDevice = device as? OneDevice ?: return
        oneDevice.getSoundscapeV2Config()
    }

    fun onClickJBLOneSetSoundscapeV2Config() {
        val oneDevice = device as? OneDevice ?: return
        val element = oneDevice.soundscapeV2ListExt?.soundscapeV2List?.getOrNull(0) ?: return

        val req = SetSoundscapeV2ConfigRequest(
            soundscapeId = element.soundscapeId ?: return,
            elementId = element.elementList?.getOrNull(0)?.id ?: return,
            elementValue = element.elementList?.getOrNull(0)?.value ?: return
        )

        oneDevice.setSoundscapeV2Config(req = req)
    }

    fun onClickJBLOneGetSoundscapeV2State() {
        val oneDevice = device as? OneDevice ?: return
        oneDevice.getSoundscapeV2State()
    }

    fun onClickJBLOneControlSoundscapeV2() {
        val oneDevice = device as? OneDevice ?: return
        oneDevice.controlSoundscapeV2(req = ControlSoundscapeV2(
            actionId = EnumSoundscapeV2Action.reverse(oneDevice.controlSoundscapeV2Ext?.actionId)
                ?: EnumSoundscapeV2Action.DEMO_TRACK_PLAY.value,
            soundscapeId = EnumMoment.MOMENT_FOREST.id,
            autoResume = true,
            fadeOut = "true",
            keepAlive = null
        ))
    }

    fun onClickJBLOneSoundscapeV2EnableMomentMixMusic() {
        val oneDevice = device as? OneDevice ?: return
        val config = oneDevice.smartBtnConfigExt?.config ?: return

        config.soundscapeV2 = SmartBtnConfigSoundscapeV2().apply {
            enableMixWithMusic()
        }

        oneDevice.setSmartBtnConfig(config = config)
    }

    fun onClickChromeCastC4A() {
        val oneDevice = device as? OneDevice ?: return

        if (oneDevice.isChromeCastEnabled) {
            oneDevice.requestGoogleLogout()
        } else {
            oneDevice.setC4aPermissionStatus(status = EnumC4aPermissionStatus.ON)
        }
    }

    companion object {
        private const val TAG = "DebugDashboardDialog"
    }
}