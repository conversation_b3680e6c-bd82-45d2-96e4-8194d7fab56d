package com.harman

class VaResultItem {
    var vaName: String? = null
    var isNewSetup: Boolean = false
    var isSuccess: Boolean = false

    val value: Int
        get() {
            if (!isSuccess) {
                return VAResulType.SUCCESS_NONE.value
            }
            return if (EventUtils.Dimension.EnumVaSetupOption.ALEXA.value.equals(vaName,true)) {
                VAResulType.SUCCESS_ALEXA.value
            } else {
                VAResulType.SUCCESS_GVA.value
            }
        }

    override fun toString(): String {
        return "VaResultItem{" +
            "vaName='" + vaName + '\'' +
            ", isNewSetup=" + isNewSetup +
            ", isSuccess=" + isSuccess +
            '}'
    }
}
