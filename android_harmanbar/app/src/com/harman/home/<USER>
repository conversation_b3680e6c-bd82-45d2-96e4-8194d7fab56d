package com.harman.home

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Window
import android.widget.ImageView
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.BarUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityHomePagesBinding
import com.harman.music.mini.MiniPlayersViewModel
import com.harman.music.mini.MiniPlayersViewModelFactory
import com.harman.product.list.MyProductsFragment
import com.harman.product.setting.AppSettingsFragment
import com.harman.discover.bean.Device
import com.harman.home.HomePagesActivity.Companion.TAB_HOME
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.harman.webview.CommonHybridActivity
import com.harman.webview.CommonHybridActivity.Companion
import com.harman.webview.CommonWebView
import com.harman.widget.AppCompatBaseActivity
import com.wifiaudio.action.lan.LocaleLanConfigUtil.changeAppLan
import com.wifiaudio.app.WAApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class HomePagesActivity : AppCompatBaseActivity() {

    private val tabs = ArrayList<HomeTabInfo>()
    private lateinit var pager: ViewPager2
    private lateinit var tabHome: ImageView
    private lateinit var tabSettings: ImageView

    /**
     * @param [TAB_HOME] or [TAB_SETTINGS]
     */
    private val _tab = MutableLiveData<Int>(TAB_HOME)
    val tab: LiveData<Int>
        get() = _tab.distinctUntilChanged()

    private var miniPlayersViewModel: MiniPlayersViewModel? = null

    private val viewModel by lazy {
        ViewModelProvider(
            owner = this@HomePagesActivity,
            HomePagesViewModelFactory(activity = this@HomePagesActivity)
        )[HomePagesViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)
//        requestWindowFeature(Window.FEATURE_NO_TITLE)

//        BarUtils.setStatusBarColor(this, ContextCompat.getColor(this, R.color.transparent), true)
        Logger.d(TAG, "onCreate() >>> ")

        val binding = genBinding()
        setContentView(binding.root)
        pager = binding.pager
        tabHome = binding.tabHome
        tabSettings = binding.tabSettings

        val tabId = savedInstanceState?.getInt(KEY_TAB_ID, TAB_HOME) ?: TAB_HOME

        val languageChanged = intent?.hasExtra(REFRESH_LANGUAGE) ?: false
        initTabsAndIndicator()

        if (languageChanged) {
            navigate(TAB_SETTINGS, false)
        } else {
            CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                delay(100) // add some delay waiting fragment finish layout calculating
                navigate(tabId)
            }
        }

        // MiniPlayersViewModel based on MyProductsViewModel.devices to
        val miniPlayersViewModel = ViewModelProvider(
            owner = this@HomePagesActivity,
            factory = MiniPlayersViewModelFactory()
        )[MiniPlayersViewModel::class.java]

        <EMAIL> = miniPlayersViewModel
    }

    @SuppressLint("MissingSuperCall")
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        tab.value?.also { outState.putInt(KEY_TAB_ID, it) }
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Logger.d(TAG, "onNewIntent")

        if (intent != null && intent.hasExtra(AFTER_OOBE_FROM_ADD_PRODUCT)) {
            navigate(TAB_HOME) // navigate to dashboard / product list
        } else if (intent != null && intent.hasExtra(REFRESH_LANGUAGE)) {
            recreate()
        }
    }

    fun onSelectTabHome() {
        navigate(TAB_HOME)
    }

    fun onSelectTabSettings() {
        navigate(TAB_SETTINGS)
    }

    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) super.attachBaseContext(newBase) else {
            val baseContext = changeAppLan(newBase)
            WAApplication.me.mAppLanguageContext = baseContext
            super.attachBaseContext(baseContext)
        }
    }

    fun navigate(tabID: Int, smoothScroll: Boolean? = true) {
        if (tabID == TAB_HOME) {
            _tab.value = TAB_HOME
            tabHome.setImageResource(R.drawable.tab_home_selected)
            tabSettings.setImageResource(R.drawable.tab_settings_unselected)
            pager.currentItem = TAB_HOME
        } else if (tabID == TAB_SETTINGS) {
            _tab.value = TAB_SETTINGS
            tabHome.setImageResource(R.drawable.tab_home_unselected)
            tabSettings.setImageResource(R.drawable.tab_settings_selected)
            pager.setCurrentItem(TAB_SETTINGS, smoothScroll == true)
        }
    }

    private fun initTabsAndIndicator() {
        Logger.d(TAG, "initTabsAndIndicator() >>> ")
        val myProducts = MyProductsFragment()
        val fragmentSettings = AppSettingsFragment()

        tabs.clear()
        tabs.add(HomeTabInfo(id = TAB_HOME, fragment = myProducts))
        tabs.add(HomeTabInfo(id = TAB_SETTINGS, fragment = fragmentSettings))

        pager.adapter = HomeTabAdapter(fa = this, tabs = tabs)
        pager.isUserInputEnabled = false
    }

    private fun genBinding() = ActivityHomePagesBinding.inflate(layoutInflater).also { binding ->
        binding.viewModel = viewModel
        binding.activity = this@HomePagesActivity
        binding.lifecycleOwner = this@HomePagesActivity
    }

    override val tag: String = TAG

    override val baseControlDevice: Device?
        get() = miniPlayersViewModel?.miniPlayerItems?.value?.getOrNull(0)?.device

    companion object {
        private const val TAG = "HomePagesActivity"

        const val TAB_HOME = 0
        const val TAB_SETTINGS = 1
        const val KEY_TAB_ID = "tabID"
        const val AFTER_OOBE_FROM_ADD_PRODUCT = "AFTER_OOBE_FROM_ADD_PRODUCT"
        const val REFRESH_LANGUAGE = "REFRESH_LANGUAGE"

        fun resumeDashboard(context: Context) {
            val intent = Intent(context, HomePagesActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)
            intent.putExtra(AFTER_OOBE_FROM_ADD_PRODUCT, true)
            context.startActivity(intent)
        }

        fun resumeDashboardAndClearTask(context: Context) {
            Logger.d(TAG, "resumeDashboard () >>> ")
            val intent = Intent(context, HomePagesActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            context.startActivity(intent)
        }

        fun resumeDashboardRefreshLanguage(activity: Activity) {
            Logger.d(TAG, "resumeDashboardRefreshLanguage () >>> ")
            val intent = Intent(activity, HomePagesActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.putExtra(REFRESH_LANGUAGE, true)
            activity.startActivity(intent)
        }

        fun resumeDashboardWithoutRecreate(activity: Activity) {
            Logger.d(TAG, "resumeDashboardWithoutRecreate () >>> $activity")
            val intent = Intent(activity, HomePagesActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            activity.startActivity(intent)
            activity.finish()
        }
    }
}