package com.harman.support

import android.text.TextUtils
import com.google.gson.Gson
import com.harman.bean.DeviceNotification
import com.harman.hkone.HarmanDeviceManager
import com.harman.hkone.deviceoffline.OfflineDeviceHandler
import com.harman.hkone.model.BatteryInfo
import com.harman.hkone.model.DestroyCastGroupNotify
import com.harman.hkone.model.GroupCalibrationNotify
import com.harman.hkone.model.HttpGroupInfo
import com.harman.hkone.model.SetAnchorNotify
import com.harman.hkone.model.SetAuthStartNotify
import com.harman.hkone.model.SetCastGroupNotify
import com.harmanbar.ble.statistic.StatisticConstant
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.ProductFeature
import com.linkplay.ota.model.OTAStatus
import com.linkplay.ota.model.UPNPSubscribeOTAStatus
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.model.rightfrag_obervable.MessageDataItem
import com.wifiaudio.service.WAUpnpDeviceManager
import com.wifiaudio.utils.GsonParseUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLBatteryItem
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLDataUtil
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLEQItem
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLFeatureSupport
import com.wifiaudio.view.pagesdevcenter.devicesetting.JBLRearSpeakerVolume
import com.wifiaudio.view.pagesdevcenter.devicesetting.calibration.HarmanBarCalibrationState
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.GeneralConfig
import org.greenrobot.eventbus.EventBus
import org.json.JSONException
import org.json.JSONObject

object LinkPlaySDKHelperImpl : ILinkPlaySDKHelperEvents {







    override fun receivedUPNPNotify(body: String, uuid: String) {
        //upnp 订阅通知消息
        LogsUtil.i("receivedUPNPNotify http body = " + body + " uuid = " + uuid)
        try {
            val jsonObject = JSONObject(body)
            if (jsonObject.has("surround_level")) {
                val msg = MessageDataItem()
                msg.strDevUUID = uuid
                val item = JBLRearSpeakerVolume()

                val sourceObj = jsonObject.getJSONObject("surround_level")
                if (sourceObj.has("level")) {
                    val level = sourceObj.getInt("level").toString() + ""
                    item.volume = level
                    item.subscribe_type = JBLDataUtil.SUBSCRIPT_REAR_SPEAKER_VOLUME
                }

                msg.obj = item
                EventBus.getDefault().post(msg)
            } else if (jsonObject.has("sod_state")) {

                val msg = MessageDataItem()
                msg.strDevUUID = uuid
                val item = JBLRearSpeakerVolume()

                val sourceObj = jsonObject.getJSONObject("sod_state")
                if (sourceObj.has("state")) {
                    val state = sourceObj.getInt("state")
                    if (state == 0) {
                        item.status = JBLDataUtil.STATUS_DETACHED
                    } else {
                        item.status = JBLDataUtil.STATUS_ATTACHED
                    }
                    item.subscribe_type = JBLDataUtil.SUBSCRIPT_REAR_SPEAKER_STATE

                    msg.obj = item
                    EventBus.getDefault().post(msg)
                }
            } else if (jsonObject.has("ota")) {
                val otaObj = jsonObject.getJSONObject("ota")
                if (otaObj.has("event_type")) {
                    val event_type = otaObj.getString("event_type")
                    if (TextUtils.equals(event_type, "downloading")) {
                        //更新进度,可暂时不处理只接收http的
                    } else if (TextUtils.equals(event_type, "reboot")) {
                        //更新状态
                        val subscribeOTAStatus = UPNPSubscribeOTAStatus()
                        val status = OTAStatus()
                        status.status = OTAStatus.MV_UP_STATUS_COMPLETE
                        subscribeOTAStatus.otaStatus = status
                        subscribeOTAStatus.UUID = uuid
                        EventBus.getDefault().post(subscribeOTAStatus)
                    }
                }
            } else if (jsonObject.has("pass_through")) {
                val pass_through = jsonObject.getJSONObject("pass_through")
                if (pass_through.has("pass_string")) {
                    val pass_string = pass_through.getJSONObject("pass_string")
                    if (pass_string.has("command")) {
                        var command = pass_string.getString("command")
                        var payload = pass_string.optJSONObject(StatisticConstant.PLAYLOAD)
                        if (payload == null || TextUtils.isEmpty(payload.toString())) {
                            payload = pass_string.optJSONObject(StatisticConstant.PAYLOAD)
                        }

                        if ("destroyCastGroup".equals(command, true)) {
                            val msg = DestroyCastGroupNotify()
                            msg.UUID = uuid
                            DestroyCastGroupNotify.parseFromJson(msg, payload)
                            val find = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
                            var group = OfflineDeviceHandler.get().getGroup(HarmanDeviceManager.getInstance().getCrc(find))
                            group?.gcUuids?.forEach { tempUUID ->
                                var item = WAUpnpDeviceManager.me().getDeviceItemByuuid(tempUUID)
                                LogsUtil.i("printDeviceInfo========== receivedUPNPNotify online = " + HarmanDeviceManager.getInstance().getCrc(item))
                                item.bOfflineDevice = false
//                                WAUpnpDeviceManager.me().addDeviceItemByUuid(tempUUID, WAUpnpDeviceManager.me().allDevices.stream().filter { item -> item?.uuid?.equals(tempUUID, true) == true }.findFirst().get())
                            }
                            OfflineDeviceHandler.get().deleteGroup(HarmanDeviceManager.getInstance().getCrc(find), false)
                            EventBus.getDefault().post(msg)
                            LogsUtil.i("receivedUPNPNotify DestroyCastGroupNotify uuid = " + uuid)
                        } else if ("setCastGroup".equals(command, true)) {
                            val msg = SetCastGroupNotify()
                            msg.UUID = uuid
                            msg.httpGroupInfo = GsonParseUtil.instance.fromJson(
                                payload.toString(),
                                HttpGroupInfo::class.java
                            )
                            val find = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
                            msg?.httpGroupInfo?.gcUuids?.forEach { tempUUID ->
                                var item = WAUpnpDeviceManager.me().getDeviceItemByuuid(tempUUID)
                                LogsUtil.i("printDeviceInfo========== receivedUPNPNotify offline = " + HarmanDeviceManager.getInstance().getCrc(item))
                                item.bOfflineDevice = true
                            }
                            EventBus.getDefault().post(msg)
                            LogsUtil.i("receivedUPNPNotify SetCastGroupNotify uuid = " + uuid)

                        } else if ("groupCalibration".equals(command, true)) {
//                            val msg = GroupCalibrationNotify()
//                            msg.UUID = uuid
//                            GroupCalibrationNotify.parseFromJson(msg, payload)
//                            EventBus.getDefault().post(msg)
//                            LogsUtil.i("receivedUPNPNotify GroupCalibrationNotify uuid = " + uuid)
                        } else if ("setAnchorResult".equals(command, true)) {
                            val msg = SetAnchorNotify()
                            msg.UUID = uuid
                            SetAnchorNotify.parseFromJson(msg, payload)
                            EventBus.getDefault().post(msg)
                            LogsUtil.i("receivedUPNPNotify setAnchorResult uuid = " + uuid)
                        } else if ("setAuthResult".equals(command, true)) {
                            val msg = SetAuthStartNotify()
                            msg.UUID = uuid
                            SetAuthStartNotify.parseFromJson(msg, payload)
                            EventBus.getDefault().post(msg)
                            LogsUtil.i("receivedUPNPNotify setAuthResult uuid = " + uuid)
                        } else if ("getBatteryStatus".equals(command, true)) {
                            var batterItem = GsonParseUtil.instance.fromJson(payload.toString(), JBLBatteryItem::class.java)
                            val msg = BatteryInfo()
                            val find = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
                            msg.crc = HarmanDeviceManager.getInstance().getCrc(find)
                            msg.itemList = mutableListOf(batterItem)
                            LogsUtil.i("receivedUPNPNotify getBatteryStatus ${msg.toString()} ")
                            find?.project?.also {
                                if (AppConfigurationUtils.getModelConfigByModelName(it)?.capability?.contains(
                                        ProductFeature.Battery) == true) {
                                    LogsUtil.i("receivedUPNPNotify getBatteryStatus supportBattery${msg.toString()} ")
                                    var httpGroupInfo = OfflineDeviceHandler.get().getGroup(msg.crc)
                                    if (httpGroupInfo == null || httpGroupInfo?.isSingle == true || httpGroupInfo?.isBeforeGroup == true) {
                                        EventBus.getDefault().post(msg)
                                    }
                                }
                            }

                        } else if ("notifyGroupParameter".equals(command, true)) {
                            val msg = BatteryInfo()
                            val find = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
                            msg.crc = HarmanDeviceManager.getInstance().getCrc(find)
                            var list = arrayListOf<JBLBatteryItem>()
                            val battery = payload.getJSONArray("battery")
                            for (i in 0 until battery.length()) {
                                var batterItem = GsonParseUtil.instance.fromJson(battery.getJSONObject(i).toString(), JBLBatteryItem::class.java)
                                if (HarmanDeviceManager.getInstance().isLeftChannel(msg.crc, batterItem!!.id)) {
                                    list.add(0, batterItem!!)
                                } else {
                                    list.add(batterItem!!)
                                }
                            }


                            msg.itemList = list
                            LogsUtil.i("receivedUPNPNotify getBatteryStatus ${msg.toString()} ")
//                            var config = AssetsJsonParser.parseProductListConfig()
//                            if(config.supportBattery(find?.project)){
                            LogsUtil.i("receivedUPNPNotify getBatteryStatus supportBattery ${msg.toString()} ")
                            EventBus.getDefault().post(msg)
//                            }
                        } else if ("notifyGernalConfigChanged".equals(command, true)) {
                            var generalConfig = GsonParseUtil.instance.fromJson(payload.toString(), GeneralConfig::class.java)
                            var notification = DeviceNotification(uuid, generalConfig)
                            LogsUtil.i("receivedUPNPNotify notifyGernalConfigChanged ${notification.toString()} ")
                            EventBus.getDefault().post(notification)
                        } else if ("notifyUserEQChanged".equals(command, true)) {
                            var jbleqItem = GsonParseUtil.instance.fromJson(payload.toString(), JBLEQItem::class.java)
                            var notification = DeviceNotification(uuid, jbleqItem)
                            LogsUtil.i("receivedUPNPNotify notifyUserEQChanged  ${notification.toString()} ")
                            EventBus.getDefault().post(notification)
                        } else if ("notifyGroupCalibrationState".equals(command, true)) {
                            val msg = GroupCalibrationNotify()
                            msg.UUID = uuid
                            GroupCalibrationNotify.parseFromJson(msg, payload.getJSONObject("calibration"))
                            EventBus.getDefault().post(msg)
                            LogsUtil.i("receivedUPNPNotify GroupCalibrationNotify jsonObject = " + jsonObject)
                        }

                    }
                }
            } else if (jsonObject.has("calibration")) {
                val harmanBarCalibrationState =  Gson().fromJson(jsonObject.toString(), HarmanBarCalibrationState::class.java)
                harmanBarCalibrationState.uuid = uuid
                EventBus.getDefault().post(harmanBarCalibrationState)
                LogsUtil.i("receivedUPNPNotify jsonObject = " + harmanBarCalibrationState)
            }

        } catch (var7: JSONException) {
            var7.printStackTrace()
        }
    }

    override fun updateSleepTimerEvent(remainSecs: Int, uuid: String?) {
        var timerItem = com.wifiaudio.view.pagesdevcenter.devicesetting.JBLTimer()
        timerItem.sleep_timer = remainSecs
        var msg = MessageDataItem()
        msg.strDevUUID = uuid
        msg.obj = timerItem
        EventBus.getDefault().post(msg)
    }

    override fun updateRemoteControlInfo(content: String, uuid: String) {

        var deviceItem = WAUpnpDeviceManager.me().getDeviceItemByuuid(uuid)
        if (deviceItem == null) {
            return
        }

        val item = GsonParseUtil.instance.fromJson(
            content,
            JBLFeatureSupport::class.java
        )
        if (item != null) {
            deviceItem.bSupportRemoteController =
                TextUtils.equals(item.featureSupport!!.remoteController!!.support, "true")
        } else {
            deviceItem.bSupportRemoteController = false
        }
    }
}