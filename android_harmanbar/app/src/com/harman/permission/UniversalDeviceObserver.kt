package com.harman.permission

import android.content.Context
import androidx.annotation.WorkerThread
import com.harman.db.DB
import com.harman.db.DeviceCache
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.oobe.wifi.LocalCacheAdapter.mapDeviceItem
import com.harman.oobe.wifi.LocalCacheAdapter.mapPartyBoxBTDevice
import com.harman.oobe.wifi.LocalCacheAdapter.markAuthed
import com.harman.oobe.wifi.LocalCacheAdapter.updateDevice
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.oobe.wifi.LocalCacheAdapter.mapPartyBandBTDevice
import com.harman.oobe.wifi.LocalCacheAdapter.updateCache
import com.harman.thread.DISPATCHER_IO
import com.wifiaudio.action.DeviceSettingAction
import com.wifiaudio.action.DeviceSettingAction.IDevicePropertyRequest
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceProperty
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentSkipListSet

/**
 * Created by gerrardzhang on 2024/7/5.
 *
 * Do sth that related with device scanner but not bind with any UI pages.
 */
object UniversalDeviceObserver : IHmDeviceObserver {

    private val handledAuthDevices = ConcurrentSkipListSet<String>()

    override fun onDeviceOnlineOrUpdate(device: Device) {
        GlobalScope.launch(DISPATCHER_IO) {
            val diagnosedDevices = loadDiagnosedDeviceList(ctx = WAApplication.me)
            diagnosedDevices?.find { it.uuid == device.UUID }?.also {
                device.customerServiceReportId = it.diagnosisReportId
            }

            when (device) {
                is OneDevice -> onOneDeviceOnlineOrUpdate(device)
                is PartyBoxDevice -> onPartyBoxDeviceOnlineOrUpdate(device)
                is PartyBandDevice -> onPartyBandDeviceOnlineOrUpdate(device)
            }
        }
    }


    @WorkerThread
    private suspend fun onOneDeviceOnlineOrUpdate(device: OneDevice) {
        val uuid = device.UUID ?: return
        device.offlineDummy = LocalCacheAdapter.sMemoryDevicesCache[uuid]?.mapDeviceItem()

        if (!handleAuth(device)) {
            LocalCacheAdapter.upsert(device = device, ctx = WAApplication.me)
        }

        updateDeviceItemStatus(device = device)
    }

    private fun updateDeviceItemStatus(device: OneDevice) {
        val deviceItem = device.wifiDevice?.deviceItem ?: return
        DeviceSettingAction.getStatusEx(deviceItem, object : IDevicePropertyRequest {
            override fun onSuccess(content: String?, deviceProperty: DeviceProperty?) {
                // no impl.
                deviceItem.ssidName = deviceProperty?.DeviceName
                deviceItem.Name = deviceProperty?.DeviceName
            }

            override fun onFailed(e: Throwable?) {
                // no impl.
            }
        })
    }

    @WorkerThread
    private suspend fun onPartyBandDeviceOnlineOrUpdate(device: PartyBandDevice) {
        val uuid = device.UUID ?: return
        val cache = LocalCacheAdapter.sMemoryDevicesCache[uuid]?.mapPartyBandBTDevice()
        device.offlineDummy = cache
        if (device.isA2DPConnected) {
            if (!handledAuthDevices.contains(device.UUID)) {
                handledAuthDevices.add(device.UUID)
                device.updateCache(hadAuth = true)
            }
        }
        if (device.firmwareVersion != cache?.firmwareVersion && handledAuthDevices.contains(device.UUID)) {
            device.updateCache(firmwareVer = device.firmwareVersion)
        }
        if (device.deviceName != cache?.deviceName && handledAuthDevices.contains(device.UUID)) {
            device.updateCache(deviceName = device.deviceName)
        }
    }

    @WorkerThread
    private suspend fun onPartyBoxDeviceOnlineOrUpdate(device: PartyBoxDevice) {
        val uuid = device.UUID
        if (uuid.isNullOrBlank()) {
            return
        }

        device.offlineDummy = LocalCacheAdapter.sMemoryDevicesCache[uuid]?.mapPartyBoxBTDevice()

        if (device.isA2DPConnected) {
            if (!handledAuthDevices.contains(uuid)) {
                handledAuthDevices.add(uuid)
                device.markAuthed(ctx = WAApplication.me)
                device.updateDevice(ctx = WAApplication.me)
            }
        }
    }

    /**
     * @return true if mark authed.
     */
    @WorkerThread
    private suspend fun handleAuth(device: OneDevice): Boolean {
        if (device.isWiFiOnline || device.isA2DPConnected) {
            val uuid = device.UUID
            if (uuid.isNullOrBlank()) {
                return false
            }

            if (!handledAuthDevices.contains(uuid) && device.markAuthed(ctx = WAApplication.me)) {
                handledAuthDevices.add(uuid)
                return true
            }
        }

        return false
    }

    suspend fun loadDiagnosedDeviceList(ctx: Context): List<DeviceCache>? {
        return DB.getAllDeviceCache(context = ctx)?.filter { device ->
            device.diagnosisReportId?.isNotBlank() == true
        }
    }

    private const val TAG = "'UniversalDeviceObserver'"
}