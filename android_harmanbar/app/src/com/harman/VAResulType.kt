package com.harman

enum class VAResulType(val resultName: String, val value: Int) {
    SUCCESS_NONE(EventUtils.Dimension.EnumVaSetupResult.SUCCESS_NONE.value, 0),
    SUCCESS_ALEXA(EventUtils.Dimension.EnumVaSetupResult.SUCCESS_ALEXA.value, 1),
    SUCCESS_GVA(EventUtils.Dimension.EnumVaSetupResult.SUCCESS_GVA.value, 2),
    SUCCESS_GVA_ALEXA(EventUtils.Dimension.EnumVaSetupResult.SUCCESS_GVA_ALEXA.value, 3);


    companion object {
        fun getItemByName(name: String): VAResulType? {
            for (item in entries) {
                if (item.resultName == name) {
                    return item
                }
            }
            return null
        }

        fun getItemByValue(value: Int): VAResulType? {
            for (item in entries) {
                if (item.value == value) {
                    return item
                }
            }
            return null
        }
    }
}
