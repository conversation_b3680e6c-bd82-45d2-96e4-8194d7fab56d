package com.harman.widget

import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.harman.bar.app.R
import com.harman.partylight.util.fitSystemBar
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools.roundPercentage
import com.harman.getBLEProtocol
import com.harman.log.Logger

abstract class AppCompatBaseActivity : AppCompatActivity() {

    open val tag: String = this::class.java.simpleName

    open val baseControlDevice: Device? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fitSystemBar()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        val current = baseControlDevice?.volume ?: run {
            return super.onKeyDown(keyCode, event)
        }

        val target = when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP -> {
                val volume = (current + 7).roundPercentage()
                Logger.d(tag, "onKeyDown() >>> KEYCODE_VOLUME_UP. current[$current] target[$volume]")
                volume
            }
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                val volume = (current - 7).roundPercentage()
                Logger.d(tag, "onKeyDown() >>> KEYCODE_VOLUME_DOWN. current[$current] target[$volume]")
                volume
            }
            else -> {
                return super.onKeyDown(keyCode, event)
            }
        }

        when (val dummy = baseControlDevice) {
            is OneDevice -> {
                if (!dummy.isA2DPConnected) {
                    dummy.setRemoteVolume(value = target)
                }
            }
            is PartyBoxDevice -> {
                if (!dummy.isA2DPConnected) {
                    dummy.setRemoteVolume(protocol = dummy.getBLEProtocol(), value = target)
                }
            }
        }

        return super.onKeyDown(keyCode, event)
    }

    val launcher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            Logger.d(tag, "ActivityResultLauncher >>> ")
            onActivityResult(result = result)
        }

    open fun onActivityResult(result: ActivityResult) {
        // override this method to receive onActivityResult message
    }
}
