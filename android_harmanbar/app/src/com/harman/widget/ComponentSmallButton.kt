package com.harman.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.Gravity
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.graphics.scale
import androidx.core.view.updatePadding
import androidx.core.widget.TextViewCompat
import com.blankj.utilcode.util.ImageUtils
import com.harman.bar.app.R
import com.harman.dp

/**
 * @Description global component small button
 * <AUTHOR>
 * @Time 2024/7/24
 */
class ComponentSmallButton : AppCompatTextView {
    private val styles = mapOf(
        BtnType.Highlight to Pair(Style(R.color.fg_inverse, R.color.bg_highlighted), Style(R.color.fg_disabled, R.color.bg_disable)),
        BtnType.Jump to Pair(Style(R.color.fg_primary, R.color.transparent), Style(R.color.fg_disabled, R.color.transparent)),
        BtnType.Flat to Pair(Style(R.color.fg_primary, R.color.bg_opacity_10), Style(R.color.fg_secondary, R.color.bg_opacity_10)),
        BtnType.Line to Pair(Style(R.color.fg_primary, R.color.transparent), Style(R.color.fg_disabled, R.color.transparent)),
        BtnType.Red to Pair(Style(R.color.red_1, R.color.bg_opacity_10), Style(R.color.fg_disabled, R.color.bg_opacity_10)),
    )
    private val drawRect = RectF()
    private val decorationPaint = Paint()
    private lateinit var btnType: BtnType
    private var dispatchPressEnable: Boolean? = true
    constructor(context: Context) : super(context) {
        init(null, 0)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle) {
        init(attrs, defStyle)
    }


    private fun init(attrs: AttributeSet?, defStyle: Int) {
        // Load attributes
        val a = context.obtainStyledAttributes(
            attrs, R.styleable.ComponentSmallButton, defStyle, 0
        )
        btnType = a.getInteger(R.styleable.ComponentSmallButton_csb_btn_type, 0).let { typeIntValue ->
            BtnType.entries.find { it.value == typeIntValue }
        } ?: BtnType.Highlight
        a.recycle()
        build()
    }

    private fun build() {
        setTextAppearance(R.style.Text_Body_Strong)
        gravity = Gravity.CENTER
        minWidth = 72.dp()
        minHeight = 29.dp()
        maxLines = 1
        if (btnType != BtnType.Jump) {
            updatePadding(left = 12.dp(), right = 12.dp())
        }
        buildText()
    }

    @SuppressLint("RestrictedApi", "UseCompatLoadingForDrawables")
    private fun buildText() {
        styles[btnType]!!.also {
            val tColor = resources.getColor(if (isEnabled) it.first.tColorId else it.second.tColorId, context.theme)
            setTextColor(tColor)
            if (btnType == BtnType.Jump) {
                compoundDrawablePadding = 2.dp()
                TextViewCompat.setCompoundDrawableTintList(this, ColorStateList.valueOf(tColor))
                TextViewCompat.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    this,
                    null,
                    null,
                    resources.getDrawable(R.drawable.icon_arrow_outward, context.theme).let { res ->
                        val bmp = ImageUtils.drawable2Bitmap(res)
                        val bmpScaled = bmp.scale(10f.dp(), 10.dp())
                        ImageUtils.bitmap2Drawable(bmpScaled)
                    },
                    null,
                )
            } else {
                TextViewCompat.setCompoundDrawablesRelativeWithIntrinsicBounds(this, 0, 0, 0, 0)
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        val style = if (!isEnabled) {
            styles[btnType]!!.second
        } else {
            styles[btnType]!!.first
        }
        drawRect.set(0f, 0f, width.toFloat(), height.toFloat())
        //background
        decorationPaint.reset()
        decorationPaint.color = resources.getColor(style.bgColorId, context.theme)
        canvas.drawRoundRect(drawRect, height / 2f, height / 2f, decorationPaint)
        if (btnType == BtnType.Line) {
            //stroke if need
            decorationPaint.apply {
                reset()
                this.style = Paint.Style.STROKE
                strokeWidth = 1.dp().toFloat()
                color = resources.getColor(style.tColorId, context.theme)
            }
            (decorationPaint.strokeWidth / 2).also {
                drawRect.inset(it, it)
            }
            canvas.drawRoundRect(drawRect, height / 2f, height / 2f, decorationPaint)
        }
        super.onDraw(canvas)
        //pressed foreground
        if (isPressed && true == dispatchPressEnable) {
            decorationPaint.reset()
            decorationPaint.color = resources.getColor(R.color.fixed_blc_50, context.theme)
            canvas.drawRoundRect(drawRect, height / 2f, height / 2f, decorationPaint)
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        buildText()
    }

    override fun dispatchSetPressed(pressed: Boolean) {
        super.dispatchSetPressed(pressed)
        invalidate()
    }

    fun dispatchPressed(enabled: Boolean) {
        this.dispatchPressEnable = enabled
        invalidate()
    }
    fun btnType(type: BtnType) {
        if (type == btnType) {
            return
        }
        btnType = type
        build()
    }

}

private data class Style(
    val tColorId: Int,
    val bgColorId: Int,
)

enum class BtnType(val value: Int) {
    //<enum name="highlight" value="0" />
    //<enum name="jump" value="1" />
    //<enum name="flat" value="2" />
    //<enum name="line" value="3" />
    //<enum name="red" value="4" />
    Highlight(0),
    Jump(1),
    Flat(2),
    Line(3),
    Red(4),
}