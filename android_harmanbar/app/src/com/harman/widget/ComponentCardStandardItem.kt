package com.harman.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import com.harman.bar.app.R
import com.harman.log.Logger

class ComponentCardStandardItem @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    RelativeLayout(context, attrs) {

    init { init(context, attrs) }

    private var tvTitle: TextView? = null
    private var tvDesc: TextView? = null
    private var ivStartImage: ImageView? = null
    private var ivEndImage: ImageView? = null
    private var ivForwardImage: ImageView? = null
    private var tvExtraAction: TextView? = null

    private fun init(context: Context, attrs: AttributeSet?) {
        val root = LayoutInflater.from(context).inflate(R.layout.component_card_standard_item, this, true)
        tvTitle = root.findViewById(R.id.tv_title)
        tvDesc = root.findViewById(R.id.tv_desc)
        ivStartImage = root.findViewById(R.id.iv_start_icon)
        ivEndImage = root.findViewById(R.id.iv_end_icon)
        ivForwardImage = root.findViewById(R.id.iv_forward_icon)
        tvExtraAction = root.findViewById(R.id.tv_extra_action)

        val attributes = context.obtainStyledAttributes(attrs, R.styleable.ComponentCardStandardItem)

        try {
            val startImageSrc: Int = attributes.getResourceId(R.styleable.ComponentCardStandardItem_startIconImage, -1)
            if (startImageSrc != -1) {
                ivStartImage?.setImageResource(startImageSrc)
            }

            val startImageVisibility: Int = attributes.getInt(R.styleable.ComponentCardStandardItem_startIconVisibility, 2)
            when (startImageVisibility) {
                2 -> {
                    ivStartImage?.visibility = View.GONE
                }
                1 -> {
                    ivStartImage?.visibility = View.INVISIBLE
                }
                0 -> {
                    ivStartImage?.visibility = View.VISIBLE
                }
            }

            val titleText: String? = attributes.getString(R.styleable.ComponentCardStandardItem_titleText)
            tvTitle?.text = titleText

            val descText: String? = attributes.getString(R.styleable.ComponentCardStandardItem_descText)
            tvDesc?.text = descText

            val descVisibility: Int = attributes.getInt(R.styleable.ComponentCardStandardItem_descVisibility, 0)
            when (descVisibility) {
                2 -> {
                    tvDesc?.visibility = View.GONE
                }
                1 -> {
                    tvDesc?.visibility = View.INVISIBLE
                }
                0 -> {
                    tvDesc?.visibility = View.VISIBLE
                }
            }

            val extraActionText: String? = attributes.getString(R.styleable.ComponentCardStandardItem_extraActionText)
            tvExtraAction?.text = extraActionText

            val extraActionVisibility: Int = attributes.getInt(R.styleable.ComponentCardStandardItem_extraActionVisibility, 0)
            when (extraActionVisibility) {
                2 -> {
                    tvExtraAction?.visibility = View.GONE
                }
                1 -> {
                    tvExtraAction?.visibility = View.INVISIBLE
                }
                0 -> {
                    tvExtraAction?.visibility = View.VISIBLE
                }
            }

            val endImageSrc: Int = attributes.getResourceId(R.styleable.ComponentCardStandardItem_endIconImage, -1)
            if (endImageSrc != -1) {
                ivEndImage?.setImageResource(endImageSrc)
            }

            val endIconVisibility: Int = attributes.getInt(R.styleable.ComponentCardStandardItem_endIconImageVisibility, 2)
            when (endIconVisibility) {
                2 -> {
                    ivEndImage?.visibility = View.GONE
                }
                1 -> {
                    ivEndImage?.visibility = View.INVISIBLE
                }
                0 -> {
                    ivEndImage?.visibility = View.VISIBLE
                }
            }

            val forwardImgSrc: Int = attributes.getResourceId(R.styleable.ComponentCardStandardItem_forwardIconImage, -1)
            if (forwardImgSrc != -1) {
                ivForwardImage?.setImageResource(forwardImgSrc)
            }

            val forwardIconVisibility: Int = attributes.getInt(R.styleable.ComponentCardStandardItem_forwardIconImageVisibility, 2)
            when (forwardIconVisibility) {
                2 -> {
                    ivForwardImage?.visibility = View.GONE
                }
                1 -> {
                    ivForwardImage?.visibility = View.INVISIBLE
                }
                0 -> {
                    ivForwardImage?.visibility = View.VISIBLE
                }
            }

        } catch (e: Exception) {
            Logger.e(TAG, "", e)
        } finally {
            attributes.recycle()
        }
    }

    fun setTitle(title: String) {
        tvTitle?.text = title
    }

    fun setDesc(desc: String) {
        tvDesc?.text = desc
    }

    fun setDescVisible(visible: Boolean) {
        if (visible) {
            tvDesc?.visibility = View.VISIBLE
        } else {
            tvDesc?.visibility = View.GONE
        }
    }

    fun showImage(show: Boolean) {
        if (show) {
            ivEndImage?.visibility = VISIBLE
        } else {
            ivEndImage?.visibility = GONE
        }
    }

    fun setImage(@DrawableRes resourceId: Int) {
        ivEndImage?.setImageResource(resourceId)
    }

    fun setEndImgClickListener(listener: OnClickListener) {
        ivEndImage?.setOnClickListener(listener)
    }

    companion object {
        const val TAG = "SettingsOptionsItem"
    }
}

