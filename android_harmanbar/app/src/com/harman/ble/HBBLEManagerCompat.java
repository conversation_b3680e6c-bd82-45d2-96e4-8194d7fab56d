package com.harman.ble;

import android.annotation.SuppressLint;
import android.app.Application;
import android.text.TextUtils;

import com.harman.hkone.HarmanDeviceManager;
import com.harmanbar.ble.entity.HBBluetoothDevice;
import com.harmanbar.ble.inner.BLEConnector;
import com.harmanbar.ble.inner.BLEManager;
import com.harmanbar.ble.inner.BLEScanner;
import com.harmanbar.ble.inner.BleConnectStatus;
import com.harmanbar.ble.listener.BLECharacteristicChangedListener;
import com.harmanbar.ble.listener.BLEScanListener;
import com.harmanbar.ble.listener.HBBLEAPListListener;
import com.harmanbar.ble.listener.HBBLEConfigSoundListener;
import com.harmanbar.ble.listener.HBBLEControlListener;
import com.harmanbar.ble.listener.HBBLEDeviceStatusChangedListener;
import com.harmanbar.ble.listener.HBBLEScanListener;
import com.harmanbar.ble.listener.HBConnectBLEListener;
import com.harmanbar.ble.listener.HBConnectWiFiListener;
import com.harmanbar.ble.listener.StatisticCallback;
import com.harmanbar.ble.manager.HBBLEConfig;
import com.harmanbar.ble.model.BLESetupResultItem;
import com.harmanbar.ble.model.ConnectBLEInfo;
import com.harmanbar.ble.model.HBApInfo;
import com.harmanbar.ble.model.HBApItem;
import com.harmanbar.ble.model.HBBLEDeviceInfo;
import com.harmanbar.ble.statistic.HBBLEConnectApResult;
import com.harmanbar.ble.statistic.StatisticConstant;
import com.harmanbar.ble.utils.BLEProfile;
import com.harmanbar.ble.utils.BLEUtil;
import com.harmanbar.ble.utils.GsonUtil;
import com.harmanbar.ble.utils.HexUtil;
import com.harmanbar.clj.fastble.callback.BleWriteCallback;
import com.harmanbar.clj.fastble.exception.BleException;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.app.ApplicationViewModel;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import config.AppLogTagUtil;

/**
 * harmanbar ble manager
 */
public class HBBLEManagerCompat {

    private static HBBLEManagerCompat instance;
    private String version = "1.0.0";
    private boolean mNeedHexStr;//is need hex str
    private HBBLEConfig bleConfig;//init config
    //    private Handler handler = new Handler(Looper.getMainLooper());
    private BLEConnector mBLEConnector;

    private static final String TAG = HBBLEManagerCompat.class.getSimpleName();

    public String getVersion() {
        return version;
    }

    //keep crc-connector pair multi ble connect
    private Map<String, BLEConnector> bleConnectorMap = new LinkedHashMap<>();

    /**
     * get connected crc list
     *
     * @return
     */
    public List<String> getConnectedDeviceCrcList() {
        if (bleConnectorMap.isEmpty()) return Collections.emptyList();
        List<String> crcList = new ArrayList<>();
        for (Map.Entry<String, BLEConnector> entry : bleConnectorMap.entrySet()) {
            if (entry != null && entry.getValue() != null && entry.getValue().isConnected()) crcList.add(entry.getKey());
        }
        return crcList;
    }

    private HBBLEManagerCompat() {

    }

    public static HBBLEManagerCompat getInstance() {
        if (instance == null) {
            synchronized (HBBLEManagerCompat.class) {
                if (instance == null) {
                    instance = new HBBLEManagerCompat();
                }
            }
        }
        return instance;
    }

    public void init(Application application) {
        bleConfig = new HBBLEConfig();

        BLEManager.getInstance().init(application);
    }

    public void init(Application application, HBBLEConfig config) {

        this.bleConfig = config;

        BLEManager.getInstance().init(application);
    }

    private boolean isConnectorMatch() {
        return mBLEConnector != null && mBLEConnector.getDevice() != null && mBLEConnector.getDevice().match();
    }

    /**
     * scan ble
     *
     * @param listener
     */
    public void startScan(final HBBLEScanListener listener) {

        BLEScanner.getInstance().stopScan();

        BLEScanner.getInstance().startScan(new BLEScanListener() {
            @Override
            public void onScanFinished() {

                if (listener != null) listener.BLEScanFinish();
            }

            @Override
            public void onScanFailed(int error) {

                if (listener != null) listener.BLEScanFailed(error);
            }

            @Override
            public void onScanResult(HBBluetoothDevice device) {

                /**
                 * ZH:目前没有做[去重]逻辑，直接上抛，先让用户用起来
                 * En:PopUP
                 */
                if (listener != null) listener.BLEScanResult(device);
            }
        });
    }

    /**
     * stop scan
     */
    public void stopScan() {
        BLEScanner.getInstance().stopScan();
    }

    /**
     * release strong reference
     */
    public void release() {
        if (listener != null) {
            listener = null;
        }
    }

    /**
     * 1.cache ble connect
     *
     * @param lpBluetoothDevice
     * @param listener
     */
    public void connectBLE(HBBluetoothDevice lpBluetoothDevice, final HBConnectBLEListener listener) {
        //should consider crsString ==""?
        if (lpBluetoothDevice == null) {
            return;
        }
        final String deviceCrc = lpBluetoothDevice.getCRCString();
        mBLEConnector = bleConnectorMap.get(deviceCrc);
        if (mBLEConnector != null && mBLEConnector.getConnectStatus() == BleConnectStatus.Connecting) {
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " BLE Connecting:device crc:" + deviceCrc);
            return;
        }
        if (mBLEConnector != null) {
            bleConnectorMap.remove(deviceCrc);
            mBLEConnector.unRegisterAllCharacteristicListener();
            mBLEConnector.disconnect();
            mBLEConnector = null;
        }
        mBLEConnector = new BLEConnector(lpBluetoothDevice);
        bleConnectorMap.put(deviceCrc, mBLEConnector);
        mBLEConnector.connect(listener);
        mBLEConnector.registerCharacteristicListener(new BLECharacteristicChangedListener() {
            @Override
            public void onCharacteristicChanged(int responseCode, String result) {

                if (StatisticConstant.LP_GET_DEVICEINFO_CMD == responseCode) {
                    //cache device common info
                    HBBLEDeviceInfo deviceInfo = GsonUtil.parseJsonToBean(result, HBBLEDeviceInfo.class);
                    if (deviceInfo != null && deviceInfo.getCommonInfo() != null) {
                        HarmanDeviceManager.getInstance().save(deviceCrc, deviceInfo.getCommonInfo());
                    }
                    //maybe has been disconnect
                    if (mBLEConnector != null) mBLEConnector.unRegisterCharacteristicListener(this);
                }
            }

            @Override
            public void onResponseTimeout(int responseCode) {
                if (mBLEConnector != null) mBLEConnector.unRegisterCharacteristicListener(this);
            }
        });
    }

    public void getWLANList(HBBluetoothDevice lpBluetoothDevice, final HBBLEAPListListener listener) {
        //should consider crsString ==""?
        if (lpBluetoothDevice == null) {
            return;
        }
        mBLEConnector = bleConnectorMap.get(lpBluetoothDevice.getCRCString());
        if (mBLEConnector == null) {
            listener.onFailed(new Exception("mBLEConnector is null"));
            return;
        }
        mBLEConnector.registerCharacteristicListener(new BLECharacteristicChangedListener() {
            @Override
            public void onCharacteristicChanged(int responseCode, String result) {

                LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " BLECommand getWLANList onCharacteristicChanged: " + (result != null ? result : "") + ", responseCode:" + responseCode);

                if (responseCode == StatisticConstant.LP_GET_AP_LIST_CMD) {
                    List<HBApItem> wlanApInfo = getWLANApInfo(result);

                    if (listener != null) {
                        listener.onSuccess(wlanApInfo);
                    }
                    mBLEConnector.unRegisterCharacteristicListener(this);
                }
            }

            @Override
            public void onResponseTimeout(int responseCode) {

                LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " BLECommand getWLANList onResponseTimeout: responseCode:" + responseCode);

                if (StatisticConstant.LP_GET_AP_LIST_CMD == responseCode) {
                    mBLEConnector.unRegisterCharacteristicListener(this);
                    if (listener != null) {
                        listener.onFailed(new Exception("response timeout"));
                    }
                }
            }
        });

        mBLEConnector.writeCommand(StatisticConstant.LP_GET_AP_LIST_CMD, "", new BleWriteCallback() {
            @Override
            public void onWriteSuccess(int current, int total, byte[] justWrite) {

            }

            @Override
            public void onWriteFailure(BleException exception) {
                if (listener != null) {
                    listener.onFailed(new Exception(exception.getDescription()));
                }
            }
        });
    }

    private List<HBApItem> getWLANApInfo(String result) {

        if (TextUtils.isEmpty(result)) {
            return null;
        }
        try {
            HBApInfo info = GsonUtil.parseJsonToBean(result, HBApInfo.class);
            if (info != null) {

                List<HBApItem> BLEAPInfoList = info.getAplist();
                if (BLEAPInfoList == null || BLEAPInfoList.size() == 0)
                    return null;

                for (int i = 0; i < BLEAPInfoList.size(); i++) {
                    HBApItem apItem = BLEAPInfoList.get(i);
                    if (apItem != null)
                        apItem.setDisplaySSID(HexUtil.hex2Str(apItem.getSsid()));
                }
                return BLEAPInfoList;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private HBApItem getCurrApItem(List<HBApItem> apItemList, String ssid) {

        if (apItemList == null || apItemList.size() == 0)
            return null;

        for (int i = 0; i < apItemList.size(); i++) {
            HBApItem tmpApItem = apItemList.get(i);
            if (TextUtils.equals(tmpApItem.getSsid(), ssid)
                    || TextUtils.equals(tmpApItem.getDisplaySSID(), ssid)) {
                LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " Find target router: " + tmpApItem.getDisplaySSID());
                return tmpApItem;
            }
        }

        return null;
    }


    public void sendBLEControlCmd(HBBluetoothDevice bluetoothDevice, int code, String cmd, HBBLEControlListener listener) {
        if (bluetoothDevice == null) {
            return;
        }
        mBLEConnector = bleConnectorMap.get(bluetoothDevice.getCRCString());
        if (mBLEConnector == null) {
            if (listener != null) listener.onFailed(new Exception("mBLEConnector is null"));
//            FirebaseMgr.Companion.getInstance().logEventCommandError(bluetoothDevice,code,"mBLEConnector is null");
            LogsUtil.i(AppLogTagUtil.DEVICE_TAG, TAG + " sendBLEControlCmd : mBLEConnector == null ble connection fail");
            return;
        }
        mBLEConnector.addControlListener(code, listener);


        mBLEConnector.writeCommand(code, cmd, null);
    }

    /**
     * 配置声音
     *
     * @param timeout  默认是60s
     * @param listener
     */
    public void configSound(HBBluetoothDevice bluetoothDevice, int timeout, HBBLEConfigSoundListener listener) {
        if (bluetoothDevice == null || TextUtils.isEmpty(bluetoothDevice.getCRCString())) return;
        mBLEConnector = bleConnectorMap.get(bluetoothDevice.getCRCString());
        if (mBLEConnector != null) {
            mBLEConnector.registerCharacteristicListener(new BLECharacteristicChangedListener() {
                @Override
                public void onCharacteristicChanged(int responseCode, String result) {
                    if (responseCode == StatisticConstant.LP_AUTH_START) {

                    } else if (responseCode == StatisticConstant.LP_AUTH_RESULT) {

                        if (listener != null) listener.onSuccess(responseCode, result);

                        if (mBLEConnector != null) mBLEConnector.unRegisterCharacteristicListener(this);
                    }
                }

                @Override
                public void onResponseTimeout(int responseCode) {

                    if (mBLEConnector != null) mBLEConnector.unRegisterCharacteristicListener(this);
                }
            });

            String countrycode = "{\"timeout\":" + timeout + "}";

            if (mBLEConnector != null) mBLEConnector.writeCommand(StatisticConstant.LP_AUTH_START, countrycode, null);

        } else {
            listener.onFailed(new Exception("mBLEConnector is null"));
        }
    }

    public void bleAuthCancelAll() {
        for (Map.Entry<String, BLEConnector> entry : bleConnectorMap.entrySet()) {
            if (entry.getValue() != null) {
                entry.getValue().writeCommand(StatisticConstant.LP_AUTH_CANCEL, "", null);
            }
        }
    }

    /**
     * cancel auth
     */
    public void bleAuthCancel(HBBluetoothDevice bluetoothDevice) {
        if (bluetoothDevice == null || TextUtils.isEmpty(bluetoothDevice.getCRCString())) {
            return;
        }
        mBLEConnector = bleConnectorMap.get(bluetoothDevice.getCRCString());
        if (mBLEConnector != null) {

            mBLEConnector.registerCharacteristicListener(new BLECharacteristicChangedListener() {
                @Override
                public void onCharacteristicChanged(int responseCode, String result) {

                    if (mBLEConnector != null) mBLEConnector.unRegisterCharacteristicListener(this);

                    LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " bleAuthCancel onCharacteristicChanged:" + responseCode + ", result:" + result);
                }

                @Override
                public void onResponseTimeout(int responseCode) {

                    LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " bleAuthCancel onResponseTimeout:" + responseCode);

                    if (mBLEConnector != null) mBLEConnector.unRegisterCharacteristicListener(this);
                }
            });

            if (mBLEConnector != null)
                mBLEConnector.writeCommand(StatisticConstant.LP_AUTH_CANCEL, "", null);

        } else {
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " bleAuthCancel failed mBLEConnector == null");
        }
    }

    /**
     * ble connect without auth and encry
     *
     * @param ssid
     * @param password
     * @param listener
     */
    public void connectWLAN(HBBluetoothDevice bluetoothDevice, final String ssid, String password, final HBConnectWiFiListener listener) {

        connectWLAN(bluetoothDevice, ssid, password, "", "", listener);
    }

    /**
     * convert json
     *
     * @param currApItem
     * @return
     */
    private String getCommandInfo(HBApItem currApItem) {

        JSONObject obj = new JSONObject();
        try {
            obj.put("ssid", currApItem == null ? ssid : currApItem.getSsid());
            obj.put("pass", mNeedHexStr ? HexUtil.str2HexStr(password) : password);
            obj.put("password", mNeedHexStr ? HexUtil.str2HexStr(password) : password);
            obj.put("auth", currApItem == null ? auth : currApItem.getAuth());
            obj.put("encry", currApItem == null ? encry : currApItem.getEncry());
            obj.put("identify", "");
            obj.put("os", osname);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return obj.toString();
    }


    BLECharacteristicChangedListener characteristicChangedListener = new BLECharacteristicChangedListener() {

        private void setCountryCode() {

            sendWiFiInfoCommand(1);
        }

        private void bleSetupResult(String result) {

            try {

                BLESetupResultItem resultItem = GsonUtil.parseJsonToBean(result, BLESetupResultItem.class);

                if (resultItem != null)
                    resultItem.setWlan0_mac(wlan0_mac);

                if (listener != null) {
                    listener.ConnectResult(getConnectApResult(resultItem.getCode()), resultItem);
                }

            } catch (Exception e) {
                e.printStackTrace();

                if (listener != null) {
                    listener.ConnectFailed(new Exception(result));
                }
            }
        }

        private int requestWLANListIndex = 0;//失败请求2次

        private void getWLNListInfo(int index) {

            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " getWLANList start:" + index);

            if (bOtherNetwork) {

                final String command = getCommandInfo(null);

                LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " send password command: " + command);
                mBLEConnector.writeCommand(StatisticConstant.LP_SEND_SSID_PASSWORD, command, new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {

                    }

                    @Override
                    public void onWriteFailure(BleException exception) {

                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " BLECommand LP_SEND_SSID_PASSWORD onWriteFailure: " + exception.getDescription());

                        if (listener != null) {
                            listener.ConnectFailed(new Exception(exception.getDescription()));
                        }
                    }
                });
                return;
            }

            if (mBLEConnector != null && mBLEConnector.getDevice() != null) {
                getWLANList(mBLEConnector.getDevice(), new HBBLEAPListListener() {
                    @Override
                    public void onSuccess(List<HBApItem> apItemList) {

                        requestWLANListIndex = 0;
                        HBApItem currApItem = getCurrApItem(apItemList, ssid);

                        if (currApItem == null && !bOtherNetwork) {
                            currApItem = new HBApItem();
                            currApItem.setSsid(ssid);
                            currApItem.setAuth("WPA2PSK");
                            currApItem.setEncry(encry);
//                        if (listener != null) {
//                            listener.ConnectResult(HBBLEConnectApResult.CONNECT_AP_NO_SSID, null);
//                        }
//                        return;
                        }

                        final String command = getCommandInfo(currApItem);

                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " send password command: " + command);

                        mBLEConnector.writeCommand(StatisticConstant.LP_SEND_SSID_PASSWORD,
                                command, new BleWriteCallback() {

                                    @Override
                                    public void onWriteSuccess(int current, int total, byte[] justWrite) {

                                    }

                                    @Override
                                    public void onWriteFailure(BleException exception) {

                                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " BLECommand LP_SEND_SSID_PASSWORD onWriteFailure: " + exception.getDescription());

                                        if (listener != null) {
                                            listener.ConnectFailed(new Exception(exception.getDescription()));
                                        }
                                    }
                                });
                    }

                    @Override
                    public void onFailed(Exception e) {

                        requestWLANListIndex++;

                        if (requestWLANListIndex < 2) {
                            getWLNListInfo(index);
                            return;
                        }

                        requestWLANListIndex = 0;
                        if (listener != null) {
                            listener.ConnectFailed(new Exception("getWLANList onFailed:" + e.getLocalizedMessage()));
                        }
                    }
                });
            }

        }

        /**
         *
         * @param index
         */
        private void sendWiFiInfoCommand(int index) {
            LogsUtil.d(AppLogTagUtil.BLE_TAG, TAG + " sendWiFiInfoCommand index: " + index);
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " sendWiFiInfoCommand isConnectorMatch: " + isConnectorMatch());
            LogsUtil.d(AppLogTagUtil.BLE_TAG, TAG + " sendWiFiInfoCommand auth: " + auth);
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " sendWiFiInfoCommand encry: " + encry);
            if (isConnectorMatch() && (TextUtils.isEmpty(auth) || TextUtils.isEmpty(encry))) {
                //如果上层传入的auth或者encry是空时，这里做一次getWLanList请求
                requestWLANListIndex = 0;
                getWLNListInfo(index);
            } else {

                final String command = getCommandInfo(null);

                LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " send password command: " + command);
                if(mBLEConnector == null){
                    return;
                }

                mBLEConnector.writeCommand(StatisticConstant.LP_SEND_SSID_PASSWORD, command, new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {

                    }

                    @Override
                    public void onWriteFailure(BleException exception) {

                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " BLECommand LP_SEND_SSID_PASSWORD onWriteFailure: " + exception.getDescription());

                        if (listener != null) {
                            listener.ConnectFailed(new Exception(exception.getDescription()));
                        }
                    }
                });
            }
        }

        private void getDeviceInfo() {
            if (bleConfig == null) {
                if (listener != null) {
                    listener.ConnectFailed(new Exception("ble config null."));
                }
                return;
            }

            if (bleConfig.isbHexSSIDPass()) {
                mNeedHexStr = true;
                sendWiFiInfoCommand(2);

            } else {

                String countrycode = "{\n" +
                        "\"code\":\"" + BLEUtil.getCountryCode() + "\",\n" +
                        "\"ccode\":\"" + BLEUtil.getCountryCode() + "\",\n" +
                        "}";

                mBLEConnector.writeCommand(StatisticConstant.LP_SET_COUNTRY_CODE, countrycode, new BleWriteCallback() {

                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {
                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " country code onWriteSuccess");
                    }

                    @Override
                    public void onWriteFailure(BleException exception) {

                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " country code onWriteFailure:" + exception.toString());

                        if (listener != null) {
                            listener.ConnectFailed(new Exception(exception.getDescription()));
                        }
                    }
                });
            }

        }

        @Override
        public void onCharacteristicChanged(int responseCode, String result) {

            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " onCharacteristicChanged responseCode: " + responseCode + "; result: " + result);

            if (responseCode == StatisticConstant.LP_AUTH_START) {

            } else if (responseCode == StatisticConstant.LP_AUTH_RESULT) {

            } else if (responseCode == StatisticConstant.LP_SET_COUNTRY_CODE) {
                setCountryCode();

            } else if (responseCode == StatisticConstant.LP_WIFI_SETUP_START) {
                if (listener != null) {
                    BLESetupResultItem resultItem = new BLESetupResultItem();
                    resultItem.setResult(result);
                    listener.ConnectResult(HBBLEConnectApResult.CONNECT_AP_START, resultItem);
                }
            } else if (responseCode == StatisticConstant.LP_WIFI_SETUP_END) {
                bleSetupResult(result);

            } else if (responseCode == StatisticConstant.LP_GET_DEVICEINFO_CMD) {
                LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " LP_GET_DEVICEINFO:" + result);
                mNeedHexStr = false;
                try {

                    HBBLEDeviceInfo deviceInfo = GsonUtil.parseJsonToBean(result, HBBLEDeviceInfo.class);

                    if (deviceInfo != null) {
                        if (mBLEConnector != null && mBLEConnector.getDevice() != null) {
                            mBLEConnector.getDevice().setHBBLEDeviceInfo(deviceInfo);
                        }
                        String protocolVersion = deviceInfo.getCommonInfo().getProtocolVersion();
                        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " protocolVersion:" + protocolVersion);

                        wlan0_mac = deviceInfo.getCommonInfo().getWlan0_mac();

                        if (!TextUtils.isEmpty(protocolVersion)) {

                            String tmpProtocolVersion = protocolVersion.replaceAll("\\.", "");

                            if (TextUtils.isDigitsOnly(tmpProtocolVersion)) {
                                mNeedHexStr = Integer.parseInt(tmpProtocolVersion) > 102;
                            }
                        }
                    }
                    //默认为真
                    mNeedHexStr = true;
                    bleConfig.setbHexSSIDPass(mNeedHexStr);

                } catch (Exception e) {
                    e.printStackTrace();
                    LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " protocolVersion Exception:" + e.getMessage());
                }

                getDeviceInfo();

            } else if (responseCode == 0) {//之前没有处理这块的逻辑，2020/5/25加的
                //老ble返回的结果
                setupResultOld(result, listener);
            }
        }


        @Override
        public void onResponseTimeout(int responseCode) {
            if (StatisticConstant.LP_SET_COUNTRY_CODE == responseCode) {
                if (listener != null) {
                    listener.ConnectFailed(new Exception("set country code timeout"));
                }

                mBLEConnector.unRegisterCharacteristicListener(this);
            }
            if (StatisticConstant.LP_SEND_SSID_PASSWORD == responseCode) {
                if (listener != null) {
                    listener.ConnectFailed(new Exception("send ssid password timeout"));
                }

                mBLEConnector.unRegisterCharacteristicListener(this);
            }
        }
    };

    String ssid = "", password = "", auth = "", encry = "", osname = "android";
    boolean bOtherNetwork = false;
    //jbl one ble配网成功后返回的uuid和ip在连接这因特网时，找不到对应的设备，只能借助wlan0_mac，所以这里在配网时需要记录下getDeviceInfo返回来的wlan0_mac
    String wlan0_mac = "";

    HBConnectWiFiListener listener;

    public void connectWLAN(HBBluetoothDevice bluetoothDevice, ConnectBLEInfo info, final HBConnectWiFiListener listener) {
        bOtherNetwork = info.isbOtherNetwork();
        connectWLAN(bluetoothDevice, info.getSsid(), info.getPassword(), info.getAuth(), info.getEncry(), listener);
    }

    /**
     * 新接口配网
     *
     * @param ssid
     * @param password
     * @param auth
     * @param encry
     * @param listener
     */
    @SuppressLint("MissingPermission")
    public void connectWLAN(HBBluetoothDevice bluetoothDevice, final String ssid, final String password, final String auth, final String encry, final HBConnectWiFiListener listener) {
        if (bluetoothDevice == null) {
            return;
        }
        LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " connectWLAN getCRCString:" + bluetoothDevice.getCRCString());
        mBLEConnector = bleConnectorMap.get(bluetoothDevice.getCRCString());

        if (mBLEConnector == null) {
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " mBLEConnector is null");
            if (listener != null) {
                listener.ConnectFailed(new Exception("ble connector is null"));
            }

            return;
        }

        this.ssid = ssid;
        this.password = password;
        this.auth = auth;
        this.encry = encry;
        this.listener = listener;

        mBLEConnector.unRegisterCharacteristicListener(characteristicChangedListener);
        mBLEConnector.registerCharacteristicListener(characteristicChangedListener);

        if (!isConnectorMatch()) {

            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " " + mBLEConnector.getDevice().getBluetoothDevice().getName() + " is 1.0 protocol");

            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " ble1.0 device");
            mBLEConnector.writeCommand(BLEUtil.getBleConnCmd(ssid, password), null);
        } else {
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " ble2.0 device");

            mBLEConnector.writeCommand(StatisticConstant.LP_GET_DEVICEINFO_CMD, "", null);
        }
    }

    private void setupResultOld(String result, HBConnectWiFiListener listener) {
        String ipStr = "IP:";
        String uuidStr = ":UUID:";
        if (result.contains(ipStr) && result.contains(uuidStr)) {
            int ipIndex = result.indexOf(ipStr);
            int uuidIndex = result.indexOf(uuidStr);
            String ip = result.substring(ipIndex, uuidIndex).replace(ipStr, "");
            String uuid = result.substring(uuidIndex).replace(uuidStr, "");
            if (listener != null) {

                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("IP", ip);
                    jsonObject.put("UUID", uuid);
                    jsonObject.put("code", "0");

                    BLESetupResultItem resultItem = GsonUtil.parseJsonToBean(jsonObject.toString(), BLESetupResultItem.class);

                    listener.ConnectResult(HBBLEConnectApResult.CONNECT_AP_SUCCESS, resultItem);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } else if (result.startsWith(BLEProfile.BLE_LINK_ERROR)) {
            if (listener != null)
                listener.ConnectFailed(new Exception("ble link error"));
        }
    }

    public void disConnect() {
        if (mBLEConnector != null) {
            mBLEConnector.disconnect();
            if (characteristicChangedListener != null)
                mBLEConnector.unRegisterCharacteristicListener(characteristicChangedListener);
            mBLEConnector = null;
        }
    }

    public void disconnect(HBBluetoothDevice bluetoothDevice) {

        if (bluetoothDevice == null || TextUtils.isEmpty(bluetoothDevice.getCRCString())) return;

        mBLEConnector = bleConnectorMap.get(bluetoothDevice.getCRCString());
        disConnect();
        bleConnectorMap.remove(bluetoothDevice.getCRCString());
        ApplicationViewModel.getInstance().autoConnectDeviceCrcList.remove(bluetoothDevice.getCRCString());
    }


    private HBBLEConnectApResult getConnectApResult(int code) {
        HBBLEConnectApResult result = HBBLEConnectApResult.CONNECT_AP_PASSWORD_ERROR;

        switch (code) {
            case 0:
                result = HBBLEConnectApResult.CONNECT_AP_SUCCESS;
                break;
            case 1:
                result = HBBLEConnectApResult.CONNECT_AP_NO_SSID;
                break;
            case 2:
                result = HBBLEConnectApResult.CONNECT_AP_WIFI_TIMEOUT;
                break;
            case 3:
                result = HBBLEConnectApResult.CONNECT_AP_DHCP_TIMEOUT;
                break;
            case 4:
                result = HBBLEConnectApResult.CONNECT_AP_PASSWORD_ERROR;
                break;
            case 5:
                result = HBBLEConnectApResult.CONNECT_AP_NO_SUPPORT_SECRET;
                break;
            case 6:
                result = HBBLEConnectApResult.CONNECT_AP_PARAMETER_ERROR;
                break;
            case 7:
                result = HBBLEConnectApResult.CONNECT_AP_OTHER_ERROR;
                break;
        }

        return result;
    }

    public boolean isBluetoothEnable() {
        return BLEManager.getInstance().isBluetoothEnable();
    }

    public void setStatisticCallback(StatisticCallback callback) {
        BLEManager.getInstance().setStatisticCallback(callback);
    }

    public void addModule(String module, String level, String payload) {
        BLEManager.getInstance().addModule(module, level, payload);
    }

    public void registerCharacteristicListener(HBBluetoothDevice bleDevice, HBBLEDeviceStatusChangedListener listener) {
        if (bleDevice == null) {
            return;
        }
        mBLEConnector = bleConnectorMap.get(bleDevice.getCRCString());
        if (mBLEConnector != null) {
            mBLEConnector.registerCharacteristicListener(new BLECharacteristicChangedListener() {
                @Override
                public void onCharacteristicChanged(int responseCode, String result) {
//                     if (responseCode==StatisticConstant.RESPONSE_BATTERY_STATUS) {
//                        if (listener != null) listener.onSuccess(responseCode, result);
//                    }
                    LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " registerCharacteristicListener mBLEConnector result: " + result);
                    if (listener != null) listener.onSuccess(responseCode, result);
                }

                @Override
                public void onResponseTimeout(int responseCode) {
                }
            });
        } else {
            LogsUtil.i(AppLogTagUtil.BLE_TAG, TAG + " registerCharacteristicListener mBLEConnector is null");
            if (listener != null) listener.onFailed(new Exception("mBLEConnector is null"));
        }
    }

    public void unregisterAllListener() {
        for (BLEConnector bleConnector : bleConnectorMap.values()) {
            if (bleConnector != null) {
                bleConnector.unRegisterAllCharacteristicListener();
            }

        }
    }

}
