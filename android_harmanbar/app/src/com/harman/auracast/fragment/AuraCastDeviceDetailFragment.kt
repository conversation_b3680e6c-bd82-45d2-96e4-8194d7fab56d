package com.harman.auracast.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.databinding.FragmentAuracastDeviceDetailBinding
import com.harman.auracast.AuraCastActivity
import com.harman.auracast.AuraCastViewModel
import com.harman.hasBatteryInfo
import com.harman.safeAddSource
import com.harman.safeAddSources
import com.harman.discover.bean.Device
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/6/13.
 */
class AuraCastDeviceDetailFragment : Fragment(), View.OnClickListener {

    private val viewModel: AuraCastViewModel by activityViewModels()

    private val _howToRemoveTheSpeakerVisible = MediatorLiveData<Boolean>()
    val howToRemoveTheSpeakerVisible: LiveData<Boolean>
        get() = _howToRemoveTheSpeakerVisible.distinctUntilChanged()

    private val _quitPartyLoadingVisible = MediatorLiveData<Boolean>()
    val quitPartyLoadingVisible: LiveData<Boolean>
        get() = _quitPartyLoadingVisible.distinctUntilChanged()

    private val _hasValidBatteryLevel = MediatorLiveData<Boolean>()
    val hasValidBatteryLevel: LiveData<Boolean>
        get() = _hasValidBatteryLevel.distinctUntilChanged()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentAuracastDeviceDetailBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = this
        binding.activity = activity as? AuraCastActivity
        binding.fragment = this
        binding.viewModel = viewModel

        bindViewModel()
        _hasValidBatteryLevel.value = viewModel.selectedDevice.value?.hasBatteryInfo() ?: false

        return binding.root
    }

    private fun bindViewModel() {
        _quitPartyLoadingVisible.safeAddSources(
            viewModel.selectedDevice,
            viewModel.quittingAuraCastDevices
        ) { _, _ ->
            mapQuitPartyLoadingVisible()
        }

        _howToRemoveTheSpeakerVisible.safeAddSource(viewModel.selectedDevice) { device ->
            _howToRemoveTheSpeakerVisible.value = if (null == device) {
                false
            } else {
                !device.ableQuitByApp()
            }
        }

        _hasValidBatteryLevel.safeAddSource(viewModel.selectedDevice) { device ->
            _hasValidBatteryLevel.value = device?.hasBatteryInfo() ?: false
        }
    }

    private fun mapQuitPartyLoadingVisible(
        device: Device? = viewModel.selectedDevice.value,
        quittingDevices: List<Device>? = viewModel.quittingAuraCastDevices.value
    ): Boolean {
        device ?: return false
        if (!device.ableQuitByApp()) {
            return false
        }

        return quitting(device = device, quittingDevices = quittingDevices)
    }

    private fun quitting(
        device: Device,
        quittingDevices: List<Device>?
    ): Boolean = !quittingDevices.isNullOrEmpty() && quittingDevices.contains(device)

    private fun Device.ableQuitByApp(): Boolean {
        val device = this
        Logger.d(TAG, "ableQuitByApp() >>> device[${device.UUID}] " +
                "isWiFiOnline[${device.isWiFiOnline}] isGattConnected[${device.isGattConnected}] " +
                "isBrEdrConnected[${device.isBrEdrConnected}]")
        return device.isWiFiOnline || device.isGattConnected || device.isBrEdrConnected
    }

    fun onQuitBtnClick() {
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            if (viewModel.quitAuraCast(context = context, device = viewModel.selectedDevice.value)) {
                (activity as? AuraCastActivity)?.onDeviceDetailPutAway()
            }
        }
    }

    fun onHowToRemoveThisSpeakerClick() {
        (activity as? AuraCastActivity)?.showHowToExitAuraCastDialog()
    }

    override fun onClick(v: View?) {
        // do nothing
    }

    companion object {
        private const val TAG = "AuraCastDeviceDetailFragment"
    }
}