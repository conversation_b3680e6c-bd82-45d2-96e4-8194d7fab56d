package com.harman.auracast

import android.bluetooth.BluetoothDevice
import android.content.Context
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.command.portable.EnumSetLinkMode
import com.harman.connect.disconnectGatt
import com.harman.connect.syncEnterAuraCastWithTimeout
import com.harman.connect.syncExitAuraCastWithTimeout
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncSetAuraCastStatusWithTimeout
import com.harman.connect.syncSetLinkModeWithTimeout
import com.harman.safeAddSources
import com.harman.discover.DeviceScanner
import com.harman.discover.DeviceStore
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PortableDevice
import com.harman.discover.util.JobQueue
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.addIfAbsent
import com.harman.discover.util.Tools.bleConnectable
import com.harman.discover.util.Tools.updateWithoutReOrder
import com.harman.discover.util.Tools.filterBroadcaster
import com.harman.discover.util.Tools.isBroadcasterOn
import com.harman.discover.util.Tools.isConnectedInAuraCast
import com.harman.discover.util.Tools.isNearby
import com.harman.discover.util.Tools.isReceiverOn
import com.harman.discover.util.Tools.refresh
import com.harman.discover.util.Tools.remove
import com.harman.discover.util.Tools.supportAuraCast
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_SINGLE_POOL
import com.harman.v5protocol.bean.devinfofeat.V5AuracastMode
import com.harman.v5protocol.bean.devinfofeat.V5AuracastModeEnum
import com.jbl.one.configuration.AppConfigurationUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by gerrardzhang on 2024/6/11.
 */

class AuraCastViewModel : ViewModel(), DefaultLifecycleObserver {

    private val _auraCastSupportDevices = MutableLiveData<List<Device>?>()
    val auraCastSupportDevices: LiveData<List<Device>?>
        get() = _auraCastSupportDevices

    private val _nearbyDevices = MutableLiveData<List<Device>?>()
    val nearbyDevices: LiveData<List<Device>?>
        get() = _nearbyDevices

    private val _broadcasterDevice = MutableLiveData<Device?>()
    val broadcasterDevice: LiveData<Device?>
        get() = _broadcasterDevice

    private val _receiverDevices = MediatorLiveData<List<Device>?>()
    val receiverDevices: LiveData<List<Device>?>
        get() = _receiverDevices

    private val _receiverDeviceOne = MutableLiveData<Device?>()
    val receiverDeviceOne: LiveData<Device?>
        get() = _receiverDeviceOne

    private val _receiverDeviceTwo = MutableLiveData<Device?>()
    val receiverDeviceTwo: LiveData<Device?>
        get() = _receiverDeviceTwo

    private val _receiverDeviceThree = MutableLiveData<Device?>()
    val receiverDeviceThree: LiveData<Device?>
        get() = _receiverDeviceThree

    private val _receiverDeviceFour = MutableLiveData<Device?>()
    val receiverDeviceFour: LiveData<Device?>
        get() = _receiverDeviceFour

    private val _receiverDeviceFive = MutableLiveData<Device?>()
    val receiverDeviceFive: LiveData<Device?>
        get() = _receiverDeviceFive

    private val _selectedDevice = MutableLiveData<Device?>()
    val selectedDevice: LiveData<Device?>
        get() = _selectedDevice

    private val _quittingAuraCastDevices = MutableLiveData<List<Device>?>()
    val quittingAuraCastDevices: LiveData<List<Device>?>
        get() = _quittingAuraCastDevices

    private val _enteredAuraCastDevices = MutableLiveData<List<Device>?>()
    val enteredAuraCastDevices: LiveData<List<Device>?>
        get() = _enteredAuraCastDevices

    private val _waitingAuraCastDevices = MutableLiveData<List<Device>?>()
    val waitingAuraCastDevices: LiveData<List<Device>?>
        get() = _waitingAuraCastDevices

    private val jobQueue = JobQueue(scope = viewModelScope)

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)

        auraCastSupportDevices.observe(owner) { devices ->
            val nearbys = devices?.filter { device ->
                device.isNearby()
            }?.sortedWith(nearbyDevicesComparator)

            Logger.d(TAG, "nearbyDevices >>> ${nearbys?.listAuraCastRelatedInfo()}")
            _nearbyDevices.value = nearbys

            val broadcaster = devices?.filterBroadcaster()
            Logger.d(TAG, "broadcaster >>> ${broadcaster?.auraCastRelatedInfo()}")
            _broadcasterDevice.value = broadcaster
        }

        _receiverDevices.safeAddSources(auraCastSupportDevices, broadcasterDevice) { devices, broadcaster ->
            _receiverDevices.value = filterReceivers(devices = devices, broadcaster = broadcaster)
        }

        updateByFullDevices(devices = DeviceStore.baseDevices)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)

        jobQueue.cancel()
    }

    internal fun startScan(context: Context?) {
        DeviceScanner.registerObserver(observer = scanObserver)
    }

    internal fun stopScan(context: Context?) {
        DeviceScanner.unregisterObserver(observer = scanObserver)
    }

    @MainThread
    internal fun onSelectedDevice(device: Device?) {
        _selectedDevice.value = device
    }

    @MainThread
    internal suspend fun quitAuraCast(context: Context?, device: Device?): Boolean {
        device ?: return false

        Logger.i(TAG, "quitAuraCast() >>> launch[${device.UUID}] platform[${device.platform}]")
        _enteredAuraCastDevices.remove(device)
        _quittingAuraCastDevices.addIfAbsent(device)
        val rst = asyncQuitAuraCast(context = context, device = device)
        Logger.i(TAG, "quitAuraCast() >>> change UI. rst[$rst] [${device.UUID}] platform[${device.platform}]")

        if (rst) {
            _selectedDevice.value = null
            _quittingAuraCastDevices.remove(device)
        } else {
            _quittingAuraCastDevices.remove(device)
        }

        return rst
    }

    private suspend fun asyncQuitAuraCast(context: Context?, device: Device): Boolean {
        Logger.i(TAG, "asyncQuitAuraCast() >>> target[${device.UUID}] platform[${device.platform}]")
        val rst = when (device) {
            is OneDevice -> {
                if (!device.isWiFiOnline && !device.syncGattConnectWithTimeout(context = context)) {
                    Logger.w(TAG, "asyncQuitAuraCast() >>> fail to connect Gatt. target[${device.UUID}] platform[${device.platform}]")
                    return false
                }

                val rst = device.syncExitAuraCastWithTimeout(logTag = TAG)
                Logger.i(TAG, "asyncQuitAuraCast() >>> rst[$rst] [${device.UUID}]")

                if (device.isGattConnected) {
                    device.disconnectGatt()
                }

                rst
            }

            is PartyBoxDevice -> asyncPartyBoxQuitAuraCast(context = context, device = device)

            is PortableDevice -> {
                if (!device.syncGattConnectWithTimeout(context = context)) {
                    Logger.w(TAG, "asyncQuitAuraCast() >>> fail to connect Gatt. [${device.UUID}]")
                    return false
                }

                val rst = device.syncSetLinkModeWithTimeout(logTag = TAG, mode = EnumSetLinkMode.OFF)
                Logger.i(TAG, "asyncQuitAuraCast() >>> rst[$rst] [${device.UUID}]")
                device.disconnectGatt()
                rst
            }

            is PartyBandDevice -> {
                val connectedBefore = device.isGattConnected
                if (!device.syncGattConnectWithTimeout(context = context)) {
                    return false
                }
                val rst = device.setDevInfoFeat(V5AuracastMode(V5AuracastModeEnum.Normal))
                if (!connectedBefore) {
                    device.disconnectGatt()
                }
                rst?.isSuccess() == true
            }

            else -> false
        }

        Logger.i(TAG, "asyncQuitAuraCast() >>> rst[$rst] target[${device.UUID}] platform[${device.platform}]")
        return rst
    }

    private suspend fun asyncPartyBoxQuitAuraCast(context: Context?, device: PartyBoxDevice): Boolean {
        if (device.isBrEdrConnected) {
            val rst = device.syncSetAuraCastStatusWithTimeout(logTag = TAG, open = false, protocol = BluetoothDevice.TRANSPORT_BREDR)
            Logger.i(TAG, "asyncPartyBoxQuitAuraCast() >>> BREDR rst[$rst] [${device.UUID}]")
            return rst
        }

        val connectedBefore = device.isGattConnected
        if (!device.syncGattConnectWithTimeout(context = context)) {
            Logger.w(TAG, "asyncPartyBoxQuitAuraCast() >>> fail to connect Gatt. target[${device.UUID}] platform[${device.platform}]")
            return false
        }

        val rst = device.syncSetAuraCastStatusWithTimeout(logTag = TAG, open = false, protocol = BluetoothDevice.TRANSPORT_LE)
        Logger.i(TAG, "asyncPartyBoxQuitAuraCast() >>> BLE rst[$rst] [${device.UUID}] connectedBefore[$connectedBefore]")
        if (!connectedBefore) {
            device.disconnectGatt()
        }
        return rst
    }

    @MainThread
    internal fun enterAuraCast(context: Context?, device: Device?) {
        device ?: return

        _quittingAuraCastDevices.remove(device)
        _waitingAuraCastDevices.addIfAbsent(device)
        Logger.i(TAG, "enterAuraCast() >>> [${device.UUID}] platform[${device.productLine}] join job queue")

        // in queue and wait DISPATCHER_SINGLE_POOL dispatch one by one
        jobQueue.submit(DISPATCHER_SINGLE_POOL) {
            // @DISPATCHER_SINGLE_POOL

            val rst = withContext(DISPATCHER_DEFAULT) {
                syncEnterAuraCast(context = context, device = device)
            }

            Logger.i(TAG, "enterAuraCast() >>> rst[$rst] target[${device.UUID}] platform[${device.productLine}] thread[${Tools.threadName()}]")
            withContext(DISPATCHER_FAST_MAIN) {
                _waitingAuraCastDevices.remove(device)

                if (rst) {
                    _auraCastSupportDevices.refresh()

                    _enteredAuraCastDevices.addIfAbsent(device)
                    delay(AURACAST_ENTER_DISPLAY_DELAY_MILLS)
                    _enteredAuraCastDevices.remove(device)
                }
            }
        }
    }

    @WorkerThread
    private suspend fun syncEnterAuraCast(context: Context?, device: Device): Boolean {
        return when (device) {
            is OneDevice -> {
                if (!device.isWiFiOnline && !device.syncGattConnectWithTimeout(context = context)) {
                    Logger.w(TAG, "syncEnterAuraCast() >>> fail to connect Gatt. [${device.UUID}]")
                    return false
                }

                device.syncEnterAuraCastWithTimeout(logTag = TAG)
            }

            is PartyBoxDevice -> syncPartyBoxEnterAuraCast(device = device, context = context)

            is PortableDevice -> {
                if (!device.syncGattConnectWithTimeout(context = context)) {
                    Logger.w(TAG, "syncEnterAuraCast() >>> fail to connect Gatt. [${device.UUID}]")
                    return false
                }

                val rst = device.syncSetLinkModeWithTimeout(logTag = TAG, mode = EnumSetLinkMode.AURACAST)
                Logger.i(TAG, "syncEnterAuraCast() >>> rst[$rst] [${device.UUID}]")
                device.disconnectGatt()
                rst
            }

            is PartyBandDevice -> {
                val connectedBefore = device.isGattConnected
                if (!device.syncGattConnectWithTimeout(context = context)) {
                    return false
                }
                val rst = device.setDevInfoFeat(V5AuracastMode(V5AuracastModeEnum.On))
                if (!connectedBefore) {
                    device.disconnectGatt()
                }
                rst?.isSuccess() == true
            }

            else -> {
                Logger.w(TAG, "syncEnterAuraCast() >>> unsupported platform[${device.productLine}]")
                false
            }
        }
    }

    private suspend fun syncPartyBoxEnterAuraCast(context: Context?, device: PartyBoxDevice): Boolean {
        if (device.isBrEdrConnected) {
            val rst = device.syncSetAuraCastStatusWithTimeout(logTag = TAG, open = true, protocol = BluetoothDevice.TRANSPORT_BREDR)
            Logger.i(TAG, "syncPartyBoxEnterAuraCast() >>> BREDR rst[$rst] [${device.UUID}] ")
            return rst
        }

        val connectedBefore = device.isGattConnected
        if (!device.syncGattConnectWithTimeout(context = context)) {
            Logger.w(TAG, "syncPartyBoxEnterAuraCast() >>> fail to connect Gatt. [${device.UUID}]")
            return false
        }

        val rst = device.syncSetAuraCastStatusWithTimeout(logTag = TAG, open = true, protocol = BluetoothDevice.TRANSPORT_LE)
        Logger.i(TAG, "syncPartyBoxEnterAuraCast() >>> BLE rst[$rst] [${device.UUID}] connectedBefore[$connectedBefore]")
        if (!connectedBefore) {
            device.disconnectGatt()
        }
        return rst
    }


    private val scanObserver = object : IHmDeviceObserver {
        @AnyThread
        override fun onDevicesUpdate(devices: List<Device>) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                updateByFullDevices(devices = devices)
            }
        }
    }

    @MainThread
    private fun updateByFullDevices(devices: List<Device>) {
        Logger.d(
            TAG, "updateByFullDevices() >>> total before filter[${devices.size}]\n" +
                    devices.listAuraCastRelatedInfo()
        )

        val filteredDevices = devices.filter { device ->
            device.supportAuraCast()
        }

        Logger.d(
            TAG, "updateByFullDevices() >>> total after[${filteredDevices.size}]\n" +
                    filteredDevices.listAuraCastRelatedInfo()
        )
        _auraCastSupportDevices.updateWithoutReOrder(filteredDevices)

        _selectedDevice.value?.let { selected ->
            if (!filteredDevices.contains(selected)) {
                //Logger.d(TAG, "onDevicesUpdate() >>> remove selected device")
                _selectedDevice.value = null
            }
        }
    }

    private fun List<Device>.listAuraCastRelatedInfo(): String {
        if (isEmpty()) return ""

        val sb = StringBuilder()
        forEach { device ->
            sb.append(device.auraCastRelatedInfo())
        }

        return sb.toString()
    }

    /**
     * @link [Device.supportAuraCast]
     */
    private fun Device.auraCastRelatedInfo(): String {
        val device = this
        val sb = StringBuilder()
        sb.append("UUID[${device.UUID}] ")
        sb.append("Name[${device.deviceName}] ")
        sb.append("ProductLine[${device.productLine.desc}] ")
        sb.append("Pid[${device.pid}]")
        sb.append("Cid[${device.cid}]")
        sb.append("AuracastSupport[${device.isAuraCastSupport}] ")

        val configSupportCrossAuracast = device.pid?.let { AppConfigurationUtils.isSupportedCrossAuraCast(it) } ?: false
        sb.append("ConfigSupportCrossAuracast[$configSupportCrossAuracast] ")

        sb.append("isAuraCastOn[${device.isAuraCastOn}] ")
        if (device is OneDevice) {
            sb.append("Role[${device.role}] ")
        }
        sb.append("AuracastRole[${device.auraCastRole}] ")
        sb.append("isAuraCastDisabled[${device.isAuraCastDisabled}] ")
        sb.append("isA2DPConnected[${device.isA2DPConnected}] ")
        sb.append("bleConnectable[${device.bleConnectable()}]")
        sb.append("isGattConnected[${device.isGattConnected}]")
        sb.append("isBrEdrConnected[${device.isBrEdrConnected}]")
        sb.append("isWiFiOnline[${device.isWiFiOnline}] ")
        sb.append("isConnectedInAuraCast[${device.isConnectedInAuraCast()}] ")

        sb.append("\n")
        return sb.toString()
    }

    private fun filterReceivers(
        devices: List<Device>?,
        broadcaster: Device?
    ): List<Device>? {
        val receivers = devices?.filter { device ->
            device.isReceiverOn() || (device.isBroadcasterOn() && device != broadcaster)
        }

        Logger.d(TAG, "filterReceivers() >>> ${receivers?.listAuraCastRelatedInfo()}")

        _receiverDeviceOne.value = receivers?.getOrNull(0)
        _receiverDeviceTwo.value = receivers?.getOrNull(1)
        _receiverDeviceThree.value = receivers?.getOrNull(2)
        _receiverDeviceFour.value = receivers?.getOrNull(3)
        _receiverDeviceFive.value = receivers?.getOrNull(4)

        return receivers
    }

    /**
     * Priority:
     * 1. Classic BT connected + Online(WiFi or BLE connected)
     * 2. Online(WiFi or BLE connected)
     * 3. Classic BT connected - Online(WiFi or BLE connected)
     * 4. Others
     */
    private val nearbyDevicesComparator = Comparator<Device> { o1, o2 ->
        val p1 = o1.getPriority()
        val p2 = o2.getPriority()

        if (p1 != p2) {
            return@Comparator if (p1 < p2) -1 else 1
        }

        return@Comparator when {
            o1.firstTouchTime > o2.firstTouchTime -> -1
            o1.firstTouchTime < o2.firstTouchTime -> 1
            else -> 0
        }
    }

    /**
     * @return Littler, higher
     */
    private fun Device.getPriority(): Int = when {
        isA2DPConnected && isWiFiOnline -> 0
        isA2DPConnected && isGattConnected -> 1
        isWiFiOnline -> 2
        isGattConnected -> 3
        isA2DPConnected && !isWiFiOnline && !isGattConnected -> 4
        else -> 5
    }

    companion object {
        private const val TAG = "AuraCastViewModel"

        private const val AURACAST_ENTER_DISPLAY_DELAY_MILLS = 2 * 1000L
    }
}
