package com.harman

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.bluetooth.BluetoothDevice
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.net.Uri
import androidx.activity.ComponentActivity
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.GsonUtils
import com.bumptech.glide.signature.MediaStoreSignature
import com.google.gson.JsonObject
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.command.one.bean.GetOtaAccessPointResponse
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.Member
import com.harman.connect.syncBrEdrConnectWithTimeout
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetFirmwareVersionWithTimeout
import com.harman.connect.syncGetOtaAccessPointWithTimeout
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.discover.info.EnumProductLine
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.getBarPort
import com.harman.discover.util.Tools.getGroupMode
import com.harman.discover.util.Tools.isPartyLightCategory
import com.harman.discover.util.Tools.safeResume
import com.harman.hkone.DeviceImageUtil
import com.harman.log.Logger
import com.harman.multichannel.HarmancastManager
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.ota.PartyOtaFetchTask
import com.harman.ota.RemoteUpdateModel
import com.harman.product.list.EnumUIConnectStatus
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_DEFAULT
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.AppSupportedModels
import com.jbl.one.configuration.model.AuraCastSupportedModels
import com.jbl.one.configuration.model.Model
import com.jbl.one.configuration.model.ModelConfig
import com.jbl.one.configuration.model.Orientation
import com.jbl.one.configuration.model.ProductFeature
import com.wifiaudio.action.lan.LocaleLanConfigUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.app.debug.DebugConfigKey
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp
import com.wifiaudio.utils.cloudRequest.model.ImageBean
import com.wifiaudio.utils.cloudRequest.rxhttp.CloudEncrypt
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import rxhttp.wrapper.param.RxHttp
import java.io.Serializable
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 * Created by gerrardzhang on 2024/4/30.
 */
private val supportedModels: AppSupportedModels?
    get() = AppConfigurationUtils.getSupportModelList()

private val redirectModels: AppSupportedModels?
    get() = AppConfigurationUtils.getRedirectionSupportList()

private val crossAuraCastModels: AuraCastSupportedModels?
    get() = AppConfigurationUtils.getCrossAuraCastSupportList()

fun Device.hexPid(): Int? = pid?.toIntOrNull(16)

fun isJBLOneApp() = BuildConfig.FLAVOR.contains("jblone")

fun isHKOneApp() = BuildConfig.FLAVOR.contains("hkone")

fun OneDevice.modelName(): String? {
    val name = wifiDevice?.deviceItem?.devStatus?.project ?: offlineDummy?.devStatus?.project
    if (!name.isNullOrBlank()) {
        return name
    }

    val pid = bleDevice?.pid ?: return null
    return AppConfigurationUtils.getSupportModelList()?.modelList?.let { it.get(pid)?.modelName }
}

fun Device.shortName(context: Context? = null): String? {
    val device = this
    val ctx = context ?: WAApplication.me.baseContext

    val prefix = if (BuildConfig.ENABLE_LOCAL_FLAG && !BuildConfig.IS_RELEASE) "[${device.UUID}]" else ""

    val shortDeviceName = deviceName?.removePrefix(ctx.getString(R.string.brand_name))
        ?.removePrefix(ctx.getString(R.string.brand_name).uppercase())
        ?.trimStart()

    return prefix + if (!shortDeviceName.isNullOrBlank()) {
        shortDeviceName
    } else {
        device.modelName()?.removePrefix(WAApplication.me.mAppLanguageContext.getString(R.string.brand_name))
            ?.removePrefix(WAApplication.me.mAppLanguageContext.getString(R.string.brand_name).uppercase())
            ?.trimStart()
    }
}

fun Device.colorId(): String? =
    wifiDevice?.colorStr ?: bleDevice?.colorID ?: dummyCid

fun OneDevice?.supportBattery(): Boolean {
    if ((this?.wifiDevice?.deviceItem?.devInfoExt?.batteryPercent ?: 0) > 0) {
        return true
    }

    val pidGet = (this?.wifiDevice?.deviceItem ?: this?.offlineDummy)?.project?.let {
        AppConfigurationUtils.getPidByModelName(it)
    } ?: run {
        this?.pid
    } ?: run {
        return false
    }

    return AppConfigurationUtils.getModelConfig(pidGet)?.capability?.contains(ProductFeature.Battery) ?: false
}

fun Device?.supportBattery(): Boolean {
    val device = this ?: return false

    return if (this is OneDevice) {
        this.supportBattery() || (wifiDevice?.deviceItem?.devInfoExt?.batteryPercent ?: 0) > 0
    } else {
        AppConfigurationUtils.isSupportBattery(device.pid)
    }
}

/**
 * Gavin Gan.
 * @return true when
 * 1. Device model config exists "Battery" in "capability" group;
 * 2. Device battery level was not 0 (had valid battery level from BLE broadcast or command);
 * 3. Device is charging and this status comes from BLE broadcast or command;
 */
fun Device.hasBatteryInfo(): Boolean {
    return supportBattery() && (batteryLevel > 0 || isCharging)
}

fun Device.modelName(): String? {
    wifiDevice?.deviceItem?.devStatus?.project?.let { modelName ->
        if (modelName.isNotBlank()) {
            return modelName
        }
    }

    return pid?.modelName()
}

fun String.modelName(): String? {
    val pid = this
    if (pid.isBlank()) {
        return null
    }

    return supportedModels?.modelList?.filter { pid.equals(it.key, true) }?.firstNotNullOfOrNull { it.value.modelName }
        ?: redirectModels?.modelList?.filter { pid.equals(it.key, true) }?.firstNotNullOfOrNull { it.value.modelName }
        ?: crossAuraCastModels?.modelList?.filter { pid.equals(it.key, true) }?.firstNotNullOfOrNull { it.value.modelName }
}

fun OneDevice.model(): Model? {
    val modelName = modelName()
    if (modelName.isNullOrBlank()) {
        return null
    }
    return supportedModels?.modelList?.filterValues { modelName.equals(it.modelName, true) }?.firstNotNullOfOrNull { it.value }
}

fun OneDevice.modelConfig(): ModelConfig? {
    val modelName = modelName()
    if (modelName.isNullOrBlank()) {
        return null
    }
    val pid = AppConfigurationUtils.getPidByModelName(modelName) ?: return null
    return AppConfigurationUtils.getModelConfig(pid)
}

fun OneDevice.category(): String? {
    return model()?.category
}

fun OneDevice.isSoundBar(): Boolean {
    return pid?.let { AppConfigurationUtils.isSoundBar(it) } == true
}

fun OneDevice.isPartyBox(): Boolean {
    return "2095".equals(pid, true)
}

fun OneDevice.isHomeSeries(): Boolean {
    return when (pid?.lowercase(Locale.ROOT)) {
        "20bf", // Authentic 200
        "20c0", // Authentic 300
        "20c1" -> true // Authentic 500
        else -> false
    }
}

fun OneDevice.isSub(): Boolean {
    return pid?.let { AppConfigurationUtils.isSub(it) } == true
}

fun OneDevice.isOneCommander(): Boolean {
    return pid?.let { AppConfigurationUtils.isOneCommander(it) } == true
}

fun Device?.isOneCommander(): Boolean {
    if (this !is OneDevice) return false
    return "214d".equals(pid, true)
}

fun OneDevice.isOneCommanderGroup(): Boolean {
    return isOneCommander() && this.getGroupMode() == GroupMode.GROUP && getBarPort()?.isNotEmpty() == true
}

fun Device?.isNonePlayableDevice(): Boolean  = when {
    this is OneDevice && isSub() -> true
    isOneCommander() -> true
    else -> false
}

/**
 * Req. /checkFwVersion api depends on info in [GetOtaAccessPointResponse]
 */
@SuppressLint("CheckResult")
@WorkerThread
suspend fun GetOtaAccessPointResponse.checkFwVersion(tag: String, oneDevice: OneDevice): CheckFWResp? = suspendCoroutine { continuation ->
    val otaAccessPoint = this
    val domain = otaAccessPoint.url
    val pkgName = otaAccessPoint.packageName?.ifBlank { "update" } ?: "update"

    val deviceItem = oneDevice.wifiDevice?.deviceItem

    if (0 != otaAccessPoint.errorCode ||
        domain.isNullOrBlank() ||
        null == deviceItem
    ) {
        Logger.e(
            tag, "checkFwVersion() >>> some params illegal: " +
                    "errCode[${otaAccessPoint.errorCode}] " +
                    "domain[$domain] " +
                    "deviceItem is null?[${null == deviceItem}]"
        )
        continuation.resume(null)
        return@suspendCoroutine
    }

    val images = mutableListOf<ImageBean>()

    val firmware = deviceItem.devStatus?.firmware
    if (!firmware.isNullOrBlank()) {
        images.add(ImageBean(pkgName, firmware))
    }

    val mcuVersion = deviceItem.devStatus?.mcu_ver
    if (!mcuVersion.isNullOrBlank()) {
        images.add(ImageBean("mcu", mcuVersion))
    }

    val selectLan = LocaleLanConfigUtil.getDefaultAppLan()

    val modelConfig = oneDevice.modelConfig()
    val body = """
        [{
            "mcuName": "${modelConfig?.mcuName}",
            "fwUid": "${modelConfig?.fwUid}",
            "version": "1.0",
            "language": "${selectLan.lowercase()}",
            "deviceUid": "${deviceItem.devStatus?.uuid}",
            "fwVersion": "$firmware",
            "macAddr": "${oneDevice.wifiDevice?.deviceItem?.devStatus?.mac}",
            "region": "${deviceItem.devStatus?.region}",
            "isBackup": 0,
            "imgList": ${GsonUtils.toJson(images)}
        }]
    """.trimIndent().replace(Regex("\n"), "")

    val url = "https://${domain}/checkFwVersion"
    Logger.d(tag, "checkFwVersion() >>> url:$url\nbody:$body")

    val encryptBody = CloudEncrypt.encryptAES2Base64(body)

    RxHttp.PostTextBodyParam(url)
        .setBody(encryptBody)
        .asString()
        .map { contents ->
            CloudEncrypt.decryptBase64AES(contents)
        }
        .subscribe({ content ->
            if (content.isNullOrBlank()) {
                Logger.w(tag, "checkFwVersion() >>> empty rsp")
                continuation.resume(null)
                return@subscribe
            }

            Logger.d(tag, "checkFwVersion() >>> raw rsp: $content")
            val checkFwRsp = GsonUtils.fromJson(content, CheckFWResp::class.java)
            continuation.resume(checkFwRsp)
        }, { t ->
            Logger.e(tag, "checkFwVersion() >>> exception while request:$t")
            continuation.resume(null)
        })
}

fun Long.toMin(): Int {
    if (this < 0) {
        return -1
    }

    return (this / 1000 / 60).toInt() + 1
}

fun String?.ableUpgrade(): Boolean {
    return !"no".equals(this, true)
}

@MainThread
fun <SpecDevice : Device> MutableLiveData<List<SpecDevice>?>.offer(t: SpecDevice) {
    val lasts = this.value?.toMutableList() ?: mutableListOf()
    lasts.add(t)
    this.value = lasts
}

@MainThread
fun MutableLiveData<List<Device>?>.contains(t: Device): Boolean {
    return this.value?.any { device ->
        !device.UUID.isNullOrBlank() && !t.UUID.isNullOrBlank() && device.UUID == t.UUID
    } ?: false
}

@MainThread
fun MutableLiveData<List<Device>?>.remove(t: Device) {
    val lasts = this.value?.toMutableList() ?: return
    lasts.removeIf { last ->
        !last.UUID.isNullOrBlank() && !t.UUID.isNullOrBlank() && last.UUID == t.UUID
    }
    this.value = lasts
}

fun Device.displayDeviceName(): String {
    val device = this
    val deviceName = device.deviceName
    val modelName = device.modelName()

    val prefix = if (BuildConfig.ENABLE_LOCAL_FLAG && !BuildConfig.IS_RELEASE) "[${device.UUID}]" else ""

    return prefix + if (!deviceName.isNullOrBlank()) {
        deviceName
    } else {
        modelName
    }
}

fun Device.displayShortDeviceName() = shortName()

fun Device?.uiConnectStatus(): EnumUIConnectStatus {
    val device = this ?: return EnumUIConnectStatus.OFFLINE

    val ret = if (device.isWiFiOnline) {
        EnumUIConnectStatus.WIFI_ONLINE
    } else if (device.isA2DPControllable()) {
        EnumUIConnectStatus.BLUETOOTH_CONNECTED
    } else if (device.isReadyToConnect()) {
        EnumUIConnectStatus.READY_TO_CONNECT
    } else if (device.isBLEAuthControllable()) {
        EnumUIConnectStatus.BLE_CONNECTED
    } else if (device.isPartyLightOnline()) {
        EnumUIConnectStatus.BLE_CONNECTED
    } else if (device.isOffline) {
        EnumUIConnectStatus.OFFLINE
    } else {
        EnumUIConnectStatus.OFFLINE
    }

    /*Logger.d("uiConnectStatus", "UUID[$UUID] ui[$ret] pid[$pid] dev.name[$deviceName] " +
            " WiFi[${device.isWiFiOnline}] A2DP[${device.isA2DPConnected}] Gatt[${device.isGattConnected}]" +
            " BLE[${device.isBLEOnline}] Offline[${device.isOffline}]")*/
    return ret
}

fun Device.isA2DPControllable(): Boolean {
    val device = this
    val isSupportGattControl = AppConfigurationUtils.isSupportBleControl(device.pid)
    //Logger.d("uiConnectStatus", "UUID[$UUID] A2DP[${device.isA2DPConnected}] supportGatt[${isSupportGattControl}]")

    return device.isA2DPConnected && isSupportGattControl
}

/**
 * Only One platform products in PartyBox protocol support [EnumUIConnectStatus.BLE_CONNECTED]
 */
fun Device.isBLEAuthControllable(): Boolean {
    val isSupportBLEAuth = supportBLEAuth()
    val hadAuthed = hadAuthed()
    /*Logger.d("uiConnectStatus", "UUID[$UUID] GattConnected[${device.isGattConnected}] " +
            "supportGatt[$isSupportGattControl] hadAuthed[$hadAuthed] isSupportBLEAuth[$isSupportBLEAuth]")
*/
    return isSupportBLEAuth && hadAuthed && !isA2DPConnected && isBLEOnline
}

fun Device.isPartyLightOnline(): Boolean {
    return isPartyLightCategory(pid) && isBLEOnline
}


/**
 * Only WiFi products support [EnumUIConnectStatus.READY_TO_CONNECT]
 */
fun Device?.isReadyToConnect(): Boolean {
    val device = this as? OneDevice ?: return false

    val isSupportGattControl = AppConfigurationUtils.isSupportBleControl(device.pid)
    val isNetworkConnected = device.isNetworkConnected

    return if (isSupportGattControl) {
        !device.isWiFiOnline &&
                device.isBLEOnline &&
                !isNetworkConnected &&
                !device.isA2DPConnected &&
                device.hadAuthed()
    } else {
        !device.isWiFiOnline &&
                device.isBLEOnline &&
                !isNetworkConnected &&
                device.hadAuthed()
    }
}

fun Device?.deviceImgPath(): String? {
    val device = this

    val digPid = device?.pid ?: AppConfigurationUtils.getPidByModelName((device as? OneDevice)?.modelName() ?: "") ?: ""
    val colorID = device?.cid ?: "01"

    return DeviceImageUtil.getBLEDeviceImageByPid(digPid, colorID)
}

fun Device?.deviceAuraCastImgPath(): String? {
    val device = this

    val digPid = device?.pid ?: AppConfigurationUtils.getPidByModelName((device as? OneDevice)?.modelName() ?: "") ?: ""
    val colorID = device?.cid ?: "01"

    return AppConfigurationUtils.getAuraCastModelRenderPath(digPid, colorID)
}

fun Member.deviceImgPath(): String {
    return DeviceImageUtil.getDeviceImgNameByProject(deviceName, colorId)
}

fun Device?.getOOBEAuthImgPath(): String? {
    this ?: return null
    val safePid = pid
    if (safePid.isNullOrBlank()) {
        return null
    }

    val colorID = cid ?: "01"
    return AppConfigurationUtils.getOOBEAuthPath(pid = safePid, colorId = colorID)
}

fun OneDevice.canBeShowInDiagnosisReport(): Boolean {
    return configuredDiagnosisReport && hadAuthed() && (role == OneRole.SINGLE || role == OneRole.GO)
}

fun Activity.portalUrl(url: String) {
    val intent = Intent()
    intent.action = Intent.ACTION_VIEW
    intent.data = Uri.parse(url)
    startActivity(intent)
}

fun Activity.portalUrl(config: String?, default: String) {
    portalUrl(
        if (!config.isNullOrBlank()) {
            config
        } else {
            default
        }
    )
}

fun Fragment.portalUrl(config: String?, default: String) {
    activity?.portalUrl(
        if (!config.isNullOrBlank()) {
            config
        } else {
            default
        }
    )
}

/**
 * # get [GetOtaAccessPointResponse] from device.
 * # get [CheckFWResp] from remote.
 */
@AnyThread
suspend fun OneDevice.getFwCheckRspWithTimeout(logTag: String): CheckFWResp? {
    val device = this
    val otaAccessPoint = withContext(DISPATCHER_DEFAULT) {
        device.syncGetOtaAccessPointWithTimeout(logTag = logTag)
    }

    otaAccessPoint ?: run {
        Logger.w(logTag, "getFwCheckRspWithTimeout() >>> fail to get ota access point")
        return null
    }

    Logger.d(logTag, "getFwCheckRspWithTimeout() >>> ota access point: $otaAccessPoint")

    val checkFwRsp = withContext(DISPATCHER_API) {
        otaAccessPoint.checkFwVersion(tag = logTag, oneDevice = device)
    } ?: run {
        Logger.w(logTag, "getFwCheckRspWithTimeout() >>> fail to get check fw rsp")
        null
    }

    Logger.i(logTag, "getFwCheckRspWithTimeout() >>> ${checkFwRsp?.printf()}")
    return checkFwRsp
}

fun CheckFWResp.printf(): String {
    val rsp = this.result?.getOrNull(0)
    val sb = StringBuilder()

    sb.append("code[").append(rsp?.code).append("]\n")
    sb.append("domain[").append(rsp?.domain).append("]\n")
    sb.append("newOta[").append(rsp?.newOta).append("]\n")
    sb.append("version[").append(rsp?.version).append("]\n")
    sb.append("date[").append(rsp?.releaseDate).append("]\n")
    sb.append("main note[").append(rsp?.releaseNotes).append("]\n")
    sb.append("sub note[").append(rsp?.releaseNotesSubject).append("]\n")

    return sb.toString()
}

private const val BUNDLE_UUID = "UUID"

/**
 * @param intentCallback insert customize extra into intent in this callback if needed.
 */
fun <Clz> Context?.portalActivity(
    target: Class<Clz>,
    device: Device,
    intentCallback: ((Intent) -> Unit)? = null
): Boolean {
    val context = this ?: run {
        Logger.e("portal", "portal() >>> missing context")
        return false
    }

    val uuid = device.UUID
    if (uuid.isNullOrBlank()) {
        Logger.e("portal", "portal() >>> missing uuid")
        return false
    }

    val intent = Intent(context, target)
    intent.putExtra(BUNDLE_UUID, uuid)
    intentCallback?.invoke(intent)
    context.startActivity(intent)
    return true
}

/**
 * @param intentCallback insert customize extra into intent in this callback if needed.
 */
fun <Clz> Activity?.portalActivity(
    target: Class<Clz>,
    device: Device,
    requestCode: Int,
    intentCallback: ((Intent) -> Unit)? = null
): Boolean {
    val context = this ?: run {
        Logger.e("portal", "portal() >>> missing context")
        return false
    }

    val uuid = device.UUID
    if (uuid.isNullOrBlank()) {
        Logger.e("portal", "portal() >>> missing uuid")
        return false
    }

    val intent = Intent(context, target)
    intent.putExtra(BUNDLE_UUID, uuid)
    intentCallback?.invoke(intent)
    context.startActivityForResult(intent, requestCode)
    return true
}

fun Activity.parseAsAnyDevice(productLine: EnumProductLine? = null): Device? {
    val uuid = intent?.getStringExtra(BUNDLE_UUID)
    if (uuid.isNullOrBlank()) {
        Logger.e("portal", "parseAsAnyDevice() >>> fail to parse uuid")
        return null
    }

    return DeviceStore.find(uuid = uuid, productLine = productLine) ?: run {
        Logger.e("portal", "parseAsAnyDevice() >>> can't find target device based on uuid[$uuid]")
        null
    }
}

fun Activity.parseAsOneDevice(): OneDevice? {
    return parseAsAnyDevice(productLine = EnumProductLine.ONE) as? OneDevice
}

fun Activity.parseAsPartyBoxDevice(): PartyBoxDevice? {
    return parseAsAnyDevice(productLine = EnumProductLine.PARTY_BOX) as? PartyBoxDevice
}

fun OneDevice.isSoundBar300(): Boolean {
    return "2071".equals(pid, true)
}

fun OneDevice.isSoundBar500(): Boolean {
    return "2072".equals(pid, true)
}

fun OneDevice.isSoundBar700(): Boolean {
    return "20d1".equals(pid, true)
}

fun OneDevice.isSoundBar800(): Boolean {
    return "2073".equals(pid, true)
}

fun OneDevice.isSoundBar1000(): Boolean {
    return "2074".equals(pid, true)
}

fun OneDevice.isSoundBar1300(): Boolean {
    return "208b".equals(pid, true)
}

fun OneDevice.isEnchant900(): Boolean {
    return "20de".equals(pid, true)
}

fun OneDevice.isEnchant1100(): Boolean {
    return "20df".equals(pid, true)
}
//https://www.figma.com/design/3B7JkmPuWKRYoU60f58OeM/%F0%9F%94%B5-3-in-One_Platform-UIS?node-id=5317-856639&m=dev                                  enchant remote control style
//https://www.figma.com/design/3B7JkmPuWKRYoU60f58OeM/%F0%9F%94%B5-3-in-One_Platform-UIS?node-id=5300-781424&m=dev                                  bar gen 3 remote control style
//https://www.figma.com/design/riw6dTClVZ5ifzFErnKonV/%E2%9A%AB%EF%B8%8F-JBL-Soundbar-Gen4-App-UIS?node-id=3295-112213&p=f&t=SLRPJ5iTRpKnVIx9-0     bar gen 4 remote control style
fun OneDevice.isSoundBarGenOne() = isSoundBar300() || isSoundBar500()

fun OneDevice.isSoundBarGenTwo() = isSoundBar700()

fun OneDevice.isSoundBarGenThree() = isSoundBar800() || isSoundBar1000() || isSoundBar1300() || isOneCommander()

fun OneDevice.isEnchantGenOne() = isEnchant900() || isEnchant1100()

fun OneDevice.isCN(): Boolean = "CN".equals(regionCode, true)

fun OneDevice.isAuthentic300(): Boolean {
    return "20c0".equals(pid, true)
}

fun OneDevice.isAuraStudio5WIFI(): Boolean {
    return "215a".equals(pid, true)
}

fun <T, S1> MediatorLiveData<T>.safeAddSource(
    sourceOne: LiveData<S1?>,
    onChanged: (S1?) -> Unit
) {
    removeSource(sourceOne)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1)
    }
}

fun <T, S1, S2> MediatorLiveData<T>.safeAddSources(
    sourceOne: LiveData<S1?>,
    sourceTwo: LiveData<S2?>,
    onChanged: (S1?, S2?) -> Unit
) {
    removeSource(sourceOne)
    removeSource(sourceTwo)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1, sourceTwo.value)
    }

    addSource(sourceTwo) { s2 ->
        onChanged.invoke(sourceOne.value, s2)
    }
}

fun <T, S1, S2, S3> MediatorLiveData<T>.safeAddSources(
    sourceOne: LiveData<S1?>,
    sourceTwo: LiveData<S2?>,
    sourceThree: LiveData<S3?>,
    onChanged: (S1?, S2?, S3?) -> Unit
) {
    removeSource(sourceOne)
    removeSource(sourceTwo)
    removeSource(sourceThree)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1, sourceTwo.value, sourceThree.value)
    }

    addSource(sourceTwo) { s2 ->
        onChanged.invoke(sourceOne.value, s2, sourceThree.value)
    }

    addSource(sourceThree) { s3 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, s3)
    }
}

fun <T, S1, S2> MediatorLiveData<T>.safeAddNonNullSources(
    sourceOne: LiveData<S1>,
    sourceTwo: LiveData<S2>,
    onChanged: (S1?, S2?) -> Unit
) {
    removeSource(sourceOne)
    removeSource(sourceTwo)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1, sourceTwo.value)
    }

    addSource(sourceTwo) { s2 ->
        onChanged.invoke(sourceOne.value, s2)
    }
}

fun <T, S1, S2, S3> MediatorLiveData<T>.safeAddNonNullSources(
    sourceOne: LiveData<S1>,
    sourceTwo: LiveData<S2>,
    sourceThree: LiveData<S3>,
    onChanged: (S1?, S2?, S3?) -> Unit
) {
    removeSource(sourceOne)
    removeSource(sourceTwo)
    removeSource(sourceThree)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1, sourceTwo.value, sourceThree.value)
    }

    addSource(sourceTwo) { s2 ->
        onChanged.invoke(sourceOne.value, s2, sourceThree.value)
    }

    addSource(sourceThree) { s3 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, s3)
    }
}

fun <T, S1, S2, S3, S4> MediatorLiveData<T>.safeAddSources(
    sourceOne: LiveData<S1?>,
    sourceTwo: LiveData<S2?>,
    sourceThree: LiveData<S3?>,
    sourceFour: LiveData<S4?>,
    onChanged: (S1?, S2?, S3?, S4?) -> Unit
) {
    removeSource(sourceOne)
    removeSource(sourceTwo)
    removeSource(sourceThree)
    removeSource(sourceFour)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1, sourceTwo.value, sourceThree.value, sourceFour.value)
    }

    addSource(sourceTwo) { s2 ->
        onChanged.invoke(sourceOne.value, s2, sourceThree.value, sourceFour.value)
    }

    addSource(sourceThree) { s3 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, s3, sourceFour.value)
    }

    addSource(sourceFour) { s4 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, sourceThree.value, s4)
    }
}

fun <T, S1, S2, S3, S4, S5> MediatorLiveData<T>.safeAddSources(
    sourceOne: LiveData<S1?>,
    sourceTwo: LiveData<S2?>,
    sourceThree: LiveData<S3?>,
    sourceFour: LiveData<S4?>,
    sourceFive: LiveData<S5?>,
    onChanged: (S1?, S2?, S3?, S4?, S5?) -> Unit
) {
    removeSource(sourceOne)
    removeSource(sourceTwo)
    removeSource(sourceThree)
    removeSource(sourceFour)
    removeSource(sourceFive)

    addSource(sourceOne) { s1 ->
        onChanged.invoke(s1, sourceTwo.value, sourceThree.value, sourceFour.value, sourceFive.value)
    }

    addSource(sourceTwo) { s2 ->
        onChanged.invoke(sourceOne.value, s2, sourceThree.value, sourceFour.value, sourceFive.value)
    }

    addSource(sourceThree) { s3 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, s3, sourceFour.value, sourceFive.value)
    }

    addSource(sourceFour) { s4 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, sourceThree.value, s4, sourceFive.value)
    }

    addSource(sourceFive) { s5 ->
        onChanged.invoke(sourceOne.value, sourceTwo.value, sourceThree.value, sourceFour.value, s5)
    }
}

/**
 * For One platform devices:
 * 1. WiFi scanned
 * 2. A2DP connected
 * 3. Been authed:
 *    3.1 Ever WiFi scanned before.
 *    3.2 Ever A2DP connected before.
 *    3.3 Ever ran OOBE flow on this phone before.
 */
fun List<Device>.filterVisibleDevice(): List<Device> {
    val source = this

    return source.filter { device ->
        when (device) {
            is OneDevice -> device.hadAuthed() && AppConfigurationUtils.isSupportedDevice(device.pid)
            is PartyBoxDevice -> device.isUiOnline()
            is PartyLightDevice -> device.isOnline && AppConfigurationUtils.isSupportedDevice(device.pid)
            is PartyBandDevice -> device.isUiOnline()

            else -> false
        }
    }
}

/**
 * For devices which had feature in [ProductFeature.BLEAuthentication], the device will be regarded as
 * online state after doing BLE authentication. (like SS5, AS5, Horizon 3, etc.)
 * For others, keep original judgement: A2DP connected && isConnectable
 */
private fun PartyBoxDevice.isUiOnline(): Boolean {
    val device = this
    if (!AppConfigurationUtils.isSupportedDevice(device.pid)) {
        return false
    }

    if (device.isA2DPConnected) {
        return true
    }

    val supportBLEAuth = device.supportBLEAuth()
    if (supportBLEAuth) {
        return device.isBLEOnline && device.hadAuthed()
    }

    return false
}

private fun PartyBandDevice.isUiOnline(): Boolean {
    val device = this
    if (!AppConfigurationUtils.isSupportedDevice(device.pid)) {
        return false
    }
    val isConnectable = device.miscInfo?.isConnectable ?: false
    val isGattConnected = device.isGattConnected
    return device.hadAuthed() && (isGattConnected || isConnectable)
}

fun Device.hadAuthed() = when (val device = this) {
    is OneDevice -> {
        // Ever WiFi scanned before.
        // Ever A2DP connected before.
        // Ever ran OOBE flow on this phone before.
        when {
            device.isWiFiOnline -> true
            device.isA2DPConnected -> true
            LocalCacheAdapter.fastHadAuthed(uuid = device.UUID) -> true
            else -> false
        }
    }

    is PartyBandDevice -> {
        device.isA2DPConnected || LocalCacheAdapter.fastHadAuthed(uuid = device.UUID)
    }

    else -> LocalCacheAdapter.fastHadAuthed(uuid = device.UUID)
}

fun OneDevice.isWiFiOrBTConnected() = when {
    isWiFiOnline -> true
    isA2DPConnected -> true
    else -> false
}

fun OneDevice.supportDiagnosisReport(): Boolean = (pid?.let {
    AppConfigurationUtils.getModelConfig(it)?.capability?.contains(ProductFeature.DiagnosisReport)
} ?: false) && hadAuthed() && (featSupportExt?.featSupport?.isDiagnosisReportSupport() ?: false)

fun OneDevice.supportRearSpeaker(): Boolean {
    return true == featSupportExt?.featSupport?.rearSpeakerStatus?.isSupport &&
            configSupportRearSpeaker()
}

private fun Device?.configSupportRearSpeaker(): Boolean {
    val device = this ?: return false
    val pid = device.pid
    if (pid.isNullOrBlank()) {
        return false
    }

    return AppConfigurationUtils.getModelConfig(pid)?.capability?.contains(ProductFeature.RearSpeaker) == true
}

fun OneDevice.supportPureVoice(): Boolean {
    return featSupportExt?.featSupport?.pureVoice?.isSupport ?: false
}

fun OneDevice.supportFlexListening(): Boolean {
    return featSupportExt?.featSupport?.flexListening?.isSupport ?: false
}

fun OneDevice.supportDeepSleep(): Boolean {
    return featSupportExt?.featSupport?.deepSleep?.isSupport ?: false
}

fun OneDevice.isAuraCastQualitySupport(): Boolean {
    return featSupportExt?.featSupport?.isAuraCastQualitySupport() ?: false
}

fun OneDevice.supportPersonalListeningMode(): Boolean {
    return featSupportExt?.featSupport?.personalListeningMode?.isSupport ?: false
}

/**
 * Use [pid] as query key cause [modelName] might not ready after device setup WiFi success.
 */
fun OneDevice.supportMultiChanel(): Boolean {
    return HarmancastManager.getInstance().supportMultiChannelByPid(this.pid)
}

fun OneDevice.supportOOBEEncryption(): Boolean {
    return featSupportExt?.featSupport?.OOBEEncryption?.isSupport ?: false
}

inline fun <reified T> JsonObject?.toBean(): T? {
    return this?.let {
        GsonUtil.parseJsonToBean(it.toString(), T::class.java)
    }
}

fun OneDevice.supportBleControl(): Boolean {
    val pid = pid ?: return false
    return AppConfigurationUtils.isSupportBleControl(pid)
}

fun Number.dp() = ConvertUtils.dp2px(this.toFloat())
fun Number.sp() = ConvertUtils.sp2px(this.toFloat())

private val circleUpgrade: Boolean
    get() = DebugConfigKey.circleUpgrade

@MainThread
suspend fun PartyBoxDevice.fetchRemoteUpdateModel(context: Context, logTag: String): RemoteUpdateModel? {
    val device = this
    val pid = device.pid
    val protocol = device.getBLEProtocol()
    if (pid.isNullOrBlank()) {
        Logger.e(logTag, "fetchRemoteUpdateModel() >>> missing pid")
        return null
    }

    Logger.d(logTag, "fetchRemoteUpdateModel() >>> try to fetch firmware version. protocol[$protocol]")
    val firmwareVer = if (!device.firmwareVersion.isNullOrBlank()) {
        device.firmwareVersion
    } else {
        withContext(DISPATCHER_DEFAULT) {
            when (protocol) {
                BluetoothDevice.TRANSPORT_LE -> {
                    device.syncGattConnectWithTimeout(context = context)
                    device.syncGetFirmwareVersionWithTimeout(protocol = protocol)
                }

                BluetoothDevice.TRANSPORT_BREDR -> {
                    device.syncBrEdrConnectWithTimeout(context = context)
                    device.syncGetFirmwareVersionWithTimeout(protocol = protocol)
                }

                else -> {
                    // no impl.
                }
            }
        }

        device.firmwareVersion
    }

    Logger.d(logTag, "fetchOtaStatus() >>> firmware ver.[$firmwareVer]")
    if (firmwareVer.isNullOrBlank()) {
        Logger.w(logTag, "fetchRemoteUpdateModel() >>> missing firmware version")
        return null
    }

    val model = withContext(DISPATCHER_API) {
        executeOtaFetchTask(
            context = context,
            pid = pid,
            firmwareVer = firmwareVer
        )
    }

    model?.let {
        Logger.i(logTag, "fetchRemoteUpdateModel() >>> fetch success. $it")
    } ?: run {
        Logger.w(logTag, "fetchRemoteUpdateModel() >>> fetch fail")
    }

    return model
}

@WorkerThread
private suspend fun executeOtaFetchTask(
    context: Context,
    pid: String,
    firmwareVer: String
): RemoteUpdateModel? = Tools.repeatWithTimeout(0, 30 * 1000L) {
    suspendCancellableCoroutine { continuation ->
        PartyOtaFetchTask(
            pid = pid,
            appVersion = if (!circleUpgrade) BuildConfig.VERSION_NAME else "99.99.99",
            deviceFirmwareVer = firmwareVer,
        ) { model ->
            continuation.safeResume(model)
        }.execute(context = context)
    }
}

/**
 * Either [OneDevice.isLwaSupport] or "AmazonAlexaVA" exists in the model config will be regarded as Alexa VA(LWA) support.
 */
fun OneDevice.supportCalibration(): Boolean {
    val pid = pid
    if (pid.isNullOrBlank()) {
        return false
    }

    return AppConfigurationUtils.getModelConfig(pid)?.capability?.contains(ProductFeature.Calibration) ?: false
}

const val JBL_PORTABLE_PKG_NAME = "com.harman.ble.jbllink"
const val JBL_PARTYBOX_PKG_NAME = "com.jbl.partybox"
const val JBL_BAR_PKG_NAME = "com.wifiaudio.harmanbar"
const val GOOGLE_STORE_PKG_NAME = "com.android.vending"
const val GOOGLE_HOME_PKG_NAME = "com.google.android.apps.chromecast.app"

fun hasInstalledJBLPortable(activity: Activity): Boolean {
    return activity.packageManager.getLaunchIntentForPackage(JBL_PORTABLE_PKG_NAME) != null
}

fun hasInstalledJBLPartyBox(activity: Activity): Boolean {
    return activity.packageManager.getLaunchIntentForPackage(JBL_PORTABLE_PKG_NAME) != null
}

fun hasInstalledJBLBarSetup(activity: Activity): Boolean {
    return activity.packageManager.getLaunchIntentForPackage(JBL_BAR_PKG_NAME) != null
}

fun hasInstalledGoogleHome(activity: Activity): Boolean {
    return activity.packageManager.getLaunchIntentForPackage(GOOGLE_HOME_PKG_NAME) != null
}

fun openJBLPortable(activity: Activity) {
    val pkgName = JBL_PORTABLE_PKG_NAME
    try {
        val intent = activity.packageManager.getLaunchIntentForPackage(pkgName)
        activity.startActivity(intent)
    } catch (ex: Exception) {
        openAppInGoogleStoreAppOrBrowser(pkgName = pkgName, activity = activity, appUrl = BuildConfig.JBL_PORTABLE_APP_URL)
    }
}

fun openJBLPartyBox(activity: Activity) {
    val pkgName = JBL_PARTYBOX_PKG_NAME
    try {
        val intent = activity.packageManager.getLaunchIntentForPackage(pkgName)
        activity.startActivity(intent)
    } catch (ex: Exception) {
        openAppInGoogleStoreAppOrBrowser(pkgName = pkgName, activity = activity, appUrl = BuildConfig.JBL_PARTYBOX_APP_URL)
    }
}

fun openJBLBarSetup(activity: Activity) {
    val pkgName = JBL_BAR_PKG_NAME
    try {
        val intent = activity.packageManager.getLaunchIntentForPackage(pkgName)
        activity.startActivity(intent)
    } catch (ex: Exception) {
        openAppInGoogleStoreAppOrBrowser(pkgName = pkgName, activity = activity, appUrl = BuildConfig.JBL_BAR_SETUP_APP_URL)
    }
}

fun openGoogleHome(activity: Activity) {
    val pkgName = GOOGLE_HOME_PKG_NAME
    try {
        val intent = activity.packageManager.getLaunchIntentForPackage(pkgName)
        activity.startActivity(intent)
    } catch (ex: Exception) {
        openAppInGoogleStoreAppOrBrowser(pkgName = pkgName, activity = activity, appUrl = BuildConfig.GOOGLE_HOME_APP_URL)
    }
}

private fun openAppInGoogleStoreAppOrBrowser(pkgName: String, appUrl: String, activity: Activity) {
    val playStorePackageName = GOOGLE_STORE_PKG_NAME
    try {
        val intent = activity.packageManager.getLaunchIntentForPackage(playStorePackageName)
        if (intent != null) {
            val uri = Uri.parse("https://play.google.com/store/apps/details?id=$pkgName")
            val appStoreIntent = Intent(Intent.ACTION_VIEW, uri)
            appStoreIntent.setPackage(playStorePackageName)
            activity.startActivity(appStoreIntent)
        } else {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(appUrl))
            activity.startActivity(browserIntent)
        }
    } catch (activityNotFound: ActivityNotFoundException) {
        try {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(appUrl))
            activity.startActivity(browserIntent)
        } catch (_: ActivityNotFoundException) {
        }
    }
}

fun Device.supportBLEAuth(): Boolean {
    val pid = pid
    if (pid.isNullOrBlank()) {
        return false
    }

    return AppConfigurationUtils.getModelConfig(pid)?.capability?.contains(ProductFeature.BLEAuthentication) ?: false
}

/**
 * Whether device use BrEdr as default wireless session.
 */
fun Device.isRemoteConfigSupportBrEdr(): Boolean {
    val pid = pid
    if (pid.isNullOrBlank()) {
        return false
    }

    return true == AppConfigurationUtils.getModelConfig(pid)?.capability?.contains(ProductFeature.BrEdr)
}

/**
 * Whether device use BrEdr as default wireless session.
 */
fun Device.isRemoteConfigAutoGattConnect(): Boolean {
    val pid = pid
    if (pid.isNullOrBlank()) {
        return false
    }

    return true == AppConfigurationUtils.getModelConfig(pid)?.capability?.contains(ProductFeature.AutoBLEConnect)
}

/**
 * Whether device's network status in BLE ADV
 */
fun Device.isNetworkConnectedInBleAdv(): Boolean {
    return (this as? OneDevice)?.isNetworkConnected == true
}

fun Device.isOrientationVertical(): Boolean {
    val pid = pid
    if (pid.isNullOrBlank()) {
        return false
    }
    return true == AppConfigurationUtils.getModelConfig(pid)?.orientation?.equals(Orientation.VERTICAL.value)
}

fun Device.getOrientation(): String {
    val pid = pid
    if (pid.isNullOrBlank()) {
        return Orientation.HORIZONTAL.value
    }
    return AppConfigurationUtils.getModelConfig(pid)?.orientation ?: Orientation.HORIZONTAL.value
}

fun Float.dpToPx(): Float =
    this * Resources.getSystem().displayMetrics.density
/**
 * @return [BluetoothDevice.TRANSPORT_LE] or [BluetoothDevice.TRANSPORT_BREDR]
 * @update 2025/3/18
 * remove use BREDR session
 * only with remote config [ProductFeature.BrEdr] support will use BREDR connection
 */
fun Device.getBLEProtocol(): Int {
    return when (this) {
        is PartyBoxDevice -> {
            when {
                // Remote config marked as support [capability?.contains(ProductFeature.BrEdr)]
                isRemoteConfigSupportBrEdr() -> BluetoothDevice.TRANSPORT_BREDR
                // BLE Broadcast marked as support [supportGattOverBrEdr]
//                ableUseBrEdrSession() -> BluetoothDevice.TRANSPORT_BREDR
                else -> BluetoothDevice.TRANSPORT_LE
            }
        }

        else -> BluetoothDevice.TRANSPORT_LE
    }
}

fun Device.gattConnectBlockByAuracast(): Boolean =
    this is PartyBoxDevice &&
            isRemoteConfigAutoGattConnect() &&
            !isConnectable &&
            !isGattConnected &&
            isAuraCastOn

inline fun <reified T : Serializable> T.deepCopy(): T = GsonUtils.fromJson(GsonUtils.toJson(this), T::class.java)

fun Device.shouldBlockGattConnect(): Boolean =
    this is PartyBoxDevice &&
            isRemoteConfigAutoGattConnect() &&
            !isGattConnected && !isConnectable

fun genMediaStoreSignature(imgPath: String, pid: String?, tag: String, isAuraCast: Boolean? = false): MediaStoreSignature {
    val productId = if (pid.isNullOrBlank()) AppConfigurationUtils.getPidByRenderPath(imgPath) else pid
    val version = productId?.let {
        if (true == isAuraCast) {
            AppConfigurationUtils.getAuraCastImageVer(it)
        } else {
            AppConfigurationUtils.getImageVer(it)
        }
    } ?: "no image_version"

    Logger.d(tag, "imgPath: $imgPath, \nproductId: $productId, \nversion: $version")
    return MediaStoreSignature("image/png", ("$imgPath:$version").hashCode().toLong(), 0)
}

fun <T> LiveData<T>.singleObserve(owner: LifecycleOwner, observer: Observer<in T>) {
    val liveData = this
    liveData.removeObservers(owner)
    liveData.observe(owner, observer)
}

fun LifecycleOwner.context(): Context? = when (val owner = this) {
    is ComponentActivity -> owner
    is Fragment -> owner.context
    else -> null
}

fun OneDevice.ableEnterGoogleCastFlow(tag: String): Boolean {
    val device = this

    val isNonePlayable = device.isNonePlayableDevice()
    val supportGoogleVA = device.deviceSupportGoogleVA()
    val supportAlexaVA = device.deviceSupportAlexaVA()
    val isCN = device.isCN()

    Logger.d(tag, "ableEnterGoogleCastFlow() >>> isNonePlayable[$isNonePlayable] " +
            "SupportGoogleVA[$supportGoogleVA] SupportAlexaVA[$supportAlexaVA] isCN[$isCN]")

    return !isNonePlayable && !(supportGoogleVA && supportAlexaVA) && !isCN
}

fun Dialog?.isShowing(): Boolean = true == this?.isShowing