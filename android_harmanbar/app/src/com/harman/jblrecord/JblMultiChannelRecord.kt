package com.harman.jblrecord

import android.content.Context
import androidx.annotation.Keep
import com.harman.log.Logger

/**
 * @ClassName: jbl multi-channel record tools
 * @Description:https://github.com/google/oboe/tree/main/docs （）
 * @Author: xiemingming
 * @Date: 2025/3/25
 */
@Keep
class JblMultiChannelRecord {
    companion object {
        const val TAG = "JblMultiChannelRecord"
        const val JNITAG="JNITAG"
        init {
            System.loadLibrary("jbl_multi_channel_record")
        }

        /*
         *#define ILOG 0
         *#define ELOG 1
         *#define DLOG 2
         *write log to file
         *dont del
         */
        @JvmStatic
        @Keep
        fun jniLog(type: Int, tag: String, message: String) {
            when (type) {
                0 -> {
                    Logger.i(JNITAG, message)
                }

                1 -> {
                    Logger.e(JNITAG, message)
                }

                2 -> {
                    Logger.d(JNITAG, message)
                }

                else -> {
                    Logger.i(JNITAG, message)
                }
            }

        }

    }

    @Keep
    interface AudioCallback {
        fun onAudioReady(audioData: ByteArray)
    }

    external fun init(context: Context, sampleRate: Int, channels: Int, format: Int)
    external fun startRecording(callback: AudioCallback): Int
    external fun stopRecording()
    external fun exitRecord()

}