package com.harman

import com.harman.oobe.wifi.WiFiOOBEDialog

object EventUtils {

    object EventName {

        const val EVENT_DEVICE_MAIN_SCREEN = "event_device_main_screen"

        const val EVENT_OFFLINE_DEVICE_MAIN_SCREEN = "event_offline_device_main_screen"

        const val EVENT_ACTION_OOBE = "event_action_oobe"

        const val EVENT_ACTION = "event_action"

        const val EVENT_CAMPAIGN_BANNER = "event_campaign_banner"
    }

    object Dimension {
        const val DI_MODEL_NAME = "di_model_name"
        const val DI_DEVICE_PID = "di_device_pid"
        const val DI_LAUNCH_SEQ = "di_launch_seq"
        const val DI_FIRMWARE_VERSION = "di_firmware_version"
        const val DI_DEVICE_BT_MAC = "di_device_bt_mac"
        const val DI_DEVICE_WLAN0_MAC = "di_device_wlan0_mac"
        const val DI_DEVICE_UID_SHA256 = "di_device_uid_sha256"
        const val DI_REGION_CODE = "di_region_code"
        const val DI_IS_SINGLE_DEVICE = "di_is_single_device"

        /**
         * @see [EnumDiConnectProtocol]
         */
        const val DI_CONNECT_PROTOCOL = "di_connect_protocol"

        /**
         * @see [EnumDiActionType]
         */
        const val DI_ACTION_TYPE = "di_action_type"

        /**
         * @see [WiFiOOBEDialog.reportOOBEUid]
         */
        const val DI_OOBE_UID = "di_oobe_uid"

        /**
         * @see [EnumOOBEType]
         */
        const val DI_OOBE_TYPE = "di_oobe_type"

        /**
         * @see [EnumOOBEActionItem]
         * @see [EnumMainScreenActionItem]
         */
        const val DI_ACTION_ITEM = "di_action_item"

        const val DI_CONNECT_TIME_DURATION = "di_connect_time_duration"

        /**
         * @see [EnumConnectResult]
         */
        const val DI_CONNECT_RESULT = "di_connect_result"
        const val DI_AUTH_TIME_DURATION = "di_auth_time_duration"

        /**
         * @see [EnumAuthResult]
         */
        const val DI_AUTH_RESULT = "di_auth_result"
        // true or false
        const val DI_WIFI_SETUP_CONNECT_NOTE = "di_wifi_setup_connect_note"
        const val DI_WIFI_SETUP_TIME_DURATION = "di_wifi_setup_time_duration"

        const val DI_COULSON_SETUP_START_TIME = "di_coulson_setup_start_time"
        /**
         * @see [EnumCoulsonEntry]
         */
        const val DI_COULSON_ENTRY = "di_coulson_entry"
        const val DI_LAND_ON_SERVICE = "di_land_on_service"
        const val DI_LAND_ON_SERVICE_VA = "di_land_on_service_va"
        const val DI_ALEXA_CANCEL = "di_alexa_cancel"
        const val DI_GVA_CANCEL = "di_gva_cancel"

        /**
         * @see [EnumVaSetupOption]
         */
        const val DI_VA_SETUP_OPTION = "di_va_setup_option"

        /**
         * @see [EnumVaSetupResult]
         */
        const val DI_VA_SETUP_RESULT = "di_va_setup_result"

        const val DI_GVA_SETUP_START = "di_gva_setup_start"
        const val DI_GVA_SETUP_END = "di_gva_setup_end"
        const val DI_GVA_SETUP_DURATION = "di_gva_setup_duration"
        const val DI_ALEXA_SETUP_START = "di_alexa_setup_start"
        const val DI_ALEXA_SETUP_END = "di_alexa_setup_end"
        const val DI_ALEXA_SETUP_DURATION = "di_alexa_setup_duration"
        const val DI_CAMPAIGN_IDS = "di_campaign_ids"
        const val DI_COULSON_SEQ = "di_coulson_seq"

        enum class EnumDiConnectProtocol(val value: String) {
            BLE("BLE"),
            BR_EDR("EDR"),
            WIFI("WIFI")
        }

        enum class EnumDiActionType(val value: String) {
            OOBE_CONNECTION("action_oobe_connection"),
            OOBE_DEVICE_CONNECT_RESULT("action_oobe_device_connect_result"),
            OOBE_DEVICE_AUTH("action_oobe_device_auth"),
            OOBE_DEVICE_AUTH_RESULT("action_oobe_device_auth_result"),
            OOBE_WIFI_SETUP("action_oobe_wifi_setup"),
            OOBE_SUCCESS("action_oobe_success"),
            ACTION_COULSON_SETUP("action_coulson_setup"),
            ACTION_MAIN_SCREEN("action_main_screen")
        }

        enum class EnumOOBEType(val value: String) {
            AUTO("auto"),
            MANUAL("manual")
        }

        enum class EnumOOBEActionItem(val value: String) {
            LATER("later"),
            CONNECT("connect"),
            EXIT("exit"),
            RETRY("retry"),
            AUTH("auth"),
            CONTINUE("continue"),
            TRY_AGAIN("try again"),
            GET_HELP("get help"),
            CLOSE("close")
        }

        enum class EnumConnectResult(val value: String) {
            SUCCESS("success"),
            FAIL("fail")
        }

        enum class EnumAuthResult(val value: String) {
            SUCCESS("success"),
            FAIL("fail")
        }

        enum class EnumCoulsonEntry(val value: String) {
            OOBE("oobe"),
            POST_OOBE_TOP_BANNER("post_oobe_top_banner"),
            POST_OOBE_BANNER_GVA("post_oobe_banner_gva"),
            POST_OOBE_BANNER_ALEXA("post_oobe_banner_alexa"),
            POST_OOBE_BANNER_GVA_ALEXA("post_oobe_banner_gva_alexa"),
            POST_OOBE_WIFI_STREAMING("post_oobe_wifi_streaming"),
            POST_OOBE_DEVICE_SETTING("post_oobe_device_setting")
        }

        enum class EnumVaSetupOption(val value: String) {
            GVA("gva"),
            ALEXA("alexa"),
            GVA_ALEXA("gva_alexa")
        }

        enum class EnumVaSetupResult(val value: String) {
            SUCCESS_GVA_ALEXA("success_gva_alexa"),
            SUCCESS_GVA("success_gva"),
            SUCCESS_ALEXA("success_alexa"),
            SUCCESS_NONE("success_none")
        }

        enum class EnumVAResult(val value: String) {
            TRUE("true"),
            FALSE("false")
        }

        enum class EnumMainScreenActionItem(val value: String) {
            MUSIC_CONTROL("music_control"),
            IN_APP_MUSIC("in_app_music"),
            EQ("EQ"),
            MOMENT("moment"),
            PRODUCT_SETTINGS("product_settings"), // report by native directly
            PRODUCT_INFO("product_info"), // report by native directly
            WIFI_STREAMING("Wi_Fi_Streaming"),
            VA_SETUP("va_setup"),
            MULTI_ROOM_SETUP("multi_room_setup"),
            GROUPING("grouping"),
            ROON_READY_FREE_TRIAL("roon_ready_free_trial")
        }
    }

    const val CAMPAIGN_CLICK_PREFIX = "campaign_click_"
}