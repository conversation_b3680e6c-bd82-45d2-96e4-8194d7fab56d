package com.harman.webview

import androidx.annotation.Keep
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.harman.command.one.bean.BASS_BOOST_DEEP
import com.harman.command.one.bean.BASS_BOOST_OFF
import com.harman.command.one.bean.BASS_BOOST_PUNCHY
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.Rear
import com.harman.command.one.bean.TypeBassBoostEq
import com.harman.command.one.bean.UltimateLight
import com.harman.command.one.bean.UltimateLightId
import com.harman.command.one.bean.UltimatePattern
import com.harman.command.partylight.PLLightInfo
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.util.Tools.printList
import com.harman.isSoundBar
import com.harman.log.Logger
import com.harman.partylight.util.toRgbList
import com.harman.supportBattery
import com.harman.supportRearSpeaker
import com.harman.v5protocol.bean.devinfofeat.SoloMusicianEnum
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationTrackType
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationTrackVolume
import com.harman.v5protocol.bean.devinfofeat.V5AUXType
import com.harman.v5protocol.bean.devinfofeat.V5ActiveAudioSource
import com.harman.v5protocol.bean.devinfofeat.V5AudioSource
import com.harman.v5protocol.bean.devinfofeat.V5AudioVolume
import com.harman.v5protocol.bean.devinfofeat.V5CHInputType
import com.harman.v5protocol.bean.devinfofeat.V5ChannelInputStatus
import com.harman.v5protocol.bean.devinfofeat.V5ChannelVolume
import com.harman.v5protocol.bean.devinfofeat.V5DeviceOOBE
import com.harman.v5protocol.bean.devinfofeat.V5TrackNumber
import com.harman.webview.models.EnumNetworkCardViewStyle
import java.io.Serializable

/**
 * Created by gerrardzhang on 2024/4/1.
 *
 * Collections of unmarshalled JSON data format used in [CustomJSBridge]
 *
 * e.g.
 * {\"handlerName\":\"customEvent\",\"data\":{\"eventName\":\"playMusic\",\"data\":{}},\"callbackID\":\"cb_9_1711956840310\"}
 */
data class Event(
    @SerializedName("handlerName")
    val handlerName: String?,
    @SerializedName("data")
    val data: JsonObject?,
    @SerializedName("callbackID")
    val callbackID: String?
)

// customEvent: input || output
data class CustomEvent(
    @SerializedName("eventName")
    val eventName: String?,
    @SerializedName("params")
    val data: JsonObject?
) {
    companion object {
        const val KEY = "customEvent"
    }
}

// navigate: navigate to out page, include other package, website, native page
data class NavigateEvent(
    @SerializedName("pageTitle")
    val pageTitle: String?,
    @SerializedName("pageName")
    val pageName: String?,
    @SerializedName("isPush")
    val isPush: String?,
    @SerializedName("params")
    val data: JsonObject?
) {
    companion object {
        const val KEY = "navigateTo"
    }
}

/*
 * logger
 * Key	    Value	comment
 * level	int	    0: debug, 1: info, 2: warning, 3: error
 * message	String	The object is defined in the input field below
 */
data class LoggerEvent(
    @SerializedName("level")
    val level: Int? = null,
    @SerializedName("message")
    val message: String? = null
) {
    companion object {
        const val KEY = "logger"
    }
}

// getEQ
data class GetEQ(
    @SerializedName("isEnable")
    val isEnable: Boolean? = null,
    @SerializedName("currentEQId")
    val currentEQId: String? = null,
    @SerializedName("customEQId")
    val customEQId: String? = null,
    @SerializedName("eqList")
    val eqList: List<EQItem>? = null,
    //bandbox use,other device ignore this filed
    @SerializedName("channelId")
    val channelId: Int? = null,
)

data class EQItem(
    @SerializedName("eqName")
    val eqName: String? = null,
    @SerializedName("eqId")
    val eqId: String? = null,
    @SerializedName("fs")
    val fs: List<Int>? = null,
    @SerializedName("gain")
    val gain: List<Double>? = null,
    @SerializedName("max")
    val max: Double? = null,
    @SerializedName("min")
    val min: Double? = null
)

// setEQ
data class SetEQ(
    @SerializedName("eqId")
    val eqId: String? = null,
    @SerializedName("eqName")
    val eqName: String? = null,
    @SerializedName("fs")
    val fs: List<Double>? = null,
    @SerializedName("gain")
    val gain: List<Double>? = null,
    @SerializedName("max")
    val max: Double? = null,
    @SerializedName("min")
    val min: Double? = null,
    //bandbox use,other device ignore this filed
    @SerializedName("channelId")
    val channelId: Int? = null,
)

/*
 *  | Key    | Value  |    comment           |
 *  |--------|--------|---------------|
 *  |musicSource     | Int    |  0:'none' </br> 1:'wi-fi' </br> 2: 'bt' </br>3:'usb' </br>4:'hdmi' </br>5:'tv' </br>6:'aux'|
 *  |momentType      | Int    |  0:'none' </br> 1: 'forest' </br>2: 'rain' </br>3: 'ocean' </br>4: 'cityWalk' |
 *  |imageSrc        | String |   base64 string   |
 *  |status          | Int    |  0:'no music' </br>1: 'play' </br>2: 'pause' |
 *  |musicName       | String |    |
 *  |artisName       | String |    |
 *  |totalTime       | Int    |   Total song playing time. unit: second  |
 *  |playingTime     | Int    |   Song playing time. unit: second  |
 *  |volume          | Int    |  spk volume  0 ~ 100   |
*/

data class PlayInfo(
    @SerializedName("musicSource")
    val musicSource: Int? = null,
    @SerializedName("momentType")
    val momentType: Int? = null,
    @SerializedName("imageSrc")
    var imageSrc: String? = null,
    @SerializedName("status")
    val status: Int? = null,
    @SerializedName("musicName")
    val musicName: String? = null,
    @SerializedName("artisName")
    val artistName: String? = null,
    @SerializedName("totalTime")
    val totalTime: Int? = null,
    @SerializedName("playingTime")
    val playingTime: Int? = null,
    @SerializedName("volume")
    val volume: Int? = null,
    @Transient
    val albumCoverUrl: String? = null // used only for native side
) {

    override fun equals(other: Any?): Boolean {
        if (other !is PlayInfo) {
            return false
        }

        return this.musicSource == other.musicSource &&
                this.momentType == other.momentType &&
                this.status == other.status &&
                this.musicName == other.musicName &&
                this.artistName == other.artistName &&
                this.totalTime == other.totalTime &&
                this.playingTime == other.playingTime &&
                this.volume == other.volume &&
                this.albumCoverUrl == other.albumCoverUrl
    }

    override fun hashCode(): Int {
        var result = musicSource.hashCode()
        result = 31 * result + momentType.hashCode()
        result = 31 * result + status.hashCode()
        result = 31 * result + musicName.hashCode()
        result = 31 * result + artistName.hashCode()
        result = 31 * result + totalTime.hashCode()
        result = 31 * result + playingTime.hashCode()
        result = 31 * result + volume.hashCode()
        result = 31 * result + albumCoverUrl.hashCode()
        return result
    }

    override fun toString(): String {
        return StringBuilder()
            .append("musicSource[").append(musicSource).append("]\n")
            .append("momentType[").append(momentType).append("]\n")
            //.append("imageSrc[").append(imageSrc).append("]\n")
            .append("status[").append(status).append("]\n")
            .append("musicName[").append(musicName).append("]\n")
            .append("artisName[").append(artistName).append("]\n")
            .append("totalTime[").append(totalTime).append("]\n")
            .append("playingTime[").append(playingTime).append("]\n")
            .append("volume[").append(volume).append("]\n")
            .append("albumCoverUrl[").append(albumCoverUrl).append("]\n")
            .toString()
    }
}

class PopPage {
    companion object {
        const val KEY = "popPage"
    }
}

/*
 * OTAStatus
 * Key	Value	comment
 * available	Bool
 */
data class OTAStatus(
    @SerializedName("available")
    val available: Boolean? = null
) {

    override fun toString(): String {
        return "available[$available]"
    }
}

data class RoonReadyStatus(
    @SerializedName("available")
    val available: Boolean? = null,
    @SerializedName("roonReadyCode")
    val roonReadyCode: String? = null,

    ) {

    override fun toString(): String {
        return "available[$available],roonReadyCode[$roonReadyCode]"
    }
}

/*
 *   | Key    | Value  |       isRequire        |       comment        |
 *   |--------|--------|---------------|---------------|
 *   |status| Int| true |device status
 *   </br>1: 'connected'
 *   </br>2: 'disconnected'
 *   </br>3: 'connecting'
 *   </br>4: 'ready to connect'|
 *   |groupType| Int | true | 0: 'single' 1: 'tws' 2: 'party' 3: 'stereo'  4: 'multi_channel' |
 *   |pId| String| true| |
 *   |cId| String | true| color Id |
 *   |deviceName| String | true |  |
 *   |modelName| String | true |  |
 *   |macAddr|String| true |  |
 *   |fwVersion| String | true | software version |
 *   |serialNumber| String | true | serial number |
 *   |channel|Int| true| 0: none 1:left  2: right | |
 *   |groupName| String |false | |
 *   |dolbySupport| Bool | false |||
 *   |supportFeatures| [String] | false| define see  Features |
 *   |googleCastEnabled| Bool | true|  |
 *   |googleCastForVA| Bool | false|  |
 */
data class DeviceInfo(
    /**
     * @link [EnumDeviceInfoStatus]
     */
    @SerializedName("status")
    val status: Int? = null,
    @SerializedName("groupType")
    val groupType: Int? = null,
    @SerializedName("pId")
    val pId: String? = null,
    @SerializedName("cId")
    val cId: String? = null,
    @SerializedName("deviceName")
    var deviceName: String? = null,
    @SerializedName("modelName")
    val modelName: String? = null,
    @SerializedName("macAddr")
    val macAddr: String? = null,
    @SerializedName("regionCode")
    val regionCode: String? = null,
    @SerializedName("oneOSVersion")
    val oneOsVer: String? = null,
    @SerializedName("fwVersion")
    val fwVersion: String? = null,
    @SerializedName("serialNumber")
    var serialNumber: String? = null,
    @SerializedName("channel")
    val channel: Int? = null,
    @SerializedName("groupName")
    val groupName: String? = null,
    @SerializedName("subDeviceList")
    val features: List<String>? = null,
    @SerializedName("batteryInfos")
    var batteryInfos: List<BatteryInfo>? = null,
    @SerializedName("supportFeatures")
    val supportFeatures: List<String>? = null,
    @SerializedName("dolbySupport")
    val dolbySupport: Boolean = false,
    @SerializedName("bleControlSupport")
    val bleControlSupport: Boolean = false,
    @SerializedName("googleCastEnabled")
    val googleCastEnabled: Boolean = false,
    @SerializedName("googleCastForVA")
    val googleCastForVA: Boolean = false,
    @SerializedName("isFeedbackToneOn")
    val isFeedbackToneOn: Boolean = false,
    @SerializedName("orientation")
    val orientation: String? = null,
)

/*
 * Key	        Value	isRequire	comment
 * status	    Int	    true	    device status 1: 'connected' 2: 'disconnected' 3: 'connecting'
 * pId	        String	true
 * cId	        String	true	    color Id
 * deviceName	String	true
 * modelName	String	true
 * macAddr	    String	true
 * fwVersion	String	true	    software version
 * serialNumber	String	true	    serial number
 * channel	    Int	    true	    0: none 1:left 2: right
 */
data class SecondaryDeviceInfo(
    @SerializedName("status")
    val status: Int? = null,
    @SerializedName("pId")
    val pId: String? = null,
    @SerializedName("cId")
    val cId: String? = null,
    @SerializedName("deviceName")
    val deviceName: String? = null,
    @SerializedName("modelName")
    val modelName: String? = null,
    @SerializedName("macAddr")
    val macAddr: String? = null,
    @SerializedName("oneOSVersion")
    val oneOSVersion: String? = null,
    @SerializedName("serialNumber")
    val serialNumber: String? = null,
    @SerializedName("fwVersion")
    val fwVersion: String? = null,
    @SerializedName("channel")
    val channel: Int? = null,
    @SerializedName("batteryInfos")
    val batteryInfos: List<BatteryInfo>? = emptyList(),
)

data class SecondaryDeviceInfos(
    @SerializedName("deviceInfos")
    val deviceInfos: List<SecondaryDeviceInfo>? = null
)

/*
 * | Key    | Value  |       comment        |
 * |--------|--------|---------------|
 * |isCharge| Bool | require |
 * |acCable| Bool | option |
 * |value| Int |require |
 */
data class BatteryInfo(
    @SerializedName("isCharge")
    val isCharge: Boolean? = null,
    @SerializedName("acCable")
    val acCable: Boolean? = null,
    @SerializedName("value")
    val value: Int? = null
) {

    override fun toString(): String {
        return "isCharge[$isCharge] acCable[$acCable] value[$value]"
    }
}

data class NearbyProductList(
    @SerializedName("nearbyList")
    val nearbyList: List<NearbyProduct>
)

/*
 * Key	    Value	comment
 * id	    String	require, unique value
 * pid	    String	require, decimal value
 * name	    String	option, product name
 * vid	    String	option
 * btMACCrc	String	option
 */
data class NearbyProduct(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("pid")
    val pid: String? = null,
    @SerializedName("name")
    val productName: String? = null,
    @SerializedName("vid")
    val vid: String? = null,
    @SerializedName("btMACCrc")
    val btMACCrc: String? = null,
    @SerializedName("imgSrc")
    val imageSrc: String? = null
) {
    override fun toString(): String = StringBuilder().apply {
        append("id[$id]\n")
        append("pid[$pid]\n")
        append("name[$productName]\n")
        append("vid[$vid]\n")
        append("btMACCrc[$btMACCrc]")
    }.toString()
}

/*
 * > Enter music service manager when the input is empty
 * input
 *     | Key    | Value  |       comment        |
 *     |--------|--------|---------------|
 *     |id      |String  | Music Service id|  |
 */
data class EnterMusicService(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("image")
    val image: String? = null,
    @SerializedName("desc")
    val desc: String? = null,
    @SerializedName("supportDolby")
    val supportDolby: Boolean? = null
)

/**
 * >  music service support list
 * output
 *     | Key    | Value  |       comment        |
 *     |--------|--------|---------------|
 *     |ids     |[String]| Music Service id|  id menu: 'Amazon', 'iHeartRadio', 'Qobuz', 'TIDAL', 'TuneIn', 'vTuner', 'SoundMachine', 'CalmRadio', 'Napster'  |
 */
data class MusicServiceList(
    @SerializedName("ids")
    val ids: List<String>? = null
)

/**
 * >  product support list
 * output
 *     | Key    | Value  |       comment        |
 *     |--------|--------|---------------|
 *     |pIds     |[String]| Music Service id|
 */
data class ProductSupportList(
    @SerializedName("pIds")
    val ids: List<String>? = null
)

/**
 * >  To retrieve product image
 * output
 *     | Key    | Value  |       comment        |
 *     |--------|--------|---------------|
 *     |pId     |[String]|   product ID |
 *     |cId     |[String]|   color ID   |
 */
data class ProductBaseInfo(
    @SerializedName("pId")
    val pid: String? = null,
    @SerializedName("cId")
    val colorId: String? = null,
) : Serializable {
    override fun toString(): String {
        return "ProductBaseInfo(pid=$pid, colorId=$colorId)"
    }
}

data class ProductRenderImage(
    @SerializedName("base64String")
    val base64String: String? = null
) : Serializable

/**
 * > networkInfo
 *
 * input
 * | Key | Value  | Comment |
 * |--------|--------|---------------|
 * | viewStyle | Int | Whether show setup network card and which style should be displayed. `0`: not displayed; `1` never setup before; `2` Network not connected.
 * | essid | String | encrpted SSID name displayed when `viewStyle` i
 *
 * @param viewStyle link [EnumNetworkCardViewStyle]
 */
data class NetworkInfo(
    @SerializedName("viewStyle")
    val viewStyle: Int? = null,
    @SerializedName("essid")
    val essid: String? = null
)

/**
| Key    | Value  |    comment           |
|--------|--------|---------------|
|appName  | String    |    JBLPortable, JBLPartybox, GoogleHome, JBLBarSetup  |
 */
data class RedirectionAppInfo(
    @SerializedName("appName")
    val appName: String? = null
)

/**
 * ### 1.47 getCoulsonStatus

 * input

null

 * output

| Key    | Value  |    comment           |
|--------|--------|---------------|
|status  | Int    |    0. none set, 1. only setup google, 2. only setup alexa  3. all setup  |
 */
data class GetCoulsonStatus(
    @SerializedName("status")
    val status: Int? = null
)

data class JSBassboostInfo(
    @SerializedName("selectedIndex")
    val selectedIndex: Int?,
    @SerializedName("isOn")
    val isOn: Boolean,
) {
    fun toGeneralConfig(): GeneralConfig {
        return GeneralConfig(
            TypeBassBoostEq,
            if (!isOn)
                BASS_BOOST_OFF
            else if (selectedIndex == 0)
                BASS_BOOST_DEEP
            else if (selectedIndex == 1)
                BASS_BOOST_PUNCHY
            else BASS_BOOST_DEEP
        )
    }
}

data class JsBatteryInfo(
    @SerializedName("batteryList")
    val batteryList: List<BatteryInfo>,
) {
    companion object {

        fun fromOneDevice(dev: OneDevice): List<BatteryInfo>? {
            if (dev.isSoundBar()) {
                Logger.d(
                    "Event",
                    "fromOneDevice() >>> bar device[${dev.UUID}] pid[${dev.pid}] support rear speaker[${dev.supportRearSpeaker()}]"
                )
                return if (dev.supportRearSpeaker()) {
                    buildSodSupportedInfo(device = dev)
                } else {
                    // regard as no rear spk supported
                    emptyList()
                }
            }

            if (!dev.supportBattery()) {
                Logger.d(
                    "Event",
                    "fromOneDevice() >>> device[${dev.UUID}] pid[${dev.pid}] didn't support battery"
                )
                return emptyList()
            }

            Logger.d(
                "Event", "fromOneDevice() >>> device[${dev.UUID}] pid[${dev.pid}] " +
                        "isCharging[${dev.isCharging}] batteryLevel[${dev.batteryLevel}]"
            )
            return listOf(BatteryInfo(dev.isCharging, true, dev.batteryLevel))
        }

        private fun buildSodSupportedInfo(device: OneDevice): List<BatteryInfo> {
            val batteryInfos = mutableListOf<BatteryInfo>()
            Logger.d(
                "Event",
                "buildSodSupportedInfo() >>> ${device.wifiDevice?.rears?.printList()}"
            )

            val left = device.wifiDevice?.rears?.firstOrNull { rear ->
                rear.isLeft() && rear.isOnline() && !rear.docked
            }

            batteryInfos.add(left?.toBatteryInfo() ?: BatteryInfo())

            val right = device.wifiDevice?.rears?.firstOrNull { rear ->
                rear.isRight() && rear.isOnline() && !rear.docked
            }

            batteryInfos.add(right?.toBatteryInfo() ?: BatteryInfo())
            return batteryInfos.toList()
        }

        private fun Rear.toBatteryInfo(): BatteryInfo =
            BatteryInfo(charging, true, if (charging) null else capicity)
    }
}

data class WhatsNewStatus(
    @SerializedName("pId")
    val pId: String? = null,
    @SerializedName("cId")
    val cId: String? = null,
    @SerializedName("oneOSVer")
    val oneOSVer: String? = null,
    @SerializedName("preOneOSVer")
    val preOneOSVer: String? = null,
    @SerializedName("modelName")
    val modelName: String? = null
)

data class MomentStatus(
    @SerializedName("status")
    val momentName: String? = null
) {
    override fun toString(): String {
        return "status[$momentName]"
    }
}

data class JsSoundbarFeature(
    @SerializedName("audioSync")
    val audioSync: JsAudioSync? = null,
    @SerializedName("rearSpeaker")
    val rearSpeaker: JsRearSpeaker? = null,
    @SerializedName("supportPersonalListeningMode")
    val supportPersonalListeningMode: Boolean? = null,
    @SerializedName("supportMultiChannel")
    val supportMultiChannel: Boolean? = null,
    @SerializedName("supportSoundTuning")
    val supportSoundTuning: Boolean? = null,
    @SerializedName("supportBroadcasting")
    val supportBroadcasting: Boolean? = null,
    @SerializedName("pureVoiceStatus")
    val pureVoiceStatus: Boolean? = null
) {

    data class JsAudioSync(
        @SerializedName("level")
        val level: Int? = null,
        @SerializedName("enable")
        val enable: Boolean? = null,
    ) {
        override fun toString(): String {
            return "JsAudioSync(level=$level, enable=$enable)"
        }
    }

    data class JsRearSpeaker(
        @SerializedName("level")
        val level: Int? = null,
        @SerializedName("enable")
        val enable: Boolean? = null,
    ) {
        override fun toString(): String {
            return "JsRearSpeaker(level=$level, enable=$enable)"
        }
    }

    data class SetBean(
        @SerializedName("audioSync")
        val audioSync: Int? = null,
        @SerializedName("rearSpeakerLevel")
        val rearSpeakerLevel: Int? = null,
        @SerializedName("pureVoiceStatus")
        val pureVoiceStatus: Boolean? = null
    ) {
        override fun toString(): String {
            return "SetBean(audioSync=$audioSync, rearSpeakerLevel=$rearSpeakerLevel, pureVoiceStatus=$pureVoiceStatus)"
        }
    }

    override fun toString(): String {
        return "JsSoundbarFeature(audioSync=$audioSync, rearSpeaker=$rearSpeaker, supportPersonalListeningMode=$supportPersonalListeningMode, pureVoiceStatus=$pureVoiceStatus)"
    }
}

data class FlexListeningFeature(
    @SerializedName("enable")
    val enabled: Boolean? = null,
    @SerializedName("outputType")
    val outputType: Int? = null
)

/**
 * This is the data model of the device lighting information passed to JS, applicable devices including 'Ultimate','PartyLight' and so on...
 */
data class JsLightInfo(
    @SerializedName("enable")
    val enable: Boolean? = null,
    @SerializedName("patternId")
    val patternId: String? = null,
    @SerializedName("color")
    val color: List<Int>? = null,
    @SerializedName("supportPatternIds")
    val supportPatternIds: List<String>? = null,
    @SerializedName("disablePatternIds")
    val disablePatternIds: List<String>? = null,
    @SerializedName("lightTokens")
    val lightTokens: List<JsLightToken>? = null,
    @SerializedName("lightToken")
    val lightToken: JsLightToken? = null,
) {
    data class JsLightToken(
        @SerializedName("id")
        val id: Int? = null,
        @SerializedName("state")
        val state: Boolean? = null,
    )

    /**
     * all the PatterIDs enumerated in JS,If a product is not supported, the corresponding parameter will be null
     * don't modify each name unless h5 modify !!!
     */
    enum class JsPatternId(
        val key: String,
        val plPatter: PLLightInfo.Pattern? = null,
        val uPatter: UltimatePattern? = null,
    ) {
        neon(key = "neon", PLLightInfo.Pattern.NEON, UltimatePattern.NEON),
        loop(key = "loop", PLLightInfo.Pattern.LOOP, UltimatePattern.LOOP),
        bounce(key = "bounce", PLLightInfo.Pattern.BOUNCE, UltimatePattern.BOUNCE),
        trim(key = "trim", PLLightInfo.Pattern.TRIM, UltimatePattern.TRIM),
        switch(key = "switch", PLLightInfo.Pattern.SWITCH, UltimatePattern.SWITCH),
        freeze(key = "freeze", PLLightInfo.Pattern.FREEZE, UltimatePattern.FREEZE),

        //ultimate not support custom
        custom(key = "custom", PLLightInfo.Pattern.RANDOM);

        companion object {

            fun mapByKey(key: String?): JsPatternId? = entries.find { entry ->
                entry.key.equals(key, true)
            }
        }
    }

    /**
     * all the [JsLightToken]'s id enumerated in JS,If a product is not supported, the corresponding parameter will be null
     * @param uLightTokenId The ID of the item supported by Ultimate in all [JsLightToken] defined by JS
     */
    enum class JsLightTokenId(
        val id: Int,
        val uLightTokenId: UltimateLightId? = null,
    ) {
        ID1(1, UltimateLightId.Figure8LedSwitch),
        ID2(2, UltimateLightId.Figure8OutlineLEDSwitch),
        ID3(3, UltimateLightId.EdgeLedSwitch),
        ID4(4, UltimateLightId.StrobeLedSwitch),
        ID5(5, UltimateLightId.SideLedSwitch),
        ID6(6, UltimateLightId.ProjectionLightSwitch),
        ID7(7, UltimateLightId.StarLedSwitch),
        ID8(8),
        ID9(9),
        ID10(10);

        companion object {
            fun fromUltimateLightId(id: UltimateLightId) = entries.find { it.uLightTokenId == id }

            fun fromId(id: Int) = entries.find { it.id == id }
        }
    }

    companion object {
        /**
         * gen by [PLLightInfo]
         */
        fun fromPLLightInfo(info: PLLightInfo): JsLightInfo = JsLightInfo(
            patternId = JsPatternId.entries.find { it.plPatter == info.pattern }?.name,
            color = if (info.patternLooping == PLLightInfo.PatternLooping.COLOR_LOOP) listOf<Int>() else info.currentColor?.toRgbList(),
            enable = true,
            supportPatternIds = JsPatternId.entries.filter { null != it.plPatter }.map { it.name },
            disablePatternIds = listOf(),
        )

        /**
         * gen by [UltimateLight] and [ColorPicker]
         */
        fun fromUltimateLight(p: UltimateLight? = null, p2: ColorPicker? = null) = JsLightInfo(
            enable = p?.isOn(),
            patternId = JsPatternId.entries.find { it.uPatter == p?.pattern }?.name,
            supportPatternIds = JsPatternId.entries.filter { null != it.uPatter }.map { it.name },
            disablePatternIds = listOf(),
            lightTokens = p?.allLightSwitch?.map {
                JsLightToken(JsLightTokenId.fromUltimateLightId(it.key)!!.id, it.value == 1)
            },
            color = if (p2?.loop == 1) listOf<Int>() else if (p2?.loop == 0) p2.getColor()
                .toRgbList() else null
        )
    }

    fun List<Int>.toColorPicker(): ColorPicker? {
        return if (isEmpty()) {
            ColorPicker(1)
        } else if (size == 3) {
            ColorPicker(0, get(0), get(1), get(2))
        } else {
            null
        }
    }
}

data class SwitchFeedbackToneReq(
    @SerializedName("isOn")
    val isOn: Boolean? = null
)

data class DiscoverModel(
    @SerializedName("pId")
    val pid: String? = null,
    @SerializedName("timeout")
    val timeout: Long? = null,
    @SerializedName("discovered")
    val discovered: Boolean? = null
)

data class JSHKStudioLightInfo(
    @SerializedName("enable") val enable: Boolean? = null,
    @SerializedName("activePattern") val activePattern: JSHKPattern? = null,
    @SerializedName("supportPatterns") val supportPatterns: MutableList<JSHKPattern>? = null,
    @SerializedName("brightness") val brightness: Int? = null,
    @SerializedName("dynamicLevel") val dynamicLevel: Int? = null,
    @SerializedName("moodStatus") val moodStatus: Int? = null,
    @SerializedName("supportPatternColorReset") val supportPatternColorReset: Boolean? = null,
    @SerializedName("resetPatternColorId") val resetPatternColorId: String? = null,
    @SerializedName("projectionLightingStatus") val projectionLightingStatus: Boolean? = null,
) {
    companion object {
        fun mock() = JSHKStudioLightInfo(
            enable = true,
            activePattern = JSHKPattern(id = PatternName.Aurora.patt, level = 80),
            brightness = 100,
            dynamicLevel = 1,
//            lightElement = 1
        )
    }
}

data class JSHKPattern(
    @SerializedName("id") val id: String? = null,
    @SerializedName("level") val level: Int? = 0
)

enum class JSMoodStatus(val status: Int) {
    None(status = 0),

    Disable(status = 1),

    Enable(status = 2);

    companion object {
        fun getStatusBy(s: Int) = JSMoodStatus.values().find { it.status == s } ?: None
    }
}

enum class JSLightElement(val element: Int) {
    All(element = 1),

    Subwoofer(element = 2),

    Satellite(element = 3),

    Disc(element = 4),

    Sculpture(element = 5);

    companion object {
        fun getElementBy(element: Int) =
            JSLightElement.values().find { it.element == element } ?: Subwoofer
    }
}

enum class JSDynamicLevel(val level: Int) {
    Low(level = 1),

    Mid(level = 2),

    High(level = 3);

    companion object {
        fun getLevelBy(l: Int) = JSDynamicLevel.values().find { it.level == l } ?: Mid
    }
}

enum class PatternName(val patt: String) {
    Ocean(patt = "ocean"),
    Aurora(patt = "aurora"),
    Blossom(patt = "blossom"),
    Sunrise(patt = "sunrise"),
    Fireplace(patt = "fireplace"),
    Static("static");

    companion object {
        fun getPatternNameBy(patt: String) =
            PatternName.values().find { it.patt == patt } ?: PatternName.Ocean
    }
}

private enum class LightElement(val value: Int?) {
    All(value = 1),
    Subwoofer(value = 2),
    Satellite(value = 3),
    Disc(value = 4),
    Sculpture(value = 5);

    companion object {
        fun getLightElementBy(value: Int? = 0) =
            LightElement.values().find { it.value == value } ?: All
    }
}


@Keep
data class JsBandBoxFunction(val functionId: String?) {
    companion object {
        const val ID_TUNER = "tuner"
        const val ID_DRUM = "drum"
        const val ID_LOOPER = "looper"
        const val ID_METRONOME = "metronome"
        const val ID_SCALE_PRACTICE = "scale_practice"
        const val ID_OUTPUT = "output"
        const val ID_TONE = "tone"
        const val ID_ON_PRODUCT_PRESSET = "on_product_presset"
    }
}

@Keep
data class JsAIStemSeparation(
    var enable: Boolean? = null,
    var musicSource: Int? = null,
    var disableReasonText: String? = null,
    var karaokeEnable: Boolean? = null,
    var track1: Track? = null,
    var track2: Track? = null,
    var track3: Track? = null,
    var pitchValue: Int? = null,
) : Serializable {

    fun updateTracksWithTrackType(trackType: V5AIStemSeparationTrackType) {
        val type = when (trackType.trackType) {
            V5AIStemSeparationTrackType.Type.Nothing -> TYPE_OFF
            V5AIStemSeparationTrackType.Type.Guitar -> TYPE_GUITAR
            V5AIStemSeparationTrackType.Type.Vocal -> TYPE_VOCAL
            V5AIStemSeparationTrackType.Type.Drum -> TYPE_DRUM
        }
        when (trackType.trackNumber) {
            V5TrackNumber.Track1 -> track1?.type = type
            V5TrackNumber.Track2 -> track2?.type = type
            V5TrackNumber.Track3 -> Unit
        }
    }

    fun updateTracksWithTrackVolume(volume: V5AIStemSeparationTrackVolume) {
        when (volume.trackNumber) {
            V5TrackNumber.Track1 -> track1?.value = volume.volume
            V5TrackNumber.Track2 -> track2?.value = volume.volume
            V5TrackNumber.Track3 -> track3?.value = volume.volume
        }
    }


    companion object {
        const val TYPE_OTHER = 0
        const val TYPE_GUITAR = 1
        const val TYPE_VOCAL = 2
        const val TYPE_DRUM = 3
        const val TYPE_OFF = 4

        const val SRC_BT = 1
        const val SRC_USB_AUDIO = 2
        const val SRC_USB_DRIVE = 3
        const val SRC_AUX = 4


        fun default() = JsAIStemSeparation(
            enable = false,
            musicSource = SRC_BT,
            disableReasonText = null,
            karaokeEnable = false,
            track1 = Track(TYPE_GUITAR, 0),
            track2 = Track(TYPE_VOCAL, 0),
            track3 = Track(TYPE_OTHER, 0),
            pitchValue = null,
        )
    }

    @Keep
    data class Track(var type: Int? = null, var value: Int? = null) : Serializable
}

@Keep
data class JsAISetStemSeparation(
    val enable: Boolean? = null,
    val karaokeEnable: Boolean? = null,
    val track1Type: Int? = null,
    val track1Value: Int? = null,
    val track2Type: Int? = null,
    val track2Value: Int? = null,
    val track3Type: Int? = null,
    val track3Value: Int? = null,
) {
    fun toV5TrackType(trackType: Int?): V5AIStemSeparationTrackType.Type? {
        return when (trackType) {
            JsAIStemSeparation.TYPE_GUITAR -> V5AIStemSeparationTrackType.Type.Guitar
            JsAIStemSeparation.TYPE_DRUM -> V5AIStemSeparationTrackType.Type.Drum
            JsAIStemSeparation.TYPE_VOCAL -> V5AIStemSeparationTrackType.Type.Vocal
            JsAIStemSeparation.TYPE_OFF -> V5AIStemSeparationTrackType.Type.Nothing
            else -> null
        }
    }
}

@Keep
data class JsBandBoxModelInfo(
    val supportModels: List<Int>? = null,
    var activeModel: Int? = null,
    var activeChannels: List<ChannelInfo>? = null,
    //solo only
    var channelDisableType: Int? = null,
    //trio only
    var auxType: Int? = null,
    //trio only
    var masterVolumn: Int? = null,
    val supportStereo: Boolean? = null,
    var supportOnProductPreset: Boolean? = null,
    var pitchValue: Int? = null,
) {
    @Keep
    data class ChannelInfo(
        val id: Int,
        var type: Int = CI_TYPE_GUITAR,
        var name: String = "",
        var enable: Boolean = false,
        var value: Int = 0,
    )

    companion object {
        const val MODEL_MIXER_CONTROL = 1
        const val MODEL_GUITARIST = 2
        const val MODEL_SINGING = 3
        const val MODEL_SINGER = 4

        const val CDT_35MM_UNPLUGIN = 1

        const val AT_MUSIC = 1
        const val AT_PIANO = 2

        const val CI_TYPE_GUITAR = 1
        const val CI_TYPE_BLUETOOTH = 2
        const val CI_TYPE_MIC = 3
        const val CI_TYPE_MUSIC = 4
        const val CI_TYPE_PIANO = 5
        const val CI_TYPE_SOLO_BUILT_IN_MIC = 6
        const val CI_TYPE_SOLO_GUITAR = 7

        //defined by native side and h5 is responsible for transparent transmission
        const val CI_ID_S_CH1 = 0
        const val CI_ID_S_BUILT_IN_MIC = 1
        const val CI_ID_S_MUSIC = 2
        const val CI_ID_T_CH1 = 3
        const val CI_ID_T_CH2 = 4
        const val CI_ID_T_CH3 = 5
        const val CI_ID_T_CH4_INSTRUMENT = 6
        const val CI_ID_T_MUSIC = 7


        fun default(dev: PartyBandDevice): JsBandBoxModelInfo {
            return JsBandBoxModelInfo(
                if (dev.isTrio()) listOf(MODEL_MIXER_CONTROL) else listOf(
                    MODEL_GUITARIST,
                    MODEL_SINGING,
                    MODEL_SINGER
                ),
                if (dev.isTrio()) MODEL_MIXER_CONTROL else MODEL_GUITARIST,
                if (dev.isSolo()) {
                    listOf(
                        ChannelInfo(CI_ID_S_CH1, type = CI_TYPE_SOLO_GUITAR),
                        ChannelInfo(CI_ID_S_MUSIC, type = CI_TYPE_MUSIC),
                    )
                } else {
                    listOf(
                        ChannelInfo(CI_ID_T_CH1, name = "CH1", type = CI_TYPE_GUITAR),
                        ChannelInfo(CI_ID_T_CH2, name = "CH2", type = CI_TYPE_MIC),
                        ChannelInfo(CI_ID_T_CH3, name = "CH3", type = CI_TYPE_GUITAR),
                        ChannelInfo(CI_ID_T_MUSIC, name = "CH4", type = CI_TYPE_MUSIC),
                    )
                },
                supportStereo = false,
                supportOnProductPreset = false,
                pitchValue = null,
            )
        }

        fun soloMusician2ActiveModel(data: V5DeviceOOBE): Int {
            return when (data.musician) {
                SoloMusicianEnum.SINGER -> MODEL_SINGER
                SoloMusicianEnum.GUITAR_VOCALIST -> MODEL_SINGING
                else -> MODEL_GUITARIST
            }
        }

        fun trioAuxType(status: V5ChannelInputStatus? = null, auxType: V5AUXType? = null): Int? {
            if (status?.isDetected == true && null != auxType) {
                return when (auxType.type) {
                    V5AUXType.Type.Instrument -> AT_PIANO
                    V5AUXType.Type.Music -> AT_MUSIC
                }
            }
            return null
        }

        fun trioMasterVolume(masterVolume: V5ChannelVolume? = null): Int {
            return masterVolume?.volume ?: 0
        }


        /**
         * Any of the following parameter changes should recreate the [ChannelInfo] array notify to JS
         */
        fun soloChannelInfoList(
            musician: V5DeviceOOBE? = null,
            ch1Status: V5ChannelInputStatus? = null,
            ch1MicVolume: V5ChannelVolume? = null,
            ch1GuitarVolume: V5ChannelVolume? = null,
            ch4Status: V5ChannelInputStatus? = null,
            musicSource: V5ActiveAudioSource? = null,
            musicVolume: V5AudioVolume? = null,
        ): List<ChannelInfo> {
            val ret = mutableListOf<ChannelInfo>()
            if (null == musician) {
                return ret
            }
            val gen3MusicChannelInfo = {
                ChannelInfo(
                    id = CI_ID_S_MUSIC,
                    type = when (musicSource?.source) {
                        V5AudioSource.BT -> CI_TYPE_BLUETOOTH
                        else -> CI_TYPE_MUSIC
                    },
                    enable = null != musicSource && musicSource.source != V5AudioSource.None,
                    value = musicVolume?.value ?: 0,
                )
            }
            when (musician.musician) {
                SoloMusicianEnum.GUITARIST -> {
                    ret.add(
                        ChannelInfo(
                            id = CI_ID_S_CH1,
                            type = CI_TYPE_SOLO_GUITAR,
                            value = ch1GuitarVolume?.volume ?: 0,
                            enable = ch1Status?.isDetected == true && ch1Status.inputType == V5CHInputType.Guitar
                        )
                    )
                    ret.add(gen3MusicChannelInfo())
                }

                SoloMusicianEnum.GUITAR_VOCALIST -> {
                    ret.add(
                        ChannelInfo(
                            id = CI_ID_S_CH1,
                            type = CI_TYPE_SOLO_GUITAR,
                            value = ch1GuitarVolume?.volume ?: 0,
                            enable = ch1Status?.isDetected == true && ch1Status.inputType == V5CHInputType.Guitar
                        )
                    )
                    ret.add(
                        ChannelInfo(
                            id = CI_ID_S_BUILT_IN_MIC,
                            type = CI_TYPE_SOLO_BUILT_IN_MIC,
                            value = ch1MicVolume?.volume ?: 0,
                            enable = true,
                        )
                    )
                    ret.add(gen3MusicChannelInfo())
                }

                SoloMusicianEnum.SINGER -> {
                    ret.add(
                        ChannelInfo(
                            id = CI_ID_S_CH1,
                            type = if (ch1Status?.isDetected == true) CI_TYPE_MIC else CI_TYPE_SOLO_BUILT_IN_MIC,
                            value = ch1MicVolume?.volume ?: 0,
                            enable = ch1Status?.isDetected == true && ch1Status.inputType == V5CHInputType.Mic
                        )
                    )
                    ret.add(gen3MusicChannelInfo())
                }

                SoloMusicianEnum.RESET -> Unit
            }
//            ret.forEach {
//                it.enable = true
//            }
            return ret
        }

        fun trioChannelInfoList(
            ch1Status: V5ChannelInputStatus? = null,
            ch2Status: V5ChannelInputStatus? = null,
            ch3Status: V5ChannelInputStatus? = null,
            ch4Status: V5ChannelInputStatus? = null,
            ch1Volume: V5ChannelVolume? = null,
            ch2Volume: V5ChannelVolume? = null,
            ch3Volume: V5ChannelVolume? = null,
            ch4Volume: V5ChannelVolume? = null,
            auxType: V5AUXType? = null,
            audioSource: V5ActiveAudioSource? = null,
            audioVolume: V5AudioVolume? = null,
        ): List<ChannelInfo> {
            val ret = mutableListOf<ChannelInfo>()
            ret.add(
                ChannelInfo(
                    CI_ID_T_CH1, type = when (ch1Status?.inputType) {
                        V5CHInputType.Mic -> CI_TYPE_MIC
                        else -> CI_TYPE_GUITAR
                    },
                    name = "CH1",
                    enable = ch1Status?.isDetected ?: false,
                    value = ch1Volume?.volume ?: 0
                )
            )
            ret.add(
                ChannelInfo(
                    CI_ID_T_CH2, type = when (ch2Status?.inputType) {
                        V5CHInputType.Mic -> CI_TYPE_MIC
                        else -> CI_TYPE_PIANO
                    },
                    name = "CH2",
                    enable = ch2Status?.isDetected ?: false,
                    value = ch2Volume?.volume ?: 0
                ),
            )
            ret.add(
                ChannelInfo(
                    CI_ID_T_CH3,
                    type = CI_TYPE_GUITAR,
                    name = "CH3",
                    enable = ch3Status?.isDetected ?: false,
                    value = ch3Volume?.volume ?: 0,
                )
            )
            if (ch4Status?.isDetected == true && auxType?.type == V5AUXType.Type.Instrument) {
                ret.add(
                    ChannelInfo(
                        CI_ID_T_CH4_INSTRUMENT,
                        type = CI_TYPE_PIANO,
                        name = "CH4-AUX",
                        enable = true,
                        value = ch4Volume?.volume ?: 0,
                    )
                )
                ret.add(
                    ChannelInfo(
                        CI_ID_T_MUSIC,
                        type = CI_TYPE_MUSIC,
                        name = when (audioSource?.source) {
                            V5AudioSource.BT -> "CH4-BT"
                            else -> "CH4"
                        },
                        enable = null != audioSource && audioSource.source != V5AudioSource.None,
                        value = audioVolume?.value ?: 0,
                    )
                )
            } else {
                ret.add(
                    ChannelInfo(
                        CI_ID_T_MUSIC,
                        type = CI_TYPE_MUSIC,
                        name = when (audioSource?.source) {
                            V5AudioSource.USBFlashDisk,
                            V5AudioSource.UAC -> "CH4-USB"

                            V5AudioSource.AUX -> "CH4-AUX"
                            V5AudioSource.BT -> "CH4-BT"
                            else -> "CH4"
                        },
                        enable = null != audioSource && audioSource.source != V5AudioSource.None,
                        value = audioVolume?.value ?: 0,
                    )
                )
            }
//            ret.forEach {
//                it.enable = true
//            }
            return ret
        }
    }
}

@Keep
data class JsSetBandBoxModelInfo(
    val channelVolumn: ChannelVolume? = null,
    val activeModel: Int? = null,
    val auxType: Int? = null,
    val masterVolumn: Int? = null,
) {
    @Keep
    data class ChannelVolume(
        val id: Int,
        val volumn: Int
    )
}

@Keep
data class JsBandBoxModelFunction(val id: Int, val functionId: Int) {
    companion object {
        const val F_ID_GUITAR_PRESET = 1
        const val F_ID_MIC_SENS = 2
        const val F_ID_MUSIC_PITCH = 3
        const val F_ID_PICKUP = 4
        const val F_ID_EQ = 5
        const val F_ID_FX = 6

    }
}

@Keep
data class JsGetBandBoxMicInfo(val id: Int)

@Keep
data class JsBandBoxMicInfo(var channelId: Int, var reverbVolumn: Int = 0, var echoVolumn: Int = 0)

@Keep
data class JsSetBandBoxMicInfo(val channelId: Int, val reverbVolumn: Int?, val echoVolumn: Int?)

@Keep
data class JsGetBandBoxEq(val channelId: Int?)

@Keep
data class JsGetHorizon3FeatureInfo(
    var screenDisplay: ScreenDisplay? = null,
    var ambientLight: AmbientLight? = null,
    var alarms: List<Alarm>? = null,
    var sleepMode: SleepMode? = null,
    var radios: List<Radio>? = null
)

@Keep
data class JsGetHorizon3SleepMode(
    var soundSource: SoundSource? = null,
    var soundTimer: Int = 0,
    var ambientLight: AmbientLight? = null
)

@Keep
data class ScreenDisplay(
    val brightness: Int = 0
)

@Keep
data class AmbientLight(
    val enable: Boolean,
    val brightness: Int,
    val temperature: Int,
)

@Keep
data class Alarm(
    val id: Int = 0,
    val time: String,
    val sunrise: Boolean,
)

@Keep
data class SleepMode(
    val enable: Boolean,
    val soundTimer: Int = 0,
    val soundSource: String? = null,
)

@Keep
data class SoundSource(
    val id: Int = 0,
    val type: Int = 0,
    var displayName: String? = null,
)

@Keep
data class Radio(
    val id: Int = 0,
    val selected: Boolean
)

/**
 * | Key    | Value  |       comment        |
 * |--------|--------|---------------|
 * |diActionItem|[String]| music_control, in_app_music, EQ, moment, product_settings, product_info, wifi_streaming, va_setup, multi_room_setup, grouping, roon_ready_free_trial |
 */
data class EventAction(
    @SerializedName("diActionItem")
    val diActionItem: String? = null
)