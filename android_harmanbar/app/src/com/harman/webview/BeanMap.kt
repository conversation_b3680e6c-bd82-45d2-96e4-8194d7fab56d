package com.harman.webview

import android.bluetooth.BluetoothDevice
import com.harman.colorId
import com.harman.command.one.bean.BASS_BOOST_DEEP
import com.harman.command.one.bean.BASS_BOOST_OFF
import com.harman.command.one.bean.BASS_BOOST_PUNCHY
import com.harman.command.one.bean.Channel
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.GroupType
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.partybox.gatt.eq.CustomEQBandC1
import com.harman.command.partybox.gatt.eq.CustomEQBandC2
import com.harman.command.partybox.gatt.eq.CustomEQC1
import com.harman.command.partybox.gatt.eq.CustomEQC2
import com.harman.command.partybox.gatt.eq.EQBean
import com.harman.command.partybox.gatt.eq.EQSettings
import com.harman.command.partybox.gatt.eq.EnumCustomEQLevel
import com.harman.command.partybox.gatt.eq.EnumEQScope
import com.harman.command.partybox.gatt.eq.PresetEQ
import com.harman.command.partybox.gatt.light.EnumLightPattern
import com.harman.command.partybox.gatt.light.HKPattern
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.isOnyx
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.PartyBoxLightInfo
import com.harman.discover.bean.bt.lightTokenIdToJsId
import com.harman.discover.bean.deviceSupportDolby
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.AudioSource
import com.harman.discover.info.PartyBoxSecondaryInfo
import com.harman.discover.info.PartyConnectStatus
import com.harman.discover.info.PlayerStatus
import com.harman.discover.util.Tools.foundSecondaryDevice
import com.harman.discover.util.Tools.hasValidGroupId
import com.harman.discover.util.Tools.supportMood
import com.harman.displayShortDeviceName
import com.harman.getBLEProtocol
import com.harman.getOrientation
import com.harman.isCN
import com.harman.isOneCommander
import com.harman.log.Logger
import com.harman.modelName
import com.harman.supportBattery
import com.harman.supportBleControl
import com.harman.webview.models.Events
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.EnumEQCategory

/**
 * Created by gerrardzhang on 2024/6/27.
 */
object BeanMap {

    fun String?.toTargetPlayFunction(device: PartyBoxDevice): PlayerStatus? = when (this) {
        Events.PlayFunctionEvents.PLAY_OR_PAUSE -> {
            if (device.isPlaying) {
                PlayerStatus.PLAYER_STATE_PAUSE
            } else {
                PlayerStatus.PLAYER_STATE_PLAY
            }
        }

        Events.PlayFunctionEvents.PREVIOUS -> {
            PlayerStatus.PLAYER_STATE_PREV
        }

        Events.PlayFunctionEvents.NEXT -> {
            PlayerStatus.PLAYER_STATE_NEXT
        }

        else -> null
    }

    fun EQBean.toEQSettings(
        version: EnumEQCategory, setEQ: SetEQ, localEQConfig: EQBean
    ): EQSettings =
        when (val eqBean = this) {
            is PresetEQ -> {
                EQSettings(
                    version = version,
                    eqCategory = eqBean.eqCategory,
                    presetEQ = PresetEQ(
                        eqCategory = eqBean.eqCategory,
                        bandCount = eqBean.bandCount,
                        sampleRate = eqBean.sampleRate,
                        bands = eqBean.bands,
                        displayName = eqBean.displayName
                    )
                )
            }

            is CustomEQC1 -> {
                val newBands = eqBean.bands?.mapIndexedNotNull { index, customEQBand ->
                    val newGain = setEQ.gain?.getOrNull(index) ?: return@mapIndexedNotNull null
                    val newLevel =
                        EnumCustomEQLevel.valueOf(newGain) ?: return@mapIndexedNotNull null

                    CustomEQBandC1(
                        band = customEQBand.band,
                        level = newLevel,
                        frequency = customEQBand.frequency
                    )
                }

                EQSettings(
                    version = version,
                    eqCategory = eqBean.eqCategory,
                    customEQC1 = CustomEQC1(
                        scope = eqBean.scope,
                        bandCount = eqBean.bandCount,
                        bands = newBands,
                        displayName = localEQConfig.displayName
                    )
                )
            }

            is CustomEQC2 -> {
                val newBands = eqBean.bands?.mapIndexed { index, customEQBand ->
                    val newGain = setEQ.gain?.getOrNull(index) ?: customEQBand.gain
                    val newF = setEQ.fs?.getOrNull(index) ?: customEQBand.frequency

                    CustomEQBandC2(
                        band = customEQBand.band,
                        gain = newGain.toFloat(),
                        frequency = newF.toFloat(),
                        q = customEQBand.q
                    )
                }

                EQSettings(
                    version = version,
                    eqCategory = eqBean.eqCategory,
                    customEQC2 = CustomEQC2(
                        bandCount = eqBean.bandCount,
                        sampleRate = eqBean.sampleRate,
                        bands = newBands,
                        displayName = localEQConfig.displayName
                    )
                )
            }
        }

    /**
     * @param eqSettings inherit custom gain values from [EnumCustomEQLevel] stored in [eqSettings] if exists.
     */
    fun mapToEQList(eqSettings: EQSettings, eqBeans: List<EQBean>?): List<EQItem> {
        return eqBeans?.mapNotNull { bean ->
            when (bean) {
                is PresetEQ -> {
                    EQItem(
                        eqName =bean.displayName,
                        eqId = bean.eqCategory.categoryId,
                        fs = bean.bands.map { it.frequency.toInt() },
                        gain = bean.bands.map { it.gain.toDouble() },
                        max = EnumEQScope.LEVEL_SCOPE_6.absoluteValue,
                        min = -EnumEQScope.LEVEL_SCOPE_6.absoluteValue
                    )
                }

                is CustomEQC1 -> {
                    EQItem(
                        eqName = bean.displayName,
                        eqId = bean.eqCategory.categoryId,
                        fs = bean.bands?.map { it.frequency.toInt() },
                        gain = eqSettings.customEQC1?.bands?.map { it.level.gain }
                            ?: bean.bands?.map { it.level.gain },
                        max = bean.scope.absoluteValue,
                        min = -bean.scope.absoluteValue
                    )
                }

                is CustomEQC2 -> {
                    EQItem(
                        eqName = bean.displayName,
                        eqId = EnumEQCategory.CUSTOM_FULL.categoryId,
                        fs = bean.bands?.map { it.frequency.toInt() },
                        gain = eqSettings.customEQC2?.bands?.map { it.gain.toDouble() }
                            ?: bean.bands?.map { it.gain.toDouble() }
                    )
                }

                else -> null
            }
        } ?: listOf()
    }

    private fun Device.toConnectStatusInt(): Int {
        val device = this
        val protocol = device.getBLEProtocol()

        fun mapStatusToUi(status: EnumConnectionStatus?): Int = when(status) {
            EnumConnectionStatus.CONNECTED -> EnumDeviceInfoStatus.BLE_CONNECTED.value
            EnumConnectionStatus.DISCONNECTED -> EnumDeviceInfoStatus.DISCONNECTED.value
            EnumConnectionStatus.CONNECTING -> EnumDeviceInfoStatus.CONNECTING.value
            else -> EnumDeviceInfoStatus.DISCONNECTED.value
        }

        return when (protocol) {
            BluetoothDevice.TRANSPORT_LE -> mapStatusToUi(this.gattStatus)
            BluetoothDevice.TRANSPORT_BREDR -> mapStatusToUi(this.brEdrStatus)
            else -> EnumDeviceInfoStatus.DISCONNECTED.value
        }
    }

    fun PartyBoxDevice.toBatteryInfo(): BatteryInfo {
        val device = this

        return BatteryInfo(
            isCharge = device.isCharging,
            acCable = device.isAcWithoutBattery,
            value = device.batteryLevel
        )
    }

    fun PartyBoxSecondaryInfo.toBatteryInfo(): BatteryInfo {
        val device = this

        return BatteryInfo(
            isCharge = device.isCharging,
            acCable = device.isAcWithoutBattery,
            value = device.batteryLevel
        )
    }

    /**
     * | Key    |  comment |
     * |--------|--------|
     * |twsVolumeSync | TWS Volume Sync|
     * |userEQ | User EQ|
     * |deviceAnalyticsInfo | DeviceAnalyticsInfo|
     * |feedbackTone | Feedback Tone|
     * |partyLightStage | PartyLight Stage|
     * |batterySavingMode | Battery Saving Mode|
     * |crossTWS | Cross TWS|
     */
    fun PartyBoxDevice.toFeatures(): List<String> {
        val features = mutableListOf<String>()

        if (true == deviceFeature?.supportCrossTws) {
            features.add("crossTWS")
        }

        if (isOnyx() || true == deviceFeature?.supportFeedbackTone) {
            features.add("feedbackTone")
        }

        return features.toList()
    }

    /**
     * @param connectStatus [EnumDeviceInfoStatus.value]
     */
    fun PartyBoxDevice.toDeviceInfo(connectStatus: Int = toConnectStatusInt()): DeviceInfo {
        val device = this

        return DeviceInfo(
            status = connectStatus,
            groupType = device.toGroupType(),
            pId = device.pid,
            cId = device.cid,
            deviceName = device.deviceName,
            modelName = device.modelName(),
//            macAddr = device.macAddress,
            oneOsVer = null,
            fwVersion = device.firmwareVersion,
            serialNumber = device.serialNumber,
            channel = device.audioChannel.toChannelInt(),
            groupName = device.groupName,
            batteryInfos = if (EnumDeviceInfoStatus.BLE_CONNECTED.value == connectStatus && device.supportBattery()) {
                listOf(device.toBatteryInfo())
            } else {
                null
            },
            features = device.toFeatures(),
            isFeedbackToneOn = device.isFeedbackToneOn,
            orientation = device.getOrientation()
        )
    }

    fun PartyBandDevice.toDeviceInfo(connectStatus: Int = toConnectStatusInt()): DeviceInfo {
        val device = this

        return DeviceInfo(
            connectStatus,
            //todo zrz 前期不做group
            0,
            device.pid,
            device.cid,
            device.deviceName,
            device.modelName(),
            device.macAddress,
            "",
            fwVersion = device.firmwareVersion,
            channel = 0,
            bleControlSupport = true,
            googleCastEnabled = false,
        )
    }

    fun PartyBoxDevice.toSecondaryDeviceInfoList(): List<SecondaryDeviceInfo>? {
        val device = this
        val secondaryInfo = device.secondaryInfo?.toSecondaryDeviceInfo(
            primaryDevice = device
        )
        if (null != secondaryInfo) {
//            secondaryInfo.forEach { item -> if("00:00:00:00:00:00" == item.macAddr){
//                item.status = EnumDeviceInfoStatus.DISCONNECTED.value
//                }
//            }
            return secondaryInfo
        }

        val secondaryDevice = device.foundSecondaryDevice() ?: return null
        val connectStatus = secondaryDevice.toConnectStatusInt()
        return listOf(
            SecondaryDeviceInfo(
                status = connectStatus,
                pId = secondaryDevice.pid,
                cId = secondaryDevice.cid,
                deviceName = device.deviceName, // show stereo group name after grouped.
                modelName = secondaryDevice.modelName(),
//                macAddr = secondaryDevice.macAddress,
                serialNumber = secondaryDevice.serialNumber,
                fwVersion = secondaryDevice.firmwareVersion,
                channel = secondaryDevice.audioChannel.toChannelInt(),
                batteryInfos = if (EnumDeviceInfoStatus.BLE_CONNECTED.value == connectStatus && device.supportBattery()) {
                    listOf(secondaryDevice.toBatteryInfo())
                } else {
                    null
                },
            )
        )
    }

    private fun PartyBoxSecondaryInfo.toSecondaryDeviceInfo(
        primaryDevice: PartyBoxDevice
    ): List<SecondaryDeviceInfo> {
        val source = this
        val connectStatus = if (0 == source.batteryLevel) EnumDeviceInfoStatus.DISCONNECTED.value else EnumDeviceInfoStatus.BLE_CONNECTED.value
        return listOf(
            SecondaryDeviceInfo(
                status = connectStatus,
                pId = primaryDevice.pid,
                cId = source.colorID,
                deviceName = source.deviceName, // show stereo group name after grouped.
                modelName = primaryDevice.modelName(),
//                macAddr = source.macAddress,
                fwVersion = source.firmwareVersion,
                serialNumber = source.serialNumber,
                channel = primaryDevice.audioChannel?.opposite().toChannelInt(),
                batteryInfos = if (EnumDeviceInfoStatus.BLE_CONNECTED.value == connectStatus && primaryDevice.supportBattery()) {
                    listOf(source.toBatteryInfo())
                } else {
                    null
                }
            )
        )
    }

    fun OneDevice.toSecondaryDeviceInfo(
        groupInfo: GetGroupInfo? = null,
        groupParameterRsp: GetGroupParameterRsp? = null
    ): List<SecondaryDeviceInfo>? {
        val device = this
        val groupInfoExt = device.groupInfoExt?.groupInfo ?: groupInfo
        val groupParameter = device.groupParameterExt?.groupParameter ?: groupParameterRsp
        Logger.d("BeanMap","toSecondaryDeviceInfoList groupInfoExt = $groupInfoExt groupParameter = $groupParameter")
        groupInfoExt ?: return null
        groupParameter ?: return null
        return groupParameter.members?.filter { item ->
            groupInfoExt.groupInfo?.getGCIds()?.contains(
                item.id?.lowercase()
            ) == true
        }?.map { member ->
            SecondaryDeviceInfo(
                status = if (groupInfoExt.onlineDevices?.any { it.equals(member.id,true) } == true) EnumDeviceInfoStatus.WIFI_CONNECTED.value else EnumDeviceInfoStatus.DISCONNECTED.value,
                pId = AppConfigurationUtils.getPidByModelName((if(member.deviceName?.isBlank() == true) groupInfoExt.groupInfo?.members?.find { item -> item.id == member.id }?.deviceName else member.deviceName)?:""),
                cId = if (member.colorId != null && member.colorId != 0) member.colorId.toString() else groupInfoExt.groupInfo?.members?.find { item -> item.id == member.id }?.colorId.toString(),
                deviceName = member.friendlyName,
                modelName = if(member.deviceName?.isBlank() == true) groupInfoExt.groupInfo?.members?.find { item -> item.id == member.id }?.deviceName else member.deviceName,
                macAddr = member.getMacAddr()?.uppercase(),
                oneOSVersion = member.oneOsVer,
                fwVersion = member.version,
                serialNumber = member.serialNumber,
                batteryInfos = if (device.supportBattery()) groupParameter.battery?.filter { item -> item.id == member.id }
                    ?.map { item -> BatteryInfo(item.isACMode, true, item.batteryLevel) } ?: emptyList() else emptyList()
            )
        }
    }

    fun OneDevice.toSecondaryDeviceInfoList(
        groupInfo: GetGroupInfo? = null,
        groupParameterRsp: GetGroupParameterRsp? = null
    ): List<SecondaryDeviceInfo>? {
        val device = this
        val groupInfoExt = device.groupInfoExt?.groupInfo ?: groupInfo
        val groupParameter = device.groupParameterExt?.groupParameter ?: groupParameterRsp
        Logger.d(
            "BeanMap",
            "toSecondaryDeviceInfoList groupInfoExt = $groupInfoExt groupParameter = $groupParameter"
        )
        groupInfoExt ?: return null
        return groupInfoExt.groupInfo?.getGCMembers()?.map { groupInfoMember ->
            val groupParameterMember =
                groupParameter?.members?.find { item -> item.id == groupInfoMember.id }
            val gcDevice = groupInfoMember.crc?.let { DeviceStore.findOne(it) }
            SecondaryDeviceInfo(
                status = if (groupInfoExt.onlineDevices?.any {
                        it.equals(
                            groupInfoMember.id,
                            true
                        )
                    } == true) EnumDeviceInfoStatus.WIFI_CONNECTED.value else EnumDeviceInfoStatus.DISCONNECTED.value,
                pId = AppConfigurationUtils.getPidByModelName(
                    groupInfoMember.deviceName ?: groupParameterMember?.deviceName
                    ?: gcDevice?.modelName() ?: ""
                ),
                cId = if (groupParameterMember?.colorId != null && groupParameterMember.colorId != 0) groupParameterMember.colorId.toString() else groupInfoMember.colorId.toString(),
                deviceName = groupParameterMember?.friendlyName ?: groupInfoMember.friendlyName
                ?: gcDevice?.deviceName,
                modelName = groupInfoMember.deviceName ?: groupParameterMember?.deviceName
                ?: gcDevice?.modelName(),
                macAddr = groupInfoMember.getMacAddr()?.uppercase()
                    ?: groupParameterMember?.getMacAddr()?.uppercase() ?: gcDevice?.macAddress,
                oneOSVersion = groupParameterMember?.oneOsVer ?: gcDevice?.oneOsVer,
                fwVersion = groupParameterMember?.version ?: gcDevice?.firmware,
                serialNumber = groupParameterMember?.serialNumber ?: gcDevice?.serialNumber,
                batteryInfos = if (device.supportBattery()) groupParameter?.battery?.filter { item -> item.id == groupInfoMember.id }
                    ?.map { item -> BatteryInfo(item.isACMode, true, item.batteryLevel) }
                    ?: emptyList() else emptyList()
            )
        }

    }


    /**
     * @return
     * groupType 0: 'single'
     * 1: 'twsParty'
     * 2: 'twsStereo'
     * 3: 'auracastParty'
     * 4: 'auracastStereo'
     * 5: 'multiChannel'
     */
    private fun Device.toGroupType(groupInfo: GetGroupInfo? = null): Int =
        when (this) {
            is PartyBoxDevice -> {
                if(hasValidGroupId()){
                    if (PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED == partyConnectStatus) {
                        2
                    } else {
                        2
                    }
                }else{
                    0
                }

            }

            is OneDevice -> {
                val groupInfoNew = this.groupInfoExt?.groupInfo ?: groupInfo
                val groupMode = groupInfoNew?.groupMode
                val groupInfoTemp = groupInfoNew?.groupInfo
                if (groupInfoTemp == null || groupMode == GroupMode.SINGLE.value) {
                    0
                } else if (groupMode == GroupMode.BEFORE_GROUP.value) {
                    3
                } else if (groupInfoTemp.getGroupType() == GroupType.STEREO) {
                    2
                } else if (groupInfoTemp.getGroupType() == GroupType.MULTICHANNEL) {
                    1
                } else {
                    0
                }
            }

            else -> 0
        }

    private fun OneDevice.toGroupName(groupInfo: GetGroupInfo? = null): String? =
        if (groupInfo?.isSingle() == true) this.deviceName else groupInfo?.groupInfo?.group?.name

    /**
     * @return
     * 0: none
     * 1: left
     * 2: right
     */
    private fun AudioChannel?.toChannelInt(): Int = when (this) {
        AudioChannel.STEREO_LEFT -> 1
        AudioChannel.STEREO_RIGHT -> 2
        AudioChannel.NONE_CHANNEL -> 0
        else -> 0
    }

    fun PartyBoxLightInfo.collectLightInfo(): JsLightInfo {
        val source = this

        return JsLightInfo(
            enable = source.mainSwitch?.boolValue,
            patternId = source.pattern?.name?.lowercase(),
            disablePatternIds = source.inActivePatterns.map { pattern -> pattern.patternName.lowercase() },
            lightTokens = source.elementsSwitch.filter { it.key.lightTokenIdToJsId() != 0 }.map { (element, switch) ->
                JsLightInfo.JsLightToken(id = element.lightTokenIdToJsId(), state = switch.boolValue)
            }.let {
                if (it.size > 3 && it[0].id == 9) { // Strobe light should not be the first light.
                    arrayListOf(it[1], it[2], it[0], it[3])
                } else {
                    it
                }
            },

            supportPatternIds = arrayListOf(EnumLightPattern.NEON.patternName.lowercase(),
                EnumLightPattern.LOOP.patternName.lowercase(),
                EnumLightPattern.BOUNCE.patternName.lowercase(),
                EnumLightPattern.TRIM.patternName.lowercase(),
                EnumLightPattern.SWITCH.patternName.lowercase(),
                EnumLightPattern.FREEZE.patternName.lowercase()),

            color = if (source.isLoopingPattern()) {
                emptyList()
            } else {
                source.color?.list ?: emptyList()
            }
        )
    }

    fun PartyBoxDevice.collectLightInfo(): JsLightInfo? {
        val lightInfo = this.lightInfo ?: return null
        return lightInfo.collectLightInfo()
    }

    fun PartyBoxDevice.isStudioInfoNotEmpty(): Boolean = this.studioLightInfo != null&&this.studioLightInfo?.enable != null

    fun PartyBoxDevice.collectStudioLightInfo(): JSHKStudioLightInfo? {
        val studioLightInfo = this.studioLightInfo ?: return null
        try {
            val hkPattern = studioLightInfo.patterns?.find { it.pattern == studioLightInfo.activePattern }
                ?: HKPattern(pattern = EnumLightPattern.OCEAN, colorLevel = 0)
            val patterns = studioLightInfo.patterns?.map { JSHKPattern(id = it.pattern?.patternName?.lowercase(), level = it.colorLevel) }?.toMutableList()
            val level = if (studioLightInfo.dynamicLevel == 0) {
                JSDynamicLevel.Mid.level
            } else {
                studioLightInfo.dynamicLevel?.let { JSDynamicLevel.getLevelBy(it).level } ?: JSDynamicLevel.Mid.level
            }
//            val element = if (studioLightInfo.lightElement == 0) {
//                JSLightElement.All.element
//            } else {
//                studioLightInfo.lightElement?.let { JSLightElement.getElementBy(it).element }
//                    ?: JSLightElement.All.element
//            }
            val moodStatus = if (this.supportMood()) {
                if (this.audioSource == AudioSource.MOOD) JSMoodStatus.Enable.status else JSMoodStatus.Disable.status
            } else {
                JSMoodStatus.None.status
            }
            val supportResetPatternColor = true == this.deviceFeature?.supportResetPatternColor
            val projection = if (true == this.deviceFeature?.supportProjection) this.studioLightInfo?.projection else null
            return JSHKStudioLightInfo(enable = studioLightInfo.enable,
                activePattern = JSHKPattern(id = hkPattern.pattern?.patternName?.lowercase(), level = hkPattern.colorLevel),
                supportPatterns = patterns,
                brightness = studioLightInfo.brightness ?: 50,
                dynamicLevel = level,
                projectionLightingStatus = projection,
                moodStatus = moodStatus,
                supportPatternColorReset = supportResetPatternColor
            )
        } catch (e: Exception) {
            Logger.d("collectStudioLightInfo", "e:${e.toString()}")
        }
        return null
    }

    fun Device.toNearbyProduct(): NearbyProduct {
        val device = this

        return NearbyProduct(
            id = device.UUID,
            pid = device.pid,
            productName = device.deviceName,
            vid = device.vid,
            btMACCrc = device.macAddressCRC(),
            imageSrc = AppConfigurationUtils.getModelRenderPath(pid = device.pid!!, colorId = colorId())
        )
    }

    fun ProdSettingResponse<*, *>?.toFlexListeningFeature(): FlexListeningFeature {
        val prodSetting = this
        return FlexListeningFeature(
            enabled = true == prodSetting?.isFlexListeningMono || true == prodSetting?.isFlexListeningStereo,
            outputType = if (true == prodSetting?.isFlexListeningMono) {
                ProdSettingResponse.FLEX_LISTENING_MONO
            } else if (true == prodSetting?.isFlexListeningStereo) {
                ProdSettingResponse.FLEX_LISTENING_STEREO
            } else { // set FLEX_LISTENING_MONO as default type
                ProdSettingResponse.FLEX_LISTENING_MONO
            }
        )
    }

    /**
     * @param status [EnumDeviceInfoStatus]
     */
    fun OneDevice.toDeviceInfo(
        status: EnumDeviceInfoStatus,
        groupInfo: GetGroupInfo? = null,
    ): DeviceInfo {

        /**
         * @return `false` if need to display google cast banner
         */
        fun OneDevice.mapGoogleCastEnabled(): Boolean {
            val device = this

            return if (isOneCommander()) {
                // display banner only when had fetched [GetGroupDevicesFlagRsp]
                val flag = device.getGroupDevicesFlagExt?.groupDevicesFlag ?: return true

                !flag.needGoogleCast
            } else {
                device.isChromeCastEnabled || device.isCN() || !device.isWiFiOnline
            }
        }

        val device = this
        val go = groupInfo?.groupInfo?.getGOMember()
        val groupParameterOfGo = device.groupParameterExt?.groupParameter?.members?.find { item -> item.id == go?.id }
        val groupType = device.toGroupType(groupInfo)
        return DeviceInfo(
            status = status.value,
            groupType = groupType,
            pId = device.pid,
            cId = device.colorId(),
            deviceName = if(groupType == 3) device.displayShortDeviceName() else groupParameterOfGo?.friendlyName ?: go?.friendlyName ?: device.displayShortDeviceName(),
            modelName = device.modelName() ?: go?.deviceName,
            macAddr = device.wlan0 ?: go?.id?.chunked(2)?.joinToString(":"),
            regionCode = device.regionCode,
            oneOsVer = device.oneOsVer,
            fwVersion = device.firmware,
            serialNumber = device.serialNumber,
            groupName = device.toGroupName(groupInfo),
//            features = device.toFeatures()  // TODO features
            batteryInfos = device.buildBatteryInfos(status = status),
            dolbySupport = device.deviceSupportDolby(),
            bleControlSupport = device.supportBleControl(),
            googleCastEnabled = device.mapGoogleCastEnabled(),
            googleCastForVA = device.deviceSupportGoogleVA(),
            channel = if (go?.channel?.contains(Channel.Left.channel) == true) 1 else if (go?.channel?.contains(Channel.Right.channel) == true) 2 else 0,
            orientation = device.getOrientation()
        )
    }

    private fun OneDevice.buildBatteryInfos(status: EnumDeviceInfoStatus): List<BatteryInfo>? {
        val device = this

        return when (status) {
            EnumDeviceInfoStatus.WIFI_CONNECTED,
            EnumDeviceInfoStatus.BLE_CONNECTED -> {
                JsBatteryInfo.fromOneDevice(device)
            }

            else -> emptyList()
        }
    }
}

fun Int.toJsBassBoostInfo(): JSBassboostInfo {
    return JSBassboostInfo(
        when (this) {
            BASS_BOOST_DEEP -> 0
            BASS_BOOST_PUNCHY -> 1
            else -> null
        },
        this != BASS_BOOST_OFF
    )
}

enum class EnumDeviceInfoStatus(val value: Int) {
    WIFI_CONNECTED(1), // wifi connected
    DISCONNECTED(2),
    CONNECTING(3),
    READY_TO_CONNECT(4),
    BLE_CONNECTED(5),//only ble connected
}