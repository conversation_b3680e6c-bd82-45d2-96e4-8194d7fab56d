package com.harman.webview

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.activity.result.ActivityResult
import androidx.annotation.MainThread
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.gson.JsonObject
import com.harman.EventUtils
import com.harman.calibration.CalibrationBaseActivity
import com.harman.calibration.CalibrationGuideDialog
import com.harman.calibration.IAudioCalibrationDialogEvent
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.toJSONObj
import com.harman.home.HomePagesActivity
import com.harman.isCN
import com.harman.isSoundBar
import com.harman.log.Logger
import com.harman.oobe.IOOBEDialogEventListener
import com.harman.oobe.ble.BLEOOBEDialog
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.WiFiOOBEDialog
import com.harman.oobe.wifi.WiFiOOBEDialogHelper
import com.harman.openGoogleHome
import com.harman.openJBLBarSetup
import com.harman.openJBLPartyBox
import com.harman.openJBLPortable
import com.harman.supportBLEAuth
import com.harman.supportCalibration
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.va.VAGuideDialog
import com.harman.webview.HybridTools.getNewProductUrl
import com.harman.webview.HybridTools.getSetupProductUrl
import com.harman.webview.models.AddNewProductViewModel
import com.harman.webview.models.AddNewProductViewModelFactory
import com.harman.webview.models.Events
import com.harman.webview.models.HybridViewModel
import com.harman.webview.models.JsCallback
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean

class AddNewProductHybridActivity : CommonHybridActivity() {

    private var viewModel: HybridViewModel? = null

    private var bleOOBEDialog: BLEOOBEDialog? = null
    private var wifiOOBEDialogHelper: WiFiOOBEDialogHelper? = null

    private val isShowingOOBEDlg = AtomicBoolean(false)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        super.loadUrlFromBundle()

        viewModel = ViewModelProvider(
            this,
            AddNewProductViewModelFactory(activity = this@AddNewProductHybridActivity)
        )[AddNewProductViewModel::class.java].also { vm ->
            lifecycle.addObserver(vm)
            vm.notificationInvoker = this
            vm.gattConnectStatusListener = null
            Logger.i(TAG, "onCreate()")
        }

        (viewModel as AddNewProductViewModel).discoveredDevice.observe(this@AddNewProductHybridActivity) { model ->
            if (model is OneDevice && true == model.pid?.isNotBlank()) {
                doOOBE(activity = this@AddNewProductHybridActivity, device = model, callback = null)
            } else if (model is PartyBoxDevice && model.supportBLEAuth()) {
                doOOBE(activity = this@AddNewProductHybridActivity, device = model, callback = null)
            } else if (model is PartyBandDevice) {
                doOOBE(activity = this@AddNewProductHybridActivity, device = model, callback = null)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        bleOOBEDialog?.dismiss()

        viewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }
    }

    override fun onCustomEvent(
        eventName: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        Logger.d(TAG, "eventName: $eventName, data: $data")

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            Tools.repeatWithTimeout(
                repeatTimes = 0,
                timeoutMills = JS_EVENT_TIMEOUT_MILLS,
                onBlock = {
                    when (eventName) {
                        Events.START_ON_BOARDING -> {
                            handleStartOnBoarding(activity = this@AddNewProductHybridActivity, data = data, callback = callback)
                        }

                        Events.ENTER_FAQ -> {
                            val pId = data?.get("pId")?.asString
                            handleEnterFAQ(activity = this@AddNewProductHybridActivity, callback = callback, pId)
                        }

                        Events.BACK_TO_DASHBOARD -> {
                            handleBackToDashboard(activity = this@AddNewProductHybridActivity, callback = callback)
                        }

                        Events.REDIRECT_APP -> {
                            handleRedirectionApp(activity = this@AddNewProductHybridActivity, data = data, callback = callback)
                        }

                        Events.START_DISCOVER_PRODUCT -> {
                            handleStartDiscover(activity = this@AddNewProductHybridActivity, data = data, callback = callback)
                        }

                        Events.GET_NEARBY_PRODUCTS -> {
                            handleGetNearbyProducts(activity = this@AddNewProductHybridActivity, data = data, callback = callback)
                        }

                        Events.GET_SUPPORT_PRODUCTS -> {
                            handleGetSupportProducts(activity = this@AddNewProductHybridActivity, data = data, callback = callback)
                        }
                    }
                },
                onTimeout = {
                    callback.invoke("-1", null, null)
                }
            )
        }
    }

    private fun handleEnterFAQ(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterFAQ(), pidFromJS: $pidFromJS")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlFaq ?: return
        Logger.d(TAG, "handleEnterFAQ pid: $pid, \nurls: $linkUrl")
//        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("jbl_FAQ"))
        loadingUrlInBrowser(activity, linkUrl)
        callback.invoke("0", null, null)

    }

    private fun handleBackToDashboard(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "handleBackToDashboard()")
        callback.invoke("0", "back to dashboard", null)
        HomePagesActivity.resumeDashboardAndClearTask(activity)
        finish()
    }

    private fun loadingUrlInAppWebView(activity: CommonHybridActivity, linkUrl: String, title: String) {
        Logger.d(TAG, "loadingUrlInAppWebView Url: $linkUrl")
        QuickStartGuideWebActivity.launchQuickStartGuideWebActivity(activity, linkUrl, title)
    }

    private fun loadingUrlInBrowser(activity: CommonHybridActivity, linkUrl: String) {
        Logger.d(TAG, "loadingUrlInBrowser Url: $linkUrl")
        runCatching {
            val intent = Intent()
            intent.action = "android.intent.action.VIEW"
            intent.data = Uri.parse(linkUrl)
            activity.startActivity(intent)
        }.onFailure { e ->
            Logger.e(TAG, "", e)
        }
    }

    private fun handleStartDiscover(
        activity: AddNewProductHybridActivity,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        val jsonString = data?.toString() ?: run {
            callback.invoke("-1", null, null)
            return
        }

        GsonUtil.parseJsonToBean(jsonString, DiscoverModel::class.java)?.also { model ->
            if (viewModel is AddNewProductViewModel) {
                (viewModel as AddNewProductViewModel).searchDevice(model)
            }
        }
    }

    private fun handleGetNearbyProducts(
        activity: AddNewProductHybridActivity,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {

        val nearbyList = (viewModel as? AddNewProductViewModel)?.nearbyList
        val jsonStr = GsonUtil.parseBeanToJson(nearbyList ?: emptyList<NearbyProduct>())
        Logger.d(TAG, "handleGetNearbyProducts() >>> $jsonStr")

        callback.invoke("0", "", jsonStr.toJSONObj())
    }

    private fun handleGetSupportProducts(
        activity: AddNewProductHybridActivity,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        val pIds = AppConfigurationUtils.getSupportPidList()
        val jsonStr = GsonUtil.parseBeanToJson(pIds ?: emptyList<String>())
        Logger.d(TAG, "handleGetSupportProducts() >>> $jsonStr")

        callback.invoke("0", "", jsonStr.toJSONObj())
    }

    @MainThread
    private fun handleStartOnBoarding(
        activity: AddNewProductHybridActivity,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        val jsonString = data?.toString() ?: run {
            callback.invoke("-1", null, null)
            return
        }

        GsonUtil.parseJsonToBean(jsonString, NearbyProduct::class.java)?.also { nearProduct ->
            val id = nearProduct.id ?: return

            DeviceStore.findOne(id)?.let { device ->
                doOOBE(activity = activity, device = device, callback = callback)
                return
            }

            DeviceStore.findPartyBox(id)?.let { device ->
                if (!device.supportBLEAuth()) {
                    return
                }

                doOOBE(activity = activity, device = device, callback = callback)
                return
            }
        }

    }

    private fun doOOBE(
        activity: AddNewProductHybridActivity,
        device: OneDevice,
        callback: ((code: String, msg: String?, passBack: JSONObject?) -> Unit)?
    ) {
        if (isShowingOOBEDlg.getAndSet(true)) {
            return
        }

        wifiOOBEDialogHelper = WiFiOOBEDialogHelper(
            logTag = TAG,
            activity = activity,
            device = device,
            mode = EnumMode.FULL,
            oobeType = EventUtils.Dimension.EnumOOBEType.MANUAL,
            oobeDialogDismissListener = {
                isShowingOOBEDlg.set(false)
                callback?.invoke("-1", null, null)
            },
            oobeDialogEventListener = object : IOOBEDialogEventListener {
                override fun onDebugCloseAllClick() {
                    isShowingOOBEDlg.set(false)
                    bleOOBEDialog?.dismiss()
                    wifiOOBEDialogHelper?.dismissAllDialogs()
                }
            },
            calibrationDialogEvent = null,
            vaGuideDialogDismissListener = null
        ).run()
    }

    private fun doOOBE(
        activity: AddNewProductHybridActivity,
        device: Device,
        callback: ((code: String, msg: String?, passBack: JSONObject?) -> Unit)?
    ) {
        if (isShowingOOBEDlg.getAndSet(true)) return

        bleOOBEDialog = BLEOOBEDialog(
            activity = activity,
            device = device,
            listener = object : IOOBEDialogEventListener {
                override fun onDebugCloseAllClick() {
                    isShowingOOBEDlg.set(false)
                    bleOOBEDialog?.dismiss()
                }
            }
        ).also { dialog ->
            dialog.show()
            dialog.setOnDismissListener {
                isShowingOOBEDlg.set(false)
                callback?.invoke("-1", null, null)
            }
        }
    }

    private fun handleRedirectionApp(
        activity: AddNewProductHybridActivity,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        val jsonString = data?.toString() ?: run {
            callback.invoke("-1", null, null)
            return
        }

        GsonUtil.parseJsonToBean(jsonString, RedirectionAppInfo::class.java)?.also { app ->
            when (app.appName) {
                JBL_PORTABLE -> openJBLPortable(activity)
                JBL_PARTYBOX -> openJBLPartyBox(activity)
                JBL_BAR_SETUP -> openJBLBarSetup(activity)
                GOOGLE_HOME -> openGoogleHome(activity)
            }
        }
        callback.invoke("0", null, null)
    }

    override fun onNavigate(
        pageTitle: String?,
        pageName: String?,
        isPush: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        callback.invoke("0", null, null)
    }

    override fun onPopPage(callback: (code: String, msg: String?) -> Unit) {
        callback.invoke("0", "pop page")
        finish()
    }

    companion object {
        private const val TAG = "AddProductHybridActivity"

        private const val JS_EVENT_TIMEOUT_MILLS = 9 * 1000L

        const val BUNDLE_HARMAN_BAR_SEARCH = "Harman_Bar_Search"
        const val JBL_PORTABLE = "JBLPortable"
        const val JBL_PARTYBOX = "JBLPartybox"
        const val GOOGLE_HOME = "GoogleHome"
        const val JBL_BAR_SETUP = "JBLBarSetup"


        fun launchAddProduct(activity: Activity?): Boolean {
            activity ?: run {
                Logger.w(TAG, "launchAddProduct() >>> missing contect")
                return false
            }
            val url = getNewProductUrl(activity) ?: run {
                Logger.e(TAG, "launchAddProduct() >>> cannot get new product url")
                return false
            }
            Logger.d(TAG, "url: $url")
            val intent = Intent(activity, AddNewProductHybridActivity::class.java)
            intent.putExtra(BUNDLE_URL, url)
            intent.putExtra(BUNDLE_HARMAN_BAR_SEARCH, "searching_ble")

            activity.startActivity(intent)
//            activity.overridePendingTransition(R.anim.push_left_in, R.anim.push_right_out)
            return true
        }

        fun launchSetupProduct(activity: Activity?, pid: String): Boolean {
            activity ?: return false
            val url = getSetupProductUrl(activity = activity, pid = pid)
            Logger.d(TAG, "url: $url")

            val intent = Intent(activity, AddNewProductHybridActivity::class.java)
            intent.putExtra(BUNDLE_URL, url)
            intent.putExtra(BUNDLE_HARMAN_BAR_SEARCH, "searching_ble")
            activity.startActivity(intent)
            return true
        }
    }
}