package com.harman.webview.models

import com.harman.command.one.bean.EnumCoulsonStatus
import com.harman.discover.bean.OneDevice
import com.harman.music.service.EnumMusicServiceSource
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp

/**
 * UI event definition between [IHybridViewModel] and [com.harman.webview.DeviceControlHybridActivity]
 */
sealed class OHVMEvent {

    data class EnterCoulson(val status: EnumCoulsonStatus) : OHVMEvent()

    object ShowCalibrationGuideDialog : OHVMEvent()

    object ShowRoonReadyLaterTipsDialog : OHVMEvent()

    object EnterWiFiSetupFlow : OHVMEvent()

    object UnGroup : OHVMEvent()

    data class OtaGuide(val checkFwRsp: CheckFWResp?) : OHVMEvent()

    data class PopMusicServiceDialog(val source: EnumMusicServiceSource?) : OHVMEvent()
}