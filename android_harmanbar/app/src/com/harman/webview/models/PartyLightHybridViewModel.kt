package com.harman.webview.models

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.harman.command.common.IGeneralCommand
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.TypeStickNumber
import com.harman.command.partylight.AuracastMode
import com.harman.command.partylight.AuracastMode.Companion.toAuracastMode
import com.harman.command.partylight.AuracastMode.Companion.toEnable
import com.harman.command.partylight.GattPacketFormat
import com.harman.command.partylight.PLDevInfo
import com.harman.command.partylight.PLLightInfo
import com.harman.command.partylight.ReqDevInfo
import com.harman.command.partylight.ReqLightInfo
import com.harman.command.partylight.ReqSetDevInfo
import com.harman.command.partylight.ReqSetLightInfo
import com.harman.command.partylight.Switch.Companion.toEnable
import com.harman.command.partylight.Switch.Companion.toSwitch
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.listener.IPartyLightDeviceListener
import com.harman.connect.sendCmdSync
import com.harman.connect.session.PartyLightBusinessSession
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetDeviceFeatureInfoWithTimeout
import com.harman.connect.syncGetGeneralConfig
import com.harman.partylight.MIN_STAGE_DEV_COUNT
import com.harman.partylight.checkOta
import com.harman.partylight.findStageMainSpeaker
import com.harman.partylight.isStick
import com.harman.partylight.util.isAllFieldNull
import com.harman.partylight.util.toColorInt
import com.harman.webview.CommonHybridActivity
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.JsLightInfo
import com.harman.webview.ProductBaseInfo
import com.harman.webview.ProductRenderImage
import com.harman.webview.models.JsPartyLightFeature.ConnectedPartybox
import com.harman.discover.DeviceScanner
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyLightBTDevice
import com.harman.discover.info.PartyLightRole
import com.harman.discover.util.Tools.toJSONObj
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * @Description viewmodel for PartyLight dashboard page
 * <AUTHOR>
 * @Time 2024/7/17
 */
private const val spKeyIsFirstStage = "spKeyIsFirstStage"

class PartyLightHybridViewModelFactory(
    private val device: PartyLightDevice
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return PartyLightHybridViewModel(device) as T
    }
}

class PartyLightHybridViewModel(private var mainDev: PartyLightDevice) : ViewModel(),
    IHybridViewModel<PartyLightBusinessSession, DefaultSppBusinessSession, PartyLightBTDevice, PartyLightRole, PartyLightRole, PartyLightBTDevice, PartyLightDevice> {

    override val device: Device
        get() = mainDev

    private val deviceListener = object : IPartyLightDeviceListener {
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            gattConnectStatusListener?.invoke(status, session)
        }

        override fun onCommandReceived(device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand) {
            viewModelScope.launch {
                if (null == sendCommand) {
                    //this is device active notify
                    onNotifyFromDevice(receivedCommand)
                } else {
                    //this is response for request
                }
            }
        }
    }
    private val discoverObserver = object : IHmDeviceObserver {
        override fun onDevicesUpdate(devices: List<Device>) {
            super.onDevicesUpdate(devices)
            synchronized(this@PartyLightHybridViewModel) {
                val newGroupSize = mainDev.group().size
                if (newGroupSize != groupSize) {
                    groupSize = newGroupSize
                    viewModelScope.launch {
                        notificationInvoker?.notifyObj(Events.NOTIFY_PARTY_LIGHT_FEATURE, JsPartyLightFeature(memberCount = groupSize).toJSONObj())
                        //数量变化，触发stage检测
                        checkStickStageEnable()
                    }
                }
            }
        }

        override fun onDeviceOnlineOrUpdate(device: Device) {
            super.onDeviceOnlineOrUpdate(device)
            synchronized(this@PartyLightHybridViewModel) {
                if (mainDev.UUID == device.UUID && mainDev !== device) {
                    //实例发生变化
                    mainDev.unregisterDeviceListener(deviceListener)
                    mainDev = device as PartyLightDevice
                    mainDev.registerDeviceListener(deviceListener)
                }
            }
        }
    }
    private var lightInfo: PLLightInfo = PLLightInfo()
    private var devInfo: PLDevInfo = PLDevInfo()
    private var groupSize = mainDev.group().size
    val eventStream = MutableSharedFlow<PLVMEvent>()

    init {
        mainDev.registerDeviceListener(deviceListener)
        DeviceScanner.registerObserver(discoverObserver)
    }

    override val tag: String = ""

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)

        super.reportV5Analytics(owner = owner, scope = viewModelScope)
    }

    override suspend fun onCustomEvent(activity: CommonHybridActivity, eventName: String?, data: JsonObject?, callback: JsCallback) {
        super.onCustomEvent(activity, eventName, data, callback)
        when (eventName) {
            Events.GET_DEVICE_INFO -> getDeviceInfo()
            Events.GET_PARTY_LIGHT_FEATURE -> getPartyLightFeature(callback)
            Events.GET_LIGHT_INFO -> getLightInfo(callback)
            Events.SET_LIGHT_CONTROL -> setLightInfo(data, callback)
            Events.ENTER_COLOR_PICKER -> chooseCustomColor(callback)
            Events.ENTER_PRODUCT_INFO -> eventStream.emit(PLVMEvent.EnterProductInfo(mainDev.UUID!!))
            Events.GET_OTA_STATUS -> getOtaStatus(callback)
            Events.ENTER_OTA -> eventStream.emit(PLVMEvent.EnterOta(mainDev.UUID!!))
            Events.SET_PARTY_LIGHT_FEATURE -> setPartyLightFeature(data, callback)
            Events.ENTER_PARTY_STAGE -> onStageTap()
            Events.GET_PRODUCT_IMAGE -> getProductImage(data, callback)
        }
    }


    override fun onCleared() {
        super.onCleared()
        mainDev.unregisterDeviceListener(deviceListener)
        DeviceScanner.unregisterObserver(discoverObserver)
    }

    private fun getProductImage(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val baseInfo = GsonUtil.parseJsonToBean(data!!.toString(), ProductBaseInfo::class.java)!!
            val renderPath = baseInfo.pid?.let { pid -> AppConfigurationUtils.getModelRenderPath(pid, baseInfo.colorId) }
            val renderImage = ProductRenderImage(base64String = renderPath)
            val callbackObj = JSONObject(GsonUtil.parseBeanToJson(renderImage))
            callback.invoke("0", "", callbackObj)
        }.onFailure {
            callback.invoke("-1", "fail to getProductImage $it", null)
        }
    }

    private suspend fun onStageTap() {
        if (devInfo.isStickStaged()) {
            eventStream.emit(PLVMEvent.EnterDismissPartyStage(mainDev.UUID!!))
            return
        }
        if (stickCheckingStageJob?.isCompleted == true && stickCheckingStageJob?.await() == true) {
            eventStream.emit(
                PLVMEvent.EnterPartyStage(
                    mainDev.UUID!!,
                    groupSize,
                    SPUtils.getInstance().getBoolean(spKeyIsFirstStage, true),
                ) {
                    SPUtils.getInstance().put(spKeyIsFirstStage, false)
                },
            )
        } else {
            eventStream.emit(PLVMEvent.ShowPartyStageGuide {
                SPUtils.getInstance().put(spKeyIsFirstStage, false)
            })
        }
    }

    private suspend fun getOtaStatus(jsCallback: JsCallback) {
        val ret = checkOta(mainDev)
        jsCallback.invoke("0", "", mapOf("available" to (null != ret)).toJSONObj())
    }

    /**
     * device active notify , is not a response to a request
     */
    private fun onNotifyFromDevice(receivedCommand: IGeneralCommand) {
        when (receivedCommand.commandID) {
            GattPacketFormat.Rsp.LIGHT_INFO -> {
                PLLightInfo.fromPayload(receivedCommand.payload)?.also {
                    updateLightInfo(it)
                }
            }

            GattPacketFormat.Rsp.DEV_INFO -> {
                PLDevInfo.fromPayload(receivedCommand.payload)?.also {
                    updateDevInfo(it)
                }
            }
        }
    }

    private fun getDeviceInfo() {
        notificationInvoker?.notifyObj(
            Events.NOTIFY_DEVICE_INFO, JsDevInfo(
                pId = mainDev.pid,
                cId = mainDev.cid,
                deviceName = mainDev.deviceName,
                fwVersion = mainDev.firmwareVersion,
            ).toJSONObj()
        )
    }

    private suspend fun getPartyLightFeature(callback: JsCallback) {
        runCatching {
            val plLightInfo = reqPLLightInfo()!!
            val plDevInfo = reqPLDevInfo()!!
            callback.invoke(
                "0",
                "",
                JsPartyLightFeature.fromLightOrDev(plLightInfo, plDevInfo).copy(
                    memberCount = groupSize,
                    connectedPartybox = ConnectedPartybox.fromLightAndDev(plLightInfo.speakerIDtoLight, plDevInfo.auracastMode),
                ).toJSONObj(),
            )
            //拉取全量信息，触发stage检查
            checkStickStageEnable()
        }.onFailure {
            callback.invoke("-1", "getPartyLightInfo fail $it", null)
        }
    }

    private fun setPartyLightFeature(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val jsPartyLightFeature = GsonUtil.parseJsonToBean(data.toString(), JsPartyLightFeature::class.java)!!
            val plLightInfo = jsPartyLightFeature.convert2PLLightInfo()
            val plDevInfo = jsPartyLightFeature.convert2PLDevInfo()
            if (!plLightInfo.isAllFieldNull()) {
                mainDev.sendCommand(ReqSetLightInfo(plLightInfo))
                updateLightInfo(plLightInfo)
            }
            if (!plDevInfo.isAllFieldNull()) {
                mainDev.sendCommand(ReqSetDevInfo(plDevInfo))
                updateDevInfo(plDevInfo)
            }
            callback.invoke("0", "", null)
        }.onFailure {
            callback.invoke("-1", "setPartyLightFeature fail $it", null)
        }
    }

    /**
     * 如果ac状态改变，执行额外逻辑
     */
    private fun sideEffectOfSetAuracastMode(new: AuracastMode?, old: AuracastMode?) {
        if (new == AuracastMode.ON) {
            val mainSpeaker = findStageMainSpeaker()
            if (null != mainSpeaker) {
                //存在符合跟随条件的speaker,发送speaker id
                mainDev.sendCommand(ReqSetLightInfo(PLLightInfo(speakerIDtoLight = mainSpeaker.macAddressCRC())))
                updateLightInfo(PLLightInfo(speakerIDtoLight = mainSpeaker.macAddressCRC()))
            } else {
                //不存在符合跟随条件的speaker，发送空id给light
                mainDev.sendCommand(ReqSetLightInfo(PLLightInfo(speakerIDtoLight = PLLightInfo.EMPTY_SPEAKER_ID)))
                updateLightInfo(PLLightInfo(speakerIDtoLight = PLLightInfo.EMPTY_SPEAKER_ID))
            }
            //通知js更新ac同步状态
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE,
                JsPartyLightFeature(
                    auracastEnable = true,
                    connectedPartybox = ConnectedPartybox.fromLightAndDev(lightInfo.speakerIDtoLight, new),
                ).toJSONObj(),
            )
        }
        if (new == AuracastMode.OFF) {
            //用户主动关闭ac,由于设备没有notify依赖ac状态的其他状态，需要手动更新
            updateLightInfo(PLLightInfo(speakerIDtoLight = PLLightInfo.EMPTY_SPEAKER_ID))
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE,
                JsPartyLightFeature(
                    auracastEnable = false,
                ).toJSONObj(),
            )
        }

        if (new == AuracastMode.ON_LINKED) {
            //同步上speaker后通知js
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE,
                run {
                    JsPartyLightFeature(
                        connectedPartybox = ConnectedPartybox.fromLightAndDev(lightInfo.speakerIDtoLight, new)
                    )
                }.toJSONObj(),
            )
        }

        if (new == AuracastMode.ON_NOT_LINKED) {
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE,
                JsPartyLightFeature(
                    connectedPartybox = ConnectedPartybox.fromLightAndDev(lightInfo.speakerIDtoLight, new)
                ).toJSONObj(),
            )
        }
        //ac状态变化，触发stage检测
        checkStickStageEnable()
    }

    private suspend fun getLightInfo(callback: JsCallback) {
        runCatching {
            val resp = reqPLLightInfo()!!
            callback.invoke(
                "0",
                "",
                JsLightInfo.fromPLLightInfo(resp).toJSONObj(),
            )
        }.onFailure {
            callback.invoke("-1", "getLightInfo fail $it", null)
        }
    }

    private suspend fun setLightInfo(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val jsLightInfo = GsonUtil.parseJsonToBean(data.toString(), JsLightInfo::class.java)!!
            val plLightInfo = jsLightInfo.convert2PLLightInfo()
            mainDev.sendCommand(ReqSetLightInfo(plLightInfo))
            //立即更新本地状态（存在缺陷）
            updateLightInfo(plLightInfo)
            callback.invoke("0", "", null)
        }.onFailure {
            callback.invoke("-1", "setLightInfo fail $it", null)
        }
    }

    private var stickCheckingStageJob: Deferred<Boolean>? = null

    /**
     * 检查stick stage区域是否可用
     * 受 [PLLightInfo.speakerIDtoLight]、[PLDevInfo.auracastMode]、[groupSize]、[PLDevInfo.stageGroupId] 变化影响
     */
    private fun checkStickStageEnable() {
        if (!mainDev.isStick()) {
            return
        }
        if (devInfo.isStickStaged()) {
            //已设置stage
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE, JsPartyLightFeature(stageEnable = true).toJSONObj()
            )
            return
        }
        stickCheckingStageJob?.cancel()
        stickCheckingStageJob = viewModelScope.async {
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE, JsPartyLightFeature(stageEnable = false).toJSONObj()
            )
            val enableStage = checkStickEnableStageWithSpeaker()
            if (enableStage) {
                notificationInvoker?.notifyObj(
                    Events.NOTIFY_PARTY_LIGHT_FEATURE, JsPartyLightFeature(stageEnable = true).toJSONObj()
                )
            }
            return@async enableStage
        }
    }

    private suspend fun chooseCustomColor(jsCallback: JsCallback) {
        eventStream.emit(PLVMEvent.ChooseColor(lightInfo.currentColor) { pickColor ->
            PLLightInfo(patternLooping = PLLightInfo.PatternLooping.STATIC_COLOR, currentColor = pickColor).also { newLightInfo ->
                mainDev.sendCommand(ReqSetLightInfo(newLightInfo))
                //立即更新本地状态（存在缺陷）
                updateLightInfo(newLightInfo)
            }
        })
        jsCallback.invoke("0", "", null)
    }

    private suspend fun reqPLDevInfo(): PLDevInfo? {
        val respDevInfo = mainDev.sendCmdSync(ReqDevInfo(), PLDevInfo::fromPayload)
        return respDevInfo?.also {
            //全量更新
            devInfo = it
        }
    }

    private suspend fun reqPLLightInfo(): PLLightInfo? {
        val respLightInfo = mainDev.sendCmdSync(ReqLightInfo(), PLLightInfo::fromPayload)
        return respLightInfo?.also {
            //全量更新
            lightInfo = it
        }
    }

    /**
     * 增量更新[lightInfo]
     */
    private fun updateLightInfo(new: PLLightInfo) {
        val old = lightInfo.copy()
        lightInfo = lightInfo.copyWith(new)
        sideEffectOfUpdateLightInfo(new, old)
    }

    /**
     * 增量更新[devInfo]
     */
    private fun updateDevInfo(new: PLDevInfo) {
        val old = devInfo.copy()
        devInfo = devInfo.copyWith(new)
        sideEffectOfUpdateDevInfo(new, old)
    }

    private fun Any?.diffWith(other: Any?) = null != this && this != other

    /**
     * 增量更新[lightInfo]后的副作用
     * @param new 增量[PLLightInfo]
     * @param old 全量[PLLightInfo]
     */
    private fun sideEffectOfUpdateLightInfo(new: PLLightInfo, old: PLLightInfo) {
        if (new.pattern.diffWith(old.pattern) || new.patternLooping.diffWith(old.patternLooping) || new.currentColor.diffWith(old.currentColor)) {
            //灯效变化
            notificationInvoker?.notifyObj(
                Events.NOTIFY_LIGHT_INFO,
                JsLightInfo.fromPLLightInfo(old.copyWith(new)).toJSONObj(),
            )
        }
        if (new.lightBrightness.diffWith(old.lightBrightness) || new.backLightMode.diffWith(old.backLightMode) || new.lEDMovementSpeed.diffWith(old.lEDMovementSpeed)) {
            //常规feature改变
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE,
                JsPartyLightFeature.fromLightOrDev(new).toJSONObj(),
            )
        }
        if (new.speakerIDtoLight.diffWith(old.speakerIDtoLight)) {
            //跟随的speaker id发生变化更新 connectedPartybox 字段
            ConnectedPartybox.fromLightAndDev(new.speakerIDtoLight, devInfo.auracastMode)?.also {
                notificationInvoker?.notifyObj(
                    Events.NOTIFY_PARTY_LIGHT_FEATURE,
                    it.toJSONObj(),
                )
            }
            //跟随的speaker id发生变化，触发stage检测
            checkStickStageEnable()
        }
    }

    /**
     * 增量更新[devInfo]后的副作用
     * @param new 增量[PLDevInfo]
     * @param old 全量[PLDevInfo]
     */
    private fun sideEffectOfUpdateDevInfo(new: PLDevInfo, old: PLDevInfo) {
        if (new.danceMode.diffWith(old.danceMode) || new.soundDetection.diffWith(old.soundDetection) || new.auracastMode.diffWith(old.auracastMode)) {
            //常规device info改变
            notificationInvoker?.notifyObj(
                Events.NOTIFY_PARTY_LIGHT_FEATURE,
                JsPartyLightFeature.fromLightOrDev(plDevInfo = new).toJSONObj(),
            )
        }

        if (new.auracastMode.diffWith(old.auracastMode)) {
            //新的PLDevInfo包含ac变化，执行ac变化副作用
            sideEffectOfSetAuracastMode(new.auracastMode, old.auracastMode)
        }
    }

    /**
     * 检查stick是否与当前满足stage条件
     */
    private suspend fun checkStickEnableStageWithSpeaker(): Boolean {
        if (groupSize < MIN_STAGE_DEV_COUNT) {
            Logger.w(TAG, "can not stage: device count not enough >> ${groupSize}")
            return false
        }
        if (devInfo.auracastMode != AuracastMode.ON_LINKED) {
            Logger.w(TAG, "can not stage: device ac is not ON_LINKED >> ${devInfo.auracastMode}")
            return false
        }
        val mainSpeaker = findStageMainSpeaker()
        if (null == mainSpeaker) {
            Logger.w(TAG, "can not find main speaker; speaker not connect bt,or not master,or ac is off")
            return false
        }
        val linkedSpeakerId = lightInfo.speakerIDtoLight
        if (!linkedSpeakerId.equals(mainSpeaker.macAddressCRC(), ignoreCase = true)) {
            Logger.w(
                TAG,
                "can not stage: device follows speaker's id is not main speaker's id. follow id is: $linkedSpeakerId ;" + " main speaker id " + "is: ${mainSpeaker.macAddressCRC()}"
            )
            return false
        }
        if (mainSpeaker is OneDevice) {
            //mainSpeaker is Ultimate
            if (!mainSpeaker.isWiFiOnline && !mainSpeaker.syncGattConnectWithTimeout(Utils.getApp(), timeoutMills = 20 * 1000)) {
                Logger.e(TAG, "connect main speaker failed $mainSpeaker")
                return false
            }
            val resp = mainSpeaker.syncGetGeneralConfig(GeneralConfigType(TypeStickNumber), TAG)
            return resp?.success() == true && (resp.value is Number)
        } else if (mainSpeaker is PartyBoxDevice) {
            //mainSpeaker is Partybox
            if (!mainSpeaker.syncGattConnectWithTimeout(Utils.getApp())) {
                Logger.e(TAG, "connect main speaker failed $mainSpeaker")
                return false
            }
            val featureInfo = mainSpeaker.syncGetDeviceFeatureInfoWithTimeout(protocol = mainSpeaker.getBLEProtocol())
            if (null == featureInfo) {
                Logger.e(TAG, "read or write feature info error $mainSpeaker")
                return false
            }
            if (!featureInfo.supportPartyLightStage) {
                Logger.e(TAG, "not support stage $mainSpeaker")
            }
            return featureInfo.supportPartyLightStage
        }
        Logger.e(TAG, "not support device $mainSpeaker")
        return false
    }
}

private const val TAG = "PartyLightHybridViewModel"

sealed class PLVMEvent {
    data class ChooseColor(val currentColor: Int? = null, val onPickedColor: (Int) -> Unit) : PLVMEvent()
    data class EnterProductInfo(val uid: String) : PLVMEvent()
    data class EnterOta(val uid: String) : PLVMEvent()
    data class EnterPartyStage(val uid: String, val groupSize: Int, val showGuideFirst: Boolean, val onGuideAppeared: () -> Unit) : PLVMEvent()
    data class EnterDismissPartyStage(val uid: String) : PLVMEvent()
    data class ShowPartyStageGuide(val onGuideAppeared: () -> Unit) : PLVMEvent()
}

/**
 * 与js交互的设备信息的模型
 */
private data class JsDevInfo(
    @SerializedName("pId")
    val pId: String? = null,
    @SerializedName("cId")
    val cId: String? = null,
    @SerializedName("deviceName")
    val deviceName: String? = null,
    @SerializedName("fwVersion")
    val fwVersion: String? = null,
    @SerializedName("serialNumber")
    val serialNumber: String? = null,
)

/**
 * 与js交互的PartyLight业务的模型
 */
data class JsPartyLightFeature(
    @SerializedName("auracastEnable")
    val auracastEnable: Boolean? = null,
    @SerializedName("danceModeEnable")
    val danceModeEnable: Boolean? = null,
    @SerializedName("soundDetectionEnable")
    val soundDetectionEnable: Boolean? = null,
    @SerializedName("backLightEnable")
    val backLightEnable: Boolean? = null,
    @SerializedName("speedValue")
    val speedValue: Int? = null,
    @SerializedName("brightnessValue")
    val brightnessValue: Int? = null,
    @SerializedName("memberCount")
    val memberCount: Int? = null,
    @SerializedName("connectedPartybox")
    val connectedPartybox: ConnectedPartybox? = null,
    //js用来判断stage入口的小绿点是否展示，native无法使用
    @SerializedName("stageEnable")
    val stageEnable: Boolean? = null,
) {
    data class ConnectedPartybox(
        @SerializedName("status")
        val status: Int? = null,
        @SerializedName("name")
        val name: String? = null,
        @SerializedName("pId")
        val pId: String? = null,
        @SerializedName("cId")
        val cId: String? = null,
    ) {
        enum class ConnStatus(val value: Int) {
            Waiting(0),
            Connected(1),
            ConnectedOther(2),
        }

        companion object {
            fun fromLightAndDev(speakerIDtoLight: String?, auracastMode: AuracastMode?): ConnectedPartybox? {
                return run<ConnectedPartybox?> {
                    if (null == auracastMode || auracastMode == AuracastMode.OFF) {
                        return@run null
                    }
                    val mainSpeaker = findStageMainSpeaker()
                    ConnectedPartybox(
                        status = when (auracastMode) {
                            AuracastMode.ON, AuracastMode.ON_NOT_LINKED -> ConnStatus.Waiting.value

                            AuracastMode.ON_LINKED -> {
                                if (mainSpeaker?.macAddressCRC().equals(speakerIDtoLight, true)) ConnStatus.Connected.value
                                else ConnStatus.ConnectedOther.value
                            }

                            else -> null
                        },
                        name = mainSpeaker?.deviceName,
                        pId = mainSpeaker?.pid,
                        cId = mainSpeaker?.cid,
                    )
                }
            }
        }
    }

    fun convert2PLLightInfo(): PLLightInfo = PLLightInfo(
        backLightMode = backLightEnable.toSwitch(),
        lEDMovementSpeed = speedValue,
        lightBrightness = brightnessValue,
    )

    fun convert2PLDevInfo(): PLDevInfo = PLDevInfo(
        auracastMode = auracastEnable.toAuracastMode(),
        danceMode = danceModeEnable.toSwitch(),
        soundDetection = soundDetectionEnable.toSwitch(),
    )

    companion object {
        fun fromLightOrDev(
            plLightInfo: PLLightInfo? = null,
            plDevInfo: PLDevInfo? = null,
        ) = JsPartyLightFeature(
            backLightEnable = plLightInfo?.backLightMode.toEnable(),
            speedValue = plLightInfo?.lEDMovementSpeed,
            brightnessValue = plLightInfo?.lightBrightness,
            auracastEnable = plDevInfo?.auracastMode.toEnable(),
            danceModeEnable = plDevInfo?.danceMode.toEnable(),
            soundDetectionEnable = plDevInfo?.soundDetection.toEnable(),
        )
    }
}

private fun JsLightInfo.convert2PLLightInfo(): PLLightInfo {
    val targetPatternId = this.patternId
    val pattern = JsLightInfo.JsPatternId.mapByKey(key = targetPatternId)

    return PLLightInfo(
        pattern = pattern?.plPatter,
        patternLooping = color?.let { if (it.isEmpty()) PLLightInfo.PatternLooping.COLOR_LOOP else PLLightInfo.PatternLooping.STATIC_COLOR },
        currentColor = color?.toColorInt(),
    )
}


