package com.harman.webview.models

import android.app.Activity
import androidx.annotation.MainThread
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import com.harman.command.one.bean.GetGroupInfo
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.session.OneBusiness
import com.harman.multichannel.repository.CacheRepository
import com.harman.oobe.wifi.LocalCacheAdapter.deleteDeviceCache

import com.harman.product.info.ProductInfoActivity
import com.harman.webview.AddNewProductHybridActivity
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.BeanMap.toSecondaryDeviceInfoList
import com.harman.webview.CommonHybridActivity
import com.harman.webview.EnumDeviceInfoStatus
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.WhatIsNewHybridActivity
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by gerrardzhang on 2024/10/12.
 *
 * @param deviceDummy A fake device instance generated from cache.
 */
class OneDeviceOfflineViewModelFactory(
    private val deviceDummy: Device
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OneDeviceOfflineViewModel(deviceDummy = deviceDummy) as T
    }
}

class OneDeviceOfflineViewModel(
    private val deviceDummy: Device
) : ViewModel(), IHybridViewModel<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem,
        OneDevice> {

    override val tag: String = TAG

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    override val device: Device = deviceDummy

    @MainThread
    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: JsCallback
    ) {
        super.onCustomEvent(activity, eventName, data, callback)
        Logger.d(TAG, "onCustomEvent() >>> event[$eventName] ${data?.toString()}")
        when (eventName) {
            Events.GET_DEVICE_INFO -> {
                handleGetDeviceInfo()
                handleSecondaryDeviceInfos()
            }

            Events.ENTER_PRODUCT_INFO -> {
                handleEnterProductInfo(activity = activity, device = deviceDummy, callback = callback)
            }

            Events.REMOVE_OFFLINE_PRODUCT -> {
                handleRemoveOfflineProduct(activity = activity)
            }

            Events.SETUP_PRODUCT -> {
                handleSetupProduct(activity = activity, device = deviceDummy)
            }

            Events.ENTER_QSG -> {
                val pId = data?.get("pId")?.asString
                handleEnterQSG(activity = activity, callback = callback, pId)
            }

            Events.ENTER_PRODUCT_SUPPORT -> {
                val pId = data?.get("pId")?.asString
                handleEnterProductSupport(activity = activity, callback = callback, pId)
            }

            Events.SHOW_WHATS_NEW_DIALOG -> {
                WhatIsNewHybridActivity.launchActivity(activity, deviceDummy)
            }
        }
    }

    private fun handleSetupProduct(activity: CommonHybridActivity, device: Device) {
        val pid = deviceDummy.pid
        if (pid.isNullOrBlank()) {
            Logger.e(TAG, "handleSetupProduct() >>> missing pid for device[${device.UUID}]")
            return
        }

        AddNewProductHybridActivity.launchSetupProduct(activity = activity, pid = pid)
    }

    private fun handleRemoveOfflineProduct(activity: CommonHybridActivity) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val uuid = deviceDummy.UUID
            if (!uuid.isNullOrBlank()) {
                withContext(DISPATCHER_IO) {
                    when (deviceDummy) {
                        is OneDevice -> deviceDummy.deleteDeviceCache(ctx = activity)
                        is PartyBoxDevice -> deviceDummy.deleteDeviceCache(ctx = activity)
                        is PartyBandDevice -> deviceDummy.deleteDeviceCache(ctx = activity)
                    }

                    CacheRepository.getInstance().removeGroupCache(uuid)
                }

                Logger.i(TAG, "handleRemoveOfflineProduct() >>> clear cache about[$uuid]")
            }

            activity.finish()
        }
    }

    private fun handleEnterQSG(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterQSG()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlQsg ?: return
        Logger.d(TAG, "launchQsg pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("jbl_Quick_Start_Guide"))
        callback.invoke("0", null, null)
    }

    private fun handleEnterProductSupport(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterProductSupport()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlSupport ?: return
        Logger.d(TAG, "handleEnterProductSupport pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("harmanbar_jbl_Product_Support"))
        callback.invoke("0", null, null)
    }

    private fun loadingUrlInAppWebView(activity: CommonHybridActivity, linkUrl: String, title: String) {
        Logger.d(TAG, "loadingUrlInAppWebView Url: $linkUrl")
        QuickStartGuideWebActivity.launchQuickStartGuideWebActivity(activity, linkUrl, title)
    }

    private fun handleGetDeviceInfo() {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            // notify first with no GroupInfo to avoid empty UI
            notify(groupInfo = null)

            val groupInfo = withContext(DISPATCHER_DEFAULT) {
                CacheRepository.getInstance().getGroupInfo(deviceDummy.UUID)
            }

            notify(groupInfo = groupInfo)
        }
    }

    private fun notify(groupInfo: GetGroupInfo?) {
        when (deviceDummy) {
            is OneDevice -> {
                notifyDeviceInfo(info = deviceDummy.toDeviceInfo(status = EnumDeviceInfoStatus.DISCONNECTED, groupInfo = groupInfo))
            }

            is PartyBoxDevice -> {
                notifyDeviceInfo(info = deviceDummy.toDeviceInfo(connectStatus = EnumDeviceInfoStatus.DISCONNECTED.value))
            }

            is PartyBandDevice -> {
                notifyDeviceInfo(info = deviceDummy.toDeviceInfo(connectStatus = EnumDeviceInfoStatus.DISCONNECTED.value))
            }
        }
    }

    private fun handleSecondaryDeviceInfos() {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            when (deviceDummy) {
                is OneDevice -> {
                    notifySecondaryDeviceInfos(
                        deviceDummy.toSecondaryDeviceInfoList(groupInfo = null, groupParameterRsp = null)
                    )

                    val groupInfo = CacheRepository.getInstance().getGroupInfo(deviceDummy.UUID)
                    val groupParameter = CacheRepository.getInstance().getGroupParameter(deviceDummy.UUID)

                    notifySecondaryDeviceInfos(
                        deviceDummy.toSecondaryDeviceInfoList(groupInfo = groupInfo, groupParameterRsp = groupParameter)
                    )
                }
            }
        }
    }

    private fun handleEnterProductInfo(
        activity: Activity,
        device: Device,
        callback: JsCallback
    ) {
        Logger.d(TAG, "handleEnterProductInfo() >>> ")
        ProductInfoActivity.launchProductInfoPage(activity, device)
        callback.invoke("0", null, null)
    }

    companion object {
        private const val TAG = "OneDeviceOfflineViewModel"
    }
}