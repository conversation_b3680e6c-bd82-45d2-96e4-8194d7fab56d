package com.harman.webview.models

import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.google.gson.JsonObject
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.session.OneBusiness
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.CommonHybridActivity
import com.harman.webview.EnumDeviceInfoStatus
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.wifiaudio.model.DeviceItem
import java.lang.ref.WeakReference
import org.json.JSONObject

class WhatIsNewViewModelFactory(
    private val activity: Activity,
    private val device: OneDevice?
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return WhatIsNewViewModel(weakActivity = WeakReference(activity),device) as T
    }
}

class WhatIsNewViewModel(
    private val weakActivity: WeakReference<Activity>,
    override val device: OneDevice?
) : ViewModel(), IHybridViewModel<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem,
        OneDevice>
{

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        super.onCustomEvent(activity, eventName, data, callback)
        when (eventName) {
            Events.DISMISS_WHATS_NEW_DIALOG -> {
                activity.finishAfterTransition()
            }

//            Events.GET_DEVICE_INFO -> {
//                notifyDeviceInfo()
//            }

        }
    }

    private fun notifyDeviceInfo() {
        device?.also {
            notifyDeviceInfo(info = it.toDeviceInfo(status = if (it.isWiFiOnline) EnumDeviceInfoStatus.WIFI_CONNECTED else EnumDeviceInfoStatus.BLE_CONNECTED))

        }
}



    override val tag: String = TAG

    companion object {
        private const val TAG = "WhatIsNewViewModel"
    }
}