package com.harman.webview.models

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.Base64
import androidx.activity.result.ActivityResult
import androidx.annotation.CallSuper
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.collection.LruCache
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.google.gson.JsonObject
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.bar.app.BuildConfig
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.session.BaseBusinessSession
import com.harman.context
import com.harman.discover.bean.BaseDevice
import com.harman.discover.bean.Device
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.safeResume
import com.harman.genMediaStoreSignature
import com.harman.log.Logger
import com.harman.music.Tools.isUriConst
import com.harman.music.service.EnumMusicServiceSource
import com.harman.product.info.ProductInfoActivity.Companion.isOtaSuccess
import com.harman.report.OneCloudAnalyticsTemp
import com.harman.report.OneCloudReporter
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.webview.BatteryInfo
import com.harman.webview.CommonHybridActivity
import com.harman.webview.DeviceInfo
import com.harman.webview.DiscoverModel
import com.harman.webview.EventAction
import com.harman.webview.GetEQ
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.JSHKStudioLightInfo
import com.harman.webview.JsGetHorizon3FeatureInfo
import com.harman.webview.JsLightInfo
import com.harman.webview.MomentStatus
import com.harman.webview.MusicServiceList
import com.harman.webview.NearbyProduct
import com.harman.webview.NearbyProductList
import com.harman.webview.NetworkInfo
import com.harman.webview.OTAStatus
import com.harman.webview.PlayInfo
import com.harman.webview.ProductBaseInfo
import com.harman.webview.ProductRenderImage
import com.harman.webview.SecondaryDeviceInfo
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.debug.OneCloudReportLocalCache
import com.wifiaudio.model.DeviceItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.ByteArrayOutputStream


/**
 * Created by gerrardzhang on 2024/5/23.
 *
 * Universal js callback and notification handler for all kinds of hybrid scenarios.
 */
typealias JsCallback = (code: String, msg: String?, passBack: JSONObject?) -> Unit

typealias GattStatusListener = (status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) -> Unit

typealias BrEdrStatusListener = (status: EnumConnectionStatus, session: BaseBrEdrSession<*, *, *>) -> Unit

typealias HybridViewModel = IHybridViewModel<*, *, *, *, *, *, *>

interface IHybridViewModel<
        out GattBusiness : BaseBusinessSession,
        out SppBusiness : DefaultSppBusinessSession,
        out BTDeviceType : BaseBTDevice<Role, AuraCastRole>,
        Role,
        AuraCastRole,
        OfflineDummyType,
        DeviceType : BaseDevice<GattBusiness, SppBusiness, BTDeviceType, Role, AuraCastRole, OfflineDummyType>
        > : DefaultLifecycleObserver {

    val tag: String

    var notificationInvoker: IHybridNotification?

    var gattConnectStatusListener: GattStatusListener?

    var brEdrConnectStatusListener: BrEdrStatusListener?

    var eventListener: IViewModelEvents?

    val device: Device?

    /**
     * handle universal hybrid interface here
     */
    @MainThread
    @CallSuper
    suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: JsCallback
    ) {
        Logger.d(tag, "onCustomEvent() >>> event[$eventName] ${data?.toString()}")

        when (eventName) {
            Events.GET_PRODUCT_IMAGE -> {
                handleGetProductImage(activity = activity, data = data, callback = callback)
            }

            Events.VIBRATION -> {
                handleVibration(activity, callback)
            }

            Events.EVENT_ACTION -> {
                handleEventAction(activity = activity, data = data, callback = callback)
            }
        }
    }

    fun handleVibration(
        activity: CommonHybridActivity,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager =
                activity.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as? VibratorManager
            vibratorManager?.defaultVibrator
        } else {
            activity.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
        }

        if (null != vibrator) {
            vibrator.vibrate(VibrationEffect.createOneShot(10, 128))
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    fun notifyOtaStatus(status: OTAStatus) {
        val jsonStr = GsonUtil.parseBeanToJson(status)
        Logger.d(tag, "notifyOtaStatus() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.OTA_STATUS, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyPlayInfo(info: PlayInfo?) {
        Logger.d(tag, "notifyPlayInfo() >>> $info")
        info ?: run {
            notificationInvoker?.notifyObj(Notifications.PLAY_INFO, null)
            return
        }

        val jsonStr = GsonUtil.parseBeanToJson(info)
        notificationInvoker?.notifyObj(Notifications.PLAY_INFO, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyDeviceInfo(info: DeviceInfo) {
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyDeviceInfo() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.DEVICE_INFO, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyLightInfo(info: JsLightInfo) {
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyLightInfo() >>> $info")
        notificationInvoker?.notifyObj(Notifications.LIGHT_INFO, JSONObject(jsonStr))
    }

    @MainThread
    fun notifySecondaryDeviceInfos(info: List<SecondaryDeviceInfo>?) {
        val jsonStr = GsonUtil.parseBeanToJson(info ?: emptyList<SecondaryDeviceInfo>())
        Logger.d(tag, "notifySecondaryDeviceInfos() >>> $jsonStr")
        notificationInvoker?.notifyArray(Notifications.SECONDARY_DEVICE_INFOS, JSONArray(jsonStr))
    }

    @MainThread
    fun notifyBatteryInfo(info: List<BatteryInfo>?) {
        val jsonStr = GsonUtil.parseBeanToJson(info ?: emptyList<BatteryInfo>())
        Logger.d(tag, "notifyBatteryInfo() >>> $jsonStr")
        notificationInvoker?.notifyArray(Notifications.BATTERY_INFO, JSONArray(jsonStr))
    }

    @MainThread
    fun notifyEQInfo(info: GetEQ) {
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyEQInfo() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.EQ_INFO, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyNearbyProduct(info: NearbyProductList?) {
        val jsonStr = GsonUtil.parseBeanToJson(info ?: emptyList<NearbyProduct>())
        Logger.d(tag, "notifyNearbyProduct() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.NEARBY_PRODUCT, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyDiscoveredProduct(model: DiscoverModel?) {
        model?.let {
            val jsonStr = GsonUtil.parseBeanToJson(it)
            Logger.d(tag, "notifyDiscoveredProduct() >>> $jsonStr")
            notificationInvoker?.notifyObj(Notifications.DISCOVERED_PRODUCT, JSONObject(jsonStr))
        }
    }

    @MainThread
    fun notifyMusicService(info: MusicServiceList) {
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyMusicService() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.MUSIC_SERVICE, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyNetworkInfo(info: NetworkInfo) {
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyNetworkInfo() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.NETWORK_INFO, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyMomentStatus(status: MomentStatus) {
        val jsonStr = GsonUtil.parseBeanToJson(status)
        Logger.d(tag, "notifyMomentStatus() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.MOMENT_STATUS, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyHKStudioLightInfo(info: JSHKStudioLightInfo) {
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyHKStudioLightInfo() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.HK_STUDIO_LIGHT_INFO, JSONObject(jsonStr))
    }

    @MainThread
    fun notifyHorizon3Feature(info: JsGetHorizon3FeatureInfo){
        val jsonStr = GsonUtil.parseBeanToJson(info)
        Logger.d(tag, "notifyHorizon3Feature() >>> $jsonStr")
        notificationInvoker?.notifyObj(Notifications.HORIZON3_FEATURE, JSONObject(jsonStr))
    }

    @MainThread
    private suspend fun handleGetProductImage(
        activity: FragmentActivity,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        val jsonStr = data?.toString()

        val baseInfo = if (jsonStr.isNullOrBlank()) {
            null
        } else {
            GsonUtil.parseJsonToBean(jsonStr, ProductBaseInfo::class.java)
        }

        baseInfo ?: run {
            callback.invoke("-1", "Input invalid message from HTML", null)
            return
        }

        val renderPath = baseInfo.pid?.let { pid ->
            AppConfigurationUtils.getModelRenderPath(
                pid,
                baseInfo.colorId
            )
        }

        if (renderPath.isNullOrBlank()) {
            callback.invoke("-1", "empty render path by pid[${baseInfo.pid}]", null)
            return
        }

        val base64 = loadProductImgBase64WithTimeout(
            context = activity,
            uri = renderPath,
            pid = baseInfo.pid,
            format = Bitmap.CompressFormat.PNG
        )

        val content = base64 ?: run {
            callback.invoke("-1", "fail to load bitmap by path: $renderPath", null)
            renderPath
        }

        val renderImage = ProductRenderImage(base64String = content)
        val callbackObj = JSONObject(GsonUtil.parseBeanToJson(renderImage))
        callback.invoke("0", "", callbackObj)
    }

    @MainThread
    suspend fun loadProductImgBase64WithTimeout(
        context: Context?,
        uri: String,
        pid: String?,
        format: Bitmap.CompressFormat
    ) = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = 5 * 1000) {
        Logger.d(tag, "loadProductImgBase64WithTimeout() >>> uri: $uri. format[${format.name}]")
        if (uri.isNotBlank()) {
            productImgBase64Lru.get(uri)?.let { base64String -> // hit cache
                return@repeatWithTimeout base64String
            }
        }

        val ctx = context ?: return@repeatWithTimeout null
        val bitmap = loadUriAsBitmap(tag = tag, uri = uri, ctx = ctx, pid = pid)
        Logger.d(tag, "loadProductImgBase64WithTimeout() >>> bmp.size[${bitmap?.byteCount}]")

        bitmap ?: return@repeatWithTimeout null

        val base64String = withContext(DISPATCHER_IO) {
            bitmap.compressAndToBase64(maxByteSize = MAX_BITMAP_SIZE, format)
        }

        Logger.d(tag, "loadProductImgBase64WithTimeout() >>> base64.size[${base64String?.length}]")
        if (!base64String.isNullOrBlank() && uri.isNotBlank()) {
            productImgBase64Lru.put(uri, base64String)
        }

        return@repeatWithTimeout base64String
    }

    @CallSuper
    open fun handleStartForResult(result: ActivityResult) {
        Logger.d(tag, "handleStartForResult() >>> $result")
        when {
            Activity.RESULT_OK == result.resultCode && result.isOtaSuccess()-> {
                Logger.d(tag, "handleStartForResult() >>> remove ota info from")
                notifyOtaStatus(OTAStatus(available = false))
            }
        }
    }

    @MainThread
    private suspend fun loadUriAsBitmap(tag: String, uri: String?, ctx: Context?, pid: String?): Bitmap? {
        if (uri.isNullOrBlank()) {
            Logger.w(tag, "loadUriAsBitmap() >>> invalid uri")
            return null
        }

        ctx ?: run {
            Logger.w(tag, "loadUriAsBitmap() >>> missing context. $uri")
            return null
        }

        return suspendCancellableCoroutine { continuation ->
            Glide.with(ctx)
                .load(uri)
                .asBitmap()
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .signature(genMediaStoreSignature(imgPath = uri, pid = pid, tag = "loadAsBitmap"))
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap?, glideAnimation: GlideAnimation<in Bitmap>?) {
                        Logger.d(tag, "loadUriAsBitmap() >>> load success. bitmap.size[${resource?.byteCount}]. uri:$uri.")
                        continuation.safeResume(resource)
                    }

                    override fun onLoadFailed(e: Exception?, errorDrawable: Drawable?) {
                        Logger.w(tag, "loadUriAsBitmap() >>> load fail:$uri. exception:$e")
                        continuation.safeResume(null)
                    }
                })
        }
    }

    @WorkerThread
    private fun Bitmap.compressAndToBase64(maxByteSize: Int, format: CompressFormat? = Bitmap.CompressFormat.PNG): String? {
        val sourceBmp = this
        if (maxByteSize <= 0 || sourceBmp.byteCount <= 0) {
            return null
        }

        var quality = 100
        val byteArrayOutputStream = ByteArrayOutputStream()

        if (CompressFormat.PNG == format) {
            sourceBmp.compress(CompressFormat.PNG, quality, byteArrayOutputStream)
        } else {
            do {
                sourceBmp.compress(format ?: CompressFormat.PNG, quality, byteArrayOutputStream)
                quality -= 10
            } while (byteArrayOutputStream.size() > maxByteSize && quality > 10)
        }

        return Base64.encodeToString(
            byteArrayOutputStream.toByteArray(),
            Base64.NO_WRAP // disable '\n' in Base64
        )
    }

    @MainThread
    suspend fun loadAlbumCoverBase64WithTimeout(
        context: Context?,
        uri: String,
        pid: String?,
        format: Bitmap.CompressFormat,
        musicService: EnumMusicServiceSource?,
        deviceItem: DeviceItem?,
        width: Int,
        height: Int,
        logTag: String
    ) = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = 5 * 1000) {
        Logger.d(tag, "loadAlbumCoverBase64WithTimeout() >>> format[${format.name}] musicService[$musicService] uri: $uri")
        if (uri.isNotBlank() && !musicService.isUriConst()) {
            productImgBase64Lru.get(uri)?.let { base64String -> // hit cache
                return@repeatWithTimeout base64String
            }
        }

        val bitmap = com.harman.music.Tools.syncLoadAlbumCover(context = context, albumUrl = uri,
            source = musicService, deviceItem = deviceItem, width = width, height = height, logTag = logTag)
        Logger.d(tag, "loadAlbumCoverBase64WithTimeout() >>> bmp.size[${bitmap?.byteCount}]")

        bitmap ?: return@repeatWithTimeout null

        val base64String = withContext(DISPATCHER_IO) {
            bitmap.compressAndToBase64(maxByteSize = MAX_BITMAP_SIZE, format)
        }

        Logger.d(tag, "loadAlbumCoverBase64WithTimeout() >>> base64.size[${base64String?.length}]")
        if (!base64String.isNullOrBlank() && uri.isNotBlank()) {
            productImgBase64Lru.put(uri, base64String)
        }

        return@repeatWithTimeout base64String
    }

    fun handleEventAction(
        activity: CommonHybridActivity,
        data: JsonObject?,
        callback: JsCallback
    ) {
        val jsonStr = data?.toString() ?: run {
            callback.invoke("-1", null, null)
            return
        }

        val actionItem = GsonUtil.parseJsonToBean(jsonStr, EventAction::class.java)?.diActionItem
        if (actionItem.isNullOrBlank()) {
            callback.invoke("-1", null, null)
            return
        }

        FirebaseEventManager.report(
            eventName = EventUtils.EventName.EVENT_ACTION,
            device = device,
            dimensions = mapOf(
                EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.ACTION_MAIN_SCREEN.value,
                EventUtils.Dimension.DI_ACTION_ITEM to actionItem
            )
        )
    }

    fun reportV5Analytics(owner: LifecycleOwner, scope: CoroutineScope) {
        val device = device ?: return

        if (!OneCloudReporter.isUploadAvailable(device = device)) {
            return
        }

        val context = owner.context()

        scope.launch(DISPATCHER_FAST_MAIN) {
            val reportJObj = OneCloudAnalyticsTemp.formatV5AnalyticsReport(device = device)
            val reportJStr = try {
                reportJObj.toString()
            } catch (e: Exception) {
                Logger.e(tag, "reportV5Analytics() >>> exception while converting report json obj to string")
                return@launch
            }

            val result = withContext(DISPATCHER_API) {
                OneCloudReporter.upload(reportJStr = reportJStr)
            }

            Logger.d(tag, "reportV5Analytics() >>> report result[$result]")
            if (result) {
                OneCloudReporter.markUpload(device = device)

                if (BuildConfig.DEBUG) {
                    CoroutineScope(DISPATCHER_IO).launch {
                        OneCloudReportLocalCache.saveDeviceReport(context = context, device = device, jsStr = reportJStr)
                    }
                }
            }
        }
    }

    companion object {
        /**
         * store the latest several result after [handleGetProductImage] to save network flow and calculation.
         * Key: Uri String
         * Value: Base64 String
         */
        private val productImgBase64Lru = LruCache<String, String>(5)

        private const val MAX_BITMAP_SIZE = 200 * 1024
    }
}