package com.harman.webview.models

import android.app.Activity
import android.app.Dialog
import android.bluetooth.BluetoothDevice
import android.content.Intent
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.MainThread
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.btstereopair.BTStereoPairActivity
import com.harman.command.one.bean.ColorPicker
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.command.partybox.gatt.eq.EQBean
import com.harman.command.partybox.gatt.light.Color
import com.harman.command.partybox.gatt.light.EnumLightPattern
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.command.partybox.gatt.sleep.SleepModeInfo
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.PartyBoxSppBusiness
import com.harman.connect.blockGetPlayerStatusWithTimeout
import com.harman.connect.isHomeBtCategory
import com.harman.connect.isSupportLightControl
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.session.PartyBoxBusinessSession
import com.harman.connect.syncActivePresetStation
import com.harman.connect.syncBrEdrConnectWithTimeout
import com.harman.connect.syncFactoryResetWithTimeout
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.connect.syncGetAdvancedEQWithTimeout
import com.harman.connect.syncGetAlarmInfo
import com.harman.connect.syncGetAmbientLightInfo
import com.harman.connect.syncGetDeviceFeatureInfoWithTimeout
import com.harman.connect.syncGetFirmwareVersionWithTimeout
import com.harman.connect.syncGetLastStationWithTimeout
import com.harman.connect.syncGetLightInfoWithTimeout
import com.harman.connect.syncGetPresetRadioListWithTimeout
import com.harman.connect.syncGetScreenDisplayInfo
import com.harman.connect.syncGetSleepModeInfo
import com.harman.connect.syncGetStudioLightInfoWithTimeout
import com.harman.connect.syncSetSleepModeInfo
import com.harman.control.GroupRenameActivity
import com.harman.control.StereoSwitchChannelDialog
import com.harman.discover.DeviceStore
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.jsIdToLightToken
import com.harman.discover.info.AmbientLightInfo
import com.harman.discover.info.AudioSource
import com.harman.discover.info.AudioSource.Companion.isPlayerSource
import com.harman.discover.info.AudioSource.Companion.isRadioSource
import com.harman.discover.info.GeneralRole
import com.harman.discover.info.PlayerStatus
import com.harman.discover.info.ScreenDisplayInfo
import com.harman.discover.util.Tools.foundSecondaryDevice
import com.harman.discover.util.Tools.roundPercentage
import com.harman.discover.util.Tools.toJSONObj
import com.harman.effectlab.EffectLabActivity
import com.harman.eq.EQExts.toEQSettings
import com.harman.getBLEProtocol
import com.harman.hkone.whatsnew.VersionCompareUtil
import com.harman.log.Logger
import com.harman.multichannel.UngroupDialog
import com.harman.music.Tools.getRadioSubTitle
import com.harman.music.Tools.getRadioTitle
import com.harman.music.Tools.isRadioPlay
import com.harman.music.Tools.isRadioStyle
import com.harman.music.Tools
import com.harman.music.Tools.millsToSecs
import com.harman.music.Tools.radioType
import com.harman.music.player.FullPlayerActivity
import com.harman.music.player.PartyBoxPlayerViewModel
import com.harman.music.player.mapPlayerViewModel
import com.harman.music.player.toEnumInputSource
import com.harman.oobe.wifi.LocalCacheAdapter.deleteDeviceCache
import com.harman.ota.PartyOtaFetchTask
import com.harman.ota.RemoteUpdateModel
import com.harman.ota.partybox.PartyBoxOtaActivity
import com.harman.partybox.EnumTwsMode
import com.harman.partybox.PartyBoxTwsActivity
import com.harman.portalActivity
import com.harman.product.controls.screendisplay.horizon.SetBedtimeActivity
import com.harman.product.controls.screendisplay.horizon.SetScreenSaverActivity
import com.harman.product.info.ProductInfoActivity
import com.harman.product.rename.RenameDeviceActivity
import com.harman.product.setting.IRestoreFactoryConfirm
import com.harman.product.setting.RestoreFactoryConfirmDialog
import com.harman.product.setting.activity.BaseProductSettingsActivity
import com.harman.radio.RadioActivity
import com.harman.singleObserve
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.webview.Alarm
import com.harman.webview.AmbientLight
import com.harman.webview.BatteryInfo
import com.harman.webview.BeanMap.collectLightInfo
import com.harman.webview.BeanMap.collectStudioLightInfo
import com.harman.webview.BeanMap.toBatteryInfo
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.BeanMap.toSecondaryDeviceInfoList
import com.harman.webview.BeanMap.toTargetPlayFunction
import com.harman.webview.CommonHybridActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.JsGetHorizon3FeatureInfo
import com.harman.webview.JsGetHorizon3SleepMode
import com.harman.webview.OTAStatus
import com.harman.webview.PlayInfo
import com.harman.webview.Radio
import com.harman.webview.ScreenDisplay
import com.harman.webview.SleepMode
import com.harman.webview.SoundSource
import com.harman.webview.SwitchFeedbackToneReq
import com.harman.webview.repository.CustomEQRepository
import com.harman.webview.repository.HKStudioRepository
import com.harman.widget.ColorPickerDialog
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity
import com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.BTHorizonAlarmsActivity
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.Locale

/**
 * Created by gerrardzhang on 2024/5/23.
 */
class PartyBoxHybridViewModelFactory(
    private val activity: DeviceControlHybridActivity,
    private val uuid: String,
    private val launcher: ActivityResultLauncher<Intent>?
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return PartyBoxHybridViewModel(
            weakActivity = WeakReference(activity),
            uuid = uuid,
            launcher = launcher
        ) as T
    }
}

class PartyBoxHybridViewModel(
    private val weakActivity: WeakReference<DeviceControlHybridActivity>,
    /**
     * The Device instance might be changed after OTA success so we only provide uuid and plz
     * refer [device] function when you need device instance every time.
     */
    private val uuid: String,
    private val launcher: ActivityResultLauncher<Intent>?
) : ViewModel(), IHybridViewModel<
        PartyBoxBusinessSession,
        PartyBoxSppBusiness,
        PartyBoxBTDevice,
        GeneralRole,
        GeneralRole,
        PartyBoxBTDevice,
        PartyBoxDevice> {

    @Volatile
    private var remoteUpdateModel: RemoteUpdateModel? = null

    override var notificationInvoker: IHybridNotification? = null

    override var eventListener: IViewModelEvents? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    private var customEQRepository: CustomEQRepository? = CustomEQRepository()

    private var hkStudioRepository: HKStudioRepository? = HKStudioRepository()

    override val device: PartyBoxDevice?
        get() = DeviceStore.findPartyBox(uuid)

    private val playerViewModel: PartyBoxPlayerViewModel?
        get() {
            val activity = weakActivity.get() ?: return null
            return device?.mapPlayerViewModel(activity) as? PartyBoxPlayerViewModel
        }

    @MainThread
    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        super.onCustomEvent(activity, eventName, data, callback)

        Logger.w(TAG, "onCustomEvent() >>> eventName: $eventName")

        val targetActivity = activity as DeviceControlHybridActivity

        when (eventName) {
            Events.PLAY_FUNCTION -> {
                handlePlayFunction(data = data, callback = callback)
            }

            Events.VOLUME_CHANGE -> {
                handleVolumeChange(data = data, callback = callback)
            }

            Events.GET_EQ -> {
                handleGetEQ(callback = callback, activity = targetActivity)
            }

            Events.SET_EQ -> {
                handleSetEQ(data = data, callback = callback, activity = targetActivity)
            }

            Events.GET_PLAY_INFO -> {
                handleGetPlayInfo(callback = callback)
            }

            Events.GET_OTA_STATUS -> {
                handleGetOtaStatus(activity = targetActivity, callback = callback)
            }

            Events.ENTER_OTA -> {
                handleEnterOTA(activity = targetActivity, callback = callback)
            }

            Events.ENTER_TWS_STEREO -> {
                handleEnterTwsStereo(activity = targetActivity, callback = callback)
            }

            Events.ENTER_TWS_STEREO_PREVIOUS -> {
                handleEnterTwsStereoPrevious(activity = targetActivity, callback = callback)
            }

            Events.UNGROUP -> {
                handleUnGroup(activity = targetActivity, callback = callback)
            }

            Events.GROUP_RENAME -> {
                handleGroupRename(activity = targetActivity, callback = callback)
            }

            Events.GROUP_CHANNEL_CHANGE -> {
                handleGroupChannelChange(activity = targetActivity, callback = callback)
            }

            Events.ENTER_SOUND_TUNING -> {
                handleGroupChannelChange(activity = targetActivity, callback = callback)
            }

            Events.EFFECT_LAB -> {
                handleEffectLabInfo(activity = targetActivity, callback = callback)
            }

            Events.GET_DEVICE_INFO -> {
                handleGetDeviceInfo(callback = callback)
            }

            Events.SET_LIGHT_CONTROL -> {
                handleSetLightControl(data = data, callback = callback)
            }

            Events.GET_LIGHT_INFO -> {
                handleGetLightInfo(callback = callback)
            }

            Events.ENTER_COLOR_PICKER -> {
                handleEnterColorPicker(activity = activity, callback = callback)
            }

            Events.ENTER_CREATE_A_SYSTEM -> {
                handleEnterCreateASystem(activity = activity, callback = callback)
            }

            Events.ENTER_RENAME -> {
                handleEnterRename(activity = activity, callback = callback)
            }

            Events.SWITCH_FEEDBACK_TONE -> {
                handleSwitchFeedbackTone(data = data, callback = callback)
            }

            Events.ENTER_PRODUCT_INFO -> {
                handleEnterProductInfo(activity = activity, callback = callback)
            }

            Events.RESTORE_FACTORY_SETTINGS -> {
                handleRestoreFactorySettings(activity = activity, callback = callback)
            }

            Events.ENTER_FULL_PLAYER -> {
                if (device?.isRadioStyle() == true) {
                    RadioActivity.portal(activity, device = device)
                } else {
                    handleEnterFullPlayer(device = device, activity = activity, callback = callback)
                }
            }

            Events.GET_HK_STUDIO_LIGHTINFO -> {
                handleGetHKStudioLightInfo(callback = callback)
            }

            Events.SET_HK_STUDIO_LIGHTINFO -> {
                handleSetHKStudioLightInfo(data = data, callback = callback)
            }

            Events.ENTER_PRODUCT_SETTINGS -> {
                handleProductSetting(activity = activity, callback = callback)
            }

            Events.ENTER_HORIZON3_FEATURE -> {
                handleHorizon3Feature(activity = activity, data = data, callback = callback)
            }

            Events.SET_HORIZON3_FEATURE -> {
                handleSetHorizon3Feature(
                    activity = activity,
                    data = data,
                    callback = callback
                )
            }

            Events.GET_HORIZON3_FEATURE -> {
                handleGetHorizon3Feature(
                    activity = activity,
                    data = data,
                    callback = callback
                )
            }

            Events.GET_SLEEP_MODE -> {
                handlerGetHorizon3SleepMode(
                    activity = activity,
                    data = data,
                    callback = callback
                )
            }

            Events.SET_SLEEP_MODE -> {
                handlerSetHorizon3SleepMode(
                    activity = activity,
                    data = data,
                    callback = callback
                )
            }

            Events.ENTER_QSG -> {
                val pId = data?.get("pId")?.asString
                handleEnterQSG(activity = activity, callback = callback, pId)
            }

            Events.ENTER_PRODUCT_SUPPORT -> {
                val pId = data?.get("pId")?.asString
                handleEnterProductSupport(activity = activity, callback = callback, pId)
            }
        }
    }

    private fun handleEnterQSG(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterQSG()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlQsg ?: return
        Logger.d(TAG, "launchQsg pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("jbl_Quick_Start_Guide"))
        callback.invoke("0", null, null)

    }

    private fun handleEnterProductSupport(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterProductSupport()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlSupport ?: return
        Logger.d(TAG, "handleEnterProductSupport pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("harmanbar_jbl_Product_Support"))
        callback.invoke("0", null, null)

    }

    private fun loadingUrlInAppWebView(activity: CommonHybridActivity, linkUrl: String, title: String) {
        Logger.d(TAG, "loadingUrlInAppWebView Url: $linkUrl")
        QuickStartGuideWebActivity.launchQuickStartGuideWebActivity(activity, linkUrl, title)
    }


    @MainThread
    private suspend fun handleGetHorizon3Feature(
        activity: Activity,
        data: JsonObject?,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.d(TAG, "handlerGetHorizon3ScreenDisplay >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", "device is null", null)
            return
        }

        val jsGetHorizon3FeatureInfo = JsGetHorizon3FeatureInfo()
        val protocol = device.getBLEProtocol()

        handleGetHorizon3FeatureNotify(jsGetHorizon3FeatureInfo)

        try {
            device.syncGetLastStationWithTimeout(device.getBLEProtocol(), logTag = TAG)
            device.syncGetPresetRadioListWithTimeout(device.getBLEProtocol(), logTag = TAG)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            if (device.syncGetScreenDisplayInfo(protocol)) {
                val bedtimeBrightness = device.screenDisplayInfo?.bedtimeBrightness ?: 0
                val screenDisplay = ScreenDisplay(brightness = bedtimeBrightness)
                jsGetHorizon3FeatureInfo.screenDisplay = screenDisplay
            } else {
                Logger.d(TAG, "handleGetHorizon3Feature >>> syncGetScreenDisplayInfo false")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            if (device.syncGetAmbientLightInfo(protocol)) {
                val ambientLightInfo = device.ambientLightInfo
                val ambientLight = AmbientLight(
                    enable = ambientLightInfo?.isActive ?: false,
                    brightness = ambientLightInfo?.lightBrightness ?: 0,
                    temperature = ambientLightInfo?.lightTemperatureColor ?: 0,
                )
                jsGetHorizon3FeatureInfo.ambientLight = ambientLight
            } else {
                Logger.d(TAG, "handleGetHorizon3Feature >>> syncGetAmbientLightInfo false")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            val result = device.syncGetAlarmInfo(logTag = TAG, protocol = protocol, requestType = AlarmInfo.RequestType.AlarmList)

            if (true == result) {
                device.alarmInfo?.alarmList?.let { info ->
                    val alarms = mutableListOf<Alarm>()
                    info.forEach {
                        if (it.isActiveAlarm == true) {
                            val formattedHour = String.format("%02d", it.alarmTime?.hour)
                            val formattedMinute = String.format("%02d", it.alarmTime?.minute)
                            val formattedAlarmTime = "${formattedHour}:${formattedMinute}"
                            alarms.add(Alarm(id = it.alarmID ?: 0, time = formattedAlarmTime, sunrise = it.isSunRise ?: false))
                        }
                    }
                    jsGetHorizon3FeatureInfo.alarms = alarms
                }
            } else {
                Logger.d(TAG, "handleGetHorizon3Feature >>> syncGetAlarmInfo false")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            val result = device.syncGetSleepModeInfo(logTag = TAG, protocol = protocol)

            if (result) {
                device.sleepModeInfo?.let { info ->
                    val sleepMode =
                        SleepMode(enable = info.isActive ?: false, soundTimer = (info.timer ?: 0) / 60, soundSource = getSourceName(info.ambientSound))
                    jsGetHorizon3FeatureInfo.sleepMode = sleepMode
                }
            } else {
                Logger.d(TAG, "handleGetHorizon3Feature >>> syncGetSleepModeInfo false")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        val resultCode = when {
            jsGetHorizon3FeatureInfo.screenDisplay != null ||
                    jsGetHorizon3FeatureInfo.ambientLight != null ||
                    jsGetHorizon3FeatureInfo.alarms != null ||
                    jsGetHorizon3FeatureInfo.radios != null ||
                    jsGetHorizon3FeatureInfo.sleepMode != null -> "0"

            else -> "-1"
        }

        val passBack = GsonUtil.parseBeanToJson(jsGetHorizon3FeatureInfo)
        callback.invoke(resultCode, null, JSONObject(passBack))
        Logger.d(TAG, "handleGetHorizon3Feature callback =${passBack}")
    }

    private fun handleGetHorizon3FeatureNotify(notifyHorizon3Feature: JsGetHorizon3FeatureInfo) {
        val device = device ?: run {
            Logger.e(TAG, "handleGetHorizon3FeatureNotify() >>> cant find device instance by uuid[$uuid]")
            return
        }

        device.radioInfo?.presetStationList?.let { presetStations ->
            if (presetStations.isNotEmpty()) {
                val radios = mutableListOf<Radio>()
                presetStations.forEach { item ->
                    var id = 0
                    val selected = item.state == RadioInfo.State.Playing
                    item.stationIndex?.let {
                        id = it
                    }
                    radios.add(Radio(id, selected))
                }
                notifyHorizon3Feature.radios = radios
            }
        }

        device.alarmInfo?.alarmList?.let { itAlarmList ->
            if (itAlarmList.isNotEmpty()) {
                val alarms = mutableListOf<Alarm>()
                itAlarmList.forEach {
                    if (it.isActiveAlarm == true) {
                        val formattedHour = String.format(Locale.ROOT, "%02d", it.alarmTime?.hour)
                        val formattedMinute = String.format(Locale.ROOT, "%02d", it.alarmTime?.minute)
                        val formattedAlarmTime = "${formattedHour}:${formattedMinute}"
                        alarms.add(Alarm(id = it.alarmID ?: 0, time = formattedAlarmTime, sunrise = it.isSunRise ?: false))
                    }
                }
                notifyHorizon3Feature.alarms = alarms
            }
        }

        device.sleepModeInfo?.let { info ->
            val sleepMode = SleepMode(
                enable = info.isActive ?: false,
                soundTimer = (info.timer ?: 0) / 60,
                soundSource = getSourceName(info.ambientSound)
            )
            notifyHorizon3Feature.sleepMode = sleepMode
        }

        device.screenDisplayInfo?.bedtimeBrightness?.let {
            val screenDisplay = ScreenDisplay(brightness = it)
            notifyHorizon3Feature.screenDisplay = screenDisplay
        }

        device.ambientLightInfo?.let {
            val ambientLight = AmbientLight(
                enable = it.isActive ?: false,
                brightness = it.lightBrightness ?: 50,
                temperature = it.lightTemperatureColor ?: 50,
            )
            notifyHorizon3Feature.ambientLight = ambientLight
        }

        notifyHorizon3Feature(notifyHorizon3Feature)
    }

    private fun getSourceName(ambientSound: SleepModeInfo.AmbientSound?): String {
        if (ambientSound?.type == SleepModeInfo.SoundType.Ambient) {
            return SleepModeInfo.Ambient.getWithCode(ambientSound.index).desc
        }
        // TODO: getRadioName
        return "radioName"
    }

    @MainThread
    private suspend fun handlerGetHorizon3SleepMode(
        activity: Activity, data: JsonObject?, callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.e(TAG, "handlerGetHorizon3SleepMode() >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
        }

        device.sleepModeInfo ?: run {
            try {
                val result = device.syncGetSleepModeInfo(logTag = TAG, protocol = device.getBLEProtocol())

                if (result) {
                    Logger.d(TAG, "handlerGetHorizon3SleepMode >>> syncGetSleepModeInfo true")
                } else {
                    Logger.d(TAG, "handlerGetHorizon3SleepMode >>> syncGetSleepModeInfo false")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        device.sleepModeInfo?.let {
            Logger.d(TAG, "handlerGetHorizon3SleepMode >>> sleepModeInfo = $it")
            val jsGetHorizon3SleepMode = JsGetHorizon3SleepMode()
            jsGetHorizon3SleepMode.soundTimer = (it.timer ?: 0) / 60
            jsGetHorizon3SleepMode.soundSource = SoundSource(
                id = it.ambientSound?.index ?: 0,
                type = it.ambientSound?.type?.code ?: SleepModeInfo.SoundType.Ambient.code,
                displayName = getSourceName(it.ambientSound)
            )
            jsGetHorizon3SleepMode.ambientLight = AmbientLight(
                enable = it.isLightActive ?: false,
                brightness = it.lightBrightness ?: 0,
                temperature = it.temperatureColor ?: 0
            )
            val passBack = GsonUtil.parseBeanToJson(jsGetHorizon3SleepMode)
            callback.invoke("0", null, JSONObject(passBack))
            Logger.d(TAG, "handlerGetHorizon3SleepMode=${passBack}")
        } ?: run {
            Logger.d(TAG, "handlerGetHorizon3SleepMode false")
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private suspend fun handlerSetHorizon3SleepMode(
        activity: Activity, data: JsonObject?, callback: JsCallback
    ) {
        val jsonString = data?.toString()
        Logger.d(TAG, "handlerSetHorizon3SleepMode() >>> \n$jsonString")
        if (jsonString.isNullOrBlank()) {
            Logger.w(TAG, "handlerSetHorizon3SleepMode() >>> missing json content")
            callback.invoke("-1", null, null)
            return
        }

        val device = device ?: run {
            Logger.w(TAG, "handlerSetHorizon3SleepMode() >>> missing device by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
        }

        val isActive = device.sleepModeInfo?.isActive?:false
        val soundTimer = (data.get(Events.SOUNDTIMER)?.asInt ?: 0) * 60
        val soundSource = data.getAsJsonObject(Events.SOUNDSOURCE)
        val soundId = soundSource?.get("id")?.asInt ?: 0
        val soundType = soundSource?.get("type")?.asInt ?: SleepModeInfo.SoundType.Ambient.code
        // event[setHorizon3Feature] {"screenDisplay":{"brightness":37}}
        val ambientLight = data.getAsJsonObject("ambientLight")
        val brightness = ambientLight?.get(Events.BRIGHTNESS)?.asInt
        val temperature = ambientLight?.get(Events.TEMPERATURE)?.asInt
        val isLightActive = ambientLight?.get(Events.ENABLE)?.asBoolean ?: false

        val sleepModeInfo = SleepModeInfo().apply {
            this.isActive = isActive
            this.isLightActive = isLightActive
            this.lightBrightness = brightness
            this.temperatureColor = temperature
            this.timer = soundTimer
            this.ambientSound = SleepModeInfo.AmbientSound(type = SleepModeInfo.SoundType.geTypeWithCode(soundType), soundId)
        }
        Logger.i(TAG, "setSleepModeInfo=${sleepModeInfo}")
        val protocol = device.getBLEProtocol()
        val result = device.syncSetSleepModeInfo(logTag = TAG, protocol = protocol,
            sleepModeInfo = sleepModeInfo, command = SleepModeInfo.Command.SaveSleepMode)

        if (true == result) {
            callback.invoke("0", null, null)
            viewModelScope.launch {
                delay(500)
                device.syncGetSleepModeInfo(logTag = TAG, protocol = protocol)
            }
            Logger.d(TAG, "handlerGetHorizon3ScreenDisplay >>> syncSetSleepModeInfo true")
        } else {
            callback.invoke("-1", null, null)
            Logger.d(TAG, "handlerGetHorizon3ScreenDisplay >>> syncSetSleepModeInfo false")
        }
    }

    @MainThread
    private suspend fun handleSetHorizon3Feature(
        activity: Activity, data: JsonObject?, callback: JsCallback
    ) {
        val jsonString = data?.toString()
        Logger.d(TAG, "handleSetHorizon3Feature() >>> \n$jsonString")
        if (jsonString.isNullOrBlank()) {
            Logger.w(TAG, "handleSetHorizon3Feature() >>> missing json content")
            callback.invoke("-1", null, null)
            return
        }

        val device = device ?: run {
            Logger.w(TAG, "handleSetHorizon3Feature() >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
        }

        // event[setHorizon3Feature] {"radio":{"id":4}}
        val radio = data.getAsJsonObject(Events.Horizon3FeatureEvents.RADIO)
        val radioId = radio?.get("id")?.asInt
        Logger.i(TAG, "handleSetHorizon3Feature() >>> radio:$radio")
        radioId?.let { id ->
            device.radioInfo?.let { itRadioInfo ->
                itRadioInfo.presetStationList?.filter { it.stationIndex == id }?.let { filterStation ->
                    Logger.d(TAG, "handleSetHorizon3Feature() >>> filterStation:$filterStation")
                    filterStation.elementAtOrNull(0)?.let { station ->
                        device.syncActivePresetStation(device.getBLEProtocol(), logTag = TAG, station = station)
                    }
                }
            }
        }

        // event[setHorizon3Feature] {"screenDisplay":{"brightness":37}}
        val screenDisplay = data.getAsJsonObject("screenDisplay")
        val screenDisplayBrightness = screenDisplay?.get(Events.BRIGHTNESS)?.asInt
        screenDisplayBrightness?.let { value ->
            val info = ScreenDisplayInfo().apply {
                this.bedtimeBrightness = value
            }
            device.updateScreenDisplayInfo(
                device.getBLEProtocol(),
                screenDisplayInfo = info,
                command = ScreenDisplayInfo.Command.BedtimeBrightness,
            )
            callback.invoke("0", null, null)
            Logger.i(TAG, "setScreenBrightness=${value}")
        }
        //event[setHorizon3Feature] {"ambientLight":{"enable":true}}
        val ambientLight = data.getAsJsonObject("ambientLight")
        val ambientLightEnable = ambientLight?.get(Events.ENABLE)?.asBoolean
        val ambientLightBrightness = ambientLight?.get(Events.BRIGHTNESS)?.asInt
        val ambientLightTemperature = ambientLight?.get(Events.TEMPERATURE)?.asInt
        ambientLightEnable?.let { value ->
            val info = AmbientLightInfo().apply {
                this.isActive = value
            }
            device.updateAmbientLightInfo(
                device.getBLEProtocol(), ambientLightInfo = info, command = AmbientLightInfo.Command.Status
            )
            callback.invoke("0", null, null)
            Logger.i(TAG, "updateAmbientLightInfo Status=${value}")
        }
        //event[setHorizon3Feature] {"ambientLight":{"brightness":77}}
        ambientLightBrightness?.let { value ->
            val info = AmbientLightInfo().apply {
                this.lightBrightness = value
            }
            device.updateAmbientLightInfo(
                device.getBLEProtocol(), ambientLightInfo = info, command = AmbientLightInfo.Command.Brightness
            )
            callback.invoke("0", null, null)
            Logger.i(TAG, "updateAmbientLightInfo Brightness=${value}")
        }

        //event[setHorizon3Feature] {"ambientLight":{"temperature":75}}
        ambientLightTemperature?.let { value ->
            val info = AmbientLightInfo().apply {
                this.lightTemperatureColor = value
            }
            device.updateAmbientLightInfo(
                device.getBLEProtocol(), ambientLightInfo = info, command = AmbientLightInfo.Command.TemperatureColor
            )
            callback.invoke("0", null, null)
            Logger.i(TAG, "updateAmbientLightInfo TemperatureColor=${value}")
        }
        val sleepMode = data.getAsJsonObject("sleepMode")
        val sleepModeEnable = sleepMode?.get(Events.ENABLE)?.asBoolean
        sleepModeEnable?.let { value ->
            val sleepModeInfo = SleepModeInfo().apply { this.isActive = value }

            if (device.syncSetSleepModeInfo(
                    logTag = TAG, protocol = device.getBLEProtocol(), sleepModeInfo = sleepModeInfo, command = SleepModeInfo.Command.Status
                )
            ) {
                callback.invoke("0", null, null)
            } else {
                callback.invoke("-1", null, null)
                Logger.d(TAG, "handlerGetHorizon3ScreenDisplay >>> syncSetSleepModeInfo false")
            }
            Logger.i(TAG, "setSleepModeInfo=${value}")
        }
    }


    @MainThread
    private fun handleHorizon3Feature(activity: DeviceControlHybridActivity, data: JsonObject?, callback: JsCallback){
        val device = device ?: run {
            Logger.e(TAG, "handleHorizon3Feature() >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
        }

        Logger.d(TAG,"handleHorizon3Feature data = ${data?.toString()}")
        val strFunction = data?.get("event")?.asString
        when (strFunction) {
            Events.Horizon3FeatureEvents.ALARM -> {
                BTHorizonAlarmsActivity.portal(activity, device = device)
            }

            Events.Horizon3FeatureEvents.RADIO -> {
                RadioActivity.portal(activity, device = device)
            }

            Events.Horizon3FeatureEvents.SCREENSAVER -> {
                SetScreenSaverActivity.launch(activity, uuid)
            }

            Events.Horizon3FeatureEvents.BEDTIME -> {
                SetBedtimeActivity.launch(activity, uuid)
            }

            else -> {

            }
        }
        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleProductSetting(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val device = device ?: run {
            Logger.e(TAG, "handleProductSetting() >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
         }

        Logger.d(TAG, "handleProductSetting() >>> ")

        BaseProductSettingsActivity.portal(activity = activity, device = device, 0).let { result ->
            if (result) {
                callback.invoke("0", null, null)
            } else {
                callback.invoke("-1", null, null)
            }
        }
    }

    @MainThread
    private fun handleSetHKStudioLightInfo(data: JsonObject?, callback: JsCallback) {
        val device = device ?: run {
            Logger.e(TAG, "handleSetHKStudioLightInfo() >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
        }

        Logger.d(TAG, "handleSetHKStudioLightInfo() >>> ${System.identityHashCode(device.bleDevice)}," +
                "${System.identityHashCode(device.gattSession)},")
        hkStudioRepository?.setStudioLightInfo(device = device, logTag = TAG, data = data) { result ->
            callback.invoke(result, null, null)
        }
    }

    @MainThread
    private suspend fun handleGetHKStudioLightInfo(callback: JsCallback) {
        // val lightInfo = JSHKStudioLightInfo.mock()
        val device = device ?: run {
            Logger.e(TAG, "handleGetHKStudioLightInfo() >>> cant find device instance by uuid[$uuid]")
            callback.invoke("-1", null, null)
            return
        }

        hkStudioRepository?.getStudioLightInfo(device = device, logTag = TAG) { lightInfo ->
            val passback = GsonUtil.parseBeanToJson(lightInfo)
            Logger.i(TAG, "handleGetHKStudioLightInfo() >>> passback:\n$passback")
            callback.invoke("0", null, JSONObject(passback))
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        val device = device ?: run {
            Logger.e(TAG, "onCreate() >>> cant find device instance by uuid[$uuid]")
            weakActivity.get()?.finish()
            return
        }

        bindPlayInfoRelatedData(owner = owner)

        device.reqFeedbackTone(protocol = device.getBLEProtocol())

        super.reportV5Analytics(owner = owner, scope = viewModelScope)
    }

    /**
     * @link [toPlayOrRadioInfo]
     */
    private fun bindPlayInfoRelatedData(owner: LifecycleOwner) {
        val playerViewModel = <EMAIL> ?: run {
            Logger.w(TAG, "bindPlayInfoRelatedData() >>> missing playerViewModel")
            return
        }

        Logger.d(TAG, "bindPlayInfoRelatedData() >>> ")
        playerViewModel.isPlaying.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }
        playerViewModel.inputSource.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }
        playerViewModel.songName.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }
        playerViewModel.artistName.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }
        playerViewModel.durationMills.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }
        playerViewModel.currentMills.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }
        playerViewModel.volume.observe(owner) { notifyPlayInfo(device?.toPlayOrRadioInfo()) }

    }

    override fun onResume(owner: LifecycleOwner) {
        Logger.d(TAG, "onResume() >>> UUID[$uuid]")
        val device = device ?: run {
            Logger.e(TAG, "onResume() >>> cant find device instance by UUID[$uuid]")
            weakActivity.get()?.finish()
            return
        }
        //getLight info
        // request device info
        viewModelScope.launch(DISPATCHER_IO) {
            if (device.pid?.isHomeBtCategory() == true) {
                //get feature of device mood /color reset support
                device.syncGetDeviceFeatureInfoWithTimeout(protocol = device.getBLEProtocol())
                delay(150)
                device.syncGetStudioLightInfoWithTimeout(logTag = TAG, protocol = device.getBLEProtocol())
                device.syncGetAdvancedEQWithTimeout(protocol = device.getBLEProtocol())

//                if (true == device.deviceFeature?.supportDeviceAnalyticsInfo) {
//                    requestAnalyticsData()
//                }
            }
        }
        device.registerDeviceListener(deviceListener)

        notifyDeviceInfo()
        notifySecondaryDeviceInfos()
        notifyBatteryInfo()

        notifyPlayInfo(device.toPlayOrRadioInfo())

    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)

        device?.unregisterDeviceListener(deviceListener)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        stereoSwitchChannelDialog?.dismiss()
    }

    //clear this repository in case of leak canary
    //there is no name callback
    private fun releaseRepository() {
        hkStudioRepository = null
        customEQRepository = null
    }

    private val deviceListener = object : IPartyBoxDeviceListener {
        override fun onGattStatusChanged(
            status: EnumConnectionStatus,
            session: BaseGattSession<*, *, *>
        ) {
            gattConnectStatusListener?.invoke(status, session)

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                notifySecondaryDeviceInfos()
                notifyBatteryInfo()
            }
        }

        override fun onBrEdrStatusChanged(
            status: EnumConnectionStatus,
            session: BaseBrEdrSession<*, *, *>
        ) {
            brEdrConnectStatusListener?.invoke(status, session)

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                notifySecondaryDeviceInfos()
                notifyBatteryInfo()
            }
        }

        override fun onDeviceInfoUpdate() {
            if (device?.audioSource.isPlayerSource()) {
                device?.reqPlayerInfo(protocol = device?.getBLEProtocol() ?: BluetoothDevice.TRANSPORT_LE)
            } else if (device?.audioSource.isRadioSource()) {
                device?.reqRadioInfo(
                    protocol = BluetoothDevice.TRANSPORT_LE,
                    cmd = ReqRadioInfoCommand(RadioInfo.RequestType.LastStation)
                )
            }

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                notifySecondaryDeviceInfos()
                notifyBatteryInfo()
            }
        }

        override fun onDeviceFeatureUpdate() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
            }
        }

        override fun onLightInfoUpdate() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyLightInfo()
            }
        }

        override fun onAdvanceEQUpdate() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyEQInfo()
            }
        }

        override fun onFeedbackTone() {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
            }
        }

        override fun onDeviceNameUpdated(name: String) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
            }
        }

        override fun onStereoGroupNameUpdated(groupName: String) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
            }
        }

        override fun onRadioInfo(radioInfo: RadioInfo?, functionality: RadioInfo.Command?) {
            super.onRadioInfo(radioInfo, functionality)
            Logger.i(TAG, "notifyHorizon3Feature >>> device audioSource ${device?.audioSource}")
            Logger.i(TAG, "notifyHorizon3Feature >>> radioInfo $radioInfo")
            val horizon3FeatureInfo = JsGetHorizon3FeatureInfo()
            radioInfo?.presetStationList?.let { presetStations ->
                val radios = mutableListOf<Radio>()
                presetStations.forEach { item ->
                    var id = 0
                    val selected = item.state == RadioInfo.State.Playing
                    item.stationIndex?.let {
                        id = it
                    }
                    radios.add(Radio(id, selected))
                }
                horizon3FeatureInfo.radios = radios
            }

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                notifyPlayInfo(device?.toPlayOrRadioInfo())
                notifyHorizon3Feature(horizon3FeatureInfo)
            }
        }

        override fun onAlarmInfo(alarmInfo: AlarmInfo?) {
            super.onAlarmInfo(alarmInfo)
            Logger.i(TAG, "notifyHorizon3Feature >>> onAlarmInfo $alarmInfo")
            alarmInfo?.alarmList?.let { itAlarmList ->
                val horizon3FeatureInfo = JsGetHorizon3FeatureInfo()
                val alarms = mutableListOf<Alarm>()
                itAlarmList.forEach {
                    if (it.isActiveAlarm == true) {
                        val formattedHour = String.format("%02d", it.alarmTime?.hour)
                        val formattedMinute = String.format("%02d", it.alarmTime?.minute)
                        val formattedAlarmTime = "${formattedHour}:${formattedMinute}"
                        alarms.add(Alarm(id = it.alarmID ?: 0, time = formattedAlarmTime, sunrise = it.isSunRise ?: false))
                    }
                }
                horizon3FeatureInfo.alarms = alarms

                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    notifyHorizon3Feature(horizon3FeatureInfo)
                }
            }
        }

    }

    @MainThread
    private fun notifyLightInfo() {
        val device = device ?: run {
            Logger.e(TAG, "notifyLightInfo() >>> cant find device instance by uuid[$uuid]")
            return
        }

        val isStudioDevice = device.isHomeBtCategory() && device.isSupportLightControl()
        Logger.d(TAG, "notifyLightInfo isStudioDevice : $isStudioDevice")
        if (isStudioDevice) {
            notifyStudioLightInfo()
            return
        }

        val source = device.lightInfo ?: run {
            Logger.e(TAG, "notifyLightInfo() >>> cant find lightInfo")
            return
        }

        val target = source.collectLightInfo()
        notifyLightInfo(info = target)
    }

    @MainThread
    private fun notifyDeviceInfo() {
        val device = device ?: run {
            Logger.e(TAG, "notifyDeviceInfo() >>> cant find device instance by uuid[$uuid]")
            return
        }

        notifyDeviceInfo(info = device.toDeviceInfo())
        //current audio source change
        if (device.pid?.isHomeBtCategory() == true && device.pid?.isSupportLightControl() == true && isAudioSourceChanged()) {
            Logger.d(TAG, "notifyDeviceInfo() >>> audio source change:$lastAudioSource, current ${device.audioSource}")
            notifyStudioLightInfo()
        }
    }

    @MainThread
    private fun notifySecondaryDeviceInfos() {
        val device = device ?: run {
            Logger.e(TAG, "notifySecondaryDeviceInfos() >>> cant find device instance by uuid[$uuid]")
            return
        }

        notifySecondaryDeviceInfos(info = device.toSecondaryDeviceInfoList())
    }

    @MainThread
    private fun notifyStudioLightInfo() {
        val device = device ?: run {
            Logger.e(TAG, "notifyStudioLightInfo() >>> cant find device instance by uuid[$uuid]")
            return
        }

        val targetInfo = device.collectStudioLightInfo() ?: run {
            Logger.e(TAG, "notifyStudioLightInfo() >>> cant find studio light info")
            return
        }

        Logger.d(TAG, "notifyStudioLightInfo : $targetInfo")
        notifyHKStudioLightInfo(info = targetInfo)
    }

    @MainThread
    private fun notifyBatteryInfo() {
        val device = device ?: run {
            Logger.e(TAG, "notifyBatteryInfo() >>> cant find device instance by uuid[$uuid]")
            return
        }

        val batteryInfo = device.toBatteryInfo()
        val batteryInfoList = mutableListOf<BatteryInfo>(batteryInfo)

        device.foundSecondaryDevice()?.let { secondaryDevice ->
            batteryInfoList.add(secondaryDevice.toBatteryInfo())
        }

        notifyBatteryInfo(info = batteryInfoList.toList())
    }

    @MainThread
    private suspend fun notifyEQInfo() {
        val activity = weakActivity.get() ?: run {
            Logger.e(TAG, "notifyEQInfo() >>> missing context")
            return
        }

        val device = device ?: run {
            Logger.e(TAG, "notifyEQInfo() >>> missing device instance by uuid[$uuid]")
            return
        }

        Logger.d(TAG, "device?.rawEQSettings : ${device.rawEQSettings}")
        val eqSettings = device.toEQSettings(context = activity) ?: run {
            Logger.e(TAG, "notifyEQInfo() >>> fail to exec toEQSettings")
            return
        }

        val info = customEQRepository?.toGetEQ(device = device, eqSettings = eqSettings, context = activity, logTag = TAG) ?: run {
            Logger.e(TAG, "notifyEQInfo() >>> fail to exec toGetEQ")
            return
        }

        notifyEQInfo(info = info)
    }

    @MainThread
    private fun handleGetOtaStatus(activity: DeviceControlHybridActivity, callback: JsCallback) {
        viewModelScope.launch {
            val mainPid = device?.pid
            if (mainPid.isNullOrBlank()) {
                Logger.e(TAG, "syncGetRemoteUpdateModel() >>> mainPid is null. device[$uuid]")
                callback.invoke("0", null, OTAStatus(false).toJSONObj())
                return@launch
            }

            runCatching {
                val model = syncGetRemoteUpdateModel(pid = mainPid, activity = activity).await()
                callback.invoke("0", null, OTAStatus(model?.isUpdateAvailable ?: false).toJSONObj())
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", "", null)
            }
        }
    }

    /**
     * Check both primary and secondary ota status
     */
    private suspend fun syncGetRemoteUpdateModel(
        pid: String,
        activity: DeviceControlHybridActivity
    ): CompletableDeferred<RemoteUpdateModel?> {

        val deffer = CompletableDeferred<RemoteUpdateModel?>()

        withContext(DISPATCHER_IO) {
            val device = device ?: run {
                Logger.w(TAG, "syncGetRemoteUpdateModel() >>> missing device by uuid[${uuid}]")
                deffer.complete(null)
                return@withContext
            }

            Logger.d(TAG, "syncGetRemoteUpdateModel() >>> sync get primary device[${device.UUID}] fw.")
            device.syncGetFirmwareVersionWithTimeout(protocol = device.getBLEProtocol())
            val mainFwVersion = device.firmwareVersion
            val secondaryFwVersion = getSecondaryFwVersion(mainDev = device, activity = activity)

            Logger.d(TAG, "syncGetRemoteUpdateModel() >>> main[${device.UUID}]fw[${mainFwVersion}] " +
                    "secondary.fw[$secondaryFwVersion]")

            val targetFw = targetFw(mainFwVersion = mainFwVersion, secondaryFwVersion = secondaryFwVersion)
            if (targetFw.isNullOrBlank()) {
                Logger.e(TAG, "syncGetRemoteUpdateModel() >>> no suitable targetFw")
                deffer.complete(null)
                return@withContext
            }

            Logger.d(TAG, "syncGetRemoteUpdateModel() >>> targetFw[$targetFw]")
            PartyOtaFetchTask(
                pid = pid,
                appVersion = BuildConfig.VERSION_NAME,
                deviceFirmwareVer = targetFw,
            ) { model ->
                Logger.d(TAG, "syncGetRemoteUpdateModel() >>> result: $model")
                remoteUpdateModel = model
                deffer.complete(model)
            }.execute(context = activity)
        }

        return deffer
    }

    private suspend fun getSecondaryFwVersion(
        mainDev: PartyBoxDevice,
        activity: DeviceControlHybridActivity
    ): String? {
        if (!mainDev.secondaryInfo?.firmwareVersion.isNullOrBlank()) {
            Logger.d(TAG, "getSecondaryFwVersion() >>> use fw in secondaryInfo directly.[${mainDev.secondaryInfo?.firmwareVersion}]")
            return mainDev.secondaryInfo?.firmwareVersion
        }

        val secondaryDev = mainDev.foundSecondaryDevice() ?: run {
            Logger.w(TAG, "getSecondaryFwVersion() >>> didn't have secondary device")
            return null
        }

        if (!secondaryDev.firmwareVersion.isNullOrBlank()) {
            Logger.d(TAG, "getSecondaryFwVersion() >>> use cache fw version in secondary device directly.[${secondaryDev.firmwareVersion}]")
            return secondaryDev.firmwareVersion
        }

        val protocol = secondaryDev.getBLEProtocol()
        Logger.d(TAG, "syncGetRemoteUpdateModel() >>> sync get secondaryDev device[${secondaryDev.UUID}] fw. protocol[$protocol]")
        val connectRst = when (protocol) {
            BluetoothDevice.TRANSPORT_LE -> secondaryDev.syncGattConnectWithTimeout(context = activity)
            BluetoothDevice.TRANSPORT_BREDR -> secondaryDev.syncBrEdrConnectWithTimeout(context = activity)
            else -> false
        }

        if (!connectRst) {
            Logger.e(TAG, "getSecondaryFwVersion() >>> fail to connect")
            return null
        }

        secondaryDev.syncGetFirmwareVersionWithTimeout(protocol = protocol)
        Logger.d(TAG, "getSecondaryFwVersion() >>> final secondary fw.[${secondaryDev.firmwareVersion}]")
        return secondaryDev.firmwareVersion
    }

    private fun targetFw(mainFwVersion: String?, secondaryFwVersion: String?) =
        if (mainFwVersion.isNullOrBlank() && secondaryFwVersion.isNullOrBlank()) {
            null
        } else if (mainFwVersion.isNullOrBlank()) {
            secondaryFwVersion
        } else if (secondaryFwVersion.isNullOrBlank()) {
            mainFwVersion
        } else {
            when (VersionCompareUtil.compareVersion(mainFwVersion, secondaryFwVersion)) {
                -1 -> mainFwVersion
                1 -> secondaryFwVersion
                else -> mainFwVersion
            }
        }

    private fun handleEnterOTA(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val model = remoteUpdateModel ?: run {
            Logger.w(TAG, "handleEnterOTA() >>> missing remoteUpdateModel")
            callback.invoke("-1", null, null)
            return
        }

        val device = device ?: run {
            Logger.w(TAG, "handleEnterOTA() >>> missing device")
            callback.invoke("-1", null, null)
            return
        }

        PartyBoxOtaActivity.launchOtaPage(
            activity = activity,
            launcher = launcher,
            device = device,
            model = model
        )
        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handlePlayFunction(data: JsonObject?, callback: JsCallback) {
        val strFunction = data?.get("function")?.asString

        val device = device ?: run {
            Logger.w(TAG, "handlePlayFunction() >>> missing device")
            callback.invoke("-1", null, null)
            return
        }

        if (device.isRadioStyle()) {
            val isRadioPlay = device.isRadioPlay()
            Logger.d(TAG, "handlePlayFunction() >>> radio function[$strFunction] isRadioPlay[$isRadioPlay]")
            if (strFunction == Events.PlayFunctionEvents.PLAY_OR_PAUSE) {
                device.setRadioInfo(
                    protocol = BluetoothDevice.TRANSPORT_LE,
                    cmd = if (isRadioPlay) {
                        RadioInfo.Command.FunctionalityStop
                    } else {
                        RadioInfo.Command.FunctionalityPlay
                    },
                    cmdBytes = null
                )
            }

            callback.invoke("0", null, null)
        } else {
            val targetPlayFunction = strFunction?.toTargetPlayFunction(device = device) ?: run {
                Logger.w(TAG, "handlePlayFunction() >>> invalid function[$strFunction]")
                callback.invoke("-1", null, null)
                return
            }

        val protocol = device.getBLEProtocol() ?: BluetoothDevice.TRANSPORT_LE
        Logger.d(TAG, "handlePlayFunction() >>> play function[$targetPlayFunction] protocol[$protocol]")
        when (targetPlayFunction) {
            // switch inner isPlaying state inside device bean directly by using playMusic/pauseMusic
            PlayerStatus.PLAYER_STATE_PLAY -> device.playMusic(protocol = protocol)
            PlayerStatus.PLAYER_STATE_PAUSE -> device.pauseMusic(protocol = protocol)
            else -> device.setPlayerStatus(protocol = protocol, status = targetPlayFunction)
        }

            callback.invoke("0", null, null)
        }


    }

    @MainThread
    private fun handleVolumeChange(data: JsonObject?, callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleVolumeChange() >>> missing device")
            callback.invoke("-1", null, null)
            return
        }

        val volume = data?.get("volume")?.asInt?.roundPercentage() ?: return
        val protocol = device.getBLEProtocol()
        Logger.d(TAG, "handleVolumeChange() >>> volume[$volume] protocol[$protocol]")

        device.setRemoteVolume(protocol = protocol, value = volume)
        callback.invoke("0", null, null)
    }

    @MainThread
    private suspend fun handleGetEQ(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleGetEQ() >>> missing device")
            callback.invoke("-1", null, null)
            return
        }

        customEQRepository?.getEQSetting(context = activity, device = device, logTag = TAG) { info ->
            Logger.d(TAG, "handleGetEQ() >>> info:$info")
            if (info == null) {
                callback.invoke("-1", null, null)
            } else {
                val passback = GsonUtil.parseBeanToJson(info)
                Logger.i(TAG, "handleGetEQ() >>> passback: $passback")
                callback.invoke("0", null, JSONObject(passback))
            }
        }
    }


    @MainThread
    private suspend fun handleSetEQ(data: JsonObject?, callback: JsCallback, activity: DeviceControlHybridActivity) {
        val device = device ?: run {
            Logger.w(TAG, "handleSetEQ() >>> missing device")
            callback.invoke("-1", null, null)
            return
        }

        customEQRepository?.setEQSetting(context = activity, device = device, logTag = TAG, data = data) { result, msg ->
            callback.invoke(result, msg, null)
        }
    }

    @MainThread
    private suspend fun handleGetPlayInfo(callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleGetPlayInfo() >>> missing device")
            callback.invoke("-1", null, null)
            return
        }

        device.blockGetPlayerStatusWithTimeout(protocol = device.getBLEProtocol())
        val info = device.toPlayOrRadioInfo()
        Logger.d(TAG, "handleGetPlayInfo() >>> $info")
        callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(info)))
    }

    private fun PartyBoxDevice.toPlayOrRadioInfo(): PlayInfo {
        val device = this

        return if (device.isRadioStyle()) {
            device.toRadioInfo()
        } else {
            device.toPlayInfo()
        }
    }

    /**
     * Don't use this function directly except need to display as player state force.
     */
    private fun PartyBoxDevice.toPlayInfo(): PlayInfo {
        val device = this
        device.onAudioSourceChanged()
        val inputSource = device.audioSource.toEnumInputSource()
        val musicName: String? = Tools.mapPlayerTitle(songName = device.songName, inputSource = inputSource)
        val artistName: String? = Tools.mapPlayerSubTitle(artistName = device.artistName, inputSource = inputSource)
        Logger.i(TAG, "toPlayInfo: inputSource:${inputSource},musicName:$musicName,artistName:$artistName")
        return PlayInfo(
            musicSource = device.audioSource.toHybridMusicSourceInt(),
            momentType = 0,
            status = device.audioSource.toStatus(device.isPlaying),
            musicName = musicName,
            artistName = artistName,
            totalTime = device.durationMills.millsToSecs().toInt(),
            playingTime = device.currentMills.millsToSecs().toInt(),
            volume = device.volumeWithMute
        )
    }

    /**
     * Don't use this function directly except need to display as radio state force.
     */
    private fun PartyBoxDevice.toRadioInfo(): PlayInfo {
        val device = this

        return PlayInfo(
            musicSource = device.radioType().toHybridMusicSourceInt(),
            momentType = 0,
            status = if (device.isRadioPlay()) 1 else 2,
            musicName = device.getRadioTitle(),
            artistName = device.getRadioSubTitle(),
            totalTime = 0,
            playingTime = 0,
            volume = device.volumeWithMute
        )
    }

    private var lastAudioSource: AudioSource? = null

    /**
     * if audio source changed to usb clear [PartyBoxDevice.songName] [PartyBoxDevice.artistName]
     */

    private fun PartyBoxDevice.onAudioSourceChanged(){
        if (isAudioSourceChanged() && AudioSource.USB == device?.audioSource) {
            device?.songName = null
            device?.artistName = null
        }
        lastAudioSource = device?.audioSource
    }

    private fun isAudioSourceChanged(): Boolean = lastAudioSource != null && device?.audioSource != lastAudioSource


    /**
     * 0:'no music' </br>1: 'play' </br>2: 'pause'
     */
    private fun AudioSource?.toStatus(isPlaying: Boolean): Int =
        when (this) {
            AudioSource.AUX -> 3
            AudioSource.HDMI,
            AudioSource.NONE_AUDIO,
            null -> 0

            else -> if (isPlaying) 1 else 2
        }

    private fun AudioSource.toSourceName(): String =
        when (this) {
            AudioSource.NONE_AUDIO -> WAApplication.me.getString(R.string.audio)
            else -> sourceName
        }

    /**
     * 0:'none' </br> 1:'wi-fi' </br> 2: 'bt' </br>3:'usb' </br>4:'hdmi' </br>5:'tv' </br>6:'aux'</br>
     * 7:'fm'</br>8:'dab'</br>9:'mood'
     */
    private fun AudioSource?.toHybridMusicSourceInt(): Int {
        val source = this

        return when (source) {
            AudioSource.BLUETOOTH -> 2
            AudioSource.USB -> 3
            AudioSource.HDMI -> 4
            AudioSource.AUX -> 6
            AudioSource.FM_AUDIO -> 7
            AudioSource.DAB_AUDIO -> 8
            AudioSource.MOOD -> 9
            else -> 0
        }
    }

    /**
     * 0:'none' </br> 1:'wi-fi' </br> 2: 'bt' </br>3:'usb' </br>4:'hdmi' </br>5:'tv' </br>6:'aux'</br>
     * 7:'fm'</br>8:'dab'</br>9:'mood'
     */
    private fun RadioInfo.Type?.toHybridMusicSourceInt(): Int {
        val type = this

        return when (type) {
            RadioInfo.Type.FMRadio -> 7
            RadioInfo.Type.DABRadio -> 8
            else -> 7
        }
    }

    private fun handleEnterTwsStereo(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleEnterTwsStereo() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        PartyBoxTwsActivity.launchTwsPage(
            activity = activity,
            device = device,
            mode = EnumTwsMode.GEN3
        )
        callback.invoke("0", null, null)
    }

    private fun handleEnterTwsStereoPrevious(
        activity: DeviceControlHybridActivity,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.w(TAG, "handleEnterTwsStereoPrevious() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        PartyBoxTwsActivity.launchTwsPage(
            activity = activity,
            device = device,
            mode = EnumTwsMode.GEN2_GEN3_MIX
        )
        callback.invoke("0", null, null)
    }

    private var mUngroupDialog: Dialog? = null

    @MainThread
    private fun handleUnGroup(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleUnGroup() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        mUngroupDialog = UngroupDialog(device = device, activity = activity).apply {
            show()
        }

        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleGroupRename(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val rst = GroupRenameActivity.portal(
            context = activity,
            device = device
        )

        callback.invoke(if (rst) "0" else "-1", null, null)
    }

    private var stereoSwitchChannelDialog: StereoSwitchChannelDialog? = null

    @MainThread
    private fun handleGroupChannelChange(
        activity: DeviceControlHybridActivity,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.w(TAG, "handleGroupChannelChange() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        stereoSwitchChannelDialog?.dismiss()
        stereoSwitchChannelDialog = StereoSwitchChannelDialog(
            context = activity,
            primaryDevice = device,
            secondaryDevice = device.foundSecondaryDevice()
        ).also { dialog ->
            dialog.show()
        }

        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleGetDeviceInfo(callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleGetDeviceInfo() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        val protocol = device.getBLEProtocol()
        device.reqDeviceInfo(protocol = protocol)

        if (null == device.deviceFeature) {
            // regard less of fetch result.
            device.reqDeviceFeatureInfo(protocol = protocol)
        }

        callback.invoke("0", null, null)
        notifyDeviceInfo()
    }

    private fun handleEffectLabInfo(activity: DeviceControlHybridActivity, callback: JsCallback) {
        val device = device ?: run {
            Logger.w(TAG, "handleEffectLabInfo() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        EffectLabActivity.launchEffectLab(activity = activity, device = device,)
        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleSetLightControl(data: JsonObject?, callback: JsCallback) {
        val jsonString = data?.toString()
        Logger.d(TAG, "handleSetLightControl() >>> \n$jsonString")
        if (jsonString.isNullOrBlank()) {
            Logger.w(TAG, "handleSetLightControl() >>> missing json content")
            callback.invoke("-1", null, null)
            return
        }

        val lightCtrl = GsonUtil.parseJsonToBean(jsonString, SetLightControl::class.java) ?: run {
            Logger.w(TAG, "handleSetLightControl() >>> fail to unmarshall as json bean")
            callback.invoke("-1", null, null)
            return
        }

        val device = device ?: run {
            Logger.w(TAG, "handleSetLightControl() >>> device is null")
            callback.invoke("-1", null, null)
            return
        }

        val protocol = device.getBLEProtocol()

        // Don't block single light control command as there might be several commands at the same time.
        lightCtrl.enable?.let { enable ->
            Logger.i(TAG, "handleSetLightControl() >>> set main switch[$enable]")
            device.setLightMainSwitch(protocol = protocol, on = enable)
        }

        EnumLightPattern.getByValue(lightCtrl.patternId)?.let { pattern ->
            Logger.i(TAG, "handleSetLightControl() >>> set light pattern[$pattern]")
            device.setLightPattern(protocol = protocol, pattern = pattern)
        }

        lightCtrl.lightToken?.let { token ->
            Logger.i(TAG, "handleSetLightControl() >>> set light token. [${token.id}][${token.state}]")
            device.setLightElementSwitch(
                protocol = protocol,
                element = token.id?.jsIdToLightToken() ?: 0,
                on = (token.state ?: false)
            )
        }

        if (true == data.keySet()?.contains(SetLightControl.Companion.COLOR_KEY)) {
            val color = lightCtrl.toRGBColor()
            if (null == color) {
                Logger.i(TAG, "handleSetLightControl() >>> open light pattern loop")
                device.openLightPatternLoop(protocol = protocol)
            } else {
                Logger.i(TAG, "handleSetLightControl() >>> set color:$color")
                device.setLightColor(protocol = protocol, color = color)
            }
        }

        callback.invoke("0", null, null)
    }

    @MainThread
    private suspend fun handleGetLightInfo(callback: JsCallback) {
        val device = device ?: run {
            Logger.e(TAG, "handleGetLightInfo() >>> cant find device instance by uuid[${uuid}]")
            callback.invoke("-1", null, null)
            return
        }

        device.syncGetLightInfoWithTimeout(logTag = TAG, protocol = device.getBLEProtocol())
        val lightInfo = device.collectLightInfo()

        if (null != lightInfo) {
            val passback = GsonUtil.parseBeanToJson(lightInfo)
            Logger.i(TAG, "handleGetLightInfo() >>> passback:\n$passback")
            callback.invoke("0", null, JSONObject(passback))
        } else {
            Logger.w(TAG, "handleGetLightInfo() >>> fail to fetch full light info")
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private fun handleEnterColorPicker(activity: CommonHybridActivity, callback: JsCallback) {
        val device = device ?: run {
            Logger.e(TAG, "handleEnterColorPicker() >>> cant find device instance by uuid[${uuid}]")
            callback.invoke("-1", null, null)
            return
        }

        ColorPickerDialog(
            activity,
            onColorPick = {
                ColorPicker(0, it.red, it.green, it.blue).run {
                    device.setLightColor(
                        protocol = device.getBLEProtocol(),
                        color = Color(it.red, it.green, it.blue)
                    )
                }
            },
            color = device.lightInfo?.color?.let {
                android.graphics.Color.rgb(
                    it.red,
                    it.green,
                    it.blue
                )
            }
        ).show()

        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleEnterRename(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.e(TAG, "handleEnterRename() >>> cant find device instance by uuid[${uuid}]")
            callback.invoke("-1", null, null)
            return
        }

        if (activity.portalActivity(target = RenameDeviceActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private fun handleEnterCreateASystem(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.e(TAG, "handleEnterCreateASystem() >>> cant find device instance by uuid[${uuid}]")
            callback.invoke("-1", null, null)
            return
        }

        if (activity.portalActivity(target = BTStereoPairActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    @MainThread
    private fun handleSwitchFeedbackTone(data: JsonObject?, callback: JsCallback) {
        val jsonStr = data?.toString()
        if (jsonStr.isNullOrBlank()) {
            Logger.w(TAG, "handleSwitchFeedbackTone() >>> cannot parse json:$jsonStr")
            callback.invoke("-1", null, null)
            return
        }

        val isOn = GsonUtil.parseJsonToBean(jsonStr, SwitchFeedbackToneReq::class.java)?.isOn ?: run {
            Logger.w(TAG, "handleSwitchFeedbackTone() >>> cannot parse json:$jsonStr")
            callback.invoke("-1", null, null)
            return
        }

        val device = device ?: run {
            Logger.e(TAG, "handleSwitchFeedbackTone() >>> cant find device instance by uuid[${uuid}]")
            return
        }

        device.setFeedbackTone(protocol = device.getBLEProtocol(), isOn = isOn)
        Logger.i(TAG, "handleSwitchFeedbackTone() >>> set[$isOn]")
    }

    @MainThread
    private fun handleEnterProductInfo(
        activity: DeviceControlHybridActivity,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.e(TAG, "handleEnterProductInfo() >>> cant find device instance by uuid[${uuid}]")
            return
        }

        Logger.d(TAG, "handleEnterProductInfo() >>> ")
        ProductInfoActivity.launchProductInfoPage(activity, device)
        callback.invoke("0", null, null)
    }

    private var restoreFactoryDialog: RestoreFactoryConfirmDialog? = null

    @MainThread
    private fun handleRestoreFactorySettings(
        activity: DeviceControlHybridActivity,
        callback: JsCallback
    ) {
        Logger.d(TAG, "handleRestoreFactorySettings() >>> start")
        restoreFactoryDialog?.dismiss()
        restoreFactoryDialog = RestoreFactoryConfirmDialog(
            context = activity,
            listener = object : IRestoreFactoryConfirm {
                override fun onRestoreClick() {
                    viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                        syncFactoryReset(activity = activity, callback = callback)
                    }
                }

                override fun onCancelClick() {
                    // no impl.
                }
            }
        ).apply {
            show()
        }
    }

    @MainThread
    private suspend fun syncFactoryReset(
        activity: DeviceControlHybridActivity,
        callback: JsCallback
    ) {
        val device = device ?: run {
            Logger.e(TAG, "syncFactoryReset() >>> cant find device instance by uuid[${uuid}]")
            callback.invoke("-1", "", null)
            return
        }

        Logger.d(TAG, "syncFactoryReset() >>> start")
        val result = withContext(DISPATCHER_DEFAULT) {
            device.syncFactoryResetWithTimeout(logTag = TAG, protocol = device.getBLEProtocol())
        }

        Logger.d(TAG, "syncFactoryReset() >>> result[$result]")
        callback.invoke(if (result) "0" else "-1", "", null)

        if (result) {
            GlobalScope.launch(DISPATCHER_DEFAULT) {
                device.also { DeviceStore.factoryReset(device = it) }
                device.deleteDeviceCache(ctx = activity)
            }

            activity.finish()
        }
    }

    private fun List<EQBean>?.printf(): String {
        val beans = this ?: return "null"

        val sb = StringBuilder()
        beans.forEach { bean ->
            sb.append(bean.toString()).append("\n")
        }

        return sb.toString()
    }

    @MainThread
    private fun handleEnterFullPlayer(
        device: PartyBoxDevice?,
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        device ?: run {
            Logger.e(TAG, "handleEnterFullPlayer() >>> cant find device instance by uuid[${uuid}]")
            return
        }

        activity.portalActivity(FullPlayerActivity::class.java, device)
        callback.invoke("0", null, null)
    }

    fun requestAnalyticsData() {
        val device = device ?: run {
            Logger.e(TAG, "requestAnalyticsData() >>> cant find device instance by uuid[${uuid}]")
            return
        }

        device.reqAnalyticsData(device.getBLEProtocol())
    }

    fun cleanAnalyticsData() {
        val device = device ?: run {
            Logger.e(TAG, "cleanAnalyticsData() >>> cant find device instance by uuid[${uuid}]")
            return
        }

        device.cleanAnalyticsData(device.getBLEProtocol())
    }

    override fun handleStartForResult(result: ActivityResult) {
        super.handleStartForResult(result)
        Logger.d(tag, "handleStartForResult() >>> $result")
        val context = weakActivity.get() ?: run {
            Logger.e(TAG, "handleStartForResult() >>> missing context")
            return
        }

        val device = device ?: run {
            Logger.e(TAG, "handleStartForResult() >>> cant find device instance by uuid[${uuid}]")
            return
        }

        playerViewModel?.switchDevice(device = device, context = context)
        bindPlayInfoRelatedData(owner = context)
        device.reqPlayerInfo(device.getBLEProtocol())
        device.reqFeedbackTone(device.getBLEProtocol())
        Logger.d(TAG, "handleStartForResult() >>> re.bind player view model to device[${uuid}]")
    }

    override val tag: String = TAG

    companion object {
        private const val TAG = "PartyBoxHybridViewModel"
    }
}

