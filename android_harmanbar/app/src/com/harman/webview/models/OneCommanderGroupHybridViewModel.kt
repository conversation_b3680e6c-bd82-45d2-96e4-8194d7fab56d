package com.harman.webview.models


import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.AudioSync
import com.harman.command.one.bean.AudioSyncResp
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.CUSTOM_EQ_ID
import com.harman.command.one.bean.EQListItem
import com.harman.command.one.bean.EQListPayload
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQPayload
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EnumCoulsonStatus
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetSmartBtnConfigRsp
import com.harman.command.one.bean.GroupDevicesChangeItem
import com.harman.command.one.bean.ProdSetting
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.Rear
import com.harman.command.one.bean.RearSpeakerVolume
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.displayBattery
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.session.OneBusiness
import com.harman.connect.syncCmd
import com.harman.connect.syncGetGroupParameterWithTimeout
import com.harman.control.controller.RemoteControllerActivity
import com.harman.discover.DeviceScanner
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools.getBarPort
import com.harman.discover.util.Tools.roundPercentage
import com.harman.discover.util.Tools.safeResume
import com.harman.discover.util.Tools.toJSONObj
import com.harman.log.Logger
import com.harman.moment.MomentViewModel
import com.harman.multichannel.TriggerCastLedUtil
import com.harman.music.Tools
import com.harman.music.mini.EnumMiniPlayerType
import com.harman.music.player.PlayerViewModel
import com.harman.music.service.MusicServiceSDKHelper
import com.harman.nightlistening.NightListeningActivity
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.ota.one.OneOtaActivity
import com.harman.portalActivity
import com.harman.portalUrl
import com.harman.product.setting.activity.BaseProductSettingsActivity
import com.harman.streaming.google.GooglePortalHelper
import com.harman.streaming.roon.CampaignResponse
import com.harman.streaming.roon.RoonReadyManager
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.toBean
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.BeanMap.toFlexListeningFeature
import com.harman.webview.BeanMap.toSecondaryDeviceInfoList
import com.harman.webview.CommonHybridActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.webview.EQItem
import com.harman.webview.EnumDeviceInfoStatus
import com.harman.webview.FlexListeningFeature
import com.harman.webview.GetEQ
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.JsSoundbarFeature
import com.harman.webview.NetworkInfo
import com.harman.webview.PlayInfo
import com.harman.webview.RoonReadyStatus
import com.harman.webview.SetEQ
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONObject


/**
 * Created by sky
 *
 * @param launcher used to receive msg from [OneOtaActivity]
 */
class OneCommanderGroupHybridViewModelFactory(
    private val device: OneDevice,
    private val launcher: ActivityResultLauncher<Intent>?,
    private val momentViewModel: MomentViewModel?,
    private val playerViewModel: PlayerViewModel<*>?
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OneCommanderGroupHybridViewModel(
            device = device,
            launcher = launcher,
            momentViewModel = momentViewModel,
            playerViewModel = playerViewModel
        ) as T
    }
}

class OneCommanderGroupHybridViewModel(
    override val device: OneDevice,
    val launcher: ActivityResultLauncher<Intent>?,
    private val momentViewModel: MomentViewModel?,
    private val playerViewModel: PlayerViewModel<*>?
) : ViewModel(), IHybridViewModel<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem,
        OneDevice> {

    private var uiState = UiState()

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    private val _networkCardVisible = MutableLiveData<Boolean>(false)
    val networkCardVisible: LiveData<Boolean>
        get() = _networkCardVisible.distinctUntilChanged()

    private val _musicId = MutableLiveData<String?>(device.smartBtnConfigExt?.config?.music?.musicId)

    val eventStream = MutableSharedFlow<OHVMEvent>(extraBufferCapacity = 1)

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)

        Logger.i(TAG, "onCreate() >>> device[${device.UUID}]")

        OneDeviceListenerProxy(owner = owner).also { deviceListener ->
            jblOneDeviceListener = deviceListener
            device.registerDeviceListener(deviceListener)
        }

        refreshNetworkCardViewStyle()

        DeviceScanner.registerObserver(observer = scanObserver)

        updateMetadata(owner = owner)
    }

    private fun updateMetadata(owner: LifecycleOwner) {
        device.getDeviceInfo()
        // for oneCommander
        device.getGroupDevicesFlag()

        if (device.groupParameterExt == null) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                device.syncGetGroupParameterWithTimeout(TAG,3,3000)
            }
        }
        networkCardVisible.observe(owner) { visible ->
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyNetworkInfo(isVisible = visible)
                syncNotifyEQInfo()
            }
        }
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        notifyDeviceInfo()
        updateJsSoundbarFeature(device.rears)
        handleSecondaryDeviceInfos()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        device.unregisterDeviceListener(jblOneDeviceListener)
        DeviceScanner.unregisterObserver(observer = scanObserver)

    }

    @MainThread
    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: JsCallback
    ) {
        super.onCustomEvent(activity, eventName, data, callback)
        Logger.d(TAG, "onCustomEvent() >>> event[$eventName] ${data?.toString()}")
        when (eventName) {
            Events.VOLUME_CHANGE -> {
                handleVolumeChange(device = device, data = data, callback = callback)
            }

            Events.GET_EQ -> {
                handleGetEQ(device = device, callback = callback)
            }

            Events.SET_EQ -> {
                handleSetEQ(device = device, data = data, callback = callback)
            }

            Events.GET_PLAY_INFO -> {
                handleGetPlayInfo(activity = activity, device = device, callback = callback)
            }

            Events.ENTER_PRODUCT_SETTINGS -> {
                handleEnterProductSettings(activity = activity, device = device, callback = callback)
            }

            Events.GET_DEVICE_INFO -> {
                notifyDeviceInfo()
                handleSecondaryDeviceInfos()
            }

            Events.ENTER_CALIBRATION -> {
                handleEnterCalibration(callback = callback)
            }

            Events.ENTER_REMOTE_CONTROL -> {
                handleEnterRemoteControl(activity = activity, callback = callback)
            }

            Events.GET_NETWORK_INFO -> {
                notifyNetworkInfo(isVisible = networkCardVisible.value ?: false)
            }

            Events.UNGROUP -> {
                eventStream.tryEmit(OHVMEvent.UnGroup)
            }

            Events.GET_ROON_READY_STATUS -> {
                handleGetRoonReadyStatus(activity = activity, callback = callback)
            }

            Events.ENTER_QSG -> {
                val pId = data?.get("pId")?.asString
                handleEnterQSG(activity = activity, callback = callback, pId)
            }

            Events.ENTER_PRODUCT_SUPPORT -> {
                val pId = data?.get("pId")?.asString
                handleEnterProductSupport(activity = activity, callback = callback, pId)
            }

            Events.SET_SOUNDBAR_FEATURE -> {
                handleSetSoundbarFeature(data, callback)
            }

            Events.GET_SOUNDBAR_FEATURE -> {
                handleGetSoundbarFeature(callback = callback)
            }

            Events.GET_BROADCASTING_INFO -> {
                handleGetFlexListeningFeature(callback = callback)
            }

            Events.SET_BROADCASTING_INFO -> {
                handleSetFlexListeningFeature(data, callback)
            }

            Events.ENTER_PERSONAL_LISTENING_MODE -> {
                handleEnterPersonalListeningMode(activity, callback)
            }

            Events.COPY_COUPON_CODE -> {
                handleCopyCouponCode(activity = activity, callback = callback)
            }

            Events.TO_ROON_READY_WEB -> {
                handleToRoonReadyWeb(activity = activity, callback = callback)
            }

            Events.SHOW_ROON_READY_LATER_TIPS -> {
                eventStream.tryEmit(OHVMEvent.ShowRoonReadyLaterTipsDialog)
            }

            Events.CLICK_ROON_READY_CARD -> {
                handleClickRoonReadyCard()
            }

            Events.ENABLE_GOOGLE_CAST -> {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    GooglePortalHelper.portalCastOrVA(
                        device = device,
                        activity = activity,
                        logTag = TAG,
                        vaEntry = EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_TOP_BANNER
                    )
                }
            }

            Events.SHOW_PRIMARY_GREEN_LED -> {
                handleGreenLED(device, true)
            }

            Events.HIDE_PRIMARY_GREEN_LED -> {
                handleGreenLED(device, false)
            }
        }
    }

    private fun handleGreenLED(device: OneDevice, showLED: Boolean) {
        if (showLED) {
            TriggerCastLedUtil.start()
            TriggerCastLedUtil.add(device)
        } else {
            TriggerCastLedUtil.stop()
        }

    }

    private fun handleClickRoonReadyCard() {
        viewModelScope.launch {
            RoonReadyManager.hideRoonReadyCard(device.UUID, true)
            val id = campaign?.couponCampaign?.id ?: return@launch
            FirebaseEventManager.report(
                eventName = EventUtils.EventName.EVENT_ACTION,
                device = device,
                dimensions = mapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.ACTION_MAIN_SCREEN.value,
                    EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.CAMPAIGN_CLICK_PREFIX.plus(id)
                )
            )
        }
    }

    private fun handleEnterPersonalListeningMode(activity: CommonHybridActivity, callback: JsCallback) {
        if (activity.portalActivity(target = NightListeningActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }

    }

    private fun handleSetSoundbarFeature(data: JsonObject?, callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                val setBean = data.toBean<JsSoundbarFeature.SetBean>()!!

                setBean.audioSync?.also {
                    val audioSync = uiState.jsSoundbarFeature.audioSync?.copy(it,true)
                    uiState = uiState.copy(jsSoundbarFeature = uiState.jsSoundbarFeature.copy(audioSync =audioSync ))
                    device.syncCmd<Unit>(
                        EnumCommandMapping.SET_AUDIO_SYNC,
                        paramsJsonString = GsonUtil.parseBeanToJson(AudioSync(it.toString())), port = device.getBarPort()
                    )
                }
                setBean.rearSpeakerLevel?.also {
                    device.syncCmd<Unit>(
                        EnumCommandMapping.SET_REAR_SPEAKER_VOLUME,
                        paramsJsonString = GsonUtil.parseBeanToJson(RearSpeakerVolume(volume = it.toString())), port = device.getBarPort()
                    )
                }
                setBean.pureVoiceStatus?.also {
                    device.setProSetting(
                        pureVoice = if (it) ProdSettingResponse.ON else ProdSettingResponse.OFF, port = device.getBarPort()
                    )
                }
                callback.invoke("0", null, null)
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", it.message, null)
            }
        }
    }

    private fun handleGetSoundbarFeature(callback: JsCallback) {
        viewModelScope.launch {
            if(featureSupport == null){
                featureSupport = getFeatureSupport(device)
            }
            val audioSyncRet = if (true) {
                device.syncCmd<AudioSyncResp>(EnumCommandMapping.GET_AUDIO_SYNC, port = device.getBarPort())
            } else null
            val rearSpeakerVolumeRet = if (featureSupport?.rearSpeakerStatus?.isSupport == true) {
                device.syncCmd<RearSpeakerVolumeResponse>(EnumCommandMapping.GET_REAR_SPEAKER_VOLUME,port = device.getBarPort())
            } else null
            val prodSetting = if (featureSupport?.pureVoice?.isSupport == true) {
                device.getProSetting(device.getBarPort())
            } else null

            val supportPersonalListeningMode = featureSupport?.personalListeningMode?.isSupport == true
            val supportMultiChannel = false
            val pureVoiceStatus = prodSetting?.isPureVoiceOn

            callback.invoke(
                "0",
                null,
                JsSoundbarFeature(
                    audioSyncRet?.let { JsSoundbarFeature.JsAudioSync(it.intValue(), true) },
                    rearSpeakerVolumeRet?.let { JsSoundbarFeature.JsRearSpeaker(it.volume?.toInt(), it.isDetached) },
                    supportPersonalListeningMode,
                    supportMultiChannel,
                    supportBroadcasting = featureSupport?.flexListening?.isSupport == true,
                    supportSoundTuning = device.groupInfoExt?.groupInfo?.groupInfo?.notNeedCalibration() == false,
                    pureVoiceStatus = pureVoiceStatus
                ).apply {
                    Logger.d(TAG, "JsSoundbarFeature: $this")
                    uiState = uiState.copy(jsSoundbarFeature = this)
                }.toJSONObj(),
            )
        }
    }

    private fun handleSetFlexListeningFeature(data: JsonObject?, callback: JsCallback) {
        viewModelScope.launch {
            runCatching {
                val setBean = data.toBean<FlexListeningFeature>()
                setBean?.also {
                    device.setProSetting(
                        flexListening = if (true != it.enabled
                            && ProdSettingResponse.FLEX_LISTENING_MONO != it.outputType
                            && ProdSettingResponse.FLEX_LISTENING_STEREO != it.outputType) {
                            ProdSettingResponse.OFF
                        } else {
                            (it.outputType ?: ProdSettingResponse.FLEX_LISTENING_MONO).toString()
                        }, port = device.getBarPort()
                    )
                }
                callback.invoke("0", null, null)
            }.onFailure {
                it.printStackTrace()
                callback.invoke("-1", it.message, null)
            }
        }
    }

    private fun handleGetFlexListeningFeature(callback: JsCallback) {
        viewModelScope.launch {
            val prodSetting = if (featureSupport?.pureVoice?.isSupport == true) {
                device.getProSetting(device.getBarPort())
            } else null

            notifyBroadcasting(callback, prodSetting)
        }
    }

    fun notifyBroadcasting(callback: JsCallback, prodSetting: ProdSettingResponse<*, *>?){
        callback.invoke(
            "0",
            null,
            prodSetting.toFlexListeningFeature().apply {
                Logger.d(TAG, "FlexListeningFeature: $this")
            }.toJSONObj(),
        )
    }

    var campaign: CampaignResponse? = null

    private fun reportCampaignBanner(dut: Device, campaignResponse: CampaignResponse?){
        val campaignList = campaignResponse?.campaigns ?: return
        val ids = campaignList.map { it.id }?.joinToString(separator = ";")
        Logger.d(TAG, "roon_ready_firebase $ids")
        FirebaseEventManager.report(
            eventName = EventUtils.EventName.EVENT_CAMPAIGN_BANNER,
            device = dut,
            dimensions = mapOf(
                EventUtils.Dimension.DI_CAMPAIGN_IDS to ids
            )
        )
    }

    private fun handleGetRoonReadyStatus(activity: CommonHybridActivity, callback: JsCallback) {
        val roonReadySupport = device.featSupportExt?.featSupport?.isRoonReadySupport() == true
        Logger.d(TAG, "handleGetRoonReadyStatus() roonReady support $roonReadySupport")
        Logger.d(TAG, "handleGetRoonReadyStatus() featSupport ${device.featSupportExt?.featSupport}")
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val hasClickRoonReadyCard = RoonReadyManager.hasClickRoonReadyCard(device.UUID)
            if (!hasClickRoonReadyCard && roonReadySupport) {
                campaign = RoonReadyManager.getCampaignFromCache(device.UUID)
                var code = campaign?.couponCampaign?.data?.deviceCouponCode
                if (!code.isNullOrBlank()) {
                    val status = RoonReadyStatus(true, code)
                    reportCampaignBanner(device,campaign)
                    callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(status)))
                } else {
                    campaign = RoonReadyManager.getRemoteCouponCampaign(device)
                    code = campaign?.couponCampaign?.data?.deviceCouponCode
                    val status = RoonReadyStatus(!code.isNullOrBlank(), code)
                    if(!code.isNullOrBlank()){
                        reportCampaignBanner(device,campaign)
                    }
                    callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(status)))
                }
            }
        }
    }

    private fun handleCopyCouponCode(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "onCustomEvent() >>> handleCopyCouponCode")
        val redeemCode = campaign?.couponCampaign?.data?.deviceCouponCode
        if (redeemCode.isNullOrBlank()) {
            return
        }
        val clipboard = activity.getSystemService(FragmentActivity.CLIPBOARD_SERVICE) as? ClipboardManager ?: return
        clipboard.setPrimaryClip(ClipData.newPlainText("Copied Text", redeemCode))
    }

    private fun handleToRoonReadyWeb(activity: CommonHybridActivity, callback: JsCallback) {
        Logger.d(TAG, "onCustomEvent() >>> handleToRoonReadyWeb")
        val redeemCode = campaign?.couponCampaign?.data?.deviceCouponCode
        if (redeemCode.isNullOrBlank()) {
            return
        }
        activity.portalUrl(
            config = AppConfigurationUtils.getRoonRedeemFormat()?.format(redeemCode),
            default = RoonReadyManager.DEFAULT_ROON_READY_REDEEM_URL.format(redeemCode)
        )
    }

    private fun handleEnterQSG(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterQSG()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlQsg ?: return
        Logger.d(TAG, "launchQsg pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("jbl_Quick_Start_Guide"))
        callback.invoke("0", null, null)

    }

    private fun handleEnterProductSupport(activity: CommonHybridActivity, callback: JsCallback, pidFromJS: String?) {
        Logger.d(TAG, "handleEnterProductSupport()")
        val pid = pidFromJS ?: return
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlSupport ?: return
        Logger.d(TAG, "handleEnterProductSupport pid: $pid, \nurls: $linkUrl")
        loadingUrlInAppWebView(activity, linkUrl, SkinResourcesUtils.getString("harmanbar_jbl_Product_Support"))
        callback.invoke("0", null, null)

    }

    private fun loadingUrlInAppWebView(activity: CommonHybridActivity, linkUrl: String, title: String) {
        Logger.d(TAG, "loadingUrlInAppWebView Url: $linkUrl")
        QuickStartGuideWebActivity.launchQuickStartGuideWebActivity(activity, linkUrl, title)
    }

    private fun handleGetPlayInfo(
        activity: FragmentActivity,
        device: OneDevice,
        callback: JsCallback
    ) {
        callback.invoke("0", null, null)
//        notifyPlayInfo(device = device, force = true)
        super.notifyPlayInfo(PlayInfo(musicSource = 5, musicName = "Audio", artistName = "Wireless TV"))
    }

    fun notifyDeviceInfo() {
        notifyDeviceInfo(
            info = device.toDeviceInfo(
                status = if (device.isWiFiOnline) EnumDeviceInfoStatus.WIFI_CONNECTED else EnumDeviceInfoStatus.BLE_CONNECTED,
                groupInfo = device.groupInfoExt?.groupInfo,
            ),
        )
    }

    private fun handleSecondaryDeviceInfos() {
        notifySecondaryDeviceInfos(device.toSecondaryDeviceInfoList())
    }

    private fun handleVolumeChange(
        device: OneDevice,
        data: JsonObject?,
        callback: JsCallback,
    ) {
        val volume = data?.get("volume")?.asInt ?: return
        Logger.i(TAG, "handleVolumeChange() >>> volume[$volume]")

        device.setRemoteVolume(volume.roundPercentage())
        callback.invoke("0", null, null)
    }

    @MainThread
    private suspend fun handleGetEQ(
        device: OneDevice,
        callback: JsCallback
    ) {
        val eqInfo = syncBuildGetEQ(device = device) ?: return
        callback.invoke("0", null, JSONObject(GsonUtil.parseBeanToJson(eqInfo)))
        notifyEQInfo(eqInfo)
    }

    private fun EQSetting?.mapToEQList(): List<EQItem>? {
        val eqSetting = this ?: return null

        val eqItem = EQItem(
            eqName = eqSetting.eqName,
            eqId = eqSetting.eqID,
            fs = eqSetting.eqPayload?.fs?.map { it.toInt() },
            gain = eqSetting.eqPayload?.gain?.map { it.toDouble() },
            max = 6.0,
            min = -6.0
        )

        return listOf(eqItem)
    }

    private fun List<EQListItem>?.mapToEQList(): List<EQItem>? {
        val eqListItem = this ?: return null

        return eqListItem.map { item ->
            EQItem(
                eqName = item.eqName,
                eqId = item.eqID,
                fs = item.eqListPayload?.fs?.map { it.toInt() },
                gain = item.eqListPayload?.gain?.map { it.toDouble() },
                max = 6.0,
                min = -6.0
            )
        }
    }

    @MainThread
    private fun handleSetEQ(
        device: OneDevice,
        data: JsonObject?,
        callback: JsCallback
    ) {
        val setEQ = GsonUtil.parseJsonToBean(data?.toString(), SetEQ::class.java) ?: run {
            Logger.w(TAG, "handleSetEQ() >>> fail to parse data as SetEQ:${data?.toString()}")
            callback.invoke("-1", "fail to parse set eq bean", null)
            return
        }

        if (featureSupport?.userEQ?.band == "7" ){
            handleSet7BandsEQ(device = device, setEQ = setEQ, callback = callback)
        } else {
            handleSetEQ(device = device, setEQ = setEQ, callback = callback)
        }
    }

    @MainThread
    private fun handleSetEQ(device: OneDevice, setEQ: SetEQ, callback: JsCallback) {
        val settings = retrieveEqSetting(device = device, setEQ = setEQ, callback = callback) ?: run {
            callback.invoke("-1", "fail to retrieve set eq request", null)
            return
        }
        // send async and ignore result.
        device.setEQ(eq = settings)
        device.updateEQSettings(settings)
        callback.invoke("0", "set eq success", null)
    }

    @MainThread
    private fun handleSet7BandsEQ(device: OneDevice, setEQ: SetEQ, callback: JsCallback) {
        val request = retrieve7BandsEQRequest(device = device, setEQ = setEQ, callback = callback) ?: run{
            callback.invoke("-1", "fail to retrieve set active eq request", null)
            return
        }

        // send async and ignore result.
        device.setActiveEQ(request,device.getBarPort())
//        device.updateActiveEQ(request)
        eqListCache?.activeEQID = request.activeEQID

        if (request.isCustomEQActive()) {
            eqListCache?.findCustomEQ()?.eqListPayload?.gain = request.eqPayload?.gain
        }
        callback.invoke("0", "set preset eq success", null)
    }

    private fun retrieveEqSetting(
        device: OneDevice,
        setEQ: SetEQ,
        callback: JsCallback
    ): EQSetting? {
        val eqSettings = device.eqExt?.data?.eqSetting ?: run {
            Logger.w(TAG, "retrieveEqSetting() >>> empty eq settings from device instance")
            callback.invoke("-1", "eq list is empty", null)
            return null
        }

        return EQSetting(
            eqName = eqSettings.eqName,
            eqID = eqSettings.eqID,
            eqStatus = eqSettings.eqStatus,
            band = eqSettings.band,
            eqPayload = EQPayload(
                type = eqSettings.eqPayload?.type,
                q = eqSettings.eqPayload?.q,
                fs = eqSettings.eqPayload?.fs,
                gain = setEQ.gain?.map { it.toFloat() }
            )
        )
    }

    private fun retrieve7BandsEQRequest(
        device: OneDevice,
        setEQ: SetEQ,
        callback: JsCallback
    ): SetActiveEQItemRequest? {
        val eqList = eqListCache?: run {
            Logger.w(TAG, "retrieve7BandsEQRequest() >>> empty eq list from device instance")
            callback.invoke("-1", "eq list is empty", null)
            return null
        }

        val eqItem = eqList.eqList?.firstOrNull { item ->
            item.eqID == setEQ.eqId
        }

        if (null == eqItem) {
            Logger.w(TAG, "setEQOnPresetEQSupport() >>> didn't find target eq item match id[${setEQ.eqId}]")
            callback.invoke("-1", "didn't find target eq item match id[${setEQ.eqId}]", null)
            return null
        }

        val payload = EQListPayload(
            gain = if (eqItem.isCustomEQ()) {
                // use passback gain from hybrid
                setEQ.gain?.map { it.toFloat() }
            } else {
                // use gain from eq list
                eqItem.eqListPayload?.gain
            },
            fs = eqItem.eqListPayload?.fs
        )

        return SetActiveEQItemRequest(
            activeEQID = eqItem.eqID,
            band = eqItem.band,
            eqPayload = payload
        )
    }

    @MainThread
    private suspend fun getFeatureSupport(device: OneDevice): FeatureSupport =
        suspendCancellableCoroutine<FeatureSupport> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onFeatureSupport(featureSupport: FeatureSupport) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(featureSupport)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.getFeatureSupport(device.getBarPort())
        }

    @MainThread
    private suspend fun getEQList(device: OneDevice): EQListResponse =
        suspendCancellableCoroutine<EQListResponse> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onEQList(eqListResponse: EQListResponse) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(eqListResponse)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.getEQList(device.getBarPort())
        }

    @MainThread
    private suspend fun getEQ(device: OneDevice): EQResponse =
        suspendCancellableCoroutine<EQResponse> { continuation ->
            val listener = object : IOneDeviceListener {
                override fun onEQ(eqResponse: EQResponse) {
                    device.unregisterDeviceListener(this)
                    continuation.safeResume(eqResponse)
                }
            }

            continuation.invokeOnCancellation {
                device.unregisterDeviceListener(listener)
            }
            device.registerDeviceListener(listener)
            device.getEQ(device.getBarPort())
        }

    private var jblOneDeviceListener: OneDeviceListenerProxy? = null

    inner class OneDeviceListenerProxy(
        private val owner: LifecycleOwner
    ) : IOneDeviceListener {

        private val activity = owner as? FragmentActivity

        override fun onUpnpNotifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {
            Logger.d(TAG, "onUpnpNotifyGroupDevicesChange() >>> [${device.UUID}] [$uuid] [${device.wifiDevice?.deviceItem?.uuid}] $result")
            super.onUpnpNotifyGroupDevicesChange(uuid, result)
            if(uuid == device.wifiDevice?.deviceItem?.uuid){
                viewModelScope.launch {
                    result?.payload?.rearSpeakerStatus?.rears?.let {
                        notifyDeviceInfo()
                        updateJsSoundbarFeature(it)
                    }

                    result?.payload?.surroundState?.level?.let {
                        updateJsSoundbarFeatureBySurroundLevel(it)

                    }

                    result?.payload?.eqList?.let {
                        syncNotifyEQInfo(eqListResponse = it)

                    }

                    result?.payload?.prodSetting?.let{
                        if(it.containsKey(ProdSetting.FlexListening.value)){
                            notifyBroadcastingByUpnp(it)
                        }
                        if(it.containsKey(ProdSetting.PureVoice.value)){
                            updateJsSoundbarFeatureByPureVoice(it.isPureVoiceOn)
                        }


                    }
                }
            }
        }

        override fun onUpnpSetCastGroup(uuid: String, result: GetGroupInfo?) {
            super.onUpnpSetCastGroup(uuid, result)
            Logger.d(TAG, "onUpnpSetCastGroup() >>> [${device.UUID}] [$uuid] [${device.wifiDevice?.deviceItem?.uuid}] $result")
            if(uuid == device.wifiDevice?.deviceItem?.uuid){
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    notifyDeviceInfo()
                    handleSecondaryDeviceInfos()
                }
            }
        }

        override fun onSetCastGroup(success: Boolean) {
            super.onSetCastGroup(success)
            Logger.d(TAG, "onSetCastGroup() >>> [${device.UUID}]")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                handleSecondaryDeviceInfos()
            }
        }

        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            gattConnectStatusListener?.invoke(status, session)
        }

        @WorkerThread
        override fun onDeviceInfo(rsp: GetDeviceInfoResponse) {
            val context = WAApplication.me ?: return

            val info = rsp.deviceInfo
            if (rsp.success() && null != info) {
                viewModelScope.launch(DISPATCHER_DEFAULT) {
                    LocalCacheAdapter.upsert(device = device, ctx = context)
                }
            }
        }

        @WorkerThread
        override fun onGetGroupParameter(rsp: GetGroupParameterRsp) {
            super.onGetGroupParameter(rsp)
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
                handleSecondaryDeviceInfos()
            }
        }

        @WorkerThread
        override fun onSpeakerRearNotify(rears: List<Rear>) {
            Logger.d(TAG, "onSpeakerRearNotify() >>> [${device.UUID}] rsp:$rears")
            super.onSpeakerRearNotify(rears)
            viewModelScope.launch {
                notifyDeviceInfo()
                updateJsSoundbarFeature(rears)
            }
        }

        override fun onBatteryStatus(rsp: BatteryStatusResponse) {
            viewModelScope.launch {
                notifyDeviceInfo()
            }
        }

        override fun onC4aPermissionStatus(rsp: C4aPermissionStatusResponse) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyDeviceInfo()
            }
        }

        override fun onEQ(eqResponse: EQResponse) {
            Logger.d(TAG, "onEQ() >>> [${device.UUID}] rsp:$eqResponse")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                syncNotifyEQInfo(eqResponse = eqResponse)
            }
        }

        override fun onEQList(eqListResponse: EQListResponse) {
            Logger.d(TAG, "onEQList() >>> [${device.UUID}] rsp:$eqListResponse")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                syncNotifyEQInfo(eqListResponse = eqListResponse)
            }
        }

        @WorkerThread
        override fun onGroupDeviceFlag(rsp: GetGroupDevicesFlagRsp) {
            Logger.d(TAG, "onGroupDeviceFlag() >>> $rsp")
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                if (rsp.needCalibration && null != activity) {
                    eventStream.tryEmit(OHVMEvent.ShowCalibrationGuideDialog)
                }

                if (rsp.needGoogleCast) {
                    notifyDeviceInfo()
                }
            }
        }
    }

    private suspend fun syncNotifyEQInfo(
        eqResponse: EQResponse? = null,
        eqListResponse: EQListResponse? = null
    ) {
        val eqInfo = syncBuildGetEQ(
            device = device,
            eqResponse = eqResponse,
            eqListResponse = eqListResponse
        ) ?: return
        notifyEQInfo(info = eqInfo)
    }

    private fun updateJsSoundbarFeature(rears: List<Rear>?) {
        uiState.jsSoundbarFeature.rearSpeaker?.copy(enable = rears?.all { it.displayBattery() })
            .let {
                uiState.jsSoundbarFeature.copy(rearSpeaker = it)
            }.let {
                uiState.copy(jsSoundbarFeature = it)
            }.also {
                uiState = it
                notificationInvoker?.notifyObj(
                    Events.NOTIFY_SOUNDBAR_FEATURE,
                    it.jsSoundbarFeature.toJSONObj(),
                )
            }
    }

    private fun updateJsSoundbarFeatureBySurroundLevel(level: Int?) {
        uiState.jsSoundbarFeature.rearSpeaker?.copy(level = level)
            .let {
                uiState.jsSoundbarFeature.copy(rearSpeaker = it)
            }.let {
                uiState.copy(jsSoundbarFeature = it)
            }.also {
                uiState = it
                notificationInvoker?.notifyObj(
                    Events.NOTIFY_SOUNDBAR_FEATURE,
                    it.jsSoundbarFeature.toJSONObj(),
                )
            }
    }

    private fun updateJsSoundbarFeatureByPureVoice(status: Boolean?) {
        uiState.jsSoundbarFeature.copy(pureVoiceStatus = status)
            .let{
                uiState.copy(jsSoundbarFeature = it)
            }.also { uiState = it
                notificationInvoker?.notifyObj(
                    Events.NOTIFY_SOUNDBAR_FEATURE,
                    it.jsSoundbarFeature.toJSONObj(),
                ) }
    }

    fun notifyBroadcastingByUpnp(prodSetting: ProdSettingResponse<*, *>?){
        val nightListeningObj = prodSetting.toFlexListeningFeature().apply {
            Logger.d(TAG, "notifyBroadcastingByUpnp: $this")
        }.toJSONObj()
        notificationInvoker?.notifyObj(Events.NOTIFY_BROADCASTING_INFO, nightListeningObj)

    }

    @MainThread
    private fun handleEnterProductSettings(
        activity: CommonHybridActivity,
        device: OneDevice,
        callback: JsCallback
    ) {
        val result = BaseProductSettingsActivity.portal(
            activity = activity,
            device = device,
            requestCode = DeviceControlHybridActivity.REQUEST_CODE
        )

        callback.invoke(if (result) "0" else "-1", null, null)
    }

    private fun EQListItem.isCustomEQ(): Boolean {
        return CUSTOM_EQ_ID == this.eqID
    }

    @MainThread
    private fun handleEnterCalibration(callback: JsCallback) {
        eventStream.tryEmit(OHVMEvent.ShowCalibrationGuideDialog)
        callback.invoke("0", null, null)
    }

    @MainThread
    private fun handleEnterRemoteControl(
        activity: CommonHybridActivity,
        callback: JsCallback
    ) {
        if (activity.portalActivity(target = RemoteControllerActivity::class.java, device = device)) {
            callback.invoke("0", null, null)
        } else {
            callback.invoke("-1", null, null)
        }
    }

    // Memory cache to reduce the visit time from DB.
    private var sESSID: String? = null

    @Volatile
    private var sESSIDRestoreFlag = false

    /**
     * While device is not WiFi online:
     * Show [EnumNetworkCardViewStyle.NETWORK_NOT_CONNECTED] if exists essid in DB cache before,
     * or [EnumNetworkCardViewStyle.NEVER_SETUP_BEFORE] if not.
     */
    @MainThread
    private suspend fun notifyNetworkInfo(isVisible: Boolean) {
        if (!isVisible) {
            notifyNetworkInfo(
                NetworkInfo(
                    viewStyle = EnumNetworkCardViewStyle.NOT_DISPLAYED.value,
                    essid = null
                )
            )
            return
        }

        val eSSID = eSSIDFromCache()
        val viewStyle = if (!eSSID.isNullOrBlank()) {
            EnumNetworkCardViewStyle.NETWORK_NOT_CONNECTED
        } else {
            EnumNetworkCardViewStyle.NEVER_SETUP_BEFORE
        }

        notifyNetworkInfo(
            NetworkInfo(
                viewStyle = viewStyle.value,
                essid = eSSID
            )
        )
    }

    /**
     * Read from DB only once in each lifecycle.
     */
    @MainThread
    private suspend fun eSSIDFromCache(): String? {
        if (sESSIDRestoreFlag) {
            return sESSID
        }

        sESSIDRestoreFlag = true
        sESSID = withContext(DISPATCHER_IO) {
            val ctx = WAApplication.me ?: return@withContext null
            val uuid = device.UUID ?: return@withContext null

            LocalCacheAdapter.getTargetDeviceCache(context = ctx, uuid = uuid)
        }?.eSSID

        return sESSID
    }

    /**
     * Refresh card view style when device online/offline.
     */
    @MainThread
    private fun refreshNetworkCardViewStyle() {
        _networkCardVisible.value = !device.isWiFiOnline
    }

    private val scanObserver = object : IHmDeviceObserver {
        @AnyThread
        override fun onDeviceOffline(device: Device) {
            if (device is OneDevice && device == <EMAIL>) {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    refreshNetworkCardViewStyle()
                }
            }
        }

        @AnyThread
        override fun onDeviceOnlineOrUpdate(device: Device) {
            if (device is OneDevice && device == <EMAIL>) {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    refreshNetworkCardViewStyle()
                }
            }
        }
    }

    var featureSupport: FeatureSupport? = null
    @MainThread
    private suspend fun syncBuildGetEQ(
        device: OneDevice,
        eqResponse: EQResponse? = null,
        eqListResponse: EQListResponse? = null
    ): GetEQ? {
        Logger.d(TAG, "syncBuildGetEQ() >>> cached featSupport:\n${featureSupport}")
        if(featureSupport == null){
            featureSupport = getFeatureSupport(device)
        }
        val band = featureSupport?.userEQ?.band
        Logger.d(TAG, "syncBuildGetEQ() >>> featSupport:\n${GsonUtil.parseBeanToJson(featureSupport)}")
        Logger.d(TAG, "syncBuildGetEQ() >>> featSupport band:\n${band}, ${band== "7"}")

        return if (band== "7") {
            syncBuild7BandsGetEQ(eqListResponse = eqListResponse)
        } else {
            syncBuildNon7BandsGetEQ(eqResponse = eqResponse)
        }
    }
    lateinit var eqListCache: EQListResponse
    @MainThread
    private suspend fun syncBuild7BandsGetEQ(eqListResponse: EQListResponse? = null): GetEQ? {
        eqListCache = eqListResponse ?: getEQList(device)
        Logger.d(TAG, "syncBuild7BandsGetEQ() >>> eqList:\n${GsonUtil.parseBeanToJson(eqListCache)}")

        return if (eqListCache.success()) {
            GetEQ(
                isEnable = true,
                currentEQId = eqListCache.activeEQID,
                customEQId = CUSTOM_EQ_ID,
                eqList = eqListCache.eqList.mapToEQList()?.filter {
                    if (featureSupport?.userEQ?.presetSupport != "true") {
                        it.eqId == CUSTOM_EQ_ID
                    } else true
                }
            )
        } else {
            Logger.w(TAG, "syncBuild7BandsGetEQ() >>> fail to get eq list")
            null
        }
    }

    @MainThread
    private suspend fun syncBuildNon7BandsGetEQ(
        eqResponse: EQResponse? = null
    ): GetEQ {
        val eqRsp = eqResponse ?: getEQ(device = device)
        Logger.d(TAG, "syncBuildNon7BandsGetEQ() >>> eqRsp:\n${GsonUtil.parseBeanToJson(eqRsp)}")

        return GetEQ(
            isEnable = eqRsp.success(),
            currentEQId = eqRsp.eqSetting?.eqID,
            customEQId = eqRsp.eqSetting?.eqID,
            eqList = eqRsp.eqSetting.mapToEQList()
        )
    }

    override val tag: String = TAG

    companion object {
        private const val TAG = "OneCommanderGroupHybridViewModel"
        const val LEARN_MORE_ABOUT_VA = "https://www.jbl.com/mae"
    }
}

