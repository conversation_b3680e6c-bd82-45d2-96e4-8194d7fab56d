package com.harman.webview.models

import android.app.Activity
import androidx.activity.result.ActivityResult
import androidx.annotation.MainThread
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.google.gson.JsonObject
import com.harman.EventUtils
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.session.OneBusiness
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools.overThreshold
import com.harman.log.Logger
import com.harman.oobe.IOOBEDialogEventListener
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.WiFiOOBEDialogHelper
import com.harman.product.info.ProductInfoActivity
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.CommonHybridActivity
import com.harman.webview.EnumDeviceInfoStatus
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.wifiaudio.model.DeviceItem

/**
 * Created by gerrardzhang on 2024/10/21.
 */
class OneDeviceReadyToConnectViewModelFactory(
    private val device: OneDevice
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OneDeviceReadyToConnectViewModel(device = device) as T
    }
}

class OneDeviceReadyToConnectViewModel(
    override val device: OneDevice
) : ViewModel() , IHybridViewModel<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem,
        OneDevice> {

    override val tag: String = TAG

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    private var wifiOOBEDialogHelper: WiFiOOBEDialogHelper? = null

    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: JsCallback
    ) {
        super.onCustomEvent(activity, eventName, data, callback)

        when (eventName) {
            Events.GET_DEVICE_INFO -> {
                handleGetDeviceInfo()
            }
            Events.ENTER_PRODUCT_INFO -> {
                handleEnterProductInfo(activity = activity, device = device, callback = callback)
            }
            Events.SETUP_WIFI -> {
                handleSetupWifi(activity = activity, device = device, callback = callback)
            }
        }
    }

    private fun handleGetDeviceInfo() {
        notifyDeviceInfo(info = device.toDeviceInfo(status = EnumDeviceInfoStatus.READY_TO_CONNECT))
    }

    private fun handleEnterProductInfo(
        activity: Activity,
        device: OneDevice,
        callback: JsCallback
    ) {
        Logger.d(TAG, "handleEnterProductInfo() >>> ")
        ProductInfoActivity.launchProductInfoPage(activity, device)
        callback.invoke("0", null, null)
    }

    private var lastObserveTs: Long = -1L

    private fun handleSetupWifi(
        activity: CommonHybridActivity,
        device: OneDevice,
        callback: JsCallback
    ) {
        if (device.isSecureBleSupport &&
            !lastObserveTs.overThreshold(thresholdMills = 60000, setter = {
                lastObserveTs = it
            })
        ) {
            Logger.w(TAG, "handleSetupWifi() >>> block by RED threshold")
            callback.invoke("0", null, null)
            return
        }

        Logger.d(TAG, "handleSetupWifi() >>> ")
        wifiOOBEDialogHelper?.dismissAllDialogs()
        wifiOOBEDialogHelper = WiFiOOBEDialogHelper(
            logTag = TAG,
            activity = activity,
            device = device,
            mode = EnumMode.WIFI_SETUP,
            oobeType = EventUtils.Dimension.EnumOOBEType.MANUAL,
            oobeDialogDismissListener = null,
            oobeDialogEventListener = object : IOOBEDialogEventListener {
                @MainThread
                override fun onSetupWiFiSuccess() {
                    eventListener?.refreshAsOnline(device = device)
                }
            },
            calibrationDialogEvent = null,
            vaGuideDialogDismissListener = null
        ).run()

        callback.invoke("0", null, null)
    }

    companion object {
        private const val TAG = "OneDeviceReadyToConnectViewModel"
    }
}