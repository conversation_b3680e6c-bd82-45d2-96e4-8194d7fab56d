package com.harman.webview.models

import androidx.core.os.bundleOf
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.google.gson.JsonObject
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.session.BaseBusinessSession
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.context
import com.harman.deepCopy
import com.harman.discover.DeviceScanner
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.info.DefaultRole
import com.harman.discover.util.Tools.toJSONObj
import com.harman.log.Logger
import com.harman.ota.PartyOtaChecker
import com.harman.ota.PartyOtaCheckerParams
import com.harman.partyband.drummetronome.DrumMetronomeActivity
import com.harman.partyband.guitarpreset.GuitarPresetActivity
import com.harman.partyband.guitarpreset.OnProductPresetActivity
import com.harman.partyband.looper.PartyBandGuitarLooperActivity
import com.harman.partyband.micsensitivity.MicSensitivityActivity
import com.harman.partyband.ota.PartyBandOtaActivity
import com.harman.partyband.output.AudioPlayerServerBind
import com.harman.partyband.output.PartBandUsbOutputActivity
import com.harman.partyband.control.ToneShifterDialog
import com.harman.partyband.tuner.TunerActivity
import com.harman.partylight.util.push
import com.harman.product.info.ProductInfoActivity
import com.harman.product.setting.activity.BaseProductSettingsActivity
import com.harman.report.OneCloudAnalyticsTemp
import com.harman.report.OneCloudReporter
import com.harman.thread.DISPATCHER_API
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.toBean
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.EQCategoryEnum
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.SoloMusicianEnum
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationKaraokeMode
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationMode
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationTrackType
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationTrackTypeQuery
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationTrackVolume
import com.harman.v5protocol.bean.devinfofeat.V5AIStemSeparationTrackVolumeQuery
import com.harman.v5protocol.bean.devinfofeat.V5AUXType
import com.harman.v5protocol.bean.devinfofeat.V5ActiveAudioSource
import com.harman.v5protocol.bean.devinfofeat.V5AudioSource
import com.harman.v5protocol.bean.devinfofeat.V5AudioVolume
import com.harman.v5protocol.bean.devinfofeat.V5CHInputType
import com.harman.v5protocol.bean.devinfofeat.V5ChannelInputStatus
import com.harman.v5protocol.bean.devinfofeat.V5ChannelInputStatusQuery
import com.harman.v5protocol.bean.devinfofeat.V5ChannelNumber
import com.harman.v5protocol.bean.devinfofeat.V5ChannelVolume
import com.harman.v5protocol.bean.devinfofeat.V5ChannelVolumeQuery
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5DeviceName
import com.harman.v5protocol.bean.devinfofeat.V5DeviceOOBE
import com.harman.v5protocol.bean.devinfofeat.V5EqInfo
import com.harman.v5protocol.bean.devinfofeat.V5EqInfoQuery
import com.harman.v5protocol.bean.devinfofeat.V5EqInfoWrap
import com.harman.v5protocol.bean.devinfofeat.V5LeftDeviceBatteryStatus
import com.harman.v5protocol.bean.devinfofeat.V5LeftSerialNumber
import com.harman.v5protocol.bean.devinfofeat.V5MasterControlEQInfo
import com.harman.v5protocol.bean.devinfofeat.V5MasterControlEQInfoQuery
import com.harman.v5protocol.bean.devinfofeat.V5Mic1Echo
import com.harman.v5protocol.bean.devinfofeat.V5Mic1Reverb
import com.harman.v5protocol.bean.devinfofeat.V5Mic2Echo
import com.harman.v5protocol.bean.devinfofeat.V5Mic2Reverb
import com.harman.v5protocol.bean.devinfofeat.V5MicKeyboardEQInfo
import com.harman.v5protocol.bean.devinfofeat.V5MicKeyboardEQInfoQuery
import com.harman.v5protocol.bean.devinfofeat.V5PartyGroupType
import com.harman.v5protocol.bean.devinfofeat.V5PartyGroupTypeEnum
import com.harman.v5protocol.bean.devinfofeat.V5PitchChange
import com.harman.v5protocol.bean.devinfofeat.V5TrackNumber
import com.harman.v5protocol.discover.PartyInfo
import com.harman.webview.BatteryInfo
import com.harman.webview.BeanMap.toDeviceInfo
import com.harman.webview.CommonHybridActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.webview.DeviceInfo
import com.harman.webview.EQItem
import com.harman.webview.GetEQ
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.JsAISetStemSeparation
import com.harman.webview.JsAIStemSeparation
import com.harman.webview.JsAIStemSeparation.Companion.SRC_AUX
import com.harman.webview.JsAIStemSeparation.Companion.SRC_BT
import com.harman.webview.JsAIStemSeparation.Companion.SRC_USB_AUDIO
import com.harman.webview.JsAIStemSeparation.Companion.SRC_USB_DRIVE
import com.harman.webview.JsBandBoxFunction
import com.harman.webview.JsBandBoxMicInfo
import com.harman.webview.JsBandBoxModelFunction
import com.harman.webview.JsBandBoxModelInfo
import com.harman.webview.JsBandBoxModelInfo.Companion.AT_MUSIC
import com.harman.webview.JsBandBoxModelInfo.Companion.AT_PIANO
import com.harman.webview.JsBandBoxModelInfo.Companion.CDT_35MM_UNPLUGIN
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_S_BUILT_IN_MIC
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_S_CH1
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_S_MUSIC
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_T_CH1
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_T_CH2
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_T_CH3
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_T_CH4_INSTRUMENT
import com.harman.webview.JsBandBoxModelInfo.Companion.CI_ID_T_MUSIC
import com.harman.webview.JsBandBoxModelInfo.Companion.MODEL_GUITARIST
import com.harman.webview.JsBandBoxModelInfo.Companion.MODEL_SINGER
import com.harman.webview.JsBandBoxModelInfo.Companion.MODEL_SINGING
import com.harman.webview.JsGetBandBoxEq
import com.harman.webview.JsGetBandBoxMicInfo
import com.harman.webview.JsSetBandBoxMicInfo
import com.harman.webview.JsSetBandBoxModelInfo
import com.harman.webview.OTAStatus
import com.harman.webview.SetEQ
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.debug.OneCloudReportLocalCache
import com.wifiaudio.view.pagesmsccontent.showDebugToast
import com.wifiaudio.view.pagesmsccontent.showToast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @Description viewmodel for [PartyBandDevice] device control
 * <AUTHOR>
 * @Time 2024/12/6
 */
class PartyBandHybridViewFactory(
    private val device: PartyBandDevice
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return PartyBandHybridViewModel(device) as T
    }
}

class PartyBandHybridViewModel(
    override var device: PartyBandDevice
) : ViewModel(),
    IHybridViewModel<BaseBusinessSession, DefaultSppBusinessSession, PartyBandBTDevice, DefaultRole, PartyInfo.Role, PartyBandBTDevice, PartyBandDevice> {
    override val tag = "PartyBandHybridViewModel"
    override var notificationInvoker: IHybridNotification? = null
    override var gattConnectStatusListener: GattStatusListener? = null
    override var brEdrConnectStatusListener: BrEdrStatusListener? = null
    override var eventListener: IViewModelEvents? = null
    val eventStream = MutableSharedFlow<PBHVMEvent>(replay = 1, extraBufferCapacity = 1)
    private val presetMusicEqList by lazy {
        try {
            AppConfigurationUtils.getRemoteEq(device.pid!!)!!.map { V5EqInfo.fromCloudPreset(it) }.toMutableList()
        } catch (e: Exception) {
            showDebugToast("parse cloud preset eq fail $e")
            null
        }
    }
    private val scanObserver by lazy {
        object : IHmDeviceObserver {
            override fun onDeviceOnlineOrUpdate(device: Device) {
                super.onDeviceOnlineOrUpdate(device)
                val thisDev = <EMAIL>
                if (device is PartyBandDevice && thisDev.UUID == device.UUID && thisDev.bleAddress != device.bleAddress) {
                    thisDev.unregisterDeviceListener(deviceListener)
                    <EMAIL> = device
                    device.registerDeviceListener(deviceListener)
                }
            }
        }
    }
    private val deviceListener = object : IV5GattListener {
        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            super.onGattStatusChanged(status, session)
            gattConnectStatusListener?.invoke(status, session)
        }

        override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
            super.onDevFeat(devInfoMap, isNotify)
            viewModelScope.launch {
                (devInfoMap[V5DevInfoFeatID.AIStemSeparationTrackType] as? V5AIStemSeparationTrackType)?.also {
                    onAIStemSeparationTrackType(it)
                }
                (devInfoMap[V5DevInfoFeatID.AIStemSeparationTrackVolume] as? V5AIStemSeparationTrackVolume)?.also {
                    onAIStemSeparationTrackVolume(it)
                }
                (devInfoMap[V5DevInfoFeatID.AIStemSeparationKaraokeMode] as? V5AIStemSeparationKaraokeMode)?.also {
                    onAIStemSeparationKaraokeMode(it)
                }
                (devInfoMap[V5DevInfoFeatID.AIStemSeparationMode] as? V5AIStemSeparationMode)?.also {
                    onAIStemSeparationMode(it)
                }
                (devInfoMap[V5DevInfoFeatID.DeviceOOBE] as? V5DeviceOOBE)?.also {
                    //solo only
                    onSoloMusicianChanged(it)
                }
                (devInfoMap[V5DevInfoFeatID.ChannelVolume] as? V5ChannelVolume)?.also {
                    onChannelVolume(it)
                }
                (devInfoMap[V5DevInfoFeatID.ActiveAudioSource] as? V5ActiveAudioSource)?.also {
                    onActiveAudioSource(it)
                }
                (devInfoMap[V5DevInfoFeatID.AudioVolume] as? V5AudioVolume)?.also {
                    onAudioVolume(it)
                }
                (devInfoMap[V5DevInfoFeatID.ChannelInputStatus] as? V5ChannelInputStatus)?.also {
                    onChannelInputStatus(it)
                }
                (devInfoMap[V5DevInfoFeatID.AUXType] as? V5AUXType)?.also {
                    //trio only
                    onAuxType(it)
                }
                (devInfoMap[V5DevInfoFeatID.Mic1Echo] as? V5Mic1Echo)?.also {
                    onMic1Echo(it)
                }
                (devInfoMap[V5DevInfoFeatID.Mic1Reverb] as? V5Mic1Reverb)?.also {
                    onMic1Reverb(it)
                }
                (devInfoMap[V5DevInfoFeatID.Mic2Echo] as? V5Mic2Echo)?.also {
                    onMic2Echo(it)
                }
                (devInfoMap[V5DevInfoFeatID.Mic2Reverb] as? V5Mic2Reverb)?.also {
                    onMic2Reverb(it)
                }
                (devInfoMap[V5DevInfoFeatID.LeftDeviceBatteryStatus] as? V5LeftDeviceBatteryStatus)?.also {
                    onLeftDeviceBatteryStatus(it)
                }
                (devInfoMap[V5DevInfoFeatID.LeftSerialNumber] as? V5LeftSerialNumber)?.also {
                    onLeftSerialNumber(it)
                }
                (devInfoMap[V5DevInfoFeatID.DeviceName] as? V5DeviceName)?.also {
                    onDeviceName(it)
                }
                (devInfoMap[V5DevInfoFeatID.PartyGroupType] as? V5PartyGroupType)?.also {
                    onPartyGroupType(it)
                }
                (devInfoMap[V5DevInfoFeatID.MasterControlEQInfo] as? V5MasterControlEQInfo)?.also {
                    onTrioMasterControlEQInfo(it)
                }
                (devInfoMap[V5DevInfoFeatID.EqInfo] as? V5EqInfoWrap)?.also {
                    onMusicEqInfo(it)
                }
                (devInfoMap[V5DevInfoFeatID.MicKeyboardEQInfo] as? V5MicKeyboardEQInfo)?.also {
                    if (isNotify) {
                        onMicKeyboardEQInfo(it)
                    }
                }
                (devInfoMap[V5DevInfoFeatID.PitchChange] as? V5PitchChange)?.also {
                    onPitchChange(it)
                }
            }
        }
    }

    private fun onPitchChange(pitch: V5PitchChange) {
        val jsPitchValue = if (pitch.value == 0) null else pitch.value
        jsState.bandBoxModelInfo.pitchValue = jsPitchValue
        notifyJsBandBoxModelInfo()
        notifyAiStemSeparationIfNeed(jsState.aiStemSeparation.copy(pitchValue = jsPitchValue))
    }

    private fun V5MicKeyboardEQInfo.toGetEq(): GetEQ? {
        return when (chNum) {
            V5ChannelNumber.CH1 -> {
                if (device.isSolo()) {
                    when (devState.soloMusician?.musician) {
                        SoloMusicianEnum.GUITAR_VOCALIST -> CI_ID_S_BUILT_IN_MIC
                        SoloMusicianEnum.SINGER -> CI_ID_S_CH1
                        else -> null
                    }
                } else {
                    CI_ID_T_CH1
                }
            }

            V5ChannelNumber.CH2 -> CI_ID_T_CH2
            V5ChannelNumber.CH4 -> CI_ID_T_CH4_INSTRUMENT
            else -> null
        }?.let { cid ->
            GetEQ(
                isEnable = true,
                currentEQId = EQCategoryEnum.CUSTOM_EQ.name,
                customEQId = EQCategoryEnum.CUSTOM_EQ.name,
                channelId = cid,
                eqList = listOf(
                    EQItem(
                        "",
                        EQCategoryEnum.CUSTOM_EQ.name,
                        listOf(0, 0, 0),
                        listOf(
                            bassGain, midGain, trebleGain
                        ).map { e -> e!!.toDouble() }
                        //todo zrz 目前h5不支持对单个band设置最值以及步长
                    ),
                )
            )
        }
    }

    private fun onMicKeyboardEQInfo(eqInfo: V5MicKeyboardEQInfo) {
        eqInfo.toGetEq()?.also {
            notificationInvoker?.notifyObj(Events.NOTIFY_BAND_BOX_EQ, it.toJSONObj())
        }
    }

    private fun onMusicEqInfo(data: V5EqInfoWrap) {
        if (data.info.category == EQCategoryEnum.CUSTOM_EQ) {
            //如果设备当前使用的是custom eq，需要更新到本地
            presetMusicEqList?.replaceAll {
                if (it.category == EQCategoryEnum.CUSTOM_EQ) it.copyWith(data.info) else it
            }
        }
    }

    private fun onTrioMasterControlEQInfo(data: V5MasterControlEQInfo) {
        devState.trioMasterEq = data.info
    }

    private fun onPartyGroupType(type: V5PartyGroupType) {
        notifyAiStemSeparationIfNeed(
            jsState.aiStemSeparation.copy(
                disableReasonText = when (type.type) {
                    V5PartyGroupTypeEnum.Normal -> null
                    V5PartyGroupTypeEnum.AuracastParty -> Utils.getApp().getString(R.string.the_product_is_in_partytogether_mode)
                    V5PartyGroupTypeEnum.AuracastStereo,
                    V5PartyGroupTypeEnum.TWSStereo,
                    V5PartyGroupTypeEnum.LongLastingStereo -> Utils.getApp().getString(R.string.the_product_is_in_a_stereo_group)
                },
            ),
        )
    }

    private fun onDeviceName(name: V5DeviceName) {
        jsState.devInfo.deviceName = name.name
        notifyDeviceInfo(jsState.devInfo)
    }

    private fun onLeftSerialNumber(serialNumber: V5LeftSerialNumber) {
        jsState.devInfo.serialNumber = serialNumber.serialNumber
        notifyDeviceInfo(jsState.devInfo)
    }

    private fun onLeftDeviceBatteryStatus(battery: V5LeftDeviceBatteryStatus) {
        jsState.devInfo.batteryInfos = listOf(
            BatteryInfo(battery.isCharging, true, battery.batteryPercent)
        )
        notifyDeviceInfo(jsState.devInfo)
    }


    private val devState = BandBoxDevState()
    private val jsState = BandBoxJsState(
        JsAIStemSeparation.default(),
        JsBandBoxModelInfo.default(device),
        JsBandBoxMicInfo(-1),
        device.toDeviceInfo(),
    )

    init {
        device.registerDeviceListener(deviceListener)
        DeviceScanner.registerObserver(scanObserver)
        DeviceScanner.stopScan(Utils.getApp(), false)
    }

    override suspend fun onCustomEvent(activity: CommonHybridActivity, eventName: String?, data: JsonObject?, callback: JsCallback) {
        super.onCustomEvent(activity, eventName, data, callback)
        when (eventName) {
            Events.GET_OTA_STATUS -> getOtaStatus(callback)
            Events.GET_DEVICE_INFO -> getDeviceInfo()
            Events.ENTER_OTA -> enterOta(activity, callback)
            Events.BAND_BOX_FUNCTION -> onBandBoxFunction(activity, data, callback)
            Events.ENTER_PRODUCT_SETTINGS -> onEnterProductSetting(activity, callback)
            Events.ENTER_PRODUCT_INFO -> onEnterProductInfo(activity, callback)
            Events.GET_AI_STEM_SEPARATION -> getAiStemSeparation(callback)
            Events.SET_AI_STEM_SEPARATION -> setAiStemSeparation(data, callback)
            Events.GET_BAND_BOX_MODEL_INFO -> getBandBoxModelInfo(callback)
            Events.SET_BAND_BOX_MODEL_INFO -> setBandBoxModelInfo(data, callback)
            Events.ENTER_BAND_BOX_MODEL_FUNCTION -> enterBandBoxModelFunction(activity, data, callback)
            Events.GET_BAND_BOX_MIC_INFO -> getBandBoxMicInfo(data, callback)
            Events.SET_BAND_BOX_MIC_INFO -> setBandBoxMicInfo(data, callback)
            Events.GET_BAND_BOX_EQ -> getBandBoxEq(data, callback)
            Events.SET_BAND_BOX_EQ -> setBandBoxEq(data, callback)
        }
    }

    private fun getChannelInputStatusByJsChannelId(cId: Int) = run {
        when (cId) {
            CI_ID_T_CH1 -> devState.trioCh1Status
            CI_ID_T_CH2 -> devState.trioCh2Status
            CI_ID_T_CH4_INSTRUMENT -> devState.trioCh4Status
            CI_ID_S_CH1 -> when (devState.soloMusician?.musician) {
                SoloMusicianEnum.GUITARIST,
                SoloMusicianEnum.GUITAR_VOCALIST -> V5ChannelInputStatus(V5ChannelNumber.CH1, true, V5CHInputType.Guitar)

                SoloMusicianEnum.SINGER -> V5ChannelInputStatus(V5ChannelNumber.CH1, true, V5CHInputType.Mic)
                else -> null
            }

            CI_ID_S_BUILT_IN_MIC -> V5ChannelInputStatus(V5ChannelNumber.CH1, true, V5CHInputType.Mic)
            else -> null
        }
    }

    private fun setBandBoxEq(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val setEq = data!!.toBean<SetEQ>()!!
            when (val cId = setEq.channelId) {
                CI_ID_T_CH1, CI_ID_T_CH2, CI_ID_T_CH4_INSTRUMENT, CI_ID_S_CH1, CI_ID_S_BUILT_IN_MIC -> {
                    //3 bands,trio ch1,ch2 mic,solo ch1 or built in mic
                    val chInputStatus = getChannelInputStatusByJsChannelId(cId)!!
                    device.asyncSetDevInfoFeat(
                        V5MicKeyboardEQInfo(
                            chInputStatus.channelNumber,
                            chInputStatus.inputType,
                            setEq.gain!![0].toFloat(),
                            setEq.gain[1].toFloat(),
                            setEq.gain[2].toFloat(),
                        )
                    )
                }

                CI_ID_T_MUSIC, CI_ID_S_MUSIC, null -> {
                    //7 bands,trio/solo music, null means trio master eq
                    run {
                        if (null == cId) {
                            devState.trioMasterEq!!.deepCopy().apply {
                                bandInfos.forEachIndexed { index, eachBand -> eachBand.gain = setEq.gain!![index].toFloat() }
                            }
                        } else {
                            if (setEq.eqId == EQCategoryEnum.CUSTOM_EQ.name) {
                                //custom_eq 才可以设置各个band值
                                presetMusicEqList?.find { it.category == EQCategoryEnum.CUSTOM_EQ }!!.deepCopy().apply {
                                    bandInfos.forEachIndexed { index, eachBand -> eachBand.gain = setEq.gain!![index].toFloat() }
                                }
                            } else {
                                presetMusicEqList?.find { it.category.name == setEq.eqId }!!
                            }
                        }
                    }.let {
                        if (null == cId) V5MasterControlEQInfo(it) else V5EqInfoWrap(it)
                    }.also {
                        device.asyncSetDevInfoFeat(it)
                    }
                }

                else -> throw Exception("wrong channel id")
            }
            callback.invoke("0", null, null)
        }.onFailure {
            callback.invoke("-1", "setBandBoxEq failed $it", null)
        }
    }

    private suspend fun getBandBoxEq(data: JsonObject?, callback: JsCallback) {
        runCatching {
            when (val cId = data!!.toBean<JsGetBandBoxEq>()!!.channelId) {
                CI_ID_T_CH1, CI_ID_T_CH2, CI_ID_T_CH4_INSTRUMENT, CI_ID_S_CH1, CI_ID_S_BUILT_IN_MIC -> {
                    //trio ch1,ch2 mic,solo ch1 or built in mic
                    val chInputStatus = getChannelInputStatusByJsChannelId(cId)!!
                    device.asyncSetDevInfoFeat(V5MicKeyboardEQInfoQuery(chInputStatus.channelNumber, chInputStatus.inputType))
                    val getEq = device.awaitDevInfo<V5MicKeyboardEQInfo>()!!.toGetEq()!!
                    callback.invoke("0", null, getEq.toJSONObj())
                }

                CI_ID_T_MUSIC, CI_ID_S_MUSIC, null -> {
                    //trio/solo music, null means trio master eq
                    val getEq = if (null == cId) {
                        device.asyncSetDevInfoFeat(V5MasterControlEQInfoQuery(EQCategoryEnum.CUSTOM_EQ))
                        device.awaitDevInfo<V5MasterControlEQInfo>()!!.info
                    } else {
                        device.asyncSetDevInfoFeat(V5EqInfoQuery(EQCategoryEnum.CURRENT_EQ))
                        device.awaitDevInfo<V5EqInfoWrap>()!!.info
                    }.let { curEqInfo ->
                        //current v5 eq
                        GetEQ(
                            isEnable = true,
                            currentEQId = curEqInfo.category.name,
                            customEQId = EQCategoryEnum.CUSTOM_EQ.name,
                            channelId = cId,
                            eqList = if (cId == CI_ID_T_MUSIC) presetMusicEqList?.map {
                                val bandInfos = if (it.category == curEqInfo.category) {
                                    curEqInfo.bandInfos
                                } else it.bandInfos
                                EQItem(
                                    it.name,
                                    it.category.name,
                                    bandInfos.map { e -> e.frequency.toInt() },
                                    bandInfos.map { e -> e.gain.toDouble() }
                                    //todo zrz 目前h5不支持对单个band设置最值以及步长
                                )
                            } else listOf(
                                EQItem(
                                    curEqInfo.name,
                                    EQCategoryEnum.CUSTOM_EQ.name,
                                    curEqInfo.bandInfos.map { e -> e.frequency.toInt() },
                                    curEqInfo.bandInfos.map { e -> e.gain.toDouble() }
                                    //todo zrz 目前h5不支持对单个band设置最值以及步长
                                ),
                            )
                        )
                    }
                    callback.invoke("0", null, getEq.toJSONObj())
                }

                else -> throw Exception("wrong channel id")
            }
        }.onFailure {
            callback.invoke("-1", "getBandBoxEq failed $it", null)
        }
    }


    override fun onCleared() {
        super.onCleared()
        device.unregisterDeviceListener(deviceListener)
        DeviceScanner.unregisterObserver(scanObserver)
        AudioPlayerServerBind.getIns().unbindService()//?? Confirm later
    }

    private fun onMic2Reverb(reverb: V5Mic2Reverb) {
        notificationInvoker?.notifyObj(
            Events.NOTIFY_BAND_BOX_MIC_INFO,
            jsState.currentMicInfo.apply { reverbVolumn = reverb.value }.toJSONObj()
        )
    }

    private fun onMic2Echo(echo: V5Mic2Echo) {
        notificationInvoker?.notifyObj(
            Events.NOTIFY_BAND_BOX_MIC_INFO,
            jsState.currentMicInfo.apply { echoVolumn = echo.value }.toJSONObj()
        )
    }

    private fun onMic1Reverb(reverb: V5Mic1Reverb) {
        notificationInvoker?.notifyObj(
            Events.NOTIFY_BAND_BOX_MIC_INFO,
            jsState.currentMicInfo.apply { reverbVolumn = reverb.value }.toJSONObj()
        )
    }

    private fun onMic1Echo(echo: V5Mic1Echo) {
        notificationInvoker?.notifyObj(
            Events.NOTIFY_BAND_BOX_MIC_INFO,
            jsState.currentMicInfo.apply { echoVolumn = echo.value }.toJSONObj()
        )
    }

    private fun onAIStemSeparationMode(mode: V5AIStemSeparationMode) {
        notifyAiStemSeparationIfNeed(jsState.aiStemSeparation.copy(enable = mode.isOn))
    }

    private fun onAIStemSeparationKaraokeMode(mode: V5AIStemSeparationKaraokeMode) {
        notifyAiStemSeparationIfNeed(jsState.aiStemSeparation.copy(karaokeEnable = mode.isOn))
    }

    private fun onAIStemSeparationTrackVolume(volume: V5AIStemSeparationTrackVolume) {
        notifyAiStemSeparationIfNeed(jsState.aiStemSeparation.deepCopy().apply { updateTracksWithTrackVolume(volume) })
    }

    private fun onAIStemSeparationTrackType(type: V5AIStemSeparationTrackType) {
        notifyAiStemSeparationIfNeed(jsState.aiStemSeparation.deepCopy().apply { updateTracksWithTrackType(type) })
    }

    private fun notifyAiStemSeparationIfNeed(newState: JsAIStemSeparation) {
        if (newState != jsState.aiStemSeparation) {
            jsState.aiStemSeparation = newState
            notificationInvoker?.notifyObj(Events.NOTIFY_AI_STEM_SEPARATION, jsState.aiStemSeparation.toJSONObj())
        }
    }

    private fun setBandBoxMicInfo(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val setBean = data.toBean<JsSetBandBoxMicInfo>()!!
            when (setBean.channelId) {
                CI_ID_S_CH1,
                CI_ID_S_BUILT_IN_MIC,
                CI_ID_T_CH1 -> {
                    setBean.echoVolumn?.also {
                        device.asyncSetDevInfoFeat(V5Mic1Echo(it))
                    }
                    setBean.reverbVolumn?.also {
                        device.asyncSetDevInfoFeat(V5Mic1Reverb(it))
                    }
                }

                CI_ID_T_CH2 -> {
                    setBean.echoVolumn?.also {
                        device.asyncSetDevInfoFeat(V5Mic2Echo(it))
                    }
                    setBean.reverbVolumn?.also {
                        device.asyncSetDevInfoFeat(V5Mic2Reverb(it))
                    }
                }

                else -> throw Exception("Unknown id ${setBean.channelId}")
            }
            jsState.currentMicInfo.channelId = setBean.channelId
            callback.invoke("0", null, jsState.currentMicInfo.toJSONObj())
        }.onFailure {
            callback.invoke("-1", "setBandBoxMicInfo failed $it", null)
        }
    }

    private fun getBandBoxMicInfo(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val id = data!!.toBean<JsGetBandBoxMicInfo>()!!.id
            when (id) {
                CI_ID_S_CH1,
                CI_ID_S_BUILT_IN_MIC,
                CI_ID_T_CH1 -> {
                    device.asyncGetDevInfoFeat(V5DevInfoFeatID.Mic1Echo, V5DevInfoFeatID.Mic1Reverb)
                }

                CI_ID_T_CH2 -> {
                    device.asyncGetDevInfoFeat(V5DevInfoFeatID.Mic2Echo, V5DevInfoFeatID.Mic2Reverb)
                }

                else -> throw Exception("Unknown id $id")
            }
            jsState.currentMicInfo.channelId = id
            callback.invoke("0", null, jsState.currentMicInfo.toJSONObj())
        }.onFailure {
            callback.invoke("-1", "getBandBoxMicInfo failed $it", null)
        }
    }

    private fun enterBandBoxModelFunction(activity: CommonHybridActivity, data: JsonObject?, callback: JsCallback) {
        runCatching {
            val enterBean = data!!.toBean<JsBandBoxModelFunction>()!!
            when (enterBean.functionId) {
                JsBandBoxModelFunction.F_ID_GUITAR_PRESET -> {
                    when (enterBean.id) {
                        CI_ID_T_CH1,
                        CI_ID_S_CH1 -> GuitarPresetActivity.launch(activity, device, V5ChannelNumber.CH1)

                        CI_ID_T_CH3 -> GuitarPresetActivity.launch(activity, device, V5ChannelNumber.CH3)
                    }
                }

                JsBandBoxModelFunction.F_ID_MIC_SENS -> {
                    when (enterBean.id) {
                        CI_ID_S_CH1,
                        CI_ID_T_CH1 -> {
                            MicSensitivityActivity.launch(activity, device.UUID!!, MicSensitivityActivity.MIC1)
                        }

                        CI_ID_T_CH2 -> {
                            MicSensitivityActivity.launch(activity, device.UUID!!, MicSensitivityActivity.MIC2)
                        }
                    }
                }

                JsBandBoxModelFunction.F_ID_MUSIC_PITCH -> {
                    ToneShifterDialog(activity, device).show(activity.supportFragmentManager, null)
                }

                JsBandBoxModelFunction.F_ID_PICKUP -> {
                    when (enterBean.id) {
                        CI_ID_T_CH1 -> V5ChannelNumber.CH1
                        CI_ID_T_CH3 -> V5ChannelNumber.CH3
                        else -> null
                    }?.also {
                        eventStream.tryEmit(PBHVMEvent.PickUp(device, it))
                    } ?: run {
                        showDebugToast("it should not be display")
                    }
                }
            }
            callback.invoke("0", null, null)
        }.onFailure {
            callback.invoke("-1", "enterBandBoxModelFunction failed", null)
        }
    }

    private fun setBandBoxModelInfo(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val setBean = data!!.toBean<JsSetBandBoxModelInfo>()
            setBean?.channelVolumn?.also {
                when (it.id) {
                    CI_ID_S_CH1 -> {
                        when (devState.soloMusician?.musician) {
                            SoloMusicianEnum.SINGER -> V5CHInputType.Mic
                            SoloMusicianEnum.GUITARIST,
                            SoloMusicianEnum.GUITAR_VOCALIST -> V5CHInputType.Guitar

                            else -> null
                        }?.also { inputType ->
                            device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.CH1, inputType, it.volumn))
                        } ?: run {
                            showDebugToast("can not find right solo musician: ${devState.soloMusician}")
                        }
                    }

                    CI_ID_S_BUILT_IN_MIC -> {
                        device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.CH1, V5CHInputType.Mic, it.volumn))
                    }

                    CI_ID_S_MUSIC -> {
                        device.asyncSetDevInfoFeat(V5AudioVolume(it.volumn))
                    }

                    CI_ID_T_CH1 -> {
                        device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.CH1, devState.trioCh1Status!!.inputType, it.volumn))
                    }

                    CI_ID_T_CH2 -> {
                        device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.CH2, devState.trioCh2Status!!.inputType, it.volumn))
                    }

                    CI_ID_T_CH3 -> {
                        device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.CH3, V5CHInputType.Guitar, it.volumn))
                    }

                    CI_ID_T_CH4_INSTRUMENT -> {
                        device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.CH4, V5CHInputType.AUX_INSTRUMENT, it.volumn))
                    }

                    CI_ID_T_MUSIC -> {
                        device.asyncSetDevInfoFeat(V5AudioVolume(it.volumn))
                    }
                }
            }
            setBean?.activeModel?.also {
                when (it) {
                    MODEL_SINGER -> {
                        device.asyncSetDevInfoFeat(V5DeviceOOBE(SoloMusicianEnum.SINGER))
                    }

                    MODEL_GUITARIST -> {
                        device.asyncSetDevInfoFeat(V5DeviceOOBE(SoloMusicianEnum.GUITARIST))
                    }

                    MODEL_SINGING -> {
                        device.asyncSetDevInfoFeat(V5DeviceOOBE(SoloMusicianEnum.GUITAR_VOCALIST))
                    }
                }
            }
            setBean?.auxType?.also {
                when (it) {
                    AT_MUSIC -> {
                        device.asyncSetDevInfoFeat(V5AUXType(V5AUXType.Type.Music))
                    }

                    AT_PIANO -> {
                        device.asyncSetDevInfoFeat(V5AUXType(V5AUXType.Type.Instrument))
                    }
                }
            }
            setBean?.masterVolumn?.also {
                device.asyncSetDevInfoFeat(V5ChannelVolume(V5ChannelNumber.Master, V5CHInputType.Unknown, it))
            }
            callback.invoke("0", null, null)
        }.onFailure {
            callback.invoke("-1", "setBandBoxModelInfo failed", null)
        }
    }


    private fun onAuxType(type: V5AUXType) {
        devState.trioAuxType = type
        Logger.d(tag, "called from onAuxType $type")
        jsState.bandBoxModelInfo.auxType = JsBandBoxModelInfo.trioAuxType(status = devState.trioCh4Status, auxType = devState.trioAuxType)
        notifyJsBandBoxModelInfo()
    }

    private fun onChannelInputStatus(status: V5ChannelInputStatus) {
        if (device.isSolo()) {
            when (status.channelNumber) {
                V5ChannelNumber.CH1 -> {
                    devState.soloCh1Status = status
                    updateJsSoloChannelInfos()
                    updateJsSoloChannelDisableType()
                    notifyJsBandBoxModelInfo()
                }

                V5ChannelNumber.CH4 -> {
                    devState.soloCh4Status = status
                    updateJsSoloChannelInfos()
                    updateJsSoloChannelDisableType()
                    notifyJsBandBoxModelInfo()
                }

                else -> return
            }
            return
        }
        //trio
        when (status.channelNumber) {
            V5ChannelNumber.CH1 -> {
                devState.trioCh1Status = status
                Logger.d(tag, "called from onChannelInputStatus $status")
                updateJsTrioChannelInfos()
                notifyJsBandBoxModelInfo()
            }

            V5ChannelNumber.CH2 -> {
                devState.trioCh2Status = status
                Logger.d(tag, "called from onChannelInputStatus $status")
                updateJsTrioChannelInfos()
                notifyJsBandBoxModelInfo()
            }

            V5ChannelNumber.CH3 -> {
                devState.trioCh3Status = status
                Logger.d(tag, "called from onChannelInputStatus $status")
                updateJsTrioChannelInfos()
                notifyJsBandBoxModelInfo()
            }

            V5ChannelNumber.CH4 -> {
                devState.trioCh4Status = status
                Logger.d(tag, "called from onChannelInputStatus $status")
                updateJsTrioChannelInfos()
                jsState.bandBoxModelInfo.auxType = JsBandBoxModelInfo.trioAuxType(status = devState.trioCh4Status, auxType = devState.trioAuxType)
                notifyJsBandBoxModelInfo()
            }

            else -> Unit
        }
    }

    private fun notifyJsBandBoxModelInfo() {
        notificationInvoker?.notifyObj(
            Events.NOTIFY_BAND_BOX_MODEL_INFO,
            jsState.bandBoxModelInfo.toJSONObj()
        )
    }

    private fun updateJsTrioChannelInfos() {
        jsState.bandBoxModelInfo.activeChannels = JsBandBoxModelInfo.trioChannelInfoList(
            devState.trioCh1Status,
            devState.trioCh2Status,
            devState.trioCh3Status,
            devState.trioCh4Status,
            when (devState.trioCh1Status?.inputType) {
                V5CHInputType.Mic -> devState.trioCh1MicVolume
                V5CHInputType.Guitar -> devState.trioCh1GuitarVolume
                else -> null
            },
            when (devState.trioCh2Status?.inputType) {
                V5CHInputType.Mic -> devState.trioCh2MicVolume
                V5CHInputType.LineIn -> devState.trioCh2LineInVolume
                else -> null
            },
            devState.trioCh3Volume,
            devState.trioCh4Volume,
            devState.trioAuxType,
            devState.musicSource,
            devState.musicVolume,
        )
    }

    private fun updateJsSoloChannelInfos() {
        jsState.bandBoxModelInfo.activeChannels = JsBandBoxModelInfo.soloChannelInfoList(
            devState.soloMusician,
            devState.soloCh1Status,
            devState.soloCh1MicVolume,
            devState.soloCh1GuitarVolume,
            devState.soloCh4Status,
            devState.musicSource,
            devState.musicVolume,
        )
    }


    private fun onAudioVolume(volume: V5AudioVolume) {
        devState.musicVolume = volume
        if (device.isSolo()) {
            updateJsSoloChannelInfos()
        } else {
            Logger.d(tag, "called from onAudioVolume $volume")
            updateJsTrioChannelInfos()
        }
        notifyJsBandBoxModelInfo()
    }

    private fun onActiveAudioSource(source: V5ActiveAudioSource) {
        devState.musicSource = source
        if (device.isSolo()) {
            updateJsSoloChannelInfos()
        } else {
            Logger.d(tag, "called from onActiveAudioSource $source")
            updateJsTrioChannelInfos()
        }
        notifyJsBandBoxModelInfo()
        notifyAiStemSeparationIfNeed(
            jsState.aiStemSeparation.copy(
                musicSource = when (source.source) {
                    V5AudioSource.BT -> SRC_BT
                    V5AudioSource.AUX -> SRC_AUX
                    V5AudioSource.USBFlashDisk -> SRC_USB_AUDIO
                    V5AudioSource.UAC -> SRC_USB_DRIVE
                    else -> SRC_BT
                },
            ),
        )
    }

    private fun onChannelVolume(volume: V5ChannelVolume) {
        if (device.isSolo()) {
            when (volume.channelNumber) {
                V5ChannelNumber.CH1 -> {
                    when (volume.inputType) {
                        V5CHInputType.Mic -> devState.soloCh1MicVolume = volume
                        V5CHInputType.Guitar -> devState.soloCh1GuitarVolume = volume
                        else -> return
                    }
                }

                else -> return
            }
            updateJsSoloChannelInfos()
            notifyJsBandBoxModelInfo()
        } else {
            when (volume.channelNumber) {
                V5ChannelNumber.Master -> {
                    devState.trioMasterVolume = volume
                    Logger.d(tag, "called from onChannelVolume $volume")
                    jsState.bandBoxModelInfo.masterVolumn = JsBandBoxModelInfo.trioMasterVolume(volume)
                    notifyJsBandBoxModelInfo()
                }

                V5ChannelNumber.CH1 -> {
                    when (volume.inputType) {
                        V5CHInputType.Mic -> devState.trioCh1MicVolume = volume
                        V5CHInputType.Guitar -> devState.trioCh1GuitarVolume = volume
                        else -> return
                    }
                    if (volume.inputType == devState.trioCh1Status?.inputType) {
                        Logger.d(tag, "called from onChannelVolume $volume")
                        updateJsTrioChannelInfos()
                        notifyJsBandBoxModelInfo()
                    }
                }

                V5ChannelNumber.CH2 -> {
                    when (volume.inputType) {
                        V5CHInputType.Mic -> devState.trioCh2MicVolume = volume
                        V5CHInputType.LineIn -> devState.trioCh2LineInVolume = volume
                        else -> return
                    }
                    if (volume.inputType == devState.trioCh2Status?.inputType) {
                        Logger.d(tag, "called from onChannelVolume $volume")
                        updateJsTrioChannelInfos()
                        notifyJsBandBoxModelInfo()
                    }
                }

                V5ChannelNumber.CH3 -> {
                    devState.trioCh3Volume = volume
                    Logger.d(tag, "called from onChannelVolume $volume")
                    updateJsTrioChannelInfos()
                    notifyJsBandBoxModelInfo()
                }

                V5ChannelNumber.CH4 -> {
                    devState.trioCh4Volume = volume
                    Logger.d(tag, "called from onChannelVolume $volume")
                    updateJsTrioChannelInfos()
                    notifyJsBandBoxModelInfo()
                }
            }
        }
    }

    private fun onSoloMusicianChanged(data: V5DeviceOOBE) {
        devState.soloMusician = data
        jsState.bandBoxModelInfo.activeModel = JsBandBoxModelInfo.soloMusician2ActiveModel(data)
        jsState.bandBoxModelInfo.supportOnProductPreset = when (data.musician) {
            SoloMusicianEnum.GUITARIST,
            SoloMusicianEnum.GUITAR_VOCALIST -> true

            else -> false
        }
        updateJsSoloChannelDisableType()
        updateJsSoloChannelInfos()
        notifyJsBandBoxModelInfo()
    }

    private suspend fun getBandBoxModelInfo(callback: JsCallback) {
        callback.invoke("0", null, jsState.bandBoxModelInfo.toJSONObj())
        if (device.isSolo()) {
            device.getDevInfoFeat<V5DeviceOOBE>() ?: run {
                showDebugToast("oobe data error")
                return
            }
            device.asyncGetDevInfoFeat(
                V5DevInfoFeatID.ActiveAudioSource,
                V5DevInfoFeatID.AudioVolume,
                V5DevInfoFeatID.PitchChange,
            )
            device.asyncSetDevInfoFeat(
                V5ChannelVolumeQuery(V5ChannelNumber.CH1, V5CHInputType.Guitar),
                V5ChannelVolumeQuery(V5ChannelNumber.CH1, V5CHInputType.Mic),
                V5ChannelInputStatusQuery(V5ChannelNumber.CH1),
                V5ChannelInputStatusQuery(V5ChannelNumber.CH4),
            )
        } else {
            device.asyncGetDevInfoFeat(
                V5DevInfoFeatID.ActiveAudioSource,
                V5DevInfoFeatID.AudioVolume,
                V5DevInfoFeatID.AUXType,
                V5DevInfoFeatID.PitchChange,
            )
            device.asyncSetDevInfoFeat(
                V5ChannelVolumeQuery(V5ChannelNumber.Master, V5CHInputType.Unknown),
                V5ChannelVolumeQuery(V5ChannelNumber.CH1, V5CHInputType.Guitar),
                V5ChannelVolumeQuery(V5ChannelNumber.CH1, V5CHInputType.Mic),
                V5ChannelVolumeQuery(V5ChannelNumber.CH2, V5CHInputType.LineIn),
                V5ChannelVolumeQuery(V5ChannelNumber.CH2, V5CHInputType.Mic),
                V5ChannelVolumeQuery(V5ChannelNumber.CH3, V5CHInputType.Guitar),
                V5ChannelVolumeQuery(V5ChannelNumber.CH4, V5CHInputType.AUX_INSTRUMENT),
                V5ChannelInputStatusQuery(V5ChannelNumber.CH1),
                V5ChannelInputStatusQuery(V5ChannelNumber.CH2),
                V5ChannelInputStatusQuery(V5ChannelNumber.CH3),
                V5ChannelInputStatusQuery(V5ChannelNumber.CH4),
            )
        }
    }

    private fun setAiStemSeparation(data: JsonObject?, callback: JsCallback) {
        runCatching {
            val setBean = GsonUtil.parseJsonToBean(data.toString(), JsAISetStemSeparation::class.java)!!
            setBean.enable?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationMode(it))
            }
            setBean.karaokeEnable?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationKaraokeMode(it))
            }
            setBean.toV5TrackType(setBean.track1Type)?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationTrackType(V5TrackNumber.Track1, it))
            }
            setBean.toV5TrackType(setBean.track2Type)?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationTrackType(V5TrackNumber.Track2, it))
            }
            setBean.track1Value?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationTrackVolume(V5TrackNumber.Track1, it))
            }
            setBean.track2Value?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationTrackVolume(V5TrackNumber.Track2, it))
            }
            setBean.track3Value?.also {
                device.asyncSetDevInfoFeat(V5AIStemSeparationTrackVolume(V5TrackNumber.Track3, it))
            }
            callback.invoke("0", null, null)
        }.onFailure {
            callback.invoke("-1", it.stackTraceToString(), null)
        }
    }

    private fun getAiStemSeparation(callback: JsCallback) {
        callback.invoke("0", null, jsState.aiStemSeparation.toJSONObj())
        device.asyncGetDevInfoFeat(
            V5DevInfoFeatID.AIStemSeparationMode,
            V5DevInfoFeatID.AIStemSeparationKaraokeMode,
            V5DevInfoFeatID.PartyGroupType
        )
        device.asyncSetDevInfoFeat(
            V5AIStemSeparationTrackTypeQuery(V5TrackNumber.Track1),
            V5AIStemSeparationTrackTypeQuery(V5TrackNumber.Track2),
            V5AIStemSeparationTrackVolumeQuery(V5TrackNumber.Track1),
            V5AIStemSeparationTrackVolumeQuery(V5TrackNumber.Track2),
            V5AIStemSeparationTrackVolumeQuery(V5TrackNumber.Track3),
        )
    }

    private fun onEnterProductInfo(activity: CommonHybridActivity, callback: JsCallback) {
        ProductInfoActivity.launchProductInfoPage(activity, device)
        callback.invoke("0", null, null)
    }

    private fun onEnterProductSetting(activity: CommonHybridActivity, callback: JsCallback) {
        BaseProductSettingsActivity.portal(activity, device, DeviceControlHybridActivity.REQUEST_CODE)
        callback.invoke("0", null, null)
    }


    private fun onBandBoxFunction(activity: CommonHybridActivity, data: JsonObject?, callback: JsCallback) {
        runCatching {
            when (GsonUtil.parseJsonToBean(data.toString(), JsBandBoxFunction::class.java)!!.functionId) {
                JsBandBoxFunction.ID_DRUM -> {
                    DrumMetronomeActivity.launch(activity, DrumMetronomeActivity.TYPE_DRUM, device.UUID!!)
                }

                JsBandBoxFunction.ID_METRONOME -> {
                    DrumMetronomeActivity.launch(activity, DrumMetronomeActivity.TYPE_METRONOME, device.UUID!!)
                }

                JsBandBoxFunction.ID_TUNER -> {
                    TunerActivity.launch(activity, device.UUID!!)
                }

                JsBandBoxFunction.ID_LOOPER -> {
                    PartyBandGuitarLooperActivity.launch(activity, device.UUID!!)
                }

                JsBandBoxFunction.ID_OUTPUT -> {
                    PartBandUsbOutputActivity.launch(activity, device.UUID!!)
                }

                JsBandBoxFunction.ID_SCALE_PRACTICE -> {
                    showToast("developing...")
                }

                JsBandBoxFunction.ID_TONE -> {
                    ToneShifterDialog(activity, device).show(activity.supportFragmentManager, null)
                }

                JsBandBoxFunction.ID_ON_PRODUCT_PRESSET -> {
                    activity.push<OnProductPresetActivity>(bundleOf("uuid" to device.UUID))
                }

                else -> throw Exception("do not support function")
            }
        }.onFailure {
            callback.invoke("-1", it.stackTraceToString(), null)
        }
    }

    private fun enterOta(activity: CommonHybridActivity, callback: JsCallback) {
        viewModelScope.launch {
            PartyOtaChecker.hasUpdateWithTimeout(
                PartyOtaCheckerParams(device.pid!!, device.firmwareVersion),
                100
            )?.also {
                PartyBandOtaActivity.launch(activity, device.UUID!!, it)
                callback.invoke("0", null, null)
            } ?: run {
                callback.invoke("-1", "there is no newest ota", null)
            }
        }
    }


    private fun getDeviceInfo() {
        viewModelScope.launch {
            device.asyncGetDevInfoFeat(V5DevInfoFeatID.LeftSerialNumber, V5DevInfoFeatID.LeftDeviceBatteryStatus)
            notifyDeviceInfo(jsState.devInfo)
        }
    }

    private suspend fun getOtaStatus(callback: JsCallback) {
        callback.invoke("0", null, OTAStatus(false).toJSONObj())
        fetchOtaStatus()
    }

    suspend fun fetchOtaStatus() {
        val checkRet = PartyOtaChecker.hasUpdate(
            PartyOtaCheckerParams(
                device.pid!!,
                device.firmwareVersion,
            )
        )
        notificationInvoker?.notifyObj(Events.NOTIFY_OTA_STATUS, OTAStatus(null != checkRet).toJSONObj())
    }

    private fun updateJsSoloChannelDisableType() {
        jsState.bandBoxModelInfo.channelDisableType = when (devState.soloMusician?.musician) {
            SoloMusicianEnum.GUITAR_VOCALIST,
            SoloMusicianEnum.SINGER -> if (devState.soloCh4Status?.isDetected == false) CDT_35MM_UNPLUGIN else null

            else -> null
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)

        reportV5Analytics(owner = owner)
    }

    private fun reportV5Analytics(owner: LifecycleOwner) {
        if (!OneCloudReporter.isUploadAvailable(device = device)) {
            return
        }

        val context = owner.context()

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val data = if (device.isGattConnected || device.syncGattConnectWithTimeout(context)) {
                withContext(DISPATCHER_API) {
                    device.getGetDeviceAnalyticsData()
                }
            } else {
                Logger.w(TAG, "reportV5Analytics() >>> fail to generate ble connection with [${device.UUID}]")
                null
            }

            Logger.d(TAG, "reportV5Analytics() >>> data:\n$data")
            data ?: return@launch // don't trigger report if fail to get analytics data

            val reportJObj = OneCloudAnalyticsTemp.formatV5AnalyticsReport(device = device, values = data)
            val reportJStr = try {
                reportJObj.toString()
            } catch (e: Exception) {
                Logger.e(TAG, "reportV5Analytics() >>> exception while converting report json obj to string")
                return@launch
            }

            val result = withContext(DISPATCHER_API) {
                OneCloudReporter.upload(reportJStr = reportJStr)
            }

            Logger.d(TAG, "reportV5Analytics() >>> report result[$result]")
            if (result) {
                OneCloudReporter.markUpload(device = device)
                device.asyncCleanDeviceAnalyticsData()

                if (BuildConfig.DEBUG) {
                    CoroutineScope(DISPATCHER_IO).launch {
                        OneCloudReportLocalCache.saveDeviceReport(context = context, device = device, jsStr = reportJStr)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "PartyBandHybridViewModel"
    }
}

private data class BandBoxJsState(
    var aiStemSeparation: JsAIStemSeparation,
    val bandBoxModelInfo: JsBandBoxModelInfo,
    val currentMicInfo: JsBandBoxMicInfo,
    val devInfo: DeviceInfo,
)

private data class BandBoxDevState(
    var musicVolume: V5AudioVolume? = null,
    var musicSource: V5ActiveAudioSource? = null,
    var soloMusician: V5DeviceOOBE? = null,
    var soloCh1Status: V5ChannelInputStatus? = null,
    var soloCh4Status: V5ChannelInputStatus? = null,
    var soloCh1MicVolume: V5ChannelVolume? = null,
    var soloCh1GuitarVolume: V5ChannelVolume? = null,
    var trioMasterVolume: V5ChannelVolume? = null,
    var trioCh1Status: V5ChannelInputStatus? = null,
    var trioCh2Status: V5ChannelInputStatus? = null,
    var trioCh3Status: V5ChannelInputStatus? = null,
    var trioCh4Status: V5ChannelInputStatus? = null,
    var trioCh1GuitarVolume: V5ChannelVolume? = null,
    var trioCh1MicVolume: V5ChannelVolume? = null,
    var trioCh2LineInVolume: V5ChannelVolume? = null,
    var trioCh2MicVolume: V5ChannelVolume? = null,
    var trioCh3Volume: V5ChannelVolume? = null,
    var trioCh4Volume: V5ChannelVolume? = null,
    var trioAuxType: V5AUXType? = null,
    var trioMasterEq: V5EqInfo? = null,
)

sealed class PBHVMEvent {
    data class PickUp(val device: PartyBandDevice, val chNum: V5ChannelNumber) : PBHVMEvent()
}