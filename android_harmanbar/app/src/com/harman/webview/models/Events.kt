package com.harman.webview.models

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/5/23.
 *
 * https://bitbucket2.harman.com/projects/HMSZAP/repos/jblhybrid_resource/browse/OnePlatform/productcontrol/readme.md
 */
object Events {

    const val PLAY_FUNCTION = "playFunction"

    object PlayFunctionEvents {
        const val PLAY_OR_PAUSE = "playOrPause"

        const val PREVIOUS = "previous"

        const val NEXT = "next"
    }

    const val VOLUME_CHANGE = "volumeChange"

    const val GET_EQ = "getEQ"
    const val GET_COULSON_STATUS = "getCoulsonStatus"
    const val ENTER_COULSON = "enterCoulson"

    const val SET_EQ = "setEQ"

    const val GET_PLAY_INFO = "getPlayInfo"

    const val GET_OTA_STATUS = "getOTAStatus"

    const val ENTER_OTA = "enterOTA"

    const val ENTER_TWS_STEREO = "enterTWSStereo"

    const val ENTER_TWS_STEREO_PREVIOUS = "enterTWSStereoPrevious"

    const val ENTER_PRODUCT_INFO = "enterProductInfo"

    const val UNGROUP = "ungroup"

    const val GROUP_RENAME = "groupRename"

    const val GROUP_CHANNEL_CHANGE = "groupChannelChange"

    const val EFFECT_LAB = "effectLab"

    const val GET_DEVICE_INFO = "getDeviceInfo"

    const val SET_LIGHT_CONTROL = "setLightControl"

    const val GET_LIGHT_INFO = "getLightInfo"

    const val START_ON_BOARDING = "startOnboarding"

    const val GET_NEARBY_PRODUCTS = "getNearbyProducts"

    const val GET_SUPPORT_PRODUCTS = "getSupportProducts"

    const val START_DISCOVER_PRODUCT = "startDiscoverProduct"

    const val ENTER_MUSIC_SERVICE = "enterMusicService"

    const val GET_MUSIC_SERVICE = "getMusicService"

    const val ENTER_MOMENT = "enterMoment"

    const val GET_PARTY_LIGHT_FEATURE = "getPartyLightFeature"
    const val SET_PARTY_LIGHT_FEATURE = "setPartyLightFeature"
    const val NOTIFY_LIGHT_INFO = "lightInfo"
    const val NOTIFY_DEVICE_INFO = "deviceInfo"
    const val NOTIFY_PARTY_LIGHT_FEATURE = "partyLightFeature"
    const val ENTER_COLOR_PICKER = "enterColorPicker"
    const val ENTER_PRODUCT_SETTINGS = "enterProductSettings"
    const val ENTER_HORIZON3_FEATURE = "enterHorizon3Feature"
    const val SET_HORIZON3_FEATURE = "setHorizon3Feature"
    const val GET_HORIZON3_FEATURE = "getHorizon3Feature"
    const val GET_SLEEP_MODE = "getSleepMode"
    const val SET_SLEEP_MODE = "setSleepMode"

    object Horizon3FeatureEvents {
        const val BEDTIME = "bedtime"
        const val SCREENSAVER = "screenSaver"
        const val ALARM = "alarm"
        const val RADIO = "radio"
    }

    const val ENABLE = "enable"
    const val TEMPERATURE = "temperature"
    const val GET_PRODUCT_IMAGE = "getProductImage"
    const val BRIGHTNESS = "brightness"
    const val SOUNDTIMER = "soundTimer"
    const val SOUNDSOURCE = "soundSource"

    const val ENTER_CALIBRATION = "enterCalibration"
    const val ENTER_PARTY_STAGE = "enterPartyStage"

    const val VIBRATION = "vibration"

    const val ENTER_REMOTE_CONTROL = "enterRemoteControl"

    const val ENTER_RENAME = "enterRename"

    const val ENTER_HOME_THEATRE = "enterHomeTheatre"

    const val ENTER_CREATE_A_SYSTEM = "enterCreateASystem"

    const val ENTER_WIFI_STREAMING = "enterWiFiStreaming"
    const val ENTER_MIC_EFFECTS = "enterMicEffects"
    const val ENTER_TOP_PANNEL_SETTINGS = "enterTopPannelSettings"
    const val ENTER_MULTI_ROOM = "enterMultiRoom"

    const val ENTER_SETUP_NETWORK = "enterSetupNetwork"

    const val GET_NETWORK_INFO = "getNetworkInfo"

    const val REMOVE_OFFLINE_PRODUCT = "removeOfflineProduct"

    const val SETUP_PRODUCT = "setupProduct"
    const val ENTER_SOUND_TUNING = "enterSoundTuning"

    const val GET_BASSBOOST_INFO = "getBassboostInfo"
    const val SET_BASSBOOST_INFO = "setBassboostInfo"

    const val SETUP_WIFI = "setupWiFi"

    const val GET_MOMENT_STATUS = "getMomentStatus"

    const val ENTER_QSG = "enterQSG"

    const val ENTER_PRODUCT_SUPPORT = "enterProductSupport"
    const val GET_SOUNDBAR_FEATURE = "getSoundbarFeature"
    const val SET_SOUNDBAR_FEATURE = "setSoundbarFeature"
    const val NOTIFY_SOUNDBAR_FEATURE = "soundbarFeature"
    const val GET_BROADCASTING_INFO = "getBroadcastingInfo"
    const val SET_BROADCASTING_INFO = "setBroadcastingInfo"
    const val NOTIFY_BROADCASTING_INFO = "broadcastingInfo"

    const val NOTIFY_BASSBOOST_INFO = "bassboostInfo"

    const val ENTER_PERSONAL_LISTENING_MODE = "enterPersonalListeningMode"


    const val ENABLE_GOOGLE_CAST = "enableGoogleCast"

    const val GET_ROON_READY_STATUS = "getRoonReadyStatus"

    const val CLICK_ROON_READY_CARD = "clickRoonReadyCard"

    const val COPY_COUPON_CODE = "copyCouponCode"

    const val TO_ROON_READY_WEB = "redeemToRoonWebsite"

    const val SHOW_ROON_READY_LATER_TIPS = "showRoonReadyLaterTips"

    const val SHOW_WHATS_NEW_DIALOG = "showWhatsNewDialog"

    const val DISMISS_WHATS_NEW_DIALOG = "dismissWhatsNewDialog"

    const val SWITCH_FEEDBACK_TONE = "switchFeedbackTone"

    const val RESTORE_FACTORY_SETTINGS = "restoreFactorySettings"

    const val SHOW_PRIMARY_GREEN_LED = "showPrimaryGreenLED"

    const val HIDE_PRIMARY_GREEN_LED = "hidePrimaryGreenLED"

    const val GET_WHATS_NEW_STATUS = "getWhatsNewStatus"

    const val CONNECT_TO_SOUNDBAR = "connectToSoundbar"

    const val ENTER_FAQ = "enterFAQ"

    const val BACK_TO_DASHBOARD = "backToDashboard"

    const val REDIRECT_APP = "openApp"

    const val ENTER_FULL_PLAYER = "enterFullPlayer"

    const val GET_HK_STUDIO_LIGHTINFO = "getHKStudioLightInfo"

    const val SET_HK_STUDIO_LIGHTINFO = "setHKStudioLightInfo"
    const val BAND_BOX_FUNCTION = "bandBoxFunction"
    const val GET_AI_STEM_SEPARATION = "getAIStemSeparation"
    const val NOTIFY_AI_STEM_SEPARATION = "AIStemSeparation"
    const val SET_AI_STEM_SEPARATION = "setAIStemSeparation"
    const val GET_BAND_BOX_MODEL_INFO = "getBandBoxModelInfo"
    const val NOTIFY_BAND_BOX_MODEL_INFO = "bandBoxModelInfo"
    const val SET_BAND_BOX_MODEL_INFO = "setBandBoxModelInfo"
    const val ENTER_BAND_BOX_MODEL_FUNCTION = "enterBandBoxModelFunction"
    const val GET_BAND_BOX_MIC_INFO = "getBandBoxMicInfo"
    const val NOTIFY_BAND_BOX_MIC_INFO = "bandBoxMicInfo"
    const val SET_BAND_BOX_MIC_INFO = "setBandBoxMicInfo"
    const val NOTIFY_OTA_STATUS = "OTAStatus"
    const val GET_BAND_BOX_EQ = "getBandBoxEQ"
    const val NOTIFY_BAND_BOX_EQ = "bandBoxEQ"
    const val SET_BAND_BOX_EQ = "setBandBoxEQ"

    const val EVENT_ACTION = "eventAction"
}