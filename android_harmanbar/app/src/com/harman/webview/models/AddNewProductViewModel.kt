package com.harman.webview.models

import android.app.Activity
import androidx.annotation.AnyThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import com.harman.LifeScanViewModel
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.session.OneBusiness
import com.harman.discover.DeviceScanner
import com.harman.discover.DeviceStore
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.WiFiScanner
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools.bleConnectable
import com.harman.discover.util.Tools.overThreshold
import com.harman.hadAuthed
import com.harman.log.Logger
import com.harman.product.setting.AppSettingsViewModel
import com.harman.product.setting.AppSettingsViewModel.Companion
import com.harman.supportBLEAuth
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.webview.BeanMap.toNearbyProduct
import com.harman.webview.CommonHybridActivity
import com.harman.webview.DiscoverModel
import com.harman.webview.IHybridNotification
import com.harman.webview.IViewModelEvents
import com.harman.webview.NearbyProductList
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.LinkPlayLauncher
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.utils.AppPermissionUtils.permissionsBTGpsEnable
import com.wifiaudio.utils.CountryCodeUtil
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.lang.ref.WeakReference

class AddNewProductViewModelFactory(
    private val activity: Activity
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return AddNewProductViewModel(weakActivity = WeakReference(activity)) as T
    }
}

class AddNewProductViewModel(
    private val weakActivity: WeakReference<Activity>,
) : LifeScanViewModel(), IHybridViewModel<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem,
        OneDevice
        > {

    override var notificationInvoker: IHybridNotification? = null

    override var gattConnectStatusListener: GattStatusListener? = null

    override var brEdrConnectStatusListener: BrEdrStatusListener? = null

    override var eventListener: IViewModelEvents? = null

    override val device: OneDevice? = null

    private val _discoveredDevice = MutableLiveData<Device?>()
    val discoveredDevice: LiveData<Device?>
        get() = _discoveredDevice

    var nearbyList: NearbyProductList? = null

    override suspend fun onCustomEvent(
        activity: CommonHybridActivity,
        eventName: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        super.onCustomEvent(activity, eventName, data, callback)
    }

    override fun onResume(owner: LifecycleOwner) {
        super<LifeScanViewModel>.onResume(owner)
        super<IHybridViewModel>.onResume(owner)

        DeviceScanner.registerObserver(observer = hmDeviceObserver)
    }

    override fun onPause(owner: LifecycleOwner) {
        DeviceScanner.unregisterObserver(observer = hmDeviceObserver)
    }

    private var discoveringModel: DiscoverModel? = null
    private var startTime: Long = 0L

    fun searchDevice(model: DiscoverModel) {
        discoveringModel = model
        startTime = System.currentTimeMillis()
    }

    private val hmDeviceObserver = object : IHmDeviceObserver {
        @AnyThread
        override fun onDeviceOnlineOrUpdate(device: Device) {
        }

        @AnyThread
        override fun onDeviceOffline(device: Device) {
            // no impl
        }

        private var lastCallTs: Long = -1L

        @AnyThread
        override fun onDevicesUpdate(devices: List<Device>) {
            if (!lastCallTs.overThreshold(
                    thresholdMills = WiFiScanner.NOTIFY_NEARBY_PRODUCT_INTERVAL_MILLS,
                    setter = { lastCallTs = it }
                )
            ) {
                return
            }

            val nearbyDevices = devices.filter { isNearByDevice(it) }
            val nearbyProducts = nearbyDevices.map { it.toNearbyProduct() }

            if (nearbyProducts.isEmpty()) {
                return
            }

            nearbyList = NearbyProductList(nearbyProducts)

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                notifyNearbyProduct(info = nearbyList)
            }

            nearbyDevices.forEach { device ->
                if (true == discoveringModel?.pid?.isNotBlank() && device.pid == discoveringModel?.pid &&
                    (startTime + (discoveringModel?.timeout ?: 30000) > System.currentTimeMillis())
                ) {

                    viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                        discoveringModel = null
                        notifyDiscoveredProduct(DiscoverModel(pid = device.pid, discovered = true))
                        device.UUID?.let { uuid -> _discoveredDevice.value = DeviceStore.findOne(uuid) }
                    }
                    return
                }
            }
        }

        @AnyThread
        override fun onBluetoothServiceFailed(errorCode: Int) {
            // no impl
        }
    }

    /**
     * Check if device is Un-Authenticated device.
     * @return true, is Un-Authenticated device, otherwise false.
     */
    private fun isNearByDevice(device: Device): Boolean = when (device) {
        is OneDevice -> AppConfigurationUtils.isSupportedDevice(device.pid) &&
                !device.isWiFiOnline &&
                // no need to display device which already join in any WiFi AP.
                !device.isNetworkConnected &&
                !device.isA2DPConnected &&
                !device.hadAuthed() &&
                device.bleConnectable()

        is PartyBoxDevice,
        is PartyBandDevice -> AppConfigurationUtils.isSupportedDevice(device.pid) &&
                device.supportBLEAuth() &&
                !device.hadAuthed() &&
                device.bleConnectable()

        else -> false
    }

    override val tag: String = TAG

    override val lifeScanVMLogTag: String = TAG

    companion object {
        private const val TAG = "AddNewProductViewModel"
    }
}