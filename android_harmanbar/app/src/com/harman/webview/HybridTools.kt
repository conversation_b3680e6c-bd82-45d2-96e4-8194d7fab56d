package com.harman.webview

import android.app.Activity
import android.text.TextUtils
import com.blankj.utilcode.util.ConvertUtils
import com.google.gson.JsonObject
import com.harman.bar.app.BuildConfig
import com.harman.isHKOneApp
import com.harman.log.Logger
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.action.lan.LocaleLan.Companion.LAN_EN
import com.wifiaudio.action.lan.LocaleLanConfigUtil.getLocaleLanForWeb
import com.wifiaudio.app.debug.DebugConfigKey
import com.wifiaudio.utils.statusbar.SystemBarTintManager
import org.json.JSONArray
import org.json.JSONObject
import java.lang.Integer.max
import kotlin.random.Random
import kotlin.random.nextUInt

/**
 * Created by gerrard<PERSON><PERSON> on 2024/4/2.
 */
object HybridTools {

    private const val DEV = "dev"
    private const val PRODUCTION = "prd"

    private const val DASHBOARD_HTML_LOCAL_PATH = "file:///android_asset/one_platform/%s/index.html"

    private const val UNION_PARAMS_FORMAT = "lang=%s&statusBarHeight=%s&theme=%s&brand=%s"

    private const val DASHBOARD_PATH_FORMAT = "%s?$UNION_PARAMS_FORMAT&env=%s"

    private const val PAIRING_PATH_FORMAT = "%s?pId=%s&$UNION_PARAMS_FORMAT"

    /**
     * @param pid switch to upper case.
     * @return null if [pid] didn't support hybrid page currently.
     */
    fun getDashboardUrl(activity: Activity, pid: String?): String? {
        if (pid.isNullOrBlank()) {
            Logger.w(TAG, "getDashboardUrl() >>> pid is null")
            return null
        }

        val prefix = if (DebugConfigKey.useRemoteDashboardUrl) {
            DebugConfigKey.remoteDashboardUrl?.formatAsUrl()
        } else {
            mapHybridDeviceControlUrl(pid = pid)
        }
        if (null == prefix) {
            return null
        }

        val url = DASHBOARD_PATH_FORMAT.format(
            prefix,
            *getUnionParams()
        )
        Logger.d(TAG, "getDashboardPath() >>> url:$url")
        return url
    }

    /**
     * Get "Add new product" HTML url.
     */
    fun getNewProductUrl(activity: Activity): String? {
        val localHybridUrl = if (DebugConfigKey.useRemoteDashboardUrl) {
            DebugConfigKey.remoteDashboardUrl?.formatAsUrl()
        } else {
            AppConfigurationUtils.getLocalAddProductHybridFilesUrl()
        }

        if (localHybridUrl.isNullOrBlank()) {
            Logger.w(TAG, "getNewProductUrl() >>> localHybridUrl is empty")
            return null
        }

        val url = DASHBOARD_PATH_FORMAT.format(
            localHybridUrl,
            *getUnionParams()
        )
        Logger.d(TAG, "getNewProductUrl() >>> $url")
        return url
    }

    private fun getUnionParams(): Array<String> {
        val lan = getLocaleLanForWeb().let { if (TextUtils.isEmpty(it)) LAN_EN else it }

        return arrayOf(
            lan,
            "0",
            if (isHKOneApp()) "light" else "dark",
            if (isHKOneApp()) "hk" else "jbl",
            if (BuildConfig.DEBUG) DEV else PRODUCTION
        )
    }

    /**
     * Get "Add new product" HTML url.
     */
    fun getSetupProductUrl(activity: Activity, pid: String): String {
        val localHybridUrl = if (DebugConfigKey.useRemoteDashboardUrl) {
            DebugConfigKey.remoteDashboardUrl?.formatAsUrl()
        } else {
            AppConfigurationUtils.getLocalSetupProductHybridFilesUrl()
        }

        val lan = getLocaleLanForWeb().let { if (TextUtils.isEmpty(it)) LAN_EN else it }

        val url = PAIRING_PATH_FORMAT.format(
            localHybridUrl,
            pid,
            *getUnionParams()
        )

        Logger.d(TAG, "getDashboardPath() >>> lan[$lan] $url")
        return url
    }

    /**
     * Get "Whats New" HTML url.
     */
    fun getWhatIsNewUrl(activity: Activity): String? {
        val localHybridUrl = if (DebugConfigKey.useRemoteDashboardUrl) {
            DebugConfigKey.remoteDashboardUrl?.formatAsUrl() + "whats_new_index.html"
        } else {
            AppConfigurationUtils.getLocalWhatIsNewHybridFilesUrl()
        }

        val url = DASHBOARD_PATH_FORMAT.format(
            localHybridUrl,
            *getUnionParams()
        )

        return url
    }

    /**
     * Get union offline device page on One platform.
     */
    fun getDeviceAbnormalUrl(activity: Activity): String? {
        val prefix = if (DebugConfigKey.useRemoteDashboardUrl) {
            DebugConfigKey.remoteDashboardUrl?.formatAsUrl()
        } else {
            AppConfigurationUtils.getOneDeviceAbnormalUrl()
        }

        val lan = getLocaleLanForWeb().let { if (TextUtils.isEmpty(it)) LAN_EN else it }

        val url = DASHBOARD_PATH_FORMAT.format(
            prefix,
            *getUnionParams()
        )
        Logger.d(TAG, "getOneDeviceAbnormalUrl() >>> lan[$lan] $url")

        return url
    }

    private fun mapHybridDeviceControlUrl(pid: String): String? {
        if (DebugConfigKey.useRemoteDashboardUrl) {
            return DebugConfigKey.remoteDashboardUrl?.formatAsUrl()
        }

        return AppConfigurationUtils.getLocalDeviceControlHybridFilesUrl(pid)
    }

    // http://192.168.1.129:8080/
    private fun String?.formatAsUrl(): String? {
        val raw = this
        if (raw.isNullOrBlank()) {
            return null
        }

        var tmp = raw
        if (!tmp.startsWith("http://")) {
            tmp = "http://$tmp"
        }

        if (!tmp.endsWith("/")) {
            tmp = "$tmp/"
        }

        return tmp
    }

    private fun getStatusBarHeight(activity: Activity): Int {
        val mTintManager = SystemBarTintManager(activity)
        val config = mTintManager.config
        return ConvertUtils.px2dp(config.statusBarHeight.toFloat())
    }

    /**
     * Format the request body to JS like:
     *
     * HMJSBridge._handleMessageFromNative("
     * {
     *   \"data\": {
     *     \"data\": {
     *       \"artisName\": \"323\",
     *       \"songName\": \"123\",
     *       \"volume\": 12
     *     },
     *     \"eventName\": \"playInfo\"
     *   },
     *   \"handlerName\": \"notification\",
     *   \"callbackID\": \"native_iOS_cb_1\"
     * }
     * ")
     */
    fun formatRequest(
        data: JsonObject,
        handlerName: String,
        callbackID: String
    ): String {
        val jObj = JSONObject()
        jObj.put("data", data)
        jObj.put("handlerName", handlerName)
        jObj.put("callbackID", callbackID)

        return formatBridgeCall(jObj)
    }

    /**
     * Format the callback body to JS like:
     *
     * HMJSBridge._handleMessageFromNative("
     * "{
     * \"responseData\":
     * {
     * \"msg\":\"get eq success cc \",
     * \"code\":\"0\",
     * \"data\":{\"support_preset\":false,\"custom_eq_payload\":{\"gain\":[0,0,0],\"q\":[0.70709997415542603,1,0.30000001192092896],\"type\":[17,11,16],\"fs\":[150,1000,5000]}}
     * },
     * \"responseID\":\"cb_1_1712743403203\"}"
     * ")
     */
    fun formatCallback(
        code: String,
        msg: String?,
        callbackID: String?,
        data: JSONObject?
    ): String {
        val responseObj = JSONObject()
        responseObj.put("code", code)
        responseObj.put("msg", msg ?: "")
        responseObj.put("data", data)

        val jObj = JSONObject()
        jObj.put("responseID", callbackID)
        jObj.put("responseData", responseObj)

        return formatBridgeCall(jObj)
    }

    /**
     * Format the request body to JS like:
     *
     * HMJSBridge._handleMessageFromNative("
     * {
     * \"data\":{\"data\":{\"artisName\":\"323\",\"songName\":\"123\",\"volume\":12},\"eventName\":\"playInfo\"},
     * \"handlerName\":\"notification\",
     * \"callbackID\":\"native_iOS_cb_1\"
     * }
     * ")
     */
    fun formatRequest(
        callbackID: String?,
        handleName: String?,
        data: JSONObject?
    ): String {
        val jObj = JSONObject()
        jObj.put("callbackID", callbackID ?: "")
        jObj.put("handlerName", handleName ?: "")
        jObj.put("data", data)
        jObj.put("callbackID", geneCallbackID())

        return formatBridgeCall(jObj)
    }

    /**
     * Format the notification body to JS like:
     *
     * HMJSBridge._handleMessageFromNative("
     * {
     * \"handlerName\": \"notification\"
     * \"data\":
     * {
     *      \"eventName\":\"playInfo\",
     *      \"params\":{\"artisName\":\"323\",\"songName\":\"123\",\"volume\":12},\"eventName\":\"playInfo\"},
     *      \"callbackID\":{}
     * }
     * }
     * ")
     */
    fun formatNotification(
        eventName: String?,
        params: JSONObject?
    ): String {
        val jData = JSONObject()
        jData.put("eventName", eventName ?: "")
        jData.put("params", params)

        val jObj = JSONObject()
        jObj.put("handlerName", "notification")
        jObj.put("data", jData)
        jObj.put("callbackID", geneCallbackID())

        return formatBridgeCall(jObj)
    }

    fun formatNotification(
        eventName: String?,
        params: JSONArray?
    ): String {
        val jData = JSONObject()
        jData.put("eventName", eventName ?: "")
        jData.put("params", params)

        val jObj = JSONObject()
        jObj.put("handlerName", "notification")
        jObj.put("data", jData)
        jObj.put("callbackID", geneCallbackID())

        return formatBridgeCall(jObj)
    }

    private fun formatBridgeCall(body: JSONObject): String =
        "HMJSBridge._handleMessageFromNative(\"${body.toString().doubleEscapeString()}\")"

    fun String.removeEscapeCharacter(): String {
        return this.replace("\\\\", "\\")
            .replace("\\\"", "\"")
            .replace("\\\'", "\'")
            .replace("\\n", "\n")
            .replace("\\r", "\r")
            .replace("\\u000c", "\u000c")
            .replace("\\u2028", "\u2028")
            .replace("\\u2029", "\u2029")
    }

    private fun geneCallbackID(): String {
        return "native_Android_cb_${Random(seed = 1000).nextUInt(1u, 999u)}"
    }

    fun String.doubleEscapeString(): String {
        return this.replace("\\", "\\\\")
            .replace("\"", "\\\"")
            .replace("\'", "\\\'")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\u000c", "\\u000c")
    }

    private const val TAG = "HybridTools"
}
