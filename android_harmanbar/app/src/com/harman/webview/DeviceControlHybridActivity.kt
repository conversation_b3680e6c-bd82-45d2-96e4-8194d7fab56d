package com.harman.webview

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResult
import androidx.annotation.MainThread
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.gson.JsonObject
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.calibration.CalibrationBaseActivity
import com.harman.calibration.CalibrationGuideDialog
import com.harman.calibration.IAudioCalibrationDialogEvent
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.PartyBandGattSession
import com.harman.connect.PartyBoxBrEdrSession
import com.harman.connect.PartyBoxGattSession
import com.harman.connect.PartyLightGattSession
import com.harman.connect.isDisconnected
import com.harman.discover.DeviceScanner
import com.harman.home.HomePagesActivity
import com.harman.moment.MomentViewModel
import com.harman.moment.MomentViewModelFactory
import com.harman.music.mini.AbsPlayerItem
import com.harman.music.mini.IMiniPlayerEventListener
import com.harman.music.mini.MiniPlayersAdapter
import com.harman.music.mini.MiniPlayersViewModel
import com.harman.music.mini.MiniPlayersViewModelFactory
import com.harman.music.player.FullPlayerActivity
import com.harman.music.player.PlayerViewModel
import com.harman.music.player.mapPlayerViewModel
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.ota.partybox.isPbOtaProcessing
import com.harman.partylight.control.HybridPartyLightUiController
import com.harman.partylight.isPlOtaProcessing
import com.harman.portalActivity

import com.harman.webview.models.HybridViewModel
import com.harman.webview.models.OneDeviceOfflineViewModel
import com.harman.webview.models.OneDeviceOfflineViewModelFactory
import com.harman.webview.models.OneDeviceReadyToConnectViewModel
import com.harman.webview.models.OneDeviceReadyToConnectViewModelFactory
import com.harman.webview.models.OneHybridViewModel
import com.harman.webview.models.OneHybridViewModelFactory
import com.harman.webview.models.PartyLightHybridViewModel
import com.harman.webview.models.PartyLightHybridViewModelFactory
import com.harman.discover.DeviceStore
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.hasWhatsNew
import com.harman.isOneCommanderGroup
import com.harman.log.Logger
import com.harman.partyband.control.HybridPartyBandUiController
import com.harman.partyband.ota.isBbOtaProcessing
import com.harman.product.list.EnumUIConnectStatus
import com.harman.rating.RatingInAppMgr.Companion.instance
import com.harman.rating.model.RatingGuideType
import com.harman.rating.model.TriggerEventName
import com.harman.rating.model.UserAction
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.uiConnectStatus
import com.harman.webview.models.OneCommanderGroupHybridViewModel
import com.harman.webview.models.OneCommanderGroupHybridViewModelFactory

import com.harman.webview.models.PartyBandHybridViewFactory
import com.harman.webview.models.PartyBandHybridViewModel
import com.harman.webview.models.PartyBoxHybridViewModel
import com.harman.webview.models.PartyBoxHybridViewModelFactory
import com.jbl.one.configuration.impl.Tools.hasLocalOneDeviceWhatsNewHybridFilesUrl
import com.wifiaudio.app.LinkplayApplication
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

/**
 * Created by gerrardzhang on 2024/4/2.
 *
 * Hybrid activity for device control. Also support view style which was in abnormal state like:
 * Offline, Ready to connect, etc.
 */
class DeviceControlHybridActivity : CommonHybridActivity(), IMiniPlayerEventListener {

    private var momentViewModel: MomentViewModel? = null
    private var playerViewModel: PlayerViewModel<*>? = null
    private var hybridViewModel: HybridViewModel? = null

    private var miniPlayersViewModel: MiniPlayersViewModel? = null
    private val _miniPlayerItems = MutableLiveData<List<AbsPlayerItem>?>()
    val miniPlayerItems: LiveData<List<AbsPlayerItem>?>
        get() = _miniPlayerItems.distinctUntilChanged()

    private val _miniPlayersAdapter = MutableLiveData<MiniPlayersAdapter>()
    val miniPlayersAdapter: LiveData<MiniPlayersAdapter>
        get() = _miniPlayersAdapter

    private val _showMiniPlayer = MutableLiveData<Boolean>()
    val showMiniPlayer: LiveData<Boolean>
        get() = _showMiniPlayer

    private var device: Device? = null

    private var enumViewStyle: EnumViewStyle? = null

    private val volumeSetterStream = MutableSharedFlow<SetVolumeEvent>(
        replay = 1,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        when (val style = getViewStyle()) {
            EnumViewStyle.ONLINE.ordinal -> initAsOnline()
            EnumViewStyle.OFFLINE.ordinal -> initAsOffline()
            EnumViewStyle.READY_TO_CONNECT.ordinal -> initAsReadyToConnect()
            else -> {
                Logger.e(TAG, "onCreate() >>> unsupported view style currently [$style]")
                finish()
            }
        }

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            volumeSetterStream
                .flowWithLifecycle(lifecycle = lifecycle)
                .sample(200L) // setVolume at most 200 ms per time
                .collect { (item, volume) ->
                    Logger.d(TAG, "volumeSetterStream() >>> item[${item.device.UUID}] volume[$volume]")
                    miniPlayersViewModel?.setVolume(item = item, volume = volume)
                }
        }
    }

    override fun onResume() {
        super.onResume()
        DeviceScanner.registerObserver(deviceScanner)
    }

    override fun onPause() {
        super.onPause()
        DeviceScanner.unregisterObserver(deviceScanner)
    }

    override fun onDestroy() {
        super.onDestroy()

        hybridViewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }

        momentViewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }

        playerViewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }

        LinkplayApplication.me.CurrentDevice = null
    }

    private fun getViewStyle(): Int? = intent?.getIntExtra(BUNDLE_VIEW_STYLE, -1)

    private fun initAsOnline(
        uuid: String? = intent?.getStringExtra(BUNDLE_UUID),
        url: String? = intent?.getStringExtra(BUNDLE_URL)
    ) {
        Logger.d(TAG, "initAsOnline() >>> uuid[$uuid] url:$url")

        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "initAsOnline() >>> invalid input value. uuid[$uuid]")
            finish()
            return
        }

        Logger.i(TAG, "initAsOnline() >>> input uuid[$uuid]")

        val device = DeviceStore.find(uuid = uuid) ?: run {
            Logger.e(TAG, "initAsOnline() >>> cant find device by uuid[$uuid]")
            finish()
            return
        }

        Logger.i(TAG, "initAsOnline() >>> UUID[${device.UUID}]")
        enumViewStyle = EnumViewStyle.ONLINE
        super.loadUrl(url = url)
        <EMAIL> = device

        // very important for music service.
        com.harman.music.Tools.updateDeviceItemInApplication(device = device)

        device.pid?.also { tryRateUs(it) }

        buildViewModels(device = device)

        if ((device as? OneDevice)?.hasWhatsNew() == true && hasLocalOneDeviceWhatsNewHybridFilesUrl()) {
            (hybridViewModel as? OneHybridViewModel)?.also {
                lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                    //delay(500)
                    WhatIsNewHybridActivity.launchActivity(this@DeviceControlHybridActivity, device)
                }
            }
        }
        Logger.i(TAG, "initAsOnline() >>> url:$url")
        miniPlayersViewModel = ViewModelProvider(
            owner = this,
            factory = MiniPlayersViewModelFactory()
        )[MiniPlayersViewModel::class.java]

        (hybridViewModel as? OneHybridViewModel)?.also {

            it.bottomPlayerVisibility.observe(this@DeviceControlHybridActivity) { showBottomPlayer ->
                Logger.i(TAG, "show bottom player in music service showBottomPlayer :$showBottomPlayer")
                if (showBottomPlayer) {
                    showInMusicServiceMiniPlayer()
                } else {
                    hideInMusicServiceMiniPlayer()
                }
            }
        }
    }

    private fun showInMusicServiceMiniPlayer() {
        device?.let { safeDevice ->
            miniPlayersViewModel?.updateDevices(listOf(safeDevice))
        }

        binding?.activity = this@DeviceControlHybridActivity
        binding?.lifecycleOwner = this@DeviceControlHybridActivity
        _showMiniPlayer.value = true
        val miniPlayersAdapter = MiniPlayersAdapter(data = miniPlayerItems.value, fragment = this, listener = this)
        _miniPlayersAdapter.value = miniPlayersAdapter
        miniPlayersViewModel?.miniPlayerItems?.observe(this) { items ->
            _miniPlayerItems.value = items
        }
    }

    private fun hideInMusicServiceMiniPlayer() {
        miniPlayersViewModel?.updateDevices(emptyList())
        binding?.activity = this@DeviceControlHybridActivity
        binding?.lifecycleOwner = this@DeviceControlHybridActivity
        _showMiniPlayer.value = false
    }

    private fun buildViewModels(device: Device) {
        val momentViewModel = if (device is OneDevice) {
            val vm = ViewModelProvider(this, MomentViewModelFactory(device))[MomentViewModel::class.java]
            lifecycle.addObserver(vm)
            <EMAIL> = vm
            vm
        } else null

        val playerViewModel = device.mapPlayerViewModel(owner = this)?.also { playerViewModel ->
            <EMAIL> = playerViewModel
            lifecycle.addObserver(playerViewModel)
        }

        val hybridViewModel = mapOnlineViewModel(
            device = device,
            momentViewModel = momentViewModel,
            playerViewModel = playerViewModel
        ).also { vm ->
            <EMAIL> = vm
            lifecycle.addObserver(vm)
            vm.notificationInvoker = this
            vm.gattConnectStatusListener = ::onGattConnectStatusChanged
            vm.brEdrConnectStatusListener = ::onBrEdrConnectStatusChanged
            Logger.i(TAG, "buildViewModels() >>> target device:\n$device")
        }

        (hybridViewModel as? OneHybridViewModel)?.also {
            HybridOneUiController(this, device as OneDevice, launcher = launcher, eventStream = it.eventStream, notificationInvoker = this)
        }

        (hybridViewModel as? OneCommanderGroupHybridViewModel)?.also {
            HybridOneUiController(this, device as OneDevice, launcher = launcher, eventStream = it.eventStream, notificationInvoker = this)
        }

        (hybridViewModel as? PartyLightHybridViewModel)?.also {
            HybridPartyLightUiController(this, it)
        }
        (hybridViewModel as? PartyBandHybridViewModel)?.also {
            HybridPartyBandUiController(this, it)
        }
    }


    private fun initAsOffline(
        uuid: String? = intent?.getStringExtra(BUNDLE_UUID),
        url: String? = intent.getStringExtra(BUNDLE_URL)
    ) {
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "initAsOffline() >>> invalid input value. uuid[$uuid]")
            finish()
            return
        }

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            val deviceDummy = withContext(DISPATCHER_IO) {
                LocalCacheAdapter.getOfflineDevice(context = this@DeviceControlHybridActivity, uuid = uuid)
            }

            if (null == deviceDummy) {
                Logger.e(TAG, "initAsOffline() >>> fail to restore dummy from DB. uuid[$uuid]")
                finish()
                return@launch
            }

            Logger.i(TAG, "initAsOffline() >>> UUID[${device?.UUID}] url:$url")
            <EMAIL> = deviceDummy
            enumViewStyle = EnumViewStyle.OFFLINE
            super.loadUrl(url = url)

            hybridViewModel = ViewModelProvider(
                this@DeviceControlHybridActivity,
                OneDeviceOfflineViewModelFactory(deviceDummy = deviceDummy)
            )[OneDeviceOfflineViewModel::class.java].also { vm ->
                vm.notificationInvoker = this@DeviceControlHybridActivity
                lifecycle.addObserver(vm)
            }
        }
    }

    private fun initAsReadyToConnect(
        uuid: String? = intent?.getStringExtra(BUNDLE_UUID),
        url: String? = intent.getStringExtra(BUNDLE_URL)
    ) {
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "initAsReadyToConnect() >>> invalid input value. uuid[$uuid]")
            finish()
            return
        }

        val device = DeviceStore.findOne(uuid = uuid) ?: run {
            Logger.e(TAG, "initAsReadyToConnect() >>> cant find one device by uuid[$uuid]")
            finish()
            return
        }

        Logger.i(TAG, "initAsReadyToConnect() >>> UUID[${device.UUID}] url:$url")
        <EMAIL> = device
        enumViewStyle = EnumViewStyle.READY_TO_CONNECT
        super.loadUrl(url = url)

        hybridViewModel = ViewModelProvider(
            this,
            OneDeviceReadyToConnectViewModelFactory(device = device)
        )[OneDeviceReadyToConnectViewModel::class.java].also { vm ->
            vm.notificationInvoker = this
            vm.eventListener = eventsListener
            lifecycle.addObserver(vm)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)

        Logger.i(TAG, "onNewIntent() >>>")
        when (val style = getViewStyle()) {
            EnumViewStyle.ONLINE.ordinal -> initAsOnline()
            EnumViewStyle.OFFLINE.ordinal -> initAsOffline()
            EnumViewStyle.READY_TO_CONNECT.ordinal -> initAsReadyToConnect()
            else -> {
                Logger.w(TAG, "onNewIntent() >>> unsupported view style currently [$style]")
            }
        }
    }

    private fun mapOnlineViewModel(
        device: Device,
        momentViewModel: MomentViewModel?,
        playerViewModel: PlayerViewModel<*>?
    ): HybridViewModel =
        when (device) {
            is OneDevice -> {
                if(device.isOneCommanderGroup())
                    ViewModelProvider(
                        this,
                        OneCommanderGroupHybridViewModelFactory(
                            device = device,
                            launcher = launcher,
                            momentViewModel = momentViewModel,
                            playerViewModel = playerViewModel
                        )
                    )[OneCommanderGroupHybridViewModel::class.java]
                else
                ViewModelProvider(
                    this,
                    OneHybridViewModelFactory(
                        device = device,
                        launcher = launcher,
                        momentViewModel = momentViewModel,
                        playerViewModel = playerViewModel
                    )
                )[OneHybridViewModel::class.java]
            }

            is PartyBoxDevice -> ViewModelProvider(
                this,
                PartyBoxHybridViewModelFactory(
                    activity = this@DeviceControlHybridActivity,
                    uuid = device.UUID ?: "",
                    launcher = launcher
                )
            )[PartyBoxHybridViewModel::class.java]

            is PartyLightDevice -> ViewModelProvider(
                this,
                PartyLightHybridViewModelFactory(device)
            )[PartyLightHybridViewModel::class.java]

            is PartyBandDevice -> ViewModelProvider(
                this,
                PartyBandHybridViewFactory(device)
            )[PartyBandHybridViewModel::class.java]

            else -> throw IllegalArgumentException("Un support device type currently:${device.productLine}")
        }

    override fun onCustomEvent(
        eventName: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            Tools.repeatWithTimeout(
                repeatTimes = 0,
                timeoutMills = JS_EVENT_TIMEOUT_MILLS,
                onBlock = {
                    hybridViewModel?.onCustomEvent(
                        activity = this@DeviceControlHybridActivity,
                        eventName = eventName,
                        data = data,
                        callback = callback
                    )
                },
                onTimeout = {
                    callback.invoke("-1", null, null)
                }
            )
        }
    }

    override fun onNavigate(
        pageTitle: String?,
        pageName: String?,
        isPush: String?,
        data: JsonObject?,
        callback: (code: String, msg: String?, passBack: JSONObject?) -> Unit
    ) {
        callback.invoke("0", null, null)
    }

    override fun onPopPage(callback: (code: String, msg: String?) -> Unit) {
        callback.invoke("0", "pop page")
        finish()
    }

    private fun onGattConnectStatusChanged(
        status: EnumConnectionStatus,
        session: BaseGattSession<*, *, *>
    ) {
        if (session.device.UUID != hybridViewModel?.device?.bleDevice?.UUID) {
            //Ignore devices that are not currently page actions
            Logger.w(
                TAG,
                "onGattConnectStatusChanged() >>> status:[$status], session device uuid:[${session.device.UUID}], current device uuid:[${hybridViewModel?.device?.bleDevice?.UUID}]"
            )
            return
        }
        if (status.isDisconnected() && session is PartyBoxGattSession && !isPbOtaProcessing) {
            Logger.w(TAG, "onGattConnectStatusChanged() >>> PartyBox device gatt disconnected")
            HomePagesActivity.resumeDashboardWithoutRecreate(this)
        }
        if (status.isDisconnected() && session is PartyLightGattSession && !isPlOtaProcessing) {
            Logger.w(TAG, "onGattConnectStatusChanged() >>> PartyLight device gatt disconnected")
            HomePagesActivity.resumeDashboardWithoutRecreate(this)
        }
        if (status.isDisconnected() && session is PartyBandGattSession && !isBbOtaProcessing) {
            Logger.w(TAG, "onGattConnectStatusChanged() >>> PartyBand device gatt disconnected")
            HomePagesActivity.resumeDashboardWithoutRecreate(this)
        }
    }

    private fun onBrEdrConnectStatusChanged(
        status: EnumConnectionStatus,
        session: BaseBrEdrSession<*, *, *>
    ) {
        if (status.isDisconnected() && session is PartyBoxBrEdrSession && !isPbOtaProcessing) {
            Logger.w(TAG, "onBrEdrConnectStatusChanged() >>> PartyBox device gatt disconnected")
            HomePagesActivity.resumeDashboardWithoutRecreate(this)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK || requestCode != REQUEST_CODE) {
            return
        }

        /**
         * Source: [OneHomeSeriesProductSettingsActivity.handleRestoreFactory]
         */
        if (data.isFinishSelf()) {
            finish()
        }
    }

    private fun Intent?.isFinishSelf(): Boolean {
        return null != this && getBooleanExtra(RESULT_FINISH_SELF, false)
    }

    private fun tryRateUs(devicePid: String) {
        val ratingGuideType = instance.getGuideType(devicePid)
        instance.logTriggerEvent(devicePid, TriggerEventName.DASH_BOARD)

        Logger.d(TAG, "ratingGuideType: $ratingGuideType")
        if (ratingGuideType !== RatingGuideType.RATING_NONE) {

            val reviewManager: ReviewManager = ReviewManagerFactory.create(this)
            reviewManager.requestReviewFlow().addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    reviewManager.launchReviewFlow(this, task.result).addOnCompleteListener {
                        instance.logUserAction(UserAction.ACTION_RATE_US)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "DeviceControlHybridActivity"

        /**
         * @return false if this [pid] didn't support hybrid page currently.
         */
        fun portalAsOnline(activity: Activity, pid: String?, uuid: String?): Boolean {
            if (pid.isNullOrBlank()) {
                return false
            }

            if (uuid.isNullOrBlank()) {
                return false
            }

            val url = HybridTools.getDashboardUrl(activity = activity, pid = pid) ?: run {
                return false
            }

            val intent = Intent(activity, DeviceControlHybridActivity::class.java)

            intent.putExtra(BUNDLE_VIEW_STYLE, EnumViewStyle.ONLINE.ordinal)
            intent.putExtra(BUNDLE_URL, url)
            intent.putExtra(BUNDLE_UUID, uuid)

            activity.startActivity(intent)
            return true
        }

        /**
         * Only one device support currently.
         */
        fun portalAsOffline(activity: Activity, device: Device): Boolean {
            val uuid = device.UUID
            if (uuid.isNullOrBlank()) {
                return false
            }

            val url = HybridTools.getDeviceAbnormalUrl(activity = activity) ?: run {
                return false
            }
            val intent = Intent(activity, DeviceControlHybridActivity::class.java)

            intent.putExtra(BUNDLE_VIEW_STYLE, EnumViewStyle.OFFLINE.ordinal)
            intent.putExtra(BUNDLE_URL, url)
            intent.putExtra(BUNDLE_UUID, uuid)

            activity.startActivity(intent)
            return true
        }

        /**
         * Only one device support currently.
         */
        fun portalAsReadyToConnect(activity: Activity, device: OneDevice): Boolean {
            val uuid = device.UUID

            if (uuid.isNullOrBlank()) {
                return false
            }

            val url = HybridTools.getDeviceAbnormalUrl(activity = activity) ?: run {
                return false
            }

            val intent = Intent(activity, DeviceControlHybridActivity::class.java)

            intent.putExtra(BUNDLE_VIEW_STYLE, EnumViewStyle.READY_TO_CONNECT.ordinal)
            intent.putExtra(BUNDLE_URL, url)
            intent.putExtra(BUNDLE_UUID, uuid)

            activity.startActivity(intent)
            return true
        }

        const val REQUEST_CODE = 233

        private const val RESULT_FINISH_SELF = "RESULT_FINISH_SELF"

        fun sealFinishIntent(): Intent = Intent().apply {
            putExtra(RESULT_FINISH_SELF, true)
        }
    }

    private val eventsListener = object : IViewModelEvents {
        @MainThread
        override fun refreshAsOnline(device: OneDevice) {
            /** refresh UI by [checkUiConnectStatus] */
        }
    }

    enum class EnumViewStyle(val value: Int) {
        ONLINE(0),
        OFFLINE(1),
        READY_TO_CONNECT(2)
    }

    override fun onRootLayoutClick(item: AbsPlayerItem) {
        Logger.d(TAG, "onRootLayoutClick() >>> [${item.device}]")
        portalActivity(FullPlayerActivity::class.java, item.device)
    }

    override fun onPlayPauseBtnClick(item: AbsPlayerItem) {
        Logger.d(TAG, "onPlayPauseBtnClick() >>> [${item.device}]")
        miniPlayersViewModel?.playOrPause(item = item)
    }

    override fun onStopMomentClick(item: AbsPlayerItem) {
        Logger.d(TAG, "onStopMomentClick() >>> [${item.device}]")
        miniPlayersViewModel?.cancelSoundScapeV2(item)
    }

    override fun onVolumeBarSeek(item: AbsPlayerItem, progress: Int) {
        Logger.d(TAG, "onVolumeBarSeek() >>> [${item.device}] vol[$progress]")
        volumeSetterStream.tryEmit(SetVolumeEvent(item = item, volume = progress))
    }

    override val tag: String = TAG

    override val baseControlDevice: Device?
        get() = playerViewModel?.device

    private val deviceScanner = object : IHmDeviceObserver {
        override fun onDeviceOnlineOrUpdate(device: Device) {
            checkUiConnectStatus(device = device)
        }

        override fun onDeviceOffline(device: Device) {
            checkUiConnectStatus(device = device)
        }
    }

    private fun checkUiConnectStatus(device: Device) {
        if (device != <EMAIL>) {
            return
        }

        val newUiConnectStatus = device.uiConnectStatus()
        val newEnumViewStyle = newUiConnectStatus.toEnumViewStyle()
        if (EnumViewStyle.READY_TO_CONNECT == enumViewStyle &&
            EnumViewStyle.OFFLINE == newEnumViewStyle
        ) {
            // cause LinkPlay SDK might not notify device online in time after setup wifi,
            // so filter this view style switch case
            Logger.w(TAG, "checkUiConnectStatus() >>> ignore offline status when in ready to connect mode")
            return
        }

        if (enumViewStyle != newEnumViewStyle) {
            Logger.d(
                TAG, "checkUiConnectStatus() >>> UUID[${device.UUID}] " +
                        "ViewStyle[$enumViewStyle] => [$newEnumViewStyle] " +
                        "newUiConnectStatus[$newUiConnectStatus]"
            )

            lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                when (newEnumViewStyle) {
                    EnumViewStyle.ONLINE -> {
                        if (device is OneDevice && device.groupInfoExt?.groupInfo?.isGroup() == true) {
                            //don't refresh offline group
                            return@launch
                        }
                        initAsOnline(
                            uuid = device.UUID,
                            url = HybridTools.getDashboardUrl(
                                activity = this@DeviceControlHybridActivity,
                                pid = device.pid
                            )
                        )
                    }

                    EnumViewStyle.READY_TO_CONNECT -> initAsReadyToConnect(
                        uuid = device.UUID,
                        url = HybridTools.getDeviceAbnormalUrl(activity = this@DeviceControlHybridActivity)
                    )
                    EnumViewStyle.OFFLINE -> Unit // do nothing cause during OOBE ota flow, the device might offline.
                }
            }
        }
    }

    private fun EnumUIConnectStatus.toEnumViewStyle(): EnumViewStyle = when (this) {
        EnumUIConnectStatus.OFFLINE -> EnumViewStyle.OFFLINE
        EnumUIConnectStatus.READY_TO_CONNECT -> EnumViewStyle.READY_TO_CONNECT
        EnumUIConnectStatus.WIFI_ONLINE,
        EnumUIConnectStatus.BLE_CONNECTED,
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> EnumViewStyle.ONLINE
    }

    override fun onActivityResult(result: ActivityResult) {
        super.onActivityResult(result)
        hybridViewModel?.handleStartForResult(result)
    }

    private data class SetVolumeEvent(val item: AbsPlayerItem, val volume: Int)
}