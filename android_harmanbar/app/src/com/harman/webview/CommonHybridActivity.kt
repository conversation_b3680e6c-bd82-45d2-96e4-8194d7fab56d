package com.harman.webview

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.annotation.MainThread
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.databinding.ActivityHybirdBinding
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.widget.AppCompatBaseActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject


/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/29.
 *
 * Base hybrid activity.
 */
abstract class CommonHybridActivity : AppCompatBaseActivity(), IHybridBusinessEvents, IHybridNotification {

    private var mWebView: CommonWebView? = null
    protected var binding: ActivityHybirdBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.d(TAG, "onCreate() >>> ")

        val binding = ActivityHybirdBinding.inflate(layoutInflater)
        <EMAIL> = binding
        setContentView(binding.root)

        val webview = CommonWebView(context = this, scope = lifecycleScope, eventsListener = this)
        mWebView = webview
        webview.visibility = View.INVISIBLE
        binding.webviewContainer.addView(
            webview, FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            delay(250)
            Logger.d(TAG, "onCreate() >>> show web view")
            webview.visibility = View.VISIBLE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.d(TAG, "onDestroy() >>> ")
        mWebView?.also { (it.parent as? ViewGroup)?.removeView(it) }
        mWebView = null
    }

    override fun onBackPressed() {
        Logger.d(TAG, "onBackPressed() >>> mWebView?.canGoBack(): ${mWebView?.canGoBack()}")
        if (true == mWebView?.canGoBack()) {
            mWebView?.goBack()
        } else {
            super.onBackPressed()
        }
    }

    protected fun loadUrlFromBundle() {
        val url = intent.getStringExtra(BUNDLE_URL) ?: run {
            Logger.e(TAG, "loadUrlFromBundle() >>> missing url")
            return
        }

        Logger.d(TAG, "onCreate() >>> start to load URL: $url")
        loadUrl(url)
    }

    @SuppressLint("SetJavaScriptEnabled")
    protected fun loadUrl(url: String?) {
        if (url.isNullOrBlank()) {
            Logger.e(TAG, "loadUrl() >>> missing url")
            return
        }

        val webview = mWebView ?: run {
            Logger.e(TAG, "loadUrl() >>> missing webview instance")
            return
        }

        webview.settings.javaScriptEnabled = true
        webview.settings.allowFileAccess = true
        webview.settings.allowContentAccess = true
        webview.loadUrl(url)
        Logger.d(TAG, "loadUrl() >>> $url")
    }

    @MainThread
    protected fun evaluateJavascript(script: String, callback: ((String) -> Unit)? = null) {
        mWebView?.evaluateJavascript(script, callback)
    }

    @MainThread
    override fun notifyObj(eventName: String?, params: JSONObject?) {
        mWebView?.notification(eventName = eventName, params = params)
    }

    @MainThread
    override fun notifyArray(eventName: String?, params: JSONArray?) {
        mWebView?.notification(eventName = eventName, params = params)
    }

    companion object {
        const val BUNDLE_URL = "Bundle_Url"
        const val BUNDLE_UUID = "Bundle_UUID"

        /**
         * @see [DeviceControlHybridActivity.EnumViewStyle]
         */
        const val BUNDLE_VIEW_STYLE = "Bundle_View_Style"

        const val JS_EVENT_TIMEOUT_MILLS = 9 * 1000L

        private const val TAG = "CommonHybridActivity"
    }
}