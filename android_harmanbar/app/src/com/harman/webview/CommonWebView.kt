package com.harman.webview

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.webkit.ConsoleMessage
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.annotation.MainThread
import com.google.gson.JsonObject
import com.harman.bar.app.BuildConfig
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.webview.models.Events
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/29.
 */
@SuppressLint("ViewConstructor")
class CommonWebView : WebView, IHybridRawEvents {

    private var scope: CoroutineScope? = null
    private var businessListener: IHybridBusinessEvents? = null

    constructor(context: Context, scope: CoroutineScope, eventsListener: IHybridBusinessEvents?) :
            this(context = context, attrs = null, scope = scope, eventsListener = eventsListener)

    constructor(
        context: Context,
        attrs: AttributeSet?,
        scope: CoroutineScope,
        eventsListener: IHybridBusinessEvents?
    ) : super(context, attrs) {
        <EMAIL> = scope
        <EMAIL> = eventsListener

        init()
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        scope: CoroutineScope,
        eventsListener: IHybridBusinessEvents?
    ) : super(context, attrs, defStyleAttr) {
        <EMAIL> = scope
        <EMAIL> = eventsListener

        init()
    }

    fun setListener(eventsListener: IHybridBusinessEvents) {
        <EMAIL> = eventsListener
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun init() {
        if (BuildConfig.DEBUG) {
            setWebContentsDebuggingEnabled(true)
        }

        scrollBarStyle = View.SCROLLBARS_INSIDE_OVERLAY
        overScrollMode = View.OVER_SCROLL_NEVER
        setBackgroundColor(Color.TRANSPARENT)
        setLayerType(LAYER_TYPE_HARDWARE, null) // much useful
        webViewClient = CustomWebViewClient()
        webChromeClient = CustomWebChromeClient()

        // remove security flaws in js
        removeJavascriptInterface("searchBoxJavaBridge_")
        removeJavascriptInterface("accessibility")
        removeJavascriptInterface("accessibilityTraversal")

        settings.let {
            // WebView 密码明文存储漏洞，需要禁用
            it.savePassword = false
            // 是否允许页面执行js脚本，注意不能去掉
            it.javaScriptEnabled = true
            // 设置允许访问本地路径
            it.allowContentAccess = true
            // 设置各种存储
            it.databaseEnabled = true
            it.domStorageEnabled = true
            // 设置各种存储路径
            it.databasePath = context.getDir("database", Context.MODE_PRIVATE).path
            // WebView 域控制不严格漏洞，需要禁用
            it.allowFileAccess = false
            it.allowFileAccessFromFileURLs = false
            it.allowUniversalAccessFromFileURLs = false
            // Cursor recommend
            it.setRenderPriority(WebSettings.RenderPriority.HIGH)
            it.setEnableSmoothTransition(true)
            it.useWideViewPort = true
            it.loadWithOverviewMode = true
            it.displayZoomControls = false
            it.cacheMode = WebSettings.LOAD_DEFAULT
        }

        linkJSInterface()
    }

    /**
     * link js bridge interface with WebView
     */
    private fun linkJSInterface() {
        addJavascriptInterface(
            CustomJSBridge(
                scope = scope,
                eventsListener = this,
                syncEvaluateJavascript = ::syncEvaluateJS,
                asyncEvaluateJavaScript = ::asyncEvaluateJS
            ),
            "AndroidObj"
        )
    }

    @MainThread
    override fun onCustomEvent(eventName: String?, data: JsonObject?, callbackID: String?) {
        Logger.d(TAG, "onCustomEvent() >>> eventName[$eventName] data:$data callbackID[$callbackID]")
        businessListener?.onCustomEvent(
            eventName = eventName,
            data = data
        ) { code, msg, passBack ->
            val script = HybridTools.formatCallback(
                code = code,
                msg = msg,
                callbackID = callbackID,
                data = passBack
            )

            when (eventName) {
                Events.GET_PRODUCT_IMAGE -> Unit // too much log msg about img base64
                else -> Logger.d(TAG, "onCustomEvent() >>> exe script:${script.replace("\\", "")}")
            }

            evaluateJavascript(script) { _ -> }
        }
    }

    @MainThread
    override fun onNavigate(
        pageTitle: String?, pageName: String?, isPush: String?,
        data: JsonObject?, callbackID: String?
    ) {
        Logger.d(TAG, "onNavigate() >>> pageTitle[$pageTitle] pageName[$pageName] isPush[$isPush] data:$data callbackID[$callbackID]")
        businessListener?.onNavigate(
            pageTitle = pageTitle,
            pageName = pageName,
            isPush = isPush,
            data = data
        ) { code, msg, passBack ->
            val script = HybridTools.formatCallback(
                code = code,
                msg = msg,
                callbackID = callbackID,
                data = passBack
            )

            Logger.d(TAG, "onNavigate() >>> exe script:$script")
            evaluateJavascript(script) { _ -> }
        }
    }

    override fun onPopPage(callbackID: String?) {
        Logger.d(TAG, "onPopPage() >>> ")
        businessListener?.onPopPage { code, msg ->
            val script = HybridTools.formatCallback(
                code = code,
                msg = msg,
                callbackID = callbackID,
                data = null
            )

            Logger.d(TAG, "onPopPage() >>> exe script:$script")
            evaluateJavascript(script) { _ -> }
        }
    }

    override fun onLogger(event: LoggerEvent, callbackID: String?) {
        if (!event.message.isNullOrBlank()) {
            when (event.level) {
                0 -> Logger.d(LOGGER, event.message)
                1 -> Logger.i(LOGGER, event.message)
                2 -> Logger.w(LOGGER, event.message)
                3 -> Logger.e(LOGGER, event.message)
                else -> Logger.d(LOGGER, event.message)
            }
        }
    }

    @MainThread
    internal fun notification(eventName: String?, params: JSONObject?) {
        val script = HybridTools.formatNotification(eventName = eventName, params = params)
        Logger.d(TAG, "notification() >>> exe script:${script.replace("\\", "")}")

        evaluateJavascript(script) { _ -> }
    }

    @MainThread
    internal fun notification(eventName: String?, params: JSONArray?) {
        val script = HybridTools.formatNotification(eventName = eventName, params = params)
        Logger.d(TAG, "notification() >>> exe script:$script \n json string >>> ${params?.toString()}")

        evaluateJavascript(script) { _ -> }
    }

    /**
     * Call js bridge function sync.
     */
    private suspend fun syncEvaluateJS(javascript: String): String? =
        withContext(DISPATCHER_FAST_MAIN) {
            suspendCoroutine<String?> { continuation ->
                evaluateJavascript(javascript) { v ->
                    continuation.resume(v)
                }
            }
        }

    private fun asyncEvaluateJS(javascript: String, cb: ValueCallback<String?>) {
        evaluateJavascript(javascript, cb)
    }

    private inner class CustomWebViewClient : WebViewClient() {
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            Logger.d(TAG, "shouldOverrideUrlLoading() >>> $url")
            view.loadUrl(url)
            return true
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            Logger.d(TAG, "onPageFinished() >>> $url")

        }

        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            Logger.d(TAG, "onPageStarted() >>> $url")
        }
    }

    private inner class CustomWebChromeClient : WebChromeClient() {
        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
            Logger.d(TAG_CONSOLE, "web console: ${consoleMessage?.message()}")
            return super.onConsoleMessage(consoleMessage)
        }
    }

    override fun destroy() {
        super.destroy()
        Logger.d(TAG, "destroy() >>> ")
        // Cursor recommend
        clearHistory()
        clearCache(true)
        clearFormData()
    }

    companion object {
        private const val TAG = "CommonWebView"
        private const val LOGGER = "WebViewLogger"
        private const val TAG_CONSOLE = "CommonWebViewConsole"
    }
}