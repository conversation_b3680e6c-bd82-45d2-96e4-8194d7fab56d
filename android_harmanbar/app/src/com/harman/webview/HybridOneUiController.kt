package com.harman.webview

import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.MainThread
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope

import com.harman.EventUtils
import com.harman.calibration.CalibrationBaseActivity
import com.harman.calibration.CalibrationGuideDialog
import com.harman.calibration.IAudioCalibrationDialogEvent

import androidx.lifecycle.repeatOnLifecycle

import com.harman.command.one.bean.EnumCoulsonStatus
import com.harman.command.one.bean.GroupMode
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.getGroupMode
import com.harman.getFwCheckRspWithTimeout
import com.harman.log.Logger
import com.harman.moment.IOTAGuideDialogEvent
import com.harman.moment.OTAGuideDialog
import com.harman.multichannel.UngroupDialog
import com.harman.music.service.EnumMusicServiceSource
import com.harman.music.service.IMusicServiceListListener
import com.harman.music.service.MusicServiceListDialog
import com.harman.music.service.MusicServiceProvider
import com.harman.music.service.MusicServiceSDKHelper
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.WiFiOOBEDialogHelper
import com.harman.ota.one.OneOtaActivity
import com.harman.portalUrl
import com.harman.printf
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.va.VAGuideDialog
import com.harman.va.refactor.VAPortalHelper
import com.harman.webview.models.Notifications
import com.harman.webview.models.OHVMEvent
import com.harman.webview.models.OneHybridViewModel
import com.harman.webview.models.OneHybridViewModel.Companion.LEARN_MORE_ABOUT_VA
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.local_music.MainItem
import com.wifiaudio.service.DlnaServiceProviderPool
import com.wifiaudio.utils.cloudRequest.model.CheckFWResp
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * @Description One device in [DeviceControlHybridActivity] business UI operations
 * <AUTHOR>
 * @Time 2024/12/2
 */
class HybridOneUiController(
    private val activity: DeviceControlHybridActivity,
    private val device: OneDevice,
    private val eventStream: MutableSharedFlow<OHVMEvent>,
    private val notificationInvoker: IHybridNotification?,
    private val launcher: ActivityResultLauncher<Intent>
) : DefaultLifecycleObserver {

    private val loginHelper = MusicServiceSDKHelper(TAG = TAG, viewModelScope = activity.lifecycleScope, ::device)

    private var vaGuideDialog: VAGuideDialog? = null
    private var audioCalibrationDialog: CalibrationGuideDialog? = null
    private var mRoonReadyLaterTipsDialog: RoonReadyLaterTipsDialog? = null
    private var wifiOOBEDialogHelper: WiFiOOBEDialogHelper? = null
    private var mBleStereoDialog: BleStereoSetupWifiTipsDialog? = null
    private var mUngroupDialog: UngroupDialog? = null
    private var otaGuideDialog: OTAGuideDialog? = null
    private var musicServiceListDialog: MusicServiceListDialog? = null

    init {
        activity.lifecycle.addObserver(this)
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        owner.lifecycleScope.launch {
            eventStream.collect {
                onEvent(it)
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        activity.lifecycle.removeObserver(this)
        audioCalibrationDialog?.dismiss()
        mRoonReadyLaterTipsDialog?.dismiss()
        wifiOOBEDialogHelper?.dismissAllDialogs()
        mBleStereoDialog?.dismiss()
        mUngroupDialog?.dismiss()
        otaGuideDialog?.dismiss()
        musicServiceListDialog?.dismiss()
    }

    @MainThread
    private fun onEvent(event: OHVMEvent) {
        when (event) {
            is OHVMEvent.EnterCoulson -> {
                when (event.status) {
                    EnumCoulsonStatus.NONE_SETUP -> {
                        vaGuideDialog = VAGuideDialog(activity, device, false).apply {
                            show()
                        }
                    }

                    EnumCoulsonStatus.ONLY_SETUP_GOOGLE -> {
                        VAPortalHelper.portalAlexa(activity, device, EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_BANNER_ALEXA)
                    }

                    EnumCoulsonStatus.ONLY_SETUP_ALEXA -> {
                        VAPortalHelper.portalGVA(activity, device, EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_BANNER_GVA)
                    }

                    EnumCoulsonStatus.ALL_SETUP -> {
                        activity.portalUrl(
                            config = AppConfigurationUtils.getLearnMoreAboutVaUrl(),
                            default = LEARN_MORE_ABOUT_VA
                        )
                    }
                }
            }
            is OHVMEvent.ShowCalibrationGuideDialog -> {
                showCalibrationGuideDialog(event = event)
            }
            is OHVMEvent.ShowRoonReadyLaterTipsDialog -> {
                showRoonReadyLaterTipsDialog()
            }
            is OHVMEvent.EnterWiFiSetupFlow -> {
                handleEnterSetupNetwork(event = event)
            }
            is OHVMEvent.UnGroup -> {
                handleUngroup(event = event)
            }
            is OHVMEvent.OtaGuide -> {
                showOtaGuideDialog(event = event)
            }
            is OHVMEvent.PopMusicServiceDialog -> {
                portalMusicServicePage(event = event)
            }
        }
    }

    @MainThread
    private fun showCalibrationGuideDialog(event: OHVMEvent.ShowCalibrationGuideDialog) {
        audioCalibrationDialog?.dismiss()
        audioCalibrationDialog = CalibrationGuideDialog(
            activity = activity,
            device = device,
            listener = object : IAudioCalibrationDialogEvent {
                override fun onCalibrationBtnClick() {
                    CalibrationBaseActivity.portal(context = activity, device = device)
                }

                override fun onLaterBtnClick() {
                    // do nothing
                }
            }
        ).apply {
            show()
        }
    }

    private fun showRoonReadyLaterTipsDialog() {
        mRoonReadyLaterTipsDialog?.dismiss()
        mRoonReadyLaterTipsDialog = RoonReadyLaterTipsDialog(activity).apply {
            show()
        }
    }

    private fun handleEnterSetupNetwork(event: OHVMEvent.EnterWiFiSetupFlow) {
        val device = device
        Logger.d(TAG, "handleEnterSetupNetwork() >>> isBLEOnline[${device.isBLEOnline}] isPlaying[${device.isPlaying}] isGroup[${device.getGroupMode() != GroupMode.SINGLE}]")
        if (device.isBLEOnline && device.isPlaying && device.getGroupMode() == GroupMode.GROUP) {
            showSetupWifiForBleStereo()
            return
        }

        wifiOOBEDialogHelper?.dismissAllDialogs()
        wifiOOBEDialogHelper = WiFiOOBEDialogHelper(
            logTag = TAG,
            activity = activity,
            device = device,
            mode = EnumMode.WIFI_SETUP,
            oobeType = EventUtils.Dimension.EnumOOBEType.MANUAL,
            oobeDialogDismissListener = null,
            oobeDialogEventListener = null,
            calibrationDialogEvent = null,
            vaGuideDialogDismissListener = null
        ).run()
    }

    private fun showSetupWifiForBleStereo() {
        mBleStereoDialog = BleStereoSetupWifiTipsDialog(context = activity).apply {
            show()
        }
    }

    private fun handleUngroup(event: OHVMEvent.UnGroup) {
        mUngroupDialog?.dismiss()
        mUngroupDialog = UngroupDialog(
            device = device,
            activity = activity,
            oneHybridViewModel = null
        ).apply {
            show()
        }
    }

    private fun showOtaGuideDialog(event: OHVMEvent.OtaGuide) {
        otaGuideDialog?.dismiss()
        otaGuideDialog = OTAGuideDialog(
            context = activity,
            device = device,
            checkFwRsp = event.checkFwRsp,
            listener = OTAGuideDialogListener(activity = activity, event = event)
        ).apply {
            show()
        }
    }

    private inner class OTAGuideDialogListener(
        private val activity: FragmentActivity,
        private val event: OHVMEvent.OtaGuide
    ) : IOTAGuideDialogEvent {
        override fun onContinueBtnClick(callbackRsp: CheckFWResp?) {
            activity.lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                val rsp = event.checkFwRsp ?:
                    callbackRsp ?:
                    device.getFwCheckRspWithTimeout(logTag = TAG)

                rsp ?: run {
                    Logger.e(TAG, "onContinueBtnClick() >>> fail to get CheckFWResp")
                    return@launch
                }

                Logger.i(TAG, "onContinueBtnClick() >>> ${rsp.printf()}")

                OneOtaActivity.launchOtaPage(
                    launcher = launcher,
                    activity = activity,
                    device = device,
                    checkFwRsp = rsp
                )
            }
        }
    }

    @MainThread
    fun portalMusicServicePage(event: OHVMEvent.PopMusicServiceDialog): Boolean {
        val result = if (null == event.source) { // fix bug HOP-27630
            musicServiceListDialog = MusicServiceListDialog(
                device = device,
                activity = activity,
                listener = object : IMusicServiceListListener {
                    override fun onMusicServices(services: List<MainItem>) {
                        val jsonStr = GsonUtil.parseBeanToJson(MusicServiceList(ids = getEnableMusicServiceIds()))
                        notificationInvoker?.notifyObj(Notifications.MUSIC_SERVICE, JSONObject(jsonStr))
                    }
                },
                uiController = this@HybridOneUiController
            ).apply {
                show()
            }
            true
        } else {
            loginHelper.notifyEnterMusicServiceMainContentPage()
            portalMusicService(source = event.source, activity = activity)
        }

        val provider = device.wifiDevice?.deviceItem?.uuid?.let {
            DlnaServiceProviderPool.me().getDlanHelper(it)
        }

        if (provider != null) { // fix bug HOP-27001
            WAApplication.me.SetCurrentServerProvider(provider)
        }

        return result
    }

    /**
     * @return false if meeting unsupported music service type
     * supported source [MusicServiceProvider.defaultSource]
     */
    @MainThread
    private fun portalMusicService(
        source: EnumMusicServiceSource?,
        activity: FragmentActivity
    ): Boolean = when (source) {
        EnumMusicServiceSource.TUNEIN_NEW -> {
            loginHelper.portalTuneIn(activity = activity)
            true
        }

        EnumMusicServiceSource.IHEART -> {
            loginHelper.portalIHeart(activity = activity)
            true
        }

        EnumMusicServiceSource.TIDAL -> {
            loginHelper.portalTidal(activity = activity)
            true
        }

        EnumMusicServiceSource.VTUNER -> {
            loginHelper.portalVTuner(activity = activity)
            true
        }

        EnumMusicServiceSource.NAPSTER -> {
            loginHelper.portalNapster(activity = activity)
            true
        }

        EnumMusicServiceSource.QOBUZ -> {
            loginHelper.portalQOBuz(activity = activity)
            true
        }

        EnumMusicServiceSource.AMAZON_PRIME -> {
            loginHelper.portalAmazonPrime(activity = activity)
            true
        }

        EnumMusicServiceSource.CALM_RADIO -> {
            loginHelper.portalCalmRadio(activity = activity)
            true
        }

        else -> {
            Logger.e(TAG, "portalMusicService() >>> unsupported service source[${source?.strValue}]")
            false
        }
    }

    private fun getEnableMusicServiceIds(): List<String> =
        MusicServiceProvider.getEnableSources(currentDevice = device.wifiDevice?.deviceItem)
            .map { source ->
                source.strValue
            }

    companion object {
        private const val TAG = "HybridOneUiController"
    }
}