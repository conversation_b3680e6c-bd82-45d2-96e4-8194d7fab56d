package com.harman.nightlistening

import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.Button
import android.widget.CompoundButton
import android.widget.Switch
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityNightListeningBinding
import com.harman.bean.CommonEvent
import com.harman.command.one.bean.GroupDevicesChangeItem
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.syncGetPersonalListeningMode
import com.harman.discover.util.Tools.getBarPort
import com.harman.parseAsOneDevice
import com.harman.partylight.util.fitSystemBar
import com.harman.discover.util.Tools.wlan0Mac
import com.harman.hkone.ScreenUtil
import com.harman.log.Logger
import com.skin.SkinResourcesUtils
import com.skin.font.FontUtils
import com.tencent.mmkv.MMKV
import com.wifiaudio.action.log.print_log.LogsUtil
import com.wifiaudio.app.WAApplication
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevPersonalListeningFragment
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevPersonalListeningModel
import com.wifiaudio.view.pagesdevcenter.devicesetting.DevPersonalListeningViewPager2Adapter
import config.LogTags
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @Description Most of the code comes from [DevPersonalListeningFragment]
 * <AUTHOR>
 * @Time 2024/9/25
 */
class NightListeningActivity : AppCompatActivity(), View.OnClickListener, CompoundButton.OnCheckedChangeListener {
    private val binding by lazy { ActivityNightListeningBinding.inflate(layoutInflater) }
    private val mainDev by lazy { parseAsOneDevice() }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (null == mainDev) {
            finish()
            return
        }
        fitSystemBar()
        setContentView(binding.root)
        initView()
        bindSlots()
        register()
        mainDev?.registerDeviceListener(jblOneDeviceListener)
    }

    var dataInfo: DataFragInfo? = null
    var isPersonalListeningMode = false

    var vback: Button? = null
    var vTitle: TextView? = null
    private var tvSubTitle: TextView? = null
    private var switchButton: Switch? = null
    private var layoutOOBE: ConstraintLayout? = null
    private var layoutEdit: ConstraintLayout? = null
    private var layoutDesc: ConstraintLayout? = null
    private var layoutViewPager: ConstraintLayout? = null
    private var btnContinue: Button? = null
    private var viewPager2: ViewPager2? = null
    private var personalListeningViewPager2Adapter: DevPersonalListeningViewPager2Adapter? = null
    private var currList: List<DevPersonalListeningModel> = ArrayList()

    private val jblOneDeviceListener = object : IOneDeviceListener {

        override fun onUpnpNotifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {
            Logger.d(TAG, "onUpnpNotifyGroupDevicesChange() >>> [${mainDev?.UUID}] [$uuid] [${mainDev?.wifiDevice?.deviceItem?.uuid}] $result")
            super.onUpnpNotifyGroupDevicesChange(uuid, result)
            if(uuid == mainDev?.wifiDevice?.deviceItem?.uuid){
                lifecycleScope.launch {
                    result?.payload?.personalListeningMode?.isOn()?.let{
                        if (it != isPersonalListeningMode) {
                            isPersonalListeningMode = it
                            updatePersonalListeningModeUI(isPersonalListeningMode)
                        }
                    }
                }
            }
        }
    }

    private fun initView() {
        vback = findViewById(R.id.vback)
        vTitle = findViewById(R.id.vtitle)
        tvSubTitle = findViewById(R.id.tv_sub_title)
        switchButton = findViewById(R.id.switch_button)
        layoutOOBE = findViewById(R.id.layout_OOBE)
        layoutEdit = findViewById(R.id.layout_edit)
        layoutDesc = findViewById(R.id.layout_desc)
        layoutViewPager = findViewById(R.id.layout_view_pager)
        viewPager2 = findViewById(R.id.viewPager2)
        btnContinue = findViewById(R.id.btn_continue)

        switchButton?.setOnCheckedChangeListener(this)
        btnContinue?.setOnClickListener(this)

        vback?.setBackgroundResource(R.drawable.select_icon_menu_back)
        vTitle?.apply {
            text = getString(R.string.night_listening)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, WAApplication.mResources.getDimension(R.dimen.font_16))
            setTextColor(ContextCompat.getColor(this@NightListeningActivity, R.color.fg_primary))
            typeface = FontUtils.getInstance().getResFontTypeface(R.font.poppins_bold)
        }

        initViewPager2()
        initData()
    }

    private fun initViewPager2() {
        personalListeningViewPager2Adapter = DevPersonalListeningViewPager2Adapter(this, initViewPager2DataList())
        viewPager2?.let { pager ->
            val recyclerView = pager.getChildAt(0) as RecyclerView
            val padding = ScreenUtil.dip2px(this, 24f)
            recyclerView.let {
                it.setPadding(padding, 0, padding, 0)
                it.clipToPadding = false
                it.overScrollMode = View.OVER_SCROLL_NEVER
            }
//            pager.offscreenPageLimit = ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT
            pager.offscreenPageLimit = 3
//            pager.clipChildren = false
//            pager.clipToPadding = false
            pager.setPageTransformer(MarginPageTransformer(padding))
            pager.adapter = personalListeningViewPager2Adapter
        }
    }

    private fun initViewPager2DataList(): List<DevPersonalListeningModel> {
        val dataList: MutableList<DevPersonalListeningModel> = ArrayList()
        val data1 = DevPersonalListeningModel()
        data1.title = SkinResourcesUtils.getString("step_1")
        data1.subTitle = SkinResourcesUtils.getString("Detach_rear_speakers")
        data1.image = R.drawable.image_personal_listening_mode_desc1
        dataList.add(data1)
        val data2 = DevPersonalListeningModel()
        data2.title = SkinResourcesUtils.getString("step_2")
        data2.subTitle = getString(R.string.place_rear_speakers_in_front_of_you)
        data2.image = R.drawable.image_personal_listening_mode_desc2
        dataList.add(data2)
        val data3 = DevPersonalListeningModel()
        data3.subTitle = getString(R.string.enjoy_movies_at_night_without_disturbing_others)
        data3.image = R.drawable.image_personal_listening_mode_desc3
        dataList.add(data3)
        currList = dataList
        return dataList
    }

    private fun initData() {
        lifecycleScope.launch {
            updatePersonalListeningModeUI(isPersonalListeningMode)
            val isOn = mainDev!!.syncGetPersonalListeningMode(port = mainDev?.getBarPort()) ?: false
            if (isOn != isPersonalListeningMode) {
                isPersonalListeningMode = isOn
                updatePersonalListeningModeUI(isPersonalListeningMode)
            }
        }
    }

    private fun updatePersonalListeningModeUI(isPersonalListeningMode: Boolean) {
        mainDev?.let { itAuthDevice ->
            val isOOBECompleted = MMKV.defaultMMKV().decodeBool(itAuthDevice.wlan0Mac(), false)

            if (isOOBECompleted) {
                layoutOOBE?.visibility = View.GONE
                layoutEdit?.visibility = View.VISIBLE
            } else {
                layoutOOBE?.visibility = View.VISIBLE
                layoutEdit?.visibility = View.GONE
            }

            switchButton?.isChecked = isPersonalListeningMode
            if (isPersonalListeningMode) {
                if (isOOBECompleted) {
                    layoutDesc?.visibility = View.GONE
                    layoutViewPager?.visibility = View.VISIBLE
                } else {
                    layoutDesc?.visibility = View.GONE
                    layoutViewPager?.visibility = View.GONE
                }
                tvSubTitle?.setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_activate))

            } else {
                tvSubTitle?.setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_secondary))

                if (isOOBECompleted) {
                    layoutDesc?.visibility = View.VISIBLE
                    layoutViewPager?.visibility = View.GONE
                } else {
                    layoutDesc?.visibility = View.GONE
                    layoutViewPager?.visibility = View.GONE
                }
            }
        }


    }

    private fun bindSlots() {
        vback?.setOnClickListener {
            finish()
        }
    }

    fun register() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    fun unregister() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onResume() {
        super.onResume()
        LogsUtil.d(LogTags.Device, "$TAG onResume()")
    }

    override fun onPause() {
        super.onPause()
        LogsUtil.d(LogTags.Device, "$TAG onPause()")
    }

    override fun onDestroy() {
        super.onDestroy()
        LogsUtil.d(LogTags.Device, "$TAG onDestroy()")
        unregister()
        mainDev?.unregisterDeviceListener(jblOneDeviceListener)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCommonEvent(commonEvent: CommonEvent?) {
        LogsUtil.d(LogTags.Device, "$TAG onCommonEvent:${commonEvent.toString()}")
    }

    companion object {
        val TAG = DevPersonalListeningFragment::class.simpleName
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.switch_button -> {

            }

            R.id.btn_continue -> {
                mainDev?.let { itAuthDevice ->
                    MMKV.defaultMMKV().encode(itAuthDevice.wlan0Mac(), true)
                }
                layoutOOBE?.apply {
                    fadeOut(this)
                }
                layoutEdit?.apply {
                    fadeIn(this)
                }
                if (isPersonalListeningMode) {
                    layoutDesc?.apply {
                        fadeOut(this)
                    }
                    layoutViewPager?.apply {
                        fadeIn(this)
                    }
                } else {
                    layoutDesc?.apply {
                        fadeIn(this)
                    }
                    layoutViewPager?.apply {
                        fadeOut(this)
                    }
                }


            }
        }

    }

    override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
        buttonView?.let { itButtonView ->
            if (itButtonView.isPressed) {
                if (isChecked) {
                    tvSubTitle?.setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_activate))
                    layoutDesc?.apply {
                        fadeOut(this)
                    }
                    layoutViewPager?.apply {
                        fadeIn(this)
                    }
                } else {
                    tvSubTitle?.setTextColor(ContextCompat.getColor(WAApplication.me, R.color.fg_secondary))
                    layoutDesc?.apply {
                        fadeIn(this)
                    }
                    layoutViewPager?.apply {
                        fadeOut(this)
                    }
                }

                lifecycleScope.launch {
                    mainDev!!.setPersonalListeningMode(isChecked,mainDev?.getBarPort())
                }
            }
        }
    }

    private fun fadeIn(view: View) {
        fadeIn(view, 0f, 1f, 300)
        view.isEnabled = true
    }

    private fun fadeOut(view: View) {
        fadeOut(view, 1f, 0f, 300)
        view.isEnabled = false
    }

    private fun fadeIn(view: View, startAlpha: Float, endAlpha: Float, duration: Long) {
        if (view.visibility == View.VISIBLE) return
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.duration = duration
        view.startAnimation(animation)
        view.visibility = View.VISIBLE
    }

    private fun fadeOut(view: View, startAlpha: Float, endAlpha: Float, duration: Long) {
        if (view.visibility == View.GONE) return
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.duration = duration
        view.startAnimation(animation)
        view.visibility = View.GONE
    }
}