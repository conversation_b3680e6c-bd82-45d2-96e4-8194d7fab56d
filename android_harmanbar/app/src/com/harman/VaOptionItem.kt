package com.harman

enum class VaOptionItem(val optionName: String, val expectResult: Int) {
    ALEXA_GVA(EventUtils.Dimension.EnumVaSetupOption.GVA_ALEXA.value, 2),
    ALEX(EventUtils.Dimension.EnumVaSetupOption.ALEXA.value, 1),
    GVA(EventUtils.Dimension.EnumVaSetupOption.GVA.value, 1);

    companion object {
        fun findItemByOptionName(opName: String): VaOptionItem? {
            for (item in entries) {
                if (item.optionName == opName) {
                    return item
                }
            }
            return null
        }
    }
}
