package com.harman.product.irlearn

import android.content.Context
import android.os.Bundle
import com.harman.bar.app.databinding.DialogIrLearningGuideBinding
import com.harman.bar.app.databinding.DialogIrLearningStartBinding
import com.harman.BottomPopUpDialog
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.getBarPort

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/9/4.
 */
class IRLearningDialog(
    private val device: OneDevice,
    context: Context
) : BottomPopUpDialog(context = context) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        showGuideViewStyle()
    }

    private fun showGuideViewStyle() {
        val binding = DialogIrLearningGuideBinding.inflate(layoutInflater)
        binding.dialog = this
        binding.lifecycleOwner = this
        setContentView(binding.root)
    }

    private fun showStartViewStyle() {
        val binding = DialogIrLearningStartBinding.inflate(layoutInflater)
        binding.dialog = this
        binding.lifecycleOwner = this
        setContentView(binding.root)
    }

    fun onCloseBtnClick() {
        dismiss()
    }

    fun onStartIRLearningClick() {
        showStartViewStyle()
        device.setIRLearn(device.getBarPort())
    }
}