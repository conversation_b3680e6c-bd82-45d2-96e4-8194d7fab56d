package com.harman.product.info

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ClickUtils
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityProductInfoBinding
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.log.Logger
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.ota.PartyOtaChecker
import com.harman.ota.PartyOtaCheckerParams
import com.harman.ota.one.IViewStyleListener
import com.harman.ota.one.OneOtaActivity
import com.harman.ota.one.ViewStyle
import com.harman.ota.partybox.PartyBoxOtaActivity
import com.harman.partyband.ota.PartyBandOtaActivity
import com.harman.partylight.util.fitSystemBar
import com.harman.safeAddNonNullSources
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.utils.Utils
import com.harman.v5protocol.bean.devinfofeat.V5FirmwareVersion
import com.harman.webview.OTAStatus
import com.harman.widget.AppCompatBaseActivity
import com.jbl.one.configuration.AppConfigurationUtils
import com.skin.SkinResourcesUtils
import com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by sky on 2024/7/5.
 */
class ProductInfoActivity : AppCompatBaseActivity(), IViewStyleListener {
    private var mOnlineDiagnosisDialog: OnlineDiagnosisDialog? = null

    private val _device = MutableLiveData<Device>()

    private var viewModel: ProductInfoViewModel? = null

    private val _hasProductSupportUrl = MutableLiveData<Boolean>()
    val hasProductSupportUrl: LiveData<Boolean>
        get() = _hasProductSupportUrl

    private val _showMacAddress = MutableLiveData<Boolean>()
    val showMacAddress: LiveData<Boolean>
        get() = _showMacAddress

    private val _isOtaAvailable = MutableLiveData<Boolean>()
    val isOtaAvailable: LiveData<Boolean>
        get() = _isOtaAvailable

    private val _fwDesc = MediatorLiveData<String?>()
    val fwDesc: LiveData<String?>
        get() = _fwDesc
    private val binding by lazy { ActivityProductInfoBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        lifecycleScope.launch {
            val device = parseBundle(intent) ?: run {
                finish()
                return@launch
            }

            val viewModel = ViewModelProvider(
                this@ProductInfoActivity,
                ProductInfoViewModelFactory(device = device)
            )[ProductInfoViewModel::class.java].also { vm ->
                <EMAIL> = vm
                <EMAIL>(vm)
            }

            binding.viewModel = viewModel
            binding.activity = this@ProductInfoActivity
            binding.lifecycleOwner = this@ProductInfoActivity
            binding.device = _device.value

            binding.layoutSoftware.setOnClickListener(MultiClickListener(7, 666L))

            viewModel.isOtaAvailable.observe(this@ProductInfoActivity) { isVisible ->
                binding.itemSoftware.showImage(isVisible)
                _isOtaAvailable.value = isVisible
            }

            _hasProductSupportUrl.value = device.pid?.let { AppConfigurationUtils.getModelConfig(it)?.urls?.urlSupport }?.isNotBlank()

            viewModel.fetchOtaStatus(this@ProductInfoActivity)

            val showAddress =
                (device is OneDevice) && true == device.macAddress?.isNotBlank()//confirmed by UI team,only show mac address of wifi device
            Logger.d(TAG, "onCreate showAddress = $showAddress")
            _showMacAddress.value = showAddress
//            binding?.itemMacAddress?.isVisible = showAddress

            _fwDesc.safeAddNonNullSources(_device, isOtaAvailable) { d, a ->
                lifecycleScope.launch {
                    _fwDesc.value = mapFwDesc(device = d, isOtaAvailable = a)
                }
            }
        }
    }

    private suspend fun mapFwDesc(device: Device?, isOtaAvailable: Boolean?): String {
        val versionStr = when (device) {
            is OneDevice -> {
                device.oneOsVer?.let { osVer ->
                    val fwVersion = device.firmware?.let { fwVer -> "($fwVer)" } ?: ""
                    val finalVer = SkinResourcesUtils.getString("version_with_num")
                        .toString()
                        .format("OneOS $osVer $fwVersion")

                    finalVer
                } ?: run {
                    val fwVersion = device.firmware ?: ""
                    fwVersion
                }
            }

            is PartyBoxDevice -> getString(R.string.format_version, (device.firmwareVersion ?: ""))
            is PartyBandDevice -> getString(
                R.string.format_version,
                if (device.isGattConnected) device.getDevInfoFeat<V5FirmwareVersion>()?.version ?: "" else device.firmwareVersion
            )

            else -> ""
        }

        return if (true != isOtaAvailable) {
            "$versionStr\n${SkinResourcesUtils.getString("jbl_Software_is_up_to_date")}"
        } else {
            versionStr
        }
    }

    fun launchProductSupport() {
        val pid = _device.value?.pid ?: return
        Logger.d(TAG, "launchProductSupport pid: ${_device.value?.pid}, \nurls: ${AppConfigurationUtils.getModelConfig(pid)?.urls?.urlSupport}")
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlSupport ?: return
        loadingUrlInBrowser(linkUrl)
    }

    fun launchQsg() {
        val pid = _device.value?.pid ?: return
        Logger.d(TAG, "launchQsg pid: ${_device.value?.pid}, \nurls: ${AppConfigurationUtils.getModelConfig(pid)?.urls?.urlQsg}")
        val linkUrl = AppConfigurationUtils.getModelConfig(pid)?.urls?.urlQsg ?: return
        loadingUrlInAppWebView(linkUrl, SkinResourcesUtils.getString("jbl_Quick_Start_Guide"))
    }

    fun showOnlineDiagnosisDialog() {
        when (val oneDevice = _device.value) {
            is OneDevice -> {
                mOnlineDiagnosisDialog = OnlineDiagnosisDialog(device = oneDevice, activity = this).apply {
                    show()
                }
            }
        }
    }

    inner class MultiClickListener(triggerClickCount: Int, clickInterval: Long) :
        ClickUtils.OnMultiClickListener(triggerClickCount, clickInterval) {
        override fun onTriggerClick(v: View?) {
            Logger.d(TAG, "onTriggerClick")
            when (v?.id) {
                R.id.layout_software -> {
                    showOnlineDiagnosisDialog()
                }
            }
        }

        override fun onBeforeTriggerClick(v: View?, count: Int) {
            Logger.d(TAG, "onBeforeTriggerClick count=$count")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mOnlineDiagnosisDialog?.dismiss()

        viewModel?.let { vm ->
            lifecycle.removeObserver(vm)
        }
    }

    fun onOtaIconClick() {
        when (val dev = _device.value) {
            is OneDevice -> {
                val rsp = viewModel?.checkFwRsp?.value ?: run {
                    Logger.e(TAG, "onOtaIconClick() >>> checkFwRsp not available")
                    return
                }

                Logger.d(TAG, "onOtaIconClick() >>> CheckFwRsp: $rsp")
                OneOtaActivity.launchOtaPage(
                    activity = this@ProductInfoActivity,
                    launcher = null,
                    device = dev,
                    checkFwRsp = rsp
                )
            }

            is PartyBoxDevice -> {
                val model = viewModel?.remoteUpdateModel?.value ?: run {
                    Logger.e(TAG, "onOtaIconClick() >>> remoteUpdateModel not available")
                    return
                }

                Logger.d(TAG, "onOtaIconClick() >>> RemoteUpdateModel: $model")
                PartyBoxOtaActivity.launchOtaPage(
                    activity = this@ProductInfoActivity,
                    launcher = launcher,
                    device = dev,
                    model = model
                )
            }

            is PartyBandDevice -> {
                lifecycleScope.launch {
                    PartyOtaChecker.hasUpdateWithTimeout(
                        PartyOtaCheckerParams(
                            dev.pid!!,
                            dev.firmwareVersion,
                        ), 100
                    )?.also {
                        PartyBandOtaActivity.launch(this@ProductInfoActivity, dev.UUID!!, it)
                    }
                }
            }
        }
    }

    @MainThread
    private suspend fun parseBundle(intent: Intent?): Device? {
        intent ?: return null
        val uuid = intent.getStringExtra(BUNDLE_UUID)
        return findDeviceByUUID(uuid)
    }

    private suspend fun findDeviceByUUID(uuid: String?): Device? {
        if (uuid.isNullOrBlank()) {
            Logger.e(TAG, "findDeviceByUUID() >>> invalid UUID")
            return null
        }

        // try to get online first, than offline.
        val targetDevice = DeviceStore.find(uuid = uuid) ?: withContext(DISPATCHER_IO) {
            LocalCacheAdapter.getOfflineDevice(context = this@ProductInfoActivity, uuid = uuid)
        } ?: run {
            Logger.e(TAG, "findDeviceByUUID() >>> fail to get dummy from online or DB [$uuid]")
            return null
        }

        Logger.w(TAG, "findDeviceByUUID() >>> targetDevice: $targetDevice")
        _device.value = targetDevice
        return targetDevice
    }

    private fun loadingUrlInAppWebView(linkUrl: String, title: String) {
        Logger.d(TAG, "loadingUrlInAppWebView Url: $linkUrl")
        QuickStartGuideWebActivity.launchQuickStartGuideWebActivity(this, linkUrl, title)
    }

    private fun loadingUrlInBrowser(linkUrl: String) {
        Logger.d(TAG, "loadingUrlInBrowser Url: $linkUrl")
        runCatching {
            val intent = Intent()
            intent.action = "android.intent.action.VIEW"
            intent.data = Uri.parse(linkUrl)
            startActivity(intent)
        }.onFailure { e ->
            Logger.e(TAG, "", e)
        }
    }

    override val tag: String = TAG

    override val baseControlDevice: Device?
        get() = _device.value

    override fun onChange(viewStyle: ViewStyle) {
        // no impl.
    }

    override fun onActivityResult(result: ActivityResult) {
        super.onActivityResult(result)
        handleStartForResult(result)
    }

    private fun handleStartForResult(result: ActivityResult) {
        Logger.d(tag, "handleStartForResult() >>> $result")
        when {
            Activity.RESULT_OK == result.resultCode && result.isOtaSuccess() -> {
                Logger.d(tag, "handleStartForResult() >>> update fwDesc")
                lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                    _isOtaAvailable.value = false
                    refreshFwDesc()
                }
            }
        }
    }

    @MainThread
    private suspend fun refreshFwDesc() {
        val device = findDeviceByUUID(_device.value?.UUID) ?: _device.value

        when (device) {
            is OneDevice -> {
                Logger.d(TAG, "refreshFwDesc() >>> device[${device.UUID}] fwVersion[${device.firmware}] oneOsVer[${device.oneOsVer}]")
            }

            is PartyBoxDevice -> {
                Logger.d(TAG, "refreshFwDesc() >>> device[${device.UUID}] fwVersion[${device.firmwareVersion}]")
            }
        }

        _fwDesc.value = mapFwDesc(device = device, isOtaAvailable = false)
    }

    companion object {
        private const val TAG = "ProductInfoActivity"
        private const val BUNDLE_UUID = "Bundle_UUID"

        const val BUNDLE_OTA_RESULT = "Bundle_Ota_Result"

        fun launchProductInfoPage(
            activity: Activity,
            device: Device
        ) {
            FirebaseEventManager.report(
                eventName = EventUtils.EventName.EVENT_ACTION,
                device = device,
                dimensions = mapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.ACTION_MAIN_SCREEN.value,
                    EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumMainScreenActionItem.PRODUCT_INFO.value
                )
            )

            val intent = Intent(activity, ProductInfoActivity::class.java).apply {
                putExtra(BUNDLE_UUID, device.UUID)
            }
            activity.startActivity(intent)
        }

        fun ActivityResult?.isOtaSuccess(): Boolean =
            true == this?.data?.getBooleanExtra(BUNDLE_OTA_RESULT, false)
    }
}