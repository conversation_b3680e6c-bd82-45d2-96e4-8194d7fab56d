package com.harman.product.list.bean

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.product.list.EnumViewHolderType
import com.harman.discover.bean.PartyBandDevice
import com.harman.hkone.EnumBatteryViewChannel
import com.harman.modelName
import com.harman.product.list.EnumUIConnectStatus
import com.harman.uiConnectStatus

/**
 * @Description product card for [PartyBandDevice]
 * <AUTHOR>
 * @Time 2025/1/23
 */
data class PartyBandUIBean(
    val device: PartyBandDevice,
    val deviceName: String = buildDeviceName(device),
    val batteryAreaUiBean: BatteryAreaUiBean? = buildBatteryAreaUiBean(device),
    override val uiConnectStatus: EnumUIConnectStatus = device.uiConnectStatus(),
    override val firstTouchTime: Long = device.firstTouchTime,
    override val isOffline: Boolean = device.isOffline,
    override val viewType: Int = EnumViewHolderType.PARTY_BAND.ordinal,
    override val pid: String? = device.pid,
    val isBLEConnecting: Boolean
) : BaseProductUIBean<PartyBandDevice>(device) {

    @get:DrawableRes
    val icConnectStatusRes = when (uiConnectStatus) {
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.drawable.component_svg_icon_connect_type_bt
        else -> null
    }

    @get:ColorRes
    val tvConnectStatusColor = when (uiConnectStatus) {
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.color.fg_activate
        EnumUIConnectStatus.OFFLINE -> R.color.fg_disabled
        else -> null
    }

    @get:StringRes
    val tvConnectStatusText = when (uiConnectStatus) {
        EnumUIConnectStatus.BLUETOOTH_CONNECTED -> R.string.jbl_Connected
        EnumUIConnectStatus.OFFLINE -> R.string.unavailable
        else -> null
    }

    val statusDeviceNameTextColor = when (uiConnectStatus) {
        EnumUIConnectStatus.OFFLINE -> R.color.fg_disabled
        else -> R.color.fg_primary
    }

    companion object {
        fun buildBatteryAreaUiBean(device: PartyBandDevice): BatteryAreaUiBean? {
            return if (device.isOffline) null else BatteryAreaUiBean(
                listOf(BatteryAreaUiBean.Item(device.isCharging, device.batteryLevel, EnumBatteryViewChannel.NONE))
            )
        }

        fun buildDeviceName(device: PartyBandDevice): String {
            val prefix = if (BuildConfig.BUILD_TYPE.contains("debug", true)) "[${device.displayUuid}]" else ""
            return prefix + if (!device.deviceName.isNullOrBlank()) {
                device.deviceName
            } else {
                device.modelName()
            }
        }
    }
}