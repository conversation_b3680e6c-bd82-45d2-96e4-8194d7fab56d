package com.harman.product.list

import android.app.Activity
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.harman.bar.app.R
import com.harman.bar.app.databinding.DialogGroupAutoPopupBinding
import com.harman.BottomPopUpDialog
import com.harman.modelName
import com.harman.multichannel.MultichannelActivity
import com.harman.portalActivity
import com.harman.discover.DeviceStore
import com.jbl.one.configuration.model.CombinationItem
import com.skin.SkinResourcesUtils

/**
 * Created by sky on 2024/12/30.
 */
class GroupAutoPopupDialog(
    private val combinationItem: CombinationItem,
    private val activity: Activity,
) : BottomPopUpDialog(context = activity, anim = R.style.SlideDialogAnim) {

    private val _showIconPage = MutableLiveData<Boolean>()
    val showIconPage: LiveData<Boolean>
        get() = _showIconPage

    private val _image = MutableLiveData<Drawable>()
    val image: LiveData<Drawable>
        get() = _image

    private val _text = MutableLiveData<CharSequence?>()
    val text: LiveData<CharSequence?>
        get() = _text


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val binding = DialogGroupAutoPopupBinding.inflate(LayoutInflater.from(context))
        binding.dialog = this
        binding.lifecycleOwner = this

        setContentView(binding.root)
        setCancelable(false)
        _showIconPage.value = true
        if (combinationItem?.isStereo() == true) {
            _image.value = SkinResourcesUtils.getDrawable("stereo_auto_popup")
            _text.value =
                SkinResourcesUtils.getString("newStructure_Upgrade_your_listening_experience_with_stereo_effect")

        } else if (combinationItem.isOneCommanderGroup()) {
            _image.value = SkinResourcesUtils.getDrawable("one_commander_group")
            _text.value =
                SkinResourcesUtils.getString("group_one_commander_msg")

        } else {
            _image.value = SkinResourcesUtils.getDrawable("multichannel_auto_popup")
            _text.value =
                SkinResourcesUtils.getString("newStructure_Upgrade_your_listening_experience_with_multi_channel_effect")

        }

    }

    override fun onStart() {
        super.onStart()

    }

    override fun onStop() {
        super.onStop()


    }

    fun onContinueClick() {
        dismiss()
        val device =
            DeviceStore.oneDevices.filter { it.isWiFiOnline && it.groupInfoExt?.groupInfo?.isSingle() == true }
                .find { item -> item.modelName() == combinationItem.getGoModelName() }
        device?.also {
            activity.portalActivity(
                target = MultichannelActivity::class.java,
                device = device
            )
        }

    }

    fun onNotNowClick() {
        _showIconPage.value = false
    }

    fun onGotItClick() {
        dismiss()
    }

}