package com.harman.product.list

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.LocationManager
import android.net.ConnectivityManager
import android.net.Network
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import com.blankj.utilcode.util.SPUtils
import com.harman.CustomToast
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.MainTabBaseFragment
import com.harman.auracast.AuraCastActivity
import com.harman.bar.app.BuildConfig
import com.harman.bar.app.R
import com.harman.bar.app.databinding.FragmentMyProductsBinding
import com.harman.calibration.IAudioCalibrationDialogEvent
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GroupType
import com.harman.connect.secureBleGattConnect
import com.harman.connect.syncBrEdrConnectWithTimeout
import com.harman.discover.DeviceScanner
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.info.EnumProductLine
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools.addIfAbsent
import com.harman.discover.util.Tools.contains
import com.harman.discover.util.Tools.hasValidGroupId
import com.harman.discover.util.Tools.isHorizon3
import com.harman.discover.util.Tools.remove
import com.harman.getBLEProtocol
import com.harman.hkone.ScreenUtil
import com.harman.home.HomePagesActivity
import com.harman.isRemoteConfigAutoGattConnect
import com.harman.log.LogToastUtil
import com.harman.log.Logger
import com.harman.multichannel.repository.CacheRepository
import com.harman.music.mini.AbsPlayerItem
import com.harman.music.mini.IMiniPlayerEventListener
import com.harman.music.mini.MiniPlayersAdapter
import com.harman.music.mini.MiniPlayersGroupDecoration
import com.harman.music.mini.MiniPlayersViewModel
import com.harman.music.mini.MiniPlayersViewModelFactory
import com.harman.music.mini.MomentPlayerItem
import com.harman.music.mini.MusicPlayerItem
import com.harman.music.mini.RadioPlayerItem
import com.harman.music.player.FullPlayerActivity
import com.harman.oobe.IOOBEDialogEventListener
import com.harman.oobe.SecureBlePairingDialog
import com.harman.oobe.ble.BLEOOBEDialog
import com.harman.oobe.wifi.EnumMode
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.oobe.wifi.WiFiOOBEDialogHelper
import com.harman.partyband.debug.PartyBandDebugFloatingViews
import com.harman.partyband.musician.PartyBandChooseMusicianDialog
import com.harman.permission.UniversalDeviceObserver
import com.harman.portalActivity
import com.harman.product.list.MyProductsViewModel.Companion.banAuth
import com.harman.product.list.bean.BaseProductUIBean
import com.harman.product.list.bean.GeneralProductUIBean
import com.harman.product.list.bean.OneHarmanCastMultiChannelUIBean
import com.harman.product.list.bean.OneHarmanCastStereoUIBean
import com.harman.product.list.bean.OneProductUIBean
import com.harman.product.list.bean.PartyBandUIBean
import com.harman.product.list.bean.PartyBoxStereoUIBean
import com.harman.product.list.bean.PartyLightGroupUIBean
import com.harman.radio.RadioActivity
import com.harman.safeAddSources
import com.harman.shortName
import com.harman.shouldBlockGattConnect
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.v5protocol.bean.devinfofeat.V5DeviceOOBE
import com.harman.webview.AddNewProductHybridActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.webview.MBUtils
import com.harman.widget.AppCompatBaseActivity
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.CombinationItem
import com.wifiaudio.app.WAApplication
import com.wifiaudio.utils.AppPermissionUtils
import com.wifiaudio.view.pagesmsccontent.showToast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.min


/**
 * Created by gerrardzhang on 2024/5/22.
 */
class MyProductsFragment : MainTabBaseFragment(), IMiniPlayerEventListener {

    private var myProductsViewModel: MyProductsViewModel? = null

    private var binding: FragmentMyProductsBinding? = null

    private var miniPlayersViewModel: MiniPlayersViewModel? = null

    private val _myProductsAdapter = MutableLiveData<MyProductsAdapter>()
    val myProductsAdapter: LiveData<MyProductsAdapter>
        get() = _myProductsAdapter

    private val _myProductsDecoration = MutableLiveData<MyProductsDecoration>()
    val myProductsDecoration: LiveData<MyProductsDecoration>
        get() = _myProductsDecoration

    private val _miniPlayersAdapter = MutableLiveData<MiniPlayersAdapter>()
    val miniPlayersAdapter: LiveData<MiniPlayersAdapter>
        get() = _miniPlayersAdapter

    private val _miniPlayerDecoration = MutableLiveData<MiniPlayersGroupDecoration>()
    val miniPlayerDecoration: LiveData<MiniPlayersGroupDecoration>
        get() = _miniPlayerDecoration

    // VAGuideDialog's priority is higher than OOBEDialog
    private var bleOOBEDialog: BLEOOBEDialog? = null
    private var wifiOOBEDialogHelper: WiFiOOBEDialogHelper? = null

    private val showedBluetoothScanFailedDlg = AtomicBoolean(false)

    /**
     * UI beans which used to display.
     */
    private val _productBeans = MediatorLiveData<List<BaseProductUIBean<*>>>()
    val productBeans: LiveData<List<BaseProductUIBean<*>>>
        get() = _productBeans.distinctUntilChanged()

    private val _miniPlayerItems = MutableLiveData<List<AbsPlayerItem>?>()
    val miniPlayerItems: LiveData<List<AbsPlayerItem>?>
        get() = _miniPlayerItems

    private val singleMiniItem: LiveData<AbsPlayerItem?> = miniPlayerItems.map {
        it?.getOrNull(0)
    }
    val singleMiniPlayerItem: LiveData<MusicPlayerItem?> = singleMiniItem.map {
        it as? MusicPlayerItem
    }
    val singleMiniMomentItem: LiveData<MomentPlayerItem?> = singleMiniItem.map {
        it as? MomentPlayerItem
    }
    val singleMiniRadioItem: LiveData<RadioPlayerItem?> = singleMiniItem.map {
        it as? RadioPlayerItem
    }

    private val _viewStyle = MutableLiveData<ViewStyle?>()
    val viewStyle: LiveData<ViewStyle?>
        get() = _viewStyle

    private val permissionGranted: LiveData<Boolean> = viewStyle.map { viewStyle ->
        when (viewStyle) {
            ViewStyle.DISCOVERING,
            ViewStyle.PRODUCTS -> true

            else -> false
        }
    }.distinctUntilChanged()

    private val _isAuraCastEntranceVisible = MediatorLiveData<Boolean>()
    val isAuraCastEntranceVisible: LiveData<Boolean>
        get() = _isAuraCastEntranceVisible.distinctUntilChanged()

    /**
     * The layout height of [FragmentMyProductsBinding.layoutMiniPlayerContainer]
     */
    private val _miniPlayersLayoutHeight = MutableLiveData<Int>().apply { value = 0 }
    val miniPlayersLayoutHeight: LiveData<Int>
        get() = _miniPlayersLayoutHeight

    private val _miniPlayersLayoutHorizontalPadding = MutableLiveData<Int>().apply { value = 0 }
    val miniPlayersLayoutHorizontalPadding: LiveData<Int>
        get() = _miniPlayersLayoutHorizontalPadding

    private val _miniPlayerListPanelCoverHeight = MutableLiveData<Int>().apply { value = 0 }
    val miniPlayerListPanelCoverHeight: LiveData<Int>
        get() = _miniPlayerListPanelCoverHeight

    private val _redirectMarginBottom = MediatorLiveData<Int>().apply { value = 0 }
    val redirectMarginBottom: LiveData<Int>
        get() = _redirectMarginBottom

    /**
     * Display [FragmentMyProductsBinding.layoutMiniPlayerContainer] or not.
     */
    private val _isMiniPlayersLayoutVisible = MediatorLiveData<Boolean>()
    val isMiniPlayersLayoutVisible: LiveData<Boolean>
        get() = _isMiniPlayersLayoutVisible

    val speakersLayoutHeight = MutableLiveData<Int>()

    val miniPlayersDragTouchListener = MiniPlayersDragTouchListener(
        context = context,
        onHeight = { height, percentage ->
            updateMiniPlayerHeight(height = height, verticalShowingPercent = percentage)
        },
        miniPlayersLayoutHeight = miniPlayersLayoutHeight,
        speakersLayoutHeight = speakersLayoutHeight,
        miniPlayerItems = miniPlayerItems,
        coroutineScope = lifecycleScope,
        onScrollDirection = { direction ->
            if (EnumMoveDirection.UP == direction) {
                binding?.layoutMiniPlayerContainer?.recyclerViewMinis?.smoothScrollToPosition(0)
            }
        }
    )

    /**
     * Display [FragmentMyProductsBinding.layoutRedirectionContainer] or not.
     */
    private val _isRedirectionLayoutVisible = MediatorLiveData<Boolean>()
    val isRedirectionLayoutVisible: LiveData<Boolean>
        get() = _isRedirectionLayoutVisible

    private val _currentRedirectDevice = MediatorLiveData<Device?>()
    val currentRedirectDevice: LiveData<Device?>
        get() = _currentRedirectDevice

    private val _redirectDeviceImgUrl = MediatorLiveData<String>()
    val redirectDeviceImgUrl: LiveData<String>
        get() = _redirectDeviceImgUrl

    val draggerVisible: LiveData<Boolean> = miniPlayerItems.map { items ->
        !items.isNullOrEmpty() && items.size > 1
    }

    private val _showCantFindProduct = MutableLiveData<Boolean>()
    val showCantFindProduct: LiveData<Boolean>
        get() = _showCantFindProduct

    val isSingleMiniPlayerVisible: LiveData<Boolean> = miniPlayersLayoutHeight.map { height ->
        height <= miniPlayersDragTouchListener.minHeight
    }

    private val _runAnim = MutableLiveData<Boolean>()
    val runAnim: LiveData<Boolean>
        get() = _runAnim

    /**
     * Devices which doing BLE connecting triggered by user. (Display loading anim)
     */
    private val bleConnectingDevices = MutableLiveData<List<Device>?>()

    private val volumeSetterStream = MutableSharedFlow<SetVolumeEvent>(
        replay = 1,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val owner = activity ?: run {
            Logger.e(TAG, "onCreate() >>> missing owner activity")
            return
        }

        // MiniPlayersViewModel based on MyProductsViewModel.devices to
        val miniPlayersViewModel = ViewModelProvider(
            owner = owner,
            factory = MiniPlayersViewModelFactory()
        )[MiniPlayersViewModel::class.java]

        <EMAIL> = miniPlayersViewModel

        val myProductsViewModel = ViewModelProvider(
            owner = owner,
            factory = MyProductsViewModelFactory()
        )[MyProductsViewModel::class.java]

        <EMAIL> = myProductsViewModel

        lifecycle.addObserver(myProductsViewModel)
        lifecycle.addObserver(miniPlayersViewModel)

        listenSysBroadcast()
    }

    private fun listenSysBroadcast() {
        val ctx = context ?: return

        if (VERSION.SDK_INT >= VERSION_CODES.TIRAMISU) {
            ctx.registerReceiver(mReceiver, intentFilters, Context.RECEIVER_NOT_EXPORTED)
        } else {
            ctx.registerReceiver(mReceiver, intentFilters)
        }
    }

    private val intentFilters = IntentFilter().apply {
        // BT on/off
        addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
        // GPS on/off
        addAction(LocationManager.PROVIDERS_CHANGED_ACTION)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _myProductsAdapter.value = MyProductsAdapter(fragment = this@MyProductsFragment)
        _myProductsDecoration.value =
            MyProductsDecoration(isMiniPlayersLayoutVisible = isMiniPlayersLayoutVisible)

        val miniPlayersAdapter = MiniPlayersAdapter(
            data = miniPlayerItems.value,
            fragment = this@MyProductsFragment,
            listener = this@MyProductsFragment
        )
        _miniPlayersAdapter.value = miniPlayersAdapter
        _miniPlayerDecoration.value = MiniPlayersGroupDecoration(adapter = miniPlayersAdapter)

        val binding =
            FragmentMyProductsBinding.inflate(inflater, container, false).also { binding ->
                binding.fragment = this@MyProductsFragment
                binding.lifecycleOwner = this@MyProductsFragment
            }

        <EMAIL> = binding
        dataBinding(binding)

        return binding.root
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onResume() {
        super.onResume()

        // check if permission has been granted
        lifecycleScope.launch(DISPATCHER_IO) {
            delay(200)
            // wait app loading online/offline devices
            lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                updateViewStyle()
            }
        }

        processRedirection()

        myProductsAdapter.value?.notifyDataSetChanged()
    }

    override fun onDestroy() {
        super.onDestroy()
        groupAutoPopupDialog?.dismiss()
        context?.unregisterReceiver(mReceiver)
        stopDiscoveryAnimation(binding)

        bleOOBEDialog?.dismiss()
        permissionGrantDialog?.dismiss()
        permissionRationaleDialog?.dismiss()

        myProductsViewModel?.let { viewModel ->
            lifecycle.removeObserver(viewModel)
        }

        miniPlayersViewModel?.let { viewModel ->
            lifecycle.removeObserver(viewModel)
        }
    }

    private var cantFindProductDisplayJob: Job? = null

    @MainThread
    private fun updateViewStyle() {
        val style = if (!AppPermissionUtils.isAllPermissionGranted(context)) {
            ViewStyle.GRANT_PERMISSION
        } else if (!AppPermissionUtils.isBluetoothEnabled()) {
            ViewStyle.TURN_ON_BLUETOOTH
        } else if (!AppPermissionUtils.isGpsEnabled(context)) {
            ViewStyle.TURN_ON_GPS
        } else if ((productBeans.value?.size ?: 0) <= 0) {
            ViewStyle.DISCOVERING
        } else {
            ViewStyle.PRODUCTS
        }

        handleCantFindProductDisplayJob(style = style)
        _viewStyle.value = style
    }

    @MainThread
    private fun handleCantFindProductDisplayJob(style: ViewStyle) {
        if (ViewStyle.DISCOVERING == style) {
            if (true != _showCantFindProduct.value && true != cantFindProductDisplayJob?.isActive) {
                _showCantFindProduct.value = false
                cantFindProductDisplayJob?.cancel()
                cantFindProductDisplayJob = lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                    delay(CANT_FIND_PRODUCT_DISPLAY_JOB_MILLS)
                    _showCantFindProduct.value = true
                }
            }
        } else {
            _showCantFindProduct.value = false
            cantFindProductDisplayJob?.cancel()
        }
    }

    private fun startDiscoveryAnimation(binding: FragmentMyProductsBinding?) {
        _runAnim.value = true
    }

    private fun stopDiscoveryAnimation(binding: FragmentMyProductsBinding?) {
        _runAnim.value = false
    }

    private fun dataBinding(binding: FragmentMyProductsBinding) {
        (activity as? HomePagesActivity)?.tab?.observe(viewLifecycleOwner) { tab ->
            if (tab.isMyProductPageShowing()) {
                checkOOBEDialog()
            } else {
                foldMiniPlayer()
            }
        }

        val myProductsViewModel = myProductsViewModel ?: run {
            Logger.e(TAG, "dataBinding() >>> missing myProductsViewModel instance")
            return
        }

        val miniPlayersViewModel = miniPlayersViewModel ?: run {
            Logger.e(TAG, "dataBinding() >>> missing miniPlayersViewModel instance")
            return
        }

        myProductsViewModel.unAuthDevices.observe(viewLifecycleOwner) {
            checkOOBEDialog()
        }

        myProductsViewModel.bluetoothScanFailed.observe(viewLifecycleOwner) {
            showBluetoothScanFailedDialog()
        }

        viewStyle.observe(viewLifecycleOwner) { style ->
            when (style) {
                ViewStyle.TURN_ON_GPS,
                ViewStyle.TURN_ON_BLUETOOTH,
                ViewStyle.GRANT_PERMISSION -> {
                    bleOOBEDialog?.dismiss()
                    stopDiscoveryAnimation(binding)
                }

                ViewStyle.PRODUCTS -> {
                    stopDiscoveryAnimation(binding)
                    myProductsAdapter.value?.notifyDataSetChanged()
                }

                ViewStyle.DISCOVERING -> {
                    startDiscoveryAnimation(binding)
                }

                else -> {
                    // no impl
                    stopDiscoveryAnimation(binding)
                }
            }
        }

        _isAuraCastEntranceVisible.also { mediator ->
            mediator.addSource(viewStyle) { vs ->
                mediator.value = mapAuraCastEntranceVisible(
                    viewStyle = vs,
                    devices = myProductsViewModel.devices.value
                )
            }
            mediator.addSource(myProductsViewModel.devices) { devices ->
                mediator.value =
                    mapAuraCastEntranceVisible(viewStyle = viewStyle.value, devices = devices)
            }
        }

        permissionGranted.observe(viewLifecycleOwner) { granted ->
            if (granted) {
                DeviceScanner.registerObserver(UniversalDeviceObserver)
            } else {
                DeviceScanner.unregisterObserver(UniversalDeviceObserver)
            }
        }

        miniPlayersViewModel.miniPlayerItems.observe(viewLifecycleOwner) { items ->
            _miniPlayerItems.value = items
        }

        myProductsViewModel.groupAutoPopupItem.observe(viewLifecycleOwner) { item ->
            showAutoPopupisDialog(item)
        }

        _isMiniPlayersLayoutVisible.safeAddSources(
            miniPlayersViewModel.miniPlayerItems,
            viewStyle
        ) { miniPlayerItems, viewStyle ->
            _isMiniPlayersLayoutVisible.value = mapIsMiniPlayersLayoutVisible(
                miniPlayerItems = miniPlayerItems,
                viewStyle = viewStyle
            )
        }
        _productBeans.safeAddSources(
            myProductsViewModel.devices,
            myProductsViewModel.groupInfos,
            myProductsViewModel.rearsWrappers,
            bleConnectingDevices
        ) { devices, groupInfos, _, _ ->
            if (!isResumed) {
                return@safeAddSources
            }
            lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                _productBeans.value = mapToProductBeans(devices = devices, groupInfos = groupInfos)
                updateViewStyle()
            }

            miniPlayersViewModel.updateDevices(devices = devices)
            processRedirection()
        }

        _redirectMarginBottom.safeAddSources(
            _miniPlayersLayoutHeight,
            _isMiniPlayersLayoutVisible
        ) { height, isVisible ->
            _redirectMarginBottom.value = if (isVisible == true && height != null) {
                min(height + ScreenUtil.dip2px(context, 16f), ScreenUtil.dip2px(context, 142f))
            } else {
                ScreenUtil.dip2px(context, 16f)
            }
        }

        dataBindingSingleMiniPlayer(binding = binding)

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            volumeSetterStream
                .flowWithLifecycle(lifecycle = lifecycle)
                .sample(200L) // setVolume at most 200 ms per time
                .collect { (item, volume) ->
                    Logger.d(TAG, "volumeSetterStream() >>> item[${item.device.UUID}] volume[$volume]")
                    miniPlayersViewModel.setVolume(item = item, volume = volume)
                }
        }
    }

    private fun dataBindingSingleMiniPlayer(binding: FragmentMyProductsBinding) {
        singleMiniPlayerItem.observe(viewLifecycleOwner) { item ->
            MiniPlayersAdapter.decoItemMiniPlayerBinding(
                binding = binding.layoutMiniPlayerContainer.singleMiniPlayer.layoutMiniPlayer,
                item = item,
                listener = this@MyProductsFragment,
                fragment = viewLifecycleOwner
            )
        }

        singleMiniMomentItem.observe(viewLifecycleOwner) { item ->
            MiniPlayersAdapter.decoItemMiniMomentBinding(
                binding = binding.layoutMiniPlayerContainer.singleMiniPlayer.layoutMiniMoment,
                item = item,
                listener = this@MyProductsFragment,
                fragment = viewLifecycleOwner
            )
        }

        singleMiniRadioItem.observe(viewLifecycleOwner) { item ->
            MiniPlayersAdapter.decoItemMiniRadioBinding(
                binding = binding.layoutMiniPlayerContainer.singleMiniPlayer.layoutMiniRadio,
                item = item,
                listener = this@MyProductsFragment,
                fragment = viewLifecycleOwner
            )
        }
    }

    private var groupAutoPopupDialog: GroupAutoPopupDialog? = null
    fun showAutoPopupisDialog(item: CombinationItem) {
        activity?.also {
            groupAutoPopupDialog =
                GroupAutoPopupDialog(combinationItem = item, activity = it).apply {
                    show()
                }
        }

    }

    private fun mapIsMiniPlayersLayoutVisible(
        miniPlayerItems: List<AbsPlayerItem>?,
        viewStyle: ViewStyle?
    ): Boolean {
        return ViewStyle.PRODUCTS == viewStyle && !miniPlayerItems.isNullOrEmpty()
    }

    private fun mapAuraCastEntranceVisible(viewStyle: ViewStyle?, devices: List<Device>?): Boolean {
        return when (viewStyle) {
            ViewStyle.PRODUCTS -> !devices.isNullOrEmpty()
            else -> false
        }
    }

    @MainThread
    private fun checkOOBEDialog() {
        val activity = activity ?: run {
            Logger.w(TAG, "checkOOBEDialog() >>> missing activity")
            return
        }

        val unAuthDevices = myProductsViewModel?.unAuthDevices?.value
        val device = unAuthDevices?.getOrNull(0)
        if (!ableDisplayOOBEDialog(device)) {
            return
        }

        bleOOBEDialog?.dismiss()
        bleOOBEDialog = null
        wifiOOBEDialogHelper?.dismissAllDialogs()
        wifiOOBEDialogHelper = null

        when (device) {
            is OneDevice -> showWiFiOOBEDialog(activity = activity, device = device)
            is PartyBoxDevice,
            is PartyBandDevice -> {
                showBLEOOBEDialog(activity = activity, device = device)
            }
        }
    }

    @MainThread
    private fun ableDisplayOOBEDialog(device: Device?): Boolean {
        Logger.d(TAG, "ableDisplayOOBEDialog() >>> top unAuth device[${device?.UUID}] " +
                    "isBLEOnline[${device?.isBLEOnline}]" +
                    "other oobe showing[${bleOOBEDialog?.isShowing}] " +
                    "wifi oobe dialog showing[${wifiOOBEDialogHelper?.isAnyDialogShowing()}] " +
                    "banAuth[$banAuth] " +
                    "isMyProductPageShowing[${isMyProductPageShowing()}]")

        val rst = null != device &&
                device.isBLEOnline && // BLE device still online
                true != bleOOBEDialog?.isShowing &&
                true != wifiOOBEDialogHelper?.isAnyDialogShowing() &&
                !banAuth &&
                isMyProductPageShowing() // Showing settings page

        if (true != device?.isBLEOnline) {
            myProductsViewModel?.removeUnAuthDevice(device = device)
        }

        return rst
    }

    private fun showWiFiOOBEDialog(activity: FragmentActivity, device: OneDevice) {
        Logger.i(TAG, "showWiFiOOBEDialog() >>> show [${device.UUID}]")
        wifiOOBEDialogHelper = WiFiOOBEDialogHelper(
            logTag = TAG,
            activity = activity,
            device = device,
            mode = EnumMode.FULL,
            oobeType = EventUtils.Dimension.EnumOOBEType.AUTO,
            oobeDialogDismissListener = {
                // trigger next unAuthed device oobe dialog via LiveData in ViewModel
                myProductsViewModel?.authed(device)
            },
            oobeDialogEventListener = oobeDialogEvents,
            calibrationDialogEvent = object : IAudioCalibrationDialogEvent {
                override fun onLaterBtnClick() {
                    Logger.i(TAG, "CalibrationGuideDialog.onLaterBtnClick() >>> check OOBE again")
                    checkOOBEDialog()
                }
            },
            vaGuideDialogDismissListener = {
                checkOOBEDialog()
            }
        ).run()
    }

    private fun showBLEOOBEDialog(activity: FragmentActivity, device: Device) {
        Logger.i(TAG, "showBLEOOBEDialog() >>> show [${device.UUID}]")
        bleOOBEDialog = BLEOOBEDialog(
            activity = activity,
            device = device,
            listener = oobeDialogEvents
        ).also { dialog ->
            dialog.show()
            dialog.setOnDismissListener {
                myProductsViewModel?.authed(device)
            }
        }
    }

    private val oobeDialogEvents = object : IOOBEDialogEventListener {
        override fun onDebugCloseAllClick() {
            banAuth = true
            bleOOBEDialog?.dismiss()
        }

        override fun onOOBEFlowEnd(device: PartyBandDevice, isInterrupt: Boolean) {
            super.onOOBEFlowEnd(device, isInterrupt)
            if (!isInterrupt) {
                myProductsViewModel?.authed(device)
            }
        }
    }

    fun onDeviceItemLongClick(bean: BaseProductUIBean<*>?) {
        // do nothing
    }

    fun onDeviceItemClick(bean: BaseProductUIBean<*>?) {
        if (MBUtils.isFastClick()) {// Rapidly clicking the device card will cause the oncreate method of HybridPartyLightUiController to execute multiple times.HOP-27583 [jbl one][3in1][android] app display two same partystage pages
            return
        }

        when (bean) {
            is GeneralProductUIBean,
            is PartyBoxStereoUIBean,
            is OneProductUIBean,
            is OneHarmanCastStereoUIBean,
            is PartyBandUIBean,
            is OneHarmanCastMultiChannelUIBean -> {
                onGeneralProductClick(bean = bean)
            }

            is PartyLightGroupUIBean -> {
                onPartyLightClick(bean = bean)
            }
        }

        foldMiniPlayer()
    }

    private fun onGeneralProductClick(bean: BaseProductUIBean<*>) {
        val device = bean.dataSource as? Device ?: return
        Logger.d(TAG, "onGeneralProductClick() >>> UUID[${device.UUID}] uiStatus[${bean.uiConnectStatus}]")

        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            when (device) {
                is PartyBoxDevice -> onPartyBoxClick(bean = bean, device = device)
                is OneDevice -> onOneDeviceClick(bean = bean, device = device)
                is PartyBandDevice -> onPartyBandDeviceClick(bean = bean, device)
            }
        }
    }

    @MainThread
    private suspend fun onPartyBandDeviceClick(bean: BaseProductUIBean<*>, device: PartyBandDevice) {
        if (EnumUIConnectStatus.OFFLINE == bean.uiConnectStatus) {
            DeviceControlHybridActivity.portalAsOffline(
                activity = requireActivity(),
                device = device
            )

            reportEventOfflineDeviceMainScreen(device = device, isSingleDev = bean.isSingle())
            return
        }
        if (!handleGattConnect(device = device)) {
            return
        }

        if (device.isSolo()) {
            val musician = device.getDevInfoFeat<V5DeviceOOBE>() ?: return
            val isAppFirstUseBandBoxSolo = SPUtils.getInstance().getBoolean("isAppFirstUseBandBoxSolo", true)
            if (musician.isReset) {
                PartyBandChooseMusicianDialog(requireContext(), device).syncShow()
            } else if (isAppFirstUseBandBoxSolo) {
                SPUtils.getInstance().put("isAppFirstUseBandBoxSolo", false)
                PartyBandChooseMusicianDialog(requireContext(), device, musician).syncShow()
            }
        }

        //todo zrz monitor view
        if (BuildConfig.DEBUG) {
            PartyBandDebugFloatingViews.monitor()
        }

        enterDeviceControl(dev = device, isSingleDev = device.isSolo())
    }

    @MainThread
    private suspend fun onPartyBoxClick(bean: BaseProductUIBean<*>, device: PartyBoxDevice) {
        if (EnumUIConnectStatus.OFFLINE == bean.uiConnectStatus) {
            DeviceControlHybridActivity.portalAsOffline(
                activity = requireActivity(),
                device = device
            )

            reportEventOfflineDeviceMainScreen(device = device, isSingleDev = bean.isSingle())
            return
        }

        val protocol = device.getBLEProtocol()
        when (protocol) {
            BluetoothDevice.TRANSPORT_LE -> {
                if (!handleGattConnect(device = device)) {
                    return
                }
            }

            BluetoothDevice.TRANSPORT_BREDR -> {
                if (!device.isBrEdrConnected && !device.syncBrEdrConnectWithTimeout(context = context)) {
                    LogToastUtil.ToastLong(context, "Fail to connect BR/EDR")
                    return
                }
            }
        }

        enterDeviceControl(dev = device, isSingleDev = bean.isSingle())
    }

    @MainThread
    private suspend fun onOneDeviceClick(bean: BaseProductUIBean<*>, device: OneDevice) {
        val status = bean.uiConnectStatus
        Logger.d(TAG, "onOneDeviceClick() >>> device[${device.UUID}] status[$status]， ${device.isSecureBleSupport}")
        when (status) {
            EnumUIConnectStatus.OFFLINE -> {
                DeviceControlHybridActivity.portalAsOffline(
                    activity = requireActivity(),
                    device = device
                )

                reportEventOfflineDeviceMainScreen(device = device, isSingleDev = bean.isSingle())
            }

            EnumUIConnectStatus.READY_TO_CONNECT -> {
                DeviceControlHybridActivity.portalAsReadyToConnect(
                    activity = requireActivity(),
                    device = device
                )

                reportEventDeviceMainScreen(device = device, isSingleDev = bean.isSingle())
            }

            EnumUIConnectStatus.WIFI_ONLINE -> {
                enterDeviceControl(dev = device, isSingleDev = bean.isSingle())
            }

            EnumUIConnectStatus.BLUETOOTH_CONNECTED,
            EnumUIConnectStatus.BLE_CONNECTED -> {
                if (device.isWiFiOnline) {
                    enterDeviceControl(dev = device, isSingleDev = bean.isSingle())
                } else if (device.isSecureBleSupport) {
                    handleSecureGattConnect(device = device, bean = bean)
                } else {
                    if (!handleGattConnect(device = device)) {
                        return
                    }
                    enterDeviceControl(dev = device, isSingleDev = bean.isSingle())
                }
            }
        }
    }

    private fun onPartyLightClick(bean: PartyLightGroupUIBean) {
        lifecycleScope.launch {
            bean.dataSource.forEach {
                if (it.isGattConnected) {
                    enterDeviceControl(dev = it, isSingleDev = false)
                    return@launch
                }
            }
            val devs = bean.dataSource.toMutableList().apply {
                sortByDescending { it.isConnectable() }
            }.toList()

            handlePartyLightsGattConnect(devices = devs)
            return@launch
        }
    }

    private fun enterDeviceControl(dev: Device, isSingleDev: Boolean) {
        lifecycleScope.launch {
            val loadingSuccess = LoadingDevControlResActivity.launch(requireActivity(), dev.pid!!)
            Logger.w(TAG, "device control page resource load loadingSuccess $loadingSuccess")

            if (loadingSuccess) {
                DeviceControlHybridActivity.portalAsOnline(
                    activity = requireActivity(),
                    pid = dev.pid,
                    uuid = dev.UUID
                )
                reportEventDeviceMainScreen(device = dev, isSingleDev = isSingleDev)
            } else {
                //do something when loading failed
                Logger.w(TAG, "device control page resource load failed $dev")
            }
        }
    }

    fun onGrantClick() {
        showPermissionDialog()
    }

    fun turnOnGps() {
        val ctx = activity ?: return
        if (!AppPermissionUtils.isGpsEnabled(ctx.applicationContext)) {
            AppPermissionUtils.enableGps(ctx, AppPermissionUtils.REQUEST_PERMISSION_CODE)
        }
    }

    fun turnOnBluetooth() {
        val ctx = activity ?: return
        if (!AppPermissionUtils.isBluetoothEnabled()) {
            AppPermissionUtils.enableBluetooth(ctx)
        }
    }

    fun onRedirect() {
        currentRedirectDevice.value?.pid?.also {
            myProductsViewModel?.saveRedirectedPid(it)
        }

        context?.also {
            currentRedirectDevice.value?.pid?.let { it1 ->
                RedirectAppActivity.launchRedirectAppActivity(
                    it, activity,
                    it1, currentRedirectDevice.value?.deviceName
                )
            }
        }

        CoroutineScope(DISPATCHER_FAST_MAIN).launch {
            _currentRedirectDevice.value = null
            _isRedirectionLayoutVisible.value = false
        }
    }

    fun onAuraCastEntranceClick() {
        val activity = activity ?: return
        if (!checkPermissionsBTGpsEnable(activity = activity)) {
            return
        }

        AuraCastActivity.portal(context = activity)

        foldMiniPlayer()
    }

    private val mReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            // It means the user has changed his bluetooth state.
            when (intent.action) {
                BluetoothAdapter.ACTION_STATE_CHANGED,
                LocationManager.PROVIDERS_CHANGED_ACTION -> {
                    updateViewStyle()
                }
            }
        }
    }

    private fun addBean(
        groupInfoItem: GetGroupInfo?,
        device: OneDevice?,
        output: MutableList<BaseProductUIBean<*>>
    ) {
        device?.also {
            if (groupInfoItem?.groupInfo?.getGroupType() == GroupType.STEREO) {
                output.add(OneHarmanCastStereoUIBean(it, groupInfoItem))
            } else if (groupInfoItem?.groupInfo?.getGroupType() == GroupType.MULTICHANNEL) {
                output.add(OneHarmanCastMultiChannelUIBean(it, groupInfoItem))
            } else {
                // party mode not implements
            }
        }

    }

    /**
     * Group and map devices into different type of [BaseProductUIBean]
     */
    private suspend fun mapToProductBeans(
        devices: List<Device>?,
        groupInfos: List<GetGroupInfo>? = null
    ): List<BaseProductUIBean<*>> {
        if (devices.isNullOrEmpty()) return emptyList()

        return withContext(DISPATCHER_IO) {
            val output = mutableListOf<BaseProductUIBean<*>>()

            devices.groupBy { device ->
                device.productLine
            }.entries.forEach { (key, devices) ->
                when (key) {
                    EnumProductLine.PARTY_LIGHT -> mapPartyLight(devices = devices, output = output)
                    EnumProductLine.ONE -> mapOne(devices = devices, groupInfos = groupInfos, output = output)
                    EnumProductLine.PARTY_BOX -> mapPartyBox(devices = devices, output = output)
                    EnumProductLine.BAND_BOX -> mapPartyBand(devices, output)

                    else -> {
                        output.addAll(devices.map { device ->
                            GeneralProductUIBean(device = device, isBLEConnecting = bleConnectingDevices.contains(device))
                        })
                    }
                }
            }

            output.toList().sortedWith(productBeanComparator)
        }
    }

    private fun mapPartyBand(devices: List<Device>, output: MutableList<BaseProductUIBean<*>>) {
        devices.filterIsInstance<PartyBandDevice>().forEach {
            output.add(PartyBandUIBean(device = it, isBLEConnecting = bleConnectingDevices.contains(it)))
        }
    }

    private fun mapPartyBox(devices: List<Device>, output: MutableList<BaseProductUIBean<*>>) {
        devices.filterIsInstance<PartyBoxDevice>().forEach { device ->
            if (device.hasValidGroupId()) {
                output.add(PartyBoxStereoUIBean(device))
            } else {
                output.add(GeneralProductUIBean(device = device, isBLEConnecting = bleConnectingDevices.contains(device)))
            }
        }
    }

    private fun mapPartyLight(devices: List<Device>, output: MutableList<BaseProductUIBean<*>>) {
        devices.filterIsInstance<PartyLightDevice>().let { lights ->
            lights.filter { device ->
                "2109" == device.pid
            }.let { sticks ->
                if (sticks.isNotEmpty()) {
                    output.add(
                        PartyLightGroupUIBean(
                            devices = sticks,
                            devCount = sticks.size,
                            title = "JBL PartyLight Stick",
                            isBLEConnecting = sticks.any { stick ->
                                bleConnectingDevices.contains(stick)
                            }
                        )
                    )
                }
            }

            lights.filter { device ->
                "2108" == device.pid
            }.let { beams ->
                if (beams.isNotEmpty()) {
                    output.add(
                        PartyLightGroupUIBean(
                            devices = beams,
                            devCount = beams.size,
                            title = "JBL PartyLight Beam",
                            isBLEConnecting = beams.any { beam ->
                                bleConnectingDevices.contains(beam)
                            }
                        )
                    )
                }
            }
        }
    }

    private fun getOnlineGroupInfo(device: OneDevice?, groupInfos: List<GetGroupInfo>?):GetGroupInfo?{
        var groupInfo = device?.groupInfoExt?.groupInfo
        if(groupInfo != null){
            Logger.d(TAG,"1.0.0 ============= mapOne uuid = ${device?.UUID}  groupInfo = $groupInfo")
            return groupInfo
        }
        groupInfo = groupInfos?.find { groupInfoItem ->
            val member = groupInfoItem.groupInfo?.members?.find { memberItem -> memberItem.crc == device?.UUID }
            member != null && groupInfoItem.isGroup()
        }
//        groupInfo?.also {
//            device?.bleDevice?.groupInfoExt = GroupInfoExt(it)
//            device?.wifiDevice?.groupInfoExt = GroupInfoExt(it)
//        }

        Logger.d(TAG,"1.0.1 ============= mapOne uuid = ${device?.UUID}  groupInfo = $groupInfo")
        return groupInfo
    }

    private suspend fun addOneGroupCard(
        devices: List<Device>,
        groupInfos: List<GetGroupInfo>?,
        output: MutableList<BaseProductUIBean<*>>,
        allGroupInfoOffline: List<GetGroupInfo>?
    ) {
        Logger.d(
            TAG,
            "0 mapOne after distinct size = ${allGroupInfoOffline?.size} allGroupInfoOffline ${allGroupInfoOffline} "
        )
        Logger.d(TAG, "0 mapOne after distinct  onlineGroupInfos ${groupInfos} ")
        allGroupInfoOffline?.forEach { groupInfoItem ->
            val goDevice = devices.filterIsInstance<OneDevice>()
                .find { device -> device?.UUID == groupInfoItem.getGoCrc() }
            var goDeviceGroupInfoOnline = getOnlineGroupInfo(goDevice, groupInfos)

            Logger.d(TAG, "1.1  mapOne uuid = ${goDevice?.UUID} groupInfoItem= ${groupInfoItem} ")
            if (goDeviceGroupInfoOnline?.isGroup() == true) {
                Logger.d(TAG, "1.2 online group mapOne uuid = ${goDevice?.UUID} ")
                addBean(goDeviceGroupInfoOnline, goDevice, output)
            }else if (goDeviceGroupInfoOnline?.isSingle() == true) {
                Logger.d(TAG, "1.2.1 remove offline group by online go mapOne uuid = ${goDevice?.UUID} ")
                CacheRepository.getInstance().removeGroupCache(goDevice?.UUID)
            } else {
                Logger.d(TAG, "1.3 mapOne uuid = ${goDevice?.UUID} ")
                goDevice?.also {
                    val offlineDevice =
                        LocalCacheAdapter.getOfflineDevice(WAApplication.me, it.UUID)
                            as? OneDevice ?: return@forEach
                    Logger.d(TAG, "1.3.1 offline group mapOne uuid = ${goDevice?.UUID} ")
                    addBean(groupInfoItem, offlineDevice, output)
                }
            }
        }
    }

    private fun addOneSingleCard(
        devices: List<Device>,
        groupInfos: List<GetGroupInfo>?,
        output: MutableList<BaseProductUIBean<*>>,
        allGroupInfoOffline: List<GetGroupInfo>?
    ) {
        devices.filterIsInstance<OneDevice>().forEach { oneDevice ->
            oneDevice?.run {
                if (oneDevice.isOffline) {
                    Logger.d(TAG, "2. mapOne uuid = ${oneDevice.UUID}  ")
                    val groupInfoOffline =
                        allGroupInfoOffline?.find { groupInfoItem ->
                            val member = groupInfoItem.groupInfo?.members?.find { memberItem ->
                                memberItem.crc == oneDevice.UUID
                            }
                            member != null
                        }
                    Logger.d(TAG, "3. offline single mapOne uuid = ${oneDevice.UUID} groupInfoOffline = $groupInfoOffline groupInfoOffline.isSingle() = ${groupInfoOffline?.isSingle()}")
                    if (groupInfoOffline == null || groupInfoOffline.isSingle()) {
                        output.add(OneProductUIBean(device = oneDevice, isBLEConnecting = bleConnectingDevices.contains(oneDevice))) // Offline single state
                    }
                } else {
                    var groupInfoOnline = getOnlineGroupInfo(oneDevice, groupInfos)
                    Logger.d(TAG, "4. online single mapOne uuid = ${oneDevice.UUID} role = ${oneDevice.role.roleName} groupInfoOnline = $groupInfoOnline ")
                    if (groupInfoOnline == null || groupInfoOnline.isSingle() || groupInfoOnline.isBeforeGroup()) {
                        Logger.d(TAG, "5. online single mapOne uuid = ${oneDevice.UUID} groupInfoOnline = $groupInfoOnline ")
                        output.add(OneProductUIBean(device = oneDevice, isBLEConnecting = bleConnectingDevices.contains(oneDevice))) // Online single state

                    }else if(oneDevice.role == OneRole.SINGLE){
                        Logger.d(TAG, "6. online single mapOne uuid = ${oneDevice.UUID} role = ${oneDevice.role.roleName} groupInfoOnline = $groupInfoOnline reset gc device from group")
                        output.add(OneProductUIBean(device = oneDevice, isBLEConnecting = bleConnectingDevices.contains(oneDevice))) // Online single state
                    }else{
                        Logger.d(TAG, "7. online  group case 1.3.1 mapOne uuid = ${oneDevice.UUID} role = ${oneDevice.role.roleName} groupInfoOnline = $groupInfoOnline ")
                    }
                }
            }
        }
    }

    @WorkerThread
    private suspend fun mapOne(
        devices: List<Device>,
        groupInfos: List<GetGroupInfo>?,
        output: MutableList<BaseProductUIBean<*>>
    ) {
        var allGroupInfoOffline = CacheRepository.getInstance().getAllGroupInfo()
        Logger.d(
            TAG,
            "0 mapOne size = ${allGroupInfoOffline?.size} allGroupInfoOffline ${allGroupInfoOffline} "
        )
        allGroupInfoOffline = allGroupInfoOffline?.distinctBy { it.groupInfo?.group?.p2pMac }

        addOneGroupCard(devices, groupInfos, output, allGroupInfoOffline)

        //one single processing flow
        addOneSingleCard(devices, groupInfos, output, allGroupInfoOffline)

    }

    override fun onRootLayoutClick(item: AbsPlayerItem) {
        Logger.d(TAG, "onRootLayoutClick() >>> [${item.device}]")
        when (item) {
            is RadioPlayerItem -> {
                RadioActivity.portal(context = activity ?: return, device = item.device as? PartyBoxDevice)
            }
            else -> {
                activity?.portalActivity(FullPlayerActivity::class.java, item.device)
            }
        }

        foldMiniPlayer()
    }

    override fun onPlayPauseBtnClick(item: AbsPlayerItem) {
        Logger.d(TAG, "onPlayPauseBtnClick() >>> [${item.device}]")
        if (item.isTransitioning) {
            Logger.i(TAG, "onPlayPauseBtnClick() >>> device[${item.device.UUID}] block control cause is transitioning")
            return
        }

        miniPlayersViewModel?.playOrPause(item = item)
    }

    override fun onStopMomentClick(item: AbsPlayerItem) {
        Logger.d(TAG, "onStopMomentClick() >>> [${item.device}]")
        miniPlayersViewModel?.cancelSoundScapeV2(item)
    }

    override fun onVolumeBarSeek(item: AbsPlayerItem, progress: Int) {
        Logger.d(TAG, "onVolumeBarSeek() >>> [${item.device}] vol[$progress]")
        volumeSetterStream.tryEmit(SetVolumeEvent(item = item, volume = progress))
    }

    private fun processRedirection() {
        if (_currentRedirectDevice.value != null) {
            return
        }

        val device = myProductsViewModel?.redirectingDevices?.firstOrNull()
        device?.pid?.also {
            myProductsViewModel?.saveRedirectedPid(it)
        }
        myProductsViewModel?.redirectingDevices?.remove(device)

        device?.also { dev ->
            _currentRedirectDevice.value = dev
            _redirectDeviceImgUrl.value = dev.pid?.let {
                AppConfigurationUtils.getModelRenderPath(it, device.cid)
            } ?: ""
            if (_redirectDeviceImgUrl.value?.isNotEmpty() == true && dev.shortName(requireContext())
                    ?.isNotEmpty() == true
            ) {
                _isRedirectionLayoutVisible.value = true
            } else {
                _isRedirectionLayoutVisible.value = false
            }
        } ?: run {
            _isRedirectionLayoutVisible.value = false
        }
    }

    private fun showBluetoothScanFailedDialog() {
        if (true == myProductsViewModel?.bluetoothScanFailed?.value && !showedBluetoothScanFailedDlg.getAndSet(
                true
            )
        ) {
            this.activity?.let {
                val alertDialog: AlertDialog.Builder = AlertDialog.Builder(it)
                alertDialog.setMessage(mContext.getString(R.string.please_restart_your_mobile_device_to_connect_with_product))
                alertDialog.setPositiveButton(
                    R.string.jbl_OK
                ) { dialog, _ ->
                    dialog.dismiss()
                }
                alertDialog.show()
            }
        }
    }

    /**
     * Same with [MiniPlayersViewModel.viewHolderComparator]
     */
    private val productBeanComparator = Comparator<BaseProductUIBean<*>> { o1, o2 ->
        if (o1.uiConnectStatus != o2.uiConnectStatus) {
            return@Comparator if (EnumUIConnectStatus.OFFLINE == o2.uiConnectStatus) -1 else 1
        }

        return@Comparator when {
            o1.firstTouchTime > o2.firstTouchTime -> -1
            o1.firstTouchTime < o2.firstTouchTime -> 1
            else -> 0
        }
    }

    fun onCantFindProductClick() {
        if (!AddNewProductHybridActivity.launchAddProduct(activity)) {
            AppConfigurationUtils.loadAddProductZipFile()
            showToast(getString(R.string.function_is_initializing_please_enter_later))
        }
    }

    override val logTag: String = TAG

    private fun Int?.isMyProductPageShowing(): Boolean = this == HomePagesActivity.TAB_HOME

    private fun isMyProductPageShowing(): Boolean =
        (activity as? HomePagesActivity)?.tab?.value.isMyProductPageShowing()

    @MainThread
    private fun foldMiniPlayer() {
        updateMiniPlayerHeight(
            height = miniPlayersDragTouchListener.minHeight,
            verticalShowingPercent = 0
        )
    }

    @MainThread
    private fun updateMiniPlayerHeight(height: Int, verticalShowingPercent: Int) {
        _miniPlayersLayoutHeight.value = height
        _miniPlayersLayoutHorizontalPadding.value =
            verticalShowingPercent * (context?.let { ScreenUtil.dip2px(it, 8f) } ?: 0) / 100
        _miniPlayerListPanelCoverHeight.value =
            verticalShowingPercent * (context?.let { ScreenUtil.dip2px(it, 20f) } ?: 0) / 100
    }

    private var auracastTipsDialog: AuracastTipsDialog? = null

    @MainThread
    private suspend fun handleGattConnect(device: Device): Boolean {
        Logger.d(TAG, "handleGattConnect() >>> device[${device.UUID}]")
        //this update from Ken
        if (device.shouldBlockGattConnect()) {
            Logger.w(TAG, "handleGattConnect() >>> device[${device.UUID}] pid[${device.pid}] " +
                    "need to quit AuraCast first. " +
                    "isRemoteConfigAutoGattConnect[${device.isRemoteConfigAutoGattConnect()}] " +
                    "isConnectable[${(device as? PartyBoxDevice)?.isConnectable}] " +
                    "isGattConnected[${device.isGattConnected}] " +
                    "isAuraCastOn[${device.isAuraCastOn}]")
            auracastTipsDialog?.dismiss()
            val titleTextID = if (device.pid?.isHorizon3() == true && device.isAuraCastOn) {
                R.string.please_disable_partytogether_on_your_product_to_continue
            } else {
                R.string.restart_the_product_make_sure_it_s_not_connected_to_othermobile_devices
            }
            auracastTipsDialog = AuracastTipsDialog(activity = activity ?: return false, titleTextID = titleTextID).apply {
                show()
            }
            return false
        }

        val vm = myProductsViewModel ?: return false
        vm.cancelDelayGattAutoConnectJob()

        if (device.isGattConnected) {
            Logger.i(TAG, "handleGattConnect() >>> [${device.UUID}] already in gatt connected state.")
            return true
        }

        bleConnectingDevices.addIfAbsent(device)
        val rst = vm.syncGattConnectWithTimeout(device = device)
        bleConnectingDevices.remove(device)

        Logger.i(TAG, "handleGattConnect() >>> [${device.UUID}] rst[$rst]")
        if (!rst && isResumed) {
            CustomToast.showError(textRes = R.string.jbl_Connect_Failed, duration = Toast.LENGTH_LONG)
        }

        return rst
    }

    @MainThread
    private suspend fun handlePartyLightsGattConnect(devices: List<PartyLightDevice>): Boolean {
        val vm = myProductsViewModel ?: return false
        vm.cancelDelayGattAutoConnectJob()

        if (devices.any { device -> device.isGattConnected }) {
            return true
        }

        devices.forEach { device ->
            bleConnectingDevices.addIfAbsent(device)
            val rst = vm.syncGattConnectWithTimeout(device = device)
            bleConnectingDevices.remove(device)

            if (rst) {
                enterDeviceControl(dev = device, isSingleDev = false)
                return true
            }
        }

        LogToastUtil.ToastLong(context, "Fail to connect Gatt")
        return false
    }

    fun onNoInternetClick() {
        updateViewStyle()
    }

    private fun handleSecureGattConnect(device: OneDevice, bean: BaseProductUIBean<*>) {
        //this update from Ken
        if (device.shouldBlockGattConnect()) {
            Logger.w(TAG, "handleSecureGattConnect() >>> device[${device.UUID}] pid[${device.pid}] " +
                    "need to quit AuraCast first. " +
                    "isRemoteConfigAutoGattConnect[${device.isRemoteConfigAutoGattConnect()}] " +
                    "isGattConnected[${device.isGattConnected}] " +
                    "isAuraCastOn[${device.isAuraCastOn}]")
            auracastTipsDialog?.dismiss()
            val titleTextID = if (device.pid?.isHorizon3() == true && device.isAuraCastOn) {
                R.string.please_disable_partytogether_on_your_product_to_continue
            } else {
                R.string.restart_the_product_make_sure_it_s_not_connected_to_othermobile_devices
            }
            auracastTipsDialog = AuracastTipsDialog(activity = activity ?: return, titleTextID = titleTextID).apply {
                show()
            }
            return
        }

        val dialog = SecureBlePairingDialog(activity = requireActivity(), device = device)

        Logger.d(TAG, "handleSecureGattConnect() >>> start to connect gatt with ${device.bleDevice?.bleAddress}")

        CoroutineScope(DISPATCHER_IO).launch {
            device.secureBleGattConnect(context = requireContext(), logTag = TAG, onDisconnected = {
                Logger.w(TAG, "handleSecureGattConnect() >>> fail to connect gatt")
                cancel()
                dialog.dismiss()
                if (isResumed) {
                    CustomToast.showError(textRes = R.string.jbl_Connect_Failed, duration = Toast.LENGTH_LONG)
                }
            }, onPairFailed = {
                Logger.w(TAG, "handleSecureGattConnect() >>> auth PAIRED_FAILED")
                cancel()
                dialog.dismiss()
                if (isResumed) {
                    CustomToast.showError(textRes = R.string.jbl_Connect_Failed, duration = Toast.LENGTH_LONG)
                }
            }, onConnected = {
                Logger.i(TAG, "handleSecureGattConnect() >>> PAIRED or CONNECTED")
                cancel()
                dialog.dismiss()
                enterDeviceControl(dev = device, isSingleDev = bean.isSingle())
            }, onPairing = {
                Logger.w(TAG, "handleSecureGattConnect() >>> auth PAIRING")
                dialog.show()
            }, iConnectReporter = null)
        }

        Logger.d(TAG, "doConnectSecureBle() >>> end")
    }

    private fun BaseProductUIBean<*>.isSingle() = when (this) {
        is PartyBoxStereoUIBean -> false
        is OneHarmanCastStereoUIBean -> false
        is OneHarmanCastMultiChannelUIBean -> false
        else -> true
    }

    private fun reportEventDeviceMainScreen(device: Device, isSingleDev: Boolean) {
        FirebaseEventManager.report(
            eventName = EventUtils.EventName.EVENT_DEVICE_MAIN_SCREEN,
            device = device,
            dimensions = mapOf(
                EventUtils.Dimension.DI_IS_SINGLE_DEVICE to isSingleDev.toString(),
                EventUtils.Dimension.DI_CONNECT_PROTOCOL to if (device.isWiFiOnline) {
                    EventUtils.Dimension.EnumDiConnectProtocol.WIFI.value
                } else if (device.isBrEdrConnected) {
                    EventUtils.Dimension.EnumDiConnectProtocol.BR_EDR.value
                } else {
                    EventUtils.Dimension.EnumDiConnectProtocol.BLE.value
                }
            )
        )
    }

    private fun reportEventOfflineDeviceMainScreen(device: Device, isSingleDev: Boolean) {
        FirebaseEventManager.report(
            eventName = EventUtils.EventName.EVENT_OFFLINE_DEVICE_MAIN_SCREEN,
            device = device,
            dimensions = mapOf(
                EventUtils.Dimension.DI_IS_SINGLE_DEVICE to isSingleDev.toString()
            )
        )
    }

    private data class SetVolumeEvent(val item: AbsPlayerItem, val volume: Int)

    companion object {
        private const val TAG = "MyProductsFragment"

        private const val CANT_FIND_PRODUCT_DISPLAY_JOB_MILLS = 10 * 1000L
    }
}