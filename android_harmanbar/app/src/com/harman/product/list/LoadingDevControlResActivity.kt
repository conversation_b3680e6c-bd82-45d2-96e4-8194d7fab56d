package com.harman.product.list

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityLoadingDevControlResBinding
import com.harman.discover.util.Tools
import com.harman.log.Logger
import com.harman.partylight.util.fitSystemBar
import com.harman.partylight.util.gone
import com.harman.partylight.util.popResult
import com.harman.partylight.util.syncPush
import com.harman.partylight.util.visible
import com.harman.thread.DISPATCHER_IO
import com.harman.webview.HybridTools
import com.harman.widget.AppCompatBaseActivity
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.observer.FailureCode
import com.jbl.one.configuration.observer.LoadObserver
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @Description activity for loading device control page resource
 * <AUTHOR>
 * @Time 2024/10/25
 */
class LoadingDevControlResActivity : AppCompatBaseActivity() {
    private val binding by lazy { ActivityLoadingDevControlResBinding.inflate(layoutInflater) }
    private var isLoadSuccess = false
    private val pid by lazy { intent.getStringExtra(REQ_PID)!! }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fitSystemBar()
        setContentView(binding.root)
        buildView()
        loadDevControlRes()
    }


    override fun finish() {
        Logger.d(TAG, "finish() >>> isLoadSuccess[$isLoadSuccess]")
        popResult(isLoadSuccess)
        super.finish()
    }

    private fun loadDevControlRes() {
        lifecycleScope.launch {
            Logger.i(TAG, "loadDevControlRes() >>> start loading hybrid resource")
            binding.gLoading.visible()
            binding.gRetry.gone()

            val ret = Tools.repeatWithTimeout(timeoutMills = RESOURCE_LOAD_TIMEOUT_MILLS) {
                loadDeviceControlRes(pid)
            } ?: FailureCode.TIMEOUT

            Logger.i(TAG, "loadDevControlRes() >>> ret[$ret]")
            isLoadSuccess = FailureCode.SUCCESS == ret

            if (isLoadSuccess) {
                finish()
            } else {
                binding.gLoading.gone()
                binding.gRetry.visible()
                if (ret == FailureCode.UNAVAILABLE_NETWORK) {
                    binding.ivError.setImageResource(R.drawable.ic_no_internet)
                    binding.tvError.text = getString(R.string.no_internet)
                } else {
                    binding.ivError.setImageResource(R.drawable.ic_speaker_disable)
                    binding.tvError.text = getString(R.string.loading_fail)
                }
            }
        }
    }

    private fun buildView() {
        binding.appbar.ivLeading.setOnClickListener {
            finish()
        }
        binding.btnRetry.setOnClickListener {
            loadDevControlRes()
        }
    }

    private suspend fun loadDeviceControlRes(pid: String): FailureCode {
        val completer = CompletableDeferred<FailureCode>()
        withContext(DISPATCHER_IO) {
            AppConfigurationUtils.loadDeviceControlZipFile(pid = pid, loadObserver = object : LoadObserver {
                override fun onSucceed() {
                    Logger.d(TAG, "onSucceed()")
                    completer.complete(FailureCode.SUCCESS)
                }

                override fun onFailed(code: FailureCode) {
                    Logger.d(TAG, "onFailed(), $code")
                    completer.complete(code)
                }

                override fun onProgress(progress: Float) {
                    Logger.d(TAG, "onProgress(): $progress")
                }
            }, retry = true)
        }

        return completer.await()
    }

    companion object {
        private const val TAG = "LoadingDevControlResActivity"
        private const val REQ_PID = "pid"
        private const val RESOURCE_LOAD_TIMEOUT_MILLS = 30 * 1000L

        suspend fun launch(context: ComponentActivity, pid: String): Boolean {
            if (null != HybridTools.getDashboardUrl(context, pid)) {
                return true
            }
            return context.syncPush<LoadingDevControlResActivity, Boolean>(
                bundleOf(REQ_PID to pid)
            ) ?: false
        }
    }
}