package com.harman.product.list

import android.bluetooth.BluetoothDevice
import android.bluetooth.le.ScanCallback
import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.core.content.edit
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.google.gson.reflect.TypeToken
import com.harman.LifeScanViewModel
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GroupInfo
import com.harman.command.one.bean.Rear
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.asyncBrEdrConnect
import com.harman.connect.disconnectBrEdr
import com.harman.connect.disconnectGatt
import com.harman.connect.isHomeBtCategory
import com.harman.connect.isHorizonCategory
import com.harman.connect.isSupportLightControl
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.discover.DeviceScanner
import com.harman.discover.DeviceStore
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.util.Tools.bleConnectable
import com.harman.discover.util.Tools.contains
import com.harman.discover.util.Tools.plus
import com.harman.discover.util.Tools.removeIfMatch
import com.harman.discover.util.Tools.safeResume
import com.harman.discover.util.Tools.upsertIfMatch
import com.harman.filterVisibleDevice
import com.harman.gattConnectBlockByAuracast
import com.harman.hadAuthed
import com.harman.isJBLOneApp
import com.harman.isNetworkConnectedInBleAdv
import com.harman.isReadyToConnect
import com.harman.isRemoteConfigAutoGattConnect
import com.harman.isRemoteConfigSupportBrEdr
import com.harman.isSoundBar
import com.harman.isWiFiOrBTConnected
import com.harman.log.Logger
import com.harman.multichannel.HarmancastManager
import com.harman.multichannel.repository.CacheRepository
import com.harman.offer
import com.harman.oobe.wifi.LocalCacheAdapter
import com.harman.product.list.MyProductsViewModel.Companion.FULL_DEVICES_SAMPLE_THRESHOLD_MILLS
import com.harman.product.list.MyProductsViewModel.Companion.MAX_BLE_CONNECTION_COUNT
import com.harman.oobe.wifi.LocalCacheAdapter.deleteDeviceCache
import com.harman.oobe.wifi.LocalCacheAdapter.updateCache
import com.harman.product.list.bean.OneDeviceRearsWrapper
import com.harman.remove
import com.harman.supportBLEAuth
import com.harman.supportBattery
import com.harman.supportRearSpeaker
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5LeftSerialNumber
import com.harmanbar.ble.utils.GsonUtil
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.impl.runOnUiThread
import com.jbl.one.configuration.model.CombinationItem
import com.wifiaudio.app.WAApplication
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.LinkedList
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList


/**
 * Created by gerrardzhang on 2024/5/22.
 */
class MyProductsViewModelFactory : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return MyProductsViewModel() as T
    }

}

class MyProductsViewModel : LifeScanViewModel() {

    private val _devices = MutableLiveData<List<Device>?>()
    val devices: LiveData<List<Device>?>
        get() = _devices

    private val _unAuthDevices = MutableLiveData<List<Device>?>()
    val unAuthDevices: LiveData<List<Device>?>
        get() = _unAuthDevices.distinctUntilChanged()

    private val _bluetoothScanFailed = MutableLiveData<Boolean>()
    val bluetoothScanFailed: LiveData<Boolean>
        get() = _bluetoothScanFailed.distinctUntilChanged()

    private val _groupAutoPopupItem = MutableLiveData<CombinationItem>()
    val groupAutoPopupItem: LiveData<CombinationItem>
        get() = _groupAutoPopupItem.distinctUntilChanged()

    /**
     * Need to redirect device list.
     */
    val redirectingDevices = CopyOnWriteArrayList<Device>()

    /**
     * Redirected device list.
     */
    val redirectedDevicesPid = loadRedirectedPid()

    private val _groupInfos = MutableLiveData<List<GetGroupInfo>?>()
    val groupInfos: LiveData<List<GetGroupInfo>?>
        get() = _groupInfos.distinctUntilChanged()

    private val _rearsWrappers = MutableLiveData<List<OneDeviceRearsWrapper>?>()
    val rearsWrappers: LiveData<List<OneDeviceRearsWrapper>?>
        get() = _rearsWrappers

    private val authHandledUUIDs = HashSet<String?>()

    private val oneDeviceListeners = ConcurrentHashMap<String?, IOneDeviceListener>()
    private val partyBoxDeviceListeners = ConcurrentHashMap<String?, IPartyBoxDeviceListener>()
    private val partyBandDeviceListeners = ConcurrentHashMap<String?, IV5GattListener>()

    /**
     * Used for collect and debounce msgs from [IHmDeviceObserver.onDevicesUpdate] to avoid too much msgs.
     */
    private val devicesUpdateFlow =
        MutableSharedFlow<List<Device>>(replay = 1, extraBufferCapacity = 2, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    @Volatile
    private var hasShowGroupAutoPopup = false

    private var delayGattAutoConnectJob: Job? = null

    /**
     * A queue to maintain max BLE connection count in My Products env.
     * Must operate on main thread. !!
     */
    private val bleConnectQueue = LinkedList<Device>()

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        DeviceScanner.registerObserver(observer = hmDeviceObserver)

        viewModelScope.launch {
            // blocked logic
            devicesUpdateFlow
                .flowWithLifecycle(lifecycle = owner.lifecycle)
                .collect { devices ->
                    handleDevicesUpdate(devices = devices)
                }
        }

        delayGattAutoConnectJob = viewModelScope.launch(DISPATCHER_DEFAULT) {
            delayGattAutoConnectJob()
        }

        viewModelScope.launch {
            devicesUpdateFlow
                .flowWithLifecycle(lifecycle = owner.lifecycle)
                .collect { devices ->
                    showGroupAutoPopup(devices = devices)
                }
        }
        devicesUpdateFlow.tryEmit(emptyList())
    }

    @WorkerThread
    private suspend fun mergeOfflineDevices(onlineDevices: List<Device>): List<Device> =
        (onlineDevices.filterVisibleDevice() + LocalCacheAdapter.getOfflineDevices())
            .filter { device ->
                true != _unAuthDevices.value?.contains(device)
            }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        DeviceScanner.unregisterObserver(observer = hmDeviceObserver)
        oneDeviceListeners.clear()
        partyBoxDeviceListeners.clear()
        partyBandDeviceListeners.clear()
    }

    private val hmDeviceObserver = object : IHmDeviceObserver {
        @AnyThread
        override fun onDeviceOnlineOrUpdate(device: Device) {
            Logger.d(TAG, "onDeviceOnlineOrUpdate() >>> $device")
            device.pid?.also {
                if (device.deviceName?.isNotBlank() == true
                    && AppConfigurationUtils.isSupportedRedirection(it)
                    && !redirectedDevicesPid.contains(device.pid)
                    && !(redirectingDevices.map { dev -> dev.pid }.contains(device.pid))
                    && !redirectingDevices.contains(device)
                ) {
                    redirectingDevices.addIfAbsent(device)
                }
            }

            if (!AppConfigurationUtils.isSupportedDevice(device.pid ?: "")) {
                return
            }

            when (device) {
                is OneDevice -> onOneDeviceOnlineOrUpdate(device = device)
                is PartyBoxDevice -> onPartyBoxDeviceOnlineOrUpdate(device = device)
                is PartyBandDevice -> onPartyBandDeviceOnlineOrUpdate(device)
            }
        }

        @AnyThread
        override fun onDeviceOffline(device: Device) {
            Logger.d(TAG, "onDeviceOffline() >>> UUID[${device.UUID}]")
            redirectingDevices.remove(device)

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _devices.remove(device)
                _unAuthDevices.remove(device)

                if (bleConnectQueue.remove(device)) {
                    Logger.d(TAG, "onDeviceOffline() >>> pop device[${device.UUID}] pid[${device.pid}] from BLE connect queue")
                }

                when (device) {
                    is OneDevice -> onOneDeviceOffline(device = device)

                    is PartyBoxDevice -> onPartyBoxDeviceOffline(device = device)

                    is PartyLightDevice -> onPartyLightDeviceOffline(device)

                    is PartyBandDevice -> onPartyBandDeviceOffline(device)
                }
            }
        }

        @AnyThread
        override fun onDevicesUpdate(devices: List<Device>) {
            Logger.d(TAG, "onDevicesUpdate() >>> online devices[${devices.size}]")
            devicesUpdateFlow.tryEmit(devices)
        }

        @AnyThread
        override fun onBluetoothServiceFailed(errorCode: Int) {
            Logger.d(TAG, "onBluetoothServiceFailed() >>> errorCode: $errorCode")
            if (ScanCallback.SCAN_FAILED_APPLICATION_REGISTRATION_FAILED == errorCode) {
                viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                    this@MyProductsViewModel._bluetoothScanFailed.value = true
                }
            }
        }

        @AnyThread
        override fun onDeviceFactoryReset(device: Device) {
            // allow this device trigger oobe again after factory reset.
            Logger.d(TAG, "onDeviceFactoryReset() >>> ${device.UUID}")
            authHandledUUIDs.remove(device.UUID)
        }

        @AnyThread
        override fun onDeviceSppConnect(connectedDevices: List<Device>) {
            Logger.d(TAG, "onDeviceSppConnect() >>> [${connectedDevices.size}]")
            // no impl.
        }

        @AnyThread
        override fun onDeviceSppDisconnect(disconnectedDevices: List<Device>) {
            Logger.d(TAG, "onDeviceSppDisconnect() >>> [${disconnectedDevices.size}]")
            // no impl.
        }
    }

    /**
     * Run under a sample window with interval [FULL_DEVICES_SAMPLE_THRESHOLD_MILLS]
     */
    @MainThread
    private suspend fun handleDevicesUpdate(devices: List<Device>) {
        val visibleDevices = withContext(DISPATCHER_IO) {
            mergeOfflineDevices(onlineDevices = devices)
        }

        Logger.d(TAG, "handleDevicesUpdate() >>> devices[${visibleDevices.size}]")
        this@MyProductsViewModel._devices.value = visibleDevices
        updateGroupInfos()
    }

    private fun onPartyBandDeviceOffline(device: PartyBandDevice) {
        viewModelScope.launch {
            _devices.remove(device)
            device.unregisterDeviceListener(partyBandDeviceListeners[device.UUID])
            partyBandDeviceListeners.remove(device.UUID)
        }
    }

    @AnyThread
    private fun onOneDeviceOnlineOrUpdate(device: OneDevice) {
        bindDeviceListener(device = device)

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            handleUnAuthDevices(device = device)
        }

        handleWiFiConnect(device = device)

        handleBrEdrConnect(device = device)

        if (null == device.featSupportExt && device.ableSendCmd) {
            device.getFeatureSupport()
        }

        if (device.ableSendCmd) {
            device.getGroupInfo()
        }

        if (null == device.groupParameterExt && device.ableSendCmd) {
            device.getGroupParameter()
        }

        if (null == device.deviceInfoExt && device.ableSendCmd) {
            device.getDeviceInfo()
        }

        if (device.supportBattery() && null == device.batteryStatusExt && device.ableSendCmd) {
            //If the device supports battery mode, the battery status is requested
            device.getBatteryStatus()
        }

        if (device.isWiFiOnline && device.isSoundBar() && device.supportRearSpeaker() &&
            null == device.wifiDevice?.rears
        ) {
            //soundbar request rear speaker battery status
            device.getRearSpeakerStatus()
        }
    }

    private fun bindDeviceListener(device: OneDevice) {
        if (oneDeviceListeners.contains(device)) {
            return
        }

        val deviceListener = DelegateOneDeviceListener(device = device)
        device.registerDeviceListener(deviceListener)
        oneDeviceListeners[device.UUID] = deviceListener
    }

    private fun bindDeviceListener(device: PartyBoxDevice) {
        if (partyBoxDeviceListeners.contains(device)) {
            return
        }

        val deviceListener = DelegatePartyBoxDeviceListener(device = device)
        device.registerDeviceListener(deviceListener)
        partyBoxDeviceListeners[device.UUID] = deviceListener
    }

    @MainThread
    private suspend fun showGroupAutoPopup(devices: List<Device>) {
        if (hasShowGroupAutoPopup) {
            return
        }

        delay(5000)
        val onlineDevices =
            devices.filterIsInstance<OneDevice>().filter { item -> item.isWiFiOnline && item.groupInfoExt?.groupInfo?.isSingle() == true }

        val combinationList = HarmancastManager.getInstance().getCombination(onlineDevices)
        if (!combinationList.isNullOrEmpty()) {
            for (combinationItem in combinationList) {
                val hasShowAutoPopup = withContext(DISPATCHER_IO) {
                    CacheRepository.getInstance().getFlag(combinationItem.getAutoPopKey())
                }

                if (!hasShowAutoPopup) {
                    viewModelScope.launch(DISPATCHER_IO) {
                        CacheRepository.getInstance().saveFlag(combinationItem.getAutoPopKey(), true)
                    }

                    hasShowGroupAutoPopup = true
                    _groupAutoPopupItem.postValue(combinationItem)
                }
            }
        }
    }

    /**
     * Assemble [GroupInfo] after one device changed (Online or Offline)
     */
    @MainThread
    private fun updateGroupInfos() {
        val onlineDevices = _devices.value?.filterIsInstance<OneDevice>() ?: emptyList()

        val onlineGroupInfos = onlineDevices.mapNotNull { device ->
            device.groupInfoExt?.groupInfo
        }
        _groupInfos.value = onlineGroupInfos
    }

    @AnyThread
    private fun onPartyBoxDeviceOnlineOrUpdate(device: PartyBoxDevice) {
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                handleUnAuthDevices(device = device)
            }

            handleBrEdrConnect(device = device)
        }
    }

    private fun onPartyBandDeviceOnlineOrUpdate(device: PartyBandDevice) {
        if (null == partyBandDeviceListeners[device.UUID]) {
            partyBandDeviceListeners[device.UUID] = object : IV5GattListener {
                override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
                    when (status) {
                        EnumConnectionStatus.DISCONNECTED -> bleConnectQueue.remove(device)
                        else -> Unit
                    }
                }

                override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
                    super.onDevFeat(devInfoMap, isNotify)
                    viewModelScope.launch {
                        (devInfoMap[V5DevInfoFeatID.FactoryReset])?.also {
                            DeviceStore.factoryReset(device = device)
                            device.deleteDeviceCache(ctx = Utils.getApp())
                            device.disconnectGatt()
                            authHandledUUIDs.remove(device.UUID)
                        }
                        (devInfoMap[V5DevInfoFeatID.LeftSerialNumber] as? V5LeftSerialNumber)?.also {
                            device.updateCache(serialNumber = it.serialNumber)
                        }
                    }
                }
            }.apply {
                device.registerDeviceListener(this)
            }
        }

        if (device.isA2DPConnected) {
            //When BT is connected, it is automatically connected to BLE
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                syncGattConnectWithTimeout(device)
            }
        } else {
            runOnUiThread {
                handleUnAuthDevices(device = device)
            }
        }
    }

    @MainThread
    private fun handleUnAuthDevices(device: Device) {
        val needAuthentication = when (device) {
            is OneDevice -> needAuth(device = device)
            is PartyBoxDevice -> needAuth(device = device)
            is PartyBandDevice -> needAuth(device = device)
            else -> return
        }

        val inPendingList = inPendingAuthList(device = device)
        Logger.i(TAG, "handleUnAuthDevices() >>> device[${device.UUID}] pid[${device.pid}]" +
                "needAuthentication[$needAuthentication] inPendingList[$inPendingList]")

        if (needAuthentication && !inPendingList) {
            _unAuthDevices.offer(device)
        } else if (!needAuthentication) { // remove from _unAuthDevices if exist
            _unAuthDevices.remove(device)
        }
    }

    private fun needAuth(device: OneDevice): Boolean {
        val rst = (!device.isNetworkConnected) &&
                !device.isWiFiOrBTConnected() &&
                !authHandledUUIDs.contains(device.UUID) &&
                !device.hadAuthed() &&
                !banAuth &&
                device.bleConnectable()

        Logger.d(
            TAG, "needAuth() >>> device[${device.UUID}] " +
                    "isNetworkConnected[${device.isNetworkConnected}] " +
                    "WiFiOrBT[${device.isWiFiOrBTConnected()}] " +
                    "authHandledUUIDs.contains[${authHandledUUIDs.contains(device.UUID)}] " +
                    "hadAuthed[${device.hadAuthed()}] " +
                    "banAuth[$banAuth] " +
                    "isBLEConnectable[${device.bleConnectable()}]" +
                    "rst[$rst]"
        )

        return rst
    }

    private fun inPendingAuthList(device: Device): Boolean = _unAuthDevices.contains(device)

    private fun needAuth(device: PartyBoxDevice): Boolean {
        val rst = device.supportBLEAuth() &&
                !authHandledUUIDs.contains(device.UUID) &&
                !device.hadAuthed() &&
                !device.isHotelMode &&
                !banAuth &&
                device.bleConnectable()

        Logger.d(
            TAG, "needAuth() >>> device[${device.UUID}] " +
                    "support BLE Auth[${device.supportBLEAuth()}]" +
                    "authHandledUUIDs.contains[${authHandledUUIDs.contains(device.UUID)}] " +
                    "hadAuthed[${device.hadAuthed()}] " +
                    "hotelMode[${device.isHotelMode}] " +
                    "banAuth[$banAuth] " +
                    "isBLEConnectable[${device.bleConnectable()}]" +
                    "rst[$rst]"
        )
        return rst
    }

    private fun needAuth(device: PartyBandDevice): Boolean {
        val rst = device.supportBLEAuth() &&
                !authHandledUUIDs.contains(device.UUID) &&
                !device.hadAuthed() &&
                device.miscInfo?.isConnectable == true &&
                !banAuth &&
                device.bleConnectable()

        Logger.d(
            TAG, "needAuth() >>> device[${device.UUID}] " +
                    "support BLE Auth[${device.supportBLEAuth()}]" +
                    "miscInfo.isConnectable[${device.miscInfo?.isConnectable}] " +
                    "hadAuthed[${device.hadAuthed()}] " +
                    "banAuth[$banAuth] " +
                    "isBLEConnectable[${device.bleConnectable()}]" +
                    "rst[$rst]"
        )

        return rst
    }

    @MainThread
    fun authed(device: Device) {
        Logger.d(TAG, "authed() >>> [${device.UUID}]")
        _unAuthDevices.remove(device)
        authHandledUUIDs.add(device.UUID)
    }

    private fun onOneDeviceOffline(device: OneDevice) {
        oneDeviceListeners.remove(device.UUID)
        updateGroupInfos()
        _rearsWrappers.removeIfMatch { t ->
            device == t.device
        }
    }

    private fun onPartyBoxDeviceOffline(device: PartyBoxDevice) {
        partyBoxDeviceListeners.remove(device.UUID)
    }

    private fun onPartyLightDeviceOffline(device: PartyLightDevice) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            _devices.remove(device)
        }
    }

    /**
     * Returns a list containing all elements of the original collection and then all elements of the given [infos] collection.
     */
    private operator fun Collection<GroupInfo>?.plus(infos: Iterable<GroupInfo>): List<GroupInfo> {
        this ?: return infos.toList()

        val noDuplicates = infos.filter { info ->
            this.none { source ->
                source.getValidGroupId() == info.getValidGroupId()
            }
        }

        if (noDuplicates.isEmpty()) {
            return this.toList()
        }

        val result = ArrayList<GroupInfo>(this.size + noDuplicates.size)
        result.addAll(this)
        result.addAll(noDuplicates)
        return result
    }

    fun saveRedirectedPid(pid: String) {
        redirectedDevicesPid.add(pid)
        saveRedirectedPid(redirectedDevicesPid)
    }

    @MainThread
    fun removeUnAuthDevice(device: Device?) {
        Logger.d(TAG, "removeUnAuthDevice() >>> remove[${device?.UUID}]")
        device ?: return
        _unAuthDevices.remove(device)
    }

    private fun loadRedirectedPid(): HashSet<String> {
        if (isJBLOneApp()) {
            val keyRedirectedPid = "keyRedirectedPid"
            val context = WAApplication.me
            val pref: SharedPreferences = context.getSharedPreferences(
                context.packageName
                        + javaClass.simpleName + "_preferences_redirected_pids",
                Context.MODE_PRIVATE
            )

            val content = pref.getString(keyRedirectedPid, "")
            if (!content.isNullOrBlank()) {
                runCatching {
                    return GsonUtil.GSON.fromJson(
                        content,
                        object : TypeToken<HashSet<String>>() {}.type
                    )
                }.onFailure { e ->
                    Logger.e(TAG, "", e)
                }
            }
        }
        return HashSet<String>()
    }

    private fun saveRedirectedPid(pid: HashSet<String>?) {
        val keyRedirectedPid = "keyRedirectedPid"
        val context = WAApplication.me
        val sp: SharedPreferences = context.getSharedPreferences(
            context.packageName
                    + javaClass.simpleName + "_preferences_redirected_pids", Context.MODE_PRIVATE
        )

        runCatching {
            pid?.run { sp.edit { putString(keyRedirectedPid, GsonUtil.GSON.toJson(pid)) } }
        }.onFailure { e ->
            Logger.e(TAG, "", e)
        }
    }

    private inner class DelegateOneDeviceListener(
        private val device: OneDevice
    ) : IOneDeviceListener {
        @AnyThread
        override fun onSpeakerRearNotify(rears: List<Rear>) {
            val new = OneDeviceRearsWrapper(device = device, rears = rears)

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _rearsWrappers.upsertIfMatch(new = new) { t ->
                    new == t
                }
            }
        }

        @AnyThread
        override fun onGetGroupInfo(rsp: GetGroupInfoRsp) {
            if (rsp.isGroup()) {
                viewModelScope.launch(DISPATCHER_IO) {
                    CacheRepository.getInstance().saveGroupInfo(device.UUID, rsp)
                }
            }
        }

        @AnyThread
        override fun onGetGroupParameter(rsp: GetGroupParameterRsp) {
            if (rsp.members?.isNotEmpty() == true) {
                viewModelScope.launch(DISPATCHER_IO) {
                    CacheRepository.getInstance().saveGroupParameter(device.UUID, rsp)
                }
            }

        }

        @AnyThread
        override fun onBrEdrStatusChanged(status: EnumConnectionStatus, session: BaseBrEdrSession<*, *, *>) {
            Logger.d(TAG, "onBrEdrStatusChanged() >>> UUID[${device.UUID}] pid[${device.pid}] status[$status]")
            when (status) {
                EnumConnectionStatus.CONNECTED -> {
                    device.getDeviceInfo()
                }

                else -> {
                    // do nothing
                }
            }
        }

        @AnyThread
        override fun onUpnpSetCastGroup(uuid: String, result: GetGroupInfo?) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _groupInfos.postValue(groupInfos.value)
            }
        }

        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            Logger.d(TAG, "onGattStatusChanged() >>> onGattStatusChanged UUID[${device.UUID}] pid[${device.pid}] status[$status]")
            when (status) {
                EnumConnectionStatus.CONNECTED -> {
                    device.getDeviceInfo()
                }

                else -> {
                    // do nothing
                }
            }
        }
    }

    private inner class DelegatePartyBoxDeviceListener(
        private val device: PartyBoxDevice
    ) : IPartyBoxDeviceListener {

        override fun onBrEdrStatusChanged(status: EnumConnectionStatus, session: BaseBrEdrSession<*, *, *>) {
            Logger.d(TAG, "onBrEdrStatusChanged() >>> UUID[${device.UUID}] pid[${device.pid}] status[$status]")
            when (status) {
                EnumConnectionStatus.CONNECTED -> {
                    reqInfosAfterConnected(protocol = BluetoothDevice.TRANSPORT_BREDR)
                }

                else -> Unit
            }
        }

        override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
            Logger.d(TAG, "onGattStatusChanged() >>> UUID[${device.UUID}] pid[${device.pid}] status[$status]")
            when (status) {
                EnumConnectionStatus.CONNECTED -> {
                    reqInfosAfterConnected(protocol = BluetoothDevice.TRANSPORT_LE)
                }

                else -> {
                    // do nothing
                }
            }
        }

        private fun reqInfosAfterConnected(protocol: Int) {
            device.reqDeviceInfo(protocol = protocol)
            device.reqPlayerInfo(protocol = protocol)
            device.reqAdvancedEQ(protocol = protocol)

            when {
                device.isHomeBtCategory() && device.isSupportLightControl() -> {
                    device.getStudioLightInfo(protocol = protocol)
                }
                device.isHorizonCategory() -> {
                    device.reqRadioInfo(
                        protocol = BluetoothDevice.TRANSPORT_LE,
                        cmd = ReqRadioInfoCommand(RadioInfo.RequestType.LastStation)
                    )
                }
            }
        }
    }

    /**
     * Auto connect with PartyBox device which was not BrEdr connected but A2DP connected.
     */
    @AnyThread
    private fun handleBrEdrConnect(device: Device) {
        val context = WAApplication.me ?: return
        if (device.isBrEdrConnected || !device.isA2DPConnected || !device.isRemoteConfigSupportBrEdr()) {
            return
        }

        Logger.i(TAG, "handleBrEdrConnect() >>> try to auto connect BR/EDR with [${device.UUID}] pid[${device.pid}]")
        when (device) {
            is PartyBoxDevice -> bindDeviceListener(device = device)
            is OneDevice -> bindDeviceListener(device = device)
        }

        device.asyncBrEdrConnect(context = context)
    }

    @AnyThread
    private fun handleWiFiConnect(device: Device) {
        if (!device.isWiFiOnline) {
            return
        }

        val inOobe = (device as? OneDevice)?.inOobe?.get()
        Logger.i(TAG, "handleWiFiConnect() >>> inOobe, $inOobe")
        if (true != inOobe) {
            Logger.i(TAG, "handleWiFiConnect() >>> disconnect GATT connect for WiFi online device")
            device.disconnectGatt()
            device.disconnectBrEdr()
        }
    }

    /**
     * Union BLE Gatt connection.
     * Only allow [MAX_BLE_CONNECTION_COUNT] devices to kept BLE connection at the same time.
     * FIFO strategy is worked when new device was required to connect BLE.
     */
    @MainThread
    suspend fun syncGattConnectWithTimeout(device: Device): Boolean {
        val context = WAApplication.me ?: run {
            Logger.w(TAG, "syncGattConnectWithTimeout() >>> missing context")
            return false
        }
        if (device.isGattConnected) {
            return true
        }

        when (device) {
            is PartyBoxDevice -> bindDeviceListener(device = device)
            is OneDevice -> bindDeviceListener(device = device)
            is PartyLightDevice -> Unit
            is PartyBandDevice -> {
                if (device.miscInfo?.isConnectable != true) {
                    return false
                }
            }
        }

        if (bleConnectQueue.size >= MAX_BLE_CONNECTION_COUNT && !bleConnectQueue.contains(device)) {
            val disconnected = bleConnectQueue.poll()
            disconnected.disconnectGatt()
            Logger.d(
                TAG,
                "syncGattConnectWithTimeout() >>> pop device[${disconnected.UUID}] pid[${disconnected.pid}] from bleConnectQueue and disconnect device"
            )
        }

        bleConnectQueue.offer(device)
        Logger.d(
            TAG,
            "syncGattConnectWithTimeout() >>> offer bleConnectQueue device[${device.UUID}] pid[${device.pid}]. queue.size[${bleConnectQueue.size}]"
        )

        Logger.d(TAG, "syncGattConnectWithTimeout() >>> try to auto connect gatt with device[${device.UUID}] pid[${device.pid}]")
        val rst = device.syncGattConnectWithTimeout(context)
        Logger.d(TAG, "syncGattConnectWithTimeout() >>> connect gatt with device[${device.UUID}] pid[${device.pid}] rst[$rst]")

        if (!rst) {
            Logger.d(TAG, "syncGattConnectWithTimeout() >>> pop device[${device.UUID}] pid[${device.pid}] from BLE connect queue")
            bleConnectQueue.remove(device)
        }

        return rst
    }

    /**
     * Cancel [delayGattAutoConnectJob] which launched in [onCreate] cause user launch a manual Gatt connection.
     */
    @MainThread
    fun cancelDelayGattAutoConnectJob() {
        delayGattAutoConnectJob ?: return

        delayGattAutoConnectJob?.cancel()
        delayGattAutoConnectJob = null
        Logger.d(TAG, "cancelDelayGattAutoConnectJob() >>> ")
    }

    @WorkerThread
    private suspend fun delayGattAutoConnectJob() {
        Logger.d(TAG, "delayGattAutoConnectJob() >>> start timer")
        delay(DELAY_GATT_AUTO_CONNECT_JOB_MILLS)
        Logger.d(TAG, "delayGattAutoConnectJob() >>> timeout, start to filter")

        val autoConnectDevices = devices.value
            ?.filter { device ->
                device.needAutoGattConnect()
            }
            ?.sortedWith(gattAutoConnectComparator)
            ?.take(2)

        if (!autoConnectDevices.isNullOrEmpty()) {
            Logger.d(
                TAG, "delayGattAutoConnectJob() >>> select device " +
                        "[0][${autoConnectDevices.getOrNull(0)?.UUID}] " +
                        "[1][${autoConnectDevices.getOrNull(1)?.UUID}] "
            )

            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                autoConnectDevices.forEach { device ->
                    syncGattConnectWithTimeout(device = device)
                }
            }
            return
        }

        Logger.d(TAG, "delayGattAutoConnectJob() >>> no device available, wait for first available online")
        val device = waitForFirstAutoGattDeviceOnline()
        Logger.d(TAG, "delayGattAutoConnectJob() >>> select online device[${device.UUID}]")
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            syncGattConnectWithTimeout(device = device)
        }
    }

    private suspend fun waitForFirstAutoGattDeviceOnline(): Device =
        suspendCancellableCoroutine { continuation ->
            val observer = object : IHmDeviceObserver {
                override fun onDeviceOnlineOrUpdate(device: Device) {
                    if (device.needAutoGattConnect()) {
                        DeviceScanner.unregisterObserver(this)
                        continuation.safeResume(device)
                    }
                }
            }

            DeviceScanner.registerObserver(observer)
        }

    private fun Device.needAutoGattConnect(): Boolean =
        isBLEOnline &&
                !isGattConnected &&
                !isWiFiOnline &&
                !isReadyToConnect() &&
                isRemoteConfigAutoGattConnect() &&
                hadAuthed() &&
                !isNetworkConnectedInBleAdv() &&
                !gattConnectBlockByAuracast()

    private val gattAutoConnectComparator = Comparator<Device> { o1, o2 ->
        if (o1.isA2DPConnected xor o2.isA2DPConnected) {
            return@Comparator if (o1.isA2DPConnected) -1 else 1
        }

        return@Comparator if (o1.firstTouchTime < o2.firstTouchTime) -1 else 1
    }

    override val lifeScanVMLogTag: String = TAG

    companion object {
        private const val TAG = "MyProductsViewModel"

        var banAuth = false

        private const val FULL_DEVICES_SAMPLE_THRESHOLD_MILLS = 1 * 1000L

        private const val DELAY_GATT_AUTO_CONNECT_JOB_MILLS = 10 * 1000L

        private const val MAX_BLE_CONNECTION_COUNT = 2
    }
}