package com.harman.product.rename

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.harman.command.one.bean.DeviceNameRspUnion
import com.harman.connect.isHomeBtCategory
import com.harman.connect.isStereoGrouped
import com.harman.connect.syncGetDeviceNameWithTimeout
import com.harman.connect.toDisplayName
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools.truncateSafety
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.v5protocol.bean.devinfofeat.V5DeviceName

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/5.
 */
class RenameDeviceViewModelFactory(private val device: Device) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return RenameDeviceViewModel(device = device) as T
    }
}

class RenameDeviceViewModel(
    private val device: Device
) : ViewModel() {

    suspend fun getDeviceName(): DeviceNameRspUnion? = when (device) {
        is OneDevice -> device.syncGetDeviceNameWithTimeout(logTag = TAG)
            ?: DeviceNameRspUnion(
                name = device.deviceName,
                editable = true
            )

        is PartyBoxDevice -> DeviceNameRspUnion(
            name = device.toDisplayName(),
            editable = true
        )

        is PartyBandDevice -> {
            device.getDevInfoFeat<V5DeviceName>()?.let {
                DeviceNameRspUnion(it.name, true)
            }
        }

        else -> null
    }

    /**
     * Don't need to wait for result
     */
    fun setDeviceName(name: String) {
        val truncateName = name.truncateSafety(maxNameByteLength())
        when (device) {
            is OneDevice -> {
                Logger.i(TAG, "setDeviceName() >>> one platform, name after truncate: $truncateName")
                if (device.groupInfoExt?.groupInfo?.isGroup() == true ) {
                    device.renameGroup(name = truncateName)
                } else {
                    device.sendSetDeviceName(name = truncateName)
                }

            }

            is PartyBoxDevice -> if (device.isStereoGrouped()) {
                Logger.d(TAG, "setDeviceName() >>> partybox platform, rename group. name after truncate: $truncateName")
                device.gen3RenameStereo(protocol = device.getBLEProtocol(), groupName = truncateName)
            } else {
                Logger.d(TAG, "setDeviceName() >>> partybox platform, rename single. name after truncate: $truncateName")
                device.setRemoteDeviceName(protocol = device.getBLEProtocol(), name = truncateName)
            }

            is PartyBandDevice -> {
                device.asyncSetDevInfoFeat(V5DeviceName(truncateName))
            }
        }
    }

    /**
     * new adapter for studio device,such ss5/as5 should return 26
     * others return 28 or
     */
    fun maxNameByteLength(): Int {
        val maxByteLength = when (device) {
            is OneDevice -> MAX_ONE_DEVICE_RENAME_BYTE_LENGTH
            is PartyBoxDevice -> if (device.isHomeBtCategory()) MAX_STUDIO_DEVICE_NAME_BYTE_LENGTH else MAX_DEVICE_NAME_BYTE_LENGTH
            is PartyBandDevice -> MAX_BAND_BOX_DEVICE_RENAME_BYTE_LENGTH
            else -> MAX_DEVICE_NAME_BYTE_LENGTH
        }
        Logger.d(TAG, "setDeviceName() >>> maxNameByteLength: $maxByteLength")
        return maxByteLength
    }

    companion object {
        private const val TAG = "RenameDeviceViewModel"

        private const val MAX_ONE_DEVICE_RENAME_BYTE_LENGTH = 26
        private const val MAX_DEVICE_NAME_BYTE_LENGTH = 28
        private const val MAX_STUDIO_DEVICE_NAME_BYTE_LENGTH = 26
        private const val MAX_BAND_BOX_DEVICE_RENAME_BYTE_LENGTH = 29
    }

}