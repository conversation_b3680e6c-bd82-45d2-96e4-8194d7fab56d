package com.harman.product.setting.horizon

import android.content.Context
import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.harman.command.partybox.bean.AutoStandbyInfo
import com.harman.command.partybox.gatt.SetAutoStandbyTimerCommand
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.syncFactoryResetWithTimeout
import com.harman.connect.syncRestoreFactoryWithTimeout
import com.harman.connect.syncGetAutoStandbyInfoWithTimeout
import com.harman.connect.syncFactoryResetWithTimeout
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.util.Tools.isHorizon3
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.oobe.wifi.LocalCacheAdapter.deleteDeviceCache
import com.harman.product.rename.RenameDeviceActivity
import com.harman.product.setting.ProductSettingViewModel
import com.harman.product.setting.activity.BaseProductSettingsActivity
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.v5protocol.bean.devinfofeat.V5FactoryReset
import com.harman.webview.DeviceControlHybridActivity
import kotlinx.coroutines.android.awaitFrame
import com.harman.thread.DISPATCHER_DEFAULT
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *
 * @ProjectName: OneApp
 * @Package: com.harman.product.setting.horizon3
 * @ClassName: Horizon3ProductSettingsViewModel
 * @Description:
 * @Author: mixie
 * @CreateDate: 2025/3/11 15:57
 * @UpdateUser:
 * @UpdateDate: 2025/3/11 15:57
 * @UpdateRemark:
 * @Version: 1.0
 */
class Horizon3ProductSettingsViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {


    companion object {
        const val TAG = "Horizon3ProductSettingsViewModel"
    }

    private val device = DeviceStore.find(savedStateHandle.get<String>("UUID")!!) as? PartyBoxDevice


    private val _enable = MutableStateFlow<Boolean>(true)
    private val _feedbackTone = MutableStateFlow<Boolean>(false)
    private val _supportFeedbackTone = MutableStateFlow<Boolean>(false)
    private val _addSqCard = MutableStateFlow<Boolean>(false)

    private val _supportAuracastSq = MutableStateFlow<Boolean>(false)
    val supportAuracastSq = _supportAuracastSq.asStateFlow()

    private val _fullscreenLoading = MutableLiveData<Boolean>()
    val fullscreenLoading: LiveData<Boolean>
        get() = _fullscreenLoading

    private val _autoStandbyInfoLiveData = MutableLiveData<AutoStandbyInfo?>()
    val autoStandbyInfoLiveData: LiveData<AutoStandbyInfo?> = _autoStandbyInfoLiveData

    val screenData: StateFlow<ScreenData> =
        combine(
            _enable,
            _feedbackTone,
            _addSqCard,
            _supportFeedbackTone
        ) { enable, feedbackTone, addSqCard, supportFeedbackTone ->
            ScreenData.Success(
                enable = enable,
                feedbackTone = feedbackTone,
                addSqCard = addSqCard,
                supportFeedbackTone = supportFeedbackTone,
            )
        }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), ScreenData.Loading)

    private val listener = object : IPartyBoxDeviceListener {
        override fun onFeedbackTone() {
            _feedbackTone.value = device?.isFeedbackToneOn ?: false
            updateSupportFeature()
        }

        override fun onDeviceFeatureUpdate() {
            Logger.d(TAG, "onDeviceFeatureUpdate=${device?.deviceFeature}")
            updateSupportFeature()
        }
    }

    init {
        Logger.d(
            TAG,
            "init start device=${device} device?.isFeedbackToneOn=${device?.isFeedbackToneOn}"
        )
        _feedbackTone.value = device?.isFeedbackToneOn ?: false
        updateSupportFeature()
        device?.registerDeviceListener(listener)
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            device?.reqFeedbackTone(device.getBLEProtocol())
            // for ss5,sending this command will block message queue,fix bug HKSS5-1747 [Android APP]It will take 2~3s to factory reset via HK ONE app
            if(true == device?.pid?.isHorizon3()){
                device?.reqScreenDisplayInfo(device.getBLEProtocol())
                device?.reqDeviceFeatureInfo(device.getBLEProtocol())
            }
            //request auto stand by
            if (true == device?.deviceFeature?.supportAutoStandby) {
              //post device cache
                (device.bleDevice)?.autoStandbyInfo?.let { _autoStandbyInfoLiveData.postValue(it) }
                // get stand by timer
                getAutoStandbyInfo()
            }
        }
        Logger.d(TAG, "init end")

    }

    private fun updateSupportFeature() {
        _feedbackTone.value = device?.isFeedbackToneOn ?: false
        _supportAuracastSq.value = device?.deviceFeature?.supportAuracastSq ?: false
        _supportFeedbackTone.value = device?.deviceFeature?.supportFeedbackTone ?: false
        Logger.d(TAG,"feedbackTone=${_feedbackTone.value} supportAuracastSq=${_supportAuracastSq.value} " +
                " supportFeedbackTone=${_supportFeedbackTone.value}")
    }

    private fun setFeedbackTone(isOn: Boolean) {
        Logger.d(TAG, "setFeedbackTone=${isOn}")
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            device?.setFeedbackTone(protocol = device.getBLEProtocol(), isOn = isOn)
        }
    }

    private fun restore() {
        Logger.d(TAG, "restore")
        viewModelScope.launch(DISPATCHER_DEFAULT) {

        }
    }

    @MainThread
    suspend fun restoreFactory(ctx: Context): Boolean? {
        val result = withContext(DISPATCHER_DEFAULT) {
            device?.syncFactoryResetWithTimeout(logTag = TAG, protocol = device.getBLEProtocol())
        }
        Logger.d(TAG, "restoreFactory() >>> result[$result]")

        // remove from LinkPlay SDK to make sure it wont be callback before OOBE again
        // almost useless cause it will appear again after remove.
        // let DeviceStore handle it.
        // remove Wifi instance in DeviceStore and notify
        DeviceStore.factoryReset(device = device as Device)
        device.deleteDeviceCache(ctx = ctx)
        return result
    }

    fun handEvent(event: Event) {
        Logger.d(TAG, "handEvent(${event})")
        when (event) {
            is Event.SwitchFeedbackTone -> {
                setFeedbackTone(event.isOn)
            }

            is Event.RestoreEvent -> {
                restore()
            }

            is Event.AddSqCardEvent -> {
                _addSqCard.value = true
            }

            else -> {}
        }
    }

    override fun onCleared() {
        super.onCleared()
        device?.unregisterDeviceListener(listener)
        Logger.d(TAG, "onCleared")
    }

    fun setAutoStandbyTimer(value: Int) {
        Logger.d(TAG, "setAutoStandby value:$value")
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            device?.sendCommand(protocol = device.getBLEProtocol(), SetAutoStandbyTimerCommand(value = value))
        }
    }

    fun getAutoStandbyInfo(){
        Logger.d(TAG, "getAutoStandbyInfo")
        viewModelScope.launch(DISPATCHER_DEFAULT) {
            device?.syncGetAutoStandbyInfoWithTimeout(logTag = TAG, protocol = device.getBLEProtocol())
            (device?.bleDevice)?.autoStandbyInfo?.let { _autoStandbyInfoLiveData.postValue(it) }
        }
    }

    sealed interface ScreenData {
        data object Loading : ScreenData
        data class Success(
            val enable: Boolean = true,
            val feedbackTone: Boolean = false,
            val addSqCard: Boolean = false,
            val supportFeedbackTone: Boolean = false,
        ) : ScreenData
    }

    sealed interface Event {
        data class SwitchFeedbackTone(val isOn: Boolean) : Event
        data object RestoreEvent : Event
        data object AddSqCardEvent : Event
    }


}