package com.harman.product.setting

import android.content.Context
import androidx.annotation.IntRange
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumDeepSleepConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GroupDevicesChangeItem
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.connect.disconnectGatt
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.syncGetAutoPowerOffTimerWithTimeout
import com.harman.connect.syncGetFeatureSupportWithTimeout
import com.harman.connect.syncRestoreFactoryWithTimeout
import com.harman.oobe.wifi.LocalCacheAdapter.deleteDeviceCache
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.util.Tools.isEthernet
import com.harman.log.Logger
import com.harman.nightlistening.NightListeningActivity
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.v5protocol.bean.devinfofeat.V5BackwardScreen
import com.harman.v5protocol.bean.devinfofeat.V5FactoryReset
import com.harman.v5protocol.bean.devinfofeat.V5FeedbackToneStatus
import com.wifiaudio.utils.WifiResultsUtil
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by gerrardzhang on 2024/7/24.
 */
class ProductSettingViewModelFactory(
    private val device: Device,
    private val port: String?
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return ProductSettingViewModel(device = device, port = port) as T
    }
}

open class ProductSettingViewModel(
    private val device: Device,
    private val port: String?
) : ViewModel(), DefaultLifecycleObserver {

    private val _autoPowerOffTimerSecs = MutableLiveData<Int>()
    val autoPowerOffTimerSecs: LiveData<Int>
        get() = _autoPowerOffTimerSecs

    private val _feedbackToneConfig = MutableLiveData<EnumFeedbackToneConfig>()
    val feedbackToneConfig: LiveData<EnumFeedbackToneConfig>
        get() = _feedbackToneConfig

    private val _deepSleepConfig = MutableLiveData<EnumDeepSleepConfig>()
    val deepSleepConfig: LiveData<EnumDeepSleepConfig>
        get() = _deepSleepConfig

    private val _backwardScreenOn = MutableLiveData<Boolean>()
    val backwardScreenOn: LiveData<Boolean>
        get() = _backwardScreenOn

    private val _batterySavingStatus = MutableLiveData<EnumBatterySavingStatus>()
    val batterySavingStatus: LiveData<EnumBatterySavingStatus>
        get() = _batterySavingStatus

    private val _wifiName = MutableLiveData<String?>(if (device.wifiDevice?.deviceItem?.isEthernet() == false) {
        (device as? OneDevice)?.eSSID
    } else {
        ""
    })
    val wifiName: LiveData<String?>
        get() = _wifiName

    private val _smartModeOn = MutableLiveData<Boolean>()
    val smartModeOn: LiveData<Boolean>
        get() = _smartModeOn

    @IntRange(from = 0L, to = 100L)
    private val _wifiStrengthPercentage = MutableLiveData<Int>(
        if (device.isWiFiOnline && device.wifiDevice?.deviceItem?.isEthernet() == false) {
            WifiResultsUtil.getRSSIDB(device.wifiDevice?.deviceItem?.devStatus?.rssi ?: "")
        } else {
            0
        }
    )
    val wifiStrengthPercentage: LiveData<Int>
        get() = _wifiStrengthPercentage

    init {
        (device as? OneDevice)?.feedbackToneConfigExt?.config?.let { config ->
            _feedbackToneConfig.value = config
        }

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            (device as? OneDevice)?.getProSetting(port)?.let { prodSettings ->
                _deepSleepConfig.value =
                    if (prodSettings.isDeepSleepOn) EnumDeepSleepConfig.ON else EnumDeepSleepConfig.OFF
            }
        }

        (device as? PartyBandDevice)?.let {
            viewModelScope.launch {
                device.getDevInfoFeat2<V5FeedbackToneStatus, V5BackwardScreen>().also {
                    _feedbackToneConfig.value = EnumFeedbackToneConfig.fromBoolean(it?.first?.enable ?: false)
                    _backwardScreenOn.value = it?.second?.enable ?: false
                }
            }
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        (device as? OneDevice)?.also {
            it.registerDeviceListener(oneDeviceListener)
            it.getSmartMode(port)
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        device.unregisterDeviceListener(oneDeviceListener)
    }

    fun isGoogleVAEnabled(): Boolean {
        return (device as? OneDevice)?.isChromeCastEnabled == true
    }

    fun isAlexaVAEnabled(): Boolean {
        return (device as? OneDevice)?.isAlexaEnable(supportAlexaVA = (device as? OneDevice)?.deviceSupportAlexaVA() ?: false) == true
    }

    @MainThread
    suspend fun restoreFactory(ctx: Context): Boolean {
        val result = withContext(DISPATCHER_DEFAULT) {
            when (device) {
                is OneDevice -> device.syncRestoreFactoryWithTimeout(logTag = TAG)
                is PartyBandDevice -> {
                    val ret = device.setDevInfoFeat(V5FactoryReset())
                    return@withContext ret?.isSuccess() ?: false
                }

                else -> false
            }
        }
        Logger.d(TAG, "restoreFactory() >>> result[$result]")

        // remove from LinkPlay SDK to make sure it wont be callback before OOBE again
        // almost useless cause it will appear again after remove.
        // let DeviceStore handle it.
        // remove Wifi instance in DeviceStore and notify
        DeviceStore.factoryReset(device = device)
        device.deleteDeviceCache(ctx = ctx)
        (device as? PartyBandDevice)?.also {
            it.disconnectGatt()
        }

        return result
    }

    /**
     * Update [_autoPowerOffTimerSecs] by [secs] directly if pass it, or call getAutoPowerOffTimer api.
     */
    @MainThread
    fun updateAutoPowerOffTimerSecs(secs: Int?) {
        secs?.let {
            _autoPowerOffTimerSecs.value = it
            return
        }

        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            val result = withContext(DISPATCHER_DEFAULT) {
                (device as? OneDevice)?.syncGetAutoPowerOffTimerWithTimeout(logTag = TAG, port)
            }

            Logger.i(TAG, "updateAutoPowerOffTimerSecs() >>> result[$result]")
            result?.let {
                _autoPowerOffTimerSecs.value = it
            }
        }
    }

    suspend fun syncGetFeatureSupport(logTag: String): FeatureSupport?{
        return withContext(DISPATCHER_DEFAULT) {
            (device as? OneDevice)?.syncGetFeatureSupportWithTimeout(logTag = logTag, port = port)
        }
    }

    @MainThread
    fun refreshFeedbackToneConfig() {
        (device as? OneDevice)?.getFeedbackToneConfig(port)
    }

    @MainThread
    fun refreshDeepSleepConfig() {
        viewModelScope.launch(DISPATCHER_IO) {
            (device as? OneDevice)?.getProSetting(port)
        }
    }

    @MainThread
    fun setFeedbackToneConfig(config: EnumFeedbackToneConfig) {
        (device as? OneDevice)?.setFeedbackToneConfig(config = config, port)
        (device as? PartyBandDevice)?.asyncSetDevInfoFeat(V5FeedbackToneStatus(config.isOn()))
        _feedbackToneConfig.value = config
    }

    @MainThread
    fun setDeepSleepConfig(config: EnumDeepSleepConfig) {
        viewModelScope.launch(DISPATCHER_FAST_MAIN) {
            (device as? OneDevice)?.setProSetting(deepSleep = if (config.isOn()) ProdSettingResponse.ON else ProdSettingResponse.OFF, port = port)
            _deepSleepConfig.value = config
        }
    }

    @MainThread
    fun switchBackwardScreen() {
        val targetValue = _backwardScreenOn.value ?: false
        (device as? PartyBandDevice)?.asyncSetDevInfoFeat(V5BackwardScreen(targetValue))
        _backwardScreenOn.value = targetValue
    }

    @MainThread
    fun switchDeepSleepConfig() {
        val target = when (_deepSleepConfig.value) {
            EnumDeepSleepConfig.OFF -> EnumDeepSleepConfig.ON
            EnumDeepSleepConfig.ON -> EnumDeepSleepConfig.OFF
            else -> EnumDeepSleepConfig.ON
        }
        setDeepSleepConfig(config = target)
    }

    @MainThread
    fun switchFeedbackToneConfig() {
        val target = when (_feedbackToneConfig.value) {
            EnumFeedbackToneConfig.OFF -> EnumFeedbackToneConfig.ON
            EnumFeedbackToneConfig.ON -> EnumFeedbackToneConfig.OFF
            else -> EnumFeedbackToneConfig.ON
        }

        setFeedbackToneConfig(config = target)
    }

    @MainThread
    fun switchSmartMode() {
        val current = _smartModeOn.value ?: false
        Logger.d(TAG, "switchSmartMode() >>> from[$current] to [${!current}]")
        (device as? OneDevice)?.setSmartMode(!current, port)
    }

    @MainThread
    fun refreshBatterySavingStatus() {
        (device as? OneDevice)?.getBatterySavingStatus(port)
    }

    @MainThread
    fun setBatterySavingStatus(status: EnumBatterySavingStatus) {
        (device as? OneDevice)?.setBatterySavingStatus(status = status, port)
        _batterySavingStatus.value = status
    }

    @MainThread
    fun switchBatterySavingStatue() {
        val target = when (_batterySavingStatus.value) {
            EnumBatterySavingStatus.OFF -> EnumBatterySavingStatus.ON
            EnumBatterySavingStatus.ON -> EnumBatterySavingStatus.OFF
            else -> EnumBatterySavingStatus.ON
        }

        setBatterySavingStatus(status = target)
    }

    private val oneDeviceListener = object : IOneDeviceListener {

        override fun onUpnpNotifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {
            val mainDev = device as? OneDevice ?: return
            Logger.d(NightListeningActivity.Companion.TAG, "onUpnpNotifyGroupDevicesChange() >>> [${mainDev?.UUID}] [$uuid] [${mainDev?.wifiDevice?.deviceItem?.uuid}] $result")
            super.onUpnpNotifyGroupDevicesChange(uuid, result)
            if(uuid == mainDev?.wifiDevice?.deviceItem?.uuid){
                viewModelScope.launch {
                    result?.payload?.prodSetting?.isDeepSleepOn?.let{
                        _deepSleepConfig.value =
                            if (it) EnumDeepSleepConfig.ON else EnumDeepSleepConfig.OFF
                    }
                }
            }
        }
        @WorkerThread
        override fun onFeedbackToneConfig(config: EnumFeedbackToneConfig) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _feedbackToneConfig.value = config
            }
        }

        @WorkerThread
        override fun onBatterySavingStatus(status: EnumBatterySavingStatus) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _batterySavingStatus.value = status
            }
        }

        @WorkerThread
        override fun onGetSmartMode(isOn: Boolean) {
            viewModelScope.launch(DISPATCHER_FAST_MAIN) {
                _smartModeOn.value = isOn
            }
        }
    }

    companion object {
        private const val TAG = "ProductSettingViewModel"
    }
}