package com.harman.product.setting

import android.content.Context
import androidx.annotation.IntRange
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumDeepSleepConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.connect.disconnectGatt
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.syncGetAutoPowerOffTimerWithTimeout
import com.harman.connect.syncGetFeedbackToneConfigWithTimeout
import com.harman.connect.syncRestoreFactoryWithTimeout
import com.harman.oobe.wifi.LocalCacheAdapter.deleteDeviceCache
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.util.Tools.isEthernet
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harman.v5protocol.bean.devinfofeat.V5BackwardScreen
import com.harman.v5protocol.bean.devinfofeat.V5FactoryReset
import com.harman.v5protocol.bean.devinfofeat.V5FeedbackToneStatus
import com.wifiaudio.utils.WifiResultsUtil
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by sky on
 */
class OneCommanderGroupProductSettingViewModelFactory(
    private val device: Device,
    private val port: String?
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OneCommanderGroupProductSettingViewModel(device = device, port = port) as T
    }
}

class OneCommanderGroupProductSettingViewModel(
    private val device: Device,
    private val port: String?
) : ProductSettingViewModel(device,port) {

    companion object {
        private const val TAG = "ProductSettingViewModel"
    }
}