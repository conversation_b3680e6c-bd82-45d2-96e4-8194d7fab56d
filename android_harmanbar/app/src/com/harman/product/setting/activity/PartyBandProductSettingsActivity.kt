package com.harman.product.setting.activity

import com.harman.product.setting.EnumListItem
import com.harman.product.setting.EnumListItemType
import com.harman.product.setting.ProductSettingGroup
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice

class PartyBandProductSettingsActivity : BaseProductSettingsActivity() {
    override fun getWiFiGroups(device: OneDevice): List<ProductSettingGroup> = listOf()
    override fun getBTGroups(device: Device): List<ProductSettingGroup> {
        val groups = mutableListOf<ProductSettingGroup>()
        groups.add(ProductSettingGroup(EnumListItemType.GENERAL_SETTING, generalSettingsForJBL()))
        if ((device as PartyBandDevice).bleDevice?.supportAuracastBroadcastQuality == true && showAuraCastQuality) {
            groups.add(ProductSettingGroup(EnumListItemType.AURACAST_BROADCAST_QUALITY, listOf(EnumListItem.AURACAST_QUALITY)))
        }
        groups.add(ProductSettingGroup(EnumListItemType.RESET, listOf(EnumListItem.R_RESTORE_FACTORY_SETTINGS)))
        return groups.toList()
    }

    private fun generalSettingsForJBL(): List<EnumListItem> {
        val ret = mutableListOf(
            EnumListItem.GS_RENAME,
            EnumListItem.GS_FEEDBACK_TONES,
        )
        val device = device.value as PartyBandDevice
        if (device.isTrio()) {
            ret.add(EnumListItem.GS_BACKWARD_SCREEN)
        }
        return ret
    }
}