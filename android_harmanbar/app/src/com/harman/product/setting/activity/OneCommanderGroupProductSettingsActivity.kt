package com.harman.product.setting.activity

import androidx.lifecycle.ViewModelProvider
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools.getBarPort
import com.harman.isAuraCastQualitySupport
import com.harman.product.setting.EnumListItem
import com.harman.product.setting.EnumListItemType
import com.harman.product.setting.OneCommanderGroupProductSettingViewModel
import com.harman.product.setting.OneCommanderGroupProductSettingViewModelFactory
import com.harman.product.setting.ProductSettingGroup
import com.harman.product.setting.ProductSettingViewModel
import com.harman.supportDeepSleep


/**
 * Created by sky on 2024/7/23.
 *
 * Product Settings page for one commander group.
 */
class OneCommanderGroupProductSettingsActivity : OneBarProductSettingsActivity() {

     override fun genViewModel(device: Device): ProductSettingViewModel{
        return ViewModelProvider(
            this@OneCommanderGroupProductSettingsActivity,
            OneCommanderGroupProductSettingViewModelFactory(device = device, port = (device as? OneDevice)?.getBarPort())
        )[OneCommanderGroupProductSettingViewModel::class.java]
    }

    override fun getWiFiGroups(device: OneDevice): List<ProductSettingGroup> {
        val groups = mutableListOf<ProductSettingGroup>()
        groups.add(
            ProductSettingGroup(
                EnumListItemType.GENERAL_SETTING,
                assembleGeneralSetting(device = device)
            )
        )

        if (device.isAuraCastQualitySupport() && showAuraCastQuality) {
            groups.add(ProductSettingGroup(EnumListItemType.AURACAST_BROADCAST_QUALITY, listOf(EnumListItem.AURACAST_QUALITY)))
        }

        groups.add(
            ProductSettingGroup(
                EnumListItemType.NETWORK,
                listOf(
                    EnumListItem.N_WI_FI,
                    EnumListItem.N_WI_FI_STRENGTH
                )
            )
        )

        super.decoResetItem(device = device, groups = groups)

        return groups.toList()
    }

     override fun assembleGeneralSetting(device: OneDevice): List<EnumListItem> {
        val items = mutableListOf<EnumListItem>()

        items.add(EnumListItem.GS_RENAME)

        if (device.supportIRLearning) {
            items.add(EnumListItem.GS_IR_LEARNING)
        }

        /*if (device.supportSmartMode) {
            items.add(EnumListItem.GS_SMART_MODE)
        }*/

        if (device.supportDeepSleep()) {
            items.add(EnumListItem.GS_DEEP_SLEEP)
        }

        items.add(EnumListItem.GS_FEEDBACK_TONES)

        return items.toList()
    }



    companion object {
        const val TAG = "OneCommanderGroupProductSettingsActivity"
    }


}