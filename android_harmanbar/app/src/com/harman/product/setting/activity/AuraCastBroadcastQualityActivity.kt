package com.harman.product.setting.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.annotation.MainThread
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.BarUtils
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityAuracastBroadcastQualityBinding
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.discover.DeviceStore
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.getBLEProtocol
import com.harman.log.Logger
import com.harman.product.setting.DisableAuraCastSqConfirmDialog
import com.harman.product.setting.IDisableAuraCastSqConfirm
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.v5protocol.bean.devinfofeat.V5AuracastStandardQuality
import com.harman.widget.AppCompatBaseActivity
import com.wifiaudio.action.lan.LocaleLanConfigUtil.changeAppLan
import com.wifiaudio.app.WAApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class AuraCastBroadcastQualityActivity : AppCompatBaseActivity() {

    private val viewModel by lazy {
        ViewModelProvider(
            owner = this@AuraCastBroadcastQualityActivity,
            AuraCastBroadcastQualityViewModelFactory(activity = this@AuraCastBroadcastQualityActivity)
        )[AuraCastBroadcastQualityViewModel::class.java]
    }

    private var targetDevice: Device? = null

    @SuppressLint("NotifyDataSetChanged")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            targetDevice = parseBundle(intent) ?: run {
                finish()
                return@launch
            }
            BarUtils.setStatusBarColor(
                this@AuraCastBroadcastQualityActivity,
                ContextCompat.getColor(this@AuraCastBroadcastQualityActivity, R.color.transparent),
                true
            )
            val binding = uiBinding()
            setContentView(binding.root)
            targetDevice?.registerDeviceListener(deviceListener)

            when (targetDevice) {
                is PartyBandDevice -> {
                    (targetDevice as PartyBandDevice).getDevInfoFeat<V5AuracastStandardQuality>()
                        ?.also {
                            viewModel.updateSqMode(it.isOn)
                        }
                }

                is PartyBoxDevice -> {
                    targetDevice?.registerDeviceListener(devicePartyBoxListener)
                    val device = (targetDevice as PartyBoxDevice)
                    val isOn = device.bleDevice?.auracastInfo?.auracastSQBroadcast
                    viewModel.updateSqMode(isOn == true)
                }
            }

        }
    }

    override fun onDestroy() {
        super.onDestroy()
        disableAuraCastSqConfirmDialog?.dismiss()
        targetDevice?.unregisterDeviceListener(deviceListener)
        targetDevice?.unregisterDeviceListener(devicePartyBoxListener)
        viewModel.cancelJob()
    }

    override fun onPause() {
        super.onPause()
        viewModel.cancelJob()
        disableAuraCastSqConfirmDialog?.dismiss()
        when (targetDevice) {
            is OneDevice -> {
                (targetDevice as OneDevice).setAuraCastSqMode(false)
            }

            is PartyBoxDevice -> {
                (targetDevice as PartyBoxDevice).also { device ->
                    device.setAuraCastSqMode(protocol = device.getBLEProtocol(), isOn = false)
                }
            }
        }
    }

    private fun uiBinding() =
        ActivityAuracastBroadcastQualityBinding.inflate(layoutInflater).also { binding ->
            binding.activity = this@AuraCastBroadcastQualityActivity
            binding.lifecycleOwner = this@AuraCastBroadcastQualityActivity
            binding.viewModel = viewModel
        }

    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) super.attachBaseContext(newBase) else {
            val baseContext = changeAppLan(newBase)
            WAApplication.me.mAppLanguageContext = baseContext
            super.attachBaseContext(baseContext)
        }
    }

    override fun onBackPressed() {
        if (viewModel.isSqEnabled.value == true) {
            onDisableAuraCastSq()
        } else {
            super.onBackPressed()
        }
    }

    fun onChangeAuraCastBroadcastQuality() {
        Logger.d(TAG, "onChangeAuraCastBroadcastQuality=${viewModel.isSqEnabled.value}")
        if (viewModel.isSqEnabled.value == true) {
            onDisableAuraCastSq()
        } else {
            when (targetDevice) {
                is PartyBoxDevice,
                is PartyBandDevice -> {
                    viewModel.onChangeAuraCastBroadcastQuality(true)
                }

                else -> {//Keep the previous logic unchanged
                    viewModel.onChangeAuraCastBroadcastQuality()
                }
            }

        }
    }

    private var disableAuraCastSqConfirmDialog: DisableAuraCastSqConfirmDialog? = null

    private fun onDisableAuraCastSq() {
        disableAuraCastSqConfirmDialog?.dismiss()
        disableAuraCastSqConfirmDialog = DisableAuraCastSqConfirmDialog(
            context = this,
            listener = object : IDisableAuraCastSqConfirm {
                override fun onDisableAuraCastSq() {
                    viewModel.onChangeAuraCastBroadcastQuality(forceOn = false)
                    viewModel.cancelJob()
                }

                override fun onCancelClick() {
                    disableAuraCastSqConfirmDialog?.dismiss()
                }
            }
        ).apply {
            show()
        }
    }

    @MainThread
    private fun parseBundle(intent: Intent?): Device? {
        intent ?: return null

        val uuid = intent.getStringExtra(BUNDLE_UUID) ?: run {
            Logger.e(TAG, "parseBundle() >>> missing UUID")
            return null
        }

        val targetDevice = DeviceStore.find(uuid = uuid) ?: run {
            Logger.e(TAG, "parseBundle() >>> fail to get dummy from online or DB [$uuid]")
            return null
        }
        Logger.w(TAG, "parseBundle() >>> targetDevice: $targetDevice")
        viewModel.updateDevice(targetDevice)
        viewModel.updateSqMode(false)

        return targetDevice
    }

    private val deviceListener = object : IOneDeviceListener {

        override fun onAuraCastSqMode(response: AuraCastSqModelResponse) {
            Logger.d(TAG, "onAuraCastSqMode() >>>> $response")
            super.onAuraCastSqMode(response)
            CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                viewModel.updateSqMode(response.isSqOn())
            }
        }

    }
    private var execStartJob = true
    private val devicePartyBoxListener = object : IPartyBoxDeviceListener {
        override fun onSetDeviceInfo() {
            CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                val isOn =
                    (targetDevice as PartyBoxDevice).bleDevice?.auracastInfo?.auracastSQBroadcast == true
                Logger.d(TAG, "onAuraCastSqMode=${isOn} execStartJob=${execStartJob}")
                viewModel.updateIsSqEnabled(isOn)
                if (isOn && execStartJob) {
                    viewModel.startJob()
                    execStartJob = false
                }
                if (!isOn) {
                    viewModel.cancelJob()
                    execStartJob = true
                }
            }
        }
    }

    companion object {
        private const val TAG = "AuraCastBroadcastQualityActivity"
        private const val BUNDLE_UUID = "Bundle_UUID"

        fun portal(context: Context, device: Device) {
            val intent = Intent(context, AuraCastBroadcastQualityActivity::class.java).apply {
                putExtra(BUNDLE_UUID, device.UUID)
            }
            context.startActivity(intent)
        }
    }
}