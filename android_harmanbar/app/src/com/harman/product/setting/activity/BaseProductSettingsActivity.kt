package com.harman.product.setting.activity

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import com.blankj.utilcode.util.ClickUtils
import com.harman.EventUtils
import com.harman.FirebaseEventManager
import com.harman.bar.app.R
import com.harman.bar.app.databinding.ActivityProductSettingsBinding
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.LwaStateResponse
import com.harman.connect.syncGetAuraCastSqModeWithTimeout
import com.harman.isAuraCastQualitySupport
import com.harman.isCN
import com.harman.isHomeSeries
import com.harman.isPartyBox
import com.harman.isSoundBar
import com.harman.partylight.util.fitSystemBar
import com.harman.partylight.util.push
import com.harman.portalActivity
import com.harman.product.reconnect.BluetoothReconnectionActivity
import com.harman.product.rename.RenameDeviceActivity
import com.harman.product.setting.BaseViewHolderBean
import com.harman.product.setting.Content
import com.harman.product.setting.Divider
import com.harman.product.setting.EnumListItem
import com.harman.product.setting.EnumListItemType
import com.harman.product.setting.IRestoreFactoryConfirm
import com.harman.product.setting.ProductSettingAdapter
import com.harman.product.setting.ProductSettingGroup
import com.harman.product.setting.ProductSettingViewModel
import com.harman.product.setting.ProductSettingViewModelFactory
import com.harman.product.setting.RestoreFactoryConfirmDialog
import com.harman.product.setting.SettingsRecyclerViewDecoration
import com.harman.product.setting.WiFiStreamingContent
import com.harman.streaming.AirPlayActivity
import com.harman.streaming.QobuzActivity
import com.harman.streaming.SpotifyActivity
import com.harman.streaming.TidalConnectActivity
import com.harman.streaming.amazon.AmazonAlexaActivity
import com.harman.streaming.google.GooglePortalHelper
import com.harman.streaming.roon.RoonReadyActivity
import com.harman.webview.DeviceControlHybridActivity
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.deviceSupportAirPlay
import com.harman.discover.bean.deviceSupportAlexa
import com.harman.discover.bean.deviceSupportAlexaVA
import com.harman.discover.bean.deviceSupportChromeCast
import com.harman.discover.bean.deviceSupportGoogleVA
import com.harman.discover.bean.deviceSupportQobuz
import com.harman.discover.bean.deviceSupportRoonReady
import com.harman.discover.bean.deviceSupportSpotify
import com.harman.discover.bean.deviceSupportTidal
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools.getGroupMode
import com.harman.discover.util.Tools.isAuraStudioBt
import com.harman.discover.util.Tools.isHorizon3
import com.harman.discover.util.Tools.isSoundStick5Bt
import com.harman.isNonePlayableDevice
import com.harman.isOneCommanderGroup
import com.harman.log.Logger
import com.harman.parseAsAnyDevice
import com.harman.product.setting.horizon.BtDeviceProductSettingsActivity
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.utils.Utils
import com.harman.v5protocol.bean.devinfofeat.V5AuracastStandardQuality
import com.harman.va.refactor.VAPortalHelper
import com.harman.widget.AppCompatBaseActivity
import com.skin.SkinResourcesUtils
import com.wifiaudio.view.custom_view.scrollpicker.util.ScreenUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Created by gerrardzhang on 2024/7/23.
 *
 * Base activity for all kinds of product settings pages.
 */
abstract class BaseProductSettingsActivity : AppCompatBaseActivity() {

    /**
     * Assemble grouped items displayed in product setting list based on [device] when it's [OneDevice.isWiFiOnline]
     */
    abstract fun getWiFiGroups(device: OneDevice): List<ProductSettingGroup>

    /**
     * Assemble grouped items displayed in product setting list based on [device] when it's [OneDevice.isBLEOnline]
     */
    abstract fun getBTGroups(device: Device): List<ProductSettingGroup>

    /**
     * Don't invoke before [onCreate] finished !
     */
    protected var viewModel: ProductSettingViewModel? = null

    var showAuraCastQuality: Boolean = false

    private val _device = MutableLiveData<Device>()
    val device: LiveData<Device>
        get() = _device

    private val _adapter = MutableLiveData<ProductSettingAdapter>()
    val adapter: LiveData<ProductSettingAdapter>
        get() = _adapter

    private val _isVAGoogleEnable = MutableLiveData<Boolean>()
    open val isVAGoogleEnable: LiveData<Boolean>
        get() = _isVAGoogleEnable

    private val _isVAAlexaEnable = MutableLiveData<Boolean>()
    val isVAAlexaEnable: LiveData<Boolean>
        get() = _isVAAlexaEnable

    private val _deepSleepOn = MutableLiveData<Boolean>()
    val deepSleepOn: LiveData<Boolean>
        get() = _deepSleepOn

    private val _feedbackToneOn = MutableLiveData<Boolean>()
    val feedbackToneOn: LiveData<Boolean>
        get() = _feedbackToneOn

    private val _smartModeOn = MutableLiveData<Boolean>()
    val smartModeOn: LiveData<Boolean>
        get() = _smartModeOn

    val recyclerViewDecoration = adapter.map {
        SettingsRecyclerViewDecoration(it)
    }

    private val _listBeans = MutableLiveData<List<BaseViewHolderBean>>()
    val listBeans: LiveData<List<BaseViewHolderBean>>
        get() = _listBeans

    private val _fullscreenLoading = MutableLiveData<Boolean>()
    val fullscreenLoading: LiveData<Boolean>
        get() = _fullscreenLoading

    protected val _autoPowerOffText = MutableLiveData<String>()
    val autoPowerOffText: LiveData<String>
        get() = _autoPowerOffText

    protected val _batterySavingOn = MutableLiveData<Boolean>()
    val batterySavingOn: LiveData<Boolean>
        get() = _batterySavingOn

    private val _backwardScreenOn = MutableLiveData<Boolean>()
    val backwardScreenOn: LiveData<Boolean>
        get() = _backwardScreenOn

    private val _wifiName = MutableLiveData<String?>()
    val wifiName: LiveData<String?>
        get() = _wifiName

    private val _wifiStrength = MutableLiveData<String>()
    val wifiStrength: LiveData<String>
        get() = _wifiStrength

    private val _pageTitle = MutableLiveData<String>()
    val pageTitle: LiveData<String>
        get() = _pageTitle

    private var restoreFactoryConfirmDialog: RestoreFactoryConfirmDialog? = null

    private var sqModelResponse: AuraCastSqModelResponse? = null

    private var binding: ActivityProductSettingsBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        Utils.decorateHarmanWindow(this)
        super.onCreate(savedInstanceState)


        val device = parseAsAnyDevice() ?: run {
            finish()
            return
        }

        if (device is OneDevice && device.isAuraCastQualitySupport()) {
            CoroutineScope(DISPATCHER_DEFAULT).launch {
                sqModelResponse = device.syncGetAuraCastSqModeWithTimeout(TAG)
            }
        }

        uiBinding()
        dataBinding(device = device)

        if (OneRole.SINGLE == (device as? OneDevice)?.role) {
            binding?.tvTitle?.setOnClickListener(MultiClickListener(7, 666L))
        }
        if (device is PartyBandDevice) {
            binding?.tvTitle?.setOnClickListener(MultiClickListener(7, 666L))
        }
    }

    private fun uiBinding() {
        binding = ActivityProductSettingsBinding.inflate(layoutInflater)
        binding?.lifecycleOwner = this@BaseProductSettingsActivity
        binding?.activity = this@BaseProductSettingsActivity
        fitSystemBar()
        setContentView(binding?.root)
    }

    open fun genViewModel(device: Device): ProductSettingViewModel {
        return ViewModelProvider(
            this@BaseProductSettingsActivity,
            ProductSettingViewModelFactory(device = device, port = null)
        )[ProductSettingViewModel::class.java]
    }

    private fun dataBinding(device: Device) {
        _device.value = device

        val viewModel = genViewModel(device)

        <EMAIL> = viewModel

        lifecycle.addObserver(viewModel)

        _adapter.value = ProductSettingAdapter(
            groups = getGroups(device = device).mapToUI(),
            activity = this@BaseProductSettingsActivity
        )

        refreshFeatureSupportIfNecessary(device = device)
        refreshFeedbackToneConfig(viewModel = viewModel)
        refreshDeepSleepConfig(viewModel = viewModel)
        _pageTitle.value = if ((device as? OneDevice)?.getGroupMode() == GroupMode.GROUP) SkinResourcesUtils.getString("other_settings")
        else SkinResourcesUtils.getString("harmanbar_product_settings")

        viewModel.wifiName.observe(this) {
            _wifiName.value = it
        }

        viewModel.wifiStrengthPercentage.observe(this) {
            _wifiStrength.value = "$it%"
        }

        viewModel.smartModeOn.observe(this) { isOn ->
            _smartModeOn.value = isOn
        }

        viewModel.backwardScreenOn.observe(this) { isOn ->
            _backwardScreenOn.value = isOn
        }
    }

    /**
     * 'Cause some entrance item in product settings based on feature support.
     * We need to fetch it for necessary if it hadn't gotten yet.
     */
    private fun refreshFeatureSupportIfNecessary(device: Device) {
        if (device is OneDevice) {
            if(!device.isOneCommanderGroup()){
                device.featSupportExt?.featSupport?.let { feat ->
                    onFeatureSupport(feat = feat)
                    return
                }
            }

            lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                val feat = viewModel?.syncGetFeatureSupport(logTag = TAG)

                if (null != feat) {
                    updateGroups(getGroups(device))
                    onFeatureSupport(feat = feat)
                }
            }
        } else if (device is PartyBandDevice) {
            lifecycleScope.launch {
                device.getDevInfoFeat<V5AuracastStandardQuality>()?.also {
                    updateGroups(getGroups(device))
                }
            }
        }
    }

    private fun getGroups(device: Device): List<ProductSettingGroup> =
        if ((device as? OneDevice)?.isWiFiOnline == true) {
            getWiFiGroups(device = device)
        } else {
            getBTGroups(device = device)
        }

    private fun refreshFeedbackToneConfig(viewModel: ProductSettingViewModel) {
        viewModel.feedbackToneConfig.observe(this) { config ->
            _feedbackToneOn.value = config.isOn()
        }
        viewModel.refreshFeedbackToneConfig()
    }

    private fun refreshDeepSleepConfig(viewModel: ProductSettingViewModel) {
        viewModel.deepSleepConfig.observe(this) { config ->
            _deepSleepOn.value = config.isOn()
        }
        viewModel.refreshDeepSleepConfig()
    }

    override fun onResume() {
        super.onResume()
        _isVAAlexaEnable.value = isAlexaVAEnabled()
        _isVAGoogleEnable.value = isGoogleVAEnabled()
    }

    override fun onDestroy() {
        super.onDestroy()
        restoreFactoryConfirmDialog?.dismiss()

        viewModel?.let { viewModel ->
            lifecycle.removeObserver(viewModel)
        }
    }

    open fun onBackBtnClick() {
        finish()
    }

    @MainThread
    private fun updateGroups(groups: List<ProductSettingGroup>) {
        _listBeans.value = groups.mapToUI()
    }

    /**
     * Override this function if some business based on feature support result.
     */
    @MainThread
    protected open fun onFeatureSupport(feat: FeatureSupport) {

    }

    private fun List<ProductSettingGroup>.mapToUI(): List<BaseViewHolderBean> = flatMap { group ->
        val beans = mutableListOf<BaseViewHolderBean>()

        if (null != group.type.titleStringRes) {
            beans.add(Divider(titleStringRes = group.type.titleStringRes))
        }

        group.items.forEachIndexed { index, item ->
            beans.add(
                when (item) {
                    EnumListItem.WS_GOOGLE_CAST -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.ic_brand_google_cast,
                        wifiStreamingTextRes = R.string.google_cast,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_AMAZON_ALEXA -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.brand_icon_alexa_cast_fg_primary,
                        wifiStreamingTextRes = R.string.alexa_cast,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_SPOTIFY_CONNECT -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.ic_brand_spotify,
                        wifiStreamingTextRes = R.string.jbl_Spotify_Connect,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_TIDAL_CONNECT -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.ic_brand_tidal,
                        wifiStreamingTextRes = R.string.tidal_connect,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_AIRPLAY -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.component_svg_icon_air_play,
                        wifiStreamingTextRes = R.string.jbl_AirPlay,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_ROON_READY -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.ic_brand_roon,
                        wifiStreamingTextRes = R.string.roon_ready,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_QPLAY -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = false,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.component_svg_icon_qplay,
                        wifiStreamingTextRes = R.string.stream_service_qplay,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_DLNA -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = false,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.component_svg_icon_dlna,
                        wifiStreamingTextRes = R.string.jbl_DLNA,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.WS_QOBUZ -> WiFiStreamingContent(
                        itemType = item,
                        layoutId = item.layoutId,
                        isArrowIconVisible = true,
                        backgroundResId = index.toBackgroundResId(total = group.items.size),
                        wifiStreamingIcRes = R.drawable.icon_qobuz,
                        wifiStreamingTextRes = R.string.jbl_Qobuz_Connect,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    EnumListItem.R_RESTORE_FACTORY_SETTINGS -> Content(
                        itemType = item,
                        layoutId = item.layoutId,
                        backgroundResId = null,
                        bottomMargin = ScreenUtil.dpToPx(46)
                    )

                    EnumListItem.AURACAST_QUALITY -> Content(
                        itemType = item,
                        layoutId = item.layoutId,
                        backgroundResId = null,
                        bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                    )

                    else -> {
                        Logger.d(TAG, "item: $item, ${group.items.size}, $index")
                        Content(
                            itemType = item,
                            layoutId = item.layoutId,
                            backgroundResId = index.toBackgroundResId(total = group.items.size),
                            bottomMargin = index.toBottomMarginPixel(total = group.items.size)
                        )
                    }
                }
            )
        }

        beans.toList()
    }

    /**
     * @return
     * [R.drawable.radius_medium_bg_card] if the only one.
     * [R.drawable.radius_medium_top_bg_card] if the first one.
     * [R.drawable.radius_medium_bottom_bg_card] if the last one.
     * [R.drawable.bg_card] if the none of the prev. situations.
     */
    private fun Int.toBackgroundResId(total: Int): Int = when {
        total <= 1 -> R.drawable.radius_medium_bg_card
        this == 0 -> R.drawable.radius_medium_top_bg_card
        this >= total - 1 -> R.drawable.radius_medium_bottom_bg_card
        else -> R.drawable.bg_card
    }

    /**
     * @return 16dp if the last one, or 0dp not.
     */
    private fun Int.toBottomMarginPixel(total: Int): Int = when {
        this >= total - 1 -> ScreenUtil.dpToPx(16)
        else -> ScreenUtil.dpToPx(0)
    }

    /**
     * Impl. to handle item click events.
     */
    @CallSuper
    open fun onItemClick(content: Content?) {
        Logger.d(TAG, "onItemClick() >>> content: $content")

        when (content?.itemType) {
            EnumListItem.GS_RENAME -> {
                val device = device.value ?: return
                RenameDeviceActivity.portal(context = this, device = device)
            }

            EnumListItem.GS_BLUETOOTH_RECONNECTION -> {
                val device = device.value ?: return
                portalActivity(target = BluetoothReconnectionActivity::class.java, device = device)
            }

            EnumListItem.GS_DEEP_SLEEP -> {
                viewModel?.switchDeepSleepConfig()
            }

            EnumListItem.GS_FEEDBACK_TONES -> {
                viewModel?.switchFeedbackToneConfig()
            }

            EnumListItem.GS_SMART_MODE -> {
                viewModel?.switchSmartMode()
            }

            EnumListItem.WS_GOOGLE_CAST -> {
                val device = device.value ?: return

                lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
                    GooglePortalHelper.portalCast(
                        device = device as OneDevice,
                        activity = this@BaseProductSettingsActivity,
                        logTag = TAG
                    )
                }
            }

            EnumListItem.WS_AMAZON_ALEXA -> {
                val device = device.value ?: return
                AmazonAlexaActivity.portal(context = this, device = device as OneDevice)
            }

            EnumListItem.WS_SPOTIFY_CONNECT -> {
                SpotifyActivity.portal(context = this)
            }

            EnumListItem.WS_TIDAL_CONNECT -> {
                TidalConnectActivity.portal(context = this)
            }

            EnumListItem.WS_AIRPLAY -> {
                push<AirPlayActivity>()
            }

            EnumListItem.WS_QOBUZ -> {
                push<QobuzActivity>()
            }

            EnumListItem.WS_ROON_READY -> {
                val device = device.value ?: return
                RoonReadyActivity.portal(context = this, device = device as OneDevice)
            }

            EnumListItem.VA_GOOGLE_ASSISTANT -> {
                val device = device.value ?: return
                VAPortalHelper.portalGVA(context = this, device = device as OneDevice, EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_DEVICE_SETTING)
            }

            EnumListItem.VA_AMAZON_ALEXA -> {
                val device = device.value ?: return
                VAPortalHelper.portalAlexa(context = this, device = device as OneDevice, EventUtils.Dimension.EnumCoulsonEntry.POST_OOBE_DEVICE_SETTING)
            }

            EnumListItem.AURACAST_QUALITY -> {
                val device = device.value ?: return
                AuraCastBroadcastQualityActivity.portal(context = this, device = device)
            }

            EnumListItem.R_RESTORE_FACTORY_SETTINGS -> {
                onRestoreFactoryClick()
            }

            EnumListItem.WS_QPLAY,
            EnumListItem.WS_DLNA,
            EnumListItem.N_WI_FI,
            EnumListItem.N_WI_FI_STRENGTH -> {
                // no portal needed
            }

            EnumListItem.GS_BACKWARD_SCREEN -> {
                viewModel?.switchBackwardScreen()
            }

            else -> {
                // no impl
            }
        }
    }

    open fun onTipsClick(content: Content?) {
        Logger.d(TAG, "onTipsClick() >>> content: $content")

    }

    private fun onRestoreFactoryClick() {
        restoreFactoryConfirmDialog?.dismiss()
        restoreFactoryConfirmDialog = RestoreFactoryConfirmDialog(
            context = this,
            listener = object : IRestoreFactoryConfirm {
                override fun onRestoreClick() {
                    handleRestoreFactory()
                }

                override fun onCancelClick() {
                    // no impl
                }
            }
        ).apply {
            show()
        }
    }

    private fun handleRestoreFactory() {
        lifecycleScope.launch(DISPATCHER_FAST_MAIN) {
            _fullscreenLoading.value = true

            if (true == viewModel?.restoreFactory(ctx = baseContext)) {
                Logger.i(TAG, "handleRestoreFactory() >>> suc.")
                setResult(RESULT_OK, DeviceControlHybridActivity.sealFinishIntent())
                finish()
            } else {
                Logger.w(TAG, "handleRestoreFactory() >>> fail")
                _fullscreenLoading.value = false
            }
        }
    }

    /**
     * @link [OneDevice.c4aPermissionStatusExt] [C4aPermissionStatusResponse.enable]
     */
    private fun isGoogleVAEnabled(): Boolean {
        return true == viewModel?.isGoogleVAEnabled()
    }

    /**
     * @link [OneDevice.lwaStateResponseExt] [LwaStateResponse.logged]
     */
    private fun isAlexaVAEnabled(): Boolean {
        return true == viewModel?.isAlexaVAEnabled()
    }

    open fun decoWiFiStreamingItems(device: OneDevice, groups: MutableList<ProductSettingGroup>) {
        Logger.d(
            TAG, "decoWiFiStreamingItems() >>> device[${device.UUID}] pid[${device.pid}] " +
                    "CN[${device.isCN()}] AirPlay[${device.deviceSupportAirPlay()}] " +
                    "Roon[${device.deviceSupportRoonReady()}] Spotify[${device.deviceSupportSpotify()}] " +
                    "Qobuz[${device.deviceSupportQobuz()}] Tidal[${device.deviceSupportTidal()}] " +
                    "Alexa[${device.deviceSupportAlexa()}] AlexaVA[${device.deviceSupportAlexaVA()}] " +
                    "ChromeCast[${device.deviceSupportChromeCast()}] GoogleVA[${device.deviceSupportGoogleVA()}]"
        )

        if (device.isCN()) {
            decoCNWiFiStreaming(device = device, groups = groups)
        } else {
            decoNonCNWiFiStreaming(device = device, groups = groups)
        }
    }

    protected fun decoResetItem(device: OneDevice, groups: MutableList<ProductSettingGroup>) {
        if (device.getGroupMode() != GroupMode.GROUP) {
            groups.add(ProductSettingGroup(EnumListItemType.RESET, listOf(EnumListItem.R_RESTORE_FACTORY_SETTINGS)))
        }
    }

    /**
     * Full kinds of WiFi Streaming and VA providers.
     * Group by controllable / non-controllable first. Sort as alphabet inside each group later.
     */
    private fun decoNonCNWiFiStreaming(device: OneDevice, groups: MutableList<ProductSettingGroup>) {
        val vaItems = mutableListOf<EnumListItem>()
        val wsItems = mutableListOf<EnumListItem>()

        if (device.deviceSupportAirPlay()) {
            wsItems.add(EnumListItem.WS_AIRPLAY)
        }

        if (device.deviceSupportAlexaVA()) {
            vaItems.add(EnumListItem.VA_AMAZON_ALEXA)
        } else if (device.deviceSupportAlexa()) {
            wsItems.add(EnumListItem.WS_AMAZON_ALEXA)
        }

        if (device.deviceSupportGoogleVA()) {
            vaItems.add(EnumListItem.VA_GOOGLE_ASSISTANT)
        } else if (device.deviceSupportChromeCast()) {
            wsItems.add(EnumListItem.WS_GOOGLE_CAST)
        }

        if (device.deviceSupportQobuz()) {
            wsItems.add(EnumListItem.WS_QOBUZ)
        }

        if (device.deviceSupportRoonReady()) {
            wsItems.add(EnumListItem.WS_ROON_READY)
        }

        if (device.deviceSupportSpotify()) {
            wsItems.add(EnumListItem.WS_SPOTIFY_CONNECT)
        }

        if (device.deviceSupportTidal()) {
            wsItems.add(EnumListItem.WS_TIDAL_CONNECT)
        }

        wsItems.add(EnumListItem.WS_DLNA)

        if (vaItems.isNotEmpty()) {
            groups.add(ProductSettingGroup(EnumListItemType.VOICE_ASSISTANT, vaItems))
        }

        groups.add(ProductSettingGroup(EnumListItemType.WIFI_STREAMING, wsItems))
    }

    /**
     * Only contains QPlay, DLNA and Roon Ready.
     * Group by controllable / non-controllable first. Sort as alphabet inside each group later.
     */
    private fun decoCNWiFiStreaming(device: OneDevice, groups: MutableList<ProductSettingGroup>) {
        val wsItems = mutableListOf<EnumListItem>()

        wsItems.add(EnumListItem.WS_AIRPLAY)

        if (device.deviceSupportRoonReady()) {
            wsItems.add(EnumListItem.WS_ROON_READY)
        }

        wsItems.add(EnumListItem.WS_DLNA)

        wsItems.add(EnumListItem.WS_QPLAY)

        groups.add(
            ProductSettingGroup(
                EnumListItemType.WIFI_STREAMING,
                wsItems.toList()
            )
        )
    }

    inner class MultiClickListener(triggerClickCount: Int, clickInterval: Long) :
        ClickUtils.OnMultiClickListener(triggerClickCount, clickInterval) {
        override fun onTriggerClick(v: View?) {
            if (v?.id == binding?.tvTitle?.id && v?.id != null) {
                Logger.d(TAG, "showAuraCastQuality: $showAuraCastQuality")
                showAuraCastQuality = true
                device.value?.let { updateGroups(getGroups(it)) }
            }
            binding?.tvTitle?.setOnClickListener(null)
        }

        override fun onBeforeTriggerClick(v: View?, count: Int) {
            Logger.d(TAG, "onBeforeTriggerClick count=$count")
        }
    }

    override val tag: String = TAG

    override val baseControlDevice: Device?
        get() = device.value

    companion object {
        /**
         * @param requestCode for receiving result from Product Setting pages.
         */
        fun portal(activity: Activity, device: Device, requestCode: Int): Boolean {
            FirebaseEventManager.report(
                eventName = EventUtils.EventName.EVENT_ACTION,
                device = device,
                dimensions = mapOf(
                    EventUtils.Dimension.DI_ACTION_TYPE to EventUtils.Dimension.EnumDiActionType.ACTION_MAIN_SCREEN.value,
                    EventUtils.Dimension.DI_ACTION_ITEM to EventUtils.Dimension.EnumMainScreenActionItem.PRODUCT_SETTINGS.value
                )
            )

            val rst = when (device) {
                is OneDevice -> {
                    if (device.isSoundBar()) {
                        if (!device.isWiFiOnline) { // Bar series didn't allow product settings without WiFi connect.
                            Logger.w(TAG, "portal() >>> bar series didn't support BT style")
                            false
                        } else {
                            activity.portalActivity(target = OneBarProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                        }
                    }else if (device.isOneCommanderGroup()) {
                        if (!device.isWiFiOnline) { // One commander group  didn't allow product settings without WiFi connect.
                            Logger.w(TAG, "portal() >>> One commander group didn't support BT style")
                            false
                        } else {
                            activity.portalActivity(target = OneCommanderGroupProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                        }
                    }  else if (device.isPartyBox()) {
                        activity.portalActivity(target = OnePartyBoxProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                    } else if (device.isHomeSeries()) {
                        activity.portalActivity(target = OneHomeSeriesProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                    } else if (device.isNonePlayableDevice()) {
                        activity.portalActivity(target = OneSubProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                    } else {
                        activity.portalActivity(target = OnePortableProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                    }
                }

                is PartyBandDevice -> {
                    activity.portalActivity(PartyBandProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                }

                is PartyBoxDevice -> {
                    if (true == device.pid?.isHorizon3() || true == device.pid?.isSoundStick5Bt() || true == device.pid?.isAuraStudioBt()) {
                        activity.portalActivity(BtDeviceProductSettingsActivity::class.java, device = device)
                    } else {
                        // onyx single and onyx stereo has no product settings
                        activity.portalActivity(target = OnePartyBoxProductSettingsActivity::class.java, device = device, requestCode = requestCode)
                    }
                }

                else -> false
            }

            return rst
        }



        private const val TAG = "BaseProductSettingsActivity"
    }
}