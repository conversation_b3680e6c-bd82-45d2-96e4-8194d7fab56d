package com.harman.product.setting.activity

import com.harman.bar.app.R
import com.harman.discover.bean.Device
import com.harman.isAuraCastQualitySupport
import com.harman.product.irlearn.IRLearningDialog
import com.harman.product.setting.Content
import com.harman.product.setting.EnumListItem
import com.harman.product.setting.EnumListItemType
import com.harman.product.setting.ProductSettingGroup
import com.harman.discover.bean.OneDevice
import com.harman.log.Logger
import com.harman.product.deep.sleep.DeepSleepTipsDialog
import com.harman.supportDeepSleep


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/23.
 *
 * Product Settings page for one platform bar devices.
 */
open class OneBarProductSettingsActivity : BaseProductSettingsActivity() {

    private var irLearningDialog: IRLearningDialog? = null
    private var deepSleepTipsDialog: DeepSleepTipsDialog? = null

    override fun onDestroy() {
        super.onDestroy()

        irLearningDialog?.dismiss()
        deepSleepTipsDialog?.dismiss()
    }

    override fun getWiFiGroups(device: OneDevice): List<ProductSettingGroup> {
        val groups = mutableListOf<ProductSettingGroup>()
        groups.add(
            ProductSettingGroup(
                EnumListItemType.GENERAL_SETTING,
                assembleGeneralSetting(device = device)
            )
        )

        if (device.isAuraCastQualitySupport() && showAuraCastQuality) {
            groups.add(ProductSettingGroup(EnumListItemType.AURACAST_BROADCAST_QUALITY, listOf(EnumListItem.AURACAST_QUALITY)))
        }

        groups.add(
            ProductSettingGroup(
                EnumListItemType.NETWORK,
                listOf(
                    EnumListItem.N_WI_FI,
                    EnumListItem.N_WI_FI_STRENGTH
                )
            )
        )

        super.decoWiFiStreamingItems(device = device, groups = groups)
        super.decoResetItem(device = device, groups = groups)

        return groups.toList()
    }

    /**
     * Bar didn't have BT setting
     */
    override fun getBTGroups(device: Device): List<ProductSettingGroup> {
        return listOf()
    }

    open fun assembleGeneralSetting(device: OneDevice): List<EnumListItem> {
        val items = mutableListOf<EnumListItem>()

        items.add(EnumListItem.GS_RENAME)

        if (device.supportIRLearning) {
            items.add(EnumListItem.GS_IR_LEARNING)
        }

        /*if (device.supportSmartMode) {
            items.add(EnumListItem.GS_SMART_MODE)
        }*/

        if (device.supportDeepSleep()) {
            items.add(EnumListItem.GS_DEEP_SLEEP)
        }

        items.add(EnumListItem.GS_FEEDBACK_TONES)

        return items.toList()
    }

    override fun onItemClick(content: Content?) {
        super.onItemClick(content)

        when (content?.itemType) {
            EnumListItem.GS_IR_LEARNING -> {
                irLearningDialog?.dismiss()
                irLearningDialog = IRLearningDialog(
                    device = (device.value as? OneDevice) ?: return,
                    context = this
                ).apply {
                    show()
                }
            }

            else -> {
                // no impl.
            }
        }
    }

    override fun onTipsClick(content: Content?) {
        super.onTipsClick(content)

        when (content?.itemType) {
            EnumListItem.GS_DEEP_SLEEP -> {
                deepSleepTipsDialog?.dismiss()
                deepSleepTipsDialog = DeepSleepTipsDialog(
                    context = this,
                    tip = getString(R.string.deep_sleep_mode_tips)
                ).apply {
                    show()
                }
            }

            else -> {
                // no impl.
            }
        }
    }

    companion object {
        const val TAG = "OneBarProductSettingsActivity"
    }


}