package com.harman;

import android.text.TextUtils;

import com.blankj.utilcode.util.GsonUtils;
import com.harman.bean.AuthDevice;
import com.harman.bean.LWAInfo;
import com.harman.bean.RequestTone;
import com.harman.bean.VoiceLanguage;
import com.harman.ble.HBBLEManagerCompat;
import com.harman.hkone.Util;
import com.harmanbar.ble.entity.BLECommand;
import com.harmanbar.ble.entity.HBBluetoothDevice;
import com.harmanbar.ble.listener.HBBLEControlListener;
import com.harmanbar.ble.listener.HBBLEDeviceStatusChangedListener;
import com.harmanbar.ble.statistic.StatisticConstant;
import com.wifiaudio.action.DeviceCustomerSettingAction;
import com.wifiaudio.action.DeviceSettingActionCallback;
import com.wifiaudio.action.log.print_log.LogsUtil;
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.GeneralConfig;
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.LightColorPicker;
import com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.partypad.DJPad;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

import config.AppLogTagUtil;

public class WirelessConnector {
    private static HBBLEControlListener convertBLEListener(AuthDevice device, int code, DeviceSettingActionCallback callback) {
        if (callback == null) {
            return null;
        }
        HBBLEControlListener bleListener = new HBBLEControlListener() {
            @Override
            public void onResponseTimeout(int responseCode) {
                if (callback != null) {
                    callback.onFailure(new Exception("onResponseTimeout : " + responseCode));
                }
            }

            @Override
            public void onFailed(Exception e) {
                if (callback != null) {
                    callback.onFailure(e);
                }
            }

            @Override
            public void onSuccess(int responseCode, String result) {
                if (callback != null) {
                    callback.onSuccess(result);
                }
            }
        };
        return bleListener;

    }

    public static void getDeviceBatteryStatus(AuthDevice authDevice, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "makeDeviceGetBatteryStatus connType " + authDevice.getDeviceType());
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDeviceGetBatteryStatus(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_BATTERY, "", convertBLEListener(authDevice, StatisticConstant.GET_BATTERY, callback));
        }
    }

    public static void getGroupInfo(AuthDevice device, DeviceSettingActionCallback callback) {
        getGroupInfo(device, callback, false);

    }

    public static void getGroupInfo(AuthDevice device, DeviceSettingActionCallback callback, boolean isForStereo) {

    }

    public static void getGroupParameter(AuthDevice device, DeviceSettingActionCallback callback) {
        if (device == null) {
            LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "getGroupParameter: device == null");
            return;
        }

        if (device.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "getGroupParameter: wifi");
            DeviceCustomerSettingAction.getGroupParameter(device.getWifiDevice().IP, callback);
        } else {
            LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "getGroupParameter: ble 0x1d0b");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(device.getBleDevice(), StatisticConstant.LP_GET_GROUP_PARAMETER, "", convertBLEListener(device, StatisticConstant.LP_GET_GROUP_PARAMETER, callback));
        }

    }

    public static void makeDeviceGetDeviceName(@Nullable AuthDevice authDevice, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDeviceGetDeviceName(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_DEVICE_NAME, "", convertBLEListener(authDevice, StatisticConstant.GET_DEVICE_NAME, callback));
        }
    }

    public static void setGeneralConfig(@Nullable AuthDevice authDevice, GeneralConfig config, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setGeneralConfig(authDevice.getWifiDevice().IP, config, callback);
        } else {
            String payload = String.format("{\"type\":\"%s\",\"value\":%d}", config.getType(), config.getValue());
            if (!TextUtils.isEmpty(config.getSubType())) {
                payload = String.format("{\"type\":\"%s\",\"sub_type\":\"%s\",\"value\":%d}", config.getType(), config.getSubType(), config.getValue());
            }
            LogsUtil.d("sendBLEControlCmd", "setGeneralConfig Type Ble, payload: " + payload);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_GENERAL_CONFIG, payload, convertBLEListener(authDevice, StatisticConstant.SET_GENERAL_CONFIG, callback));
        }
    }

    public static void getGeneralConfig(@Nullable AuthDevice authDevice, String type, DeviceSettingActionCallback callback) {
        getGeneralConfig(authDevice, type, "", callback);
    }

    public static void getGeneralConfig(@Nullable AuthDevice authDevice, String type, String subType, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getGeneralConfig(authDevice.getWifiDevice().IP, type, subType, callback);
        } else {
            String payload = String.format("{\"type\":\"%s\"}", type);
            if (!TextUtils.isEmpty(subType)) {
                payload = String.format("{\"type\":\"%s\",\"sub_type\":\"%s\"}", type, subType);
            }
            LogsUtil.d("sendBLEControlCmd", "getGeneralConfig Type Ble, payload: " + payload);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_GENERAL_CONFIG, payload, convertBLEListener(authDevice, StatisticConstant.GET_GENERAL_CONFIG, callback));
        }
    }

    public static void setLightColorPicker(@Nullable AuthDevice authDevice, LightColorPicker colorPicker, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setLightColorPicker(authDevice.getWifiDevice().IP, colorPicker, callback);
        } else {
            String payload = String.format("{\"loop\":%d,\"r\":%d,\"g\":%d,\"b\":%d }", colorPicker.getLoop(), colorPicker.getRed(), colorPicker.getGreen(), colorPicker.getBlue());
            LogsUtil.d("sendBLEControlCmd", "setLightColorPicker Type Ble, payload: " + payload);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_LIGHT_COLOR_PICKER, payload, convertBLEListener(authDevice, StatisticConstant.SET_LIGHT_COLOR_PICKER, callback));
        }
    }

    public static void getLightColorPicker(@Nullable AuthDevice authDevice, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getLightColorPicker(authDevice.getWifiDevice().IP, callback);
        } else {
            LogsUtil.d("sendBLEControlCmd", "getLightColorPicker Type Ble");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_LIGHT_COLOR_PICKER, "", convertBLEListener(authDevice, StatisticConstant.GET_LIGHT_COLOR_PICKER, callback));
        }
    }

    /**
     * Play Party Sound
     * <p>
     * DJTone:101-108(base 101,range 101-200)
     * DJVoice:201-210(base 201,range 201-300)
     *
     * @param authDevice
     * @param value
     * @param callback
     */
    public static void playPartySound(@Nullable AuthDevice authDevice, int value, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.playPartySound(authDevice.getWifiDevice().IP, value, callback);
        } else {
            String payload = String.format("{\"index\":%d}", value);
            LogsUtil.d("sendBLEControlCmd", "playPartySound Type Ble send payload: " + payload);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_PLAY_PARTY_SOUND, payload, convertBLEListener(authDevice, StatisticConstant.SET_PLAY_PARTY_SOUND, callback));
        }
    }

    /**
     * Request parameters include:
     * source
     * It can be: alexa; so far google doesn’t support.
     * Response parameters include:
     * error_code
     * It can be: 0, success; other value means error.
     * status
     * It can be: 0, means disable; 1 means enable.
     */
    public static void setVoiceRequestTone(@Nullable AuthDevice authDevice, String command, RequestTone requestTone, DeviceSettingActionCallback callback) {
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            if (TextUtils.equals("setVoiceRequestStartTone", command)) {
                DeviceCustomerSettingAction.setVoiceRequestStartTone(authDevice.getWifiDevice().IP, requestTone.getSource(), requestTone.getStatus(), callback);
            } else if (TextUtils.equals("setVoiceRequestEndTone", command)) {
                DeviceCustomerSettingAction.setVoiceRequestEndTone(authDevice.getWifiDevice().IP, requestTone.getSource(), requestTone.getStatus(), callback);
            }
        } else {

        }
    }

    /**
     * Request parameters include:
     * source
     * It can be: alexa; so far google doesn’t support.
     * Response parameters include:
     * error_code
     * It can be: 0, success; other value means error.
     * status
     * It can be: 0, means disable; 1 means enable.
     */
    public static void getVoiceRequestTone(@Nullable AuthDevice authDevice, String command, RequestTone requestTone, DeviceSettingActionCallback callback) {
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            if (TextUtils.equals("getVoiceRequestStartTone", command)) {
                DeviceCustomerSettingAction.getVoiceRequestStartTone(authDevice.getWifiDevice().IP, requestTone.getSource(), callback);
            } else if (TextUtils.equals("getVoiceRequestEndTone", command)) {
                DeviceCustomerSettingAction.getVoiceRequestEndTone(authDevice.getWifiDevice().IP, requestTone.getSource(), callback);
            }
        } else {

        }
    }

    public static void setVoiceLanguage(@Nullable AuthDevice authDevice, VoiceLanguage voiceLanguage, DeviceSettingActionCallback callback) {
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setVoiceLanguage(authDevice.getWifiDevice().IP, voiceLanguage.getSource(), voiceLanguage.getLocale(), callback);
        } else {

        }
    }

    public static void getVoiceLanguage(@Nullable AuthDevice authDevice, VoiceLanguage voiceLanguage, DeviceSettingActionCallback callback) {
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getVoiceLanguage(authDevice.getWifiDevice().IP, voiceLanguage.getSource(), callback);
        } else {

        }
    }

    public static void getSupportedVoiceLanguage(@Nullable AuthDevice authDevice, VoiceLanguage voiceLanguage, DeviceSettingActionCallback callback) {
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getSupportedVoiceLanguage(authDevice.getWifiDevice().IP, voiceLanguage.getSource(), callback);
        } else {

        }
    }

    public static void getLWAState(@Nullable AuthDevice authDevice, VoiceLanguage voiceLanguage, DeviceSettingActionCallback callback) {
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getVoiceLanguage(authDevice.getWifiDevice().IP, voiceLanguage.getSource(), callback);
        } else {

        }
    }

    public static void rename(@Nullable AuthDevice device, String newName, DeviceSettingActionCallback callback) {
        if (device == null) return;
        if (device.isSingle()) {
            if (device.getDeviceType() == AuthDevice.DeviceType.Wifi) {
                DeviceCustomerSettingAction.makeDeviceSetDeviceName(device.getWifiDevice().IP, newName, callback);
            } else {
                String payload = String.format("{\"device_name\": \"%s\"}", newName);
                HBBLEManagerCompat.getInstance().sendBLEControlCmd(device.getBleDevice(), StatisticConstant.SET_DEVICE_NAME, payload, convertBLEListener(device, StatisticConstant.SET_DEVICE_NAME, callback));
            }
        } else {
            if (device.getDeviceType() == AuthDevice.DeviceType.Wifi) {
                DeviceCustomerSettingAction.renameGroup(device.getWifiDevice().IP, newName, callback);
            } else {
                int code = StatisticConstant.LP_RENAME_GROUP;
                String payload = String.format("{\"name\": \"%s\"}", newName);
                if (Util.isFromControlPage) {

                    HBBLEManagerCompat.getInstance().sendBLEControlCmd(device.getBleDevice(), code, payload, convertBLEListener(device, StatisticConstant.LP_RENAME_GROUP, callback));
                } else {
                }

            }
        }

    }

    public static void getDeviceInfo(@Nullable AuthDevice authDevice, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getDeviceInfo(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.LP_GET_DEVICEINFO_CMD, "", convertBLEListener(authDevice, StatisticConstant.LP_GET_DEVICEINFO_CMD, callback));
        }
    }

    public static void clearHistoryOneOSVersion(@Nullable AuthDevice authDevice, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.clearHistoryOneOSVersion(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.CLEAR_HISTORY_ONE_OS_VERSION, "", convertBLEListener(authDevice, StatisticConstant.LP_GET_DEVICEINFO_CMD, callback));
        }
    }

    public static void unregisterAllListener() {
        HBBLEManagerCompat.getInstance().unregisterAllListener();
    }

    public static void registerDeviceStatusChangedListener(HBBluetoothDevice bleDevice, IDeviceStatusChangedCallback callback) {
        HBBLEManagerCompat.getInstance().registerCharacteristicListener(bleDevice, new HBBLEDeviceStatusChangedListener() {
            @Override
            public void onFailed(Exception e) {

            }

            @Override
            public void onSuccess(int responseCode, String result) {
                if (callback != null) callback.onSuccess(responseCode, result);
            }
        });
    }

    public static void setDjEvent(AuthDevice authDevice, String value, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setDjEvent(authDevice.getWifiDevice().IP, value, callback);
        } else {
            String payload = String.format("{\"set\": \"%s\"}}", value);
            LogsUtil.d("sendBLEControlCmd", "setDjEvent Type Ble send payload: " + payload);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_DJ_EVENT, payload, convertBLEListener(authDevice, StatisticConstant.SET_DJ_EVENT, callback));
        }
    }

    public static void setDJPad(AuthDevice authDevice, String type, String set1, String set2, String set3, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setDJPad(authDevice.getWifiDevice().IP, type, set1, set2, set3, callback);
        } else {

        }
    }

    public static void setDJPad(AuthDevice authDevice, DJPad djPad, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setDJPad(authDevice.getWifiDevice().IP, djPad, callback);
        } else {
            List<Integer> curSet = djPad.getCurSet();
            StringBuilder sb = new StringBuilder();
            sb.append("[");
            for (int i = 0; i < curSet.size(); i++) {
                sb.append(curSet.get(i));
                if (i < curSet.size() - 1) {
                    sb.append(", ");
                }
            }
            sb.append("]");
            String formatCurSet = sb.toString();

            String customizeSetPayload = GsonUtils.toJson(djPad.getCustomizeSet());
            LogsUtil.d("sendBLEControlCmd", "formatCurSet: " + formatCurSet);
            String payload = String.format("{\"type\":\"%s\",\"status\":\"%s\",\"cur_set\":\"%s\",\"customize_set\":%s}", djPad.getType(), djPad.getStatus(), formatCurSet, customizeSetPayload);
            LogsUtil.d("sendBLEControlCmd", "setDJPad Type Ble send payload: " + payload);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_DJ_PAD, payload, convertBLEListener(authDevice, StatisticConstant.SET_DJ_PAD, callback));
        }
    }

    public static void getDJPad(AuthDevice authDevice, DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getDJPad(authDevice.getWifiDevice().IP, callback);
        } else {
            LogsUtil.d("sendBLEControlCmd", "getDJPad Type Ble");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_DJ_PAD, "", convertBLEListener(authDevice, StatisticConstant.GET_DJ_PAD, callback));
        }
    }

    public static void getLWAState(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getLWAState(authDevice.getWifiDevice().IP, callback);
        }
    }

    public static void setLWAAuthCode(@Nullable AuthDevice authDevice, LWAInfo lwaInfo, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setLWAAuthCode(authDevice.getWifiDevice().IP, lwaInfo.getAuthorizationCode(), lwaInfo.getClientId(), lwaInfo.getRedirectUri(), callback);
        }
    }

    public static void requestLWALogout(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.requestLWALogout(authDevice.getWifiDevice().IP, callback);
        }
    }

    public static void setAutoPowerOffTimer(@Nullable AuthDevice authDevice, int time, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setAutoPowerOffTimer(authDevice.getWifiDevice().IP, time, callback);
        } else {
            String payload = String.format("{\"timer\":%d}", time);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_AUTO_POWER_OFF_TIMER, payload, convertBLEListener(authDevice, StatisticConstant.SET_AUTO_POWER_OFF_TIMER, callback));
        }
    }

    public static void getAutoPowerOffTimer(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getAutoPowerOffTimer(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_AUTO_POWER_OFF_TIMER, "", convertBLEListener(authDevice, StatisticConstant.GET_AUTO_POWER_OFF_TIMER, callback));
        }
    }

    public static void setFeedbackToneConfig(@Nullable AuthDevice authDevice, boolean checked, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setFeedbackToneConfig(authDevice.getWifiDevice().IP, checked, callback);
        } else {
            String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_FEED_BACK_TONE_CONFIG, payload, convertBLEListener(authDevice, StatisticConstant.SET_FEED_BACK_TONE_CONFIG, callback));
        }
    }

    public static void getFeedbackToneConfig(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getFeedbackToneConfig(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_FEED_BACK_TONE_CONFIG, "", convertBLEListener(authDevice, StatisticConstant.GET_FEED_BACK_TONE_CONFIG, callback));
        }
    }

    public static void setBatterySavingMode(@Nullable AuthDevice authDevice, boolean checked, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setBatterySavingMode(authDevice.getWifiDevice().IP, checked, callback);
        } else {
            String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_BATTERY_SAVING_MODE, payload, convertBLEListener(authDevice, StatisticConstant.SET_BATTERY_SAVING_MODE, callback));
        }
    }

    public static void getBatterySavingMode(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getBatterySavingMode(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_BATTERY_SAVING_MODE, "", convertBLEListener(authDevice, StatisticConstant.GET_BATTERY_SAVING_MODE, callback));
        }
    }

    public static void requestGoogleLogout(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.requestGoogleLogout(authDevice.getWifiDevice().IP, callback);
        }
    }

    public static void getProductUsage(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getProductUsage(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_PRODUCT_USAGE, "", convertBLEListener(authDevice, StatisticConstant.GET_PRODUCT_USAGE, callback));
        }
    }

    public static void getFeatureSupport(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDeviceGetFeatureSupport(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_FEATURE_SUPPORT, "", convertBLEListener(authDevice, StatisticConstant.GET_FEATURE_SUPPORT, callback));
        }
    }

    public static void enterAuracast(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.enterAuracast(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.ENTER_AURACAST, "", convertBLEListener(authDevice, StatisticConstant.GET_FEATURE_SUPPORT, callback));
        }
    }

    public static void exitAuracast(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.exitAuracast(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.EXIT_AURACAST, "", convertBLEListener(authDevice, StatisticConstant.GET_FEATURE_SUPPORT, callback));
        }
    }

    public static void getSmartButtonConfig(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDeviceGetSmartBtnConfig(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_SMART_BUTTON_CONFIG, "", convertBLEListener(authDevice, StatisticConstant.GET_SMART_BUTTON_CONFIG, callback));
        }
    }

    public static void setSmartButtonConfig(@Nullable AuthDevice authDevice, String configJsonInfo, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDeviceSetSmartBtnConfig(authDevice.getWifiDevice().IP, configJsonInfo, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_SMART_BUTTON_CONFIG, configJsonInfo, convertBLEListener(authDevice, StatisticConstant.SET_SMART_BUTTON_CONFIG, callback));
        }
    }

    public static void previewSoundscape(@Nullable AuthDevice authDevice, int soundscape_id, int duration, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDevicePreviewSoundscape(authDevice.getWifiDevice().IP, soundscape_id, duration, callback);
        } else {
            String payload = String.format("{\"soundscape_id\": %d,\"duration\": %d}", soundscape_id, duration);
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.PREVIEW_SOUND_SCAPE, payload, convertBLEListener(authDevice, StatisticConstant.PREVIEW_SOUND_SCAPE, callback));
        }
    }

    public static void cancelSoundscape(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.makeDeviceCancelSoundscape(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.CANCEL_SOUND_SCAPE, "", convertBLEListener(authDevice, StatisticConstant.CANCEL_SOUND_SCAPE, callback));
        }
    }

    public static void setSmartMode(@Nullable AuthDevice authDevice, boolean checked, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setSmartMode(authDevice.getWifiDevice().IP, checked, callback);
        } else {
            String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_SMART_MODE, payload, convertBLEListener(authDevice, StatisticConstant.SET_SMART_MODE, callback));
        }
    }

    public static void getSmartMode(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getSmartMode(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_SMART_MODE, "", convertBLEListener(authDevice, StatisticConstant.GET_SMART_MODE, callback));
        }
    }

    public static void setPersonalListeningMode(@Nullable AuthDevice authDevice, boolean checked, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setPersonalListeningMode(authDevice.getWifiDevice().IP, checked, callback);
        } else {
            String payload = String.format("{\"status\": \"%s\"}", checked ? "on" : "off");
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_PERSONAL_LISTENING_MODE, payload, convertBLEListener(authDevice, StatisticConstant.SET_PERSONAL_LISTENING_MODE, callback));
        }
    }

    public static void sendAppController(@Nullable AuthDevice authDevice, String key_value, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        String payload = String.format("{\"key_pressed\": \"%s\"}", key_value);
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.httppostASPRequest(authDevice.getWifiDevice().IP, "sendAppController", payload, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SEND_APP_CONTROLLER, payload, convertBLEListener(authDevice, StatisticConstant.SET_PERSONAL_LISTENING_MODE, callback));
        }
    }

    public static void getPersonalListeningMode(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getPersonalListeningMode(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_PERSONAL_LISTENING_MODE, "", convertBLEListener(authDevice, StatisticConstant.GET_PERSONAL_LISTENING_MODE, callback));
        }
    }

    public static void getRearSpeakerStatus(@Nullable AuthDevice authDevice, @Nullable DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        LogsUtil.i(AppLogTagUtil.DEVICE_TAG, "getRearSpeakerStatus connType " + authDevice.getDeviceType());
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.getRearSpeakerStatus(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.GET_REAR_SPEAKER_STATUS, "", convertBLEListener(authDevice, StatisticConstant.GET_REAR_SPEAKER_STATUS, callback));
        }
    }

    public static void setIRLearn(@Nullable AuthDevice authDevice, @NotNull DeviceSettingActionCallback callback) {
        if (authDevice == null) return;
        if (authDevice.getDeviceType() == AuthDevice.DeviceType.Wifi) {
            DeviceCustomerSettingAction.setIRLearn(authDevice.getWifiDevice().IP, callback);
        } else {
            HBBLEManagerCompat.getInstance().sendBLEControlCmd(authDevice.getBleDevice(), StatisticConstant.SET_IR_LEARN, "", convertBLEListener(authDevice, StatisticConstant.SET_IR_LEARN, callback));
        }
    }

    public interface IDeviceStatusChangedCallback {
        void onSuccess(int responseCode, String content);

        void onFailure(Throwable e);
    }

}

