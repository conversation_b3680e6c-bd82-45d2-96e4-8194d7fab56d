package com.harman

import android.text.TextUtils
import com.wifiaudio.action.log.print_log.LogsUtil
import java.util.stream.Collectors


class CoulsonSetupModel {
    var coulsonEntry: String? = null
    var landOnService: String? = EventUtils.Dimension.EnumVAResult.FALSE.value
    var landOnServiceVa: String? = EventUtils.Dimension.EnumVAResult.FALSE.value
    var alexaCancel: String? = EventUtils.Dimension.EnumVAResult.FALSE.value
    var gvaCancel: String? = EventUtils.Dimension.EnumVAResult.FALSE.value
    var option: VaOptionItem? = null
    private var resultList: MutableList<VaResultItem>? = ArrayList()
    var gvaSetupStart: Long = DEFAULT_VA_SETUP_TIME
    var gvaSetupEnd: Long = DEFAULT_VA_SETUP_TIME
    var gvaSetupDuration: Long = DEFAULT_VA_SETUP_TIME
    var alexaSetupStart: Long = DEFAULT_VA_SETUP_TIME
    var alexaSetupEnd: Long = DEFAULT_VA_SETUP_TIME
    var alexaSetupDuration: Long = DEFAULT_VA_SETUP_TIME
    var coulsonSeq: String? = null


    fun getResultList(): List<VaResultItem>? {
        return resultList
    }

    fun setResultList(resultList: MutableList<VaResultItem>?) {
        this.resultList = resultList
    }

    fun newSetup(name: String) {
        var item = findVaResultItem(name)
        if (item == null) {
            item = VaResultItem()
            item.vaName = name
            item.isNewSetup = true
            resultList!!.add(item)
        } else {
            item.isNewSetup = true
        }
    }

    fun setupResult(name: String, isSuccess: Boolean) {
        val item = findVaResultItem(name)
        if (item != null) {
            item.isSuccess = isSuccess
        } else {
            LogsUtil.i("logEvent - EventName : setupResult findItem is null resultList = $resultList")
        }
    }

    private fun findVaResultItem(name: String): VaResultItem? {
        for (item in resultList!!) {
            if (name.equals(item.vaName, ignoreCase = true)) {
                return item
            }
        }
        return null
    }

    fun clear() {
        coulsonEntry = ""
        landOnService = EventUtils.Dimension.EnumVAResult.FALSE.value
        landOnServiceVa = EventUtils.Dimension.EnumVAResult.FALSE.value

        alexaCancel = EventUtils.Dimension.EnumVAResult.FALSE.value
        gvaCancel = EventUtils.Dimension.EnumVAResult.FALSE.value
        option = null
        if (resultList != null) {
            resultList!!.clear()
        }
        gvaSetupStart = DEFAULT_VA_SETUP_TIME
        gvaSetupEnd = DEFAULT_VA_SETUP_TIME
        gvaSetupDuration = DEFAULT_VA_SETUP_TIME
        alexaSetupStart = DEFAULT_VA_SETUP_TIME
        alexaSetupEnd = DEFAULT_VA_SETUP_TIME
        alexaSetupDuration = DEFAULT_VA_SETUP_TIME
    }

    val isGvaTimeValid: Boolean
        get() = gvaSetupStart > DEFAULT_VA_SETUP_TIME && gvaSetupEnd > DEFAULT_VA_SETUP_TIME && gvaSetupDuration > DEFAULT_VA_SETUP_TIME

    val isAlexaTimeValid: Boolean
        get() = alexaSetupStart > DEFAULT_VA_SETUP_TIME && alexaSetupEnd > DEFAULT_VA_SETUP_TIME && alexaSetupDuration > DEFAULT_VA_SETUP_TIME

    val isDataValid: Boolean
        get() {
            if (TextUtils.isEmpty(coulsonEntry)) {
                LogsUtil.i("logEvent - EventName : coulsonEntry is blank, not submit ")
                return false
            }
            if (option == null) {
                LogsUtil.i("logEvent - EventName : option is blank, submit ")
                //            return false;
            }

            if (resultList == null || resultList!!.isEmpty()) {
                LogsUtil.i("logEvent - EventName : resultList is blank, submit")
                //            return false;
            }

            return true
        }


    val isFlowFinish: Boolean
        get() {
            if (option == null) {
                LogsUtil.i("logEvent - EventName : isFlowFinish option is blank")
                return false
            }
            if (resultList == null) {
                LogsUtil.i("logEvent - EventName : isFlowFinish resultList is blank")
                return false
            }
            val list =
                resultList!!.stream().map { obj: VaResultItem -> obj.vaName }
                    .collect(Collectors.toList())
            LogsUtil.i("logEvent - EventName : isFlowFinish list " + list + " , option.getOptionName() " + option!!.optionName)

            if (option!!.expectResult != resultList!!.size) {
                LogsUtil.i("logEvent - EventName : isFlowFinish not equal " + option!!.expectResult + " , " + resultList!!.size)
                return false
            }
            return true
        }

    val finalResult: String
        get() {
            var sum = 0
            sum = if (resultList == null || resultList!!.isEmpty()) {
                0
            } else {
                resultList!!.stream().mapToInt { obj: VaResultItem -> obj.value }.sum()
            }
            val item = VAResulType.getItemByValue(sum)
            return if (item == null) "" else item.resultName
        }

    companion object {
        var DEFAULT_VA_SETUP_TIME: Long = 0L
    }
}
