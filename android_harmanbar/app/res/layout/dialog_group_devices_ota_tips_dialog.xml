<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.oobe.wifi.GroupDevicesOtaTipsDialog" />

        <import type="com.harman.bar.app.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:paddingTop="@dimen/dimen_20dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text_Title_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            android:text="@{dialog.title}"
            android:textColor="@color/fg_primary"
            app:layout_constraintBottom_toTopOf="@+id/iv_demo_img"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Bar 1300MK2 will be reconnected back to the system when software update is finished." />

        <ImageView
            android:id="@+id/iv_demo_img"
            android:layout_width="0dp"
            android:layout_height="262dp"
            app:imgUrl="@{dialog.demoImgUrl}"
            app:defaultResId="@{R.drawable.default_group_devices_ota_tips}"
            app:layout_constraintBottom_toTopOf="@+id/layout_tips"
            app:layout_constraintDimensionRatio="w,1125:646"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <LinearLayout
            android:id="@+id/layout_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_12dp"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dimen_44dp"
            android:paddingVertical="@dimen/dimen_24dp"
            app:layout_constraintTop_toBottomOf="@id/iv_demo_img">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dimen_20dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/dimen_24dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_alarm_fg_primary" />

                <TextView
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:gravity="start|center_vertical"
                    android:text="@string/update_takes_up_to_15_minutes"
                    android:textColor="@color/fg_primary" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/dimen_24dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_autorenew_fg_primary" />

                <TextView
                    style="@style/Text_Body_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:gravity="start|center_vertical"
                    android:text="@string/your_product_will_reboot_after_updating_is_finished"
                    android:textColor="@color/fg_primary" />
            </LinearLayout>

        </LinearLayout>

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_ok"
            style="@style/Button_Positive"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:onClick="@{() -> dialog.onBtnOkClick()}"
            app:btn_text="@string/jbl_OK"
            app:btn_text_font="Button"
            app:btn_type="btn_regular"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_tips"
            app:layout_constraintWidth_max="@dimen/dimen_255dp"
            app:textAllCaps="true" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>