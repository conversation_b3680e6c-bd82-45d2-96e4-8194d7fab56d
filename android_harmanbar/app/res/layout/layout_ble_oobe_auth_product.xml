<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.oobe.ble.BLEOOBEDialog" />

        <variable
            name="viewModel"
            type="com.harman.oobe.ble.BLEOOBEViewModel" />

        <variable
            name="flow"
            type="com.harman.oobe.ble.flow.IOOBEFlow" />

        <import type="com.harman.oobe.AuthUIState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/oobe_dialog_height"
        android:background="@drawable/radius_large_bg_card"
        android:paddingTop="@dimen/dimen_24dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text_Title_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            app:deviceOOBEAuthMessage="@{dialog.device}"
            android:textColor="@color/fg_primary"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_auth_touch"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:maxHeight="360dp"
            app:layout_constraintDimensionRatio="h,343:332"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:scaleType="centerCrop"
            app:deviceOOBEAuthDemo="@{dialog.device}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:src="@drawable/img_oobe_auth_placeholder_moment" />

        <ImageView
            android:id="@+id/iv_loading"
            android:layout_width="@dimen/dimen_36dp"
            android:layout_height="@dimen/dimen_36dp"
            android:layout_marginBottom="@dimen/dimen_24dp"
            android:src="@drawable/loading_fg_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_device"
            app:loadingStyle2="@{AuthUIState.READY == dialog.authUIStatus}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>