<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="fragment"
            type="com.harman.va.refactor.VAAlexaFragment" />
        <variable
            name="viewModel"
            type="com.harman.va.refactor.VAViewModel" />

        <import type="com.harman.va.EnumViewStyle" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> fragment.onBackBtnClick()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/select_icon_arrow_l"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title_label"
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/android_translated_amazon_alexa"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>




        <androidx.core.widget.NestedScrollView
            android:id="@+id/sv_scrollbox"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_title_bar"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/width_10"
                android:layout_marginTop="@dimen/width_20"
                android:layout_marginEnd="@dimen/width_10"
                android:layout_marginBottom="@dimen/width_20"
                android:orientation="vertical"
                android:paddingBottom="@dimen/width_10">

                <ImageView
                    android:id="@+id/iv_logo"
                    android:layout_width="@dimen/width_112"
                    android:layout_height="@dimen/width_112"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/width_60"
                    android:src="@drawable/icon_white_round_amazon_alexa" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/progressbar"
                    android:layout_width="@dimen/width_32"
                    android:layout_height="@dimen/width_32"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/width_50"
                    android:src="@drawable/svg_icon_vector_loading"
                    app:isVisible="@{EnumViewStyle.SHOW == viewModel.viewStyleLoadingAlexa}"
                    app:loadingStyle1="@{EnumViewStyle.SHOW == viewModel.viewStyleLoadingAlexa}" />

                <LinearLayout
                    android:id="@+id/layout_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/width_40"
                    android:orientation="vertical"
                    android:padding="@dimen/width_16"
                    app:isVisible="@{EnumViewStyle.SHOW == viewModel.viewStyleDetailAlexa}">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:fontFamily="@font/open_sans_bold"
                        android:gravity="center_vertical"
                        android:minHeight="@dimen/width_18"
                        android:text="@string/Wi_Fi_Streaming"
                        android:textColor="@color/fg_primary"
                        android:textSize="@dimen/font_16" />

                    <RelativeLayout
                        android:id="@+id/layout_chromecast_built_in"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_100"
                        android:onClick="@{()-> fragment.clickMusicStreamingWithAlexaCast()}"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">


                        <ImageView
                            android:id="@+id/ic_more"
                            android:layout_width="@dimen/width_20"
                            android:layout_height="@dimen/width_20"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="5dp"
                            android:src="@drawable/icon_arrow_outward" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/width_16"
                            android:layout_toStartOf="@id/ic_more"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:fontFamily="@font/open_sans_semibold"
                                android:gravity="center_vertical"
                                android:text="@string/music_streaming_with_alexa_cast"
                                android:textColor="@color/fg_primary"
                                android:textSize="@dimen/font_16" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_gravity="start"
                                android:layout_marginTop="@dimen/width_10"
                                android:layout_marginBottom="@dimen/width_5"
                                android:fontFamily="@font/open_sans_regular"
                                android:gravity="start|center_vertical"
                                android:text="@string/cast_music_to_your_speaker_from_the_amazon_music_app"
                                android:textColor="@color/fg_secondary"
                                android:textSize="@dimen/font_12" />
                        </LinearLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/layout_multi_room_music"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_100"
                        android:onClick="@{()-> fragment.clickMultiRoomMusic()}"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">


                        <ImageView
                            android:id="@+id/iv_multi_room_more"
                            android:layout_width="@dimen/width_20"
                            android:layout_height="@dimen/width_20"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="5dp"
                            android:src="@drawable/icon_arrow_outward" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/width_16"
                            android:layout_toStartOf="@id/iv_multi_room_more"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:fontFamily="@font/open_sans_semibold"
                                android:gravity="center_vertical"
                                android:text="@string/multi_room_music"
                                android:textColor="@color/fg_primary"
                                android:textSize="@dimen/font_16" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_gravity="start"
                                android:layout_marginTop="@dimen/width_5"
                                android:layout_marginBottom="@dimen/width_5"
                                android:fontFamily="@font/open_sans_regular"
                                android:gravity="start|center_vertical"
                                android:text="@string/multi_room_music_desc"
                                android:textColor="@color/fg_secondary"
                                android:textSize="@dimen/font_12" />
                        </LinearLayout>
                    </RelativeLayout>


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="@dimen/width_32"
                        android:layout_marginBottom="@dimen/width_16"
                        android:fontFamily="@font/open_sans_bold"
                        android:gravity="center_vertical"
                        android:minHeight="@dimen/width_18"
                        android:text="@string/harmanbar_voice_assistant"
                        android:textColor="@color/fg_primary"
                        android:textSize="@dimen/font_16" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_100"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:fontFamily="@font/open_sans_semibold"
                            android:gravity="center_vertical"
                            android:text="@string/try_saying"
                            android:textColor="@color/fg_primary"
                            android:textSize="@dimen/font_16" />

                        <TextView
                            android:id="@+id/tv_more_features"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/width_5"
                            android:layout_marginBottom="@dimen/width_5"
                            android:fontFamily="@font/open_sans_regular"
                            android:gravity="center_vertical"
                            android:text="@string/alexa_va_try_saying_desc"
                            android:textColor="@color/fg_secondary"
                            android:textSize="@dimen/font_12" />

                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/layout_language"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_76"
                        android:onClick="@{()-> fragment.clickVoiceLanguage()}"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/width_20"
                            android:gravity="center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:fontFamily="@font/open_sans_semibold"
                                android:gravity="center_vertical"
                                android:text="@string/voice_language"
                                android:textColor="@color/fg_primary"
                                android:textSize="@dimen/font_16" />

                            <TextView
                                android:id="@+id/tv_language"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/width_5"
                                android:ellipsize="end"
                                android:fontFamily="@font/open_sans_bold"
                                android:lines="1"
                                android:singleLine="true"
                                android:text="@{fragment.voiceLanguage}"
                                android:textColor="@color/fg_activate"
                                android:textSize="@dimen/font_12" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="@dimen/width_20"
                            android:layout_height="@dimen/width_20"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="5dp"
                            android:src="@drawable/icon_arrow_outward" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"

                        android:orientation="vertical"
                        android:padding="@dimen/width_16"
                        android:paddingTop="@dimen/width_10">

                        <LinearLayout
                            android:id="@+id/layout_send_optin"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/width_16"
                                android:layout_toStartOf="@+id/tv_sub_title"
                                android:layout_weight="3"
                                android:fontFamily="@font/open_sans_semibold"
                                android:text="@string/start_of_request"
                                android:textColor="@color/fg_primary"
                                android:textSize="@dimen/font_16" />

                            <Switch
                                android:id="@+id/switch_start_of_request"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:checked="@{fragment.startToneConfig}"

                                android:fontFamily="@font/open_sans_regular"
                                android:minWidth="@dimen/width_44"
                                android:minHeight="@dimen/width_24"
                                android:onClick="@{() -> viewModel.switchStartToneConfig()}"
                                android:thumb="@drawable/switch_thumb"
                                android:track="@drawable/switch_track"
                                tools:ignore="UseSwitchCompatOrMaterialXml" />
                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/width_5"
                            android:layout_marginBottom="@dimen/width_5"
                            android:fontFamily="@font/open_sans_regular"
                            android:gravity="center_vertical"
                            android:text="@string/start_of_request_desc"
                            android:textColor="@color/fg_secondary"
                            android:textSize="@dimen/font_12" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@drawable/shape_shade_list_round_12"

                        android:orientation="vertical"
                        android:padding="@dimen/width_16"
                        android:paddingTop="@dimen/width_10">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/width_16"
                                android:layout_toStartOf="@+id/tv_sub_title"
                                android:layout_weight="3"
                                android:fontFamily="@font/open_sans_semibold"
                                android:text="@string/end_of_request"
                                android:textColor="@color/fg_primary"
                                android:textSize="@dimen/font_16" />

                            <Switch
                                android:id="@+id/switch_end_of_request"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:checked="@{fragment.endToneConfig}"
                                android:fontFamily="@font/open_sans_regular"
                                android:minWidth="@dimen/width_44"
                                android:minHeight="@dimen/width_24"
                                android:onClick="@{() -> viewModel.switchEndToneConfig()}"
                                android:thumb="@drawable/switch_thumb"
                                android:track="@drawable/switch_track"
                                tools:ignore="UseSwitchCompatOrMaterialXml" />
                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/width_5"
                            android:layout_marginBottom="@dimen/width_5"
                            android:fontFamily="@font/open_sans_regular"
                            android:gravity="center_vertical"
                            android:text="@string/end_of_request_desc"
                            android:textColor="@color/fg_secondary"
                            android:textSize="@dimen/font_12" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_logout"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/width_52"
                        android:layout_marginTop="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:fontFamily="@font/open_sans_bold"
                        android:gravity="center"
                        android:lineHeight="20dp"
                        android:onClick="@{()-> fragment.clickLogout()}"
                        android:text="@string/log_out"
                        android:textAllCaps="true"
                        android:textColor="@color/red_1"
                        android:textSize="@dimen/font_14" />

                    <LinearLayout
                        android:id="@+id/layout_go_to_alexa_app"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/width_48"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_42"
                        android:background="@drawable/shape_shade_list_round_24"
                        android:gravity="center_vertical"
                        android:minWidth="@dimen/width_220"
                        android:onClick="@{()-> fragment.clickGoToAmazonAlexaApp()}"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/width_16"
                        android:paddingEnd="@dimen/width_16">

                        <ImageView
                            android:layout_width="@dimen/width_32"
                            android:layout_height="@dimen/width_32"
                            android:src="@drawable/icon_stream_service_alexa_app" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="@dimen/width_10"
                            android:fontFamily="@font/open_sans_regular"
                            android:gravity="center_vertical"
                            android:text="@string/go_to_amazon_alexa_app"
                            android:textColor="@color/fg_primary"
                            android:textSize="@dimen/font_14" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="12dp"
                        android:fontFamily="@font/open_sans_regular"
                        android:gravity="center"
                        android:text="@string/set_up_alexa_va_go_app"
                        android:textColor="@color/fg_secondary"
                        android:textSize="@dimen/font_14" />

                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>