<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.radio.PresetStationDialog" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text_Title_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dimen_24dp"
            android:text="@string/add_to_preset"
            android:textColor="@color/fg_primary" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/preset_station_recycle_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_done"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dimen_48dp"
            android:layout_marginBottom="@dimen/dimen_8dp"
            android:onClick="@{() -> dialog.onDoneClick()}"
            app:btn_text="@string/harmanbar_jbl_DONE"
            app:btn_type="btn_disable" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_cancel"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:onClick="@{() -> dialog.onCancelClick()}"
            app:btn_text="@string/cancel"
            app:btn_type="btn_text_button" />

    </LinearLayout>

</layout>