<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.radio.AdjustFrequencyDialog" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:minHeight="@dimen/dimen_240dp">

        <ImageView
            android:id="@+id/iv_frequency_handy_minus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{() -> dialog.onFreqHandyMinusClick()}"
            android:src="@drawable/ic_frequency_handy_minus"
            app:layout_constraintBottom_toBottomOf="@+id/tv_frequency_number"
            app:layout_constraintEnd_toStartOf="@+id/tv_frequency_number"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_frequency_number" />

        <TextView
            android:id="@+id/tv_frequency_number"
            style="@style/Text_Display_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_8dp"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:gravity="center"
            android:minWidth="@dimen/width_140"
            android:text="@{dialog.frequencyNum}"
            android:textColor="@color/fixed_white"
            app:layout_constraintEnd_toStartOf="@+id/iv_frequency_handy_plus"
            app:layout_constraintStart_toEndOf="@+id/iv_frequency_handy_minus"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="87.50" />

        <ImageView
            android:id="@+id/iv_frequency_handy_plus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{() -> dialog.onFreqHandyPlusClick()}"
            android:src="@drawable/ic_frequency_handy_plus"
            app:layout_constraintBottom_toBottomOf="@+id/tv_frequency_number"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_frequency_number"
            app:layout_constraintTop_toTopOf="@+id/tv_frequency_number" />

        <com.harman.radio.LoopScaleView
            android:id="@+id/loopScaleView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_84dp"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:visibility="visible"
            app:cursorColor="@color/fg_activate"
            app:layout_constraintTop_toBottomOf="@+id/tv_frequency_number"
            app:maxScaleValue="108"
            app:minScaleValue="87.5"
            app:scaleItemSize="10" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_ok"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="@dimen/dimen_48dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/width_76"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:onClick="@{() -> dialog.onOkClick()}"
            app:btn_text="@string/jbl_OK"
            app:btn_type="btn_regular"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loopScaleView" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>