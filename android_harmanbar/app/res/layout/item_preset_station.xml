<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="current"
            type="com.harman.command.partybox.gatt.radio.RadioInfo.Station" />

        <variable
            name="dialog"
            type="com.harman.radio.PresetStationDialog" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_root_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_60dp"
        android:background="@color/bg_card"
        android:paddingHorizontal="@dimen/dimen_20dp">

        <TextView
            android:id="@+id/tv_index"
            style="@style/Text_Caption_1_Regular"
            android:layout_width="@dimen/dimen_24dp"
            android:layout_height="@dimen/dimen_24dp"
            android:gravity="center"
            android:textColor="@color/fg_secondary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_station_name"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1" />

        <TextView
            android:id="@+id/tv_station_name"
            style="@style/Text_Body_Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/fg_primary"
            app:decoPresetStationName="@{current}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_radio_type"
            app:layout_constraintStart_toEndOf="@+id/tv_index"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="[Station name]" />

        <TextView
            android:id="@+id/tv_radio_type"
            style="@style/Text_Caption_2_Strong"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_4dp"
            android:background="@drawable/bg_radio_type"
            android:gravity="center"
            android:paddingHorizontal="6dp"
            android:paddingVertical="4dp"
            android:textColor="@color/fg_primary"
            app:decoPresetStationTag="@{current}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/rb_select"
            app:layout_constraintStart_toEndOf="@id/tv_station_name"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="FM" />

        <ImageView
            android:id="@+id/rb_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/selector_selected_unselected"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/svg_icon_security_selected" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>