<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/view_touch_outside"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_va_setup_dialog_bg"
        android:clickable="true"
        android:focusable="true"
        android:minHeight="598dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/poppins_bold"
            android:text="@string/your_voice_assistants_are_ready_just_say"
            android:textColor="@color/fg_primary"
            android:textSize="18dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toTopOf="@+id/btn_ok"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/iv_va_hey_google"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="63dp"
                    android:src="@mipmap/icon_va_hey_google_no_logo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_or"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/open_sans_bold_italic"
                    android:text="Or"
                    android:textColor="@color/fg_primary"
                    android:textSize="18sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_va_hey_google" />

                <ImageView
                    android:id="@+id/iv_va_hey_alexa"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:src="@mipmap/icon_va_hey_alexa_no_logo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_or" />

                <TextView
                    android:id="@+id/tv_play_music"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/open_sans_italic"
                    android:gravity="center"
                    android:text="@string/play_some_music"
                    android:textColor="@color/fg_primary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_va_hey_alexa" />

                <TextView
                    android:id="@+id/tv_turn_up_volume"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/open_sans_italic"
                    android:gravity="center"
                    android:text="@string/turn_up_the_volume"
                    android:textColor="@color/fg_primary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_play_music" />

                <TextView
                    android:id="@+id/tv_set_timer"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/open_sans_italic"
                    android:gravity="center"
                    android:text="@string/set_a_timer_for_15_minutes"
                    android:textColor="@color/fg_primary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_turn_up_volume" />

                <TextView
                    android:id="@+id/tv_stop_music"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/open_sans_italic"
                    android:gravity="center"
                    android:text="@string/stop_the_music"
                    android:textColor="@color/fg_primary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_set_timer" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.core.widget.NestedScrollView>


        <Button
            android:id="@+id/btn_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/btn_background_bg_s1"
            android:fontFamily="@font/poppins_bold"
            android:minWidth="@dimen/width_255"
            android:minHeight="@dimen/width_48"
            android:text="@string/harmanbar_jbl_DONE"
            android:textAllCaps="true"
            android:textColor="@color/bg_surface"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_tips"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="40dp"
            android:fontFamily="@font/poppins_regular"
            android:gravity="center"
            android:text="@string/discover_more_things_you_can_ask_alexa_or_google_assistant_learn_more"
            android:textColor="@color/half_white"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>