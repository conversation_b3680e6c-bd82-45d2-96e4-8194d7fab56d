<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="playerItem"
            type="com.harman.music.mini.MusicPlayerItem" />

        <variable
            name="momentItem"
            type="com.harman.music.mini.MomentPlayerItem" />

        <variable
            name="radioItem"
            type="com.harman.music.mini.RadioPlayerItem" />

        <variable
            name="listener"
            type="com.harman.music.mini.IMiniPlayerEventListener" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <include
            android:id="@+id/layout_mini_player"
            layout="@layout/item_mini_player"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:isVisible="@{null != playerItem}"
            app:item="@{playerItem}"
            app:listener="@{listener}" />

        <include
            android:id="@+id/layout_mini_moment"
            layout="@layout/item_mini_moment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:isVisible="@{null != momentItem}"
            app:item="@{momentItem}"
            app:listener="@{listener}" />

        <include
            android:id="@+id/layout_mini_radio"
            layout="@layout/item_mini_radio"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:isVisible="@{null != radioItem}"
            app:item="@{radioItem}"
            app:listener="@{listener}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>