<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="fragment"
            type="com.harman.va.refactor.VAGoogleFragment" />


        <import type="com.harman.va.EnumViewStyle" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface"
        android:fitsSystemWindows="true">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_44dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_8dp"
                android:onClick="@{() -> fragment.onBackBtnClick()}"
                android:padding="@dimen/dimen_10dp"
                android:src="@drawable/select_icon_arrow_l"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title_label"
                style="@style/Text_Title_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/harmanbar_google_assistant"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>




        <androidx.core.widget.NestedScrollView
            android:id="@+id/sv_scrollbox"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_title_bar"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/width_10"
                android:layout_marginTop="@dimen/width_24"
                android:layout_marginEnd="@dimen/width_10"
                android:layout_marginBottom="@dimen/width_24"
                android:orientation="vertical"
                android:paddingBottom="@dimen/width_10">

                <ImageView
                    android:layout_width="@dimen/width_112"
                    android:layout_height="@dimen/width_112"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/width_24"
                    android:src="@drawable/icon_va_google_assistant" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/progressbar"
                    android:layout_width="@dimen/width_32"
                    android:layout_height="@dimen/width_32"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/width_50"
                    android:src="@drawable/svg_icon_vector_loading"

                    app:isVisible="@{EnumViewStyle.SHOW == fragment.viewStyleLoading}"
                    app:loadingStyle1="@{EnumViewStyle.SHOW == fragment.viewStyleLoading}" />

                <LinearLayout
                    android:id="@+id/layout_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/width_40"
                    android:orientation="vertical"
                    android:padding="@dimen/width_16"
                    app:isVisible="@{EnumViewStyle.SHOW == fragment.viewStyleDetail}">

                    <TextView
                        style="@style/Text_Title_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center_vertical"
                        android:minHeight="@dimen/width_18"
                        android:text="@string/Wi_Fi_Streaming"
                        android:textColor="@color/fg_primary" />

                    <RelativeLayout
                        android:id="@+id/layout_chromecast_built_in"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_100"
                        android:onClick="@{() -> fragment.clickChromecastBuildIn()}"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">

                        <ImageView
                            android:id="@+id/ic_more"
                            android:layout_width="@dimen/width_24"
                            android:layout_height="@dimen/width_24"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="5dp"
                            android:src="@drawable/icon_arrow_outward" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/width_16"
                            android:layout_toStartOf="@id/ic_more"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="@dimen/width_22"
                                    android:layout_height="@dimen/width_22"
                                    android:src="@drawable/jbl_icon_chromecast" />

                                <TextView
                                    style="@style/Text_Body_Medium"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="@dimen/width_5"
                                    android:fontFamily="@font/poppins_semibold"
                                    android:gravity="center_vertical"
                                    android:text="@string/harmanbar_jbl_Chromecast_Built_In"
                                    android:textColor="@color/fg_primary" />
                            </LinearLayout>

                            <TextView
                                style="@style/Text_Caption_1_Regular"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/width_5"
                                android:layout_marginBottom="@dimen/width_5"
                                android:fontFamily="@font/poppins_regular"
                                android:gravity="start|center_vertical"
                                android:text="@string/google_assistant_cast_chromecast_build_in"
                                android:textColor="@color/fg_secondary" />
                        </LinearLayout>

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/layout_multi_room_music"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_100"
                        android:onClick="@{() -> fragment.clickMultiRoomMusic()}"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">


                        <ImageView
                            android:id="@+id/iv_multi_room_more"
                            android:layout_width="@dimen/width_24"
                            android:layout_height="@dimen/width_24"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="5dp"
                            android:src="@drawable/icon_arrow_outward" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/width_16"
                            android:layout_toStartOf="@id/iv_multi_room_more"
                            android:orientation="vertical">

                            <TextView
                                style="@style/Text_Body_Medium"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:fontFamily="@font/poppins_semibold"
                                android:gravity="center_vertical"
                                android:text="@string/how_to_create_multiroom_group"
                                android:textColor="@color/fg_primary" />

                            <TextView
                                style="@style/Text_Caption_1_Regular"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_gravity="start"
                                android:layout_marginTop="@dimen/width_5"
                                android:layout_marginBottom="@dimen/width_5"
                                android:fontFamily="@font/poppins_regular"
                                android:gravity="start|center_vertical"
                                android:text="@string/google_multi_room_music_desc"
                                android:textColor="@color/fg_secondary" />
                        </LinearLayout>
                    </RelativeLayout>

                    <TextView
                        style="@style/Text_Title_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="@dimen/width_32"
                        android:layout_marginBottom="@dimen/width_16"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center_vertical"
                        android:minHeight="@dimen/width_18"
                        android:text="@string/harmanbar_google_assistant"
                        android:textColor="@color/fg_primary" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@drawable/shape_shade_list_round_12"
                        android:minHeight="@dimen/width_100"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">

                        <TextView
                            style="@style/Text_Body_Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:fontFamily="@font/poppins_semibold"
                            android:gravity="center_vertical"
                            android:text="@string/try_saying"
                            android:textColor="@color/fg_primary" />

                        <TextView
                            android:id="@+id/tv_more_features"
                            style="@style/Text_Caption_1_Regular"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/width_5"
                            android:layout_marginBottom="@dimen/width_5"
                            android:fontFamily="@font/poppins_regular"
                            android:gravity="center_vertical"
                            android:text="@string/try_saying_this_desc"
                            android:textColor="@color/fg_secondary" />

                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/layout_my_assistant_fragment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_16"
                        android:layout_marginBottom="@dimen/width_16"
                        android:background="@drawable/shape_shade_list_round_12"

                        android:onClick="@{() -> fragment.clickMyAssistantActivity()}"
                        android:orientation="vertical"
                        android:padding="@dimen/width_16">


                        <ImageView
                            android:id="@+id/iv_assistant_more"
                            android:layout_width="@dimen/width_24"
                            android:layout_height="@dimen/width_24"
                            android:layout_alignParentEnd="true"

                            android:src="@drawable/icon_arrow_outward" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"

                            android:layout_marginEnd="@dimen/width_16"
                            android:layout_toStartOf="@id/iv_assistant_more"
                            android:orientation="vertical">

                            <TextView
                                style="@style/Text_Body_Medium"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:fontFamily="@font/poppins_semibold"
                                android:gravity="center_vertical"
                                android:text="@string/google_assistant_activity"
                                android:textColor="@color/fg_primary" />

                            <TextView
                                style="@style/Text_Caption_1_Regular"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_gravity="start"
                                android:layout_marginTop="@dimen/width_5"
                                android:layout_marginBottom="@dimen/width_5"
                                android:fontFamily="@font/poppins_regular"
                                android:gravity="start|center_vertical"
                                android:text="@string/View_and_manage_your_data"
                                android:textColor="@color/fg_secondary" />
                        </LinearLayout>
                    </RelativeLayout>

                    <TextView
                        android:id="@+id/tv_logout"
                        style="@style/Text_Button"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/width_52"

                        android:background="@drawable/shape_shade_list_round_12"
                        android:fontFamily="@font/poppins_bold"
                        android:gravity="center"
                        android:lineHeight="20dp"
                        android:onClick="@{() -> fragment.clickLogout()}"
                        android:text="@string/log_out"
                        android:textAllCaps="true"
                        android:textColor="@color/red_1" />

                    <LinearLayout
                        android:id="@+id/layout_go_to_google_app"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/width_48"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/width_40"
                        android:background="@drawable/shape_shade_list_round_24"
                        android:gravity="center_vertical"
                        android:minWidth="@dimen/width_220"
                        android:onClick="@{() -> fragment.clickGoToGoogleApp()}"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/width_16"
                        android:paddingEnd="@dimen/width_16">

                        <ImageView
                            android:layout_width="@dimen/width_32"
                            android:layout_height="@dimen/width_32"
                            android:src="@drawable/ic_google_app_oval" />

                        <TextView
                            style="@style/Text_Body_Regular"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="@dimen/width_10"
                            android:fontFamily="@font/poppins_regular"
                            android:gravity="center_vertical"
                            android:text="@string/go_to_google_home_app"
                            android:textColor="@color/fg_primary" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_go_to_google_app_desc"
                        style="@style/Text_Caption_1_Regular"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="12dp"
                        android:fontFamily="@font/poppins_regular"
                        android:gravity="center"
                        android:text="@string/set_up_google_home_go_app"
                        android:textColor="@color/fg_secondary" />
                </LinearLayout>
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>