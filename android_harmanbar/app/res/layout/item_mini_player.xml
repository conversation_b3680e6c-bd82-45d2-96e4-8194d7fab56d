<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="item"
            type="com.harman.music.mini.MusicPlayerItem" />

        <variable
            name="listener"
            type="com.harman.music.mini.IMiniPlayerEventListener" />

        <import type="com.harman.bar.app.R" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{() -> listener.onRootLayoutClick(item)}"
        android:orientation="vertical"
        app:setBackgroundResource="@{item.backgroundRes}"
        tools:background="@drawable/radius_medium_sp_player">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dimen_8dp"
            android:clipChildren="false"
            android:orientation="horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_cover"
                android:layout_width="@dimen/dimen_58dp"
                android:layout_height="@dimen/dimen_58dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dimen_12dp"
                android:clipChildren="false"
                android:background="@drawable/radius_medium_fixed_blc_20">

                <ImageView
                    android:id="@+id/ic_music_source"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:setImgRes="@{item.inputSourceImgRes}"
                    tools:src="@drawable/ic_input_source_bt_small" />

                <ImageView
                    android:id="@+id/iv_album_cover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"/>

                <ImageView
                    android:id="@+id/ic_mix_with_music"
                    android:layout_width="@dimen/dimen_26dp"
                    android:layout_height="@dimen/dimen_26dp"
                    android:layout_marginEnd="-4dp"
                    android:layout_marginBottom="-4dp"
                    app:isVisible="@{item.isMomentMixWithMusic}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:setImgRes="@{item.mixWithMomentImgRes}"
                    tools:src="@drawable/ic_moment_forest_mix" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_infos"
                android:layout_width="0dp"
                android:layout_height="@dimen/dimen_58dp"
                android:layout_marginStart="@dimen/dimen_12dp"
                android:layout_marginEnd="@dimen/dimen_12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_device"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toTopOf="@+id/tv_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed">

                    <ImageView
                        android:id="@+id/ic_device_placeholder"
                        android:layout_width="7.78dp"
                        android:layout_height="10dp"
                        android:src="@drawable/ic_device_small_fixed_white_50"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/tv_spk_name"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_spk_name"
                        style="@style/Text_Caption_2_Strong"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_2dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{item.speakerName}"
                        android:textColor="@color/fixed_white_50"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/ic_device_placeholder"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Spk_Name" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/Text_Body_Strong"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{item.title}"
                    android:textColor="@color/fixed_white"
                    app:layout_constraintBottom_toTopOf="@+id/tv_sub_title"
                    app:layout_constraintTop_toBottomOf="@id/layout_device"
                    tools:text="Song Name" />

                <TextView
                    android:id="@+id/tv_sub_title"
                    style="@style/Text_Caption_1_Regular"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{item.subTitle}"
                    android:textColor="@color/fixed_white_50"
                    app:isVisible="@{item.isSubTitleVisible}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_title"
                    tools:text="Artist Name"
                    tools:visibility="gone" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_play_state"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_marginEnd="@dimen/dimen_12dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_player"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:onClick="@{() -> listener.onPlayPauseBtnClick(item)}"
                    app:isVisible="@{item.isLayoutPlayerVisible}">

                    <com.wifiaudio.view.component.CircleProgress
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:innerCircleColor="@color/fixed_white_10"
                        app:isClockwise="true"
                        app:isRound="false"
                        app:isShowPercentText="false"
                        app:liveProgress="@{item.progress}"
                        app:maxProgress="100"
                        app:ringColor="@color/fixed_white_10"
                        app:ringProgressColor="@color/fixed_white"
                        app:ringWidth="2.5dp"
                        app:startAngle="-90"
                        app:useAnimation="false" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:setImgRes="@{item.playStateImgRes}"
                        tools:src="@drawable/ic_mini_player_pause" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <ImageView
                    android:id="@+id/iv_transitioning"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:isVisible="@{item.isTransitioning}"
                    app:setGif="@{com.wifiaudio.R.drawable.play_transitioning}" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_volume"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_12dp"
            android:layout_marginBottom="@dimen/dimen_12dp">

            <ImageView
                android:id="@+id/ic_volume"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_player_volume" />

            <SeekBar
                android:id="@+id/sb_volume"
                android:layout_width="0dp"
                android:layout_height="@dimen/dimen_24dp"
                android:layout_marginStart="@dimen/dimen_12dp"
                android:max="100"
                android:maxHeight="@dimen/dimen_4dp"
                android:minHeight="@dimen/dimen_4dp"
                android:paddingVertical="@dimen/dimen_8dp"
                android:paddingStart="0dp"
                android:paddingEnd="0dp"
                android:progressDrawable="@drawable/progressbar_fixed_white_fixed_white_10"
                android:thumb="@drawable/thumb_radius_12_fixed_white"
                android:thumbOffset="0dp"
                android:splitTrack="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ic_volume"
                app:layout_constraintTop_toTopOf="parent"
                tools:progress="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>
</layout>