<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_alignParentBottom="true"
    android:clickable="true"
    android:focusable="true">

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/width_330"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_ble_bg">

        <RelativeLayout
            android:id="@+id/header_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/width_15">

            <ImageView
                android:id="@+id/iv_logo"
                android:layout_width="@dimen/width_30"
                android:layout_height="@dimen/width_30"
                android:layout_centerHorizontal="true"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text_Body_Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginStart="@dimen/width_30"
                android:gravity="center"
                android:text="@string/harmanbar_adddevice_Are_you_sure_"
                android:visibility="gone"
                android:textColor="@color/fg_primary" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tv_label_1"
            style="@style/Text_Title_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/btm_layout"
            android:layout_below="@+id/header_layout"
            android:layout_marginStart="@dimen/width_20"
            android:layout_marginTop="@dimen/width_20"
            android:layout_marginEnd="@dimen/width_20"
            android:layout_marginBottom="@dimen/width_20"
            android:fontFamily="@font/poppins_regular"
            android:gravity="start"
            android:text="@string/exit_google_assistant_setup_content"
            android:textColor="@color/fg_primary" />

        <RelativeLayout
            android:id="@+id/btm_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/width_30"
            android:gravity="center">

            <Button
                style="@style/Text_Button"
                android:id="@+id/btn_enable"
                android:layout_width="@dimen/width_255"
                android:layout_height="@dimen/width_48"
                android:layout_centerHorizontal="true"
                android:background="@drawable/btn_background_bg_s1"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/continue_setup_not_question_remark"
                android:textColor="@color/fg_inverse" />

            <Button
                style="@style/Text_Button"
                android:id="@+id/btn_cancel"
                android:layout_width="@dimen/width_255"
                android:layout_height="@dimen/width_48"
                android:layout_below="@+id/btn_enable"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/width_10"
                android:background="@null"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/harmanbar_jbl_EXIT"
                android:textColor="@color/fg_primary" />

        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>