<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="-1dp"
        android:layout_marginEnd="-1dp"
        android:background="@drawable/shape_ble_bg"
        android:orientation="vertical"
        android:paddingStart="@dimen/width_16"
        android:paddingTop="@dimen/width_16"
        android:paddingEnd="@dimen/width_16">


        <include
            android:id="@+id/vheader"
            layout="@layout/item_ble_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_content"
            style="@style/Text_Title_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:layout_marginTop="10dp"

            android:gravity="start"

            android:paddingStart="@dimen/width_10"
            android:paddingEnd="@dimen/width_10"
            android:singleLine="false"
            android:textColor="@color/fg_primary"
            tools:text="After logging out, %s will no longer be accessible on this product. Would you like to log out?" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"

            android:layout_marginBottom="48dp">

            <Button
                android:id="@+id/btn_restore"
                style="@style/Text_Button"
                android:layout_width="@dimen/width_255"
                android:layout_height="@dimen/width_48"
                android:layout_centerHorizontal="true"
                android:background="@drawable/bg_on_card_radius_24"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="RESTORE"
                android:textColor="@color/red_1" />

            <Button
                android:id="@+id/btn_cancel"
                style="@style/Text_Button"
                android:layout_width="@dimen/width_255"
                android:layout_height="@dimen/width_48"
                android:layout_below="@+id/btn_restore"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/width_10"
                android:background="@null"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="CANCEL"
                android:textColor="@color/fg_primary" />

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>