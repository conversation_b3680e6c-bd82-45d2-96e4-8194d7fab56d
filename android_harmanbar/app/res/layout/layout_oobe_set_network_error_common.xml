<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.oobe.wifi.WiFiOOBEDialog" />

        <variable
            name="viewModel"
            type="com.harman.oobe.wifi.WiFiOOBEViewModel" />

        <variable
            name="flow"
            type="com.harman.oobe.wifi.flow.IOOBEFlow" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/oobe_dialog_height"
        android:background="@drawable/radius_large_bg_card"
        android:paddingTop="@dimen/dimen_24dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            app:layout_constraintBottom_toTopOf="@+id/ic_no_wifi"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_label"
                style="@style/Text_Title_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_10dp"
                android:text="@string/harmanbar_jbl_Sorry__we_are_unable_to_connect_your_speaker_to_Wi_Fi_"
                android:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/iv_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="@{() -> flow.onClose()}"
                android:src="@drawable/ic_close_fg_secondary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <ImageView
            android:id="@+id/ic_no_wifi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_52dp"
            android:layout_marginBottom="@dimen/dimen_40dp"
            android:src="@drawable/svg_icon_no_wifi"
            app:layout_constraintBottom_toTopOf="@+id/btn_try_again"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_title" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_try_again"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_8dp"
            android:onClick="@{() -> flow.onRetry()}"
            app:btn_text="@string/jbl_try_again"
            app:btn_type="btn_regular"
            app:layout_constraintBottom_toTopOf="@+id/btn_faq"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ic_no_wifi" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_faq"
            android:layout_width="@dimen/dimen_255dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_32dp"
            android:onClick="@{() -> flow.onGetHelpClick()}"
            app:btn_rear_icon_res="@drawable/icon_arrow_outward"
            app:btn_text="@string/jbl_GET_HELP"
            app:btn_type="btn_text_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>