<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/view_touch_outside"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_va_setup_dialog_bg"
        android:clickable="true"
        android:focusable="true"
        android:minHeight="598dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_marginBottom="103dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/poppins_bold"
            android:text="@string/alexa_is_ready_let_us_set_up_google_assistant_in_the_google_home_app"
            android:textColor="@color/fg_primary"
            android:textSize="18dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" app:layout_constraintTop_toBottomOf="@+id/iv_logo"/>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:src="@drawable/icon_close_dark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_title"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_marginBottom="120dp"
            android:id="@+id/iv_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/gha_logo"
            app:layout_constraintBottom_toTopOf="@+id/tv_tips"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginEnd="28dp"
            android:layout_marginBottom="25dp"
            android:fontFamily="@font/poppins_regular"
            android:gravity="center"
            android:text="@string/don_t_forget_to_return_to_the_jbl_one_app_for_audio_settings_when_you_re_done"
            android:textColor="@color/system_white_opacity_85"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@+id/btn_set_up"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btn_set_up"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="48dp"
            android:background="@drawable/btn_background_bg_s1"
            android:fontFamily="@font/poppins_bold"
            android:minWidth="@dimen/width_255"
            android:minHeight="@dimen/width_48"
            android:text="@string/multi_channel_set_up"
            android:textAllCaps="true"
            android:textColor="@color/fg_inverse"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>