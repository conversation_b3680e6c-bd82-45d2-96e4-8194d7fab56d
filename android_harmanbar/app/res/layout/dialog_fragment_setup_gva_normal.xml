<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/view_touch_outside"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_enable_google_home_bg"
        android:clickable="true"
        android:focusable="true"
        android:minHeight="598dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/poppins_bold"
            android:text="@string/setup_GVA_and_Chromecast"
            android:textColor="@color/fg_primary"
            android:textSize="18dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:src="@drawable/icon_close_dark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_title"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="16dp"
                    android:src="@drawable/gha_logo"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp">

                    <ImageView
                        android:id="@+id/iv_activate"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:src="@drawable/icon_va_set_up_wifi_streaming"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_chromecast"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/harmanbar_jbl_Chromecast_Built_In"
                        android:textColor="@color/fg_primary"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/iv_activate"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_chromecast_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:fontFamily="@font/poppins_regular"
                        android:text="@string/chromecast_built_in_desc"
                        android:textColor="@color/system_white_opacity_55"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toEndOf="@id/tv_chromecast"
                        app:layout_constraintStart_toStartOf="@id/tv_chromecast"
                        app:layout_constraintTop_toBottomOf="@id/tv_chromecast" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:fontFamily="@font/poppins_bold"
                        android:text="@string/harmanbar_jbl_Learn_more"
                        android:textColor="@color/fg_activate"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/tv_chromecast"
                        app:layout_constraintStart_toStartOf="@id/tv_chromecast"
                        app:layout_constraintTop_toBottomOf="@id/tv_chromecast_desc" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp">

                    <ImageView
                        android:id="@+id/iv_wifi_steaming"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:src="@mipmap/icon_3rd_party_service"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_google_assistant"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/harmanbar_google_assistant"
                        android:textColor="@color/fg_primary"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/iv_wifi_steaming"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_google_assistant_desc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:fontFamily="@font/poppins_regular"
                        android:text="@string/get_hands_free_help_at_home_just_say_hey_google"
                        android:textColor="@color/system_white_opacity_55"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toEndOf="@id/tv_google_assistant"
                        app:layout_constraintStart_toStartOf="@id/tv_google_assistant"
                        app:layout_constraintTop_toBottomOf="@id/tv_google_assistant" />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <Button
                    android:id="@+id/btn_set_up"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:layout_marginBottom="48dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/btn_background_bg_s1"
                    android:fontFamily="@font/poppins_bold"
                    android:minWidth="@dimen/width_255"
                    android:minHeight="@dimen/width_48"
                    android:text="@string/multi_channel_set_up"
                    android:textAllCaps="true"
                    android:textColor="@color/bg_surface"
                    android:textSize="14dp"
                     />

            </LinearLayout>


        </androidx.core.widget.NestedScrollView>





    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>