<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="fragment"
            type="com.harman.product.list.MyProductsFragment" />

        <import type="androidx.recyclerview.widget.RecyclerView" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_mini_player_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:background="@drawable/radius_medium_sp_player"
            app:miniPlayerPaddingHorizontal="@{fragment.miniPlayersLayoutHorizontalPadding}"
            tools:paddingHorizontal="@dimen/dimen_12dp">

            <FrameLayout
                android:id="@+id/layout_top_panel"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_20dp"
                app:isVisible="@{fragment.draggerVisible}"
                app:layout_constraintTop_toTopOf="parent">

                <View
                    android:layout_width="31dp"
                    android:layout_height="@dimen/dimen_4dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:background="@drawable/radius_round_fixed_white_50" />
            </FrameLayout>

            <include
                layout="@layout/layout_single_mini_player"
                android:id="@+id/single_mini_player"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:isVisible="@{fragment.isSingleMiniPlayerVisible}"
                app:playerItem="@{fragment.singleMiniPlayerItem}"
                app:momentItem="@{fragment.singleMiniMomentItem}"
                app:radioItem="@{fragment.singleMiniRadioItem}"
                app:listener="@{fragment}" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_minis"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:orientation="vertical"
                android:overScrollMode="never"
                app:isVisible="@{!fragment.isSingleMiniPlayerVisible}"
                app:adapter="@{fragment.miniPlayersAdapter}"
                app:addItemDecoration="@{fragment.miniPlayerDecoration}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:miniPlayerPaddingTop="@{fragment.miniPlayerListPanelCoverHeight}"
                app:orientation="@{RecyclerView.VERTICAL}"
                app:setLayoutManager="@{fragment.miniPlayersDragTouchListener}"
                app:setOnTouchListener="@{fragment.miniPlayersDragTouchListener}"
                app:submitMultiList="@{fragment.miniPlayerItems}"
                tools:listitem="@layout/item_mini_player" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/layout_dragger"
            android:layout_width="@dimen/dimen_120dp"
            android:layout_height="@dimen/dimen_60dp"
            app:isVisible="@{fragment.draggerVisible}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:setOnTouchListener="@{fragment.miniPlayersDragTouchListener}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>