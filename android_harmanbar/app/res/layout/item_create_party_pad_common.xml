<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:id="@+id/layout_item"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/selector_dj_sound_item_bg"
        android:paddingStart="@dimen/width_14"
        android:paddingEnd="@dimen/width_14">

        <ImageView
            android:id="@+id/iv_item_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@drawable/selector_dj_sound_party"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_item_right"
            android:layout_width="@dimen/dimen_24dp"
            android:layout_height="@dimen/dimen_24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            tools:src="@drawable/icon_create_party_pad_add" />

    </RelativeLayout>

</LinearLayout>