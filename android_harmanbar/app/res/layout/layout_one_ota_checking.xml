<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.harman.ota.one.OneOtaViewModel" />

        <variable
            name="activity"
            type="com.harman.ota.one.OneOtaActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_surface">

        <LinearLayout
            android:id="@+id/layout_whats_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:layout_marginBottom="@dimen/dimen_8dp"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/dimen_60dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_contents_title"
                style="@style/Text_Title_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/fg_primary"
                app:whatsNewTitle="@{viewModel.targetFirmwareVersion}"
                tools:text="Version ***********.10" />

            <TextView
                android:id="@+id/tv_contents_learn_more"
                style="@style/Text_Body_Strong"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:onClick="@{() -> activity.onLearnMoreClick()}"
                android:text="@string/jbl_What_s_New_"
                android:textColor="@color/fg_activate" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_control"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/tv_plug_in_power_to_continue"
                style="@style/Text_Caption_1_Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dimen_12dp"
                android:drawablePadding="@dimen/dimen_5dp"
                android:gravity="center_vertical"
                android:text="@string/connecting_to_server"
                android:textColor="@color/fg_secondary"
                app:layout_constraintBottom_toTopOf="@+id/btn_update"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/btn_update"
                style="@style/Text_Button"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginStart="@dimen/dimen_60dp"
                android:layout_marginEnd="@dimen/dimen_60dp"
                android:layout_marginBottom="@dimen/dimen_48dp"
                android:background="@drawable/radius_round_bg_inverse"
                android:backgroundTint="@color/bg_disable"
                android:gravity="center"
                android:maxWidth="@dimen/dimen_255dp"
                android:text="@string/BLE_Please_wait"
                android:textAllCaps="true"
                android:textColor="@color/fg_disabled"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_plug_in_power_to_continue" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>