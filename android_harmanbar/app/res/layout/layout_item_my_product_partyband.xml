<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_bg_card"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_device_name"
                style="@style/Text_Brand_Title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="middle"
                android:maxLines="1"
                android:singleLine="true"
                android:textAllCaps="true"
                tools:textColor="@color/fg_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/ic_more"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="BAND BOX TRIO" />

            <View
                android:id="@+id/ic_more"
                android:layout_width="@dimen/dimen_32dp"
                android:layout_height="@dimen/dimen_32dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/ic_arrow_forward_square"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_device_name"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:minHeight="@dimen/dimen_20dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/connect_status_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/connect_status_title"
                style="@style/Text_Caption_1_Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_regular"
                android:singleLine="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/connect_status_icon"
                app:layout_constraintTop_toTopOf="parent"
                tools:textColor="@color/fg_activate" />

            <View
                android:id="@+id/battery_divider"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:background="@color/bg_on_card"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/connect_status_title"
                app:layout_constraintTop_toTopOf="parent" />

            <com.harman.hkone.BatteryView
                android:id="@+id/battery_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/battery_divider"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_180dp"
            android:layout_marginTop="@dimen/dimen_32dp"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:maxWidth="@dimen/dimen_255dp"
            android:paddingHorizontal="@dimen/dimen_40dp">

            <ImageView
                android:id="@+id/ivDevImg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerInside"
                tools:src="@drawable/ic_device_fg_disable" />

            <ImageView
                android:id="@+id/lottie_loading"
                android:layout_width="@dimen/dimen_36dp"
                android:layout_height="@dimen/dimen_36dp"
                android:layout_gravity="center"
                android:src="@drawable/loading_fg_primary" />
        </FrameLayout>

    </LinearLayout>

</layout>