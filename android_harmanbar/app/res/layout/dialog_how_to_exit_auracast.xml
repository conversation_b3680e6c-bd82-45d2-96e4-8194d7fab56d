<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="dialog"
            type="com.harman.auracast.fragment.HowToExitAuraCastDialog" />

        <variable
            name="viewModel"
            type="com.harman.auracast.AuraCastViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/radius_large_top_bg_card"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_desc"
            style="@style/Text_Title_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:paddingHorizontal="@dimen/dimen_24dp"
            android:paddingTop="@dimen/dimen_20dp"
            android:textColor="@color/fg_primary"
            app:decoAuraCastExitDemoTxt="@{viewModel.selectedDevice}"
            tools:text="@string/press_the_shown_button_on_the_product_to_quit" />

        <ImageView
            android:id="@+id/iv_auracast_exit_demo"
            android:layout_width="250dp"
            android:layout_height="250dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginVertical="50dp"
            app:decoAuraCastExitDemoImg="@{viewModel.selectedDevice}"
            tools:src="@mipmap/placeholder_with_auracast" />

        <com.wifiaudio.view.component.ComponentButton
            android:id="@+id/btn_got_it"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_60dp"
            android:layout_marginBottom="@dimen/dimen_48dp"
            android:onClick="@{() -> dialog.dismiss()}"
            app:btn_text="@string/harmanbar_jbl_GOT_IT"
            app:btn_type="btn_regular" />

    </LinearLayout>
</layout>

