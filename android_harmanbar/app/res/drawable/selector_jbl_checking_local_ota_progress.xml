<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >

    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="30dip" />

            <gradient
                android:angle="270"
                android:centerColor="@color/color_343434"
                android:centerY="0.75"
                android:endColor="@color/color_343434"
                android:startColor="@color/color_343434" />
        </shape>
    </item>
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <corners android:radius="30dip" />

                <gradient
                    android:angle="270"
                    android:centerColor="@color/bg_inverse"
                    android:endColor="@color/bg_inverse"
                    android:startColor="@color/bg_inverse" />
            </shape>
        </clip>
    </item>

</layer-list>