<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
  <group>
    <clip-path
        android:pathData="M0,0h120v120h-120z"/>
    <path
        android:pathData="M60,120C93.14,120 120,93.14 120,60C120,26.86 93.14,0 60,0C26.86,0 0,26.86 0,60C0,93.14 26.86,120 60,120Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M60,0.63C93.03,0.63 119.82,27.32 119.99,60.31C119.99,60.21 120,60.11 120,60C120,26.86 93.14,0 60,0C26.86,0 0,26.86 0,60C0,60.1 0.01,60.2 0.01,60.31C0.18,27.32 26.97,0.63 60,0.63Z"
        android:fillColor="#ffffff"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M119.99,59.68C119.82,92.68 93.03,119.37 60,119.37C26.97,119.37 0.18,92.68 0.01,59.68C0.01,59.79 0,59.9 0,60C0,93.13 26.86,120 60,120C93.14,120 120,93.13 120,60C120,59.9 119.99,59.79 119.99,59.68Z"
        android:fillColor="#263238"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M60,120C93.14,120 120,93.14 120,60C120,26.86 93.14,0 60,0C26.86,0 0,26.86 0,60C0,93.14 26.86,120 60,120Z"
        android:fillAlpha="0.1">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:centerX="18.13"
            android:centerY="18.13"
            android:gradientRadius="119.21"
            android:type="radial">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M60,120C93.14,120 120,93.14 120,60C120,26.86 93.14,0 60,0C26.86,0 0,26.86 0,60C0,93.14 26.86,120 60,120Z"
        android:fillAlpha="0.1">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:centerX="18.13"
            android:centerY="18.13"
            android:gradientRadius="119.21"
            android:type="radial">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M81.21,93.28H38.79L35.39,84.79L38.79,76.31H81.21L83.33,84.79L81.21,93.28Z"
        android:fillColor="#0F9D58"/>
    <path
        android:pathData="M98.18,52.62V89.04C98.18,91.38 96.29,93.28 93.94,93.28H81.21V57.92L60,36.71L62.92,28.5L71.91,24.79L71.93,24.78L71.92,24.79C82.02,34.9 97.08,49.97 97.08,49.97C97.72,50.69 98.18,51.6 98.18,52.62Z"
        android:fillColor="#4285F4"/>
    <path
        android:pathData="M38.79,57.93V93.29H26.06C23.71,93.29 21.82,91.39 21.82,89.05V74.9L29.78,62.84L38.79,57.93Z"
        android:fillColor="#F4B400"/>
    <path
        android:pathData="M71.93,24.78L21.82,74.9V52.62C21.86,51.61 22.28,50.69 22.92,49.98C22.92,49.98 56.93,15.94 57.01,15.87C57.77,15.1 58.94,14.63 60,14.63C61.06,14.63 62.23,15.1 62.99,15.87C63.01,15.89 66.83,19.71 71.91,24.79L71.93,24.78Z"
        android:fillColor="#DB4437"/>
    <path
        android:pathData="M97.08,49.98C97.08,49.98 63.07,15.94 63,15.87C62.23,15.1 61.06,14.63 60.01,14.63C58.95,14.63 57.78,15.1 57.01,15.87C56.93,15.94 22.92,49.98 22.92,49.98C22.27,50.69 21.85,51.61 21.82,52.62V53.15C21.86,52.14 22.28,51.22 22.92,50.51C22.92,50.51 56.93,16.47 57,16.4C57.77,15.63 58.94,15.16 59.99,15.16C61.05,15.16 62.22,15.63 62.99,16.4C63.06,16.47 97.06,50.51 97.06,50.51C97.72,51.22 98.17,52.14 98.17,53.15V52.62C98.18,51.61 97.73,50.69 97.08,49.98Z"
        android:strokeAlpha="0.2"
        android:fillColor="#ffffff"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M26.06,92.76C23.71,92.76 21.82,90.86 21.82,88.52V89.05C21.82,91.39 23.71,93.29 26.06,93.29H38.79V92.76H26.06Z"
        android:strokeAlpha="0.2"
        android:fillColor="#BF360C"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M93.94,92.76H81.21V93.29H93.94C96.29,93.29 98.18,91.39 98.18,89.05V88.52C98.18,90.85 96.29,92.76 93.94,92.76Z"
        android:strokeAlpha="0.2"
        android:fillColor="#2F3D69"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M60,92.76H38.79V93.29H60H81.21V92.76H60Z"
        android:strokeAlpha="0.2"
        android:fillColor="#263238"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M60,76.31H38.79V76.84H60H81.21V76.31H60Z"
        android:strokeAlpha="0.2"
        android:fillColor="#ffffff"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M97.08,49.98C97.08,49.98 63.07,15.94 63,15.87C62.23,15.1 61.06,14.63 60.01,14.63C58.95,14.63 57.78,15.1 57.01,15.87C56.93,15.94 22.92,49.98 22.92,49.98C22.27,50.69 21.85,51.61 21.82,52.62V89.22C21.82,91.56 23.71,93.46 26.06,93.46H60H93.94C96.29,93.46 98.18,91.56 98.18,89.22V52.62C98.18,51.61 97.73,50.69 97.08,49.98ZM81.21,76.32H38.79V57.93L60,36.72L81.21,57.93V76.32Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:centerX="14.75"
            android:centerY="13.38"
            android:gradientRadius="132.37"
            android:type="radial">
          <item android:offset="0" android:color="#19FFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M60.38,36.34L60,36.71L81.21,57.93V57.18L60.38,36.34Z"
        android:strokeAlpha="0.2"
        android:fillColor="#2F3D69"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M38.79,57.93V58.68L21.82,75.65V74.9L38.79,57.93Z"
        android:strokeAlpha="0.2"
        android:fillColor="#BF360C"
        android:fillAlpha="0.2"/>
  </group>
</vector>
