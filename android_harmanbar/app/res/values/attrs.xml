<?xml version="1.0" encoding="UTF-8"?>
<resources>

    <declare-styleable name="gifView">

        <!-- 图片来源 -->
        <attr name="src" format="integer" />

        <!-- 播放延迟 -->
        <attr name="delay" format="integer" />

        <!-- 设置是否停止 -->
        <attr name="stop" format="boolean" />
    </declare-styleable>
    <declare-styleable name="pagerTabStripView">
        <attr name="entries" format="string|reference"></attr>
        <attr name="selectIndex" format="integer"></attr>
    </declare-styleable>

    <!--ColorArcSeekBar-->
    <declare-styleable name="ColorArcSeekBar">
        <attr name="bg_arc_color" format="color" />
        <attr name="degree_color" format="color" />
        <attr name="hint_color" format="color" />
        <attr name="progress_color" format="color" />
        <attr name="front_start_color" format="color" />
        <attr name="front_mid_color" format="color" />
        <attr name="front_end_color" format="color" />

        <attr name="bg_arc_width" format="dimension" />
        <attr name="front_width" format="dimension" />

        <attr name="is_seek_enable" format="boolean" />
        <attr name="is_need_title" format="boolean" />
        <attr name="is_need_content" format="boolean" />
        <attr name="is_need_unit" format="boolean" />
        <attr name="is_need_dial" format="boolean" />
        <attr name="is_need_gradient" format="boolean" />

        <attr name="string_title" format="string" />
        <attr name="string_unit" format="string" />

        <attr name="sweep_angle_value" format="float" />
        <attr name="start_angle_value" format="float" />

        <attr name="current_value" format="float" />
        <attr name="max_value" format="float" />

        <attr name="thumb" format="reference" />

    </declare-styleable>

    <attr name="speed" format="integer" />
    <attr name="littleWidth" format="integer" />

    <declare-styleable name="RaceLamp">
        <attr name="speed" />
        <attr name="littleWidth" />
    </declare-styleable>

    <declare-styleable name="RotationProgressBar">
        <attr name="style" format="reference"></attr>
        <attr name="rpb_color" format="color"></attr>
        <attr name="rpb_bg_color" format="color"></attr>
        <attr name="rpb_colors" format="reference"></attr>
        <attr name="rpb_strokewidth" format="dimension"></attr>
        <attr name="rpb_sweep_speed" format="float"></attr>
        <attr name="rpb_sweep_min" format="integer"></attr>
        <attr name="rpb_sweep_max" format="integer"></attr>
        <attr name="rpb_rotation_speed" format="float"></attr>
    </declare-styleable>


    <declare-styleable name="CountdownView">
        <attr name="isHideTimeBackground" format="boolean" />
        <attr name="isShowTimeBgDivisionLine" format="boolean" />
        <attr name="timeBgDivisionLineColor" format="color" />
        <attr name="timeBgDivisionLineSize" format="dimension" />
        <attr name="timeBgColor" format="color" />
        <attr name="timeBgSize" format="dimension" />
        <attr name="timeBgRadius" format="dimension" />

        <attr name="isTimeTextBold" format="boolean" />
        <attr name="timeTextSize" format="dimension" />
        <attr name="timeTextColor" format="color" />
        <attr name="isShowDay" format="boolean" />
        <attr name="isShowHour" format="boolean" />
        <attr name="isShowMinute" format="boolean" />
        <attr name="isShowSecond" format="boolean" />
        <attr name="isShowMillisecond" format="boolean" />
        <attr name="isConvertDaysToHours" format="boolean" />

        <attr name="isSuffixTextBold" format="boolean" />
        <attr name="suffixTextSize" format="dimension" />
        <attr name="suffixTextColor" format="color" />
        <attr name="suffix" format="string" />
        <attr name="suffixDay" format="string" />
        <attr name="suffixHour" format="string" />
        <attr name="suffixMinute" format="string" />
        <attr name="suffixSecond" format="string" />
        <attr name="suffixMillisecond" format="string" />
        <attr name="suffixGravity">
            <enum name="top" value="0" />
            <enum name="center" value="1" />
            <enum name="bottom" value="2" />
        </attr>
        <attr name="suffixLRMargin" format="dimension" />
        <attr name="suffixDayLeftMargin" format="dimension" />
        <attr name="suffixDayRightMargin" format="dimension" />
        <attr name="suffixHourLeftMargin" format="dimension" />
        <attr name="suffixHourRightMargin" format="dimension" />
        <attr name="suffixMinuteLeftMargin" format="dimension" />
        <attr name="suffixMinuteRightMargin" format="dimension" />
        <attr name="suffixSecondLeftMargin" format="dimension" />
        <attr name="suffixSecondRightMargin" format="dimension" />
        <attr name="suffixMillisecondLeftMargin" format="dimension" />

        <attr name="isShowTimeBgBorder" format="boolean" />
        <attr name="timeBgBorderColor" format="color" />
        <attr name="timeBgBorderSize" format="dimension" />
        <attr name="timeBgBorderRadius" format="dimension" />
    </declare-styleable>

    <!--ThreeBounce的加载动画-->
    <attr name="SpinKitViewStyle" format="reference" />
    <declare-styleable name="SpinKitView">
        <attr name="SpinKit_Style">
            <enum name="ThreeBounce" value="6" />
        </attr>
        <attr name="SpinKit_Color" format="color" />
    </declare-styleable>

    <declare-styleable name="MyVoiceAnimView">
        <attr name="voiceLineColor" format="color" />
        <attr name="voiceLineWidth" format="dimension" />
        <attr name="voiceSpaceLineWidth" format="dimension" />
        <attr name="voiceLineCount" format="integer" />
        <attr name="voiceLineRefreshInterval" format="integer" />
    </declare-styleable>

    <declare-styleable name="RUDY_TitleIndicator">
        <attr name="titlePadding" format="dimension" />
        <!-- Left/right padding of not active view titles. -->
        <attr name="clipPadding" format="dimension" />
        <attr name="textSizeNormal" format="dimension" />
        <attr name="textSizeSelected" format="dimension" />
        <attr name="footerLineHeight" format="dimension" />
        <attr name="footerColor" format="color" />
        <attr name="footerTriangleHeight" format="dimension" />
    </declare-styleable>
    <declare-styleable name="SlidingUpPanelLayout">
        <attr name="umanoPanelHeight" format="dimension" />
        <attr name="umanoShadowHeight" format="dimension" />
        <attr name="umanoParalaxOffset" format="dimension" />
        <attr name="umanoFadeColor" format="color" />
        <attr name="umanoFlingVelocity" format="integer" />
        <attr name="umanoDragView" format="reference" />
        <attr name="umanoOverlay" format="boolean" />
        <attr name="umanoClipPanel" format="boolean" />
        <attr name="umanoAnchorPoint" format="float" />
        <attr name="umanoInitialState" format="enum">
            <enum name="expanded" value="0" />
            <enum name="collapsed" value="1" />
            <enum name="anchored" value="2" />
            <enum name="hidden" value="3" />
        </attr>
    </declare-styleable>

    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="pstsIndicatorColor" format="color" />
        <attr name="pstsUnderlineColor" format="color" />
        <attr name="pstsDividerColor" format="color" />
        <attr name="pstsIndicatorHeight" format="dimension" />
        <attr name="pstsUnderlineHeight" format="dimension" />
        <attr name="pstsDividerPadding" format="dimension" />
        <attr name="pstsTabPaddingLeftRight" format="dimension" />
        <attr name="pstsScrollOffset" format="dimension" />
        <attr name="pstsTabBackground" format="reference" />
        <attr name="pstsShouldExpand" format="boolean" />
        <attr name="pstsTextAllCaps" format="boolean" />
    </declare-styleable>

    <declare-styleable name="WaveView">
        <attr name="wave_color" format="color" />
        <attr name="wave_width" format="integer" />
        <attr name="wave_coreImageRadius" format="integer" />
    </declare-styleable>

    <!--collapsing toolbar-->
    <declare-styleable name="CollapsingToolBarStyle">
        <attr name="title" format="reference|string" />
        <attr name="cover" format="reference" />
        <attr name="cover_height" format="dimension" />
        <attr name="back_src" format="reference" />
        <attr name="next_src" format="reference" />
        <attr name="collapsed_title_appearance" format="reference" />
        <attr name="expanded_title_appearance" format="reference" />
    </declare-styleable>
    <!--DjEffect Bar-->
    <declare-styleable name="DJEffectBar">
        <attr name="title" />
    </declare-styleable>

    <declare-styleable name="ComponentButtonDoubleMini">
        <attr name="negativeButtonText" format="reference|string" />
        <attr name="positiveButtonText" format="reference|string" />
    </declare-styleable>

    <declare-styleable name="NormalMiddleButtonWithImage">
        <attr name="text" format="string" />
        <attr name="image" format="reference" />
    </declare-styleable>

    <attr name="selectedColor" format="color" />
    <attr name="unselectedColor" format="color" />
    <declare-styleable name="HMPagerIndicator">
        <attr name="selectedColor" />
        <attr name="unselectedColor" />
        <attr name="indicatorSize" format="dimension" />
        <attr name="indicatorSpace" format="dimension" />
        <attr name="totalCount" format="integer" />
    </declare-styleable>

    <declare-styleable name="ComponentText">
        <attr name="text_font">
            <enum name="Caption" value="0" />
            <enum name="Caption_Strong" value="1" />
            <enum name="Body_Regular" value="2" />
            <enum name="Body_Strong" value="3" />
            <enum name="Body_1" value="4" />
            <enum name="Body_1_Strong" value="5" />
            <enum name="Button" value="6" />
            <enum name="Button_Strong" value="7" />
            <enum name="Title_2" value="8" />
            <enum name="Title_1" value="9" />
            <enum name="Heading_2" value="10" />
            <enum name="Heading_1" value="11" />
        </attr>
    </declare-styleable>

    <declare-styleable name="ComponentButton">
        <attr name="btn_text_font">
            <enum name="Caption_1_Regular" value="0" />
            <enum name="Caption_1_Strong" value="1" />
            <enum name="Caption_2_Regular" value="2" />
            <enum name="Caption_2_Strong" value="3" />
            <enum name="Body_Medium" value="4" />
            <enum name="Body_Strong" value="5" />
            <enum name="Body_Regular" value="6" />
            <enum name="Button" value="7" />
            <enum name="Title_1" value="8" />
            <enum name="Title_2" value="9" />
            <enum name="Heading_2" value="10" />
            <enum name="Heading_1" value="11" />
            <enum name="Display" value="12" />
        </attr>
        <attr name="btn_type">
            <enum name="btn_regular" value="0" />
            <enum name="btn_non_emphasis" value="2" />
            <enum name="btn_disable" value="3" />
            <enum name="btn_warning" value="4" />
            <enum name="btn_loading" value="5" />
            <enum name="btn_text_button" value="6" />
            <enum name="btn_un_normal" value="7" />
        </attr>
        <attr name="btn_text" format="string" />
        <attr name="btn_text_size" format="float" />
        <attr name="btn_text_color" format="reference|color" />
        <attr name="btn_text_visibility" format="boolean" />
        <attr name="btn_front_icon_res" format="reference" />
        <attr name="btn_rear_icon_res" format="reference" />
        <attr name="textAllCaps" format="boolean" />
    </declare-styleable>

    <declare-styleable name="CircularProgressBar">
        <attr name="cpb_progress" format="integer" />
        <attr name="cpb_progress_max" format="integer" />
        <attr name="cpb_indeterminate_mode" format="boolean" />
        <attr name="cpb_progressbar_color" format="color" />
        <attr name="cpb_progressbar_color_start" format="color" />
        <attr name="cpb_progressbar_color_end" format="color" />
        <attr name="cpb_progressbar_color_direction">
            <flag name="left_to_right" value="1" />
            <flag name="right_to_left" value="2" />
            <flag name="top_to_bottom" value="3" />
            <flag name="bottom_to_top" value="4" />
        </attr>
        <attr name="cpb_background_progressbar_color" format="color" />
        <attr name="cpb_background_progressbar_color_start" format="color" />
        <attr name="cpb_background_progressbar_color_end" format="color" />
        <attr name="cpb_background_progressbar_color_direction">
            <flag name="left_to_right" value="1" />
            <flag name="right_to_left" value="2" />
            <flag name="top_to_bottom" value="3" />
            <flag name="bottom_to_top" value="4" />
        </attr>
        <attr name="cpb_progressbar_width" format="dimension" />
        <attr name="cpb_background_progressbar_width" format="dimension" />
        <attr name="cpb_round_border" format="boolean" />
        <attr name="cpb_start_angle" format="float" />
        <attr name="cpb_progress_direction">
            <flag name="to_right" value="1" />
            <flag name="to_left" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="TextProgressBar">
        <attr name="pb_text_font">
            <enum name="Caption" value="0" />
            <enum name="Caption_Strong" value="1" />
            <enum name="Body_Regular" value="2" />
            <enum name="Body_Strong" value="3" />
            <enum name="Body_1" value="4" />
            <enum name="Body_1_Strong" value="5" />
            <enum name="Button" value="6" />
            <enum name="Button_Strong" value="7" />
            <enum name="Title_2" value="8" />
            <enum name="Title_1" value="9" />
            <enum name="Heading_2" value="10" />
            <enum name="Heading_1" value="11" />
        </attr>
        <attr name="pb_text" format="string" />
        <attr name="pb_text_size" format="dimension" />
        <attr name="pb_text_color" format="reference|color" />
        <attr name="pb_text_cross_color" format="reference|color" />
        <attr name="pb_text_uppercase" format="boolean" />
    </declare-styleable>

    <declare-styleable name="CircleProgress">
        <!--内圆颜色-->
        <attr name="innerCircleColor" format="color" />
        <!--圆环半径-->
        <attr name="ringRadius" format="dimension" />
        <!--圆环宽度-->
        <attr name="ringWidth" format="dimension" />
        <!--圆环颜色-->
        <attr name="ringColor" format="color" />
        <!--圆环进度颜色-->
        <attr name="ringProgressColor" format="color" />
        <!--开始角度-->
        <attr name="startAngle" format="integer" />
        <!--是否顺时针-->
        <attr name="isClockwise" format="boolean" />
        <!--当前进度-->
        <attr name="currentProgress" format="float" />
        <!--总进度-->
        <attr name="maxProgress" format="float" />
        <!--不绘制的度数-->
        <attr name="disableAngle" format="integer" />
        <!--圆环进度是否为圆角-->
        <attr name="isRound" format="boolean" />
        <!--是否设置动画-->
        <attr name="useAnimation" format="boolean" />
        <!--动画执行时间-->
        <attr name="duration" format="integer" />
        <!--进度百分比数值是否含小数-->
        <!--<attr name="progressPercent" format="integer"/>-->
        <attr name="isDecimal" format="boolean" />
        <!--小数点后几位-->
        <attr name="decimalPointLength" format="integer" />
        <!--是否显示百分比-->
        <attr name="isShowPercentText" format="boolean" />
        <!--文字颜色-->
        <attr name="innerTextColor" format="color" />
        <!--文字大小-->
        <attr name="innerTextSize" format="dimension" />

    </declare-styleable>

    <declare-styleable name="ComponentCardStandardItem">
        <attr name="startIconImage" format="reference" />
        <attr name="startIconVisibility" format="enum">
            <!-- Visible on screen; the default value. -->
            <enum name="visible" value="0" />
            <!-- Not displayed, but taken into account during layout (space is left for it). -->
            <enum name="invisible" value="1" />
            <!-- Completely hidden, as if the view had not been added. -->
            <enum name="gone" value="2" />
        </attr>

        <attr name="titleText" format="reference|string" />
        <attr name="descText" format="string" />

        <attr name="descVisibility" format="enum">
            <!-- Visible on screen; the default value. -->
            <enum name="visible" value="0" />
            <!-- Not displayed, but taken into account during layout (space is left for it). -->
            <enum name="invisible" value="1" />
            <!-- Completely hidden, as if the view had not been added. -->
            <enum name="gone" value="2" />
        </attr>

        <attr name="endIconImage" format="reference" />

        <attr name="endIconImageVisibility" format="enum">
            <!-- Visible on screen; the default value. -->
            <enum name="visible" value="0" />
            <!-- Not displayed, but taken into account during layout (space is left for it). -->
            <enum name="invisible" value="1" />
            <!-- Completely hidden, as if the view had not been added. -->
            <enum name="gone" value="2" />
        </attr>

        <attr name="forwardIconImage" format="reference" />

        <attr name="forwardIconImageVisibility" format="enum">
            <!-- Visible on screen; the default value. -->
            <enum name="visible" value="0" />
            <!-- Not displayed, but taken into account during layout (space is left for it). -->
            <enum name="invisible" value="1" />
            <!-- Completely hidden, as if the view had not been added. -->
            <enum name="gone" value="2" />
        </attr>

        <attr name="extraActionText" format="string" />

        <attr name="extraActionVisibility" format="enum">
            <!-- Visible on screen; the default value. -->
            <enum name="visible" value="0" />
            <!-- Not displayed, but taken into account during layout (space is left for it). -->
            <enum name="invisible" value="1" />
            <!-- Completely hidden, as if the view had not been added. -->
            <enum name="gone" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="ComponentSmallButton">
        <attr name="csb_btn_type" format="enum">
            <enum name="highlight" value="0" />
            <enum name="jump" value="1" />
            <enum name="flat" value="2" />
            <enum name="line" value="3" />
            <enum name="red" value="4" />
        </attr>
    </declare-styleable>

    <declare-styleable name="LoopScaleView">
        <!--可视范围内显示的大刻度数-->
        <attr name="maxShowItem" format="integer" />
        <!--最大刻度值-->
        <attr name="maxScaleValue" format="float" />
        <!--最小刻度值-->
        <attr name="minScaleValue" format="float" />
        <!--Item value 之间的步长，比如 87.50-88.00,则valueStep = 0.5 或者 76.0-77.0 则valueStep = 1.0-->
        <attr name="scaleValueStep" format="float" />
        <!-- 刻度之间的个数。比如87.5-88.0之间是10个刻度还是5个 -->
        <attr name="scaleItemSize" format="integer" />
        <attr name="scaleSpace" format="float" />
        <!--一个刻度表示的值的大小-->
        <attr name="oneScaleValue" format="float" />
        <!--指针颜色-->
        <attr name="cursorColor" format="color" />
        <!--刻度颜色-->
        <attr name="scaleColor" format="color" />
        <!--刻度文字颜色-->
        <attr name="scaleTextColor" format="color" />
        <!--指针图标，颜色和这个属性二选一-->
        <attr name="cursorBitmap" format="reference" />
    </declare-styleable>


</resources>
