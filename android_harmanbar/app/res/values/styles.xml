<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <!--Typography-->
    <style name="Text_Body_Strong">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_semibold</item>
    </style>

    <style name="Text_Body_Medium">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_medium</item>
    </style>

    <style name="Text_Body_Regular">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="Text_Caption_1_Strong">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_12sp</item>
        <item name="android:fontFamily">@font/poppins_semibold</item>
    </style>

    <style name="Text_Caption_1_Regular">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_12sp</item>
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="Text_Caption_2_Strong">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_10sp</item>
        <item name="android:fontFamily">@font/poppins_semibold</item>
    </style>

    <style name="Text_Caption_2_Regular">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_10sp</item>
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="Text_Button">
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="Text_Title_1">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_18sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
    </style>

    <style name="Text_Title_2">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_16sp</item>
        <item name="android:fontFamily">@font/poppins_semibold</item>
    </style>

    <style name="Text_Heading_1">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_27sp</item>
        <item name="android:fontFamily">@font/poppins_extrabold</item>
    </style>

    <style name="Text_Heading_2">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_24sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
    </style>

    <style name="Text_Display_1">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_42sp</item>
        <item name="android:fontFamily">@font/poppins_semibold</item>
    </style>

    <style name="Text_Effect_Lab_Effect_Part">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_19sp</item>
        <item name="android:fontFamily">@font/roc_grotesk_wide_extrabold</item>
    </style>

    <style name="Text_Effect_Lab_Lab_Part">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_19sp</item>
        <item name="android:fontFamily">@font/roc_grotesk_light</item>
    </style>

    <style name="Button_Regular">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/fg_inverse</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
        <item name="android:background">@drawable/radius_round_fg_primary</item>
        <item name="android:backgroundTint">@color/bg_inverse</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Button_Non_Emphasis">
        <item name="android:includeFontPadding">false</item>
        <item name="textColor">@color/fg_primary</item>
        <item name="textAllCaps">true</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
        <item name="background">@drawable/shape_component_button_bg_disable</item>
    </style>

    <style name="Button_Disable">
        <item name="android:includeFontPadding">false</item>
        <item name="textColor">@color/bg_s3</item>
        <item name="textAllCaps">true</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
        <item name="background">@drawable/shape_component_button_bg_disable</item>
    </style>

    <style name="Button_Warning">
        <item name="android:includeFontPadding">false</item>
        <item name="textColor">@color/red_1</item>
        <item name="textAllCaps">true</item>
        <item name="android:textSize">@dimen/font_14sp</item>
        <item name="android:fontFamily">@font/poppins_bold</item>
        <item name="background">@drawable/shape_component_button_bg_warning</item>
    </style>

    <!--Non-Text-->
    <style name="MyWidget" parent="@android:style/Theme">
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="MyWidget.TabText">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/tab_indicator_text</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">marquee</item>
    </style>

    <style name="MyWidget.TabText_MUZO2">
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/font_10sp</item>
        <item name="android:textColor">@color/tab_indicator_text_muzo2</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:ellipsize">marquee</item>
    </style>

    <!-- Common list view styles -->
    <style name="MyWidget.ListView">
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:cacheColorHint">#00ffffff</item>
        <item name="android:dividerHeight">2px</item>
        <item name="android:listSelector">@android:color/transparent</item>
        <item name="android:fadeScrollbars">true</item>
    </style>


    <declare-styleable name="MaxHeightScrollView">
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>

    <!--
        Base application theme, dependent on API level. This theme is replaced
        by AppBaseTheme from res/values-vXX/styles.xml on newer devices.
    -->
    <style name="AppBaseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
        -->
        <item name="android:textAllCaps">false</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <!-- Application theme. -->
    <style name="AppTheme" parent="AppBaseTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
        <!--     <item name="android:windowBackground">@drawable/icon_splash</item>  -->
        <!-- <item name="android:windowBackground">@null</item> -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowBackground">@color/bg_surface</item>
        <!-- 设置activity切换动画 -->
        <!--  <item name="android:windowAnimationStyle">@style/activityAnimation</item> -->
    </style>

    <style name="AppLandscapeTheme" parent="AppBaseTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="DashBoardAppTheme" parent="AppBaseTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="AppTheme_Home" parent="AppBaseTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
        <item name="android:windowNoTitle">true</item>
        <!-- 设置activity切换动画 -->
        <item name="android:windowAnimationStyle">@style/activityAnimation_Local_Setting</item>
    </style>

    <style name="AppTheme_Local_Setting" parent="AppBaseTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
        <item name="android:windowNoTitle">true</item>
        <!-- 设置activity切换动画 -->
        <item name="android:windowAnimationStyle">@style/activityAnimation_Local_Setting</item>
    </style>

    <style name="AppTheme_Play_Control" parent="AppBaseTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
        <item name="android:windowNoTitle">true</item>
        <!-- 设置activity切换动画 -->
        <item name="android:windowAnimationStyle">@style/activityAnimation_Play_Control</item>
    </style>

    <style name="TurtorsDlgTheme" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@drawable/dlgviewbg</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:includeFontPadding">false</item>

        <!-- <item name="android:keepScreenOn">true</item> -->
    </style>

    <style name="activityAnimation" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/push_left_in</item>
        <item name="android:activityOpenExitAnimation">@anim/push_left_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/push_left_in</item>
        <item name="android:activityCloseExitAnimation">@anim/push_left_out</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="activityAnimation_Home" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/push_up_in</item>
        <item name="android:activityOpenExitAnimation">@anim/push_left_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/push_left_in</item>
        <item name="android:activityCloseExitAnimation">@anim/push_left_out</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="activityAnimation_Local_Setting" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/push_up_in
        </item>                <!--current page entry -->
        <item name="android:activityOpenExitAnimation">@anim/push_up_out
        </item>                 <!--previous page exit  -->
        <!--   <item name="android:activityCloseEnterAnimation">@anim/push_left_in</item> -->    <!--previous page entry-->
        <item name="android:activityCloseExitAnimation">@anim/slide_down_out
        </item>          <!-- current page exit -->
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="activityAnimation_Play_Control" parent="@android:style/Animation">
        <item name="android:activityOpenEnterAnimation">@anim/push_up_in
        </item>                <!--current page entry -->
        <item name="android:activityOpenExitAnimation">@anim/push_up_out
        </item>                 <!--previous page exit  -->
        <!--   <item name="android:activityCloseEnterAnimation">@anim/push_left_in</item> -->    <!--previous page entry-->
        <item name="android:activityCloseExitAnimation">@anim/slide_down_out
        </item>          <!-- current page exit -->
        <item name="android:includeFontPadding">false</item>
    </style>

    <!-- popwidnow 显示消失 动画-->
    <style name="dlg_musiclist_anim_style">
        <item name="android:windowEnterAnimation">@anim/dlg_enter</item>        <!-- 指定显示的动画xml  -->
        <item name="android:windowExitAnimation">@anim/dlg_exit</item>       <!-- 指定消失的动画xml  -->
    </style>
    <!-- favorite popwidnow 显示消失 动画-->
    <style name="dlg_favorite_anim_style">
        <item name="android:windowEnterAnimation">@anim/dlg_favorite_enter
        </item>        <!-- 指定显示的动画xml  -->
        <item name="android:windowExitAnimation">@anim/dlg_favorite_exit
        </item>       <!-- 指定消失的动画xml  -->
    </style>

    <style name="dlg_slidemenu_anim_style">
        <item name="android:windowEnterAnimation">@anim/dlg_slidemenu_in
        </item>        <!-- 指定显示的动画xml  -->
        <item name="android:windowExitAnimation">@anim/dlg_slidemenu_exit
        </item>       <!-- 指定消失的动画xml  -->
    </style>
    <!-- current queue popwidnow 显示消失 动画-->
    <style name="dlg_current_queue_anim_style">
        <item name="android:windowEnterAnimation">@anim/dlg_curqueue_push_right_in
        </item>        <!-- 指定显示的动画xml  -->
        <item name="android:windowExitAnimation">@anim/dlg_curqueue_push_right_out
        </item>       <!-- 指定消失的动画xml  -->
    </style>


    <style name="ThemeWindowTranslucent" parent="@style/AppBaseTheme">
        <!-- 窗体透明设置，结合SwipeBack -->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>


    <style name="ActionSheetDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:layout_margin">20dp</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/ActionSheetDlgAnimation</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="ActionOperateGroupDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/ActionSheetDlgAnimation</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="ActionTimeDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/bg_inverse</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/ActionSheetDlgAnimation</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="ActionSheetDialogStyle1" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/ActionSheetDlgAnimation</item>
        <item name="android:includeFontPadding">false</item>
    </style>


    <style name="ActionSheetDlgAnimation" parent="@android:style/Theme.Dialog">
        <item name="android:windowEnterAnimation">@anim/actionsheet_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/actionsheet_dialog_out</item>
        <item name="android:includeFontPadding">false</item>
    </style>


    <style name="CustomDialog" parent="@android:style/Theme.Dialog">
        <!--<item name="android:windowFrame">@null</item>-->
        <item name="android:windowNoTitle">true</item><!--//无标题-->
        <item name="android:windowBackground">@drawable/transparent</item><!--//背景-->
        <!--<item name="android:windowIsFloating">true</item>&lt;!&ndash;//悬浮&ndash;&gt;-->
        <!--<item name="android:windowContentOverlay">@null</item>-->
        <item name="android:backgroundDimEnabled">true</item><!--背景变暗-->
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="CustomDialog_CanClose" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item><!--//无标题-->
        <item name="android:windowBackground">@drawable/transparent</item><!--//背景-->
        <item name="android:backgroundDimEnabled">true</item><!--背景变暗-->
        <item name="android:windowCloseOnTouchOutside">true</item><!--点击背景关闭-->
        <item name="android:includeFontPadding">false</item>
    </style>


    <style name="pull_down_menu_style">
        <item name="android:windowEnterAnimation">@null</item>        <!-- 指定显示的动画xml  -->
        <item name="android:windowExitAnimation">@null</item>       <!-- 指定消失的动画xml  -->
    </style>

    <!--WiFi强度显示-->
    <style name="wifi_strength_progress" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:maxHeight">@dimen/width_10</item>
        <item name="android:minHeight">@dimen/width_5</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">
            @android:drawable/progress_indeterminate_horizontal
        </item>
        <item name="android:progressDrawable">@drawable/playprog_time_seekbar_progress</item>
    </style>


    <style name="radionet_gridview">
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:horizontalSpacing">10dp</item>
        <item name="android:verticalSpacing">10dp</item>
    </style>


    <style name="radionet_gridview_local">
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:horizontalSpacing">10dp</item>
        <item name="android:verticalSpacing">10dp</item>
    </style>


    <!-- alexa语言选择dialog -->
    <style name="AnimBottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/dlg_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/dlg_bottom_out</item>
    </style>

    <style name="ShareDialog" parent="@android:style/Animation.Dialog">
        <item name="android:windowAnimationStyle">@style/AnimBottom</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <!-- 设置背景色 透明-->
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 设置是否显示背景 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 设置背景透明度 0 全透明 1 全不透明-->
        <item name="android:backgroundDimAmount">0.8</item>
        <!-- 设置点击空白消失 -->
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:includeFontPadding">false</item>
    </style>


    <declare-styleable name="GifView">
        <attr name="gif" format="reference" />
        <attr name="paused" format="boolean" />
    </declare-styleable>
    <declare-styleable name="CustomTheme">
        <attr name="gifViewStyle" format="reference" />
    </declare-styleable>

    <style name="Widget_GifView"></style>

    <style name="LoadingBarStyle" parent="android:Widget.Holo.ProgressBar"></style>

    <!--ThreeBounce的加载动画-->
    <style name="SpinKitView">
        <item name="android:indeterminateOnly">true</item>
        <item name="android:minWidth">100dp</item>
        <item name="android:maxWidth">100dp</item>
        <item name="android:minHeight">100dp</item>
        <item name="android:maxHeight">100dp</item>
    </style>

    <style name="SpinKitView.ThreeBounce">
        <item name="SpinKit_Style">ThreeBounce</item>
    </style>

    <declare-styleable name="IndicatorTitleView">
        <attr name="indicator" format="integer" />
    </declare-styleable>

    <style name="Theme.NoBackgroundAndTitleMain" parent="android:Theme.Translucent">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="LockScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="NoActionBarTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@color/bg_surface</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="AppTheme.Base" parent="Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <style name="AppTheme_Compat" parent="AppTheme.Base">
        <item name="colorPrimary">@android:color/transparent</item>
        <item name="colorPrimaryDark">@android:color/transparent</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="BottomDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/bottom_up_animation</item>
        <item name="android:windowExitAnimation">@anim/bottom_down_animation</item>
    </style>

    <style name="AlphaInAndOutDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/frag_fade_in</item>
        <item name="android:windowExitAnimation">@anim/frag_fade_out</item>
    </style>

    <style name="collapsed_title_appearance">
        <item name="android:textColor">@color/fg_primary</item>
        <item name="android:textSize">@dimen/font_18sp</item>
        <item name="android:fontFamily">@font/poppins_extrabold</item>
    </style>

    <style name="expanded_title_appearance">
        <item name="android:textColor">@color/fg_primary</item>
        <item name="android:textSize">@dimen/font_24sp</item>
        <item name="android:fontFamily">@font/poppins_extrabold</item>
    </style>

    <style name="Theme.AppCompat.Translucent" parent="Theme.AppCompat.NoActionBar">
        <item name="android:background">#33000000
        </item> <!-- Or any transparency or color you need -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
    </style>

    <style name="BaseBottomDialogStyle" parent="@style/Theme.AppCompat.Light.Dialog">
        <!-- <item name="android:statusBarColor">@color/transparent</item>
         <item name="android:background">@color/transparent</item>
         <item name="android:windowBackground">@color/transparent</item>
         <item name="android:windowTranslucentStatus">true</item>-->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style>

    <style name="TextProgressBarStyle" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">
            @android:drawable/progress_indeterminate_horizontal
        </item>
        <item name="android:progressDrawable">@drawable/layer_text_progress</item>
    </style>

    <style name="SlideAnimation">
        <item name="android:includeFontPadding">false</item>
        <item name="android:windowEnterAnimation">@anim/slide_up_in</item>
        <item name="android:windowExitAnimation">@anim/slide_down_out</item>
    </style>

    <style name="SlideDialogAnim" parent="@android:style/Theme.Dialog">
        <item name="android:includeFontPadding">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/SlideAnimation</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="Button_Positive" parent="@style/Text_Button">
        <item name="android:layout_height">@dimen/dimen_48dp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Button_Neutral" parent="@style/Text_Button">
        <item name="android:background">@drawable/bg_on_card_radius_24</item>
        <item name="android:layout_height">@dimen/dimen_48dp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">@color/red_1</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Button_Negative" parent="@style/Text_Button">
        <item name="android:background">@color/bg_surface</item>
        <item name="android:layout_height">@dimen/dimen_48dp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">@color/fg_primary</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Button_Negative_ConstraintLayout_Single" parent="@style/Button_Negative">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_marginStart">@dimen/dimen_60dp</item>
        <item name="android:layout_marginEnd">@dimen/dimen_60dp</item>
        <item name="android:clickable">true</item>
        <item name="android:enabled">true</item>
        <item name="android:textColor">@color/fg_primary</item>
        <item name="layout_constraintEnd_toEndOf">parent</item>
        <item name="layout_constraintStart_toStartOf">parent</item>
        <item name="layout_constraintWidth_max">@dimen/dimen_255dp</item>
    </style>

    <style name="Middle_Button_Strong" parent="@style/Text_Button">
        <item name="android:background">@drawable/middle_button_strong_background</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_35dp</item>
        <item name="android:layout_marginStart">@dimen/dimen_16dp</item>
        <item name="android:layout_marginEnd">@dimen/dimen_16dp</item>
        <item name="android:textColor">@color/fg_inverse</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Middle_Button_Normal" parent="@style/Text_Button">
        <item name="android:background">@drawable/middle_button_normal_background</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_35dp</item>
        <item name="android:layout_marginStart">@dimen/dimen_16dp</item>
        <item name="android:layout_marginEnd">@dimen/dimen_16dp</item>
        <item name="android:textColor">@color/fg_primary</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Small_Button_Enabled" parent="@style/Text_Body_Strong">
        <item name="android:background">@drawable/small_button_enabled_background</item>
        <item name="android:layout_weight">@dimen/dimen_71dp</item>
        <item name="android:layout_height">@dimen/dimen_27dp</item>
        <item name="android:textColor">@color/fg_inverse</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="Small_Button_Disabled" parent="@style/Text_Body_Strong">
        <item name="android:background">@drawable/small_button_disabled_background</item>
        <item name="android:layout_weight">@dimen/dimen_71dp</item>
        <item name="android:layout_height">@dimen/dimen_27dp</item>
        <item name="android:textColor">@color/fg_disabled</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="EditText_PartyBox_Tws_Set_Group_Name" parent="@style/Text_Body_Regular">
        <item name="android:textCursorDrawable">@drawable/textcursordrawable</item>
        <item name="android:cursorVisible">true</item>
        <item name="android:imeOptions">actionDone</item>
    </style>

    <style name="Imageview_Aura">
        <item name="android:padding">0dp</item>
        <item name="android:scaleType">centerInside</item>
    </style>

    <style name="Dialog_Top_Radius">
        <item name="android:includeFontPadding">false</item>
        <item name="android:background">@drawable/radius_large_top_bg_card</item>
        <item name="android:paddingHorizontal">@dimen/dimen_16dp</item>
        <item name="android:paddingTop">@dimen/dimen_16dp</item>
    </style>

    <style name="Dialog_Full_Radius">
        <item name="android:includeFontPadding">false</item>
        <item name="android:background">@drawable/radius_large_bg_card</item>
    </style>
</resources>
