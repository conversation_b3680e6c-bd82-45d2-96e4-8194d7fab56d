<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.harman.bar.app">

    <uses-sdk tools:overrideLibrary="com.baidu.duer.sdk,com.baidu.duer.libcore" />

    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="31" />
    <uses-permission
        android:name="android.permission.CAMERA"
        tools:node="remove" /> <!-- 去除小度之家SDK目前用不到的额外权限 -->
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" /> <!-- 去除小度之家SDK目前用不到的额外权限 -->
    <uses-permission
        android:name="android.permission.RECORD_AUDIO"
        tools:node="remove" /> <!-- SDCARD读写权限 根据需要添加 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 网络状态检测权限  根据需要添加 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/> -->
    <!-- 在打包脚本里增加了这个权限，具体搜索下 Manifest_add.xml 这个文件 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- <uses-permission android:name="android.permission.RESTART_PACKAGES" /> -->
    <!-- 后台重启权限（API8以上已无效） -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- BLE -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.GET_SIGNATURES" />
    <!-- 读写SD卡权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:resizeable="true"
        android:smallScreens="true" />

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" /> <!-- Android 11新增权限, 用于跳转其他第三方App -->
    <!-- <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/> -->
    <queries>
        <package android:name="com.spotify.music" />
        <package android:name="com.aspiro.tidal" />
        <package android:name="com.amazon.dee.app" />
        <package android:name="com.android.vending" />
        <package android:name="com.tencent.qqmusic" />
        <package android:name="com.google.android.apps.chromecast.app" />
        <package android:name="com.jbl.partybox" />
        <package android:name="com.harman.ble.jbllink" />
        <package android:name="com.whatsapp" />
        <package android:name="com.wifiaudio.harmanbar" />

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="http" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <application
        android:name="com.wifiaudio.app.WAApplication"
        android:allowBackup="true"
        android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode|locale|fontScale|orientation|keyboardHidden|layoutDirection"
        android:icon="@mipmap/icon"
        android:label="@string/applicationName"
        android:largeHeap="true"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/icon_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:allowBackup,android:label,android:supportsRtl">

        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.QuickStartGuideWebActivity"
            android:exported="false" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="${applicationId}.firebaseperfprovider"
            android:exported="false"
            android:initOrder="101"
            tools:node="remove" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" /> <!-- 声明 upnp service impl -->
        <service android:name="org.teleal.cling.android.AndroidUpnpServiceImpl" />
        <service android:name="com.wifiaudio.utils.wificonfig.ReenableAllApsWhenNetworkStateChanged$BackgroundService" />
        <service
            android:name="com.wifiaudio.service.PingService"
            android:stopWithTask="true" />
        <service
            android:name="com.wifiaudio.service.MusicControlService"
            android:stopWithTask="true" />

        <service
            android:name="com.harman.partyband.output.AudioPlayerServer"
            android:enabled="true" />

        <!--
<service
            android:name="com.wifiaudio.lock.LockService"
            android:process=":main" />
        <service
            android:name=".ForegroundService"
            android:foregroundServiceType="location">

            &lt;!&ndash; Any inner elements would go here. &ndash;&gt;
        </service> &lt;!&ndash; 无障碍服务 &ndash;&gt;
        -->
        <!--
        <service
                    android:name=".service.VolumeKeyMonitorService"
                    android:canRequestFilterKeyEvents="true"
                    android:enabled="true"
                    android:exported="true"

                    android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
                    <intent-filter>
                        <action android:name="android.accessibilityservice.AccessibilityService" />
                    </intent-filter>
                    <meta-data
                        android:name="android.accessibilityservice"
                        android:resource="@xml/accessibility_config" />
                </service>
        -->

        <!--If import Google fast pair, the launchMode need to set as `standard`.-->
        <!--But app will refresh after open from launcher even it's in background already-->
        <activity
            android:name="com.wifiaudio.app.Splash2Activity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="true"
            android:launchMode="standard"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.harman.hkone.multichannelenhanced.MultiChannelActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.multichannelenhanced.MCDetailActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.StereoIntroActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.StereoIntro2Activity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.CalibratingActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.CommunicatingActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.CheckEnvironmentActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.CommunicateSuccessActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.ManualAssignChannelActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.PlaceSpeakersActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.CalibrateDoneActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesmsccenter.CurrPlayListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.AddMoreServicesActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevconfig.DeviceConnectAPActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" /> <!-- <activity -->
        <!-- android:name="com.wifiaudio.app.Splash2Activity" -->
        <!-- android:screenOrientation="portrait"></activity> -->
        <!--        <activity-->
        <!--            android:name="com.wifiaudio.view.pagesmsccontent.MusicContentPagersActivity"-->
        <!--            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"-->
        <!--            android:launchMode="singleTask"-->
        <!--            android:resizeableActivity="false"-->
        <!--            android:screenOrientation="portrait"-->
        <!--            android:windowSoftInputMode="adjustPan|adjustResize|stateHidden" />-->
        <activity
            android:name="com.wifiaudio.view.pagesmsccontent.easylink.LinkDeviceAddActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:screenOrientation="portrait"
            android:theme="@style/ThemeWindowTranslucent"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.wifiaudio.view.custom_view.ReportPage"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.local.LocalSettingActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesmsccontent.ImpressumAndDeclarationInfo"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesmsccontent.PrivacyPolicyActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" /> <!-- 在这个Activity中加入不同的Fragment,以后不需要创建新的Activity了，也不要在清单文件中注册activity了 -->
        <activity
            android:name="com.wifiaudio.view.pagesmsccontent.ContainerActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.linkplay.ota.view.DeviceUpgradeActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait"
            android:theme="@style/ThemeWindowTranslucent"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name="com.wifiaudio.view.pagesmsccontent.AllPlayControlActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/ThemeWindowTranslucent" />
        <activity
            android:name="com.harman.hkone.StereoSetupActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.StereoTuningActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.GroupSuccessActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.effectlab.EffectLabActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:theme="@style/AppLandscapeTheme" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.MicEffectActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.TopPanelActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.LightShowKnobActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.partypad.PartyPadActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.BTHorizonAlarmsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.BTHorizonAddEditAlarmsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.BTHorizonAlarmsRepeatTypeActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.BTHorizonAlarmsWakeUpSoundActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.horizon3.BTHorizonAlarmsSettingActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesmsccontent.home.ChromecastAlexaComposeActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.linkplay.privacy.PrivacyWebActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.MultiRoomLearnMoreActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.app.debug.DevelopConfigurationActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.AutoPopupActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Translucent" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.partybox.partypad.CreatePartyPadActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.redirect.RedirectActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.redirect.RedirectSoundBarActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.hkone.newmultichannel.MCListActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.wifiaudio.view.pagesdevcenter.devicesetting.BluetoothConnectionGuideActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.amazon.identity.auth.device.workflow.WorkflowActivity"
            android:allowTaskReparenting="true"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- android:host must use the full package name found in Manifest General Attributes -->
                <data
                    android:host="${applicationId}"
                    android:scheme="amzn" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.harman.DebugActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.webview.CommonHybridActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.webview.DeviceControlHybridActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/DashBoardAppTheme" />

        <activity
            android:name="com.harman.webview.DashboardDebugHybridActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.webview.AddNewProductHybridActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.ota.partybox.PartyBoxOtaActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.ota.partylight.PartyLightOtaActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.ota.one.OneOtaActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.partybox.PartyBoxTwsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.auracast.AuraCastActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.control.GroupRenameActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.home.HomePagesActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.product.info.ProductInfoActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.customer.service.CustomerServiceActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.product.list.RedirectAppActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.language.LanguageActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.customer.service.DiagnosisReportActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.moment.MomentActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.wifiaudio.view.account.AccountActivity"
            android:screenOrientation="nosensor" />

        <activity
            android:name="com.harman.partylight.productinfo.PLProdInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.ota.partylight.PLOtaActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partylight.stage.StageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partylight.stage.StageResultActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partylight.stage.DismissStageActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.activity.OneCommanderGroupProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.activity.OneHomeSeriesProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.activity.OneBarProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.activity.AuraCastBroadcastQualityActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.activity.OnePartyBoxProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.activity.OnePortableProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.product.setting.activity.OneSubProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.streaming.google.GoogleCastActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.streaming.amazon.AmazonAlexaActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.streaming.SpotifyActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.streaming.TidalConnectActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.streaming.roon.RoonReadyActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.va.VAGoogleActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.va.VAAlexaActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.va.refactor.VAActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.rename.RenameDeviceActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.calibration.CalibrationActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.calibration.CalibrationSODActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.calibration.OneCommanderGroupCalibrationActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.control.controller.RemoteControllerActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.multichannel.MultichannelActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.harman.streaming.WiFiStreamingDetailActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.reconnect.BluetoothReconnectionActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.miceffect.MicEffectActivityV2"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.music.player.FullPlayerActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.nightlistening.NightListeningActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.multiroom.MultiRoomLearnMoreActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.multichannel.SoundTuningActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.product.list.LoadingDevControlResActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.streaming.AirPlayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.webview.WhatIsNewHybridActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.streaming.QobuzActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.btstereopair.BTStereoPairActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.debug.PartyBandDebugActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.ota.PartyBandOtaActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.drummetronome.DrumMetronomeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.debug.PartybandDebugCommunicationActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.partyband.looper.PartyBandGuitarLooperActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.output.PartBandUsbOutputActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.drummetronome.DrumTimeSignatureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.product.setting.activity.PartyBandProductSettingsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.tuner.TunerActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.partyband.micsensitivity.MicSensitivityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.tuner.TunerSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.tuner.TunerInstrumentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.tuner.TuningActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.output.InputFileNameActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.horizon.BtDeviceProductSettingsActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.horizon.TimeDateActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.setting.horizon.ProductLanguageActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.controls.screendisplay.horizon.SetBedtimeActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.guitarpreset.GuitarPresetActivity"
            android:exported="true"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.product.controls.screendisplay.horizon.SetScreenSaverActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.radio.RadioActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.harman.radio.RadioStationListActivity"
            android:configChanges="locale|screenLayout|screenSize|keyboardHidden|layoutDirection|orientation"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.guitarpreset.RenamePresetActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.harman.partyband.guitarpreset.DuplicateSaveActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.harman.partyband.guitarpreset.SlotDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.guitarpreset.AdjustSlotValueActivity"
            android:screenOrientation="portrait"/>
        <activity android:name="com.harman.partyband.looper.DrumSettingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.harman.partyband.widget.WidgetPreviewActivity"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.harman.partyband.guitarpreset.OnProductPresetActivity"
            android:screenOrientation="portrait" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" /> <!-- 声明upnpserviceimpl -->
        <service android:name="org.teleal.cling.android.AndroidUpnpServiceImpl" />
    </application>

</manifest>