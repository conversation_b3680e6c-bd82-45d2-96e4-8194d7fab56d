package com.harman.util

import android.util.Log
import com.blankj.utilcode.util.ConvertUtils
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.Locale

/**
 * @Description
 * <AUTHOR>
 * @Time 2024/12/4
 */
fun Byte.toHex(): String {
    return ConvertUtils.bytes2HexString(byteArrayOf(this))
}

fun Short.toHex(order: ByteOrder = ByteOrder.BIG_ENDIAN): String {
    return ConvertUtils.bytes2HexString(ByteBuffer.allocate(2).order(order).putShort(this).array())
}

fun Int.toHex(order: ByteOrder = ByteOrder.BIG_ENDIAN): String {
    return ConvertUtils.bytes2HexString(ByteBuffer.allocate(4).order(order).putInt(this).array())
}

fun retTokenValueAndUnprocessedPayload(unprocessedPayload: String): Array<String?> {
    val processedUnprocessedResult = arrayOfNulls<String>(2)
    val tokenIDValue: String
    try {
        val lengthIndex = 2
        val payloadIndex = lengthIndex + 2
        val dataLength = unprocessedPayload.substring(lengthIndex, payloadIndex).toInt(16)
        tokenIDValue = unprocessedPayload.substring(payloadIndex, payloadIndex + dataLength * 2)
        val nextInfoTokenIndex = payloadIndex + dataLength * 2
        val nextUnprocessedPayload = unprocessedPayload.substring(nextInfoTokenIndex)
        processedUnprocessedResult[0] = tokenIDValue
        processedUnprocessedResult[1] = nextUnprocessedPayload
    } catch (e: java.lang.Exception) {
        // e.printStackTrace();
    }
    return processedUnprocessedResult
}

fun getIntValueWith(tokenValue: String): Int {

    val byteArray = tokenValue.chunked(2).map { it.toInt(16).toByte() }.toByteArray()

    val buffer = ByteBuffer.wrap(byteArray).order(ByteOrder.LITTLE_ENDIAN)

    return buffer.short.toInt()
}

fun getIntValueWithFullByte(tokenValue: String): Int {
    val byteArray = tokenValue.chunked(2).map { it.toInt(16).toByte() }.toByteArray()

    val buffer = ByteBuffer.wrap(byteArray).order(ByteOrder.LITTLE_ENDIAN)

    return buffer.int
}

fun getFloatValueWith(tokenValue: String): Float {
    return ByteBuffer.wrap(tokenValue.chunked(2).map { it.toInt(16).toByte() }.toByteArray())
        .order(ByteOrder.LITTLE_ENDIAN)
        .float
}

fun getFloatValueWith2(tokenValue: String): Float {
    val intValue = ByteBuffer.wrap(tokenValue.chunked(2).map { it.toInt(16).toByte() }.toByteArray()).order(ByteOrder.LITTLE_ENDIAN).int
    return (intValue / 1000.0).toFloat()
}

fun Float.roundTo2DecimalPlaces(): Float {
    return (this * 100).toInt().toFloat() / 100
}

/**
 * append two bytes array to new [ByteArray]
 */
fun append(src: ByteArray, bytes: ByteArray?): ByteArray {
    if (bytes == null || bytes.isEmpty()) {
        return src
    }
    val appendPayload = ByteArray(src.size + bytes.size)

    System.arraycopy(src, 0, appendPayload, 0, src.size)

    System.arraycopy(bytes, 0, appendPayload, src.size, bytes.size)

    return appendPayload
}

/**
 *  append src [ByteArray] to new [ByteArray] with cmd and values
 *  only byte .length=1
 */
fun append(src: ByteArray, cmd: Byte, bytes: ByteArray?): ByteArray {
    if (bytes == null || bytes.isEmpty()) {
        return src
    }

    val appendPayload = ByteArray(src.size + 2 + bytes.size)
    val pivot = src.size

    System.arraycopy(src, 0, appendPayload, 0, src.size)

    try {
        appendPayload[pivot] = cmd
    } catch (e: NumberFormatException) {
        Log.e("com.harman.util.Exts", "append() >>> illegal Hex cmd[$cmd] while hexing")
        return src
    }

    appendPayload[pivot + 1] = bytes.size.toByte()
    System.arraycopy(bytes, 0, appendPayload, pivot + 2, bytes.size)

    return appendPayload
}

fun getIntFullByteArray(value: Int): ByteArray {

    val buffer = ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(value)

    return buffer.array()
}

fun getIntByteArray(value: Int): ByteArray {
    val buffer = ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(value)

    return buffer.array().sliceArray(0..1)
}

/**
 * For Alarm Custom Repeat Day
 *
 * @return
 */
fun String.toOneByte(): ByteArray {

    if (isBlank()) return byteArrayOf()

    val paddedBinaryString = this.padEnd(8, '0')

    val byteValue = paddedBinaryString.reversed().toInt(2)

    return byteArrayOf(byteValue.toByte())
}

fun ByteBuffer.safeSubArray(length: Int): ByteArray? {
    val buffer = this
    if (length <= 0 || buffer.remaining() < length) {
        return null
    }

    return runCatching {
        ByteArray(length).also { array ->
            buffer.get(array)
        }
    }.getOrNull()
}