package com.harman.discover.bean.bt

import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.command.partybox.gatt.ReqDeviceFeatureInfoCommand
import com.harman.command.partybox.gatt.PartyBoxGattCommandProcessor

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/6/4.
 *
 * @link [ReqDeviceFeatureInfoCommand] [PartyBoxGattCommandProcessor.parseDeviceFeatureInfo]
 */
data class PartyBoxDevFeat(
    private var _twsVolumeSync: Byte? = null,
    private var _userEQ: Byte? = null,
    private var _deviceAnalyticsInfo: Byte? = null,
    private var _feedbackTone: Byte? = null,
    private var _partylightStage: Byte? = null,
    private var _batterySavingMode: Byte? = null,
    private var _crossTws: Byte? = null,
    private var _radio: Byte? = null,
    private var _auracastSq: Byte? = null,
    private var _autoOff: Byte? = null,
    private var _autoStandby: Byte? = null,
    private var _resetPatternColor: Byte? = null,
    private var _projection: Byte? = null
) {

    val supportTwsVolumeSync: Boolean
        get() = SUPPORT_FLAG == _twsVolumeSync

    val supportUserEQ: Boolean
        get() = SUPPORT_FLAG == _userEQ

    val supportDeviceAnalyticsInfo: Boolean
        get() = SUPPORT_FLAG == _deviceAnalyticsInfo

    val supportFeedbackTone: Boolean
        get() = SUPPORT_FLAG == _feedbackTone

    val supportPartyLightStage: Boolean
        get() = SUPPORT_FLAG == _partylightStage

    val supportBatterySavingMode: Boolean
        get() = SUPPORT_FLAG == _batterySavingMode

    val supportCrossTws: Boolean
        get() = SUPPORT_FLAG == _crossTws

    val supportRadio:SupportRadio
        get()=SupportRadio.getSupportRadio(_radio?.toInt()?:0)

    val supportAuracastSq: Boolean
        get() = SUPPORT_FLAG == _auracastSq

    val supportResetPatternColor: Boolean
        get() = SUPPORT_FLAG == _resetPatternColor

    val supportAutoOff: Boolean
        get() = SUPPORT_FLAG == _autoOff

    val supportProjection: Boolean
        get() = SUPPORT_FLAG == _projection

    val supportAutoStandby: Boolean
        get() = SUPPORT_FLAG == _autoStandby

    fun update(command: Byte, payload: ByteArray?) = apply {
        when (command) {
            GattPacketFormat.DeviceFeatureInfo.TWS_VOLUME_SYNC -> {
                _twsVolumeSync = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.USER_EQ -> {
                _userEQ = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.DEVICE_ANALYTICS_INFO -> {
                _deviceAnalyticsInfo = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.FEEDBACK_TONE -> {
                _feedbackTone = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.PARTYLIGHT_STAGE -> {
                _partylightStage = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.BATTERY_SAVING_MODE -> {
                _batterySavingMode = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.CROSS_TWS -> {
                _crossTws = payload?.getOrNull(0)
            }
            GattPacketFormat.DeviceFeatureInfo.RADIO -> {
                _radio = payload?.getOrNull(0)
            }

            GattPacketFormat.DeviceFeatureInfo.AURACAST_SQ -> {
                _auracastSq = payload?.getOrNull(0)
            }

            GattPacketFormat.DeviceFeatureInfo.AUTO_OFF -> {
                _autoOff = payload?.getOrNull(0)
            }

            GattPacketFormat.DeviceFeatureInfo.AUTO_STANDBY -> {
                _autoStandby = payload?.getOrNull(0)
            }

            GattPacketFormat.DeviceFeatureInfo.RESET_PATTERN_COLOR -> {
                _resetPatternColor = payload?.getOrNull(0)
            }
        }
    }

    override fun toString(): String = StringBuilder().apply {
        append("TwsVolumeSync[").append(_twsVolumeSync).append("]\n")
        append("UserEQ[").append(_userEQ).append("]\n")
        append("DeviceAnalyticsInfo[").append(_deviceAnalyticsInfo).append("]\n")
        append("FeedbackTone[").append(_feedbackTone).append("]\n")
        append("PartyLightStage[").append(_partylightStage).append("]\n")
        append("BatterySavingMode[").append(_batterySavingMode).append("]\n")
        append("CrossTws[").append(_crossTws).append("]\n")
        append("_radio[").append(SupportRadio.getSupportRadio(_radio?.toInt()?:0)).append(",${_radio}]\n")
        append("_autoOff[").append(_autoOff).append("]\n")
        append("_autoStandby[").append(_autoStandby).append("]\n")
        append("_resetPatternColor[").append(_resetPatternColor).append("]\n")
    }.toString()

    companion object {
        private const val SUPPORT_FLAG = 0x1.toByte()
    }

    enum class SupportRadio(val cmd:Int,val desc:String){
        NotSupport(cmd = 0, desc = "not support"),
        FM(cmd = 1, desc = "FM  support"),
        DAB(cmd = 2, desc = "DAB support"),
        FMDAB(cmd = 3, desc = "FM and DAB support");
        companion object{
            @JvmStatic
            fun getSupportRadio(command: Int): SupportRadio = SupportRadio.values().find { it.cmd == command } ?: NotSupport
        }
    }
}