package com.harman.discover.bean

import androidx.annotation.AnyThread
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.session.BaseBusinessSession
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.info.DefaultRole
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.V5GattSession
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.IV5Read
import com.harman.v5protocol.bean.devinfofeat.IV5Write
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.discover.PartyInfo
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.withTimeout

/**
 * @Description Devices using the v5 protocol need to inherit this class
 * <AUTHOR>
 * @Time 2025/5/9
 */
abstract class V5BaseDevice<BTDeviceType : BaseBTDevice<DefaultRole, PartyInfo.Role>>(btDevice: BTDeviceType?, offlineDummy: BTDeviceType?) :
    BaseDevice<BaseBusinessSession, DefaultSppBusinessSession, BTDeviceType, DefaultRole, PartyInfo.Role, BTDeviceType>(
        btDevice = btDevice,
        offlineDummy = offlineDummy
    ) {
    override val sppBusiness: DefaultSppBusinessSession? = null
    override val songName: String? = null
    override val artistName: String? = null
    override val albumCover: String? = null
    override val mediumSource: String? = null
    override val trackSource: String? = null
    override val durationMills: Long? = null
    override val currentMills: Long? = null
    override val gattSession
        get() = super.gattSession as? V5GattSession<*>

    @AnyThread
    suspend inline fun <reified Resp : IV5Read> getDevInfoFeat() = gattSession?.getDevInfoFeat<Resp>()

    @AnyThread
    suspend inline fun <reified Resp1 : IV5Read, reified Resp2 : IV5Read> getDevInfoFeat2() =
        gattSession?.getDevInfoFeat2<Resp1, Resp2>()

    @AnyThread
    suspend inline fun <reified Resp1 : IV5Read, reified Resp2 : IV5Read, reified Resp3 : IV5Read> getDevInfoFeat3() =
        gattSession?.getDevInfoFeat3<Resp1, Resp2, Resp3>()

    @AnyThread
    fun asyncGetDevInfoFeat(vararg ids: V5DevInfoFeatID) = gattSession?.asyncGetDevInfoFeat(*ids)

    @AnyThread
    suspend fun setDevInfoFeat(vararg featValues: IV5Write) =
        gattSession?.setDevInfoFeat(*featValues)

    @AnyThread
    fun asyncSetDevInfoFeat(vararg featValues: IV5Write) = gattSession?.asyncSetDevInfoFeat(*featValues)

    @AnyThread
    suspend fun getGetDeviceAnalyticsData() = gattSession?.getGetDeviceAnalyticsData()

    @AnyThread
    fun asyncCleanDeviceAnalyticsData() = gattSession?.asyncCleanDeviceAnalyticsData()

    @AnyThread
    suspend inline fun <reified T : IV5Payload> awaitDevInfo(timeoutMs: Long = 6 * 1000): T? {
        val completer = CompletableDeferred<T>()
        val listener = object : IV5GattListener {
            override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) {
                super.onDevFeat(devInfoMap, isNotify)
                devInfoMap.entries.find { it.value is T }?.also {
                    unregisterDeviceListener(this)
                    completer.complete(it.value as T)
                }
            }
        }
        registerDeviceListener(listener)
        return runCatching {
            withTimeout(timeoutMs) {
                completer.await()
            }
        }.onFailure {
            unregisterDeviceListener(listener)
        }.getOrNull()
    }
}