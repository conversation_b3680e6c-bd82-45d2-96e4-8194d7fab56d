package com.harman.discover.info

import com.harman.discover.util.crc.McsCsrCrcHelper
import com.harman.discover.util.crc.QccVimicroAirohaCrcHelper

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/27.
 *
 * Different chip platforms which used by different devices.
 */
enum class EnumPlatform(val value: String) {

    VIMICRO(value = "VIMICRO"),
    QCC(value = "QCC"),
    CSR(value = "CSR"),
    MCS(value = "MCS"),
    AIROHA(value = "AIROHA"),
    MTK(value = "MTK"),
    BES(value = "BES"),
    AMLOGIC(value = "AMLOGIC");

    companion object {
        fun from(str: String?): EnumPlatform? {
            if (str.isNullOrBlank()) {
                return null
            }

            return entries.firstOrNull { enum ->
                enum.value.equals(str, true)
            }
        }
    }

    fun crc(filePath: String?): Long? = when (this) {
        MCS, CSR, BES -> McsCsrCrcHelper.crc(filePath = filePath)
        QCC, VIMICRO, AIROHA -> QccVimicroAirohaCrcHelper.crc(filePath = filePath)
        else -> null
    }
}