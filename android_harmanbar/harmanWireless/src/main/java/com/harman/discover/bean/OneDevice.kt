package com.harman.discover.bean

import androidx.annotation.IntRange
import com.harman.command.one.AttachOneCommand
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DJPad
import com.harman.command.one.bean.DebugMode
import com.harman.command.one.bean.DeviceNameRspUnion
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.EnumVoiceSource
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.GroupMode
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.PreviewSoundscapeRequest
import com.harman.command.one.bean.ProdSetting
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.Rear
import com.harman.command.one.bean.SendAppControllerRequest
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SetVoiceLanguageRequest
import com.harman.command.one.bean.SetVoiceToneRequest
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.TriggerDiagnosisRequest
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.session.OneBusiness
import com.harman.connect.syncCmd
import com.harman.discover.AlexaCBLStatusExt
import com.harman.discover.AutoPowerOffExt
import com.harman.discover.BatterySavingStatusExt
import com.harman.discover.BatteryStatusExt
import com.harman.discover.BluetoothConfigExt
import com.harman.discover.C4aPermissionStatusExt
import com.harman.discover.CalibrationExt
import com.harman.discover.ChromeCastOptInExt
import com.harman.discover.ControlSoundscapeV2Ext
import com.harman.discover.DeviceInfoExt
import com.harman.discover.DiagnosisStatusExt
import com.harman.discover.EQExt
import com.harman.discover.EQListExt
import com.harman.discover.FeatureSupportExt
import com.harman.discover.FeedbackToneConfigExt
import com.harman.discover.GetGroupDevicesFlagExt
import com.harman.discover.GetSmartBtnConfigExt
import com.harman.discover.GroupInfoExt
import com.harman.discover.GroupParameterExt
import com.harman.discover.LightInfoExt
import com.harman.discover.LwaStateResponseExt
import com.harman.discover.OneBusinessParams
import com.harman.discover.ProdSettingStatusExt
import com.harman.discover.RearSpeakerVolumeExt
import com.harman.discover.SleepTimerExt
import com.harman.discover.SoundscapeV2ListExt
import com.harman.discover.StreamingStatusExt
import com.harman.discover.VoiceLanguageResponseExt
import com.harman.discover.VoiceToneResponseExt
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.util.EnumJBLOneColorID
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.compareLater
import com.harman.discover.util.Tools.getBarPort
import com.harman.discover.util.Tools.getPlaybackDuration
import com.harman.discover.util.Tools.hmCastVer
import com.harman.discover.util.Tools.macAddress
import com.harman.discover.util.Tools.roundPercentage
import com.harman.discover.util.Tools.wlan0MacWithoutColon
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.ProductFeature
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DlnaPlayerStatus
import com.wifiaudio.utils.WifiResultsUtil
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by gerrardzhang on 2024/3/19.
 */
class OneDevice(
    bleDevice: BaseBTDevice<*, *>? = null,
    wifiDevice: DeviceItem? = null,
    offlineDummy: DeviceItem? = null
) : BaseDevice<
        OneBusiness,
        DefaultSppBusinessSession,
        OneBTDevice,
        OneRole,
        OneAuracastRole,
        DeviceItem
        >(btDevice = bleDevice, deviceItem = wifiDevice, offlineDummy = offlineDummy),
    OneBusiness, OneBusinessParams {

    override val volume: Int get() = volume()
    override val isPlaying: Boolean get() = isPlay()
    override val songName: String? get() = songName()
    override val artistName: String? get() = artistName()
    override val albumCover: String? get() = albumCover()
    override val mediumSource: String? get() = mediumSource()
    override val trackSource: String? get() = trackSource()
    override val durationMills: Long? get() = duration()
    override val currentMills: Long? get() = current()

    val mediaSupportDolbyAtoms: Boolean
        get() {
            if (Tools.isHDMIorTVSource(medium = mediumSource)) {
                return false
            }

            return mediaSupportDolbyAtoms()
        }

    val mediaSupportDolbyAudio: Boolean
        get() {
            if (Tools.isHDMIorTVSource(medium = mediumSource)) {
                return false
            }

            return mediaSupportDolbyAudio()
        }

    override val regionCode: String?
        get() = wifiDevice?.regionCode ?: deviceInfoExt?.deviceInfo?.countryCode

    override val featSupportExt: FeatureSupportExt?
        get() = compareLater(wifiDevice?.featSupportExt, bleDevice?.featSupportExt)

    override val smartBtnConfigExt: GetSmartBtnConfigExt?
        get() = compareLater(wifiDevice?.smartBtnConfigExt, bleDevice?.smartBtnConfigExt)

    override val soundscapeV2ListExt: SoundscapeV2ListExt?
        get() = compareLater(wifiDevice?.soundscapeV2ListExt, bleDevice?.soundscapeV2ListExt)

    override val controlSoundscapeV2Ext: ControlSoundscapeV2Ext?
        get() = compareLater(wifiDevice?.controlSoundscapeV2Ext, bleDevice?.controlSoundscapeV2Ext)

    override val deviceInfoExt: DeviceInfoExt?
        get() = compareLater(wifiDevice?.deviceInfoExt, bleDevice?.deviceInfoExt)

    override val voiceLanguageResponseExt: VoiceLanguageResponseExt?
        get() = compareLater(wifiDevice?.voiceLanguageResponseExt, bleDevice?.voiceLanguageResponseExt)

    override val startVoiceToneResponseExt: VoiceToneResponseExt?
        get() = compareLater(wifiDevice?.startVoiceToneResponseExt, bleDevice?.startVoiceToneResponseExt)

    override val endVoiceToneResponseExt: VoiceToneResponseExt?
        get() = compareLater(wifiDevice?.endVoiceToneResponseExt, bleDevice?.endVoiceToneResponseExt)

    override val c4aPermissionStatusExt: C4aPermissionStatusExt?
        get() = compareLater(wifiDevice?.c4aPermissionStatusExt, bleDevice?.c4aPermissionStatusExt)

    override val alexaCBLStatusExt: AlexaCBLStatusExt?
        get() = compareLater(wifiDevice?.alexaCBLStatusExt, bleDevice?.alexaCBLStatusExt)

    override val lwaStateResponseExt: LwaStateResponseExt?
        get() = compareLater(wifiDevice?.lwaStateResponseExt, bleDevice?.lwaStateResponseExt)

    override val getGroupDevicesFlagExt: GetGroupDevicesFlagExt?
        get() = compareLater(wifiDevice?.getGroupDevicesFlagExt, bleDevice?.getGroupDevicesFlagExt)

    /**
     * Use [OneDevice.supportAlexaVA()] in UI layer instead.
     */
    val isLwaSupport: Boolean
        get() {
            val feat = featSupportExt?.featSupport ?: return false
            return feat.isLwaSupport()
        }

    /**
     * @param supportAlexaVA [OneDevice.supportAlexaVA()]
     */
    fun isAlexaEnable(supportAlexaVA: Boolean): Boolean = if (supportAlexaVA) {
        lwaStateResponseExt?.status?.logged ?: false
    } else {
        alexaCBLStatusExt?.status?.logged ?: false
    }

    override val chromecastOptExt: ChromeCastOptInExt?
        get() = compareLater(wifiDevice?.chromecastOptExt, bleDevice?.chromecastOptExt)

    override val rearSpeakerVolumeExt: RearSpeakerVolumeExt?
        get() = compareLater(wifiDevice?.rearSpeakerVolumeExt, bleDevice?.rearSpeakerVolumeExt)

    override val diagnosisStatusExt: DiagnosisStatusExt?
        get() = compareLater(wifiDevice?.diagnosisStatusExt, bleDevice?.diagnosisStatusExt)

    override val calibrationExt: CalibrationExt?
        get() = compareLater(wifiDevice?.calibrationExt, bleDevice?.calibrationExt)

    override val autoPowerOffExt: AutoPowerOffExt?
        get() = compareLater(wifiDevice?.autoPowerOffExt, bleDevice?.autoPowerOffExt)

    override val bluetoothConfigExt: BluetoothConfigExt?
        get() = compareLater(wifiDevice?.bluetoothConfigExt, bleDevice?.bluetoothConfigExt)

    override val feedbackToneConfigExt: FeedbackToneConfigExt?
        get() = compareLater(wifiDevice?.feedbackToneConfigExt, bleDevice?.feedbackToneConfigExt)

    override val batterySavingStatus: BatterySavingStatusExt?
        get() = compareLater(wifiDevice?.batterySavingStatus, bleDevice?.batterySavingStatus)

    override val groupInfoExt: GroupInfoExt?
        get() = compareLater(wifiDevice?.groupInfoExt, bleDevice?.groupInfoExt)

    override val eqListExt: EQListExt?
        get() = compareLater(wifiDevice?.eqListExt, bleDevice?.eqListExt)

    override val batteryStatusExt: BatteryStatusExt?
        get() = compareLater(wifiDevice?.batteryStatusExt, bleDevice?.batteryStatusExt)

    override val streamingStatusExt: StreamingStatusExt?
        get() = compareLater(wifiDevice?.streamingStatusExt, bleDevice?.streamingStatusExt)

    override val sleepTimerExt: SleepTimerExt?
        get() = compareLater(wifiDevice?.sleepTimerExt, bleDevice?.sleepTimerExt)

    override val groupParameterExt: GroupParameterExt?
        get() = compareLater(wifiDevice?.groupParameterExt, bleDevice?.groupParameterExt)

    override val prodSettingStatusExt: ProdSettingStatusExt?
        get() = wifiDevice?.prodSettingStatusExt

    override var lightInfoExt: LightInfoExt? = null
        get() = compareLater(wifiDevice?.lightInfoExt, bleDevice?.lightInfoExt)

    val isChromeCastEnabled: Boolean
        get() = true == c4aPermissionStatusExt?.status?.enable

    val firmware: String?
        get() = wifiDevice?.deviceItem?.devStatus?.firmware ?: deviceInfoExt?.deviceInfo?.firmware ?: offlineDummy?.devStatus?.firmware

    val pbDuration: Int?
        get() = wifiDevice?.deviceItem?.getPlaybackDuration() ?: bleDevice?.playbackDuration ?: offlineDummy?.getPlaybackDuration()

     val hmCastVer: String?
        get() = wifiDevice?.deviceItem?.hmCastVer() ?: deviceInfoExt?.deviceInfo?.hmCastVer

      fun hmCastMajorVer(): String?{
         val index = hmCastVer?.indexOf(".")
          if(index == null || index == -1){
              return hmCastVer
          }
        return hmCastVer?.substring(0,index)
     }

    val serialNumber: String?
        get() = getValidSerialNumber()

    override val eqExt: EQExt?
        get() = compareLater(wifiDevice?.eqExt, bleDevice?.eqExt)

    @get:IntRange(from = 0L, to = 100L)
    override val batteryLevel: Int
        get() = batteryStatusExt?.data?.batteryLevel?.roundPercentage() ?: 0

    override val isCharging: Boolean
        get() = batteryStatusExt?.data?.isACMode ?: false

    val adaptBusiness: OneBusiness?
        get() = if (isWiFiOnline) wifiSession else gattBusiness

    val oneOsVer: String?
        get() = deviceInfoExt?.deviceInfo?.oneOsVer ?: offlineDummy?.Version

    val prevOneOsVer: String?
        get() = deviceInfoExt?.deviceInfo?.prevOneOsVer

    val gattBusiness: OneBusiness?
        get() = gattSession as? OneBusiness

    override val sppBusiness: DefaultSppBusinessSession?
        get() = sppSession as? DefaultSppBusinessSession

    override val bleDevice: OneBTDevice?
        get() = _btDevice as? OneBTDevice

    override val hadAuraCastPhysicalBtn: Boolean
        get() = bleDevice?.hadAuraCastPhysicalBtn ?: false

    val isNetworkConnected: Boolean
        get() = bleDevice?.isNetworkConnected ?: false

    override val isOnline: Boolean
        get() = isBLEOnline && !isNetworkConnected || isWiFiOnline || isSPPOnline

    val supportSoundScapeV1: Boolean
        get() = (pid?.let{
            AppConfigurationUtils.getModelConfig(it)?.capability?.contains(ProductFeature.Moment)
        } ?: false) && (featSupportExt?.featSupport?.soundscape?.isSupport ?: false)

    val supportSoundScapeV2: Boolean
        get() = (pid?.let{
            AppConfigurationUtils.getModelConfig(it)?.capability?.contains(ProductFeature.Moment)
        } ?: false) && (smartBtnConfigExt?.config?.supportSoundScapeV2 ?: false)

    val supportIRLearning: Boolean
        get() = (pid?.let{
            AppConfigurationUtils.getModelConfig(it)?.capability?.contains(ProductFeature.IRLearning)
        } ?: false) && (featSupportExt?.featSupport?.isLRLearningSupport() ?: false)

    val supportSmartMode: Boolean
        get() = featSupportExt?.featSupport?.isSmartModeSupport() ?: false

    val configuredDiagnosisReport: Boolean
        get() = pid?.let{
            AppConfigurationUtils.getModelConfig(it)?.capability?.contains(ProductFeature.DiagnosisReport)
        } ?: false

    val supportBatterySaving: Boolean
        get() = featSupportExt?.featSupport?.isBatterySavingSupport() ?: false

    var isLoadingDiagnosisReport: Boolean = false

    val support7BandsEq: Boolean
        get() = featSupportExt?.featSupport?.userEQ?.band == "7"

    val supportPresetEq: Boolean
        get() = featSupportExt?.featSupport?.userEQ?.presetSupport == "true"

    var inOobe = AtomicBoolean(false)

    override val cid: String?
        get() = wifiDevice?.let { wifiDevice ->
            EnumJBLOneColorID.str2Enum(wifiDevice.colorStr)?.intValue?.toString(16)
        } ?: bleDevice?.colorID ?: dummyCid

    /**
     * App need to get the real time role by command getGroupInfo, notifyGroupInfo.
     */
    val role: OneRole
        get() {
            val _groupInfo = groupInfoExt?.groupInfo ?: return getRoleByBroadcast()

            if (_groupInfo.groupMode == GroupMode.SINGLE.value) {
                return OneRole.SINGLE
            }

            return if (wlan0MacWithoutColon() == _groupInfo.groupInfo?.group?.id) {
                OneRole.GO
            } else {
                OneRole.GC
            }
        }

    /**
     * Lower case.
     */
    override fun macAddressCRC(): String? {
        return bleDevice?.macAddressCRC
    }

    private fun getRoleByBroadcast(): OneRole {
        return bleDevice?.role ?: OneRole.SINGLE
    }

    private fun getValidSerialNumber(): String? {
        var sn = wifiDevice?.serialNumber
        if (sn.isNullOrBlank()) {
            sn = deviceInfoExt?.deviceInfo?.serialNumber
        }
        if (sn.isNullOrBlank()) {
            sn = offlineDummy?.serialNum
        }
        return sn
    }

    /**
     * @Source:
     * [smartBtnConfigExt]:
     * [EnumCommandMapping.GET_SMART_BTN_CONFIG]
     * [EnumCommandMapping.SET_SMART_BTN_CONFIG]
     */
    val activeSoundscapeId: Int?
        get() = smartBtnConfigExt?.config?.soundscape?.activeSoundscapeId

    /**
     * AP Name of WiFi
     */
    val eSSID: String?
        get() = wifiDevice?.deviceItem?.devStatus?.essid

    /**
     * Only use source from rsp of [getGroupInfo] [GroupInfoExt]. Ignore info in BLE broadcast.
     */
    val groupId: String?
        get() = groupInfoExt?.groupInfo?.groupInfo?.getValidGroupId()

    /**
     * @see [DlnaPlayerStatus.IPlayStatus]
     */
    val dlnaPlayStatus: String?
        get() = wifiDevice?.dlnaPlayStatus

    val rears: List<Rear>?
        get() = wifiDevice?.rears

    val isStandby: Boolean
        get() = true == bleDevice?.isStandby

    override fun sendAttachCommand(command: AttachOneCommand, port: String?) {
        adaptBusiness?.sendAttachCommand(command = command, port = port)
    }

    override fun getDeviceInfo(port: String?) {
        adaptBusiness?.getDeviceInfo(port = port)
    }

    override fun sendSetDeviceName(name: String, port: String?) {
        adaptBusiness?.sendSetDeviceName(name = name, port = port)

        // Update name instantly before set result.
        bleDevice?.let { device ->
            device.onDeviceName(name = name)
            device.gattListeners.filterIsInstance<IOneDeviceListener>().forEach {
                it.onDeviceName(union = DeviceNameRspUnion(name = name, editable = true))
            }
        }

        wifiDevice?.let { device ->
            device.deviceItem.Name = name
            device.wifiListeners.forEach {
                it.onDeviceName(union = DeviceNameRspUnion(name = name, editable = true))
            }
        }
    }

    override fun getAPList(port: String?) {
        adaptBusiness?.getAPList(port = port)
    }

    override fun setCastGroup(payloadJson: String, port: String?) {
        adaptBusiness?.setCastGroup(payloadJson = payloadJson, port = port)
    }

    override fun playMusic() {
        adaptBusiness?.playMusic()
    }

    override fun pauseMusic() {
        adaptBusiness?.pauseMusic()
    }

    override fun prevMusic() {
        adaptBusiness?.prevMusic()
    }

    override fun nextMusic() {
        adaptBusiness?.nextMusic()
    }

    override fun setRemoteVolume(value: Int) {
        adaptBusiness?.setRemoteVolume(value = value.roundPercentage())
    }

    override fun getRemoteVolume() {
        adaptBusiness?.getRemoteVolume()
    }

    /**
     * Available for WiFi state.
     */
    fun seek(secs: Long) {
        wifiSession?.seek(secs = secs)
    }

    override fun getFeatureSupport(port: String?) {
        adaptBusiness?.getFeatureSupport(port = port)
    }

    override fun getEQList(port: String?) {
        adaptBusiness?.getEQList(port = port)
    }

    override fun setActiveEQ(item: SetActiveEQItemRequest, port: String?) {
        adaptBusiness?.setActiveEQ(item = item, port = port)
    }

    override fun getEQ(port: String?) {
        adaptBusiness?.getEQ(port = port)
    }

    override fun setEQ(eq: EQSetting, port: String?) {
        adaptBusiness?.setEQ(eq = eq, port = port)
    }

    override fun startAuth(timeoutSecs: Int, port: String?) {
        adaptBusiness?.startAuth(timeoutSecs = timeoutSecs, port = port)
    }

    override fun cancelAuth(port: String?) {
        adaptBusiness?.cancelAuth(port = port)
    }

    override fun setWifiNetwork(req: SetWifiNetworkRequest, port: String?) {
        adaptBusiness?.setWifiNetwork(req = req, port = port)
    }

    override fun getOtaStatus(port: String?) {
        wifiSession?.getOtaStatus(port = port)
    }

    override fun requestDeviceOta(port: String?) {
        adaptBusiness?.requestDeviceOta(port = port)
    }

    override fun getOtaAccessPoint(port: String?) {
        adaptBusiness?.getOtaAccessPoint(port = port)
    }

    override fun getBatteryStatus(port: String?) {
        adaptBusiness?.getBatteryStatus(port = port)
    }

    override fun setCalibration(port: String?) {
        adaptBusiness?.setCalibration(port = port)
    }

    override fun getCalibrationState(port: String?) {
        adaptBusiness?.getCalibrationState(port = port)
    }

    override fun cancelCalibration(port: String?) {
        adaptBusiness?.cancelCalibration(port = port)
    }

    override fun getC4aPermissionStatus(port: String?) {
        adaptBusiness?.getC4aPermissionStatus(port = port)
    }

    override fun setC4aPermissionStatus(status: EnumC4aPermissionStatus, port: String?) {
        adaptBusiness?.setC4aPermissionStatus(status = status, port = port)
    }

    override fun getChromeCastOptIn(port: String?) {
        adaptBusiness?.getChromeCastOptIn(port = port)
    }

    override fun setChromeCastOptIn(optIn: ChromeCastOpt, port: String?) {
        adaptBusiness?.setChromeCastOptIn(optIn = optIn, port = port)
    }

    override fun enterAuraCast(port: String?) {
        adaptBusiness?.enterAuraCast(port = port)
    }

    override fun exitAuraCast(port: String?) {
        adaptBusiness?.exitAuraCast(port = port)
    }

    override fun getAuraCastSqMode(port: String?) {
        adaptBusiness?.getAuraCastSqMode(port = port)
    }

    override fun setAuraCastSqMode(on: Boolean, port: String?) {
        adaptBusiness?.setAuraCastSqMode(on, port = port)
    }

    override fun setPlayPartySound(partySound: Int, port: String?) {
        adaptBusiness?.setPlayPartySound(partySound = partySound, port = port)
    }

    override fun setDjEvent(value: String, port: String?) {
        adaptBusiness?.setDjEvent(value = value, port = port)
    }

    override fun setDebugMode(debugMode: DebugMode, port: String?) {
        adaptBusiness?.setDebugMode(debugMode = debugMode, port = port)
    }

    override fun alexaCBLStatus(port: String?) {
        adaptBusiness?.alexaCBLStatus(port = port)
    }

    override fun alexaCBLLogout(port: String?) {
        adaptBusiness?.alexaCBLLogout(port = port)
    }

    override fun getLWAState(port: String?) {
        // This command only support WiFi session.
        wifiSession?.getLWAState(port = port)
    }

    override fun requestLWALogout(port: String?) {
        // This command only support WiFi session.
        wifiSession?.requestLWALogout(port = port)
    }

    override fun setLWAAuthCode(lwaInfo: LWAInfo, port: String?) {
        // This command only support WiFi session.
        wifiSession?.setLWAAuthCode(lwaInfo = lwaInfo, port = port)
    }

    override fun getVoiceRequestStartTone(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        wifiSession?.getVoiceRequestStartTone(source = source, port = port)
    }

    override fun getVoiceRequestEndTone(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        wifiSession?.getVoiceRequestEndTone(source = source, port = port)
    }

    override fun getVoiceLanguage(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        wifiSession?.getVoiceLanguage(source = source, port = port)
    }

    override fun getSupportedVoiceLanguage(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        wifiSession?.getSupportedVoiceLanguage(source = source, port = port)
    }

    override fun requestGoogleLogout(port: String?) {
        // This command only support WiFi session.
        wifiSession?.requestGoogleLogout(port = port)
    }

    override fun setVoiceRequestStartTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        // This command only support WiFi session.
        wifiSession?.setVoiceRequestStartTone(setVoiceToneRequest = setVoiceToneRequest, port = port)
    }

    override fun setVoiceRequestEndTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        // This command only support WiFi session.
        wifiSession?.setVoiceRequestEndTone(setVoiceToneRequest = setVoiceToneRequest, port = port)
    }

    override fun setVoiceLanguage(setVoiceLanguageRequest: SetVoiceLanguageRequest, port: String?) {
        // This command only support WiFi session.
        wifiSession?.setVoiceLanguage(setVoiceLanguageRequest = setVoiceLanguageRequest, port = port)
    }

    override fun getSmartBtnConfig(port: String?) {
        adaptBusiness?.getSmartBtnConfig(port = port)
    }

    override fun setSmartBtnConfig(config: SmartBtnConfig, port: String?) {
        adaptBusiness?.setSmartBtnConfig(config = config, port = port)
    }

    override fun previewSoundscape(request: PreviewSoundscapeRequest, port: String?) {
        adaptBusiness?.previewSoundscape(request = request, port = port)
    }

    override fun cancelSoundscape(port: String?) {
        adaptBusiness?.cancelSoundscape(port = port)
    }

    override fun getSoundscapeV2Config(port: String?) {
        adaptBusiness?.getSoundscapeV2Config(port = port)
    }

    override fun setSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest, port: String?) {
        adaptBusiness?.setSoundscapeV2Config(req = req, port = port)
    }

    override fun controlSoundscapeV2(req: ControlSoundscapeV2, port: String?) {
        adaptBusiness?.controlSoundscapeV2(req = req, port = port)
    }

    override fun getSoundscapeV2State(port: String?) {
        adaptBusiness?.getSoundscapeV2State(port = port)
    }

    override fun getProductUsage(port: String?) {
        adaptBusiness?.getProductUsage(port = port)
    }

    override fun getDeviceName(port: String?) {
        adaptBusiness?.getDeviceName(port = port)
    }

    override fun playDemoSound(port: String?) {
        adaptBusiness?.playDemoSound(port = port) // this is a WiFi only command currently.
    }

    override fun cancelDemoSound(port: String?) {
        adaptBusiness?.cancelDemoSound(port = port) // this is a WiFi only command currently.
    }

    override fun getRearSpeakerVolume(port: String?) {
        adaptBusiness?.getRearSpeakerVolume(port = port)
    }
    fun sendAppController(req: SendAppControllerRequest) {
        sendAppController(req = req, port = getBarPort())
    }

    override fun sendAppController(req: SendAppControllerRequest, port: String?) {
        adaptBusiness?.sendAppController(req = req, port = port)
    }

    override fun setFactoryRestore(port: String?) {
        adaptBusiness?.setFactoryRestore(port = port)
    }

    override fun setAutoPowerOffTimer(secs: Int, port: String?) {
        adaptBusiness?.setAutoPowerOffTimer(secs = secs, port = port)
    }

    override fun getAutoPowerOffTimer(port: String?) {
        adaptBusiness?.getAutoPowerOffTimer(port = port)
    }

    override fun setBluetoothConfig(config: EnumBluetoothConfig, port: String?) {
        adaptBusiness?.setBluetoothConfig(config = config, port = port)
    }

    override fun getBluetoothConfig(port: String?) {
        adaptBusiness?.getBluetoothConfig(port = port)
    }

    override fun setFeedbackToneConfig(config: EnumFeedbackToneConfig, port: String?) {
        adaptBusiness?.setFeedbackToneConfig(config = config, port = port)
    }

    override fun getFeedbackToneConfig(port: String?) {
        adaptBusiness?.getFeedbackToneConfig(port = port)
    }

    override fun getGeneralConfig(type: GeneralConfigType, port: String?) {
        adaptBusiness?.getGeneralConfig(type = type, port = port)
    }

    override fun setGeneralConfig(req: GeneralConfig, port: String?) {
        adaptBusiness?.setGeneralConfig(req = req, port = port)
    }

    override fun setBatterySavingStatus(status: EnumBatterySavingStatus, port: String?) {
        adaptBusiness?.setBatterySavingStatus(status = status, port = port)
    }

    override fun getBatterySavingStatus(port: String?) {
        adaptBusiness?.getBatterySavingStatus(port = port)
    }

    override fun setIRLearn(port: String?) {
        adaptBusiness?.setIRLearn(port = port)
    }

    override fun triggerCastLED(port: String?) {
        adaptBusiness?.triggerCastLED(port = port)
    }

    override fun clearHistoryOneOSVersion(port: String?) {
        adaptBusiness?.clearHistoryOneOSVersion(port = port)
    }

    override fun setAnchorIndicate(timeoutSecs: Int, port: String?) {
        adaptBusiness?.setAnchorIndicate(timeoutSecs = timeoutSecs, port = port)
    }

    override fun setAnchorCancel(port: String?) {
        adaptBusiness?.setAnchorCancel(port = port)
    }

    override fun destroyCastGroup(port: String?) {
        adaptBusiness?.destroyCastGroup(port = port)
    }

    override fun getGroupInfo(port: String?) {
        adaptBusiness?.getGroupInfo(port = port)
    }

    override fun groupCalibration(anchor: String, step: Int, port: String?) {
        adaptBusiness?.groupCalibration(anchor = anchor, step = step, port = port)
    }

    override fun switchStereoChannel(port: String?) {
        adaptBusiness?.switchStereoChannel(port = port)
    }

    override fun skipDemoSound(port: String?) {
        adaptBusiness?.skipDemoSound(port = port)
    }

    override fun getGroupParameter(port: String?) {
        adaptBusiness?.getGroupParameter(port = port)
    }

    override fun getGroupCalibrationState(port: String?) {
        adaptBusiness?.getGroupCalibrationState(port = port)
    }

    override fun renameGroup(name: String, port: String?) {
        adaptBusiness?.renameGroup(name = name, port = port)
    }

    override fun triggerDiagnosis(req: TriggerDiagnosisRequest, port: String?) {
        adaptBusiness?.triggerDiagnosis(req = req, port = port)
    }

    override fun getDJPad(port: String?) {
        adaptBusiness?.getDJPad(port = port)
    }

    override fun setDJPad(djPad: DJPad, port: String?) {
        adaptBusiness?.setDJPad(djPad = djPad, port = port)
    }

    override fun getStreamingStatus(port: String?) {
        adaptBusiness?.getStreamingStatus(port = port)
    }

    override fun setSleepTimer(secs: Int, port: String?) {
        adaptBusiness?.setSleepTimer(secs = secs, port = port)
    }

    override fun getSleepTimer(port: String?) {
        adaptBusiness?.getSleepTimer(port = port)
    }

    override fun getPersonalListeningMode(port: String?) {
        adaptBusiness?.getPersonalListeningMode(port = port)
    }

    override fun setPersonalListeningMode(isOn: Boolean, port: String?) {
        adaptBusiness?.setPersonalListeningMode(isOn = isOn, port = port)
    }

    override fun getMediaSourceStatus(port: String?) {
        adaptBusiness?.getMediaSourceStatus(port = port)
    }

    override fun getColorPicker(port: String?) {
        adaptBusiness?.getColorPicker(port = port)
    }

    override fun setColorPicker(picker: ColorPicker, port: String?) {
        adaptBusiness?.setColorPicker(picker = picker, port = port)
    }

    override fun getOOBEEncryptionKey(port: String?) {
        adaptBusiness?.getOOBEEncryptionKey(port = port)
    }

    override fun getGroupDevicesOtaStatus(port: String?) {
        adaptBusiness?.getGroupDevicesOtaStatus(port = port)
    }

    override fun getGroupDevicesFlag(port: String?) {
        adaptBusiness?.getGroupDevicesFlag(port = port)
    }

    private var lastTimeZone: String? = null

    /**
     * This is an internal API and can only be invoked while device is WiFi online.
     */
    override fun setTimeZone(formatDate: String, port: String?) {
        if (!isWiFiOnline) return
        if (lastTimeZone == formatDate) return
        lastTimeZone = formatDate

        wifiSession?.setTimeZone(formatDate = formatDate, port = port)
    }

    private var lastCountryCode: String? = null

    // Set only first time WiFi detected.
    override fun setCountryCode(code: String, port: String?) {
        if (!isWiFiOnline) return
        if (lastCountryCode == code) return
        lastCountryCode = code

        adaptBusiness?.setCountryCode(code = code, port = port)
    }

    fun updateEQSettings(settings: EQSetting) {
        wifiDevice?.eqExt?.data?.eqSetting = settings
        bleDevice?.eqExt?.data?.eqSetting = settings
    }

    fun updateActiveEQ(request: SetActiveEQItemRequest) {
        wifiDevice?.eqListExt?.data?.activeEQID = request.activeEQID
        bleDevice?.eqListExt?.data?.activeEQID = request.activeEQID

        if (request.isCustomEQActive()) {
            wifiDevice?.eqListExt?.data?.findCustomEQ()?.eqListPayload?.gain = request.eqPayload?.gain
            bleDevice?.eqListExt?.data?.findCustomEQ()?.eqListPayload?.gain = request.eqPayload?.gain
        }
    }

    /**
     * Force change [ChromeCastOpt] stored in device cache.
     */
    fun onChromeCastOptIn(optIn: ChromeCastOpt?) {
        wifiDevice?.onChromeCastOptIn(optIn = optIn)
        bleDevice?.onChromeCastOptIn(optIn = optIn)
    }

    override fun setSmartMode(isOn: Boolean, port: String?) {
        adaptBusiness?.setSmartMode(isOn = isOn, port = port)
    }

    override fun getSmartMode(port: String?) {
        adaptBusiness?.getSmartMode(port = port)
    }

    override fun getRearSpeakerStatus(port: String?) {
        adaptBusiness?.getRearSpeakerStatus(port = port)
    }

    override fun setLightInfo(req: SetLightInfoRequest, port: String?) {
        adaptBusiness?.setLightInfo(req, port = port)
    }

    override fun getLightInfo(port: String?) {
        adaptBusiness?.getLightInfo(port = port)
    }

    override fun resetLightPatternColor(id: String, port: String?) {
        adaptBusiness?.resetLightPatternColor(id, port = port)
    }

    override fun toString(): String {
        val sb = StringBuilder(super.toString())

        sb.append("pid[").append(pid).append("]\n")
        sb.append("cid[").append(cid).append("]\n")
        sb.append("serialNum[").append(serialNumber).append("]\n")
        sb.append("deviceName[").append(deviceName).append("]\n")
        sb.append("Mac From WiFi[${wifiDevice?.deviceItem?.macAddress()}").append("\n")
        sb.append("DeviceItem.UUID[").append(wifiDevice?.deviceItem?.uuid).append("]\n")
        sb.append("batteryLv source DeviceItem[")
            .append(wifiDevice?.deviceItem?.devInfoExt?.batteryPercent)
            .append("]BatteryStatusExt[${batteryStatusExt?.data?.batteryLevel}")
            .append("]\n")

        return sb.toString()
    }

    suspend fun getProSetting(port: String? = null): ProdSettingResponse<*, *>? {
        return syncCmd<ProdSettingResponse<*, *>>(
            reqCmd = EnumCommandMapping.GET_PROD_SETTING,
            params = HashMap<String, String>().apply {
                this[ProdSetting.PureVoice.value] = ""
                this[ProdSetting.FlexListening.value] = ""
                this[ProdSetting.DeepSleep.value] = ""
            },
            port = port
        )
    }

    /**
     * Set Prod Setting
     * @param pureVoice, value can be [ProdSettingResponse.ON] or [ProdSettingResponse.OFF]
     * @param flexListening, value can be [ProdSettingResponse.FLEX_LISTENING_MONO],
     * [ProdSettingResponse.FLEX_LISTENING_STEREO] or [ProdSettingResponse.OFF]
     * @param deepSleep, value can be [ProdSettingResponse.ON], [ProdSettingResponse.OFF]
     */
    suspend fun setProSetting(pureVoice: String? = null, flexListening: String? = null, deepSleep: String? = null, port: String? = null) {
        syncCmd<Unit>(
            reqCmd = EnumCommandMapping.SET_PROD_SETTING,
            params = HashMap<String, String>().apply {
                pureVoice?.let { this[ProdSetting.PureVoice.value] = it }
                flexListening?.let { this[ProdSetting.FlexListening.value] = it }
                deepSleep?.let { this[ProdSetting.DeepSleep.value] = it }
            }, port = port
        )
    }
}