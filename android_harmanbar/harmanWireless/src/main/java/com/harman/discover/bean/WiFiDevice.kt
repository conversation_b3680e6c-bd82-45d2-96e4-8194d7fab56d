package com.harman.discover.bean

import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DeviceInfo
import com.harman.command.one.bean.DiagnosisStatusResponse
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GetVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceToneResponse
import com.harman.command.one.bean.LightInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.MediaResponse
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.Rear
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.SoundscapeV2Item
import com.harman.command.one.bean.StreamingStatus
import com.harman.command.one.command.EnumMuteStatus
import com.harman.connect.OneWiFiSession
import com.harman.connect.listener.IOneDeviceListener
import com.harman.discover.AlexaCBLStatusExt
import com.harman.discover.AutoPowerOffExt
import com.harman.discover.BatterySavingStatusExt
import com.harman.discover.BatteryStatusExt
import com.harman.discover.BluetoothConfigExt
import com.harman.discover.C4aPermissionStatusExt
import com.harman.discover.CalibrationExt
import com.harman.discover.ChromeCastOptInExt
import com.harman.discover.ControlSoundscapeV2Ext
import com.harman.discover.DeviceInfoExt
import com.harman.discover.DiagnosisStatusExt
import com.harman.discover.EQExt
import com.harman.discover.EQListExt
import com.harman.discover.FeatureSupportExt
import com.harman.discover.FeedbackToneConfigExt
import com.harman.discover.GetGroupDevicesFlagExt
import com.harman.discover.GetSmartBtnConfigExt
import com.harman.discover.GroupInfoExt
import com.harman.discover.GroupParameterExt
import com.harman.discover.LightInfoExt
import com.harman.discover.LwaStateResponseExt
import com.harman.discover.OneBusinessParams
import com.harman.discover.ProdSettingStatusExt
import com.harman.discover.RearSpeakerVolumeExt
import com.harman.discover.SleepTimerExt
import com.harman.discover.SoundscapeV2ListExt
import com.harman.discover.StreamingStatusExt
import com.harman.discover.VoiceLanguageResponseExt
import com.harman.discover.VoiceToneResponseExt
import com.harman.discover.bean.bt.WiFiListenerProxy
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.cdQuality
import com.harman.discover.util.Tools.colorStr
import com.harman.discover.util.Tools.hiResSampleRate
import com.harman.discover.util.Tools.highQuality
import com.harman.discover.util.Tools.isDLNAPlay
import com.harman.discover.util.Tools.isMediaSourcePlaying
import com.harman.discover.util.Tools.macAddressCRCWithCache
import com.harman.discover.util.Tools.metaDataInfo
import com.harman.discover.util.Tools.mp3Quality
import com.harman.discover.util.Tools.supportDolbyAtoms
import com.harman.discover.util.Tools.supportDolbyAudio
import com.harman.discover.util.Tools.supportHD
import com.harman.discover.util.Tools.supportHiRes
import com.harman.discover.util.Tools.supportUHD
import com.harman.discover.util.Tools.toPid
import com.harman.discover.util.Tools.update
import com.harman.log.Logger
import com.harman.support.LinkPlaySDKHelperProxy
import com.wifiaudio.model.DeviceItem
import java.util.AbstractQueue
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Created by gerrardzhang on 2024/4/12.
 *
 * Describe a JBL One device perspective from WiFi functions.
 */
class WiFiDevice(
    var deviceItem: DeviceItem
) : OneBusinessParams {

    val firstTouchTime: Long = System.currentTimeMillis()

    /**
     * @return the exists [_wifiSession] if it is not null to avoid generate different sessions
     * with same IP Address.
     *
     * Otherwise generate a new one based on the latest IP address from [DeviceItem.IP] cause BLE address
     * might be changed in interval.
     */
    private var _wifiSession: OneWiFiSession? = null
    val wifiSession: OneWiFiSession?
        get() = syncGeneWiFiSession()

    private val _wifiListeners = ConcurrentLinkedQueue<IOneDeviceListener>()
    val wifiListeners: AbstractQueue<IOneDeviceListener>
        get() = _wifiListeners

    val UUID: String?
        get() = deviceItem.macAddressCRCWithCache()?.lowercase()

    val pid: String?
        get() = deviceItem.toPid()

    val colorStr: String?
        get() = deviceItem.colorStr()

    val serialNumber: String?
        get() = deviceItem.devStatus?.hm_sn

    override val volume: Int
        get() = deviceItem.devInfoExt?.dlnaCurrentVolume ?: 0

    private var _isPlaying: Boolean = false
    override val isPlaying: Boolean
        get() = deviceItem.devInfoExt?.dlnaPlayStatus?.isDLNAPlay() ?: _isPlaying

    val dlnaPlayStatus: String?
        get() = deviceItem.devInfoExt?.dlnaPlayStatus

    override val songName: String?
        get() = deviceItem.devInfoExt?.albumInfo?.title

    override val artistName: String?
        get() = deviceItem.devInfoExt?.albumInfo?.artist

    override val albumCover: String?
        get() = deviceItem.devInfoExt?.albumInfo?.albumArtURI

    override val mediumSource: String?
        get() = deviceItem.devInfoExt?.dlnaPlayMedium

    override val trackSource: String?
        get() = deviceItem.devInfoExt?.dlnaTrackSource

    override val durationMills: Long?
        get() = deviceItem.devInfoExt?.dlnaTotalTime?.let { it * 1000L } // secs to mills

    override val currentMills: Long?
        get() = deviceItem.devInfoExt?.dlnaTickTime?.let { it * 1000L } // secs to mills

    override val regionCode: String?
        get() = deviceItem.devStatus?.region

    override var groupInfoExt: GroupInfoExt? = null

    override var featSupportExt: FeatureSupportExt? = null
        private set

    override var smartBtnConfigExt: GetSmartBtnConfigExt? = null
        private set

    override var eqListExt: EQListExt? = null
        private set

    override var eqExt: EQExt? = null
        private set

    override var batteryStatusExt: BatteryStatusExt? = null
        private set

    override var calibrationExt: CalibrationExt? = null
        private set

    override var soundscapeV2ListExt: SoundscapeV2ListExt? = null
        private set

    override var controlSoundscapeV2Ext: ControlSoundscapeV2Ext? = null
        private set

    override var voiceLanguageResponseExt: VoiceLanguageResponseExt? = null
        private set

    override var startVoiceToneResponseExt: VoiceToneResponseExt? = null
        private set

    override var endVoiceToneResponseExt: VoiceToneResponseExt? = null
        private set

    override var deviceInfoExt: DeviceInfoExt? = null
        private set

    override var c4aPermissionStatusExt: C4aPermissionStatusExt? = null
        private set

    override var alexaCBLStatusExt: AlexaCBLStatusExt? = null
        private set

    override var lwaStateResponseExt: LwaStateResponseExt? = null
        private set

    override var chromecastOptExt: ChromeCastOptInExt? = null
        private set

    override var rearSpeakerVolumeExt: RearSpeakerVolumeExt? = null
        private set

    override var diagnosisStatusExt: DiagnosisStatusExt? = null
        private set

    override var autoPowerOffExt: AutoPowerOffExt? = null
        private set

    override var bluetoothConfigExt: BluetoothConfigExt? = null
        private set

    override var feedbackToneConfigExt: FeedbackToneConfigExt? = null
        private set

    override var batterySavingStatus: BatterySavingStatusExt? = null
        private set

    override var streamingStatusExt: StreamingStatusExt? = null
        private set

    override var sleepTimerExt: SleepTimerExt? = null
        private set

    override var groupParameterExt: GroupParameterExt? = null

    override var prodSettingStatusExt: ProdSettingStatusExt? = null

    override var lightInfoExt: LightInfoExt? = null
        private set

    override var getGroupDevicesFlagExt: GetGroupDevicesFlagExt? = null
        private set

    var rears: List<Rear>? = null
        private set

    fun registerDeviceListener(listener: IOneDeviceListener?) {
        listener ?: return

        if (!_wifiListeners.contains(listener)) {
            _wifiListeners.add(listener)
        }
        // for WiFi notify events
        LinkPlaySDKHelperProxy.registerEventListener(listener)
    }

    fun unregisterDeviceListener(listener: IOneDeviceListener?) {
        listener ?: return
        _wifiListeners.remove(listener)
        LinkPlaySDKHelperProxy.unregisterEventListener(listener)
    }

    fun clearDeviceListeners() {
        LinkPlaySDKHelperProxy.clearEventListeners()
        _wifiListeners.clear()
    }

    fun updateDeviceItem(device: DeviceItem): WiFiDevice = apply {
        deviceItem = device
    }

    internal fun onFeatureSupport(featSupport: FeatureSupport) {
        this.featSupportExt = FeatureSupportExt(featSupport = featSupport)
    }

    internal fun onSmartBtnConfig(config: SmartBtnConfig) {
        this.smartBtnConfigExt = GetSmartBtnConfigExt(config = config)
    }

    internal fun onBatteryStatus(status: BatteryStatusResponse) {
        Logger.d(TAG, "onBatteryStatus() >>> UUID[$UUID] status[$status]")

        deviceItem.devInfoExt = (deviceItem.devInfoExt ?: com.wifiaudio.model.DeviceInfoExt()).also { ext ->
            ext.batteryPercent = status.batteryLevel
        }

        // sync battery info in DeviceItem
        // LinkPlay charging flag define was opposite to EnumBatteryMode.
        deviceItem.devInfoExt?.setBatteryPercent(if (status.isACMode) 1 else 0, status.batteryLevel)
        this.batteryStatusExt = BatteryStatusExt(data = status)
    }

    internal fun onEQListResponse(eqListRsp: EQListResponse) {
        this.eqListExt = EQListExt(data = eqListRsp)
    }

    internal fun onEQResponse(eqRsp: EQResponse) {
        this.eqExt = EQExt(data = eqRsp)
    }

    internal fun onCalibration(calibration: Calibration) {
        this.calibrationExt = CalibrationExt(calibration)
    }

    internal fun onSoundscapeV2List(list: List<SoundscapeV2Item>) {
        this.soundscapeV2ListExt = SoundscapeV2ListExt(soundscapeV2List = list)
    }

    internal fun onSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest) {
        this.soundscapeV2ListExt?.soundscapeV2List?.update(req = req)
    }

    internal fun onControlSoundscapeV2(control: ControlSoundscapeV2) {
        this.controlSoundscapeV2Ext = (this.controlSoundscapeV2Ext
            ?: ControlSoundscapeV2Ext()).also { ext ->
            ext.soundscapeId = control.soundscapeId
            ext.actionId = control.actionId
            ext.refreshTimeStamp()
        }
    }

    internal fun onSoundscapeV2State(rsp: GetSoundscapeV2StateResponse) {
        this.controlSoundscapeV2Ext = (this.controlSoundscapeV2Ext
            ?: ControlSoundscapeV2Ext()).also { ext ->
            ext.soundscapeId = rsp.soundscapeId
            ext.state = rsp.state
            ext.refreshTimeStamp()
        }
    }

    internal fun onProdSettingStatus(rsp: ProdSettingResponse<String, String>) {
        this.prodSettingStatusExt = ProdSettingStatusExt(rsp)
    }

    @Synchronized
    private fun syncGeneWiFiSession(): OneWiFiSession? {
        val latestSession = _wifiSession
        val innerIP = deviceItem.IP

        // session IP must be the same if want to reuse it.
        if (null != latestSession && Tools.sameAddress(latestSession.ipAddress, innerIP)) {
            return latestSession
        }

        if (innerIP.isNullOrBlank()) {
            Logger.e(TAG, "wifiSession >>> illegal IP address [$innerIP]")
            return null
        }

        return OneWiFiSession(
            ipAddress = innerIP,
            device = this,
            listenerProxy = WiFiListenerProxy(
                getter = ::wifiListeners,
                register = ::registerDeviceListener,
                unregister = ::unregisterDeviceListener
            )
        ).also { newSession ->
            Logger.i(TAG, "gattSession >>> new WiFi session gen [${newSession.ipAddress}]")
            _wifiSession = newSession
        }
    }

    @Volatile
    private var lastMediaMeta: MediaMeta? = null

    /**
     * Notify volume inside bean once WiFi device item been updated
     */
    internal fun notifyParamsChanged(device: DeviceItem) {
        val ext = device.devInfoExt ?: return
        val newMeta = MediaMeta(
            isMute = EnumMuteStatus.isMute(ext.dlnaDesireMute),
            isPlay = ext.dlnaPlayStatus.isDLNAPlay(),
            volume = ext.dlnaCurrentVolume,
            songName = ext.albumInfo?.title,
            artistName = ext.albumInfo?.artist,
            albumCover = ext.albumInfo?.albumArtURI,
            mediaSource = ext.dlnaPlayMedium,
            trackSource = ext.dlnaTrackSource,
            duration = ext.dlnaTotalTime * 1000L, // secs to mills
            current = ext.dlnaTickTime * 1000L, // secs to mills
            dolbyAtoms = ext.supportDolbyAtoms(),
            dolbyAudio = ext.supportDolbyAudio(),
            isHD = ext.supportHD(),
            isUHD = ext.supportUHD(),
            isHiRes = ext.supportHiRes(),
            hiResSampleRate = ext.hiResSampleRate(),
            cdQuality = ext.cdQuality(),
            mp3Quality = ext.mp3Quality(),
            highQuality = device.highQuality(),
            dlnaPlayStatus = ext.dlnaPlayStatus,
            artistId = ext.albumInfo?.ArtistId,
            albumId = ext.albumInfo?.AlbumId
        )

        if (newMeta == lastMediaMeta) {
            return
        }

        Logger.d(TAG, "notifyParamsChanged() >>> UUID[${device.macAddressCRCWithCache()}] ${ext.metaDataInfo()}")
        wifiListeners.forEach { listener ->
            listener.onVolume(newMeta.volume)
            listener.onMute(newMeta.isMute)
            if (newMeta.isPlay != lastMediaMeta?.isPlay) listener.onPlayStatus(newMeta.isPlay)

            if (newMeta.albumCover != lastMediaMeta?.albumCover) {
                listener.onAlbumCover(newMeta.albumCover)
            } else if (newMeta.songName != lastMediaMeta?.songName) {
                listener.onSongName(newMeta.songName)
                // update uri align with song name cause this uri might not changed even img is different
                listener.onAlbumCover(newMeta.albumCover)
            }

            if (newMeta.artistName != lastMediaMeta?.artistName) listener.onArtistName(newMeta.artistName)
            if (newMeta.mediaSource != lastMediaMeta?.mediaSource) listener.onMediumSource(newMeta.mediaSource)
            if (newMeta.trackSource != lastMediaMeta?.trackSource) listener.onTrackSource(newMeta.trackSource)
            if (newMeta.duration != lastMediaMeta?.duration) listener.onDuration(newMeta.duration)
            listener.onCurrent(newMeta.current)

            if (!Tools.isHDMIorTVSource(mediumSource) && newMeta.dolbyAtoms != lastMediaMeta?.dolbyAtoms) {
                // dolby flag in Upnp is available only when medium source is not TV or HDMI
                listener.onDolbyAtoms(newMeta.dolbyAtoms)
            }

            if (!Tools.isHDMIorTVSource(mediumSource) && newMeta.dolbyAudio != lastMediaMeta?.dolbyAudio) {
                // dolby flag in Upnp is available only when medium source is not TV or HDMI
                listener.onDolbyAudio(newMeta.dolbyAudio)
            }

            if (newMeta.isHD != lastMediaMeta?.isHD) listener.onHD(newMeta.isHD)
            if (newMeta.isUHD != lastMediaMeta?.isUHD) listener.onUHD(newMeta.isUHD)
            if (newMeta.isHiRes != lastMediaMeta?.isHiRes) listener.onHiRes(newMeta.isHiRes)
            if (newMeta.hiResSampleRate != lastMediaMeta?.hiResSampleRate) listener.onHiResSampleRate(newMeta.hiResSampleRate)
            if (newMeta.cdQuality != lastMediaMeta?.cdQuality) listener.onCDQuality(newMeta.cdQuality)
            if (newMeta.mp3Quality != lastMediaMeta?.mp3Quality) listener.onMp3Quality(newMeta.mp3Quality)
            if (newMeta.highQuality != lastMediaMeta?.highQuality) listener.onHighQuality(newMeta.highQuality)
            if (newMeta.dlnaPlayStatus != lastMediaMeta?.dlnaPlayStatus) listener.onDlnaPlayStatus(status = newMeta.dlnaPlayStatus)
            if (newMeta.artistId != lastMediaMeta?.artistId) listener.onArtistId(newMeta.artistId)
            if (newMeta.albumId != lastMediaMeta?.albumId) listener.onAlbumId(newMeta.albumId)
        }

        lastMediaMeta = newMeta
    }

    internal fun onGetVoiceLanguage(info: GetVoiceLanguageResponse?) {
        info ?: return
        voiceLanguageResponseExt = VoiceLanguageResponseExt(info)
    }

    internal fun onGetVoiceRequestStartTone(info: GetVoiceToneResponse?) {
        info ?: return
        startVoiceToneResponseExt = VoiceToneResponseExt(info)
    }

    internal fun onGetVoiceRequestEndTone(info: GetVoiceToneResponse?) {
        info ?: return
        endVoiceToneResponseExt = VoiceToneResponseExt(info)
    }

    internal fun onDeviceInfo(info: DeviceInfo?) {
        info ?: return
        deviceInfoExt = DeviceInfoExt(info)
        Logger.d(TAG, "onDeviceInfo() >>> update fw version [${info.firmware}]")
        deviceItem.devStatus?.firmware = info.firmware
    }

    internal fun onC4APermissionStatue(rsp: C4aPermissionStatusResponse?) {
        Logger.d(TAG, "onC4APermissionStatue() >>> rsp:$rsp")
        rsp ?: run {
            this.c4aPermissionStatusExt = null
            return
        }
        this.c4aPermissionStatusExt = C4aPermissionStatusExt(status = rsp)
    }

    internal fun onAlexaCBLStatus(rsp: AlexaCBLStatusResponse?) {
        rsp ?: run {
            this.alexaCBLStatusExt = null
            return
        }

        this.alexaCBLStatusExt = AlexaCBLStatusExt(status = rsp)
    }

    internal fun onLwaStateResponse(rsp: LwaStateResponse?) {
        rsp ?: run {
            this.lwaStateResponseExt = null
            return
        }
        this.lwaStateResponseExt = LwaStateResponseExt(status = rsp)
    }

    internal fun onChromeCastOptIn(optIn: ChromeCastOpt?) {
        optIn ?: return
        this.chromecastOptExt = ChromeCastOptInExt(optIn = optIn)
    }

    internal fun onRearSpeakerVolume(rsp: RearSpeakerVolumeResponse?) {
        rsp ?: return
        this.rearSpeakerVolumeExt = RearSpeakerVolumeExt(rsp)
    }

    internal fun onDiagnosisStatus(rsp: DiagnosisStatusResponse?) {
        rsp ?: return
        this.diagnosisStatusExt = DiagnosisStatusExt(rsp)
    }

    internal fun onAutoPowerOff(secs: Int?) {
        secs ?: return
        this.autoPowerOffExt = AutoPowerOffExt(secs = secs)
    }

    internal fun onBluetoothConfig(config: EnumBluetoothConfig?) {
        config ?: return
        this.bluetoothConfigExt = BluetoothConfigExt(config = config)
    }

    internal fun onFeedbackToneConfig(config: EnumFeedbackToneConfig?) {
        config ?: return
        this.feedbackToneConfigExt = FeedbackToneConfigExt(config = config)
    }

    internal fun onGroupInfo(groupInfo: GetGroupInfo?) {
        groupInfo ?: run {
            this.groupInfoExt = null
            return
        }
        this.groupInfoExt = GroupInfoExt(groupInfo)
    }

    internal fun onBatterySavingStatus(status: EnumBatterySavingStatus?) {
        status ?: return
        this.batterySavingStatus = BatterySavingStatusExt(status = status)
    }

    internal fun onStreamingStatus(status: StreamingStatus?) {
        status ?: return
        this.streamingStatusExt = StreamingStatusExt(status)
    }

    internal fun onSleepTimer(secs: Int) {
        this.sleepTimerExt = SleepTimerExt(secs = secs)
    }

    internal fun onGroupParameter(status: GetGroupParameterRsp?) {
        status ?: run {
            this.groupParameterExt = null
            return
        }
        this.groupParameterExt = GroupParameterExt(status)
    }

    internal fun onGetRearSpeaker(rears: List<Rear>) {
        this.rears = rears
    }

    internal fun onLightInfo(lightInfo: LightInfo) {
        this.lightInfoExt = LightInfoExt(lightInfo)
    }

    /**
     * media_source: it can be HDMI1, HDMI2, HDMI3, HDMI4, BT, TV, MRM, AP2,AVS,AUX,C4A.
     * media_status: [EnumMediaSourceStatus]
     */
    internal fun onMediaSourceStatus(rsp: MediaResponse) {
        Logger.d(TAG, "onMediaSourceStatus() >>> $rsp")
        val mediumSource = rsp.mapToMediumType(lastMedium = mediumSource)

        rsp.mediaStatus?.let { mediaStatus ->
            _isPlaying = mediaStatus.isMediaSourcePlaying()

            wifiListeners.forEach { listener ->
                listener.onPlayStatus(isPlay = _isPlaying)
            }
        }

        if (null != mediumSource) {
            deviceItem.devInfoExt?.setDlnaPlayMediumByLocal(mediumSource)

            wifiListeners.forEach { listener ->
                listener.onMediumSource(source = mediumSource)
            }
        }
    }

    internal fun onGroupDevicesFlag(rsp: GetGroupDevicesFlagRsp) {
        Logger.d(TAG, "onGroupDevicesFlag() >>> $rsp")
        getGroupDevicesFlagExt = GetGroupDevicesFlagExt(rsp)
    }

    /**
     * Used for debounce duplicated [notifyParamsChanged]
     */
    private data class MediaMeta(
        val isMute: Boolean,
        val isPlay: Boolean,
        val volume: Int,
        val songName: String?,
        val artistName: String?,
        val albumCover: String?,
        val mediaSource: String?,
        val trackSource: String?,
        val duration: Long,
        val current: Long,
        val dolbyAtoms: Boolean,
        val dolbyAudio: Boolean,
        val isHD: Boolean,
        val isUHD: Boolean,
        val isHiRes: Boolean,
        val hiResSampleRate: String?, // sample rate of HI-RES
        val cdQuality: Boolean,
        val mp3Quality: Boolean,
        val highQuality: Boolean,
        val dlnaPlayStatus: String?,
        val artistId: String?,
        val albumId: String?
    ) {
        override fun equals(other: Any?): Boolean {
            if (other !is MediaMeta) {
                return false
            }

            return isMute == other.isMute &&
                    isPlay == other.isPlay &&
                    volume == other.volume &&
                    songName == other.songName &&
                    artistName == other.artistName &&
                    albumCover == other.albumCover &&
                    mediaSource == other.mediaSource &&
                    trackSource == other.trackSource &&
                    duration == other.duration &&
                    current == other.current &&
                    dolbyAtoms == other.dolbyAtoms &&
                    dolbyAudio == other.dolbyAudio &&
                    isHD == other.isHD &&
                    isUHD == other.isUHD &&
                    isHiRes == other.isHiRes &&
                    hiResSampleRate == other.hiResSampleRate &&
                    cdQuality == other.cdQuality &&
                    mp3Quality == other.mp3Quality &&
                    highQuality == other.highQuality &&
                    dlnaPlayStatus == other.dlnaPlayStatus &&
                    artistId == other.artistId &&
                    albumId == other.albumCover
        }

        override fun hashCode(): Int {
            return super.hashCode()
        }
    }

    companion object {
        private const val TAG = "WiFiDevice"
    }
}