package com.harman.discover.bean

import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.info.EnumProductLine

/**
 * @Description A product device that belongs to the [EnumProductLine.BAND_BOX] series
 * <AUTHOR>
 * @Time 2024/12/4
 */
class PartyBandDevice(
    leDevice: PartyBandBTDevice?,
    offlineDummy: PartyBandBTDevice?
) : V5BaseDevice<PartyBandBTDevice>(btDevice = leDevice, offlineDummy = offlineDummy) {
    override val bleDevice
        get() = _btDevice as? PartyBandBTDevice
    override val hadAuraCastPhysicalBtn: Boolean = false
    override val batteryLevel
        get() = bleDevice?.batteryInfo?.batteryLevel ?: 0
    override val isCharging
        get() = bleDevice?.batteryInfo?.isCharging ?: false

    enum class Model(val pid: String) {
        Trio("2111"),
        Solo("211f"),
    }

    val displayUuid
        get() = runCatching { UUID!!.takeLast(4) }.getOrNull() ?: ""

    fun isTrio() = pid == Model.Trio.pid
    fun isSolo() = pid == Model.Solo.pid

    val firmwareVersion get() = bleDevice?.firmwareVersion ?: offlineDummy?.firmwareVersion ?: "0.0.0.0.0"
    val miscInfo get() = bleDevice?.miscInfo
    val serialNumber: String?
        get() = bleDevice?.serialNumber ?: offlineDummy?.serialNumber
}