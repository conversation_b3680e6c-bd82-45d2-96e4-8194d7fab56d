package com.harman.discover

import android.content.Context
import androidx.annotation.AnyThread
import com.harman.discover.BLEScanner.isBluetoothPermissionGranted
import com.harman.discover.DeviceScanner.observerLock
import com.harman.discover.DeviceScanner.observers
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.util.Tools.sync
import com.harman.thread.DISPATCHER_LOW
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.model.DeviceItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.locks.ReentrantLock

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/2/6.
 */
object DeviceScanner {

    /**
     * The modification about [observers] must be in the scope of synchronized block of [observerLock]
     */
    private val observers = CopyOnWriteArrayList<IHmDeviceObserver>()
    private val observerLock = ReentrantLock()

    @AnyThread
    fun registerObserver(observer: IHmDeviceObserver) {
        observerLock.sync {
            observers.addIfAbsent(observer)

            BLEScanner.registerObserver(observer = innerBLEDeviceObserver)
            WiFiScanner.registerObserver(innerWiFiDeviceObserver)
            SPPScanner.registerObserver(observer = innerSPPDeviceObserver)
        }
    }

    @AnyThread
    fun startScan(
        context: Context,
        blePerformance: EnumScanPerformance = EnumScanPerformance.BALANCE
    ) {
        if (isBluetoothPermissionGranted(context)) {

            BLEScanner.startScan(context = context, performance = blePerformance)
            SPPScanner.startScan(context)
        }

        WiFiScanner.startScan(context = context)
    }

    @AnyThread
    fun unregisterObserver(observer: IHmDeviceObserver) {
        observerLock.sync {
            observers.remove(observer)

            if (observers.isEmpty()) {
                BLEScanner.unregisterObserver(observer = innerBLEDeviceObserver)
                WiFiScanner.unregisterObserver(innerWiFiDeviceObserver)
                SPPScanner.unregisterObserver(innerSPPDeviceObserver)
            }
        }
    }

    @AnyThread
    fun stopScan(context: Context, clearStore: Boolean) {
        BLEScanner.stopScan(context, clearStore = clearStore)
        WiFiScanner.stopScan(context = context, clearStore = clearStore)
        SPPScanner.stopScan()

        if (clearStore) {
            DeviceStore.clear()
        }
    }

    /**
     * Used for keeping [BLEScanner] scanning.
     */
    private val innerBLEDeviceObserver = object : IBLEDeviceObserver {
        override fun onDeviceOnline(device: BaseBTDevice<*, *>) {
            // no impl
        }

        override fun onDeviceOffline(device: BaseBTDevice<*, *>) {
            // no impl
        }

        override fun onDevicesUpdate(devices: List<BaseBTDevice<*, *>>) {
            // no impl
        }

        override fun onBluetoothServiceFailed(errorCode: Int) {
            notifyBluetoothServiceFailed(errorCode)
        }
    }

    /**
     * Used for keeping [WiFiScanner] scanning.
     */
    private val innerWiFiDeviceObserver = object : IWiFiDeviceObserver {
        override fun onDeviceOnline(deviceItem: DeviceItem) {
            // no impl
        }

        override fun onDeviceOffline(deviceItem: DeviceItem) {
            // no impl
        }

        override fun onDevicesUpdate(deviceItems: List<DeviceItem>) {
            // no impl
        }
    }

    /**
     * Used for keeping [SPPScanner] scanning.
     */
    private val innerSPPDeviceObserver = object : ISPPDeviceObserver {
        override fun onSPPDeviceConnected(bleDevices: List<BaseBTDevice<*, *>>) {
            val devices = bleDevices.mapNotNull { bleDevice ->
                val uuid = bleDevice.UUID
                if (uuid.isNullOrBlank()) {
                    return@mapNotNull null
                }

                DeviceStore.find(uuid = uuid)
            }

            notifyDeviceSPPConnect(devices)
        }

        override fun onSPPDisconnected(bleDevices: List<BaseBTDevice<*, *>>) {
            val devices = bleDevices.mapNotNull { bleDevice ->
                val uuid = bleDevice.UUID
                if (uuid.isNullOrBlank()) {
                    return@mapNotNull null
                }

                DeviceStore.find(uuid = uuid)
            }

            notifyDeviceSPPDisconnect(devices)
        }
    }

    @AnyThread
    internal fun notifyDeviceOnlineOrUpdate(device: Device) {
        observers.forEach { obr ->
            CoroutineScope(DISPATCHER_LOW).launch {
                device.pid?.also {
                    if (AppConfigurationUtils.hasPresetEqConfig(it)) {
                        AppConfigurationUtils.loadPresetEQConfig(it)
                    }
                    if (device is PartyBandDevice) {
                        AppConfigurationUtils.loadBandBoxMicModels(it)
                    }

                    AppConfigurationUtils.loadOtaConfig(it)
                    AppConfigurationUtils.loadDeviceControlZipFile(it)
                }
            }
            obr.onDeviceOnlineOrUpdate(device)
        }
    }

    @AnyThread
    internal fun notifyDeviceOffline(device: Device) {
        observers.forEach { obr ->
            obr.onDeviceOffline(device)
        }
    }

    @AnyThread
    internal fun notifyDevicesUpdate(devices: List<Device>) {
        observers.forEach { obr ->
            obr.onDevicesUpdate(devices)
        }
    }

    @AnyThread
    internal fun notifyBluetoothServiceFailed(errorCode: Int) {
        observers.forEach { obr ->
            obr.onBluetoothServiceFailed(errorCode)
        }
    }

    @AnyThread
    internal fun notifyDeviceFactoryReset(device: Device) {
        observers.forEach { obr ->
            obr.onDeviceFactoryReset(device)
        }
    }

    @AnyThread
    internal fun notifyDeviceSPPDisconnect(devices: List<Device>) {
        observers.forEach { obr ->
            obr.onDeviceSppDisconnect(disconnectedDevices = devices)
        }
    }

    @AnyThread
    internal fun notifyDeviceSPPConnect(devices: List<Device>) {
        observers.forEach { obr ->
            obr.onDeviceSppConnect(connectedDevices = devices)
        }
    }
}