package com.harman.discover.info

/**
 * Enum of device audio source
 */

enum class AudioSource(val value: Int, val sourceName: String) {
    NONE_AUDIO(0x0, "NONE_AUDIO"),
    BLUETOOTH(0x1, "BLUETOOTH"),
    AUX(0x2, "AUX"),
    USB(0x3, "USB"),
    OPTICAL(0x4, "OPTICAL"), // Bluetooth LE-Audio
    FM_AUDIO(0x5, "FM_AUDIO"),
    DAB_AUDIO(0x6, "DAB_AUDIO"),
    MOOD(0x8, "MOOD"),
    HDMI(0x9, "HDMI");

    companion object {
        fun getAudioSource(digitSource: Int?): AudioSource = entries.find { source ->
            source.value == digitSource
        } ?: NONE_AUDIO

        fun getAudioSource(digitSource: Byte?): AudioSource = entries.find { source ->
            source.value.toByte() == digitSource
        } ?: NONE_AUDIO

        fun getAudioSource(channel: String?): AudioSource = if (channel.isNullOrBlank()) {
            NONE_AUDIO
        } else {
            getAudioSource(channel.toIntOrNull())
        }

        fun AudioSource?.isPlayerSource(): Boolean = when (this) {
            BLUETOOTH,
            AUX,
            USB,
            OPTICAL,
            MOOD,
            HDMI -> true
            else -> false
        }

        fun AudioSource?.isRadioSource(): Boolean = when (this) {
            FM_AUDIO,
            DAB_AUDIO -> true
            else -> false
        }
    }

}