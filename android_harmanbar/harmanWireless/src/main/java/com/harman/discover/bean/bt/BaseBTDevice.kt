package com.harman.discover.bean.bt

import android.bluetooth.BluetoothDevice
import androidx.annotation.CallSuper
import androidx.annotation.IntRange
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.BaseSppSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.listener.IGattListener
import com.harman.connect.listener.IMediaListener
import com.harman.discover.BLEScanner
import com.harman.discover.IMediaParam
import com.harman.discover.SPPScanner
import com.harman.discover.bean.bt.BaseBTDevice.Companion.EXPIRED_GAP_MILLS_UNIT
import com.harman.discover.info.AuracastInfo
import com.harman.discover.info.AuracastSupport
import com.harman.discover.info.EnumDeviceProtocol
import com.harman.discover.info.EnumPlatform
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.productLine
import com.harman.discover.util.Tools.sameAddress
import com.harman.discover.util.Tools.supportBrEdr
import com.harman.discover.util.Tools.targetGattProtocol
import com.harman.log.Logger
import kotlinx.coroutines.cancel
import org.teleal.cling.support.playqueue.callback.xml.IPlayQueueType
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Created by gerrardzhang on 2024/1/5.
 *
 * Base data bean class for JBL One, Portable and Partybox.
 * Only store params for those which exists in all protocols.
 *
 * Based protocols' version:
 * JBL One v1.03
 * PartyBox v2.23
 * PartyLight v2.30
 * Portable v2.33
 *
 * @param Role The class type which define the role.
 * @param AuraCastRole The class type which define the Auracast role.
 */
sealed class BaseBTDevice<Role, AuraCastRole>(
    /** BLE ADV / SPP Related Params */
    @Volatile
    var bleAddress: String? = null,
    var macAddress: String? = null,
    var vendorID: String,
    var pid: String? = null,
    var colorID: String? = null,
    var deviceNameCRC: String? = null,
    var secondDeviceNameCRC: String? = null,
    var auraCastStatus: Int? = null,
    var groupIdentifier: String? = null,
    var deviceName: String? = null,
    var roleValue: Int? = null,
    var auraCastRoleValue: Int? = null,
    protected var auraCastSupport: Int? = null,
    var secureBleSupport: Int? = null,

    /** Non-BLE ADV Related */
    /** Indicate first and last time the device scanned by [BLEScanner.sysScanCallback] */
    val firstTouchTime: Long = System.currentTimeMillis(),
    var lastTouchTime: Long = System.currentTimeMillis(),
    /** chip platform */
    val platform: EnumPlatform?,
    //auracaseinfo
    var auracastInfo: AuracastInfo?=null,
) : IMediaParam {

    protected val _gattListeners = CopyOnWriteArrayList<IGattListener>()
    val gattListeners: List<IGattListener>
        get() = _gattListeners

    protected val mediaListeners: List<IMediaListener>
        get() = gattListeners.filterIsInstance<IMediaListener>()

    /**
     * Effected for both [BaseGattSession] and [BaseBrEdrSession]
     */
    internal fun registerGattListener(listener: IGattListener) {
        _gattListeners.addIfAbsent(listener)
    }

    /**
     * Effected for both [BaseGattSession] and [BaseBrEdrSession]
     */
    internal fun unregisterGattListener(listener: IGattListener) {
        _gattListeners.remove(listener)
    }

    /**
     * Effected for both [BaseGattSession] and [BaseBrEdrSession]
     */
    internal fun clearGattListeners() {
        _gattListeners.clear()
    }

    val isGattConnected: Boolean
        get() = true == gattSession?.isConnected

    val isBrEdrConnected: Boolean
        get() = true == brEdrSession?.isConnected

    /**
     * @see [SPPScanner.serviceListener]#onServiceConnected
     * Same meaning with [isA2DPConnected] but only got status from SPP session.
     * Better use [isA2DPConnected] to judge classic bluetooth connection status.
     */
    var isSPPConnected: Boolean = false

    /**
     * @see [EnumDeviceProtocol]
     * May combine several protocols.
     */
    var protocolFlags: Int = 0

    @IntRange(from = 0, to = 100)
    override var volume: Int = 0
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onVolume(volume = value)
            }
        }

    override var isPlaying: Boolean = false
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onPlayStatus(isPlay = value)
            }
        }

    override var songName: String? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onSongName(songName = value)
            }
        }

    override var artistName: String? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onArtistName(artistName = value)
            }
        }

    override var albumCover: String? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onAlbumCover(url = value)
            }
        }

    /**
     * @link [IStorageMediumType] [MenuBarConstants]
     */
    override var mediumSource: String? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onMediumSource(source = value)
            }
        }

    /**
     * @link [IPlayQueueType]
     */
    override var trackSource: String? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onTrackSource(source = value)
            }
        }

    override var durationMills: Long? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onDuration(duration = value)
            }
        }

    override var currentMills: Long? = null
        set(value) {
            field = value
            mediaListeners.forEach { listener ->
                listener.onCurrent(current = value)
            }
        }

    abstract val isMute: Boolean

    /**
     * For usage of cloning internal params between two instances
     * after BLE Adv scanned one device which already exists in the snapshots.
     */
    @CallSuper
    open fun clone(device: BaseBTDevice<*, *>) {
        this.bleAddress = device.bleAddress
        this.macAddress = device.macAddress
        this.vendorID = device.vendorID
        this.pid = device.pid
        this.colorID = device.colorID
        this.deviceNameCRC = device.deviceNameCRC
        this.secondDeviceNameCRC = device.secondDeviceNameCRC
        this.roleValue = device.roleValue
        this.auraCastRoleValue = device.auraCastRoleValue
        this.auraCastSupport = device.auraCastSupport
        this.auraCastStatus = device.auraCastStatus
        this.groupIdentifier = device.groupIdentifier
        this.lastTouchTime = device.lastTouchTime
        this.deviceName = device.deviceName

        updateMixProtocolData(device)
    }

    @CallSuper
    open fun updateAsBrEdr(brEdrDevice: BaseBTDevice<*, *>) {
        this.vendorID = brEdrDevice.vendorID
        this.pid = brEdrDevice.pid
        this.colorID = brEdrDevice.colorID
        this.macAddress = brEdrDevice.macAddress
        this.deviceName = brEdrDevice.deviceName

        updateMixProtocolData(brEdrDevice)
    }

    private fun updateMixProtocolData(device: BaseBTDevice<*, *>) {
        /*
         * BLE + BLE = override isSPPConnected
         * BLE + BR_EDR = or isSPPConnected
         * BR_EDR + BR_EDR = override isSPPConnected
         */
        if ((this.protocolFlags xor device.protocolFlags).supportBrEdr()) {
            this.isSPPConnected = this.isSPPConnected || device.isSPPConnected
        } else { // update
            this.isSPPConnected = device.isSPPConnected
        }

        this.protocolFlags = this.protocolFlags.or(device.protocolFlags)
    }

    @CallSuper
    open fun sameContent(device: BaseBTDevice<*, *>): Boolean {
        return sameAddress(this.bleAddress, device.bleAddress) &&
                sameAddress(this.macAddress, device.macAddress) &&
                this.vendorID == device.vendorID &&
                this.pid == device.pid &&
                this.colorID == device.colorID &&
                (this.deviceNameCRC == device.deviceNameCRC || this.deviceNameCRC == device.secondDeviceNameCRC) &&
                this.roleValue == device.roleValue &&
                this.auraCastRoleValue == device.auraCastRoleValue &&
                this.auraCastSupport == device.auraCastSupport &&
                this.auraCastStatus == device.auraCastStatus &&
                this.groupIdentifier == device.groupIdentifier &&
                this.lastTouchTime == device.lastTouchTime &&
                this.deviceName == device.deviceName
    }

    abstract val role: Role

    abstract val auraCastRole: AuraCastRole

    abstract val isAuraCastOn: Boolean

    abstract val isAuraCastDisabled: Boolean

    abstract val isBroadcaster: Boolean

    abstract val isReceiver: Boolean

    /**
     * Unique ID to represent this device.
     * Lower case for hex as default.
     */
    abstract val UUID: String?

    /**
     * Cause different platforms use different identifiers to represent MAC Address in BLE ADV
     * (e.g. full in PartyBox or CRC in Portable/JBL_One)
     * We define a spec function for different compare implementations.
     *
     * @param btDeviceMacAddress Returns the hardware address of [BluetoothDevice.getAddress].
     * For example, "00:11:22:AA:BB:CC".
     *
     * @return whether represent the same MAC address or not.
     */
    abstract fun matchMacAddress(btDeviceMacAddress: String?): Boolean

    /**
     * Let the child class select spec gatt session to build.
     */
    abstract fun buildGattSession(
        bleAddress: String?,
        listenerProxy: GattListenerProxy
    ): BaseGattSession<*, Role, AuraCastRole>

    /**
     * Let the child class select spec BR/EDR session to build.
     */
    abstract fun buildBrEdrSession(
        listenerProxy: GattListenerProxy
    ): BaseBrEdrSession<*, Role, AuraCastRole>

    /**
     * Must be the integer multiple of [EXPIRED_GAP_MILLS_UNIT]
     */
    open val expiredGapMills: Long = EXPIRED_GAP_MILLS_UNIT

    val isAuraCastSupport: Boolean
        get() = AuracastSupport.SUPPORT == AuracastSupport.getByValue(auraCastSupport)

    val isSecureBleSupport: Boolean
        get() = 1 == secureBleSupport

    /**
     * Whether the device is connected with the phone.
     */
    val isA2DPConnected: Boolean
        get() = isSPPConnected || Tools.isCRCMatch(deviceNameCRC) || Tools.isCRCMatch(secondDeviceNameCRC)

    open val isStandByMode: Boolean = false

    override fun hashCode(): Int {
        return UUID.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return other is BaseBTDevice<*, *> && this.UUID == other.UUID
    }

    /**
     * TODO: Not a quite good impl for BLE & SPP state maintenances on both BR_EDR & BLE support device.
     */
    fun removeBLEProtocolFlag() {
        protocolFlags = protocolFlags.and(EnumDeviceProtocol.PROTOCOL_BLE.value.inv())
    }

    /**
     * @return the exists [_gattSession] if it is not null and the [BaseGattSession.state] is in
     * [EnumConnectionStatus.CONNECTING] or [EnumConnectionStatus.CONNECTED] (which be sealed
     * as [BaseGattSession.isUsable]), or [_gattSession.bleAddress] equals current [bleAddress] to
     * avoid generate different sessions with same BLE Address.
     *
     * Otherwise generate a new one based on the latest BLE address from [bleAddress] cause BLE address
     * might be changed in interval.
     */
    private var _gattSession: BaseGattSession<*, Role, AuraCastRole>? = null
    val gattSession: BaseGattSession<*, Role, AuraCastRole>?
        get() = synchronized(this@BaseBTDevice) {
            val latestSession = _gattSession
            val innerAddress = bleAddress
            val targetProtocol = protocolFlags.targetGattProtocol()

            // session protocol must be the same if want to reuse it.
            if (null != latestSession) {
                if (latestSession.isConnecting || latestSession.isConnected) {
                    Logger.d(TAG, "gattSession >>> [$UUID] [${macAddress}] use latest session [${latestSession.bleAddress}] cause connecting/connected")
                    return latestSession
                }

                if (sameAddress(latestSession.bleAddress, innerAddress)) {
                    Logger.d(TAG, "gattSession >>> [$UUID] [${macAddress}] use latest session [${latestSession.bleAddress}] cause ble address didn't changed")
                    return latestSession
                }
            }

            if (innerAddress.isNullOrBlank()) {
                Logger.e(TAG, "gattSession >>> [$UUID] [${macAddress}] illegal ble address [$innerAddress]")
                return null
            }

            latestSession?.let { session ->
                Logger.i(TAG, "gattSession >>> [$UUID] [${macAddress}] disconnect and clear last Gatt session [${session.bleAddress}]")
                session.disconnect()
                session.cancel() // cancel CoroutineScope to stop delayed jobs
            }

            _gattSession = null

            Logger.i(TAG, "gattSession >>> [$UUID] [${macAddress}] try to gen Gatt session [$innerAddress] protocol[$targetProtocol]")
            return buildGattSession(
                innerAddress,
                GattListenerProxy(::_gattListeners, ::registerGattListener, ::unregisterGattListener)
            ).also { newSession ->
                Logger.i(TAG, "gattSession >>> [${System.identityHashCode(newSession)}] [$UUID] new Gatt session gen [${newSession.bleAddress}]")
                _gattSession = newSession
            }
        }

    val gattStatus: EnumConnectionStatus?
        get() = _gattSession?.state

    /**
     * @return the exists [_brEdrSession] if it is not null without caring [_brEdrSession.state]
     * cause [macAddress] wouldn't be changed.
     *
     * Or generate a new one based on the latest MAC address from [macAddress].
     */
    private var _brEdrSession: BaseBrEdrSession<*, Role, AuraCastRole>? = null
    val brEdrSession: BaseBrEdrSession<*, Role, AuraCastRole>?
        get() = synchronized(this@BaseBTDevice) {
            val latestSession = _brEdrSession
            if (null != latestSession) {
                Logger.d(TAG, "brEdrSession >>> use latest session [${latestSession.macAddress}]")
                return latestSession
            }

            val innerAddress = macAddress
            if (innerAddress.isNullOrBlank()) {
                return null
            }

            return buildBrEdrSession(
                GattListenerProxy(::_gattListeners, ::registerGattListener, ::unregisterGattListener)
            ).also { newSession ->
                Logger.i(TAG, "brEdrSession >>> [${System.identityHashCode(newSession)}] [${macAddress}] new BrEdr session gen")
                _brEdrSession = newSession
            }
        }

    val brEdrStatus: EnumConnectionStatus?
        get() = _brEdrSession?.state

    /**
     * @return the exists [_sppSession] if it is not null without caring [_sppSession.state]
     * cause [macAddress] wouldn't be changed.
     *
     * Or generate a new one based on the latest MAC address from [macAddress].
     */
    private var _sppSession: BaseSppSession? = null
    val sppSession: BaseSppSession?
        get() = synchronized(this@BaseBTDevice) {
            val latestSession = _sppSession

            if (null != latestSession) {
                Logger.d(TAG, "sppSession >>> use latest session [${latestSession.macAddress}]")
                return latestSession
            }

            val innerAddress = macAddress
            if (innerAddress.isNullOrBlank()) {
                Logger.e(TAG, "sppSession >>> illegal ble address [$innerAddress]")
                return null
            }

            return buildSppSession(macAddress = innerAddress, btDevice = this).also { newSession ->
                Logger.i(TAG, "sppSession >>> new SPP session gen [${newSession.macAddress}]")
                _sppSession = newSession
            }
        }

    /**
     * Allow sub class to impl spp build functions with spec. type of [DefaultSppBusinessSession]
     * @demo [PartyBoxBTDevice.buildSppSession]
     */
    open fun buildSppSession(
        macAddress: String,
        btDevice: BaseBTDevice<Role, AuraCastRole>
    ): BaseSppSession {
        return BaseSppSession(macAddress = macAddress, device = btDevice)
    }

    override fun toString(): String {
        val sb = StringBuilder()

        sb.append("pro[${productLine()}]")
        sb.append("vid[$vendorID]")
        sb.append("Pid[$pid]")
        sb.append("colorID[$colorID]")
        sb.append("UUID[$UUID]")
        sb.append("BLE[$bleAddress]")
        sb.append("MAC[$macAddress]")
        sb.append("protocolFlags[$protocolFlags]")
        sb.append("deviceName[${deviceName}]")
        sb.append("deviceNameCRC[${deviceNameCRC}]")
        sb.append("secondDeviceNameCRC[${secondDeviceNameCRC}]")
        sb.append("isSPPConnected[${isSPPConnected}]")
        sb.append("isA2DPConnected[${isA2DPConnected}]")
        sb.append("roleValue[${roleValue}]")
        sb.append("auraCastRoleValue[${auraCastRoleValue}]")
        sb.append("auraCastSupport[${auraCastSupport}]")
        sb.append("auraCastStatus[${auraCastStatus}]")
        sb.append("groupIdentifier[${groupIdentifier}]")
        sb.append("first.ts[${firstTouchTime}]")
        sb.append("last.ts[${lastTouchTime}]")
        sb.append("auracastInfo[${auracastInfo}]")

        return sb.toString()
    }

    companion object {
        private const val TAG = "BaseBLEDevice"

        const val EXPIRED_GAP_MILLS_UNIT = 30 * 1000L
    }
}
