package com.harman.discover.parser

import android.annotation.SuppressLint
import android.bluetooth.le.ScanRecord
import android.bluetooth.le.ScanResult
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.EnumDeviceProtocol
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.parser.OneBLEAdvParser.ableToParse
import com.harman.discover.parser.OneBLEAdvParser.parse
import com.harman.discover.util.Tools.extractBytes
import com.harman.log.Logger
import com.harmanbar.ble.inner.BLEScanner
import com.harmanbar.ble.statistic.StatisticConstant
import com.harmanbar.ble.utils.BLEUtil
import com.harmanbar.ble.utils.HexUtil

/**
 * Created by gerrardzhang on 2024/1/9.
 *
 * [ableToParse] refer to [BLEUtil.getManufacturerSpecificData]
 * [parse] refer to [BLEScanner.getScanCallback]#onScanResult
 *
 * Cause the manufacture ID about JBL One Platform(0x16) was different from the standard one(0xFF),
 * It was no able to get manufacture data from ScanRecord via [ScanRecord.getManufacturerSpecificData].
 */
object OneBLEAdvParser: IBLEAdvParser<OneRole, OneAuracastRole>() {

    override val vendorID: String = "FDDF"

    override fun ableToParse(scanResult: ScanResult?): Result {
        val bytes = scanResult?.scanRecord?.bytes

        if (null == bytes || bytes.isEmpty()) {
            return scanResult.toDisable()
        }

        var currentPos = 0
        while (currentPos < bytes.size) {
            val totalLen = bytes[currentPos++].toInt().and(0xFF)
            if (totalLen <= 0) {
                return scanResult.toDisable()
            }

            val payloadLen = totalLen - 1
            val fieldType = bytes[currentPos++].toInt().and(0xFF)

            if (StatisticConstant.DATA_TYPE_SERVICE_DATA_16_BIT == fieldType) {
                var payload = bytes.extractBytes(start = currentPos, length = payloadLen)
                if (null == payload || payload.isEmpty()) {
                    return scanResult.toDisable()
                }

                // Only parse BLE Adv which manu matched vendorID(FDDF)
                val vidBytes = payload.extractBytes( 0, 2)
                if (null == vidBytes || vidBytes.isEmpty()) {
                    return scanResult.toDisable()
                }

                val vid = HexUtil.byte2HexStr(vidBytes)
                if (!vendorID.equals(vid, true)) {
                    return scanResult.toDisable()
                }

                payload = payload.extractBytes( 2, payload.size - 2)

                if (!isSupport(payload)) {
                    Logger.e(TAG,"${HexUtil.byte2HexStr(payload)} is not config in remote serve")
                    return scanResult.toDisable()
                }

                return scanResult.toEnable(payload = payload)
            }

            currentPos += payloadLen
        }

        return scanResult.toDisable()
    }

    @SuppressLint("MissingPermission")
    override fun parse(result: Result): OneBTDevice? {
        val payload = result.payload
        val bluetoothDevice = result.scanResult?.device
        if (null == payload || payload.isEmpty() || null == bluetoothDevice) {
            return null
        }

        val builder = OneBTDevice.Builder(
            vendorID = vendorID,
            macAddressCRC = if (payload.size >= 8) {
                // 2 bytes and little endian
                HexUtil.byte2HexStr(HexUtil.revert(BLEUtil.extractBytes(payload, 6, 2))).lowercase()
            } else {
                null
            },
            bleAddress = bluetoothDevice.address
        )

        builder.deviceName = result.scanResult.scanRecord?.deviceName

        if (payload.size >= 1) { //1byte
            builder.version = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 0, 1)).toString(16)
        }

        if (payload.size >= 3) { //2byte
            builder.pid = BLEUtil.byteToInt(BLEUtil.extractBytes(payload, 1, 2)).toString(16)
        }

        if (payload.size >= 4) { //1byte
            builder.colorID = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 3, 1)).toString(16)
        }

        if (payload.size >= 6) { //2byte
            builder.deviceNameCRC = HexUtil.byte2HexStr(HexUtil.revert(BLEUtil.extractBytes(payload, 4, 2))).lowercase()
        }

        if (payload.size >= 9) { //1byte
            builder.status = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 8, 1))
        }

        if (payload.size >= 11) { //2byte
            builder.groupIdCRC = HexUtil.byte2HexStr(HexUtil.revert(BLEUtil.extractBytes(payload, 9, 2))).lowercase()
        }

        if (payload.size >= 12) { //1byte
            builder.role = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 11, 1))
        }

        if (payload.size >= 13) { //1byte
            builder.groupType = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 12, 1))
        }

        if (payload.size >= 14) { //1byte
            builder.networkConnected = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 13, 1))
        }

        if (payload.size >= 15) {
            // Bit 0 ~ 1，aura cast role （0：normal， 1：broadcast， 2：receiver）
            // Bit 2， is playing status （0：pause， 1：playing）
            // Bit 3,  is AuraCast disabled (0: normal, 1: AuraCast disabled)
            val value = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 14, 1))
            builder.auraCastRole = value.and(0b11)
            builder.auraCastPlayState = value.shr(2).and(0b1)
            builder.auraCastDisabled = value.shr(3).and(0b1)
        }

        if (payload.size >= 16) {
            val extra = BLEUtil.byteToInt2(BLEUtil.extractBytes(payload, 15, 1))
            // Deprecated: Bit 0 = Support Auracast (1 = Support Auracast, 0 = Does not support Auracast)
            // Bit 1 = Auracast Physical Button (1 = have button, 0 = no button), default value is 0.
            // Bit 2 = Support Auracast (1 = Support Auracast, 0 = Does not support Auracast), default value is 0
            builder.auraCastPhysicalBtn = extra.shr(1).and(0b1)
            builder.auraCastSupport = extra.shr(2).and(0b1)
            builder.secureBleSupport = extra.shr(4).and(0b1)
        }

        if (payload.size >= 19) {
            builder.secondDeviceNameCRC = HexUtil.byte2HexStr(HexUtil.revert(BLEUtil.extractBytes(payload, 17, 2))).lowercase()
        }

        val device = builder.build()


        device.protocolFlags = EnumDeviceProtocol.PROTOCOL_BLE.value

        Logger.d(TAG, "parse() >>> $device")
        Logger.d(TAG, "parse() >>> UUID[${device.UUID}] deviceName[${device.deviceName}] ${HexUtil.encodeHexStr(payload)}")
        return device
    }

    private const val TAG = "OneBLEAdvParser"
}