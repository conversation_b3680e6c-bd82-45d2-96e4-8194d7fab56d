package com.harman.discover.bean.bt

import com.harman.command.one.AttachOneCommand
import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DJPad
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.MediaResponse
import com.harman.command.one.bean.MetaDataNotify
import com.harman.command.one.bean.PreviewSoundscapeRequest
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.VolumeAndMuteResponse
import com.harman.command.one.bean.DebugMode
import com.harman.command.one.bean.DeviceInfo
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.EnumVoiceSource
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.LightInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SendAppControllerRequest
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SetVoiceLanguageRequest
import com.harman.command.one.bean.SetVoiceToneRequest
import com.harman.command.one.bean.SoundscapeV2Item
import com.harman.command.one.bean.StreamingStatus
import com.harman.command.one.command.EnumMuteStatus
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.DefaultBrEdrSession
import com.harman.connect.session.OneBusiness
import com.harman.connect.OneGattSession
import com.harman.discover.AlexaCBLStatusExt
import com.harman.discover.AutoPowerOffExt
import com.harman.discover.BatterySavingStatusExt
import com.harman.discover.BatteryStatusExt
import com.harman.discover.BluetoothConfigExt
import com.harman.discover.C4aPermissionStatusExt
import com.harman.discover.CalibrationExt
import com.harman.discover.ChromeCastOptInExt
import com.harman.discover.ControlSoundscapeV2Ext
import com.harman.discover.DeviceInfoExt
import com.harman.discover.DiagnosisStatusExt
import com.harman.discover.EQExt
import com.harman.discover.EQListExt
import com.harman.discover.FeatureSupportExt
import com.harman.discover.FeedbackToneConfigExt
import com.harman.discover.GetGroupDevicesFlagExt
import com.harman.discover.GetSmartBtnConfigExt
import com.harman.discover.GroupInfoExt
import com.harman.discover.GroupParameterExt
import com.harman.discover.LightInfoExt
import com.harman.discover.LwaStateResponseExt
import com.harman.discover.OneBusinessParams
import com.harman.discover.ProdSettingStatusExt
import com.harman.discover.RearSpeakerVolumeExt
import com.harman.discover.SleepTimerExt
import com.harman.discover.SoundscapeV2ListExt
import com.harman.discover.StreamingStatusExt
import com.harman.discover.VoiceLanguageResponseExt
import com.harman.discover.VoiceToneResponseExt
import com.harman.discover.bean.bt.BaseBTDevice.Companion.EXPIRED_GAP_MILLS_UNIT
import com.harman.discover.info.EnumOneAuthentication
import com.harman.discover.info.EnumPlatform
import com.harman.discover.info.GroupType
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.albumCoverUrl
import com.harman.discover.util.Tools.u32ToPercentage
import com.harman.discover.util.Tools.isMediaSourcePlaying
import com.harman.discover.util.Tools.update
import com.harman.log.Logger
import com.harmanbar.ble.entity.HBBluetoothDevice

/**
 * Created by gerrardzhang on 2024/1/5.
 *
 * Bean class for JBL One. Replace [HBBluetoothDevice].
 */
class OneBTDevice private constructor(
    bleAddress: String?, macAddress: String?,
    vendor: String, pid: String?, colorID: String?,
    role: Int?, auraCastRole: Int?, deviceNameCRC: String?, auraCastSupport: Int?,
    groupIdentifier: String?, deviceName: String?,
    secondDeviceNameCRC: String?,
    secureBleSupport: Int?,
    var version: String?,
    var macAddressCRC: String?,
    var status: Int?,
    var networkConnected: Int?,
    private var _groupType: Int?,
    private var _auraCastPlayState: Int?,
    private var _auraCastDisabled: Int?,
    private var _auraCastPhysicalBtn: Int?,
) : BaseBTDevice<OneRole, OneAuracastRole>(
    bleAddress = bleAddress,
    macAddress = macAddress,
    vendorID = vendor,
    pid = pid,
    colorID = colorID,
    roleValue = role,
    auraCastRoleValue = auraCastRole,
    deviceNameCRC = deviceNameCRC,
    secondDeviceNameCRC = secondDeviceNameCRC,
    auraCastSupport = auraCastSupport,
    auraCastStatus = null, // There's no spec flag describing AuraCast status. They use AuraCast Role instead.
    secureBleSupport = secureBleSupport,
    groupIdentifier = groupIdentifier,
    deviceName = deviceName,
    platform = EnumPlatform.MTK // All JBL One devices were MTK platform.
), OneBusiness, OneBusinessParams {

    var firstOnlineTime: Long? = null

    var lastOnlineTime: Long? = null

    var skipVerify: Boolean = false

    @Deprecated("This flag is no longer used in the oobe flow")
    val authentication: EnumOneAuthentication?
        get() {
            val authFlag = status?.and(0b1) ?: return null
            return EnumOneAuthentication.fromValue(authFlag)
        }

    val bleConnectable: Boolean
        get() {
            val connectable = status?.let { it shr 5 } ?: 1
            return connectable and (0b1) == 0
        }

    /**
     * bit 4: Standby mode, 1: YES, 0: NO.
     */
    val isStandby: Boolean
        get() {
            val standbyFlag = status?.let { it shr 4 } ?: return false
            return standbyFlag.and(0b1) == 1
        }

    override val regionCode: String?
        get() = deviceInfoExt?.deviceInfo?.countryCode

    override var groupInfoExt: GroupInfoExt? = null

    override var featSupportExt: FeatureSupportExt? = null
        private set

    override var smartBtnConfigExt: GetSmartBtnConfigExt? = null
        private set

    override var eqListExt: EQListExt? = null
        private set

    override var eqExt: EQExt? = null
        private set

    override var batteryStatusExt: BatteryStatusExt? = null
        private set

    override var soundscapeV2ListExt: SoundscapeV2ListExt? = null
        private set

    override var controlSoundscapeV2Ext: ControlSoundscapeV2Ext? = null
        private set

    override var voiceLanguageResponseExt: VoiceLanguageResponseExt? = null
        private set

    override var startVoiceToneResponseExt: VoiceToneResponseExt? = null
        private set

    override var endVoiceToneResponseExt: VoiceToneResponseExt? = null
        private set

    override var deviceInfoExt: DeviceInfoExt? = null
        private set

    override var c4aPermissionStatusExt: C4aPermissionStatusExt? = null
        private set

    override var alexaCBLStatusExt: AlexaCBLStatusExt? = null
        private set

    override var lwaStateResponseExt: LwaStateResponseExt? = null
        private set

    override var chromecastOptExt: ChromeCastOptInExt? = null
        private set

    override var calibrationExt: CalibrationExt? = null
        private set

    override var rearSpeakerVolumeExt: RearSpeakerVolumeExt? = null
        private set

    override var diagnosisStatusExt: DiagnosisStatusExt? = null
        private set

    override var autoPowerOffExt: AutoPowerOffExt? = null
        private set

    override var bluetoothConfigExt: BluetoothConfigExt? = null
        private set

    override var feedbackToneConfigExt: FeedbackToneConfigExt? = null
        private set

    override var batterySavingStatus: BatterySavingStatusExt? = null
        private set

    override var streamingStatusExt: StreamingStatusExt? = null
        private set

    override var sleepTimerExt: SleepTimerExt? = null
        private set

    override var groupParameterExt: GroupParameterExt? = null

    override val prodSettingStatusExt: ProdSettingStatusExt? = null

    override var lightInfoExt: LightInfoExt? = null
        private set

    override var getGroupDevicesFlagExt: GetGroupDevicesFlagExt? = null
        private set

    private var muteStatus: EnumMuteStatus? = null

    var playbackDuration: Int? = null

    override val role: OneRole
        get() = OneRole.getRole(super.roleValue)

    override val auraCastRole: OneAuracastRole
        get() = OneAuracastRole.getRole(super.auraCastRoleValue)

    override val isAuraCastOn: Boolean
        get() = isAuraCastSupport && when (auraCastRole) {
            OneAuracastRole.BROADCAST,
            OneAuracastRole.RECEIVER -> true

            OneAuracastRole.NORMAL -> false
        }

    override val isAuraCastDisabled: Boolean
        get() = 1 == _auraCastDisabled

    /**
     * Use MAC Address CRC as UUID for aligning with [DeviceItem]
     */
    override val UUID: String?
        get() = macAddressCRC?.lowercase()

    override val isBroadcaster: Boolean
        get() = OneAuracastRole.BROADCAST == auraCastRole

    override val isReceiver: Boolean
        get() = OneAuracastRole.RECEIVER == auraCastRole

    override fun clone(device: BaseBTDevice<*, *>) {
        super.clone(device)
        if (device !is OneBTDevice) {
            return
        }

        this.version = device.version
        this.macAddressCRC = device.macAddressCRC
        this.status = device.status
        this.networkConnected = device.networkConnected
        this._groupType = device._groupType
        this._auraCastPlayState = device._auraCastPlayState
        this._auraCastDisabled = device._auraCastDisabled
        this._auraCastPhysicalBtn = device._auraCastPhysicalBtn
        this.secondDeviceNameCRC = device.secondDeviceNameCRC
    }

    override fun sameContent(device: BaseBTDevice<*, *>): Boolean {
        return device is OneBTDevice &&
                super.sameContent(device) &&
                this.version == device.version &&
                Tools.equalsCRC(this.macAddressCRC, device.macAddressCRC) &&
                this.status == device.status &&
                this.networkConnected == device.networkConnected &&
                this._groupType == device._groupType &&
                this._auraCastPlayState == device._auraCastPlayState &&
                this._auraCastDisabled == device._auraCastDisabled &&
                this._auraCastPhysicalBtn == device._auraCastPhysicalBtn &&
                this.secondDeviceNameCRC == device.secondDeviceNameCRC
    }

    /**
     * Must be the integer multiple of [EXPIRED_GAP_MILLS_UNIT]
     */
    override val expiredGapMills: Long = EXPIRED_GAP_MILLS_UNIT // DEFAULT_CHECK_INTERVAL

    val isNetworkConnected: Boolean
        get() = 1 == networkConnected

    val isAuraCastPlaying: Boolean
        get() = 1 == _auraCastPlayState

    val hadAuraCastPhysicalBtn: Boolean
        get() = 1 == _auraCastPhysicalBtn

    val groupType: GroupType
        get() = GroupType.getGroupType(_groupType)

    override val isMute: Boolean
        get() = EnumMuteStatus.MUTE == muteStatus

    override fun matchMacAddress(btDeviceMacAddress: String?): Boolean {
        return Tools.equalsMacAddressCRC(
            macAddress = btDeviceMacAddress,
            macAddressCRC = macAddressCRC
        ) || Tools.sameAddress(first = btDeviceMacAddress, second = super.macAddress)
    }

    override fun buildGattSession(bleAddress: String?, listenerProxy: GattListenerProxy): OneGattSession {
        return OneGattSession(
            bleAddress = bleAddress,
            device = this@OneBTDevice,
            gattListenerProxy = GattListenerProxy(
                ::_gattListeners,
                ::registerGattListener,
                ::unregisterGattListener
            )
        )
    }

    override fun buildBrEdrSession(listenerProxy: GattListenerProxy): BaseBrEdrSession<*, OneRole, OneAuracastRole> {
        return DefaultBrEdrSession(
            device = this@OneBTDevice,
            gattListenerProxy = GattListenerProxy(
                ::_gattListeners,
                ::registerGattListener,
                ::unregisterGattListener
            )
        )
    }

    data class Builder(
        val vendorID: String,
        var bleAddress: String? = null,
        val macAddressCRC: String?, // as UUID
        var macAddress: String? = null,
        var version: String? = null,
        var pid: String? = null,
        var colorID: String? = null,
        var role: Int? = null,
        var auraCastRole: Int? = null,
        var isPlaying: Int? = null,
        var deviceNameCRC: String? = null,
        var auraCastSupport: Int? = null,
        var secureBleSupport: Int? = null,
        var groupIdCRC: String? = null,
        var status: Int? = null,
        var networkConnected: Int? = null,
        var groupType: Int? = null,
        var auraCastPlayState: Int? = null,
        var auraCastDisabled: Int? = null,
        var auraCastPhysicalBtn: Int? = null,
        var deviceName: String? = null,
        var secondDeviceNameCRC: String? = null
    ) {
        fun build(): OneBTDevice =
            OneBTDevice(
                bleAddress = <EMAIL>,
                macAddress = <EMAIL>,
                vendor = <EMAIL>,
                version = <EMAIL>,
                pid = <EMAIL>,
                colorID = <EMAIL>,
                role = <EMAIL>,
                auraCastRole = <EMAIL>,
                deviceNameCRC = <EMAIL>,
                auraCastSupport = <EMAIL>,
                secureBleSupport = <EMAIL>,
                groupIdentifier = <EMAIL>,
                macAddressCRC = <EMAIL>,
                status = <EMAIL>,
                networkConnected = <EMAIL>,
                _groupType = <EMAIL>,
                _auraCastPlayState = <EMAIL>,
                _auraCastDisabled = <EMAIL>,
                _auraCastPhysicalBtn = <EMAIL>,
                deviceName = <EMAIL>,
                secondDeviceNameCRC = <EMAIL>
            )
    }

    private val oneGattSession: OneBusiness?
        get() = gattSession as? OneBusiness

    override fun sendAttachCommand(command: AttachOneCommand, port: String?) {
        oneGattSession?.sendAttachCommand(port = port, command = command)
    }

    override fun getDeviceInfo(port: String?) {
        oneGattSession?.getDeviceInfo(port = port)
    }

    override fun sendSetDeviceName(name: String, port: String?) {
        oneGattSession?.sendSetDeviceName(port = port, name = name)
    }

    override fun alexaCBLStatus(port: String?) {
        oneGattSession?.alexaCBLStatus(port = port)
    }

    override fun alexaCBLLogout(port: String?) {
        oneGattSession?.alexaCBLLogout(port = port)
    }

    override fun getLWAState(port: String?) {
        oneGattSession?.getLWAState(port = port)
    }

    override fun requestLWALogout(port: String?) {
        // This command only support WiFi session.
        oneGattSession?.requestLWALogout(port = port)
    }

    override fun setLWAAuthCode(lwaInfo: LWAInfo, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.setLWAAuthCode(port = port, lwaInfo = lwaInfo)
    }

    override fun getVoiceRequestStartTone(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.getVoiceRequestStartTone(port = port, source = source)
    }

    override fun getVoiceRequestEndTone(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.getVoiceRequestEndTone(port = port, source = source)
    }

    override fun getVoiceLanguage(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.getVoiceLanguage(port = port, source = source)
    }

    override fun getSupportedVoiceLanguage(source: EnumVoiceSource, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.getSupportedVoiceLanguage(port = port, source = source)
    }

    override fun requestGoogleLogout(port: String?) {
        // This command only support WiFi session.
        oneGattSession?.requestGoogleLogout(port = port)
    }

    override fun setVoiceRequestStartTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.setVoiceRequestStartTone(port = port, setVoiceToneRequest = setVoiceToneRequest)
    }

    override fun setVoiceRequestEndTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.setVoiceRequestEndTone(port = port, setVoiceToneRequest = setVoiceToneRequest)
    }

    override fun setVoiceLanguage(setVoiceLanguageRequest: SetVoiceLanguageRequest, port: String?) {
        // This command only support WiFi session.
        oneGattSession?.setVoiceLanguage(port = port, setVoiceLanguageRequest = setVoiceLanguageRequest)
    }

    override fun getAPList(port: String?) {
        oneGattSession?.getAPList(port = port)
    }

    override fun setCastGroup(payloadJson: String, port: String?) {
        oneGattSession?.setCastGroup(port = port, payloadJson = payloadJson)
    }

    override fun playMusic() {
        oneGattSession?.playMusic()
    }

    override fun pauseMusic() {
        oneGattSession?.pauseMusic()
    }

    override fun prevMusic() {
        oneGattSession?.prevMusic()
    }

    override fun nextMusic() {
        oneGattSession?.nextMusic()
    }

    override fun setRemoteVolume(value: Int) {
        oneGattSession?.setRemoteVolume(value = value)
    }

    override fun getRemoteVolume() {
        oneGattSession?.getRemoteVolume()
    }

    override fun getFeatureSupport(port: String?) {
        oneGattSession?.getFeatureSupport(port = port)
    }

    override fun getEQList(port: String?) {
        oneGattSession?.getEQList(port = port)
    }

    override fun setActiveEQ(item: SetActiveEQItemRequest, port: String?) {
        oneGattSession?.setActiveEQ(port = port, item = item)
    }

    override fun getEQ(port: String?) {
        oneGattSession?.getEQ(port = port)
    }

    override fun setEQ(eq: EQSetting, port: String?) {
        oneGattSession?.setEQ(port = port, eq = eq)
    }

    override fun startAuth(timeoutSecs: Int, port: String?) {
        oneGattSession?.startAuth(port = port, timeoutSecs = timeoutSecs)
    }

    override fun cancelAuth(port: String?) {
        oneGattSession?.cancelAuth(port = port)
    }

    override fun authPair(timeoutSecs: Int, port: String?) {
        oneGattSession?.authPair(port = port, timeoutSecs = timeoutSecs)
    }

    override fun setWifiNetwork(req: SetWifiNetworkRequest, port: String?) {
        oneGattSession?.setWifiNetwork(port = port, req = req)
    }

    override fun getOtaStatus(port: String?) {
        oneGattSession?.getOtaStatus(port = port)
    }

    override fun requestDeviceOta(port: String?) {
        oneGattSession?.requestDeviceOta(port = port)
    }

    override fun getOtaAccessPoint(port: String?) {
        oneGattSession?.getOtaAccessPoint(port = port)
    }

    override fun getBatteryStatus(port: String?) {
        oneGattSession?.getBatteryStatus(port = port)
    }

    override fun setCalibration(port: String?) {
        oneGattSession?.setCalibration(port = port)
    }

    override fun getCalibrationState(port: String?) {
        oneGattSession?.getCalibrationState(port = port)
    }

    override fun cancelCalibration(port: String?) {
        oneGattSession?.cancelCalibration(port = port)
    }

    override fun getC4aPermissionStatus(port: String?) {
        oneGattSession?.getC4aPermissionStatus(port = port)
    }

    override fun setC4aPermissionStatus(status: EnumC4aPermissionStatus, port: String?) {
        oneGattSession?.setC4aPermissionStatus(port = port, status = status)
    }

    override fun getChromeCastOptIn(port: String?) {
        oneGattSession?.getChromeCastOptIn(port = port)
    }

    override fun setChromeCastOptIn(optIn: ChromeCastOpt, port: String?) {
        oneGattSession?.setChromeCastOptIn(port = port, optIn = optIn)
    }

    override fun setCountryCode(code: String, port: String?) {
        oneGattSession?.setCountryCode(port = port, code = code)
    }

    override fun enterAuraCast(port: String?) {
        oneGattSession?.enterAuraCast(port = port)
    }

    override fun exitAuraCast(port: String?) {
        oneGattSession?.exitAuraCast(port = port)
    }

    override fun getAuraCastSqMode(port: String?) {
        oneGattSession?.getAuraCastSqMode(port = port)
    }

    override fun setAuraCastSqMode(on: Boolean, port: String?) {
        oneGattSession?.setAuraCastSqMode(port = port, on = on)
    }

    override fun setPlayPartySound(partySound: Int, port: String?) {
        oneGattSession?.setPlayPartySound(port = port, partySound = partySound)
    }

    override fun setDjEvent(value: String, port: String?) {
        oneGattSession?.setDjEvent(port = port, value = value)
    }

    override fun setDebugMode(debugMode: DebugMode, port: String?) {
        oneGattSession?.setDebugMode(port = port, debugMode = debugMode)
    }

    override fun getSmartBtnConfig(port: String?) {
        oneGattSession?.getSmartBtnConfig(port = port)
    }

    override fun setSmartBtnConfig(config: SmartBtnConfig, port: String?) {
        oneGattSession?.setSmartBtnConfig(port = port, config = config)
    }

    override fun previewSoundscape(request: PreviewSoundscapeRequest, port: String?) {
        oneGattSession?.previewSoundscape(port = port, request = request)
    }

    override fun cancelSoundscape(port: String?) {
        oneGattSession?.cancelSoundscape(port = port)
    }

    override fun getSoundscapeV2Config(port: String?) {
        oneGattSession?.getSoundscapeV2Config(port = port)
    }

    override fun setSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest, port: String?) {
        oneGattSession?.setSoundscapeV2Config(port = port, req = req)
    }

    override fun controlSoundscapeV2(req: ControlSoundscapeV2, port: String?) {
        oneGattSession?.controlSoundscapeV2(port = port, req = req)
    }

    override fun getSoundscapeV2State(port: String?) {
        oneGattSession?.getSoundscapeV2State(port = port)
    }

    override fun getProductUsage(port: String?) {
        oneGattSession?.getDeviceInfo(port = port)
    }

    override fun getDeviceName(port: String?) {
        oneGattSession?.getDeviceName(port = port)
    }

    override fun playDemoSound(port: String?) {
        // This command only support WiFi session.
    }

    override fun cancelDemoSound(port: String?) {
        // This command only support WiFi session.
    }

    override fun getRearSpeakerVolume(port: String?) {
        oneGattSession?.getRearSpeakerVolume(port = port)
    }

    override fun sendAppController(req: SendAppControllerRequest, port: String?) {
        oneGattSession?.sendAppController(port = port, req = req)
    }

    override fun setFactoryRestore(port: String?) {
        oneGattSession?.setFactoryRestore(port = port)
    }

    override fun setAutoPowerOffTimer(secs: Int, port: String?) {
        oneGattSession?.setAutoPowerOffTimer(port = port, secs = secs)
    }

    override fun getAutoPowerOffTimer(port: String?) {
        oneGattSession?.getAutoPowerOffTimer(port = port)
    }

    override fun setBluetoothConfig(config: EnumBluetoothConfig, port: String?) {
        oneGattSession?.setBluetoothConfig(port = port, config = config)
    }

    override fun getBluetoothConfig(port: String?) {
        oneGattSession?.getBluetoothConfig(port = port)
    }

    override fun setFeedbackToneConfig(config: EnumFeedbackToneConfig, port: String?) {
        oneGattSession?.setFeedbackToneConfig(port = port, config = config)
    }

    override fun getFeedbackToneConfig(port: String?) {
        oneGattSession?.getFeedbackToneConfig(port = port)
    }

    override fun getGeneralConfig(type: GeneralConfigType, port: String?) {
        oneGattSession?.getGeneralConfig(port = port, type = type)
    }

    override fun setGeneralConfig(req: GeneralConfig, port: String?) {
        oneGattSession?.setGeneralConfig(port = port, req = req)
    }

    override fun setBatterySavingStatus(status: EnumBatterySavingStatus, port: String?) {
        oneGattSession?.setBatterySavingStatus(port = port, status = status)
    }

    override fun getBatterySavingStatus(port: String?) {
        oneGattSession?.getBatterySavingStatus(port = port)
    }

    override fun setIRLearn(port: String?) {
        oneGattSession?.setIRLearn(port = port)
    }

    override fun getDJPad(port: String?) {
        oneGattSession?.getDJPad(port = port)
    }

    override fun setDJPad(djPad: DJPad, port: String?) {
        oneGattSession?.setDJPad(port = port, djPad = djPad)
    }

    override fun triggerCastLED(port: String?) {
        oneGattSession?.triggerCastLED(port = port)
    }

    override fun clearHistoryOneOSVersion(port: String?) {
        oneGattSession?.clearHistoryOneOSVersion(port = port)
    }

    override fun setAnchorIndicate(timeoutSecs: Int, port: String?) {
        oneGattSession?.setAnchorIndicate(port = port, timeoutSecs = timeoutSecs)
    }

    override fun setAnchorCancel(port: String?) {
        oneGattSession?.setAnchorCancel(port = port)
    }

    override fun destroyCastGroup(port: String?) {
        oneGattSession?.destroyCastGroup(port = port)
    }

    override fun getGroupInfo(port: String?) {
        oneGattSession?.getGroupInfo(port = port)
    }

    override fun groupCalibration(anchor: String, step: Int, port: String?) {
        oneGattSession?.groupCalibration(port = port, anchor = anchor, step = step)
    }

    override fun switchStereoChannel(port: String?) {
        oneGattSession?.switchStereoChannel(port = port)
    }

    override fun skipDemoSound(port: String?) {
        oneGattSession?.skipDemoSound(port = port)
    }

    override fun getGroupParameter(port: String?) {
        oneGattSession?.getGroupParameter(port = port)
    }

    override fun getGroupCalibrationState(port: String?) {
        oneGattSession?.getGroupCalibrationState(port = port)
    }

    override fun renameGroup(name: String, port: String?) {
        oneGattSession?.renameGroup(port = port, name = name)
    }

    override fun getStreamingStatus(port: String?) {
        oneGattSession?.getStreamingStatus(port = port)
    }

    override fun setSleepTimer(secs: Int, port: String?) {
        oneGattSession?.setSleepTimer(port = port, secs = secs)
    }

    override fun getSleepTimer(port: String?) {
        oneGattSession?.getSleepTimer(port = port)
    }

    override fun getPersonalListeningMode(port: String?) {
        oneGattSession?.getPersonalListeningMode(port = port)
    }

    override fun setPersonalListeningMode(isOn: Boolean, port: String?) {
        oneGattSession?.setPersonalListeningMode(port = port, isOn = isOn)
    }

    override fun getMediaSourceStatus(port: String?) {
        oneGattSession?.getMediaSourceStatus(port = port)
    }

    override fun getColorPicker(port: String?) {
        oneGattSession?.getColorPicker(port = port)
    }

    override fun setColorPicker(picker: ColorPicker, port: String?) {
        oneGattSession?.setColorPicker(port = port, picker = picker)
    }

    override fun getOOBEEncryptionKey(port: String?) {
        oneGattSession?.getOOBEEncryptionKey(port = port)
    }

    override fun setTimeZone(formatDate: String, port: String?) {
        // no impl
    }

    override fun setSmartMode(isOn: Boolean, port: String?) {
        oneGattSession?.setSmartMode(port = port, isOn = isOn)
    }

    override fun getSmartMode(port: String?) {
        oneGattSession?.getSmartMode(port = port)
    }

    override fun getRearSpeakerStatus(port: String?) {
        oneGattSession?.getRearSpeakerStatus(port = port)
    }

    override fun setLightInfo(req: SetLightInfoRequest, port: String?) {
        oneGattSession?.setLightInfo(req, port = port)
    }

    override fun getLightInfo(port: String?) {
        oneGattSession?.getLightInfo(port = port)
    }

    override fun resetLightPatternColor(id: String, port: String?) {
        oneGattSession?.resetLightPatternColor(id, port = port)
    }

    override fun getGroupDevicesOtaStatus(port: String?) {
        oneGattSession?.getGroupDevicesOtaStatus(port = port)
    }

    override fun getGroupDevicesFlag(port: String?) {
        oneGattSession?.getGroupDevicesFlag(port = port)
    }

    internal fun onGattVolumeAndMute(rsp: VolumeAndMuteResponse) {
        rsp.volumeLevel?.u32ToPercentage()?.let { volume ->
            super.volume = volume
        }

        rsp.muteStatus?.let { status ->
            muteStatus = EnumMuteStatus.fromValue(status)

            mediaListeners.forEach { listener ->
                listener.onMute(isMute)
            }
        }
    }

    /**
     * media_source: it can be HDMI1, HDMI2, HDMI3, HDMI4, BT, TV, MRM, AP2,AVS,AUX,C4A.
     * media_status: [EnumMediaSourceStatus]
     */
    internal fun onMediaSourceStatus(rsp: MediaResponse) {
        Logger.d(TAG, "onMediaSourceStatus() >>> $rsp")
        rsp.mediaStatus?.let { mediaStatus ->
            super.isPlaying = mediaStatus.isMediaSourcePlaying()

            mediaListeners.forEach { listener ->
                listener.onPlayStatus(isPlay = super.isPlaying)
            }
        }

        super.mediumSource = rsp.mapToMediumType(lastMedium = super.mediumSource)
        mediaListeners.forEach { listener ->
            listener.onMediumSource(super.mediumSource)
        }
    }

    internal fun onFeatureSupport(featSupport: FeatureSupport) {
        this.featSupportExt = FeatureSupportExt(featSupport = featSupport)
    }

    internal fun onSmartBtnConfig(config: SmartBtnConfig) {
        this.smartBtnConfigExt = GetSmartBtnConfigExt(config = config)
    }

    internal fun onEQListResponse(eqListRsp: EQListResponse) {
        this.eqListExt = EQListExt(data = eqListRsp)
    }

    internal fun onEQResponse(eqRsp: EQResponse) {
        this.eqExt = EQExt(eqRsp)
    }

    internal fun onBatteryStatus(status: BatteryStatusResponse) {
        this.batteryStatusExt = BatteryStatusExt(data = status)
    }

    internal fun onMetaNotify(metaNotify: MetaDataNotify) {
        super.songName = metaNotify.title
        super.artistName = metaNotify.artist
        super.albumCover = metaNotify.albumCoverUrl()
        // No source info available for super.mediumSource
        super.trackSource = metaNotify.providerApp
        super.durationMills = metaNotify.duration
        super.currentMills = metaNotify.position
    }

    internal fun onSoundscapeV2List(list: List<SoundscapeV2Item>) {
        this.soundscapeV2ListExt = SoundscapeV2ListExt(soundscapeV2List = list)
    }

    internal fun onSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest) {
        this.soundscapeV2ListExt?.soundscapeV2List?.update(req = req)
    }

    internal fun onControlSoundscapeV2(control: ControlSoundscapeV2) {
        this.controlSoundscapeV2Ext = (this.controlSoundscapeV2Ext ?: ControlSoundscapeV2Ext()).also { ext ->
            ext.soundscapeId = control.soundscapeId
            ext.actionId = control.actionId
            ext.refreshTimeStamp()
        }
    }

    internal fun onSoundscapeV2State(rsp: GetSoundscapeV2StateResponse) {
        this.controlSoundscapeV2Ext = (this.controlSoundscapeV2Ext ?: ControlSoundscapeV2Ext()).also { ext ->
            ext.soundscapeId = rsp.soundscapeId
            ext.state = rsp.state
            ext.refreshTimeStamp()
        }
    }

    internal fun onDeviceInfo(info: DeviceInfo?) {
        info ?: return
        this.deviceInfoExt = DeviceInfoExt(info)
    }

    internal fun onC4APermissionStatus(rsp: C4aPermissionStatusResponse?) {
        rsp ?: return
        this.c4aPermissionStatusExt = C4aPermissionStatusExt(status = rsp)
    }

    internal fun onAlexaCBLStatus(rsp: AlexaCBLStatusResponse?) {
        rsp ?: return
        this.alexaCBLStatusExt = AlexaCBLStatusExt(status = rsp)
    }

    internal fun onLwaStateResponse(rsp: LwaStateResponse?) {
        rsp ?: return
        this.lwaStateResponseExt = LwaStateResponseExt(status = rsp)
    }

    internal fun onChromeCastOptIn(optIn: ChromeCastOpt?) {
        optIn ?: return
        this.chromecastOptExt = ChromeCastOptInExt(optIn = optIn)
    }

    internal fun onDeviceName(name: String?) {
        deviceName = name
    }

    internal fun onCalibration(calibration: Calibration) {
        this.calibrationExt = CalibrationExt(calibration)
    }

    internal fun onRearSpeakerVolume(rsp: RearSpeakerVolumeResponse?) {
        rsp ?: return
        this.rearSpeakerVolumeExt = RearSpeakerVolumeExt(rsp)
    }

    internal fun onAutoPowerOff(secs: Int?) {
        secs ?: return
        this.autoPowerOffExt = AutoPowerOffExt(secs = secs)
    }

    internal fun onBluetoothConfig(config: EnumBluetoothConfig?) {
        config ?: return
        this.bluetoothConfigExt = BluetoothConfigExt(config = config)
    }

    internal fun onGroupInfo(groupInfo: GetGroupInfo?) {
        groupInfo ?: run {
            this.groupInfoExt = null
            return
        }
        this.groupInfoExt = GroupInfoExt(groupInfo)
    }

    internal fun onFeedbackToneConfig(config: EnumFeedbackToneConfig?) {
        config ?: return
        this.feedbackToneConfigExt = FeedbackToneConfigExt(config = config)
    }

    internal fun onBatterySavingStatus(status: EnumBatterySavingStatus?) {
        status ?: return
        this.batterySavingStatus = BatterySavingStatusExt(status = status)
    }

    internal fun onStreamingStatus(status: StreamingStatus?) {
        status ?: return
        this.streamingStatusExt = StreamingStatusExt(status)
    }

    internal fun onSleepTimer(secs: Int) {
        this.sleepTimerExt = SleepTimerExt(secs = secs)
    }

    internal fun onGroupParameter(status: GetGroupParameterRsp?) {
        status ?: run {
            this.groupParameterExt = null
            return
        }
        val newStatus = this.groupParameterExt?.groupParameter?.copyWith(status) ?: status
        this.groupParameterExt = GroupParameterExt(newStatus)
    }

    internal fun onLightInfo(lightInfo: LightInfo) {
        this.lightInfoExt = LightInfoExt(lightInfo)
    }

    internal fun onGroupDevicesFlag(rsp: GetGroupDevicesFlagRsp) {
        Logger.d(TAG, "onGroupDevicesFlag() >>> $rsp")
        getGroupDevicesFlagExt = GetGroupDevicesFlagExt(rsp)
    }

    override fun toString(): String {
        val sb = StringBuilder(super.toString())

        sb.append("version[${version}]")
        sb.append("macAddressCRC[${macAddressCRC}]")
        sb.append("status[${status}]")
        sb.append("networkConnected[${networkConnected}]")
        sb.append("secondDeviceNameCRC[${secondDeviceNameCRC}]")
        sb.append("secureBleSupport[$secureBleSupport]")
        sb.append("_groupType[${_groupType}]")
        sb.append("_auraCastPlayState[${_auraCastPlayState}]")
        sb.append("_auraCastDisabled[${_auraCastDisabled}]")
        sb.append("_auraCastPhysicalBtn[${_auraCastPhysicalBtn}]")

        return sb.toString()
    }

    companion object {
        private const val TAG = "OneBTDevice"
    }
}