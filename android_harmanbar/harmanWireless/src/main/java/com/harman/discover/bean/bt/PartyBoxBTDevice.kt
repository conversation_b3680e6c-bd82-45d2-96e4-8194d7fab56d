package com.harman.discover.bean.bt

import android.bluetooth.BluetoothDevice
import androidx.annotation.IntRange
import com.harman.command.common.GeneralGattCommand
import com.harman.command.partybox.bean.AutoStandbyInfo
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.command.partybox.gatt.PartyBoxGattCommandProcessor
import com.harman.command.partybox.gatt.ReqDeviceFeatureInfoCommand
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.command.partybox.gatt.auth.EnumAuthAction
import com.harman.command.partybox.gatt.battery.BatteryStatusFeature
import com.harman.command.partybox.gatt.battery.EnumBatteryStatusFeature
import com.harman.command.partybox.gatt.eq.EQSettings
import com.harman.command.partybox.gatt.eq.RawEQSettings
import com.harman.command.partybox.gatt.identify.EnumIdentifyDevice
import com.harman.command.partybox.gatt.light.Color
import com.harman.command.partybox.gatt.light.EnumLightPattern
import com.harman.command.partybox.gatt.light.StudioLightInfo
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.command.partybox.gatt.sleep.SleepModeInfo
import com.harman.command.partybox.gatt.timer.AutoOffTimerRsp
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseSppSession
import com.harman.connect.PartyBoxBrEdrSession
import com.harman.connect.PartyBoxGattSession
import com.harman.connect.PartyBoxSppBusiness
import com.harman.connect.PartyBoxSppSession
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.session.PartyBoxBusinessSession
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.BaseBTDevice.Companion.EXPIRED_GAP_MILLS_UNIT
import com.harman.discover.info.AmbientLightInfo
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.AudioSource
import com.harman.discover.info.AuraCastStatus
import com.harman.discover.info.BLEStandbySupport
import com.harman.discover.info.BTConnectionInfo
import com.harman.discover.info.Connectable
import com.harman.discover.info.EnumPlatform
import com.harman.discover.info.EnumSyncOnOff
import com.harman.discover.info.GattOverBrEdrMode
import com.harman.discover.info.GeneralRole
import com.harman.discover.info.HotelMode
import com.harman.discover.info.MicConnectionInfo
import com.harman.discover.info.MuteStatus
import com.harman.discover.info.PartyBoxBatteryCharging
import com.harman.discover.info.PartyBoxSecondaryInfo
import com.harman.discover.info.PartyConnectStatus
import com.harman.discover.info.PlayerStatus
import com.harman.discover.info.ScreenDisplayInfo
import com.harman.discover.info.SpotifyQuickAccess
import com.harman.discover.info.SupportHotelMode
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.formatBatteryLv
import com.harman.discover.util.Tools.isPartyBox120
import com.harman.discover.util.Tools.isPartyBox320
import com.harman.discover.util.Tools.printList
import com.harman.discover.util.Tools.targetGattProtocol
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil

/**
 * Created by gerrardzhang on 2024/1/5.
 *
 * Bean class for PartyBox. Replace [BaseDevice] & [PartyboxDevice] in Partybox App.
 */
class PartyBoxBTDevice private constructor(
    bleAddress: String?, macAddress: String?,
    vendorID: String, pid: String?, colorID: String?,
    role: Int?, auraCastRole: Int?, deviceNameCRC: String?, auraCastSupport: Int?,
    auraCastStatus: Int?, groupId: String?, deviceName: String?, platform: EnumPlatform?,
    secondDeviceNameCRC: String? = null,
    var charging: Int? = null,
    var batteryLv: Int? = null,
    var acWithoutBattery: Boolean? = null,
    var rawPartyConnectStatus: Int? = null,
    var mute: Int? = null,
    private var _connectable: Int? = null,
    private var _btConnectionInfo: Int? = null,
    private var _bleStandbySupport: Int? = null,
    private var _micConnectionInfo: Int? = null,
    private var _spotifyQuickAccess: Int? = null,
    private var _supportGattEdr: Int? = null,
    private var _hotelMode: Int? = null,
    private var _supportHotelMode: Int? = null,
    private var _isAuraCastDisabled: Int? = null
) : BaseBTDevice<GeneralRole, GeneralRole>(
    bleAddress = bleAddress,
    macAddress = macAddress,
    vendorID = vendorID,
    pid = pid,
    colorID = colorID,
    roleValue = role,
    auraCastRoleValue = auraCastRole,
    deviceNameCRC = deviceNameCRC,
    secondDeviceNameCRC = secondDeviceNameCRC,
    auraCastSupport = auraCastSupport,
    auraCastStatus = auraCastStatus,
    groupIdentifier = groupId,
    deviceName = deviceName,
    platform = platform
), PartyBoxBusinessSession, PartyBoxSppBusiness {

    val deviceListeners: List<IPartyBoxDeviceListener>
        get() = gattListeners.filterIsInstance<IPartyBoxDeviceListener>()

    override val role: GeneralRole
        get() = GeneralRole.getByValue(super.roleValue)

    override val auraCastRole: GeneralRole
        get() = GeneralRole.getByValue(super.auraCastRoleValue)

    override val isAuraCastOn: Boolean
        get() = isAuraCastSupport &&
                AuraCastStatus.ON == AuraCastStatus.getByValue(auraCastStatus)

    override val isAuraCastDisabled: Boolean
        get() = 1 == _isAuraCastDisabled

    /**
     * Use MAC Address as UUID
     */
    override val UUID: String?
        get() = macAddress?.lowercase()

    override val isBroadcaster: Boolean
        get() = GeneralRole.PRIMARY == auraCastRole

    override val isReceiver: Boolean
        get() = GeneralRole.SECONDARY == auraCastRole

    override val isStandByMode: Boolean
        get() = GeneralRole.STANDBY == auraCastRole

    override fun matchMacAddress(btDeviceMacAddress: String?): Boolean {
        return Tools.sameAddress(first = btDeviceMacAddress, second = super.macAddress)
    }

    override fun buildGattSession(
        bleAddress: String?,
        listenerProxy: GattListenerProxy
    ): PartyBoxGattSession {
        return PartyBoxGattSession(
            bleAddress = bleAddress,
            device = this@PartyBoxBTDevice,
            gattListenerProxy = GattListenerProxy(
                ::gattListeners,
                ::registerGattListener,
                ::unregisterGattListener
            ),
            writeIntervalMs = if (true == pid?.isPartyBox120() || true == pid?.isPartyBox320()) 80 else 0,
        )
    }

    override fun buildBrEdrSession(
        listenerProxy: GattListenerProxy
    ): BaseBrEdrSession<*, GeneralRole, GeneralRole> {
        return PartyBoxBrEdrSession(
            device = this@PartyBoxBTDevice,
            gattListenerProxy = GattListenerProxy(
                ::gattListeners,
                ::registerGattListener,
                ::unregisterGattListener
            )
        )
    }

    override fun clone(device: BaseBTDevice<*, *>) {
        super.clone(device)
        if (device !is PartyBoxBTDevice) {
            return
        }

        this.macAddress = device.macAddress
        this.secondDeviceNameCRC = device.secondDeviceNameCRC
        this._connectable = device._connectable
        this.mute = device.mute
        this.charging = device.charging
        this.batteryLv = device.batteryLv
        this.acWithoutBattery = device.acWithoutBattery
        this.rawPartyConnectStatus = device.rawPartyConnectStatus
        this._btConnectionInfo = device._btConnectionInfo
        this._bleStandbySupport = device._bleStandbySupport
        this._micConnectionInfo = device._micConnectionInfo
        this._spotifyQuickAccess = device._spotifyQuickAccess
        this._supportHotelMode = device._supportHotelMode
        this._hotelMode = device._hotelMode
        this._supportGattEdr = device._supportGattEdr
        this._isAuraCastDisabled = device._isAuraCastDisabled
    }

    override fun sameContent(device: BaseBTDevice<*, *>): Boolean {
        return device is PartyBoxBTDevice &&
                super.sameContent(device) &&
                Tools.sameAddress(this.macAddress, device.macAddress) &&
                Tools.equalsCRC(this.secondDeviceNameCRC, device.secondDeviceNameCRC) &&
                this._connectable == device._connectable &&
                this.mute == device.mute &&
                this.charging == device.charging &&
                this.batteryLv == device.batteryLv &&
                this.acWithoutBattery == device.acWithoutBattery &&
                this.rawPartyConnectStatus == device.rawPartyConnectStatus &&
                this._btConnectionInfo == device._btConnectionInfo &&
                this._bleStandbySupport == device._bleStandbySupport &&
                this._micConnectionInfo == device._micConnectionInfo &&
                this._spotifyQuickAccess == device._spotifyQuickAccess &&
                this._supportHotelMode == device._supportHotelMode &&
                this._hotelMode == device._hotelMode &&
                this._supportGattEdr == device._supportGattEdr &&
                this._isAuraCastDisabled == device._isAuraCastDisabled
    }

    /**
     * Must be the integer multiple of [EXPIRED_GAP_MILLS_UNIT]
     */
    override val expiredGapMills: Long = EXPIRED_GAP_MILLS_UNIT // change to 30 secs for SS5/AS5/Onyx9

    /**
     * PartyBox platform own spec SPP session with business interface [PartyBoxSppBusiness]
     */
    override fun buildSppSession(
        macAddress: String,
        btDevice: BaseBTDevice<GeneralRole, GeneralRole>
    ): BaseSppSession {
        return PartyBoxSppSession(macAddress, btDevice)
    }

    val bleConnectable: Boolean
        get() = Connectable.CONNECTABLE == Connectable.getByValue(_connectable)

    val partyConnectStatus: PartyConnectStatus?
        get() = PartyConnectStatus.valueOf(rawPartyConnectStatus)

    val micConnectionInfo: MicConnectionInfo
        get() = MicConnectionInfo.getByValue(_micConnectionInfo)

    val isSpotifyTriggered: Boolean
        get() = SpotifyQuickAccess.ON == SpotifyQuickAccess.getByValue(_spotifyQuickAccess)

    override val isMute: Boolean
        get() = MuteStatus.MUTE_ON == MuteStatus.getByValue(mute)

    val isCharging: Boolean
        get() = PartyBoxBatteryCharging.CHARGING == PartyBoxBatteryCharging.getByValue(charging)

    @get:IntRange(from = 0L, to = 100L)
    val batteryLevel: Int
        get() = batteryLv.formatBatteryLv()

    val isAcWithoutBattery: Boolean
        get() = acWithoutBattery ?: false

    val volumeWithMute: Int
        get() = if (isMute) 0 else volume

    val isBLEStandbySupport: Boolean
        get() = BLEStandbySupport.SUPPORT == BLEStandbySupport.getByValue(_bleStandbySupport)

    val btConnectInfo: BTConnectionInfo
        get() = BTConnectionInfo.getByValue(_btConnectionInfo)

    val isHotelMode: Boolean
        get() = HotelMode.HOTEL_MODE.value == _hotelMode

    val supportGattOverBrEdr: Boolean
        get() = GattOverBrEdrMode.SUPPORT_GATT_OVER_EDR.value == _supportGattEdr

    val supportHotelMode: Boolean
        get() = SupportHotelMode.SUPPORT.value == _supportHotelMode

    var stereoGroupName: String? = null

    var audioSource: AudioSource = AudioSource.NONE_AUDIO

    var syncOnOff: EnumSyncOnOff = EnumSyncOnOff.OFF

    var secondaryInfo: PartyBoxSecondaryInfo? = null

    var audioChannel: AudioChannel = AudioChannel.NONE_CHANNEL

    var serialNumber: String? = null

    /**
     * format as "AA.BB.CC"
     */
    var firmwareVersion: String? = null

    var playbackDuration: Int? = null

    /**
     * EQ setting which device currently applied.
     */
    var rawEQSettings: RawEQSettings? = null

    /**
     * Device supported features.
     * @link [ReqDeviceFeatureInfoCommand] [PartyBoxGattCommandProcessor.parseDeviceFeatureInfo]
     */
    var deviceFeature: PartyBoxDevFeat? = null

    /**
     * Collect light related info and states.
     * @link [GattPacketFormat.SET_LIGHT_INFO]
     */
    val lightInfo = PartyBoxLightInfo()

    /**
     * @link [GetFeedbackToneCommand]
     */
    var isFeedbackToneOn: Boolean = false

    /**
     * @link [reqBatteryStatus]
     */
    val batteryStatusFeature = BatteryStatusFeature()

    val studioLightInfo = StudioLightInfo()

    /**
     * @link [com.harman.command.partybox.gatt.ReqScreenDisplayCommand]
     */
    var screenDisplayInfo: ScreenDisplayInfo? = null

    var alarmInfo: AlarmInfo? = null

    var sleepModeInfo: SleepModeInfo? = null

    var ambientLightInfo: AmbientLightInfo? = null

    var radioInfo: RadioInfo? = null

    var analyticsData: Map<String, Int>? = null

    var autoOffTimerRsp: AutoOffTimerRsp? = null

    var autoStandbyInfo: AutoStandbyInfo? = null

    data class Builder(
        val vendorID: String,
        var bleAddress: String? = null,
        val macAddress: String?, // as UUID
        var pid: String? = null,
        var colorID: String? = null,
        var role: Int? = null,
        var auraCastRole: Int? = null,
        var deviceNameCRC: String? = null,
        var auraCastSupport: Int? = null,
        var groupId: String? = null,
        var connectable: Int? = null,
        var mute: Int? = null,
        var charging: Int? = null,
        var batteryLv: Int? = null,
        var acWithoutBattery: Boolean? = null,
        //var volume: Int? = null,
        var partyConnectStatus: Int? = null,
        var btConnectionInfo: Int? = null,
        var bleStandbySupport: Int? = null,
        var secondDeviceNameCRC: String? = null,
        var micConnectionInfo: Int? = null,
        var auraCastStatus: Int? = null,
        var spotifyQuickAccess: Int? = null,
        var supportGattEdr: Int? = null,
        var hotelMode: Int? = null,
        var supportHotelMode: Int? = null,
        var isAuraCastDisabled: Int? = null,
        var deviceName: String? = null,
        var platform: EnumPlatform? = null
    ) {
        fun build(): PartyBoxBTDevice = PartyBoxBTDevice(
            bleAddress = <EMAIL>,
            macAddress = <EMAIL>,
            vendorID = <EMAIL>,
            pid = <EMAIL>,
            colorID = <EMAIL>,
            role = <EMAIL>,
            auraCastRole = <EMAIL>,
            deviceNameCRC = <EMAIL>,
            auraCastSupport = <EMAIL>,
            groupId = <EMAIL>,
            _connectable = <EMAIL>,
            mute = <EMAIL>,
            charging = <EMAIL>,
            batteryLv = <EMAIL>,
            acWithoutBattery = <EMAIL>,
            rawPartyConnectStatus = <EMAIL>,
            _btConnectionInfo = <EMAIL>,
            _bleStandbySupport = <EMAIL>,
            secondDeviceNameCRC = <EMAIL>,
            _micConnectionInfo = <EMAIL>,
            auraCastStatus = <EMAIL>,
            _spotifyQuickAccess = <EMAIL>,
            _supportGattEdr = <EMAIL>,
            _hotelMode = <EMAIL>,
            _supportHotelMode = <EMAIL>,
            _isAuraCastDisabled = <EMAIL>,
            deviceName = <EMAIL>,
            platform = <EMAIL>
        )
    }

    private val partyBoxGattSession: PartyBoxBusinessSession?
        get() = gattSession as? PartyBoxBusinessSession

    private val partyBoxBrEdrSession: PartyBoxBrEdrSession?
        get() = brEdrSession as? PartyBoxBrEdrSession

    private val partyBoxSppSession: PartyBoxSppSession?
        get() = sppSession as? PartyBoxSppSession

    private fun Int.toSession(): PartyBoxBusinessSession? = when (this) {
        BluetoothDevice.TRANSPORT_BREDR -> partyBoxBrEdrSession
        BluetoothDevice.TRANSPORT_LE -> partyBoxGattSession
        else -> null
    }

    override fun reqGeneralGattCommand(protocol: Int, command: GeneralGattCommand) {
        Logger.d(TAG, "reqGeneralGattCommand() >>> [$UUID] protocol[$protocol] command[$command]")
        protocol.toSession()?.reqGeneralGattCommand(protocol = protocol, command = command)
    }

    override fun reqDeviceInfo(protocol: Int) {
        Logger.d(TAG, "reqDeviceInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqDeviceInfo(protocol = protocol)
    }

    override fun setDeviceInfo(protocol: Int, contentBytes: ByteArray) {
        Logger.d(
            TAG,
            "setDeviceInfo() >>> [$UUID] protocol[$protocol] contentBytes[${
                HexUtil.encodeHexStr(contentBytes)
            }]"
        )
        protocol.toSession()?.setDeviceInfo(protocol = protocol, contentBytes = contentBytes)
    }

    override fun playMusic(protocol: Int) {
        Logger.d(TAG, "playMusic() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.playMusic(protocol = protocol)
        super.isPlaying = true
        deviceListeners.forEach { listener ->
            listener.onPlayerInfoUpdate()
        }
    }

    override fun pauseMusic(protocol: Int) {
        Logger.d(TAG, "pauseMusic() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.pauseMusic(protocol = protocol)
        super.isPlaying = false
        deviceListeners.forEach { listener ->
            listener.onPlayerInfoUpdate()
        }
    }

    override fun prevMusic(protocol: Int) {
        Logger.d(TAG, "prevMusic() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.prevMusic(protocol = protocol)
    }

    override fun nextMusic(protocol: Int) {
        Logger.d(TAG, "nextMusic() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.nextMusic(protocol = protocol)
    }

    override fun setRemoteVolume(protocol: Int, value: Int) {
        Logger.d(TAG, "setRemoteVolume() >>> [$UUID] protocol[$protocol] value[$value]")
        protocol.toSession()?.setRemoteVolume(protocol = protocol, value = value)
    }

    override fun getRemoteVolume(protocol: Int) {
        Logger.d(TAG, "getRemoteVolume() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.getRemoteVolume(protocol = protocol)
    }

    override fun setPlayerStatus(protocol: Int, status: PlayerStatus) {
        Logger.d(TAG, "setPlayerStatus() >>> [$UUID] protocol[$protocol] status[$status]")
        protocol.toSession()?.setPlayerStatus(protocol = protocol, status = status)
    }

    override fun reqPlayerInfo(protocol: Int) {
        Logger.d(TAG, "reqPlayerInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqPlayerInfo(protocol = protocol)
    }

    override fun reqAdvancedEQ(protocol: Int) {
        Logger.d(TAG, "reqAdvancedEQ() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqAdvancedEQ(protocol = protocol)
    }

    override fun setAdvancedEQLE(protocol: Int, eqSettings: EQSettings, isNeedAlgorithm: Boolean?) {
        Logger.d(TAG, "setAdvancedEQLE() >>> [$UUID] protocol[$protocol] eqSettings[$eqSettings]")
        protocol.toSession()?.setAdvancedEQLE(
            protocol = protocol,
            eqSettings = eqSettings,
            isNeedAlgorithm = isNeedAlgorithm
        )
    }

    override fun setAdvancedEQBE(protocol: Int, eqSettings: EQSettings, isNeedAlgorithm: Boolean?) {
        Logger.d(TAG, "setAdvancedEQBE() >>> [$UUID] protocol[$protocol] eqSettings[$eqSettings]")
        protocol.toSession()?.setAdvancedEQBE(
            protocol = protocol,
            eqSettings = eqSettings,
            isNeedAlgorithm = isNeedAlgorithm
        )
    }

    override fun gen3GroupStereo(
        protocol: Int,
        audioChannel: AudioChannel,
        connectStatus: PartyConnectStatus,
        groupID: String,
        groupName: String,
        secondaryDevice: PartyBoxDevice?
    ) {
        Logger.d(
            TAG, "gen3GroupStereo() >>> [$UUID] protocol[$protocol] audioChannel[$audioChannel]" +
                    " connectStatus[$connectStatus] groupID[$groupID] groupName[$groupName] secondaryDevice[${secondaryDevice?.UUID}]"
        )
        protocol.toSession()?.gen3GroupStereo(
            protocol = protocol,
            audioChannel = audioChannel,
            connectStatus = connectStatus,
            groupID = groupID,
            groupName = groupName,
            secondaryDevice = secondaryDevice
        )
    }

    override fun identifyDevice(
        protocol: Int,
        mainIdentify: EnumIdentifyDevice?,
        coIdentify: EnumIdentifyDevice?
    ) {
        Logger.d(
            TAG,
            "identifyDevice() >>> [$UUID] protocol[$protocol] mainIdentify[$mainIdentify] coIdentify[$coIdentify]"
        )
        protocol.toSession()?.identifyDevice(
            protocol = protocol,
            mainIdentify = mainIdentify,
            coIdentify = coIdentify
        )
    }

    override fun gen3RenameStereo(protocol: Int, groupName: String) {
        Logger.d(TAG, "gen3RenameStereo() >>> [$UUID] protocol[$protocol] groupName[$groupName]")
        protocol.toSession()?.gen3RenameStereo(protocol = protocol, groupName = groupName)
    }

    override fun setPrimaryChannel(
        protocol: Int,
        channel: AudioChannel,
        secondaryDevice: PartyBoxDevice?
    ) {
        Logger.d(
            TAG,
            "setPrimaryChannel() >>> [$UUID] protocol[$protocol] channel[$channel] secondaryDevice[${secondaryDevice?.UUID}]"
        )
        protocol.toSession()?.setPrimaryChannel(
            protocol = protocol,
            channel = channel,
            secondaryDevice = secondaryDevice
        )
    }

    override fun unGroup(protocol: Int, secondaryDevice: PartyBoxDevice?) {
        Logger.d(
            TAG,
            "unGroup() >>> [$UUID] protocol[$protocol] secondaryDevice[${secondaryDevice?.UUID}]"
        )
        protocol.toSession()?.unGroup(protocol = protocol, secondaryDevice = secondaryDevice)
    }

    override fun reqDeviceFeatureInfo(protocol: Int) {
        Logger.d(TAG, "reqDeviceFeatureInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqDeviceFeatureInfo(protocol = protocol)
    }

    override fun gen2GroupTws(
        protocol: Int,
        audioChannel: AudioChannel,
        connectStatus: PartyConnectStatus,
        secondaryDevice: PartyBoxDevice?
    ) {
        Logger.d(
            TAG, "gen2GroupTws() >>> [$UUID] protocol[$protocol] audioChannel[$audioChannel]" +
                    " connectStatus[$connectStatus] secondaryDevice[${secondaryDevice?.UUID}]"
        )
        protocol.toSession()?.gen2GroupTws(
            protocol = protocol,
            audioChannel = audioChannel,
            connectStatus = connectStatus,
            secondaryDevice = secondaryDevice
        )
    }

    override fun flashLight(protocol: Int, primary: Boolean, secondary: Boolean) {
        Logger.d(
            TAG,
            "flashLight() >>> [$UUID] protocol[$protocol] primary[$primary] secondary[$secondary]"
        )
        protocol.toSession()
            ?.flashLight(protocol = protocol, primary = primary, secondary = secondary)
    }

    override fun setAuraCastStatus(protocol: Int, status: AuraCastStatus) {
        Logger.d(TAG, "setAuraCastStatus() >>> [$UUID] protocol[$protocol] status[$status]")
        protocol.toSession()?.setAuraCastStatus(protocol = protocol, status = status)
    }

    override fun setDjEffectTone(protocol: Int, toneID: Int) {
        Logger.d(TAG, "setDjEffectTone() >>> [$UUID] protocol[$protocol] toneID[$toneID]")
        protocol.toSession()?.setDjEffectTone(protocol = protocol, toneID = toneID)
    }

    override fun setDjEffectVoice(protocol: Int, voiceID: Int) {
        Logger.d(TAG, "setDjEffectVoice() >>> [$UUID] protocol[$protocol] voiceID[$voiceID]")
        protocol.toSession()?.setDjEffectVoice(protocol = protocol, voiceID = voiceID)
    }

    override fun setDjEffectFilter(protocol: Int, djFilterID: Int, level: Int) {
        Logger.d(
            TAG,
            "setDjEffectFilter() >>> [$UUID] protocol[$protocol] djFilterID[$djFilterID] level[$level]"
        )
        protocol.toSession()
            ?.setDjEffectFilter(protocol = protocol, djFilterID = djFilterID, level = level)
    }

    override fun setLightPattern(protocol: Int, pattern: EnumLightPattern) {
        Logger.d(TAG, "setLightPattern() >>> [$UUID] protocol[$protocol] pattern[$pattern]")
        protocol.toSession()?.setLightPattern(protocol = protocol, pattern = pattern)
    }

    override fun setLightMainSwitch(protocol: Int, on: Boolean) {
        Logger.d(TAG, "setLightMainSwitch() >>> [$UUID] protocol[$protocol] on[$on]")
        protocol.toSession()?.setLightMainSwitch(protocol = protocol, on = on)
    }

    override fun setLightElementSwitch(protocol: Int, element: Byte, on: Boolean) {
        Logger.d(
            TAG,
            "setLightElementSwitch() >>> [$UUID] protocol[$protocol] element[$element] on[$on]"
        )
        protocol.toSession()?.setLightElementSwitch(protocol = protocol, element = element, on = on)
    }

    override fun openLightPatternLoop(protocol: Int) {
        Logger.d(TAG, "openLightPatternLoop() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.openLightPatternLoop(protocol = protocol)
    }

    override fun setLightColor(protocol: Int, color: Color) {
        Logger.d(TAG, "setLightColor() >>> [$UUID] protocol[$protocol] color[$color]")
        protocol.toSession()?.setLightColor(protocol = protocol, color = color)
    }

    override fun reqLightInfo(protocol: Int) {
        Logger.d(TAG, "reqLightInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqLightInfo(protocol = protocol)
    }

    override fun sendCommand(protocol: Int, sendCommand: GeneralGattCommand) {
        Logger.d(TAG, "sendCommand() >>> [$UUID] protocol[$protocol] sendCommand[$sendCommand]")
        protocol.toSession()?.sendCommand(protocol = protocol, sendCommand = sendCommand)
    }

    override fun reqFeedbackTone(protocol: Int) {
        Logger.d(TAG, "reqFeedbackTone() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqFeedbackTone(protocol = protocol)
    }

    override fun setFeedbackTone(protocol: Int, isOn: Boolean) {
        Logger.d(TAG, "setFeedbackTone() >>> [$UUID] protocol[$protocol] isOn[$isOn]")
        protocol.toSession()?.setFeedbackTone(protocol = protocol, isOn = isOn)
    }

    override fun setRemoteDeviceName(protocol: Int, name: String) {
        Logger.d(TAG, "setRemoteDeviceName() >>> [$UUID] protocol[$protocol] name[$name]")
        protocol.toSession()?.setRemoteDeviceName(protocol = protocol, name = name)
    }

    override fun factoryReset(protocol: Int) {
        Logger.d(TAG, "factoryReset() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.factoryReset(protocol = protocol)
    }

    override fun setAuth(protocol: Int, action: EnumAuthAction, durationSeconds: Int) {
        Logger.d(
            TAG,
            "setAuth() >>> [$UUID] protocol[$protocol] action[$action] durationSeconds[$durationSeconds]"
        )
        protocol.toSession()
            ?.setAuth(protocol = protocol, action = action, durationSeconds = durationSeconds)
    }

    override fun reqBatteryStatus(protocol: Int, features: List<EnumBatteryStatusFeature>) {
        Logger.d(
            TAG,
            "reqBatteryStatus() >>> [$UUID] protocol[$protocol] features[${features.printList()}]"
        )
        protocol.toSession()?.reqBatteryStatus(protocol = protocol, features = features)
    }

    override fun getStudioLightInfo(protocol: Int) {
        protocol.toSession()?.getStudioLightInfo(protocol = protocol)
    }

    override fun enableLightSwitch(protocol: Int, isOn: Boolean) {
        protocol.toSession()?.enableLightSwitch(protocol = protocol, isOn)
    }

    override fun setPattern(protocol: Int, activePatternId: String, colorLevel: Int) {
        protocol.toSession()?.setPattern(protocol = protocol, activePatternId, colorLevel)
    }

    override fun setBrightness(protocol: Int, brightness: Int) {
        protocol.toSession()?.setBrightness(protocol = protocol, brightness)
    }

    override fun setDynamicLevel(protocol: Int, level: Int) {
        protocol.toSession()?.setDynamicLevel(protocol = protocol, level)
    }

    override fun setProjection(protocol: Int, isOn: Boolean) {
        protocol.toSession()?.setProjection(protocol = protocol, isOn = isOn)
    }

    override fun setMoodStatus(protocol: Int, status: Int) {
        protocol.toSession()?.setMoodStatus(protocol = protocol, status)
    }

    override fun resetPatternColor(protocol: Int, patternID: String) {
        protocol.toSession()?.resetPatternColor(protocol = protocol, patternID = patternID)
    }

    override fun reqScreenDisplayInfo(protocol: Int) {
        Logger.d(TAG, "reqScreenDisplayInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqScreenDisplayInfo(protocol)
    }

    override fun updateScreenDisplayInfo(
        protocol: Int,
        screenDisplayInfo: ScreenDisplayInfo,
        command: ScreenDisplayInfo.Command
    ) {
        Logger.d(TAG, "updateScreenDisplayInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.updateScreenDisplayInfo(protocol, screenDisplayInfo, command)
    }

    override fun reqAlarmInfo(protocol: Int, requestType: AlarmInfo.RequestType) {
        Logger.d(TAG, "reqAlarmInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqAlarmInfo(protocol, requestType)
    }

    override fun setAlarmInfo(protocol: Int, alarm: AlarmInfo.Alarm?, setting: AlarmInfo.Setting?, cmd: AlarmInfo.Command) {
        Logger.d(TAG, "setAlarmInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.setAlarmInfo(protocol, alarm = alarm, setting = setting, cmd = cmd)
    }

    override fun reqSleepModeInfo(protocol: Int) {
        Logger.d(TAG, "setAlarmInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqSleepModeInfo(protocol)
    }

    override fun setSleepModeInfo(protocol: Int, sleepModeInfo: SleepModeInfo?, command: SleepModeInfo.Command) {
        Logger.d(TAG, "setAlarmInfo() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.setSleepModeInfo(protocol, sleepModeInfo = sleepModeInfo, command = command)
    }

    override fun setAuraCastSqMode(protocol: Int, isOn: Boolean) {
        Logger.d(TAG, "setAuraCastSqMode() >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.setAuraCastSqMode(protocol, isOn)
    }

    override fun reqRadioInfo(protocol: Int, cmd: ReqRadioInfoCommand) {
        Logger.d(TAG, "reqRadioInfo() >>> [$UUID] protocol[$protocol] cmd[$cmd]")
        protocol.toSession()?.reqRadioInfo(protocol = protocol, cmd = cmd)
    }

    override fun setRadioInfo(protocol: Int, cmd: RadioInfo.Command, cmdBytes: ByteArray?) {
        Logger.d(TAG, "setRadioInfo() >>> [$UUID] protocol[$protocol] cmd[${cmd.cmd}] cmdBytes[${HexUtil.encodeHexStr(cmdBytes)}]")
        protocol.toSession()?.setRadioInfo(protocol = protocol, cmd = cmd, cmdBytes = cmdBytes)
    }

    override fun reqAnalyticsData(protocol: Int) {
        Logger.d(TAG, "reqAnalyticsData >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.reqAnalyticsData(protocol = protocol)
    }

    override fun cleanAnalyticsData(protocol: Int) {
        Logger.d(TAG, "cleanAnalyticsData >>> [$UUID] protocol[$protocol]")
        protocol.toSession()?.cleanAnalyticsData(protocol = protocol)
    }

    override fun reqAmbientLightInfo(protocol: Int) {
        protocol.toSession()?.reqAmbientLightInfo(protocol)
    }

    override fun updateAmbientLightInfo(
        protocol: Int,
        ambientLightInfo: AmbientLightInfo,
        command: AmbientLightInfo.Command
    ) {
        protocol.toSession()?.updateAmbientLightInfo(protocol,ambientLightInfo,command)
    }

    override fun reqAutoOffTimer(protocol: Int) {
        protocol.toSession()?.reqAutoOffTimer(protocol = protocol)
    }

    override fun setAutoOffTimer(protocol: Int, seconds: Int) {
        protocol.toSession()?.setAutoOffTimer(protocol = protocol, seconds = seconds)
    }

    override fun getDfuInfo() {
        partyBoxSppSession?.getDfuInfo()
    }

    override fun applyDfu() {
        partyBoxSppSession?.applyDfu()
    }

    override fun cancelDfu() {
        partyBoxSppSession?.cancelDfu()
    }

    override fun startDfu(dfuCrc: Int, dfuSize: Int, dfuVersion: String, bpType: Byte) {
        partyBoxSppSession?.startDfu(
            dfuCrc = dfuCrc, dfuSize = dfuSize, dfuVersion = dfuVersion, bpType = bpType
        )
    }

    override fun setDfuData(dfuData: ByteArray) {
        partyBoxSppSession?.setDfuData(dfuData = dfuData)
    }

    fun updatePartyConnectStatusManually(status: PartyConnectStatus) {
        rawPartyConnectStatus = status.value

        deviceListeners.forEach { listener ->
            listener.onPartyConnectStatusChanged(status)
        }
    }

    /**
     * May use BR/EDR session instead of LE(Gatt) session when these conditions match:
     * 1. A2DP connected;
     * 2. BLE broadcast indicated support [supportGattOverBrEdr];
     * 3. Has valid mac address;
     *
     * Other conditions plz modified in business layer [PartyBoxExts.getBusinessSession]
     * instead of change this function directly.
     */
    fun ableUseBrEdrSession(): Boolean {
        val protocol = protocolFlags.targetGattProtocol()
        return isA2DPConnected && (BluetoothDevice.TRANSPORT_BREDR == protocol) && !macAddress.isNullOrBlank()
    }

    companion object {
        private const val TAG = "PartyBoxBTDevice"
    }
}