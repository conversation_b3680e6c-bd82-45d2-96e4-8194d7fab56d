package com.harman.discover.bean

import androidx.annotation.IntRange
import com.harman.command.one.command.EnumMuteStatus
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.session.BaseBusinessSession
import com.harman.connect.listener.IGattListener
import com.harman.connect.OneWiFiSession
import com.harman.connect.BaseSppSession
import com.harman.connect.DefaultSppBusinessSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.listener.IOneDeviceListener
import com.harman.discover.IMediaParam
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.PartyLightBTDevice
import com.harman.discover.bean.bt.PortableBTDevice
import com.harman.discover.info.EnumPlatform
import com.harman.discover.info.EnumProductLine
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.colorStr
import com.harman.discover.util.Tools.objectUniID
import com.harman.discover.util.Tools.macAddress
import com.harman.discover.util.Tools.macAddressCRCWithCache
import com.harman.discover.util.Tools.productLine
import com.harman.discover.util.Tools.toCRC
import com.harman.discover.util.Tools.toPid
import com.harman.discover.util.Tools.wlan0
import com.harman.discover.util.crc.CrcHelper.dummyUUID
import com.harman.log.Logger
import com.wifiaudio.model.DeviceItem
import java.util.UUID
import kotlin.math.min

/**
 * Created by gerrardzhang on 2024/2/1.
 *
 * Define base data bean which represent a combination of BLE and WiFi device.
 * Extend and implement [gattBusiness] to represent a spec type of device like:
 * [OneDevice] [PortableDevice] [PartyBoxDevice] [PartyLightDevice] and [DefaultDevice]
 */

typealias Device = BaseDevice<*, *, *, *, *, *>

abstract class BaseDevice<
        out GattBusiness : BaseBusinessSession,
        out SppBusiness : DefaultSppBusinessSession,
        out BTDeviceType : BaseBTDevice<Role, AuraCastRole>,
        Role,
        AuraCastRole,
        OfflineDummyType
        >
private constructor() : BaseBusinessSession, IMediaParam {

    constructor(
        btDevice: BaseBTDevice<*, *>? = null,
        deviceItem: DeviceItem? = null,
        offlineDummy: OfflineDummyType? = null
    ) : this() {
        btDevice?.let {
            this._btDevice = btDevice
            Logger.d(TAG, "init() >>> [${objectUniID()}][$UUID] update bt instance [${btDevice.objectUniID()}][${btDevice.UUID}]")
        }

        deviceItem?.let {
            this.wifiDevice = WiFiDevice(deviceItem)
        }

        this.offlineDummy = offlineDummy

        updateUUID()
    }

    private val writeDeviceLock = Object()

    /**
     * Use [writeDeviceLock] to protect async write changes on [_btDevice]|[wifiDevice] like
     * [addOrUpdate] [remove]
     */
    @Volatile
    protected var _btDevice: BaseBTDevice<*, *>? = null
        private set

    @Volatile
    var wifiDevice: WiFiDevice? = null
        private set

    /**
     * Dummy instance restored from local cache.
     * Only used for some display business.
     * Be diff from [wifiDevice]
     */
    @Volatile
    var offlineDummy: OfflineDummyType? = null

    val productLine: EnumProductLine
        get() {
            if (null != wifiDevice) {
                return EnumProductLine.ONE
            }

            bleDevice?.productLine()?.let {
                return it
            }

            return when (offlineDummy) {
                is DeviceItem -> EnumProductLine.ONE
                is PartyBoxBTDevice -> EnumProductLine.PARTY_BOX
                is PortableBTDevice -> EnumProductLine.PORTABLE
                is PartyLightBTDevice -> EnumProductLine.PARTY_LIGHT
                is PartyBandBTDevice -> EnumProductLine.BAND_BOX
                else -> EnumProductLine.UNKNOWN
            }
        }

    /**
     * @return the exists [_wifiSession] if it is not null to avoid generate different sessions
     * with same IP Address.
     *
     * Otherwise generate a new one based on the latest IP address from [DeviceItem.IP] cause BLE address
     * might be changed in interval.
     */
    private var _wifiSession: OneWiFiSession? = null
    val wifiSession: OneWiFiSession?
        get() = wifiDevice?.wifiSession

    /**
     * @see [BaseBTDevice.gattSession]
     */
    open val gattSession: BaseGattSession<*, *, *>?
        get() = bleDevice?.gattSession

    /**
     * @see [BaseBTDevice.brEdrSession]
     */
    val brEdrSession: BaseBrEdrSession<*, *, *>?
        get() = bleDevice?.brEdrSession

    /**
     * @see [BaseBTDevice.sppSession]
     */
    val sppSession: BaseSppSession?
        get() = bleDevice?.sppSession

    /**
     * @return a [sppBusiness] which represent the business call interface via SPP channel.
     */
    abstract val sppBusiness: SppBusiness?

    /**
     * @return [_btDevice] which been regarded as mapped type of BLE device type.
     */
    abstract val bleDevice: BTDeviceType?

    /**
     * @return [OneBTDevice.hadAuraCastPhysicalBtn] for one platform devices,
     * and const value for other platform devices.
     */
    abstract val hadAuraCastPhysicalBtn: Boolean

    @get:IntRange(from = 0L, to = 100L)
    abstract val batteryLevel: Int

    abstract val isCharging: Boolean

    val bleAddress: String?
        get() = bleDevice?.bleAddress

    /**
     * @return device name which comes from [wifiDevice] first, or [bleDevice] if [wifiDevice] didn't exists.
     */
    val deviceName: String?
        get() {
            wifiDevice?.deviceItem?.speakerName?.let { name ->
                if (name.isNotBlank()) {
                    return name
                }
            }

            bleDevice?.deviceName?.let { name ->
                if (name.isNotBlank()) {
                    return name
                }
            }

            return dummyDeviceName
        }

    /**
     * Use MAC Address CRC as UUID as default for easy matching between [wifiDevice] and [bleDevice]
     * Lower case for hex as default.
     */
    private var _UUID: String? = null
    val UUID: String?
        get() = _UUID ?: dummyUUID

    /**
     * Use extension function [BaseDevice.pid()] instead in App module in compatible for WiFi device.
     *
     * Use pid from BLE broadcast as the highest priority cause [DeviceItem.project] might be wrong in some cases.
     */
    val pid: String?
        get() = bleDevice?.pid ?: wifiDevice?.pid ?: dummyPid

    val vid: String?
        get() = bleDevice?.vendorID

    open val cid: String?
        get() = wifiDevice?.colorStr ?: bleDevice?.colorID ?: dummyCid

    val macAddress: String?
        get() {
            val srcWifi = wifiDevice?.deviceItem?.macAddress()
            if (!srcWifi.isNullOrBlank()) {
                return srcWifi
            }

            val srcBle = bleDevice?.macAddress
            if (!srcBle.isNullOrBlank()) {
                return srcBle
            }

            return dummyMacAddress
        }

    val wlan0: String?
        get() {
            return wifiDevice?.deviceItem?.wlan0() ?: (bleDevice as? OneBTDevice)?.deviceInfoExt?.deviceInfo?.wlan0Mac ?: dummyWlan0
        }

    /**
     * Lower case.
     */
    open fun macAddressCRC(): String? {
        return this.macAddressCRC
    }

    private var macAddressCRC: String? = null
        get() {
            if (!field.isNullOrBlank()) {
                return field
            }

            val address = macAddress
            if (address.isNullOrBlank()) {
                return null
            }

            field = address.toCRC()
            return field
        }

    var customerServiceReportId: String? = null

    val isA2DPConnected: Boolean
        get() = bleDevice?.isA2DPConnected ?: false

    val isGattConnected: Boolean
        get() = true == bleDevice?.isGattConnected

    val isBrEdrConnected: Boolean
        get() = true == bleDevice?.isBrEdrConnected

    val isSecureBleSupport: Boolean
        get() = true == bleDevice?.isSecureBleSupport

    val firstTouchTime: Long
        get() {
            val wifiTs = wifiDevice?.firstTouchTime
            val btTs = bleDevice?.firstTouchTime
            if (null != wifiTs && null != btTs) {
                return min(wifiTs, btTs)
            }

            return wifiTs ?: btTs ?: -1
        }

    val lastTouchTime: Long
        get() = bleDevice?.lastTouchTime ?: -1

    open val isAuraCastSupport: Boolean
        get() = bleDevice?.isAuraCastSupport ?: false

    val isAuraCastOn: Boolean
        get() = bleDevice?.isAuraCastOn ?: false

    /**
     * is AuraCast disabled/not allow
     */
    val isAuraCastDisabled: Boolean
        get() = bleDevice?.isAuraCastDisabled ?: false

    /**
     * Don't use as role judgement directly. Use [Device.isBroadcasterOn] instead.
     */
    open val isBroadcaster: Boolean
        get() = bleDevice?.isBroadcaster ?: false

    /**
     * Don't use as role judgement directly. Use [Device.isReceiverOn] instead.
     */
    open val isReceiver: Boolean
        get() = bleDevice?.isReceiver ?: false

    val auraCastRole: Int?
        get() = bleDevice?.auraCastRoleValue

    /**
     * Priority order: WiFi > BT
     */
    @get:IntRange(from = 0, to = 100)
    override val volume: Int
        get() = (if (isWiFiOnline) wifiDevice?.volume else bleDevice?.volume) ?: 0

    override val isPlaying: Boolean
        get() = (if (isWiFiOnline) wifiDevice?.isPlaying else bleDevice?.isPlaying) ?: false

    /**
     * Add or update [bleDevice]|[wifiDevice] inside.
     */
    fun addOrUpdate(src: Device?) {
        src?.bleDevice?.let { device ->
            addOrUpdate(device = device)
        }
        src?.wifiDevice?.deviceItem?.let { deviceItem ->
            addOrUpdate(device = deviceItem)
        }
    }

    fun addOrUpdate(device: BaseBTDevice<*, *>) {
        synchronized(writeDeviceLock) {
            _btDevice?.clone(device) ?: run {
                syncListeners(btDevice = device, wiFiDevice = wifiDevice)
                _btDevice = device
                Logger.d(TAG, "addOrUpdate() >>> [${objectUniID()}][$UUID] update bt instance [${device.objectUniID()}][${device.UUID}]")
            }

            updateUUID()
        }
    }

    fun addOrUpdate(device: DeviceItem) {
        synchronized(writeDeviceLock) {
            wifiDevice = newOrUpdate(device = device).apply {
                syncListeners(btDevice = bleDevice, wiFiDevice = this)
                notifyParamsChanged(device = device)
            }

            updateUUID()
        }
    }

    fun update(device: DeviceItem) {
        wifiDevice?.notifyParamsChanged(device = device)
    }

    private fun syncListeners(btDevice: BaseBTDevice<*, *>?, wiFiDevice: WiFiDevice?) {
        btDevice ?: return
        wiFiDevice ?: return

        wiFiDevice.wifiListeners.forEach { listener ->
            btDevice.registerGattListener(listener)
        }

        btDevice.gattListeners.forEach { listener ->
            wiFiDevice.registerDeviceListener(listener as? IOneDeviceListener)
        }
    }

    private fun newOrUpdate(device: DeviceItem): WiFiDevice {
        return wifiDevice?.updateDeviceItem(device) ?: WiFiDevice(device)
    }

    val isBLEOnline: Boolean
        get() = null != bleDevice

    val isWiFiOnline: Boolean
        get() = null != wifiDevice

    val isOffline: Boolean
        get() = !isOnline

    val isSPPOnline: Boolean
        get() = bleDevice?.isSPPConnected ?: false

    open val isOnline: Boolean
        get() = isBLEOnline || isWiFiOnline || isSPPOnline

    val ableSendCmd: Boolean
        get() = isWiFiOnline ||
                true == gattSession?.isConnected && true != (this as? OneDevice)?.inOobe?.get() ||
                true == brEdrSession?.isConnected && true != (this as? OneDevice)?.inOobe?.get() ||
                isSPPOnline && true != (this as? OneDevice)?.inOobe?.get()

    val isMute: Boolean
        get() {
            wifiDevice?.deviceItem?.devInfoExt?.dlnaDesireMute?.let { status ->
                return EnumMuteStatus.isMute(status)
            }

            bleDevice?.let { btDevice ->
                return btDevice.isMute
            }

            return false
        }

    val volumeWithMute: Int
        get() = if (isMute) 0 else volume

    val platform: EnumPlatform?
        get() = bleDevice?.platform

    val gattStatus: EnumConnectionStatus?
        get() = bleDevice?.gattStatus

    val brEdrStatus: EnumConnectionStatus?
        get() = bleDevice?.brEdrStatus

    /**
     * Remove [bleDevice]|[wifiDevice] inside.
     * @return true if neither [bleDevice]|[wifiDevice] exists and represent this [BaseDevice] as an offline one.
     */
    fun remove(device: BaseBTDevice<*, *>): Boolean {
        synchronized(writeDeviceLock) {
            _btDevice?.let { inner ->
                if (inner.UUID == device.UUID) {
                    Logger.d(TAG, "remove() >>> [${objectUniID()}][$UUID] clear bt instance [${device.objectUniID()}][${device.UUID}]")
                    _btDevice = null
                }
            }
        }

        return isOffline
    }

    fun remove(device: DeviceItem): Boolean {
        synchronized(writeDeviceLock) {
            wifiDevice?.deviceItem?.let { inner ->
                if (Tools.equalsMacAddressCRC(inner, device)) {
                    wifiDevice = null
                }
            }
        }

        return isOffline
    }

    /**
     * mix listeners management of both WiFi and Gatt
     * Effected for both [BaseGattSession] and [BaseBrEdrSession]
     */
    fun registerDeviceListener(listener: IGattListener?) {
        listener ?: return
        bleDevice?.registerGattListener(listener)
        wifiDevice?.registerDeviceListener(listener as? IOneDeviceListener)
    }

    fun unregisterDeviceListener(listener: IGattListener?) {
        listener ?: return
        bleDevice?.unregisterGattListener(listener)
        wifiDevice?.unregisterDeviceListener(listener as? IOneDeviceListener)
    }

    fun clearListeners() {
        bleDevice?.clearGattListeners()
        wifiDevice?.clearDeviceListeners()
    }

    val dummyPid: String?
        get() = when (val dummy = offlineDummy) {
            is DeviceItem -> dummy.toPid()
            is BaseBTDevice<*, *> -> dummy.pid
            else -> null
        }

    val dummyCid: String?
        get() = when (val dummy = offlineDummy) {
            is DeviceItem -> dummy.colorStr()
            is BaseBTDevice<*, *> -> dummy.colorID
            else -> null
        }

    val dummyDeviceName: String?
        get() = when (val dummy = offlineDummy) {
            is DeviceItem -> dummy.speakerName
            is BaseBTDevice<*, *> -> dummy.deviceName
            else -> null
        }

    private val dummyUUID: String?
        get() = when (val dummy = offlineDummy) {
            is DeviceItem -> dummy.dummyUUID()
            is BaseBTDevice<*, *> -> dummy.UUID
            else -> null
        }

    private val dummyMacAddress: String?
        get() = when (val dummy = offlineDummy) {
            is DeviceItem -> dummy.macAddress()
            is BaseBTDevice<*, *> -> dummy.macAddress
            else -> null
        }

    private val dummyWlan0: String?
        get() = when (val dummy = offlineDummy) {
            is DeviceItem -> dummy.wlan0() ?: dummy.devStatus?.mac
            is BaseBTDevice<*, *> -> dummy.macAddress
            else -> null
        }

    /**
     * Use [UUID] as conditions of compare.
     *
     * There might be a condition that two [DeviceItem] contains either [bleDevice] or [wifiDevice],
     * but the [OneBTDevice.UUID] equals [DeviceItem.macAddressCRCWithCache]. [equals] will return true under this condition.
     */
    override fun equals(other: Any?): Boolean {
        if (other !is Device) {
            return false
        }

        val selfUUID = UUID
        val otherUUID = other.UUID

        return !selfUUID.isNullOrBlank() &&
                !otherUUID.isNullOrBlank() &&
                selfUUID.equals(otherUUID, true)
    }

    override fun hashCode(): Int {
        return UUID?.hashCode() ?: super.hashCode()
    }

    override fun toString(): String {
        val sb = StringBuilder()
        sb.append("UUID[$UUID]").append("\n")
        sb.append("BT: ${bleDevice?.toString()}").append("\n")
        sb.append("WiFi.uuid[${wifiDevice?.deviceItem?.uuid}]\n")
        sb.append("WiFi.speakerName[${wifiDevice?.deviceItem?.speakerName}]\n")
        sb.append("WiFi.MAC[${wifiDevice?.deviceItem?.macAddress()}]\n")
        sb.append("type[${javaClass}]").append("\n")

        return sb.toString()
    }

    open fun sameContent(device: Device?): Boolean {
        device ?: return false

        if (this::class != device::class) {
            return false
        }

        val self = bleDevice
        val other = device.bleDevice

        if (null == self && null == other) {
            return wifiDevice == device.wifiDevice
        }

        if (null == self || null == other) {
            return false
        }

        return self.sameContent(other) && wifiDevice == device.wifiDevice
    }

    private fun updateUUID() {
        val bleUUID = _btDevice?.UUID
        if (!bleUUID.isNullOrBlank()) {
            _UUID = bleUUID
            return
        }

        val wifiUUID = wifiDevice?.deviceItem?.macAddressCRCWithCache()
        if (!wifiUUID.isNullOrBlank()) {
            _UUID = wifiUUID
            return
        }

        val dummyUUID = dummyUUID
        if (!dummyUUID.isNullOrBlank()) {
            _UUID = dummyUUID
            return
        }
    }

    companion object {
        private const val TAG = "BaseDevice"
    }
}