package com.harman.discover

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.bluetooth.le.ScanCallback
import androidx.annotation.VisibleForTesting
import com.debug.PingUtil
import com.harman.discover.bean.BaseDevice
import com.harman.discover.bean.DefaultDevice
import com.harman.discover.bean.Device
import com.harman.discover.bean.OneDevice
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.PartyLightDevice
import com.harman.discover.bean.PortableDevice
import com.harman.discover.bean.SppDeviceContainer
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.PartyLightBTDevice
import com.harman.discover.bean.bt.PortableBTDevice
import com.harman.discover.info.EnumDeviceProtocol
import com.harman.discover.info.EnumProductLine
import com.harman.discover.util.DiffResult
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.isBrEdrOnline
import com.harman.discover.util.Tools.macAddressCRCWithCache
import com.harman.discover.util.Tools.supportBrEdr
import com.harman.discover.util.Tools.sync
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_IO
import com.jbl.one.configuration.AppConfigurationUtils
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.rightfrag_obervable.MenuRightFragInstaller
import com.wifiaudio.service.WAUpnpDeviceManager
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.locks.ReentrantLock
import kotlin.math.abs

/**
 * Created by gerrardzhang on 2024/1/29.
 *
 * To store snapshots of BLE and WiFi devices which scanned.
 * Update snapshots of [BaseDevice] after notify events of BLE and/or WiFi.
 */
object DeviceStore {

    private val _bleSnapshots: CopyOnWriteArrayList<BaseBTDevice<*, *>> = CopyOnWriteArrayList()
    val bleDevices: List<BaseBTDevice<*, *>>
        get() = _bleSnapshots

    private val _wifiSnapshots: CopyOnWriteArrayList<DeviceItem> = CopyOnWriteArrayList()
    val wifiDevices: List<DeviceItem>
        get() = _wifiSnapshots

    private val _baseDeviceSnapshots: CopyOnWriteArrayList<Device> =
        CopyOnWriteArrayList()
    val baseDevices: List<Device>
        get() = _baseDeviceSnapshots

    /**
     * Used for verifying if WiFi device is available once network status change to false.
     */
    private val checkWiFiByPingSnapshots: CopyOnWriteArrayList<Device> = CopyOnWriteArrayList()

    /**
     * Used for protecting async write changes on [_bleSnapshots] [_wifiSnapshots] and [_baseDeviceSnapshots],
     * like [onBLEDeviceNotify] [remove] [onWiFiDevicesNotify] [onDeviceAddOrUpdate] [onDeviceRemoved]
     */
    private val bleSnapLock = ReentrantLock()
    private val wifiSnapLock = ReentrantLock()
    private val hmDeviceSnapLock = ReentrantLock()

    fun find(
        uuid: String,
        productLine: EnumProductLine? = null
    ): Device? {
        if (uuid.isBlank()) {
            return null
        }

        return baseDevices.firstOrNull { device ->
            device.UUID == uuid &&
                    (null == productLine || device.productLine == productLine)
        }
    }

    fun findOne(uuid: String): OneDevice? =
        find(uuid = uuid, klz = OneDevice::class.java) as? OneDevice

    val oneDevices: List<OneDevice>
        get() = baseDevices.filterIsInstance<OneDevice>()

    fun findPortable(uuid: String): PortableDevice? =
        find(uuid = uuid, klz = PortableDevice::class.java) as? PortableDevice

    val portableDevices: List<PortableDevice>
        get() = baseDevices.filterIsInstance<PortableDevice>()

    fun findPartyBox(uuid: String): PartyBoxDevice? =
        find(uuid = uuid, klz = PartyBoxDevice::class.java) as? PartyBoxDevice

    val partyBoxDevices: List<PartyBoxDevice>
        get() = baseDevices.filterIsInstance<PartyBoxDevice>()

    fun findPartyLight(uuid: String): PartyLightDevice? =
        find(uuid = uuid, klz = PartyLightDevice::class.java) as? PartyLightDevice

    val partyLightDevices: List<PartyLightDevice>
        get() = baseDevices.filterIsInstance<PartyLightDevice>()

    private fun find(
        uuid: String,
        klz: Class<out Device>
    ): Device? {
        if (uuid.isBlank()) {
            return null
        }

        return baseDevices.filterIsInstance(klz).firstOrNull { device ->
            device.UUID == uuid || device.wifiDevice?.deviceItem?.uuid == uuid
        }
    }

    /**
     * @return device object
     * which already exists in the [_bleSnapshots] before add with the latest params inside,
     * or the input [device] if not exists before.
     */
    fun onBLEDeviceNotify(
        device: BaseBTDevice<*, *>,
        merger: IDeviceInfoMerger = BLEMerger
    ): BaseBTDevice<*, *> {
        val dummy = bleSnapLock.sync {
            upsert(device = device, merger = merger)
        }

        // update BLE info inside _hmDeviceSnapshots
        onDeviceAddOrUpdate(dummy).also {
            it.handleBleAdvChangeByPing()
            it.notifyOnlineOrUpdate()
        }

        notifyUpdate()

        //Logger.d(TAG, "onBLEDeviceNotify() >>> BLE devices.size[${_bleSnapshots.size}]")

        return dummy
    }

    /**
     * @return devices list
     * which already exists in the [_bleSnapshots] before add with the latest params inside,
     * or the input [devices] if not exists before.
     */
    fun onBLEDevicesNotify(
        devices: List<BaseBTDevice<*, *>>,
        merger: IDeviceInfoMerger = BLEMerger
    ): List<BaseBTDevice<*, *>> {
        val dummys = bleSnapLock.sync {
            devices.map { device ->
                upsert(device = device, merger = merger)
            }
        }

        // update BLE info inside _hmDeviceSnapshots
        onDevicesAddOrUpdate(dummys).forEach { dummy ->
            dummy.handleBleAdvChangeByPing()
            dummy.notifyOnlineOrUpdate()
        }

        notifyUpdate()

        //Logger.d(TAG, "onBLEDevicesNotify() >>> BLE devices.size[${_bleSnapshots.size}]")

        return dummys
    }

    /**
     * Protected by [bleSnapLock]
     * @return snapshot device instance in [_bleSnapshots] if already exists, or [device] if not.
     */
    private fun upsert(
        device: BaseBTDevice<*, *>,
        merger: IDeviceInfoMerger
    ): BaseBTDevice<*, *> =
        if (!AppConfigurationUtils.isSupportedDevice(device.pid) &&
            (AppConfigurationUtils.isSupportedRedirection(device.pid)
                    || AppConfigurationUtils.isSupportedCrossAuraCast(device.pid))
        ) {
            device
        } else if (!_bleSnapshots.addIfAbsent(device)) {
            val snapshot = _bleSnapshots.firstOrNull { snapshot ->
                // Same class and same UUID. @see BaseBLEDevice.equals
                device == snapshot
            }

            if (null != snapshot) {
                // update different information from [device] into [snapshot]
                merger.merge(from = device, to = snapshot)
            } else {
                // snapshots might be modified async while exec firstOrNull, try to insert another time.
                _bleSnapshots.addIfAbsent(device)
                device
            }
        } else {
            device
        }

    /**
     * check SPP status if [bleDevice] support BR_EDR [BaseBTDevice.supportBrEdr] at the same time.
     * Didn't remove it if so and SPP is connected.
     */
    fun onBLEDeviceExpired(bleDevice: BaseBTDevice<*, *>): Boolean {
        if (bleDevice.supportBrEdr() && bleDevice.isBrEdrOnline()) {
            // Remove [EnumDeviceProtocol.PROTOCOL_BLE] for sure [onSPPDevicesNotify] can remove device after SPP offline
            bleDevice.removeBLEProtocolFlag()
            return false
        }

        return remove(bleDevice)
    }

    /**
     * Remove instance in [_bleSnapshots] and [_baseDeviceSnapshots] if necessary based on [bleDevice]
     * @return true if found same BLE device in [_bleSnapshots] and update [_baseDeviceSnapshots] if necessary.
     */
    @VisibleForTesting
    fun remove(bleDevice: BaseBTDevice<*, *>): Boolean {
        Logger.d(TAG, "remove() >>> pid[${bleDevice.pid}] uuid[${bleDevice.UUID}]")
        var result = false

        val snapshot = bleSnapLock.sync {
            val snapshot = _bleSnapshots.firstOrNull { inner ->
                inner == bleDevice
            }

            result = _bleSnapshots.remove(snapshot)
            snapshot
        }

        snapshot ?: return result

        // remove BLE info inside _hmDeviceSnapshots and remove the HmDevice related with it if necessary.
        onDeviceRemoved(snapshot).notifyOffline(byBleDevice = true)

        notifyUpdate()

        //Logger.d(TAG, "remove() >>> BLE devices.size[${_bleSnapshots.size}]")
        return result
    }

    /**
     * Remove [device] in [_bleSnapshots] [_wifiSnapshots] and [_baseDeviceSnapshots]
     */
    fun remove(device: Device) {
        Logger.d(TAG, "remove() >>> ${device.UUID}")
        onDeviceRemoved(device = device).notifyOffline()
    }

    /**
     * Remove [device] in [_bleSnapshots] [_wifiSnapshots] and [_baseDeviceSnapshots]
     * Be triggered at the point like factory reset.
     * Notify [IHmDeviceObserver.onDeviceFactoryReset] additionally.
     */
    fun factoryReset(device: Device) {
        Logger.d(TAG, "factoryReset() >>> ${device.UUID}")
        device.wifiDevice?.deviceItem?.let { insertBlackList(it) }
        remove(device = device)
        DeviceScanner.notifyDeviceFactoryReset(device = device)
    }

    /**
     * Update both [_wifiSnapshots] and [_baseDeviceSnapshots] after receiving latest WiFi device list.
     *
     * @param currents latest device snapshots from [WAUpnpDeviceManager.getAllDevices]
     * @return [DiffResult] after comparing with
     */
    fun onWiFiDevicesNotify(currents: List<DeviceItem>): DiffResult {
        val currentsWithoutBlackList = currents.filter { current ->
            !isOnBlackList(deviceItem = current)
        }

        val lasts = wifiDevices.toList()

        val diffs = Tools.diffChanges(lasts = lasts, currents = currentsWithoutBlackList)

        wifiSnapLock.sync {
            _wifiSnapshots.clear()
            _wifiSnapshots.addAll(currentsWithoutBlackList)
            //Logger.d(TAG, "onWiFiDevicesNotify() >>> update ${currentsWithoutBlackList.printListMacAddressCRC()}")
        }

        currentsWithoutBlackList.forEach { device ->
            onDeviceAddOrUpdate(device)
                .notifyOnlineOrUpdate()
        }

        diffs.offlineDevs.forEach { device ->
            Logger.i(TAG, "onWiFiDevicesNotify() >>> offline[${device.macAddressCRCWithCache()}]")
            onDeviceRemoved(device)
                .notifyOffline()
        }

        notifyUpdate()

        return diffs
    }

    /**
     * Only update meta info which managed by [WiFiDevice.notifyParamsChanged].
     * Didn't modify [_baseDeviceSnapshots] or [_wifiSnapshots]
     */
    fun onWiFiMetaNotify(currents: List<DeviceItem>) {
        currents.forEach { current ->
            val snapshot = _baseDeviceSnapshots.firstOrNull { snapshot ->
                snapshot.UUID == current.macAddressCRCWithCache()
            } ?: return@forEach

            snapshot.update(current)
        }
    }

    fun onWiFiDevicesOffline(deviceItem: DeviceItem) {
        Logger.i(TAG, "onWiFiDevicesOffline() >>> offline[${deviceItem.macAddressCRCWithCache()}]")
        val snapshot = _baseDeviceSnapshots.firstOrNull { snapshot ->
            snapshot.UUID == deviceItem.macAddressCRCWithCache()
        }
        snapshot?.let { onDeviceRemoved(it).notifyOffline() }
    }

    /**
     * Find out the [BaseDevice] from [_baseDeviceSnapshots] after some [BaseBTDevice] add/update/remove,
     * change the [BaseDevice.bleDevice]|[BaseDevice.wifiDevice] if so, or create a new [BaseDevice].
     *
     * @return the [BaseDevice] related with [bleDevice] or [wifiDevice]
     */
    private fun onDeviceAddOrUpdate(bleDevice: BaseBTDevice<*, *>): Device {
        return hmDeviceSnapLock.sync {
            val snapshot = _baseDeviceSnapshots.firstOrNull { hmDevice ->
                hmDevice.UUID == bleDevice.UUID
            }

            val device = snapshot?.also { device ->
                device.addOrUpdate(bleDevice)
            } ?: bleDevice.toHmDevice().also { device ->
                Logger.i(TAG, "onDeviceAddOrUpdate() >>> BT [${device.UUID}]")
                _baseDeviceSnapshots.add(device)
            }

            device
        }
    }

    private fun onDevicesAddOrUpdate(bleDevices: List<BaseBTDevice<*, *>>): List<Device> {
        return hmDeviceSnapLock.sync {
            val devices = bleDevices.map { bleDevice ->
                val snapshot = _baseDeviceSnapshots.firstOrNull { hmDevice ->
                    hmDevice.UUID == bleDevice.UUID
                }

                snapshot?.also { device ->
                    device.addOrUpdate(bleDevice)
                } ?: bleDevice.toHmDevice().also { device ->
                    Logger.i(TAG, "onDevicesAddOrUpdate() >>> BT [${device.UUID}]")
                    _baseDeviceSnapshots.add(device)
                }
            }

            devices
        }
    }

    private fun BaseBTDevice<*, *>.toHmDevice():
            Device {
        return when (this) {
            is PortableBTDevice -> PortableDevice(bleDevice = this)
            is PartyBoxBTDevice -> PartyBoxDevice(bleDevice = this)
            is OneBTDevice -> OneDevice(bleDevice = this)
            is PartyLightBTDevice -> PartyLightDevice(bleDevice = this)
            is PartyBandBTDevice -> PartyBandDevice(leDevice = this, null)
            else -> DefaultDevice(bleDevice = this)
        }
    }

    private fun onDeviceAddOrUpdate(wifiDevice: DeviceItem):
            Device {
        return hmDeviceSnapLock.sync {
            val snapshot = _baseDeviceSnapshots.firstOrNull { snapshot ->
                snapshot.UUID == wifiDevice.macAddressCRCWithCache()
            }

            snapshot?.also { device ->
                device.addOrUpdate(wifiDevice)
            } ?: wifiDevice.toHmDevice().also { device ->
                Logger.i(TAG, "onDeviceAddOrUpdate() >>> WiFi [${device.UUID}]")
                _baseDeviceSnapshots.add(device)
            }
        }
    }

    private fun DeviceItem.toHmDevice(): OneDevice {
        return OneDevice(wifiDevice = this)
    }

    /**
     * Find out the [BaseDevice] from [_baseDeviceSnapshots] after some [BaseBTDevice] remove,
     * remove the [BaseDevice.bleDevice]|[BaseDevice.wifiDevice] if match.
     *
     * @return list of [BaseDevice.isOffline]
     */
    private fun onDeviceRemoved(bleDevice: BaseBTDevice<*, *>):
            List<Device> {
        return hmDeviceSnapLock.sync {
            val snapshot = _baseDeviceSnapshots.firstOrNull { hmDevice ->
                hmDevice.UUID == bleDevice.UUID
            }

            if (null != snapshot && snapshot.remove(bleDevice)) {
                val offlines = _baseDeviceSnapshots.filter { hmDevice ->
                    hmDevice.isOffline
                }

                _baseDeviceSnapshots.removeAll(offlines)
                offlines
            } else emptyList()
        }
    }

    /**
     * Find out the [BaseDevice] from [_baseDeviceSnapshots] after some [BaseBTDevice] remove,
     * remove the [BaseDevice.bleDevice]|[BaseDevice.wifiDevice] if match.
     *
     * @return list of [BaseDevice.isOffline]
     */
    private fun onDeviceRemoved(deviceItem: DeviceItem):
            List<Device> {
        return hmDeviceSnapLock.sync {
            val snapshot = _baseDeviceSnapshots.firstOrNull { hmDevice ->
                hmDevice.UUID == deviceItem.macAddressCRCWithCache()
            }

            if (null != snapshot && snapshot.remove(deviceItem)) {
                val offlines = _baseDeviceSnapshots.filter { hmDevice ->
                    hmDevice.isOffline
                }

                _baseDeviceSnapshots.removeAll(offlines)
                offlines
            } else emptyList()
        }
    }

    private fun onDeviceRemoved(device: Device):
            List<Device> {
        wifiSnapLock.sync {
            device.wifiDevice?.deviceItem?.let { deviceItem ->
                val lasts = _wifiSnapshots.toMutableList()

                if (lasts.removeAll { last ->
                        last.uuid == deviceItem.uuid
                    }) {
                    Logger.i(TAG, "onDeviceRemoved() >>> _wifiSnapshots hit [${device.UUID}]")
                } else {
                    Logger.w(TAG, "onDeviceRemoved() >>> _wifiSnapshots not hit [${device.UUID}]")
                }

                _wifiSnapshots.clear()
                _wifiSnapshots.addAll(lasts)
            }
        }

        bleSnapLock.sync {
            device.bleDevice?.let { bleDevice ->
                val snapshot = _bleSnapshots.filter { inner ->
                    inner == bleDevice
                }

                if (_bleSnapshots.removeAll(snapshot.toSet())) {
                    Logger.i(TAG, "onDeviceRemoved() >>> _bleSnapshots hit [${device.UUID}]")
                } else {
                    Logger.w(TAG, "onDeviceRemoved() >>> _bleSnapshots not hit [${device.UUID}]")
                }
            }
        }

        return hmDeviceSnapLock.sync {
            val offlines = _baseDeviceSnapshots.filter { snapshot ->
                snapshot.UUID == device.UUID
            }

            if (offlines.isNotEmpty()) {
                if (_baseDeviceSnapshots.removeAll(offlines.toSet())) {
                    Logger.i(TAG, "onDeviceRemoved() >>> _baseDeviceSnapshots hit [${device.UUID}]")
                } else {
                    Logger.w(TAG, "onDeviceRemoved() >>> _baseDeviceSnapshots not hit [${device.UUID}]")
                }

                offlines
            } else {
                Logger.w(TAG, "onDeviceRemoved() >>> _baseDeviceSnapshots not hit [${device.UUID}]")
                emptyList()
            }
        }
    }

    /**
     * Clear all snapshots, include [BaseBTDevice], [DeviceItem] and [BaseDevice]
     */
    fun clear() {
        bleSnapLock.sync {
            _bleSnapshots.clear()
        }

        wifiSnapLock.sync {
            _wifiSnapshots.clear()
        }

        hmDeviceSnapLock.sync {
            _baseDeviceSnapshots.clear()
        }
    }

    /**
     * Clear snapshots of [BaseBTDevice] and related [BaseDevice]
     */
    fun clearBLEOnly() {
        // handle and notify related HmDevices first
        _bleSnapshots.forEach { bleDevice ->
            onDeviceRemoved(bleDevice).notifyOffline(byBleDevice = true)
        }

        notifyUpdate()
        // clear caches
        bleSnapLock.sync {
            _bleSnapshots.clear()
        }
    }

    /**
     * Clear snapshots of [DeviceItem] and related [BaseDevice]
     */
    internal fun clearWiFiOnly() {
        // handle and notify related HmDevices first
        onWiFiDevicesNotify(listOf())

        // clear caches
        wifiSnapLock.sync {
            _wifiSnapshots.clear()
        }
    }

    /**
     * Notify when:
     * [ScanCallback.onScanResult] (BLE)
     * [BluetoothProfile.ServiceListener.onServiceConnected] (BR_EDR)
     * [MenuRightFragInstaller.notifyObservers] (WiFi)
     */
    private fun Device.notifyOnlineOrUpdate() {
        DeviceScanner.notifyDeviceOnlineOrUpdate(device = this)
    }

    private fun Device.handleBleAdvChangeByPing() {
        if ((bleDevice as? OneBTDevice)?.isNetworkConnected == true) {
            checkWiFiByPingSnapshots.addIfAbsent(this)
            UUID?.also { WiFiScanner.removeBlackList(uuid = it) }
        } else {
            if (checkWiFiByPingSnapshots.remove(this)) {
                GlobalScope.launch(DISPATCHER_IO) {
                    val available = wifiDevice?.deviceItem?.IP?.let { PingUtil.isAvailableByPing(it) } ?: true
                    // drop off WiFi device.
                    if (!available) { UUID?.also { WiFiScanner.insertBlackList(uuid = it, deviceItem = wifiDevice?.deviceItem) } }
                }
            }
        }
    }

    private fun List<Device>.notifyOffline(byBleDevice: Boolean? = false) =
        forEach { hmDevice ->
            DeviceScanner.notifyDeviceOffline(device = hmDevice)
            if (true == byBleDevice) {
                checkWiFiByPingSnapshots.remove(hmDevice)
                hmDevice.UUID?.also { WiFiScanner.removeBlackList(uuid = it) }
            }
        }

    private fun notifyUpdate() {
        DeviceScanner.notifyDevicesUpdate(devices = baseDevices.toList())
    }

    /**
     * # Update [PortableBTDevice.macAddress] by [BluetoothDevice.getAddress] in [_bleSnapshots]
     * # Update [BaseBTDevice.isSPPConnected] status(not only connected but also disconnected if not in [bluetoothDevices])
     * # Filter devices in [_bleSnapshots] which only support [EnumDeviceProtocol.PROTOCOL_GATT_BR_EDR].
     * # Remove disconnected ones from [_bleSnapshots] and [_baseDeviceSnapshots] if necessary.
     *
     * @return List of device which only support [EnumDeviceProtocol.PROTOCOL_GATT_BR_EDR] and can be regarded as disconnected.
     */
    @SuppressLint("MissingPermission")
    fun onSPPDevicesNotify(
        bluetoothDevices: List<BluetoothDevice>?
    ): SppDeviceContainer {
        return bleSnapLock.sync {
            val onlines = mutableListOf<BaseBTDevice<*, *>>()
            val offlines = mutableListOf<BaseBTDevice<*, *>>()

            _bleSnapshots.forEach { bleDevice ->
                val search = bluetoothDevices?.firstOrNull { bluetoothDevice ->
                    bleDevice.matchMacAddress(bluetoothDevice.address)
                }

                if (null != search && !bleDevice.isA2DPConnected) {
                    Logger.d(TAG, "onSPPDevicesNotify() >>> [${bleDevice.UUID}] update as online state. Mac[${search.address}]")
                    bleDevice.isSPPConnected = true
                    bleDevice.deviceNameCRC = Tools.fullCRC
                    bleDevice.macAddress = search.address
                    onlines.add(bleDevice)
                } else if (null == search && bleDevice.isA2DPConnected) {
                    Logger.d(TAG, "onSPPDevicesNotify() >>> [${bleDevice.UUID}] update as offline state.")
                    bleDevice.isSPPConnected = false
                    bleDevice.deviceNameCRC = null
                    offlines.add(bleDevice)
                }
            }

            SppDeviceContainer(onlines = onlines, offlines = offlines)
        }
    }

    /**
     * Linkplay SDK may push expired device again in short time after that one set factory reset.
     * Use this [blackList] to workaround this case.
     */
    private val blackList = CopyOnWriteArrayList<BlackListBean>()
    private fun isOnBlackList(deviceItem: DeviceItem): Boolean = blackList.any { blackListBean ->
        blackListBean.uuid == deviceItem.uuid && blackListBean.isFrozen()
    }

    private fun insertBlackList(deviceItem: DeviceItem?) {
        val uuid = deviceItem?.uuid
        if (uuid.isNullOrBlank()) {
            return
        }

        val bean = BlackListBean(uuid = uuid)
        Logger.d(TAG, "insertBlackList() >>> uuid[$uuid] ts[${bean.timeStamp}]")
        blackList.add(bean)
    }

    private data class BlackListBean(
        val uuid: String,
        val timeStamp: Long = System.currentTimeMillis()
    ) {

        fun isFrozen(): Boolean {
            return abs(System.currentTimeMillis() - timeStamp) < FROZEN_THRESHOLD_MS
        }
    }

    private const val TAG = "DeviceStore"

    // It cost almost two minutes for Linkplay SDK to update correct online devices after factory reset.
    private const val FROZEN_THRESHOLD_MS = 2 * 60 * 1000L
}