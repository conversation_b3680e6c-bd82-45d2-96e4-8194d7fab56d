package com.harman.discover.bean.bt

import com.harman.connect.BaseBrEdrSession
import com.harman.connect.BaseGattSession
import com.harman.connect.DefaultBrEdrSession
import com.harman.connect.PartyBandGattSession
import com.harman.discover.info.AuracastSupport
import com.harman.discover.info.DefaultRole
import com.harman.discover.info.EnumPlatform
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.toCRC
import com.harman.v5protocol.bean.devinfofeat.V5DeviceOOBE
import com.harman.v5protocol.discover.BatteryInfo
import com.harman.v5protocol.discover.DeviceMiscInfo
import com.harman.v5protocol.discover.PartyInfo
import com.harman.v5protocol.discover.PartyMethodInfo

/**
 * @Description device object for PartyBand
 * <AUTHOR>
 * @Time 2024/11/21
 */
class PartyBandBTDevice(
    var macAddressCrc: String,
    var firmwareVersion: String,
    var batteryInfo: BatteryInfo,
    var partyMethodInfo: PartyMethodInfo,
    var partyInfo: PartyInfo,
    var miscInfo: DeviceMiscInfo,
    pid: String,
    colorId: String,
    vendorId: String,
    deviceName: String,
    bleAddress: String,
    macAddress: String? = null,
    var srcName1: String? = null,
    var srcName2: String? = null,
    var serialNumber: String? = null,
    var supportAuracastBroadcastQuality: Boolean = false,
    var sMusician: V5DeviceOOBE? = null,
) : BaseBTDevice<DefaultRole, PartyInfo.Role>(
    pid = pid,
    colorID = colorId,
    platform = EnumPlatform.AMLOGIC,
    vendorID = vendorId,
    macAddress = macAddress,
    deviceName = deviceName,
    bleAddress = bleAddress,
    deviceNameCRC = srcName1,
    secondDeviceNameCRC = srcName2,
    auraCastSupport = if (partyMethodInfo.isAuracastSupport) AuracastSupport.SUPPORT.value else AuracastSupport.DONT_SUPPORT.value
) {
    override val isMute = false
    override val role = DefaultRole.DEFAULT
    override val auraCastRole
        get() = partyInfo.role
    override val isAuraCastOn
        get() = partyInfo.isPartyOn
    override val isAuraCastDisabled = false
    override val isBroadcaster
        get() = partyInfo.role == PartyInfo.Role.Primary
    override val isReceiver
        get() = partyInfo.role == PartyInfo.Role.Secondary
    override val UUID = "BAND_BOX$macAddressCrc"

    override fun matchMacAddress(btDeviceMacAddress: String?): Boolean {
        return Tools.sameAddress(first = btDeviceMacAddress?.toCRC(), second = macAddressCrc)
    }

    override fun buildGattSession(bleAddress: String?, listenerProxy: GattListenerProxy): BaseGattSession<*, DefaultRole, PartyInfo.Role> {
        return PartyBandGattSession(
            this,
            GattListenerProxy(
                ::_gattListeners,
                ::registerGattListener,
                ::unregisterGattListener
            ),
        )
    }

    override fun buildBrEdrSession(listenerProxy: GattListenerProxy): BaseBrEdrSession<*, DefaultRole, PartyInfo.Role> {
        return DefaultBrEdrSession(
            device = this,
            gattListenerProxy = GattListenerProxy(
                ::_gattListeners,
                ::registerGattListener,
                ::unregisterGattListener
            )
        )
    }


    override fun clone(device: BaseBTDevice<*, *>) {
        super.clone(device)
        if (device !is PartyBandBTDevice) {
            return
        }
        this.macAddressCrc = device.macAddressCrc
        this.firmwareVersion = device.firmwareVersion
        if (!isGattConnected) {
            this.batteryInfo = device.batteryInfo
        }
        this.srcName1 = device.srcName1
        this.srcName2 = device.srcName2
        this.partyMethodInfo = device.partyMethodInfo
        this.partyInfo = device.partyInfo
    }

    override fun sameContent(device: BaseBTDevice<*, *>): Boolean {
        return device is PartyBandBTDevice &&
                super.sameContent(device) &&
                this.macAddressCrc == device.macAddressCrc &&
                this.firmwareVersion == device.firmwareVersion &&
                this.batteryInfo == device.batteryInfo &&
                this.srcName1 == device.srcName1 &&
                this.srcName2 == device.srcName2 &&
                this.partyMethodInfo == device.partyMethodInfo &&
                this.partyInfo == device.partyInfo
    }
}