package com.harman.discover.parser

import android.annotation.SuppressLint
import android.bluetooth.le.ScanResult
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.discover.info.DefaultRole
import com.harman.discover.info.EnumDeviceProtocol
import com.harman.log.Logger
import com.harman.v5protocol.discover.DeviceMiscInfo
import com.harman.v5protocol.discover.PartyInfo
import com.harman.v5protocol.discover.V5BLELegacyAdvParseMixin

/**
 * @Description partyband device ble parser , use v5 ble discover protocol
 * <AUTHOR>
 * @Time 2024/11/21
 */
@SuppressLint("MissingPermission")
object PartyBandBLEAdvParser : IBLEAdvParser<DefaultRole, PartyInfo.Role>(), V5BLELegacyAdvParseMixin {
    private const val TAG = "PartyBandBLEAdvParser"
    override val vendorID: String = "0ECB"

    override fun ableToParse(scanResult: ScanResult?): Result {
        return if (v5EnableParse(scanResult)) scanResult.toEnable() else scanResult.toDisable()
    }

    override fun parse(result: Result): BaseBTDevice<DefaultRole, PartyInfo.Role>? {
        val parseRet = v5Parse(result.scanResult!!) ?: return null
        PartyBandDevice.Model.entries.find { it.pid == parseRet.pid } ?: run {
            return null
        }
        try {
            return PartyBandBTDevice(
                macAddressCrc = parseRet.btMacAddressCrc!!,
                firmwareVersion = parseRet.firmwareVersion5!!,
                batteryInfo = parseRet.batteryInfo!!,
                partyMethodInfo = parseRet.partyMethodInfo!!,
                partyInfo = parseRet.partyInfo!!,
                miscInfo = parseRet.deviceMiscInfo!!,//?: DeviceMiscInfo(true),
                pid = parseRet.pid!!,
                colorId = parseRet.colorId!!,
                vendorId = vendorID,
                deviceName = result.scanResult.scanRecord?.deviceName!!,
                bleAddress = result.scanResult.device?.address!!,
                srcName1 = parseRet.srcName1Crc,
                srcName2 = parseRet.srcName2Crc,
            ).apply {
                var protocolFlags = EnumDeviceProtocol.PROTOCOL_BLE.value
                if (miscInfo?.isSupportBrEdr == true) {
                    protocolFlags = protocolFlags.or(EnumDeviceProtocol.PROTOCOL_GATT_BR_EDR.value)
                }
                this.protocolFlags = protocolFlags
            }
        } catch (e: Exception) {
            Logger.e(TAG, "parse PartyBand device failed: ${e.stackTraceToString()}")
            return null
        }
    }
}

