package com.harman.discover

import com.harman.command.one.bean.EnumSoundscapeV2State
import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.DeviceInfo
import com.harman.command.one.bean.DiagnosisStatusResponse
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.EnumSoundscapeV2Action
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceToneResponse
import com.harman.command.one.bean.LightInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.SoundscapeV2Item
import com.harman.command.one.bean.StreamingStatus
import com.harman.discover.bean.WiFiDevice

/**
 * Created by gerrardzhang on 2024/4/12.
 *
 * Cause both [OneBTDevice] and [WiFiDevice] store same type of params and they all been exposed
 * to [OneDevice]. We defined this interface which contains all params getter that they should be
 * obeyed if some params been added/ removed/ modified later.
 */

interface OneBusinessParams : IMediaParam {

    val regionCode: String?

    val groupInfoExt: GroupInfoExt?

    val featSupportExt: FeatureSupportExt?

    val eqExt: EQExt?

    val batteryStatusExt: BatteryStatusExt?

    val calibrationExt: CalibrationExt?

    val smartBtnConfigExt: GetSmartBtnConfigExt?

    val soundscapeV2ListExt: SoundscapeV2ListExt?

    val controlSoundscapeV2Ext: ControlSoundscapeV2Ext?

    val deviceInfoExt: DeviceInfoExt?

    val voiceLanguageResponseExt: VoiceLanguageResponseExt?

    val startVoiceToneResponseExt: VoiceToneResponseExt?

    val endVoiceToneResponseExt: VoiceToneResponseExt?

    /**
     * Google Cast or Assistant
     */
    val c4aPermissionStatusExt: C4aPermissionStatusExt?

    /**
     * Amazon Alexa
     */
    val alexaCBLStatusExt: AlexaCBLStatusExt?

    /**
     * Amazon Alexa VA
     */
    val lwaStateResponseExt: LwaStateResponseExt?

    val chromecastOptExt: ChromeCastOptInExt?

    val rearSpeakerVolumeExt: RearSpeakerVolumeExt?

    val diagnosisStatusExt: DiagnosisStatusExt?

    val autoPowerOffExt: AutoPowerOffExt?

    val bluetoothConfigExt: BluetoothConfigExt?

    val feedbackToneConfigExt: FeedbackToneConfigExt?

    val batterySavingStatus: BatterySavingStatusExt?

    val eqListExt: EQListExt?

    val streamingStatusExt: StreamingStatusExt?

    val sleepTimerExt: SleepTimerExt?

    val groupParameterExt: GroupParameterExt?

    val prodSettingStatusExt: ProdSettingStatusExt?

    val lightInfoExt: LightInfoExt?

    val getGroupDevicesFlagExt: GetGroupDevicesFlagExt?
}

/**
 * Interface which allow params had binding updated timestamps
 * which store in [WiFiDevice] and [OneBTDevice].
 */
abstract class ParamExt {

    var timeStamp: Long = System.currentTimeMillis()
        protected set

    fun refreshTimeStamp() {
        timeStamp = System.currentTimeMillis()
    }
}

data class FeatureSupportExt(
    val featSupport: FeatureSupport?
) : ParamExt() {

    val isPresetSupport: Boolean
        get() = featSupport?.userEQ?.isPresetSupport ?: false

    val isMediaPlayerSupport: Boolean
        get() = featSupport?.isMediaPlayerSupport() ?: false

}

data class GetSmartBtnConfigExt(
    val config: SmartBtnConfig
) : ParamExt()

data class SoundscapeV2ListExt(
    val soundscapeV2List: List<SoundscapeV2Item>?
) : ParamExt()

data class ControlSoundscapeV2Ext(
    var actionId: Int? = null,
    var soundscapeId: Int? = null,
    var state: String? = null
) : ParamExt() {

    fun playing(): Boolean = EnumSoundscapeV2State.PLAYING.value == state ||
            EnumSoundscapeV2Action.isPlaying(actionId)

    override fun toString(): String {
        return "actionId[$actionId] soundscapeId[$soundscapeId] state[$state]"
    }
}

data class GroupInfoExt(
    val groupInfo: GetGroupInfo
) : ParamExt()

data class DeviceInfoExt(
    val deviceInfo: DeviceInfo
) : ParamExt()

data class VoiceLanguageResponseExt(
    val voiceLanguageResponse: GetVoiceLanguageResponse
) : ParamExt()
data class VoiceToneResponseExt(
    val voiceToneResponse: GetVoiceToneResponse
) : ParamExt()

data class C4aPermissionStatusExt(
    val status: C4aPermissionStatusResponse
) : ParamExt()

data class AlexaCBLStatusExt(
    val status: AlexaCBLStatusResponse
) : ParamExt()

data class LwaStateResponseExt(
    val status: LwaStateResponse
) : ParamExt()

data class ChromeCastOptInExt(
    val optIn: ChromeCastOpt
) : ParamExt()

data class CalibrationExt(
    val calibration: Calibration
) : ParamExt()

data class RearSpeakerVolumeExt(
    val data: RearSpeakerVolumeResponse
) : ParamExt()

data class DiagnosisStatusExt(
    val data: DiagnosisStatusResponse
) : ParamExt()

data class AutoPowerOffExt(
    val secs: Int
) : ParamExt()

data class BluetoothConfigExt(
    val config: EnumBluetoothConfig
) : ParamExt()

data class FeedbackToneConfigExt(
    val config: EnumFeedbackToneConfig
) : ParamExt()

data class BatterySavingStatusExt(
    val status: EnumBatterySavingStatus
) : ParamExt()

data class EQListExt(
    val data: EQListResponse
) : ParamExt()

data class EQExt(
    val data: EQResponse
) : ParamExt()

data class BatteryStatusExt(
    val data: BatteryStatusResponse
) : ParamExt()

data class StreamingStatusExt(
    val status: StreamingStatus
) : ParamExt()

data class SleepTimerExt(
    val secs: Int
) : ParamExt()

data class GroupParameterExt(
    val groupParameter: GetGroupParameterRsp
) : ParamExt()

data class ProdSettingStatusExt(
    val prodSettingStatus: ProdSettingResponse<String, String>
) : ParamExt()

data class LightInfoExt(
    val lightInfo: LightInfo
) : ParamExt()

data class GetGroupDevicesFlagExt(
    val groupDevicesFlag: GetGroupDevicesFlagRsp
) : ParamExt()