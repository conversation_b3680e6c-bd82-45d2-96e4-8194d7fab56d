package com.harman.discover.parser

import android.annotation.SuppressLint
import android.bluetooth.le.ScanResult
import android.os.ParcelUuid
import com.harman.connect.isHorizonCategory
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.info.EnumDeviceProtocol
import com.harman.discover.info.GeneralRole
import com.harman.discover.info.HotelMode
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.safetySubString
import com.harman.discover.util.Tools.safetySubStringLittleEndian
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/1/9.
 *
 *  refer to [SpeakerBleBroadCastParser.parseBleScanRecordData] in Partybox Project
 */
object PartyBoxBLEAdvParser : IBLEAdvParser<GeneralRole, GeneralRole>() {

    override val vendorID: String = "0ECB"

    private val HARMAN_SERVICE_DATA_PRO = ParcelUuid.fromString("0000fddf-0000-1000-8000-00805f9b34fb")

    override fun ableToParse(scanResult: ScanResult?): Result {
        val iVendorID = vendorID.toIntOrNull(16) ?: run {
            return scanResult.toDisable()
        }

        val manuBytes = scanResult?.scanRecord?.getManufacturerSpecificData(iVendorID)
        if (null != manuBytes && manuBytes.isNotEmpty() && isSupport(manuBytes)) {
            return scanResult.toEnable(payload = manuBytes)
        }

        // protocol 4.12 get scan result from service data
        val serviceBytes = scanResult?.scanRecord?.getServiceData(HARMAN_SERVICE_DATA_PRO)
        if (null != serviceBytes && serviceBytes.isNotEmpty() && isSupport(serviceBytes)) {
            return scanResult.toEnable(payload = serviceBytes)
        }

        return scanResult.toDisable()
    }

    override fun isSupport(bytes: ByteArray?): Boolean {
        return super.isSupport(bytes) && !isHotelMode(bytes)
    }

    private fun isHotelMode(bytes: ByteArray?): Boolean {
        val hex: String = HexUtil.encodeHexStr(bytes)
        if (hex.isNullOrBlank()) {
            return false
        }

        val macAddress = hex.safetySubString(22, 34)?.formatMac()

        // Pid and platform
        var hotelMode: Int? = HotelMode.NORMAL_MODE.value

        hex.safetySubString(34, 4) { strAuracast ->
            val hexAuracast = strAuracast.toIntOrNull(16) ?: return@safetySubString
            val strBytesAuracast = HexUtil.bytes2BinStr(byteArrayOf(hexAuracast.toByte()))

            strBytesAuracast.safetySubString(3, 4) { strSpotify ->
                hotelMode = strSpotify.toIntOrNull(2)
            }
        }

        Logger.d(TAG, "isHotelMode() >>> MAC[$macAddress] hotelMode[$hotelMode]")
        return HotelMode.HOTEL_MODE.value == hotelMode
    }

    @SuppressLint("MissingPermission")
    override fun parse(result: Result): PartyBoxBTDevice? {
        val bluetoothDevice = result.scanResult?.device ?: run {
            return null
        }

        val bytes = result.payload ?: run {
            return null
        }

        val hex: String = HexUtil.encodeHexStr(bytes)
        if (hex.isNullOrBlank()) {
            return null
        }

        val builder = PartyBoxBTDevice.Builder(
            vendorID = vendorID,
            macAddress = hex.safetySubString(22, 34)?.formatMac(),
            bleAddress = bluetoothDevice.address
        )

        // Device Name
        builder.deviceName = result.scanResult.scanRecord?.deviceName

        // Pid and platform
        var start = 0
        var end = start + 4
        hex.safetySubStringLittleEndian(start, end) { pid ->
            builder.pid = pid
            builder.platform = mapPlatform(pid)
        }

        // Mid
        start = end
        end = start + 2
        hex.safetySubString(start, end) { sub ->
            builder.colorID = sub
        }

        // Sync & Role Info
        // -> Role and Connectable/Non-connectable bit
        // Apple CP IC for BLE OTA support / Not Supported
        start = end
        end = start + 2
        hex.safetySubString(start, end) { syncRoleInfo ->
            val roleData = syncRoleInfo.toIntOrNull(16)
            roleData ?: return@safetySubString

            val roleAndConnectable: String = HexUtil.bytes2BinStr(byteArrayOf(roleData.toByte()))
            if (!builder.pid.isHorizonCategory()) {
                // Horizon device didn't impl this flag
                roleAndConnectable.safetySubString(2, 3) { strMute ->
                    builder.mute = strMute.toIntOrNull(2)
                }
            }

            roleAndConnectable.safetySubString(5, 6) { strConnectable ->
                builder.connectable = strConnectable.toIntOrNull(2)
            }

            roleAndConnectable.safetySubString(6, 8) { strRole ->
                val role = strRole.toIntOrNull(2)
                builder.role = role
                builder.auraCastRole = role
            }
        }

        // Battery
        start = end
        end = start + 2
        hex.safetySubString(start, end) { batteryHex ->
            Tools.parseBatteryInfo(builder = builder, batteryHex = batteryHex)
        }

        // CRC source name(SrcName_1)
        start = end
        end = start + 4
        hex.safetySubStringLittleEndian(start, end) { deviceNameCRC ->
            builder.deviceNameCRC = deviceNameCRC
        }

        //Volume and TWS mode info
        start = end
        end = start + 2
        hex.safetySubString(start, end) { strVolAndMode ->
            val hexVolAndMode = strVolAndMode.toIntOrNull(16) ?: return@safetySubString
            val modeAndVolume: String = HexUtil.bytes2BinStr(byteArrayOf(hexVolAndMode.toByte()))
            // @Deprecated
            /*modeAndVolume.safetySubString(0, 6) { strVol ->
                builder.volume = strVol.toIntOrNull(2)
            }*/

            modeAndVolume.safetySubString(6, 8) { strMode ->
                builder.partyConnectStatus = strMode.toIntOrNull(2)  //partyConnectstatus in the broadcast packet is unstable and inaccurate  https://jira.harman.com/jira/browse/HOP-30847
            }
        }

        //BT Connection Info & BLE standby support
        start = end
        end = start + 2
        hex.safetySubString(start, end) { strBtConnectInfo ->
            val btConnectInfoData = strBtConnectInfo.toIntOrNull(16) ?: return@safetySubString
            val btConnectInfoString: String = HexUtil.bytes2BinStr(byteArrayOf(btConnectInfoData.toByte()))

            btConnectInfoString.safetySubString(5, 6) { strBLEStandbySupport ->
                builder.bleStandbySupport = strBLEStandbySupport.toIntOrNull(2)
            }

            btConnectInfoString.safetySubString(6, 8) { strConnectInfo ->
                builder.btConnectionInfo = strConnectInfo.toIntOrNull(2)
            }
        }

        //second CRC
        start = end
        end = start + 4
        hex.safetySubStringLittleEndian(start, end) { strSecondCRC ->
            builder.secondDeviceNameCRC = strSecondCRC
        }

        //Device Mac address - 6 bytes
        start = end
        end = start + 12
        /*hex.safetySubString(start, end) { strMacAddr ->
            builder.macAddress = strMacAddr.formatMac()
        }*/

        //Mic Connection Info
        start = end
        end = start + 2
        hex.safetySubString(start, end) { strHexMicCtrl ->
            val hexMicCtrl = strHexMicCtrl.toIntOrNull(16) ?: return@safetySubString
            val strBytesMicCtrl = HexUtil.bytes2BinStr(byteArrayOf(hexMicCtrl.toByte()))
            strBytesMicCtrl.safetySubString(6, 8) { strBitMicCtr ->
                builder.micConnectionInfo = strBitMicCtr.toIntOrNull(2)
            }
        }

        /*
         * Auracast Status - 1 byte
         * Parse Auracast status added in v2.12
         */
        start = end
        end = start + 2
        hex.safetySubString(start, end) { strAuracast ->
            val hexAuracast = strAuracast.toIntOrNull(16) ?: return@safetySubString
            val strBytesAuracast = HexUtil.bytes2BinStr(byteArrayOf(hexAuracast.toByte()))

            strBytesAuracast.safetySubString(7, 8) { strAuracastSupport ->
                builder.auraCastSupport = strAuracastSupport.toIntOrNull(2)
            }

            strBytesAuracast.safetySubString(6, 7) { strAuracastStatus ->
                builder.auraCastStatus = strAuracastStatus.toIntOrNull(2)
            }

            strBytesAuracast.safetySubString(5, 6) { strSpotify ->
                builder.spotifyQuickAccess = strSpotify.toIntOrNull(2)
            }

            strBytesAuracast.safetySubString(4, 5) { strSpotify ->
                builder.supportGattEdr = strSpotify.toIntOrNull(2)
            }

            strBytesAuracast.safetySubString(3, 4) { strSpotify ->
                builder.hotelMode = strSpotify.toIntOrNull(2)
            }

            strBytesAuracast.safetySubString(2, 3) { strSpotify ->
                builder.supportHotelMode = strSpotify.toIntOrNull(2)
            }

            strBytesAuracast.safetySubString(1, 2) { strSpotify ->
                builder.isAuraCastDisabled = strSpotify.toIntOrNull(2)
            }
        }

        /*
         * TWS Stereo info - 1st byte of full group id(4 bytes)
         * Parse Auracast status added in v2.12
         */
        start = end
        end = start + 2
        hex.safetySubString(start, end) { hexGroupId ->
            if (hexGroupId.any { c -> c != '0' }) {
                builder.groupId = hexGroupId
            }
        }

        val device = builder.build()

        var protocolFlags = EnumDeviceProtocol.PROTOCOL_BLE.value
        if (device.supportGattOverBrEdr) {
            protocolFlags = protocolFlags.or(EnumDeviceProtocol.PROTOCOL_GATT_BR_EDR.value)
        }
        device.protocolFlags = protocolFlags

        Logger.d(TAG, "parse() >>> $device")
        Logger.d(TAG, "parse() >>> UUID[${device.UUID}] deviceName[${device.deviceName}] $hex")

        return device
    }

    private fun String.formatMac(): String? = if (length < 12) {
        null
    } else {
        StringBuilder()
            .append(substring(0, 2)).append(":")
            .append(substring(2, 4)).append(":")
            .append(substring(4, 6)).append(":")
            .append(substring(6, 8)).append(":")
            .append(substring(8, 10)).append(":")
            .append(substring(10, 12))
            .toString().uppercase()
    }

    private const val TAG = "PartyBoxBLEAdvParser"
}
