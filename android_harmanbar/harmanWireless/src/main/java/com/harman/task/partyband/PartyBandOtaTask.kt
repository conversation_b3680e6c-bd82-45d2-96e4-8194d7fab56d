package com.harman.task.partyband

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbDeviceConnection
import android.hardware.usb.UsbManager
import android.text.TextUtils
import androidx.annotation.AnyThread
import androidx.annotation.UiThread
import androidx.annotation.WorkerThread
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.harman.connect.syncGattConnectWithTimeout
import com.harman.discover.BLEScanner
import com.harman.v5protocol.bean.V5OtaNotification
import com.harman.v5protocol.V5Package
import com.harman.v5protocol.command.V5SendOtaDataCommand
import com.harman.v5protocol.command.V5StartOtaCommand
import com.harman.v5protocol.command.V5StopOtaCommand
import com.harman.discover.DeviceScanner
import com.harman.discover.DeviceStore
import com.harman.discover.EnumScanPerformance
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBandDevice
import com.harman.discover.util.Tools.isVersionEqual
import com.harman.libusb.LibUsb
import com.harman.log.Logger
import com.harman.util.Md5Util
import com.harman.v5protocol.bean.devinfofeat.V5PrepareOtaPath
import com.harman.wireless.BuildConfig
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.impl.runOnUiThread
import com.jbl.one.configuration.model.Firmware
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.RandomAccessFile
import java.net.HttpURLConnection
import java.net.URL
import java.nio.ByteBuffer
import kotlin.math.ceil

/**
 * @Description Implement the OTA process for PartyBand
 * <AUTHOR>
 * @Time 2024/12/13
 */
@OptIn(DelicateCoroutinesApi::class)
class PartyBandOtaTask(
    private var device: PartyBandDevice,
    private val firmware: Firmware?,
    private val isCircleUpdate: Boolean,
    private val observer: PartyBandOtaObserver,
    private val offlineOtaFilePath: String? = null,
) : CoroutineScope by CoroutineScope(Dispatchers.Main) {
    companion object {
        private const val USB_INTERFACE_ID = 1
        private const val USB_INTERFACE_ALTERNATE_SETTING = 1
        private const val USB_ENDPOINT_IN_ADDRESS = 130
        private const val USB_ENDPOINT_OUT_ADDRESS = 2
        private const val MTU_SIZE = 1512
        private const val MAX_OTA_BINARY_DATA_SIZE = 1500
        private const val TRANSFER_TIMEOUT = 1000L
        private const val INSTALL_M_TIMEOUT = 6 * 60 * 1000L
        private const val INSTALL_S_TIMEOUT = 6 * 60 * 1000L
        private const val TIME_DELAY_START_OTA_RESPONSE = 2 * 1000L
        private const val TIMEOUT_PREPARE_OTA = 15 * 1000L

        private const val TAG = "PartyBandOtaTask"
    }

    private val actionUsbPermission = "${Utils.getApp().packageName}.USB_PERMISSION"
    private var usbPermissionReqCompleter: CompletableDeferred<Unit>? = null
    private val usbPermissionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            usbPermissionReqCompleter?.complete(Unit)
        }
    }
    private val partybandOtaCtx by lazy { newSingleThreadContext("partybandOtaCtx_${device.UUID}") }
    private val um by lazy { Utils.getApp().getSystemService(UsbManager::class.java) }
    private var partyBandUsbDevice: UsbDevice? = null
    private var conn: UsbDeviceConnection? = null

    /** ota file stream **/
    private var fileStream: RandomAccessFile? = null

    /** current ota process state */
    @Volatile
    private var state = PartyBandOtaState.Idle

    /** fake installation progress job */
    private var installFakePercentageUpdateJob: Job? = null
    private var installCheckCompleter: CompletableDeferred<Boolean>? = null
    private val installScanObserver by lazy {
        object : IHmDeviceObserver {
            override fun onDeviceOnlineOrUpdate(device: Device) {
                super.onDeviceOnlineOrUpdate(device)
                (device as? PartyBandDevice)?.also {
                    if (it.UUID == <EMAIL>) {
                        if (it.firmwareVersion.isVersionEqual(firmware?.version)) {
                            //Assign a new device to the 'device variable' in preparation for reconnection
                            <EMAIL> = it
                            finishInstallCheck()
                            installCheckCompleter?.complete(true)
                        } else {
                            Logger.d(TAG, "receive device broadcast version: ${it.firmwareVersion}")
                        }
                    }
                }
            }
        }
    }

    /** If you call [startFlow], you will get a job, which will be canceled when an abnormal situation such as disconnection of the device occurs */
    private var startFlowJob: Job? = null
    private var usbDeviceAttachedCompleter: CompletableDeferred<Unit>? = null
    private val usbDeviceReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                        val isCurrentUsbDeviceDetached = partyBandUsbDevice?.let {
                            it.deviceId == (intent.getParcelableExtra(UsbManager.EXTRA_DEVICE) as? UsbDevice)?.deviceId
                        } ?: false
                        if (isCurrentUsbDeviceDetached) {
                            //The device is disconnected from USB
                            onDeviceDetached()
                        }
                    }

                    UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                        usbDeviceAttachedCompleter?.complete(Unit)
                    }
                }
            }
        }
    }


    init {
        //The registered device is disconnected and listens to the broadcast
        Utils.getApp().registerReceiver(usbDeviceReceiver, IntentFilter().apply {
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        })
        //Register a USB device with permission to listen to broadcasts
        Utils.getApp().registerReceiver(usbPermissionReceiver, IntentFilter(actionUsbPermission), Context.RECEIVER_EXPORTED)
        BLEScanner.startScan(Utils.getApp(), EnumScanPerformance.HIGH)
    }

    /**
     * start ota flow
     */
    @AnyThread
    fun startFlow() {
        Logger.e(TAG, "start ota flow,the new firmware is: ${firmware?.version}, device version is: ${device.firmwareVersion}")
        if (state != PartyBandOtaState.Idle) {
            Logger.w(TAG, "The OTA process is being executed")
            return
        }
        startFlowJob = launch {
            changeState(PartyBandOtaState.BleConnChecking)
            val connected = checkBleConnection()
            if (!connected) {
                Logger.e(TAG, "ota failed,ble connect failed")
                changeState(PartyBandOtaState.Idle, PartyBandOtaError.BleConnctFail)
                return@launch
            }
            changeState(PartyBandOtaState.ModeSwitching)
            //If the device has been upgraded but does not connect to BLE successfully, skip the following processes
            if (isCircleUpdate || !TextUtils.equals(device.firmwareVersion, firmware?.version)) {
                try {
                    val isIapMode = device.getDevInfoFeat<V5PrepareOtaPath>()!!.enable
                    if (!isIapMode) {
                        //Put the device into OTA mode
                        device.asyncSetDevInfoFeat(V5PrepareOtaPath(true))
                        val prepareRet = device.awaitDevInfo<V5PrepareOtaPath>(TIMEOUT_PREPARE_OTA)?.enable ?: false
                        if (!prepareRet) {
                            if (BuildConfig.DEBUG) {
                                ToastUtils.showLong("switch iap failed or device not notify result")
                            }
                            throw Exception("prepare ret is wrong")
                        }
                        if (!findUsbDevice()) {
                            //Here's an optimization.switching the device to OTA mode will uninstall the USB driver, and the USB device will be attached once here
                            withTimeoutOrNull(TIMEOUT_PREPARE_OTA) {
                                usbDeviceAttachedCompleter = CompletableDeferred()
                                usbDeviceAttachedCompleter!!.await()
                            }
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "prepare ota path failed ${e.stackTraceToString()}")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.PrepareOtaPathFail)
                    return@launch
                }

                changeState(PartyBandOtaState.USBPreparing)
                if (!findUsbDevice()) {
                    Logger.e(TAG, "ota failed,Can Not Find Device")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.CanNotFindUsbDevice)
                    return@launch
                }
                if (!checkAndRequestPermission()) {
                    Logger.e(TAG, "ota failed,No usb Permission")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.NoUsbPermission)
                    return@launch
                }
                val openRet = openDevice()
                if (!openRet) {
                    Logger.e(TAG, "ota failed,Open Device Fail")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.OpenUsbDeviceFail)
                    return@launch
                }
                changeState(PartyBandOtaState.NetWorkChecking)
                if (!observer.onDownloadNetworkConfirm.invoke(NetworkUtils.isWifiConnected())) {
                    Logger.e(TAG, "ota failed,download fail Because the network confirm failed")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.ConfirmNetworkFail)
                    return@launch
                }
                changeState(PartyBandOtaState.Downloading)
                val downloadFiledPath = if (offlineOtaFilePath.isNullOrEmpty()) download() else offlineOtaFilePath
                if (downloadFiledPath.isNullOrEmpty()) {
                    Logger.e(TAG, "ota failed,download fail")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.DownloadFail)
                    return@launch
                }
                changeState(PartyBandOtaState.Transferring)
                val transferRet = startTransfer(downloadFiledPath)
                if (!transferRet) {
                    Logger.e(TAG, "ota failed,Transfer Fail")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.TransferFail)
                    return@launch
                }
                //transfer complete, start install
                changeState(PartyBandOtaState.Installing)
                val installRet = startInstall()
                if (!installRet) {
                    Logger.e(TAG, "ota failed,Install Fail")
                    changeState(PartyBandOtaState.Idle, PartyBandOtaError.InstallFail)
                    return@launch
                }
            }
            //install success, start reconnect
            changeState(PartyBandOtaState.BleReconnecting)
            val connectRet = device.syncGattConnectWithTimeout(Utils.getApp())
            if (!connectRet) {
                Logger.e(TAG, "ota failed,reconnect fail")
                changeState(PartyBandOtaState.Idle, PartyBandOtaError.ReconnectFail)
                return@launch
            }
            Logger.d(TAG, "ota success!!!")
            changeState(PartyBandOtaState.Idle)
        }
    }

    /**
     * Close the OTA process,A [PartyBandOtaTask] instance can only be called once,It must be called after not being used.
     * if you want to [startFlow] again after calling [exit], you should instantiate another [PartyBandOtaTask]
     */
    @AnyThread
    fun exit() {
        BLEScanner.stopScan(Utils.getApp(), false)
        launch {
            stopOta()
            conn?.close()
            conn = null
            LibUsb.close(USB_INTERFACE_ID)
            finishInstallCheck()
            fileStream?.close()
            Utils.getApp().unregisterReceiver(usbPermissionReceiver)
            Utils.getApp().unregisterReceiver(usbDeviceReceiver)
            <EMAIL>()
        }
    }

    private suspend fun checkBleConnection(): Boolean {
        return (DeviceStore.find(device.UUID ?: "") as? PartyBandDevice)?.let {
            device = it
            device.syncGattConnectWithTimeout(Utils.getApp())
        } ?: false
    }

    /**
     * Query the target device,This function can be called separately before calling [startFlow]
     * @return true if The target device is discovered
     */
    @UiThread
    @OptIn(ExperimentalStdlibApi::class)
    private fun findUsbDevice(): Boolean {
        val um = Utils.getApp().getSystemService(UsbManager::class.java)
        val deviceList = um.deviceList
        val partyBandUsbDevice = deviceList.toList().find {
            //Since the bottom layer of USB transmission is the PID and vendor ID stored in big-endian order, so the big-endian order is used as HEX
            val pidString = ByteBuffer.allocate(2).putShort(it.second.productId.toShort()).array().toHexString()
            val vendorIdString = ByteBuffer.allocate(2).putShort(it.second.vendorId.toShort()).array().toHexString()
            return@find pidString.equals(device.pid, true) && vendorIdString.equals(device.bleDevice?.vendorID, true)
        }?.second
        this.partyBandUsbDevice = partyBandUsbDevice
        return null != partyBandUsbDevice
    }

    /**
     * Check if the [partyBandUsbDevice] has permissions, and if not, try to request it once,This function can be called separately before calling [startFlow]
     * @return true if [partyBandUsbDevice] has permission
     */
    @UiThread
    suspend fun checkAndRequestPermission(): Boolean {
        if (!um.hasPermission(partyBandUsbDevice)) {
            val permissionIntent = PendingIntent.getBroadcast(
                Utils.getApp(),
                0,
                Intent(actionUsbPermission),
                PendingIntent.FLAG_IMMUTABLE
            )
            //request permission
            um.requestPermission(partyBandUsbDevice, permissionIntent)
            //wait permission result
            usbPermissionReqCompleter = CompletableDeferred()
            usbPermissionReqCompleter!!.await()
        }
        return um.hasPermission(partyBandUsbDevice)
    }

    private suspend fun download(): String? {
        return withContext(Dispatchers.IO) {
            //Immediate notification of 0 progress at a time
            withContext(Dispatchers.Main) {
                currentPercentage = 0
                observer.onDownloadPercentage(currentPercentage, calRemainingMills())
            }
            synchronized(this@PartyBandOtaTask) {
                try {
                    val firmwareDir = File(Utils.getApp().filesDir, "PartyBandFirmware")
                    if (!firmwareDir.exists()) {
                        firmwareDir.mkdir()
                    }
                    val fileName = "${firmware?.md5}_${firmware?.version}.bin"
                    val localOtaFile = File("${firmwareDir.absolutePath}${File.separator}$fileName")
                    if (localOtaFile.exists()) {
                        val localMD5: String = Md5Util.calculateMD5(localOtaFile.absolutePath)
                        // To match md5 of upgrade firmware file and remoteUpdatemodel version
                        if (localMD5.equals(firmware?.md5, ignoreCase = true)) {
                            Logger.i(TAG, "checkMD5() >>> MD5 match")
                            return@withContext localOtaFile.absolutePath
                        }
                        localOtaFile.deleteOnExit()
                    }
                    if (!httpDownload(localOtaFile)) {
                        throw Exception("http download fail")
                    }
                    if (!Md5Util.calculateMD5(localOtaFile.absolutePath).equals(firmware?.md5, ignoreCase = true)) {
                        localOtaFile.deleteOnExit()
                        throw Exception("check md5 fail")
                    }
                    return@withContext localOtaFile.absolutePath
                } catch (e: Exception) {
                    Logger.e(TAG, "download ota exception: ${e.stackTraceToString()}")
                    return@withContext null
                }
            }
        }
    }

    @WorkerThread
    private fun httpDownload(binFile: File): Boolean {
        var httpURLConnection: HttpURLConnection? = null
        var readStream: InputStream? = null
        var writeStream: FileOutputStream? = null
        try {
            val fileUrl = "${AppConfigurationUtils.getOtaRootPath(device.pid!!)}${firmware?.binPath}"
            httpURLConnection = URL(fileUrl).openConnection() as HttpURLConnection
            httpURLConnection.connectTimeout = 15 * 1000
            if (!isActive) {
                return false
            }
            readStream = httpURLConnection.inputStream
            writeStream = FileOutputStream(binFile)
            val totalLength = httpURLConnection.contentLengthLong.toDouble()
            val buffer = ByteArray(10 * 1024)
            var starting: Int
            var writtenLength: Long = 0
            while (readStream.read(buffer).also { starting = it } != -1 && isActive) {
                writeStream.write(buffer, 0, starting)
                writtenLength += starting
                val percentage = ((writtenLength / totalLength) * 100 + 0.5f).toInt()
                if (currentPercentage != percentage) {
                    currentPercentage = percentage
                    runOnUiThread {
                        observer.onDownloadPercentage.invoke(percentage, calRemainingMills())
                    }
                }
            }
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            readStream?.close()
            writeStream?.close()
            httpURLConnection?.disconnect()
        }
    }

    /**
     * If the device disconnects from the USB connection during the OTA process, this method will be called by [usbDeviceReceiver]
     */
    @UiThread
    private fun onDeviceDetached() {
        conn?.close()
        conn = null
        LibUsb.close(USB_INTERFACE_ID)
        fileStream?.close()
        when (state) {
            PartyBandOtaState.Transferring -> {
                startFlowJob?.cancel()
                changeState(PartyBandOtaState.Idle, PartyBandOtaError.TransferFail)
            }

            else -> Unit
        }
    }

    private fun openDevice(): Boolean {
        try {
            if (null != conn) {
                return true
            }
            val conn = um.openDevice(partyBandUsbDevice) ?: throw Exception("open device failed")
            val initRet = LibUsb.init(conn.fileDescriptor, USB_INTERFACE_ID, USB_INTERFACE_ALTERNATE_SETTING)
            if (0 != initRet) {
                throw Exception("LibUsb init failed")
            }
            <EMAIL> = conn
            return true
        } catch (e: Exception) {
            Logger.e(TAG, "openDevice fail ${e.stackTraceToString()}")
            return false
        }
    }

    @OptIn(ExperimentalStdlibApi::class)
    @WorkerThread
    private fun writeWithLibUsb(conn: UsbDeviceConnection, data: ByteArray): Boolean {
        val ret = LibUsb.bulkTransferWrite(conn.fileDescriptor, USB_ENDPOINT_OUT_ADDRESS, data, data.size, TRANSFER_TIMEOUT.toInt())
//            Logger.d(
//                TAG,
//                "writeWithLibUsb >>> data length: ${data.size} ; data: ${
//                    if (data.size > 50) data.take(12).toByteArray().toHexString() else data.toHexString()
//                } ; write length:$ret"
//            )
        return ret >= 0
    }

    @WorkerThread
    private fun readWithLibUsb(conn: UsbDeviceConnection): ByteArray? {
        val array = ByteArray(MTU_SIZE)
        val ret = LibUsb.bulkTransferRead(conn.fileDescriptor, USB_ENDPOINT_IN_ADDRESS, array, array.size, TRANSFER_TIMEOUT.toInt())
        val retArray = if (ret > 0) {
            array.take(ret).toByteArray()
        } else null
//            Logger.d(TAG, "readWithLibUsb >>> ${retArray?.toHexString()} , read length:$ret")
        return retArray
    }

    private suspend fun startTransfer(otaFilePath: String): Boolean {
        return withContext(partybandOtaCtx) {
            try {
                //Immediate notification of 0 progress at a time
                withContext(Dispatchers.Main) {
                    currentPercentage = 0
                    observer.onTransferPercentage(currentPercentage, calRemainingMills())
                }
                //Read out the invalid cache before transfer
                readWithLibUsb(conn!!)
                Logger.d(TAG, "start transfer")
                //write start ota command
                writeWithLibUsb(conn!!, V5StartOtaCommand().toV5Package().toBytes())
                //Send the Start OTA command and then read the status with a delay of 2 seconds
                delay(TIME_DELAY_START_OTA_RESPONSE)
                //read start ota response
                val startNotification = readWithLibUsb(conn!!)!!.let { V5OtaNotification.fromPayload(V5Package.fromDevice(it).payload!!) }
                Logger.d(TAG, "ota transferring, $startNotification")
                //start transfer ota file
                //open file stream
                fileStream?.close()
                fileStream = RandomAccessFile(otaFilePath, "r")
                var offset = startNotification.absoluteOffset
                var lengthToRead = startNotification.lengthToRead
                //loop transfer ota file
                while (isActive) {
                    //seek to the offset location required by the device
                    fileStream!!.seek(offset)
                    val otaBinaryData = ByteArray(lengthToRead)
                    //The binary data of the number of bytes that the device needs to read is read at one time
                    fileStream!!.read(otaBinaryData)
                    val packages = mutableListOf<V5Package>()
                    if (lengthToRead < MAX_OTA_BINARY_DATA_SIZE) {
                        //One pack can be delivered
                        packages.add(V5SendOtaDataCommand(offset, otaBinaryData).also {
//                            Logger.d(
//                                TAG,
//                                "ota transferring put one send data package, offset: ${it.offsetIndex}, size: ${otaBinaryData.size}"
//                            )
                        }.toV5Package())
                    } else {
                        //If a packet can't send all the otaBinaryData, then divide it into multiple V5Packages,
                        // but here it is special and you need to include the offsetIndex,
                        // so need to build a V5SendOtaDataCommand with only one package
                        val buffer = ByteBuffer.wrap(otaBinaryData)
                        val v5PackageCount = ceil(otaBinaryData.size.toFloat() / MAX_OTA_BINARY_DATA_SIZE).toInt()
                        var v5PackageIndex = 0
                        while (buffer.hasRemaining()) {
                            val remaining = buffer.remaining()
//                            Logger.d(
//                                TAG,
//                                "ota transferring buffer remaining $remaining"
//                            )
                            if (remaining >= MAX_OTA_BINARY_DATA_SIZE) {
                                packages.add(V5SendOtaDataCommand(offset, ByteArray(MAX_OTA_BINARY_DATA_SIZE).also { buffer.get(it) }).also {
//                                    Logger.d(
//                                        TAG,
//                                        "ota transferring put one send data package, offset: ${it.offsetIndex} , size: $MAX_OTA_BINARY_DATA_SIZE"
//                                    )
                                }.toV5Package(v5PackageCount, v5PackageIndex))
                                offset += MAX_OTA_BINARY_DATA_SIZE
                            } else {
                                packages.add(V5SendOtaDataCommand(offset, ByteArray(remaining).also { buffer.get(it) }).also {
//                                    Logger.d(
//                                        TAG,
//                                        "ota transferring put one send data package, offset: ${it.offsetIndex},size: $remaining"
//                                    )
                                }.toV5Package(v5PackageCount, v5PackageIndex))
                                offset += remaining
                            }
                            v5PackageIndex++
                        }
                    }
//                    Logger.d(TAG, "ota transferring split package size ${packages.size}")
                    //To improve transmission performance, it is allowed to send multiple packets before initiating an inquiry
                    // Write to the device in multiple installments
                    packages.forEach {
                        it.toBytes().also { bytes ->
//                            Logger.d(TAG, "ota transferring each package size ${bytes.size}")
                            writeWithLibUsb(conn!!, bytes)
                        }
                    }
                    //After sending a round of binary data, ask the device about the situation
                    val sendNotification = readWithLibUsb(conn!!)!!.let { V5OtaNotification.fromPayload(V5Package.fromDevice(it).payload!!) }
//                    Logger.d(TAG, "ota transferring, $sendNotification")
                    if (sendNotification.percentage >= 10000 || 0 == sendNotification.lengthToRead) {
                        return@withContext true
                    } else {
                        //Continue to the next round of binary data transfer
                        offset = sendNotification.absoluteOffset
                        lengthToRead = sendNotification.lengthToRead
                        //notify percentage
                        withContext(Dispatchers.Main) {
                            val percentage = sendNotification.percentage / 100
                            if (percentage != currentPercentage) {
                                currentPercentage = percentage
                                observer.onTransferPercentage(
                                    percentage,
                                    calRemainingMills()
                                )
                            }
                        }
                    }
                }
                return@withContext false
            } catch (e: Exception) {
                Logger.e(TAG, "transfer fail ${e.stackTraceToString()}")
                return@withContext false
            } finally {
                Logger.d(TAG, "finish transfer")
                //send stop ota command
                conn?.also { writeWithLibUsb(it, V5StopOtaCommand().toV5Package().toBytes()) }
                fileStream?.close()
            }
        }
    }

    private suspend fun startInstall(): Boolean {
        installCheckCompleter = CompletableDeferred()
        DeviceScanner.registerObserver(installScanObserver)
        launchInstallPercentageUpdateJob()
        return withTimeoutOrNull(if (device.isTrio()) INSTALL_M_TIMEOUT else INSTALL_S_TIMEOUT) {
            return@withTimeoutOrNull installCheckCompleter!!.await()
        } ?: run {
            finishInstallCheck()
            false
        }
    }

    private fun finishInstallCheck() {
        DeviceScanner.unregisterObserver(installScanObserver)
        installFakePercentageUpdateJob?.cancel()
    }

    private suspend fun stopOta() {
        conn?.also {
            withContext(partybandOtaCtx) {
                writeWithLibUsb(it, V5StopOtaCommand().toV5Package().toBytes())
            }
        }
        device.asyncSetDevInfoFeat(V5PrepareOtaPath(false))
    }

    /**
     * Start a fake job to install progress updates
     */
    private fun launchInstallPercentageUpdateJob() {
        installFakePercentageUpdateJob?.cancel()
        installFakePercentageUpdateJob = launch {
            for (i in 0..100) {
                currentPercentage = i
                observer.onInstallPercentage(i, calRemainingMills())
                delay((if (device.isTrio()) INSTALL_M_TIMEOUT else INSTALL_S_TIMEOUT) / 100)
            }
        }
    }

    @UiThread
    private fun changeState(state: PartyBandOtaState, error: PartyBandOtaError? = null) {
        if (state == this.state) {
            return
        }
        this.state = state
        observer.onStateChanged(state, error)
    }

    /** Record the time of the last update [currentPercentage] */
    private var lastPercentageUpdateTime = 0L

    /** Record the current progress of download or transfer or installation process */
    private var currentPercentage = 0

    /**
     * Estimate the time remaining for the transfer or installation
     * @return -1 if first call
     */
    private fun calRemainingMills(): Long {
        val currentTimeMills = System.currentTimeMillis()
        val eachPercentageDepleteMills = currentTimeMills - lastPercentageUpdateTime
        val totalRemainingMills =
            if (lastPercentageUpdateTime == 0L) -1L else (100 - currentPercentage) * eachPercentageDepleteMills
        lastPercentageUpdateTime = currentTimeMills
        return totalRemainingMills
    }
}

@UiThread
data class PartyBandOtaObserver(
    val onTransferPercentage: (percentage: Int, remainingTimeMs: Long) -> Unit,
    val onInstallPercentage: (percentage: Int, remainingTimeMs: Long) -> Unit,
    val onDownloadPercentage: (percentage: Int, remainingTimeMs: Long) -> Unit,
    val onDownloadNetworkConfirm: suspend (isWifi: Boolean) -> Boolean,
    //if 'error' is null and 'state' is 'idle',Indicates that the OTA was successful
    val onStateChanged: (state: PartyBandOtaState, error: PartyBandOtaError?) -> Unit,
)

enum class PartyBandOtaState {
    Idle,

    BleConnChecking,

    //Wait for the device to turn on the USB channel of the OTA
    ModeSwitching,
    USBPreparing,
    NetWorkChecking,
    Downloading,
    Transferring,
    Installing,
    BleReconnecting,
}

/**
 * @param state What stage the error belongs to,see [PartyBandOtaState]
 */
enum class PartyBandOtaError {
    BleConnctFail,
    PrepareOtaPathFail,
    CanNotFindUsbDevice,
    NoUsbPermission,
    OpenUsbDeviceFail,
    ConfirmNetworkFail,
    DownloadFail,
    TransferFail,
    InstallFail,
    ReconnectFail,
}