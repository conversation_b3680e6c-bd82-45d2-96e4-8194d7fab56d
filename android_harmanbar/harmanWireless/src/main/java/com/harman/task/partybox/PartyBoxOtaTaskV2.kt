package com.harman.task.partybox

import android.bluetooth.BluetoothDevice
import androidx.annotation.CallSuper
import androidx.annotation.WorkerThread
import com.harman.command.common.EnumDfuStatus
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.ota.EnumBtType
import com.harman.command.partybox.gatt.ota.EnumDfuOffsetStatus
import com.harman.command.partybox.gatt.ota.req.ReqDfuApplyCommand
import com.harman.command.partybox.gatt.ota.req.ReqDfuCancelCommand
import com.harman.command.partybox.gatt.ota.req.ReqDfuInfoCommand
import com.harman.command.partybox.gatt.ota.req.ReqDfuStartCommand
import com.harman.command.partybox.gatt.ota.req.ReqSetDfuDataCommand
import com.harman.command.partylight.AckResp
import com.harman.command.partylight.GattPacketFormat
import com.harman.connect.BaseBrEdrSession
import com.harman.connect.EnumConnectionStatus
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.sendCmdSync
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.info.EnumPlatform
import com.harman.discover.util.Tools.chunkVersionStringWithoutDots
import com.harman.discover.util.Tools.isEqual
import com.harman.discover.util.Tools.isGreaterThan
import com.harman.discover.util.Tools.repeatWithTimeout
import com.harman.discover.util.Tools.safetyFileLength
import com.harman.discover.util.Tools.safetySubArray
import com.harman.discover.util.Tools.splitVersionStringWithDots
import com.harman.discover.util.Tools.toBigEndian4Bytes
import com.harman.log.Logger
import com.harman.task.ApplyDfuException
import com.harman.task.CancelDfuFlowException
import com.harman.task.CrcCalculateException
import com.harman.task.DfuInfoFetchException
import com.harman.task.FileLengthException
import com.harman.task.DisconnectException
import com.harman.task.NotiDfuErrorException
import com.harman.task.SetDataException
import com.harman.task.SetDataIOException
import com.harman.task.StartDfuException
import com.harmanbar.ble.utils.HexUtil
import java.io.ByteArrayInputStream
import java.io.FileInputStream
import java.io.IOException
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.max
import com.harman.discover.util.Tools.toBigEndianUInt
import com.harman.discover.util.Tools.toPositiveInt

/**
 * @Description use gatt br/edr
 * <AUTHOR>
 * @Time 2024/11/25
 */
open class PartyBoxBaseOtaTaskV2(
    protected val device: PartyBoxDevice,
) : IPartyBoxTask {

    private val globalErrorListener = GlobalErrorListener()

    private val listeners = CopyOnWriteArrayList<IPartyBoxDfuTaskListener>()

    override fun registerListener(dfuListener: IPartyBoxDfuTaskListener) {
        listeners.addIfAbsent(dfuListener)
    }

    fun unregisterListener(listener: IPartyBoxDfuTaskListener) {
        listeners.remove(listener)
    }

    private fun notify(status: EnumPartyBoxDfuStatus, e: Exception? = null) {
        listeners.forEach { listener ->
            listener.onDfuStatusChanged(status = status, e = e)
        }
    }

    private fun notify(offset: Int, current: Int, total: Int) {
        if (offset < 0 || current < 0 || total < 0 || offset > total ||
            offset + current > total
        ) {
            return
        }

        val progress = (current + offset).toFloat() / total
        listeners.forEach { listener ->
            listener.onProgress(current = current, total = total, progress = progress)
        }
    }

    @Volatile
    var status: EnumPartyBoxDfuStatus = EnumPartyBoxDfuStatus.IDLE
        private set

    val isRunning: Boolean
        get() = when (status) {
            EnumPartyBoxDfuStatus.IDLE,
            EnumPartyBoxDfuStatus.FINISH -> false

            else -> true
        }

    /**
     * Start or resume the Dfu flow, depends on firmware version compare and breakpoint which provided
     * by device.
     *
     * Must be run on the work thread like [DISPATCHER_IO]
     *
     * Combine flows included:
     * [ReqDfuInfoCommand]
     * [ReqDfuStartCommand]
     * [ReqSetDfuDataCommand] * n
     * [ReqDfuApplyCommand]
     *
     * @param remoteFirmwareVersion format as "AA.BB.CC"
     *
     * @return true if the full Dfu flow process success.
     */
    override suspend fun startOrResumeDfuFlow(
        platform: EnumPlatform?,
        remoteFirmwareVersion: String,
        otaFilePath: String
    ): Boolean {
        // used for listening to internal errors while isRunning
        device.registerDeviceListener(globalErrorListener)
        synchronized(status) {
            if (!status.ableStartDfuFlow()) {
                Logger.d(TAG, "startOrResumeDfuFlow() >>> already in Dfu flow[$status]")
                return true
            }

            // ignore return cause force
            syncCheckAndUpdateStatus(EnumPartyBoxDfuStatus.INFO, force = true)
        }

        Logger.i(TAG, "startOrResumeDfuFlow() >>> version[$remoteFirmwareVersion] path:$otaFilePath")

        val dfuInfo = getDfuInfo() ?: run {
            syncIdleStatusWithException(DfuInfoFetchException)
            return false
        }

        if (!syncCheckAndUpdateStatus(EnumPartyBoxDfuStatus.PREPARE)) {
            return false
        }

        Logger.i(TAG, "startOrResumeDfuFlow() >>> Dfu info:$dfuInfo")

        val breakPoint: Int = calibrateBreakpoint(dfuInfo = dfuInfo, version = remoteFirmwareVersion)
        Logger.i(TAG, "startOrResumeDfuFlow() >>> breakPoint: $breakPoint")

        val bpType = breakPoint.breakPoint2BpType()

        val crc = platform?.crc(filePath = otaFilePath)?.toInt()

        if (null == crc) {
            syncIdleStatusWithException(CrcCalculateException)
            return false
        }
        Logger.i(TAG, "startOrResumeDfuFlow() >>> ota file crc[$crc]")

        val fileLen = otaFilePath.safetyFileLength()
        if (fileLen <= 0) {
            syncIdleStatusWithException(FileLengthException)
            return false
        }

        if (!syncCheckAndUpdateStatus(EnumPartyBoxDfuStatus.START)) {
            return false
        }

        Logger.i(TAG, "startOrResumeDfuFlow() >>> crc[$crc] len[$fileLen]")

        if (!startOrResumeDfuFlow(dfuCrc = crc, dfuSize = fileLen, dfuVersion = remoteFirmwareVersion, bpType = bpType.value)) {
            syncIdleStatusWithException(StartDfuException)
            return false
        }

        Logger.i(TAG, "startOrResumeDfuFlow() >>> start Dfu success")
        val result: Boolean = try {
            loopSetData(filePath = otaFilePath, bpType = bpType, offset = breakPoint, fileLen = fileLen)
        } catch (e: IOException) {
            Logger.e(TAG, "startOrResumeDfuFlow() >>> IOException happened in loopSetData:$e")
            syncIdleStatusWithException(SetDataIOException)
            false
        }

        Logger.i(TAG, "startOrResumeDfuFlow() >>> Dfu success: $result")
        return result
    }

    /**
     * Terminal the full flow and release resources if [isRunning].
     * This is a sync function until [cancel] execute finished with a result.
     *
     * @return [cancel] received by device and return ACK and some other commands.
     */
    override suspend fun cancelOta(): Boolean {
        syncIdleStatusWithException(CancelDfuFlowException)

        return repeatWithTimeout(repeatTimes = 0, timeoutMills = TIMEOUT_MILLS) {
            cancel()
        } ?: false
    }

    private fun calibrateBreakpoint(dfuInfo: DfuInfo, version: String): Int =
        if (isRemoteGreater(dfuInfo = dfuInfo, remoteFirmwareVersion = version)) {
            // Trans OTA file from the beginning regardless of breakpoint info from [RspDfuInfoCommand.breakPoint]
            0
        } else if (equalsRemote(dfuInfo = dfuInfo, remoteFirmwareVersion = version)) {
            // Make breakpoint info from [RspDfuInfoCommand.breakPoint] as start line.
            max(dfuInfo.breakPoint ?: 0, 0)
        } else {
            // Remote less than actual
            0
        }

    private fun Int.breakPoint2BpType(): EnumBtType = if (this > 0) {
        EnumBtType.RESUME_FROM_BREAKPOINT
    } else {
        EnumBtType.RESET_BREAKPOINT
    }

    @WorkerThread
    @CallSuper
    @Throws(IOException::class)
    protected open suspend fun loopSetData(filePath: String, bpType: EnumBtType, offset: Int, fileLen: Int): Boolean {
        val offsetIdx = offset / MAX_DATA_PACKET_SIZE +
                if (EnumBtType.RESUME_FROM_BREAKPOINT == bpType) 1 else 0
        var pkgIndex = 0 // always start from index 0.

        val remainingPkgCount = (fileLen - offset) / MAX_DATA_PACKET_SIZE +
                if (0 == fileLen % MAX_DATA_PACKET_SIZE) 0 else 1

        if (!syncCheckAndUpdateStatus(EnumPartyBoxDfuStatus.DATA)) {
            return false
        }

        Logger.i(
            TAG, """
            loopSetData() >>> start pkg.index[$pkgIndex] remaining pkg count[$remainingPkgCount]
            offset[$offset]
            fileLen[$fileLen]
            bpType[${bpType.desc}]
            filePath:$filePath
        """.trimIndent()
        )

        val fileBuffer = ByteArray(fileLen)

        var lastSentIndex = -1

        FileInputStream(filePath).use { fis ->
            fis.read(fileBuffer)
        }

        ByteArrayInputStream(fileBuffer).use { bytesIS ->
            bytesIS.skip(offset.toLong())

            while (isRunning && pkgIndex < remainingPkgCount) {
                val packageSize = if (pkgIndex == remainingPkgCount - 1) {
                    // last one
                    fileLen - offset - pkgIndex * MAX_DATA_PACKET_SIZE
                } else {
                    // non-last one
                    MAX_DATA_PACKET_SIZE
                }

                Logger.d(TAG, "loopSetData() >>> idx[$pkgIndex] lastSentIdx[$lastSentIndex] remainingPkgCount[$remainingPkgCount] pkg.size[$packageSize]")

                val packageBuffer = ByteArray(4 + packageSize)
                val bytesHeader = pkgIndex.toBigEndian4Bytes()

                System.arraycopy(bytesHeader, 0, packageBuffer, 0, 4)
                if (pkgIndex <= lastSentIndex) {
                    // sometimes, app need to re-send packet again according the received index from device.
                    bytesIS.reset()
                }

                bytesIS.mark(fileLen)
                bytesIS.read(packageBuffer, 4, packageSize)

                val setDfuDataResult = repeatWithTimeout(repeatTimes = 2, timeoutMills = TIMEOUT_MILLS) {
                    setDfuData(packageBuffer)
                }

                if (null == setDfuDataResult) {
                    return false
                }

                notify(offset = offsetIdx, current = pkgIndex, total = remainingPkgCount + offsetIdx)
                lastSentIndex = pkgIndex

                when (setDfuDataResult.dfuStatus) {
                    EnumDfuStatus.DOWNLOADING -> {
                        // next package
                        pkgIndex = setDfuDataResult.pkgIndex + 1
                        Logger.i(TAG, "loopSetData() >>> next package: $pkgIndex")
                    }

                    EnumDfuStatus.UPLOADING -> {
                        // package trans finished
                        Logger.i(TAG, "loopSetData() >>> trans finish and uploading")
                        break
                    }

                    else -> {
                        // error
                        Logger.e(TAG, "loopSetData() >>> exception happened at idx[$pkgIndex]")
                        syncIdleStatusWithException(SetDataException)
                        break
                    }
                }
            }
        }

        if (!syncCheckAndUpdateStatus(EnumPartyBoxDfuStatus.APPLY)) {
            return false
        }

        Logger.i(TAG, "loopSetData() >>> try to apply")
        val applyResult = applyDfu()
        if (!applyResult) {
            syncIdleStatusWithException(ApplyDfuException)
            return false
        }

        if (!syncCheckAndUpdateStatus(EnumPartyBoxDfuStatus.FINISH)) {
            return false
        }

        Logger.i(TAG, "loopSetData() >>> apply success, disconnect gatt session.")
        device.unregisterDeviceListener(globalErrorListener)
        return true
    }

    /**
     * @return true if [remoteFirmwareVersion] is greater that firmware version in [RspDfuInfoCommand.fwVersion]
     * which means need to pass OTA file from beginning.
     */
    private fun isRemoteGreater(
        dfuInfo: DfuInfo,
        remoteFirmwareVersion: String
    ): Boolean {
        val locals = dfuInfo.fwVersion.chunkVersionStringWithoutDots()

        val remotes = remoteFirmwareVersion.splitVersionStringWithDots()

        return remotes.isGreaterThan(locals)
    }

    private fun equalsRemote(
        dfuInfo: DfuInfo,
        remoteFirmwareVersion: String
    ): Boolean {
        val locals = dfuInfo.fwVersion.chunkVersionStringWithoutDots()

        val remotes = remoteFirmwareVersion.splitVersionStringWithDots()

        return remotes.isEqual(locals)
    }

    /**
     * @param force whether update [newStatus] was in [EnumPartyBoxDfuStatus.IDLE] state.
     * Used for filter status update condition after [cancel] exec.
     *
     * @return false if [cancel] executed and need to terminal further processes right now.
     */
    private fun syncCheckAndUpdateStatus(newStatus: EnumPartyBoxDfuStatus, force: Boolean = false): Boolean =
        synchronized(status) {
            if (!force && EnumPartyBoxDfuStatus.IDLE == status) {
                Logger.w(TAG, "syncCheckAndUpdateStatus() >>> terminal")
                return false
            }

            Logger.i(TAG, "syncCheckAndUpdateStatus() >>> switch to $newStatus")
            status = newStatus
            notify(newStatus)
            return true
        }

    override fun syncIdleStatusWithException(e: Exception) {
        device.unregisterDeviceListener(globalErrorListener)

        synchronized(status) {
            Logger.e(TAG, e.toString())
            status = EnumPartyBoxDfuStatus.IDLE
            notify(status = EnumPartyBoxDfuStatus.IDLE, e = e)
        }
    }

    // TODO Arlo.Zheng Code Review Plz
    private suspend fun getDfuInfo(): DfuInfo? {
        return device.sendCmdSync(
            ReqDfuInfoCommand(),
            DfuInfo::fromPayload,
            protocol = BluetoothDevice.TRANSPORT_BREDR
        )
    }

    /**
     * @param dfuVersion format as "AA.BB.CC"
     * @return Whether firmware ready or not.
     */
    // TODO Arlo.Zheng Code Review Plz
    private suspend fun startOrResumeDfuFlow(
        dfuCrc: Int,
        dfuSize: Int,
        dfuVersion: String,
        bpType: Byte,
    ): Boolean {
        return device.sendCmdSync(
            ReqDfuStartCommand(dfuCrc, dfuSize, dfuVersion, bpType),
            DfuStatus::fromPayload,
            protocol = BluetoothDevice.TRANSPORT_BREDR
        )?.ready() ?: false
    }

    /**
     * @return [EnumDfuStatus] after set last Dfu data. The response status would be switched from
     * [EnumDfuStatus.DOWNLOADING] to [EnumDfuStatus.UPLOADING], which means time to let firmware
     * apply OTA file.
     */
    // TODO Arlo.Zheng Code Review Plz
    private suspend fun setDfuData(dfuData: ByteArray): DfuStatus {
        return device.sendCmdSync(
            ReqSetDfuDataCommand(dfuData),
            DfuStatus::fromPayload,
            protocol = BluetoothDevice.TRANSPORT_BREDR
        ) ?: DfuStatus(EnumDfuStatus.ERROR, 0, EnumDfuOffsetStatus.EMPTY)
    }

    /**
     * @return nothing but resume running if received target response.
     * Firmware might response different command: [RspNotiAckCommand], [RspNotiDfuCompleteCommand]
     */
    // TODO Arlo.Zheng Code Review Plz
    private suspend fun applyDfu(): Boolean {
        return device.sendCmdSync(ReqDfuApplyCommand(), parserCmd = {
            if (it.commandID == GattPacketFormat.Rsp.DFU_ACK ||
                it.commandID == GattPacketFormat.Rsp.DFU_COMPLETE
            ) {
                return@sendCmdSync true
            }
            return@sendCmdSync null
        }, protocol = BluetoothDevice.TRANSPORT_BREDR) ?: false
    }

    /**
     * @return nothing but resume running if received target response.
     */
    // TODO Arlo.Zheng Code Review Plz
    private suspend fun cancel(): Boolean {
        return device.sendCmdSync(
            ReqDfuCancelCommand(),
            AckResp::fromPayload,
            protocol = BluetoothDevice.TRANSPORT_BREDR
        )?.let {
            it.isOk && it.reqCmd == GattPacketFormat.Req.DFU_CANCEL
        } ?: false
    }

    // TODO Arlo.Zheng Code Review Plz
    private inner class GlobalErrorListener : IPartyBoxDeviceListener {

        override fun onBrEdrStatusChanged(status: EnumConnectionStatus, session: BaseBrEdrSession<*, *, *>) {
            Logger.d(TAG, "onBrEdrStatusChanged() >>> status[$status] isRunning[$isRunning]")
            if (isRunning && status == EnumConnectionStatus.DISCONNECTED) {
                // gatt session disconnected while processing Dfu flow
                syncIdleStatusWithException(DisconnectException)
            }
        }

        override fun onCommandReceived(device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand) {
            super.onCommandReceived(device, sendCommand, receivedCommand)
            if (isRunning &&
                receivedCommand.commandID == GattPacketFormat.Rsp.DFU_STATUS &&
                true == receivedCommand.payload?.let { DfuStatus.fromPayload(it) }?.error()
            ) {
                //If an error occurs in the DFU state, an exception is thrown
                syncIdleStatusWithException(NotiDfuErrorException)
            }

        }
    }

    companion object {
        private const val TAG = "PartyBoxBaseOtaTaskV2"

        private const val MAX_DATA_PACKET_SIZE = 500
        private const val TIMEOUT_MILLS = 15 * 1000L
    }
}

private data class DfuInfo(
    val fwVersion: String,
    val breakPoint: Int,
) {
    companion object {
        fun fromPayload(payload: ByteArray): DfuInfo {
            val fwVer = payload.safetySubArray(start = 0, end = 3)?.let { bytes ->
                HexUtil.byte2HexStr(bytes)
            }!!
            val breakPoint = payload.safetySubArray(start = 3, end = 7)?.let { bytes ->
                HexUtil.byte2HexStr(bytes).toIntOrNull(16)
            }!!
            return DfuInfo(fwVer, breakPoint)
        }
    }
}

data class DfuStatus(
    val dfuStatus: EnumDfuStatus,
    val pkgIndex: Int,
    val dfuOffsetStatus: EnumDfuOffsetStatus?,
) {
    fun error(): Boolean = EnumDfuStatus.ERROR == dfuStatus
    fun ready(): Boolean = EnumDfuStatus.READY == dfuStatus
    fun downloading(): Boolean = EnumDfuStatus.DOWNLOADING == dfuStatus
    fun uploading(): Boolean = EnumDfuStatus.UPLOADING == dfuStatus

    companion object {
        private const val TAG = "PartyBoxOtaTaskV2"

        fun fromPayload(payload: ByteArray): DfuStatus {
            val dfuStatus = payload.safetySubArray(0, 1)?.getOrNull(0)?.let {
                EnumDfuStatus.from(it)
            }!!
            val pkgIndex = payload.safetySubArray(1, 5)?.toBigEndianUInt()?.toPositiveInt() ?: 0
            val dfuOffsetStatus = payload.safetySubArray(5, 6)?.getOrNull(0)?.let {
                EnumDfuOffsetStatus.from(it)
            }

            Logger.d(TAG, "fromPayload() >>> dfuStatus[$dfuStatus] pkgIndex[$pkgIndex] " +
                    "dfuOffsetStatus[$dfuOffsetStatus] payload:${HexUtil.byte2HexStr(payload)}")
            return DfuStatus(dfuStatus, pkgIndex, dfuOffsetStatus)
        }
    }

}