package com.harman.support

import androidx.annotation.WorkerThread
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.DiagnosisStatusResponse
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GroupDevicesChangeItem
import com.harman.command.one.bean.GroupInfo
import com.harman.command.one.bean.NotifySoundscapeV2MusicState
import com.harman.command.one.bean.NotifySoundscapeV2Setting
import com.harman.command.one.bean.OtaStatus
import com.harman.command.one.bean.upnp.SetAnchorResult
import com.harman.command.one.bean.upnp.SodState
import com.wifiaudio.app.LinkplaySDKHelper
import com.wifiaudio.model.DataFragInfo

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/7.
 *
 * Mapping necessary callback events in [LinkplaySDKHelper] (None Context/UI-related).
 */
interface ILinkPlaySDKHelperEvents {

    @WorkerThread
    fun logTimerEvent(dataInfo: DataFragInfo?, timer: Int) {}

    @WorkerThread
    fun playbackActionEvent(action: String) {}

    @WorkerThread
    fun receivedUPNPNotify(body: String, uuid: String) {}

    @WorkerThread
    fun serviceStatusEvent(isAdd: Boolean) {}

    @WorkerThread
    fun updateRemoteControlInfo(content: String, uuid: String) {}

    @WorkerThread
    fun updateSleepTimerEvent(remainSecs: Int, uuid: String?) {}

    @WorkerThread
    fun onUpnpSetAnchorResult(uuid: String, result: SetAnchorResult) {}

    @WorkerThread
    fun onUpnpSetCastGroup(uuid: String, result: GetGroupInfo?) {}

    @WorkerThread
    fun onUpnpNotifyGroupDevicesChange(uuid: String, result: GroupDevicesChangeItem?) {}

    /**
     * May use [IOneDeviceListener.onBatteryStatus] instead.
     */
    @WorkerThread
    fun onUpnpBatteryStatus(uuid: String, status: BatteryStatusResponse) {}

    @WorkerThread
    fun onUpnpSoundscapeV2State(uuid: String, rsp: GetSoundscapeV2StateResponse) {}

    @WorkerThread
    fun onUpnpOtaStatus(uuid: String, status: OtaStatus) {}

    @WorkerThread
    fun onUpnpCalibrationState(uuid: String, calibration: Calibration) {}

    /**
     *  Firmware will return [sodState.state] = 0 only after both two rear speakers detached.
     *  And any one attached will make [sodState.state] = 1.
     */
    @WorkerThread
    fun onUpnpSodState(uuid: String, sodState: SodState) {}

    @WorkerThread
    fun onUpnpSurroundState(uuid: String, level: Int) {}

    @WorkerThread
    fun onUpnpAudioSync(uuid: String, level: Int) {}

    @WorkerThread
    fun onDiagnosisStatus(uuid: String, rsp: DiagnosisStatusResponse) {}

    fun onUpnpNotifyGroupCalibrationState(uuid: String, rsp: GetGroupCalibrationStateRsp) {}

    @WorkerThread
    fun onUpnpSoundscapeV2MusicState(uuid: String, state: NotifySoundscapeV2MusicState) {}

    @WorkerThread
    fun onUpnpAuraCastSqStatus(uuid: String, state: AuraCastSqModelResponse) {}

    @WorkerThread
    fun onSoundscapeV2Setting(uuid: String, setting: NotifySoundscapeV2Setting) {}

    fun setMusicServiceOpened(isOpened: Boolean) {}

}