package com.harman.support

import android.app.Activity
import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.reflect.TypeToken
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.CalibrationState
import com.harman.command.one.bean.DiagnosisStatusResponse
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EnumOtaStatus
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GroupDevicesChange
import com.harman.command.one.bean.NotifyRears
import com.harman.command.one.bean.NotifySoundscapeV2MusicState
import com.harman.command.one.bean.NotifySoundscapeV2Setting
import com.harman.command.one.bean.OtaStatus
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.upnp.AudioSyncResponse
import com.harman.command.one.bean.upnp.PassString
import com.harman.command.one.bean.upnp.PassThrough
import com.harman.command.one.bean.upnp.SetAnchorResult
import com.harman.command.one.bean.upnp.UpnpNotifyOta
import com.harman.command.one.bean.upnp.UpnpNotifyPassThrough
import com.harman.command.one.bean.upnp.UpnpSodState
import com.harman.command.one.bean.upnp.UpnpSurroundState
import com.harman.discover.DeviceStore
import com.harman.discover.util.Tools.safeAsJObj
import com.harman.discover.util.Tools.safeAsJString
import com.harman.discover.util.Tools.safeToString
import com.harman.log.Logger
import com.harman.support.LinkPlaySDKHelperProxy.eventListeners
import com.harmanbar.ble.utils.GsonUtil
import com.wifiaudio.app.LinkplaySDKHelper
import com.wifiaudio.model.CusDialogProgItem
import com.wifiaudio.model.DataFragInfo
import com.wifiaudio.model.DeviceInfoExt
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.MenuSlideItem
import com.wifiaudio.model.local_music.MainItem
import com.wifiaudio.utils.okhttp.IOkHttpRequestCallback
import com.wifiaudio.view.alarm.bean.ItemAlarmSource
import java.lang.reflect.Type
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CopyOnWriteArrayList


/**
 * Created by gerrardzhang on 2024/3/7.
 *
 * A proxy which route events from [LinkplaySDKHelper] to [eventListeners] if it was defined
 * in [ILinkPlaySDKHelperEvents].
 *
 * Others will be processed by [ILinkPlaySDKHelperNonEvents]
 */
object LinkPlaySDKHelperProxy : LinkplaySDKHelper() {

    private val eventListeners = ConcurrentLinkedQueue<ILinkPlaySDKHelperEvents>()

    var nonEventHandler: ILinkPlaySDKHelperNonEvents? = null

    fun registerEventListener(listener: ILinkPlaySDKHelperEvents) {
        if (!eventListeners.contains(listener)) {
            eventListeners.add(listener)
        }
    }

    fun unregisterEventListener(listener: ILinkPlaySDKHelperEvents) {
        eventListeners.remove(listener)
    }

    fun clearEventListeners() {
        eventListeners.clear()
    }

    /** Events which defined in [ILinkPlaySDKHelperEvents], let proxy route outside. */

    override fun logTimerEvent(dataInfo: DataFragInfo?, timer: Int) {
        eventListeners.forEach { event ->
            event.logTimerEvent(dataInfo = dataInfo, timer = timer)
        }
    }

    override fun playbackActionEvent(action: String) {
        eventListeners.forEach { event ->
            event.playbackActionEvent(action = action)
        }
    }

    /**
     * @link [LinkPlaySDKHelperImpl.receivedUPNPNotify]
     */
    override fun receivedUPNPNotify(body: String, uuid: String) {
        eventListeners.forEach { event ->
            event.receivedUPNPNotify(body = body, uuid = uuid)
        }

        parseSpecNotify(body, uuid)
    }

    /**
     * Cause multiple types of data share the same key "payload".
     * We need to customize a gson with spec deserializer for [UpnpNotifyPassThrough]
     */
    private val passThroughGson = GsonBuilder()
        .registerTypeAdapter(
            UpnpNotifyPassThrough::class.java,
            object : JsonDeserializer<UpnpNotifyPassThrough?> {

                override fun deserialize(
                    json: JsonElement?,
                    typeOfT: Type?,
                    context: JsonDeserializationContext?
                ): UpnpNotifyPassThrough? {
                    val jsonObject = json?.safeAsJObj() ?: return null
                    val passThrough = jsonObject.get("pass_through")?.safeAsJObj() ?: return null
                    val passString = passThrough.get("pass_string")?.safeAsJObj() ?: return null
                    val command = passString.get("command")?.safeAsJString()
                    val payload = passString.get("payload")?.safeToString()

                    val result = UpnpNotifyPassThrough(
                        passThrough = PassThrough(
                            passString = PassString(
                                command = command
                            )
                        )
                    )
                    Logger.d(TAG, "parseSpecNotify() >>> $command, $result")
                    when (command) {
                        EnumCommandMapping.LP_SET_CAST_GROUP.wifiCmd -> {
                            result.passThrough?.passString?.groupInfo = GsonUtil.parseJsonToBean(
                                payload, GetGroupInfoRsp::class.java
                            )
                        }

                         NOTIFY_GROUP_DEVICES_CHANGED -> {
                            result.passThrough?.passString?.groupDevicesChange = GsonUtil.parseJsonToBean(
                                payload, GroupDevicesChange::class.java
                            )
                        }

                        SET_ANCHOR_RESULT -> {
                            result.passThrough?.passString?.anchor = GsonUtil.parseJsonToBean(
                                payload, SetAnchorResult::class.java
                            )
                        }

                        GET_BATTERY_STATUS -> {
                            result.passThrough?.passString?.battery = GsonUtil.parseJsonToBean(
                                payload, BatteryStatusResponse::class.java
                            )
                        }

                        NOTIFY_GROUP_PARAMETER -> {
                            val battery = GsonUtil.parseJsonToList<BatteryStatusResponse>(
                                passString.get("payload").safeAsJObj()?.get("battery")?.safeToString(),
                                object : TypeToken<List<BatteryStatusResponse>>() {}.type
                            )
                            result.passThrough?.passString?.batteryList = battery
                        }

                        EnumCommandMapping.NOTIFY_SOUNDSCAPE_V2_STATE.wifiCmd -> {
                            result.passThrough?.passString?.soundScapeV2State = GsonUtil.parseJsonToBean(
                                payload, GetSoundscapeV2StateResponse::class.java
                            )
                        }

                        NOTIFY_GERNAL_CONFIG_CHANGED -> {
                            result.passThrough?.passString?.generalConfig = GsonUtil.parseJsonToBean(
                                payload, GeneralConfig::class.java
                            )
                        }

                        EnumCommandMapping.NOTIFY_DIAGNOSIS_STATUS.wifiCmd -> {
                            result.passThrough?.passString?.diagnosisStatus = GsonUtil.parseJsonToBean(
                                payload, DiagnosisStatusResponse::class.java
                            )
                        }

                        NOTIFY_REAR_SPEAKER_STATUS -> {
                            result.passThrough?.passString?.rears = GsonUtil.parseJsonToBean(
                                payload, NotifyRears::class.java
                            )
                        }

                        NOTIFY_GROUP_CALIBRATION_STATE -> {
                            result.passThrough?.passString?.groupcalibrationState = GsonUtil.parseJsonToBean(
                                payload, GetGroupCalibrationStateRsp::class.java
                            )
                        }

                        EnumCommandMapping.NOTIFY_SOUNDSCAPE_V2_MUSIC_STATE.wifiCmd -> {
                            result.passThrough?.passString?.soundscapeV2MusicState = GsonUtil.parseJsonToBean(
                                payload, NotifySoundscapeV2MusicState::class.java
                            )
                        }

                        EnumCommandMapping.NOTIFY_AURA_CAST_SQ_MODE.wifiCmd -> {
                            result.passThrough?.passString?.auraCastSqStatus = GsonUtil.parseJsonToBean(
                                payload, AuraCastSqModelResponse::class.java
                            )
                        }

                        NOTIFY_EQ_LIST -> {
                            result.passThrough?.passString?.eqList = GsonUtil.parseJsonToBean(
                                payload, EQListResponse::class.java
                            )
                        }

                        NOTIFY_USER_EQ_CHANGED -> {
                            result.passThrough?.passString?.eqResponse = GsonUtil.parseJsonToBean(
                                payload, EQResponse::class.java
                            )
                        }

                        EnumCommandMapping.NOTIFY_SOUNDSCAPE_V2_SETTING.wifiCmd -> {
                            result.passThrough?.passString?.soundscapeV2Setting = GsonUtil.parseJsonToBean(
                                payload, NotifySoundscapeV2Setting::class.java
                            )
                        }

                        EnumCommandMapping.NOTIFY_LIGHT_INFO.wifiCmd -> {
                            result.passThrough?.passString?.notifyLightInfo = GsonUtil.parseJsonToBean(
                                payload, SetLightInfoRequest::class.java
                            )
                        }
                    }

                    return result
                }
            })
        .create()

    private fun parseSpecNotify(body: String, uuid: String) {
        Logger.d(TAG, "parseSpecNotify() >>> uuid[$uuid] body:$body")
        if(body.contains(NOTIFY_GROUP_DEVICES_CHANGED)){
            Logger.d(TAG, "parseSpecNotify() >>> onUpnpNotifyGroupDevicesChange uuid[$uuid] body:$body")
        }

        passThroughGson.fromJson(body, UpnpNotifyPassThrough::class.java)?.handlePassThroughBody(uuid = uuid)
        GsonUtil.parseJsonToBean(body, UpnpNotifyOta::class.java)?.handleOTABody(uuid = uuid)
        GsonUtil.parseJsonToBean(body, CalibrationState::class.java)?.notify(uuid = uuid)
        GsonUtil.parseJsonToBean(body, UpnpSodState::class.java)?.notify(uuid = uuid)
        GsonUtil.parseJsonToBean(body, UpnpSurroundState::class.java)?.notify(uuid = uuid)
        GsonUtil.parseJsonToBean(body, AudioSyncResponse::class.java)?.notify(uuid = uuid)
    }

    private fun UpnpNotifyPassThrough.handlePassThroughBody(uuid: String) {
        val notifyBean = this
        DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
            Logger.d(TAG, "handlePassThroughBody() >>> [${device.UUID}] $device")

        }

        when (notifyBean.passThrough?.passString?.command) {
            NOTIFY_GROUP_DEVICES_CHANGED-> {
                val result = notifyBean.passThrough.passString.groupDevicesChange ?: return
                Logger.d(TAG, "handlePassThroughBody() >>> onUpnpNotifyGroupDevicesChange ${notifyBean.passThrough.passString.groupDevicesChange}")
                eventListeners.forEach { listener ->
                    result.getGC()?.let{
                        listener.onUpnpNotifyGroupDevicesChange(uuid = uuid, result = it)
                    }

                }
            }
            EnumCommandMapping.DESTROY_CAST_GROUP.wifiCmd -> {

                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.onGroupInfo(null)
                    device.wifiListeners.forEach { listener ->
                        listener.onGroupInfo(null)
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onUpnpSetCastGroup(uuid = uuid, result = null)
                }
            }

            EnumCommandMapping.LP_SET_CAST_GROUP.wifiCmd -> {
                val result = notifyBean.passThrough.passString.groupInfo ?: return
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.onGroupInfo(result)
                    device.wifiListeners.forEach { listener ->
                        result.groupInfo?.let { listener.onGroupInfo(it) }
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onUpnpSetCastGroup(uuid = uuid, result = result)
                }
            }

            SET_ANCHOR_RESULT -> {
                val result = notifyBean.passThrough.passString.anchor ?: return

                eventListeners.forEach { listener ->
                    listener.onUpnpSetAnchorResult(uuid = uuid, result = result)
                }
            }

            NOTIFY_GROUP_PARAMETER -> {
                val batteryStatus = notifyBean.passThrough.passString.batteryList ?: return

                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    var groupParameterRsp = device.groupParameterExt?.groupParameter ?: return
                    groupParameterRsp.battery = batteryStatus
                    device.onGroupParameter(groupParameterRsp)
                    device.wifiListeners.forEach { listener ->
                        listener.onGetGroupParameter(groupParameterRsp)
                    }
                }

            }

            GET_BATTERY_STATUS -> {
                val batteryStatus = notifyBean.passThrough.passString.battery ?: return

                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.onBatteryStatus(status = batteryStatus)
                    device.wifiListeners.forEach { listener ->
                        listener.onBatteryStatus(rsp = batteryStatus)
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onUpnpBatteryStatus(uuid = uuid, status = batteryStatus)
                }
            }

            EnumCommandMapping.NOTIFY_SOUNDSCAPE_V2_STATE.wifiCmd -> {
                val rsp = notifyBean.passThrough.passString.soundScapeV2State ?: return

                // no 'error_code' param inside notify
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.onSoundscapeV2State(rsp = rsp)
                    device.wifiListeners.forEach { listener ->
                        listener.onSoundscapeV2State(rsp = rsp)
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onUpnpSoundscapeV2State(uuid = uuid, rsp = rsp)
                }
            }

            NOTIFY_GERNAL_CONFIG_CHANGED -> {
                DeviceStore.findOne(uuid)?.wifiDevice?.also {
                    it.wifiListeners.forEach { listener ->
                        listener.onGeneralConfigNotify(notifyBean.passThrough.passString.generalConfig!!)
                    }
                }
            }

            EnumCommandMapping.NOTIFY_DIAGNOSIS_STATUS.wifiCmd -> {
                val diagnosisStatus = notifyBean.passThrough.passString.diagnosisStatus ?: return

                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.onDiagnosisStatus(rsp = diagnosisStatus)
                    device.wifiListeners.forEach { listener ->
                        listener.onDiagnosisStatus(rsp = diagnosisStatus)
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onDiagnosisStatus(uuid = uuid, rsp = diagnosisStatus)
                }
            }

            NOTIFY_REAR_SPEAKER_STATUS -> {
                DeviceStore.findOne(uuid)?.wifiDevice?.also { dev ->
                    (notifyBean.passThrough.passString.rears?.rears ?: listOf()).also {
                        dev.onGetRearSpeaker(it)
                        dev.wifiListeners.forEach { listener ->
                            listener.onSpeakerRearNotify(it)
                        }
                    }
                }
            }

            NOTIFY_GROUP_CALIBRATION_STATE -> {
                val result = notifyBean.passThrough.passString.groupcalibrationState ?: return
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->

                    device.wifiListeners.forEach { listener ->
                        result.let { listener.onGetGroupCalibrationState(it) }
                    }
                }

                eventListeners.forEach { listener ->
                    result.let {
                        listener.onUpnpNotifyGroupCalibrationState(uuid = uuid, rsp = it)
                    }

                }
            }

            EnumCommandMapping.NOTIFY_SOUNDSCAPE_V2_MUSIC_STATE.wifiCmd -> {
                val state = notifyBean.passThrough.passString.soundscapeV2MusicState ?: return

                // no 'error_code' param inside notify
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.wifiListeners.forEach { listener ->
                        listener.onSoundscapeV2MusicState(state = state)
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onUpnpSoundscapeV2MusicState(uuid = uuid, state = state)
                }
            }

            EnumCommandMapping.NOTIFY_AURA_CAST_SQ_MODE.wifiCmd -> {
                val state = notifyBean.passThrough.passString.auraCastSqStatus ?: return
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.wifiListeners.forEach { listener ->
                        listener.onAuraCastSqMode(response = state)
                    }
                }
                eventListeners.forEach { listener ->
                    listener.onUpnpAuraCastSqStatus(uuid = uuid, state = state)
                }
            }

            NOTIFY_EQ_LIST -> {
                val eqList = notifyBean.passThrough.passString.eqList ?: return

                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.wifiListeners.forEach { listener ->
                        listener.onEQList(eqListResponse = eqList)
                    }
                }
            }

            NOTIFY_USER_EQ_CHANGED -> {
                val eqResponse = notifyBean.passThrough.passString.eqResponse ?: return

                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.wifiListeners.forEach { listener ->
                        listener.onEQ(eqResponse = eqResponse)
                    }
                }
            }

            EnumCommandMapping.NOTIFY_SOUNDSCAPE_V2_SETTING.wifiCmd -> {
                val setting = notifyBean.passThrough.passString.soundscapeV2Setting ?: return

                // no 'error_code' param inside notify
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.wifiListeners.forEach { listener ->
                        listener.onSoundscapeV2Setting(setting = setting)
                    }
                }

                eventListeners.forEach { listener ->
                    listener.onSoundscapeV2Setting(uuid = uuid, setting = setting)
                }
            }

            EnumCommandMapping.NOTIFY_LIGHT_INFO.wifiCmd -> {
                val notifyLightInfo = notifyBean.passThrough.passString.notifyLightInfo ?: return
                Logger.d(TAG, "LIGHT_INFO_RELATED notifyLightInfo() >>> uuid:$uuid, notifyLightInfo:$notifyLightInfo")
                DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                    device.wifiListeners.forEach { listener ->
                        listener.onNotifyLightInfo(notifyLightInfo)
                    }
                }
            }

        }
    }

    private fun UpnpNotifyOta.handleOTABody(uuid: String) {
        val notifyBean = this

        notifyBean.otaEvent?.let { event ->
            when (event.eventType) {
                "checking" -> {
                    // {"ota":{"event_type":"checking","type":"OOBE"}}
                    callbackUpnpOtaStatus(uuid = uuid, status = EnumOtaStatus.CHECKING)
                }

                "UpgradeAvailable" -> {
                    // {"ota":{"event_type":"UpgradeAvailable","type":"OOBE","version":"24.19.33.60.11"}}
                    callbackUpnpOtaStatus(uuid = uuid, status = EnumOtaStatus.NEW_VERSION)
                }

                "noUpgradeAvailable" -> {
                    // {"ota":{"event_type":"noUpgradeAvailable","type":"OOBE"}}
                    callbackUpnpOtaStatus(uuid = uuid, status = EnumOtaStatus.UPDATE_TO_DATE)
                }

                "downloading" -> {
                    // {"ota":{"event_type":"downloading","type":"OOBE","progress":"28"}}
                    callbackUpnpOtaStatus(uuid = uuid, status = EnumOtaStatus.DOWNLOADING, progress = event.progress)
                }

                "reboot" -> {
                    // {"ota":{"event_type":"reboot"}}
                    callbackUpnpOtaStatus(uuid = uuid, status = EnumOtaStatus.BURNING_START)
                }
            }
        }
    }

    private fun CalibrationState.notify(uuid: String) {
        val calibration = this.calibration ?: return

        Logger.d(TAG, "CalibrationState.notify() >>> uuid[$uuid] body:$calibration")
        eventListeners.forEach { listener ->
            listener.onUpnpCalibrationState(uuid = uuid, calibration = calibration)
        }

        DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
            Logger.d(TAG, "CalibrationState.notify() >>> found target OneDevice instance:\n$device")
            device.wifiListeners.forEach { listener ->
                listener.onCalibration(calibration = calibration)
            }
        }
    }

    private fun UpnpSodState.notify(uuid: String) {
        val sodState = this.state ?: return

        eventListeners.forEach { listener ->
            listener.onUpnpSodState(uuid = uuid, sodState = sodState)
        }

        DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
            Logger.d(TAG, "UpnpSodState.notify() >>> found target OneDevice instance:\n$device")
            device.wifiListeners.forEach { listener ->
                listener.onSodState(state = sodState)
            }
        }
    }

    private fun UpnpSurroundState.notify(uuid: String) {
        val level = this.surroundLevel?.level?: return

        eventListeners.forEach { listener ->
            listener.onUpnpSurroundState(uuid = uuid, level = level)
        }

        DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
            Logger.d(TAG, "UpnpSurroundState.notify() >>> found target OneDevice instance:\n$device level : $level")
            device.wifiListeners.forEach { listener ->
                listener.onSurroundState(level = level)
            }
        }
    }

    private fun AudioSyncResponse.notify(uuid: String) {
        val level = this.lipSync?.value?: return

        eventListeners.forEach { listener ->
            listener.onUpnpAudioSync(uuid = uuid, level = level)
        }

        DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
            Logger.d(TAG, "AudioSyncResponse.notify() >>> found target OneDevice instance:\n$device level : $level")
            device.wifiListeners.forEach { listener ->
                listener.onAudioSync(level = level)
            }
        }
    }

    private fun callbackUpnpOtaStatus(uuid: String, status: EnumOtaStatus, progress: Int? = null) {
        Logger.d(TAG, "callbackUpnpOtaStatus() >>> uuid[$uuid] status[$status] progress[$progress]")
        val otaStatus = OtaStatus(status = status.value, progress = progress)

        eventListeners.forEach { listener ->
            listener.onUpnpOtaStatus(uuid = uuid, status = otaStatus)
        }

        DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
            Logger.d(TAG, "callbackUpnpOtaStatus() >>> found target OneDevice instance:\n$device")
            device.wifiListeners.forEach { listener ->
                listener.onOtaStatus(status = otaStatus)
            }
        }
    }

    override fun serviceStatusEvent(isAdd: Boolean) {
        eventListeners.forEach { event ->
            event.serviceStatusEvent(isAdd = isAdd)
        }
    }

    override fun updateRemoteControlInfo(content: String, uuid: String) {
        eventListeners.forEach { event ->
            event.updateRemoteControlInfo(content = content, uuid = uuid)
        }
    }

    @Deprecated("Use precious remain time from GET_SLEEP_TIMER instead")
    override fun updateSleepTimerEvent(remainSecs: Int, uuid: String?) {
        Logger.d(TAG, "updateSleepTimerEvent() >>> [$uuid] $remainSecs")
        if (!uuid.isNullOrBlank()) {
            DeviceStore.findOne(uuid = uuid)?.wifiDevice?.let { device ->
                Logger.d(TAG, "updateSleepTimerEvent() >>> [${device.UUID}] $remainSecs")
                device.wifiListeners.forEach { listener ->
                    listener.onSleepTimerEvent(dummyRemainSecs = remainSecs)
                }
            }
        }
    }

    override fun changePlayView(activity: FragmentActivity?, deviceInfoExt: DeviceInfoExt?) {
        nonEventHandler?.changePlayView(activity = activity, deviceInfoExt = deviceInfoExt)
    }

    override fun changeTabBackground(activity: Activity?) {
        nonEventHandler?.changeTabBackground(activity = activity)
    }

    override fun doGrouping(): Boolean {
        return nonEventHandler?.doGrouping() ?: false
    }

    override fun exitGrouping(): Boolean {
        return nonEventHandler?.exitGrouping() ?: false
    }

    override fun getAlarmSources(
        context: Context?,
        deviceItem: DeviceItem?
    ): List<ItemAlarmSource>? {
        return nonEventHandler?.getAlarmSources(context = context, deviceItem = deviceItem)
    }

    override fun getFragMenuContentRTExitGrouping(): Boolean {
        return nonEventHandler?.getFragMenuContentRTExitGrouping() ?: false
    }

    override fun getFragMenuContentRTSlaveGroupUUID(): String {
        return nonEventHandler?.getFragMenuContentRTSlaveGroupUUID() ?: ""
    }

    override fun getMasterGroupUUID(): String {
        return nonEventHandler?.getMasterGroupUUID() ?: ""
    }

    @Deprecated("use MusicServiceRepo instead")
    override fun getMusicServiceUrl() = ""

    override fun getOperateVolume(): Int {
        return nonEventHandler?.getOperateVolume() ?: -1
    }

    override fun getSlaveGroupUUID(): String {
        return nonEventHandler?.getSlaveGroupUUID() ?: ""
    }

    override fun getSupportedSources(context: Context?): MutableList<MainItem>? {
        return nonEventHandler?.getSupportedSources(context = context)
    }

    override fun getVisibleSource(context: Context?): MutableList<MenuSlideItem>? {
        return nonEventHandler?.getVisibleSource(context = context)
    }

    override fun isMusicContentPagersActivity(activity: Activity?): Boolean {
        return nonEventHandler?.isMusicContentPagersActivity(activity = activity) ?: false
    }

    override fun isSourceDisabled(source: String, supportedSources: List<MainItem>?): Boolean {
        return nonEventHandler?.isSourceDisabled(source = source, supportedSources = supportedSources) ?: true
    }

    override fun keyToSourceType(key: Int): String {
        return nonEventHandler?.keyToSourceType(key = key) ?: ""
    }

    override fun musicContentPagersActivityClearFragmentStack() {
        nonEventHandler?.musicContentPagersActivityClearFragmentStack()
    }

    override fun musicContentPagersActivitySendMessage(activity: FragmentActivity?, arg1: Int) {
        nonEventHandler?.musicContentPagersActivitySendMessage(activity = activity, arg1 = arg1)
    }

    override fun notificationReceiver(context: Context) {
        nonEventHandler?.notificationReceiver(context = context)
    }

    override fun openHBSpotifyDlg(activity: FragmentActivity?, uuid: String) {
        nonEventHandler?.openHBSpotifyDlg(activity = activity, uuid = uuid)
    }

    override fun openMSLoginDlg(
        activity: FragmentActivity?,
        item: MenuSlideItem,
        callback: IOkHttpRequestCallback,
        uuid: String
    ) {
        nonEventHandler?.openMSLoginDlg(
            activity = activity,
            item = item,
            callback = callback,
            uuid = uuid
        )
    }

    override fun openMenu(activity: Activity?, smoothScroll: Boolean) {
        nonEventHandler?.openMenu(activity = activity, smoothScroll = smoothScroll)
    }

    override fun openPlayview(activity: Activity?) {
        nonEventHandler?.openPlayview(activity = activity)
    }

    override fun openRTMenu(activity: Activity?, smoothScroll: Boolean) {
        nonEventHandler?.openRTMenu(activity = activity, smoothScroll = smoothScroll)
    }

    override fun openServiceManager(activity: FragmentActivity?) {
        nonEventHandler?.openServiceManager(activity = activity)
    }

    override fun openSleepTimerDlg(activity: FragmentActivity?, uuid: String) {
        nonEventHandler?.openSleepTimerDlg(activity = activity, uuid = uuid)
    }

    override fun saveServiceStatus(context: Context?, mainItems: List<MainItem>?) {
        nonEventHandler?.saveServiceStatus(context = context, mainItems = mainItems)
    }

    override fun setFragMenuContentRTExitGrouping(exit: Boolean) {
        nonEventHandler?.setFragMenuContentRTExitGrouping(exit = exit)
    }

    override fun setFragMenuContentRTOnlineFromUPNP(exit: Boolean) {
        nonEventHandler?.setFragMenuContentRTOnlineFromUPNP(exit = exit)
    }

    override fun setLoadingVisibility(cusDialogProgItem: CusDialogProgItem?) {
        nonEventHandler?.setLoadingVisibility(cusDialogProgItem = cusDialogProgItem)
    }

    override fun setMusicServiceOpened(isOpened: Boolean) {
        Logger.d(TAG, "setMusicServiceOpened >>> isOpened:$isOpened")
        nonEventHandler?.setMusicServiceOpened(isOpened = isOpened)
        eventListeners.forEach { event ->
            event.setMusicServiceOpened(isOpened = isOpened)
        }
    }

    override fun setOperateVolume(int: Int) {
        nonEventHandler?.setOperateVolume(int = int)
    }

    override fun setRemoveFromSlaveList(remove: Boolean) {
        nonEventHandler?.setRemoveFromSlaveList(remove = remove)
    }

    override fun setStationPlayStatus(activity: Activity?) {
        nonEventHandler?.setStationPlayStatus(activity = activity)
    }

    override fun showMusicContent(activity: Activity?, smoothScroll: Boolean) {
        nonEventHandler?.showMusicContent(activity = activity, smoothScroll = smoothScroll)
    }

    override fun showSlidingUpPanel(activity: FragmentActivity?, open: Boolean) {
        nonEventHandler?.showSlidingUpPanel(activity = activity, open = open)
    }

    override fun showTuneInPlayView(activity: FragmentActivity?) {
        nonEventHandler?.showTuneInPlayView(activity = activity)
    }

    private const val SET_ANCHOR_RESULT = "setAnchorResult"
    private const val GET_BATTERY_STATUS = "getBatteryStatus"
    private const val NOTIFY_GERNAL_CONFIG_CHANGED = "notifyGernalConfigChanged"
    private const val NOTIFY_REAR_SPEAKER_STATUS = "notifyRearSpeakerStatus"
    private const val NOTIFY_GROUP_CALIBRATION_STATE = "notifyGroupCalibrationState"
    private const val NOTIFY_GROUP_PARAMETER = "notifyGroupParameter"
    private const val NOTIFY_EQ_LIST = "notifyEQList"
    private const val NOTIFY_USER_EQ_CHANGED = "notifyUserEQChanged"
    private const val NOTIFY_GROUP_DEVICES_CHANGED = "notifyGroupDevicesChange"

    private const val TAG = "LinkPlaySDKHelperProxy"
}
