package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName

/**
 * @Description
 * <AUTHOR>
 * @Time 2024/9/25
 */
data class StatusResp(
    @SerializedName("error_code")
    val errorCode: String? = null,
    @SerializedName("status")
    val status: String? = null,
) {
    fun success() = "0" == errorCode

    fun isOn() = statusOn == status
}

data class StatusReq(
    @SerializedName("status")
    val status: String
)

const val statusOn = "on"
const val statusOff = "off"