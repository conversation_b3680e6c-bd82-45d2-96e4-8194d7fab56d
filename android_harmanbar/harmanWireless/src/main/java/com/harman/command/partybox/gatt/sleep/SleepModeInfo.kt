package com.harman.command.partybox.gatt.sleep

import android.util.Log
import com.harman.util.append
import com.harman.util.getIntByteArray
import com.harman.util.getIntValueWith
import com.harman.util.retTokenValueAndUnprocessedPayload
import java.io.Serializable

/**
 * <AUTHOR>
 * version：1.0
 * date：2024/9/19
 * desc：SleepModeInfo
 *
 */
class SleepModeInfo : Serializable {
    var isActive: Boolean? = false
    var isLightActive: Boolean? = false

    //0-100
    var lightBrightness: Int? = 0
    var temperatureColor: Int? = 0
    var lightMode: LightMode? = null
    var timer: Int? = 0
    var ambientSound: AmbientSound? = null

    // 手动深拷贝方法
    fun deepCopy(): SleepModeInfo {
        val newSleepModeInfo = SleepModeInfo()
        newSleepModeInfo.isActive = this.isActive
        newSleepModeInfo.isLightActive = this.isLightActive
        newSleepModeInfo.lightBrightness = this.lightBrightness
        newSleepModeInfo.temperatureColor = this.temperatureColor
        newSleepModeInfo.lightMode = this.lightMode
        newSleepModeInfo.timer = this.timer
        newSleepModeInfo.ambientSound = this.ambientSound
        return newSleepModeInfo
    }

    enum class Command(val cmd: Int, val value: Int?, val desc: String?) {
        Status(cmd = 0x71, value = null, desc = "1 bytes 0x01: Static 0x02: Breathe."),
        ResetMode(
            cmd = 0x72,
            value = 0x01,
            desc = "1 bytes 0x01: Reset Sleep Mode Light to Default. Device should reply to app with changed light information."
        ),
        PreviewAmbientSound(cmd = 0x72, value = 0x02, desc = "0x02: Preview Ambient Sound."),
        StopPreviewAmbientSound(cmd = 0x72, value = 0x03, desc = "0x03: Stop Preview Ambient Sound."),
        SaveSleepMode(cmd = 0x72, value = 0x30, desc = "0x03: Save Sleep Mode Settings."),
        PreviewLight(cmd = 0x72, value = 0x04, desc = "0x04: Preview light."),
        LightBrightness(cmd = 0x73, value = null, desc = "1 bytes 0x00-0x64"),
        TemperatureColor(cmd = 0x74, value = null, desc = "1 bytes 0x00-0x64"),
        LightMode(cmd = 0x75, value = null, desc = "1 bytes 0x00-0x64"),
        Timer(cmd = 0x76, value = null, desc = "2 bytes Integer in little endian, unit is second."),
        IsLightActive(cmd = 0x77, value = null, desc = "1 byte 0x00: Off 0x01: On."),
        AmbientSound(
            cmd = 0x7A,
            value = null,
            desc = "2 bytes 1st byte:\n" +
                    "Ambient sound type: 0x01: Ambient Sound 0x02: Radio.\n" +
                    "Ambient Sound Audio index: 0x01: Forest\n" +
                    "0x02: Ocean\n" +
                    "0x03: Rain\n" +
                    "0x04: Wind chime\n" +
                    "0x05: Camp fire"
        );

        companion object {
            @JvmStatic
            fun getCommandByCode(code: Int): Command = entries.find { command -> command.cmd == code } ?: Status
        }
    }

    enum class LightMode(val code: Byte, val desc: String) {
        Static(code = 0x01, desc = "static light"),
        Breathe(code = 0x02, desc = "breathe light");

        companion object {
            @JvmStatic
            fun getModeWithCode(code: Int): LightMode = entries.find { lightType -> lightType.code == code.toByte() } ?: Static
        }
    }

    data class AmbientSound(val type: SoundType, val index: Int) : Serializable

    enum class SoundType(val code: Int, val desc: String) {
        Ambient(code = 0x01, desc = "Ambient"),
        Radio(code = 0x02, desc = "Radio");

        companion object {
            @JvmStatic
            fun geTypeWithCode(code: Int): SoundType = entries.find { value -> value.code == code } ?: Ambient
        }
    }

    enum class Ambient(val code: Int, val desc: String) {
        Forest(code = 0x01, desc = "Forest"),
        Ocean(code = 0x02, desc = "Ocean"),
        Rain(code = 0x03, desc = "Rain"),
        WindChime(code = 0x04, desc = "WindChime"),
        CampFire(code = 0x05, desc = "CampFire");

        companion object {
            @JvmStatic
            fun getWithCode(code: Int): Ambient = entries.find { ambient -> ambient.code == code } ?: Forest

            fun getCodeWithName(desc: String?): Byte = (entries.find { ambient -> ambient.desc == desc } ?: Forest).code.toByte()
        }
    }

    enum class RingTone(val code: Int, val desc: String) {
        Rise(code = 0x01, desc = "Rise"),
        Solar(code = 0x02, desc = "Solar"),
        Groove(code = 0x03, desc = "Groove"),
        Buzzer(code = 0x04, desc = "Buzzer")
    }

    override fun toString(): String {
        return "SleepModeInfo(isActive=$isActive,isLightActive=$isLightActive, lightBrightness=$lightBrightness, temperatureColor=$temperatureColor, lightMode=$lightMode, timer=$timer, ambientSound=$ambientSound)"
    }

    /**
     * build bytes to update [SetSleepModeCommand]
     */
    fun infoBytes(): ByteArray? {
        var bytes = byteArrayOf()
        //append isActive Bytes
        this.isActive?.let { bytes = append(src = bytes, cmd = Command.Status.cmd.toByte(), byteArrayOf(if (it) 1 else 0)) }
        //append lightBrightness
        this.lightBrightness?.let { bytes = append(src = bytes, cmd = Command.LightBrightness.cmd.toByte(), bytes = byteArrayOf(it.toByte())) }
        //append lightTemperatureColor
        this.temperatureColor?.let { bytes = append(src = bytes, cmd = Command.TemperatureColor.cmd.toByte(), bytes = byteArrayOf(it.toByte())) }
        //append lightMode
        this.lightMode?.let { bytes = append(src = bytes, cmd = Command.LightMode.cmd.toByte(), bytes = byteArrayOf(it.code)) }
        //append timer
        this.timer?.let {
            var timerBytes = byteArrayOf(Command.Timer.cmd.toByte(), 2)
            timerBytes = append(src = timerBytes, bytes = getIntByteArray(it))
            //append timer bytes payload
            bytes = append(src = bytes, bytes = timerBytes)
        }
        //append light status
        this.isLightActive?.let { bytes = append(src = bytes, cmd = Command.IsLightActive.cmd.toByte(), byteArrayOf(if (it) 1 else 0)) }

        //append ambient sound
        this.ambientSound?.let {
            val commandBytes = byteArrayOf(Command.AmbientSound.cmd.toByte(), 2, it.type.code.toByte(), it.index.toByte())
            bytes = append(src = bytes, bytes = commandBytes)
        }
        return bytes
    }

    companion object {
        private const val serialVersionUID: Long = -8388992208201897915L
        const val TAG = "SleepModeInfo"

        @JvmStatic
        fun parseAndSetSleepModeInfo(receivedPayload: String): SleepModeInfo? {
            val sleepModeInfo = SleepModeInfo()
            var unprocessedPayload = receivedPayload

            while (unprocessedPayload.isNotEmpty()) {
                val tokenID = unprocessedPayload.substring(0, 2)
                val (tokenValue, remainingPayload) = retTokenValueAndUnprocessedPayload(unprocessedPayload)
                unprocessedPayload = remainingPayload ?: ""
                if (tokenValue.isNullOrEmpty()) continue
                val command = Command.getCommandByCode(tokenID.toInt(16))
                Log.d(TAG, "parseAndSetSleepModeInfo command:$command, tokenValue:$tokenValue")
                when (command) {
                    SleepModeInfo.Command.Status -> sleepModeInfo.isActive = tokenValue.toInt(16) == 1
                    SleepModeInfo.Command.LightBrightness -> sleepModeInfo.lightBrightness = tokenValue.toInt(16)
                    SleepModeInfo.Command.TemperatureColor -> sleepModeInfo.temperatureColor = tokenValue.toInt(16)
                    SleepModeInfo.Command.LightMode -> sleepModeInfo.lightMode = LightMode.getModeWithCode(tokenValue.toInt(16))
                    SleepModeInfo.Command.Timer -> sleepModeInfo.timer = getIntValueWith(tokenValue)
                    SleepModeInfo.Command.IsLightActive -> sleepModeInfo.isLightActive = tokenValue.toInt(16) == 1
                    SleepModeInfo.Command.AmbientSound -> sleepModeInfo.ambientSound = getAmbientSound(tokenValue)
                    else -> Unit
                }
            }
            Log.d(TAG, "parseAndSetSleepModeInfo: sleepModeInfo:$sleepModeInfo")
            return sleepModeInfo
        }

        @JvmStatic
        fun getAmbientSound(tokenValue: String): AmbientSound? {
            if (tokenValue.length < 4) return null
            val type = SoundType.geTypeWithCode(tokenValue.substring(0, 2).toInt(16))
            val index = tokenValue.substring(2, 4).toInt(16)
            return AmbientSound(type = type, index = index)
        }
    }

}