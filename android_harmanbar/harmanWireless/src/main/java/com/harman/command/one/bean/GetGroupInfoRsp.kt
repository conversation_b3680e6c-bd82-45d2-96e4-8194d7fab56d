package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName

/**
 * Created by sky on 2024/9/12.
 *
    {
        "error_code": "0",
        "p2p_mac": "3a:c8:04:08:96:39",
        "group_mode": "group",
        "group_info": {
            "disabled": false,
            "group": {
                "name": "My Stereo Pair",
                " p2p_mac ": "3a:c8:04:08:96:39",
                "type": "multichannel",
                " id ": "38c804089639",
                "combination_id": "2.0_208c_208c"
            },
            "members": [
            {
                "id": "38c804089639",
                "channel": [
                "front_left"
                ],
                "device_name": "HK Citation 110",
                "color_id": 2
            },
            {
                "id": "38c80408972f",
                "channel": [
                "front_right"
                ],
                "device_name": "HK Citation 110",
                "color_id": 2
            }
            ]
        }
    }
 *
 */

 class GetGroupInfoRsp:GetGroupInfo(){
    @SerializedName("error_code")
    var errorCode: Int? = null
}

open class GetGroupInfo : java.io.Serializable{
    @SerializedName("p2p_mac")
    var p2pMac: String? = null
    @SerializedName("group_mode")
    var groupMode: String? = null
    @SerializedName("online_devices")
    var onlineDevices: List<String>? = null
    @SerializedName("online_devices_v2")
    var onlineDevicesV2: List<OneLineDeviceItem>? = null
    @SerializedName("group_info")
    var groupInfo: GroupInfo? = null

    companion object {
        private const val serialVersionUID: Long = 7094258044050891600
    }

    fun isSingle() : Boolean{
        return GroupMode.SINGLE.value == groupMode
    }

    fun isGroup() : Boolean{
        return GroupMode.GROUP.value == groupMode
    }

    fun isBeforeGroup() : Boolean{
        return GroupMode.BEFORE_GROUP.value == groupMode
    }

    fun getGoCrc() : String?{
        return groupInfo?.getGOMember()?.crc
    }

    override fun toString(): String {
        return "GetGroupInfo(p2pMac=$p2pMac, groupMode=$groupMode), groupInfo=$groupInfo)"
    }
}

enum class GroupMode(val value: String) {
    GROUP("group"),
    SINGLE("single"),
    BEFORE_GROUP("before_group");

    companion object {
        fun stringToGroupMode(target: String): GroupMode? = entries.firstOrNull { enum ->
            target == enum.value
        }

    }
}

enum class GroupType(val value: String) {
    PARTY_MODE("partymode"),
    STEREO("stereo"),
    MULTICHANNEL("multichannel");
}
