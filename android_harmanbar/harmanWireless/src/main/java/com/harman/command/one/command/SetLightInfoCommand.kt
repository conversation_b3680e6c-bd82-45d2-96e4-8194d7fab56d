package com.harman.command.one.command

import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.SetLightInfoRequest
import com.harmanbar.ble.utils.GsonUtil

/**
 * 0x1833
 */
class SetLightInfoCommand(val request: SetLightInfoRequest) : OneGattCommand() {
    override var commandID: Int = EnumCommandMapping.SET_LIGHT_INFO.bleCmd
    override val payload: ByteArray? = GsonUtil.parseBeanToJson(request)?.toByteArray()
}