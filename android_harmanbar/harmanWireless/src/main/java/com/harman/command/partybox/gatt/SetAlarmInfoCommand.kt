package com.harman.command.partybox.gatt

import android.util.Log
import com.harman.command.common.GeneralGattCommand
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.IdentifyDeviceCommand.Companion.ackIdentifyCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.log.Logger
import com.harman.util.append
import com.harman.util.getIntByteArray
import com.harman.util.toOneByte

/**
 * <AUTHOR>
 * version：1.0
 * date：2024/5/15
 * desc：SetAlarmInfo
 *
 */
class SetAlarmInfoCommand(val alarm: AlarmInfo.Alarm?, val setting: AlarmInfo.Setting?, val cmd: AlarmInfo.Command) : GeneralGattCommand() {

    init {
        super.commandID = GattPacketFormat.SET_ALARM_INFO
        super.responseCommandIDs.add(GattPacketFormat.DEV_ACK)
        var cmdPayload = byteArrayOf()
        when (cmd) {
            AlarmInfo.Command.FunctionalityCreate -> {
                val command = AlarmInfo.Command.FunctionalityCreate.cmd.toByte()
                val bytes = byteArrayOf(AlarmInfo.Command.FunctionalityCreate.value!!.toByte())

                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                alarm?.let { cmdPayload = append(src = cmdPayload, bytes = buildCreateAlarmPayload(it,false)) }
            }

            AlarmInfo.Command.FunctionalityRemove -> {
                var command = AlarmInfo.Command.FunctionalityRemove.cmd.toByte()
                var bytes = byteArrayOf(AlarmInfo.Command.FunctionalityRemove.value!!.toByte())
                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                command = AlarmInfo.Command.ID.cmd.toByte()

                alarm?.alarmID?.let {
                    var alarmIDBytes = byteArrayOf(command, 2)
                    alarmIDBytes = append(src = alarmIDBytes, bytes = getIntByteArray(it))
                    cmdPayload = append(src = cmdPayload, bytes = alarmIDBytes)
                }
            }

            AlarmInfo.Command.FunctionalityUpdate -> {
                val command = AlarmInfo.Command.FunctionalityUpdate.cmd.toByte()
                val bytes = byteArrayOf(AlarmInfo.Command.FunctionalityUpdate.value!!.toByte())

                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                alarm?.let { cmdPayload = append(src = cmdPayload, bytes = buildCreateAlarmPayload(it,true)) }
            }

            AlarmInfo.Command.IsActive -> {
                var command = AlarmInfo.Command.FunctionalityUpdate.cmd.toByte()
                var bytes = byteArrayOf(AlarmInfo.Command.FunctionalityUpdate.value!!.toByte())
                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)


                alarm?.let {
                    command = AlarmInfo.Command.IsActive.cmd.toByte()
                    bytes = if (it.isActiveAlarm == true) byteArrayOf(1) else byteArrayOf(0)
                    cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                    //append id
                    it.alarmID?.let { id ->
                        command = AlarmInfo.Command.ID.cmd.toByte()
                        var alarmIDBytes = byteArrayOf(command, 2)
                       val idBytes= getIntByteArray(id)

                        alarmIDBytes = append(src = alarmIDBytes, bytes =idBytes)
                        cmdPayload = append(src = cmdPayload, bytes = alarmIDBytes)
                    }
                }
            }

            AlarmInfo.Command.FunctionalityUpdateSetting -> {
                var command = AlarmInfo.Command.FunctionalityUpdateSetting.cmd.toByte()
                var bytes = byteArrayOf(AlarmInfo.Command.FunctionalityUpdateSetting.value!!.toByte())
                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                setting?.let {
                    command = AlarmInfo.Command.SnoozeStatus.cmd.toByte()
                    bytes = if (it.snoozeStatus == true) byteArrayOf(1) else byteArrayOf(0)
                    cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)
                }
            }

            AlarmInfo.Command.FunctionalityPlayDemo -> {
                var command = AlarmInfo.Command.FunctionalityPlayDemo.cmd.toByte()
                var bytes = byteArrayOf(AlarmInfo.Command.FunctionalityPlayDemo.value!!.toByte())
                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                alarm?.wakeupSound?.let {
                    command = AlarmInfo.Command.WakeupSound.cmd.toByte()
                    //2 bytes
                    bytes = byteArrayOf(command, 2, it.type.code.toByte(), it.index.toByte())
                    cmdPayload = append(src = cmdPayload, bytes = bytes)
                }
                alarm?.volume?.let {
                    command = AlarmInfo.Command.Volume.cmd.toByte()
                    cmdPayload = append(src = cmdPayload, cmd = command, bytes = byteArrayOf(it.toByte()))
                }
            }
            AlarmInfo.Command.FunctionalityStopPlayDemo -> {
                var command = AlarmInfo.Command.FunctionalityStopPlayDemo.cmd.toByte()
                var bytes = byteArrayOf(AlarmInfo.Command.FunctionalityStopPlayDemo.value!!.toByte())
                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                alarm?.wakeupSound?.let {
                    command = AlarmInfo.Command.WakeupSound.cmd.toByte()
                    //2 bytes
                    bytes = byteArrayOf(command, 2, it.type.code.toByte(), it.index.toByte())
                    cmdPayload = append(src = cmdPayload, bytes = bytes)
                }
                alarm?.volume?.let {
                    command = AlarmInfo.Command.Volume.cmd.toByte()
                    cmdPayload = append(src = cmdPayload, cmd = command, bytes = byteArrayOf(it.toByte()))
                }
            }

            AlarmInfo.Command.FunctionalityPreviewVolume -> {
                var command = AlarmInfo.Command.FunctionalityPreviewVolume.cmd.toByte()
                var bytes = byteArrayOf(AlarmInfo.Command.FunctionalityPreviewVolume.value!!.toByte())
                cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)

                alarm?.wakeupSound?.let {
                    command = AlarmInfo.Command.WakeupSound.cmd.toByte()
                    //2 bytes
                    bytes = byteArrayOf(command, 2, it.type.code.toByte(), it.index.toByte())
                    cmdPayload = append(src = cmdPayload, bytes = bytes)
                }

                alarm?.volume?.let {
                    command = AlarmInfo.Command.Volume.cmd.toByte()
                    bytes = byteArrayOf(it.toByte())
                    cmdPayload = append(src = cmdPayload, cmd = command, bytes = bytes)
                }
            }

            else -> {

            }

        }
        //set current payload
        super.payload = cmdPayload

        Log.d(TAG, "ALARM_RELATED $TAG init content:${dataHexString()}")
    }

    override fun onNotify(device: BaseBTDevice<*, *>, receivedCommand: IGeneralCommand): Boolean {
        if (device !is PartyBoxBTDevice) {
            return false
        }

        Log.d(TAG, "ALARM_RELATED $TAG onReceive receivedPayload: ${receivedCommand.dataHexString()}")

        if (receivedCommand.ackSetAlarmInfoCommand()) {
            Logger.d(TAG, "onNotify() >>> success")
            super.responseCommandIDs.clear()
            return true
        }

        Logger.w(TAG, "onNotify() >>> illegal")
        return false
    }

    @Throws
    private fun String.decodeHex(): ByteArray? {
        check(length % 2 == 0) { "Must have an even length" }

        return chunked(2)
            .map { it.toInt(16).toByte() }
            .toByteArray()
    }
    /**
     * build create alarm payload [AlarmInfo]
     */
    private fun buildCreateAlarmPayload(alarmInfo: AlarmInfo.Alarm, shouldAppendID: Boolean): ByteArray {
        var payload = byteArrayOf()
        var command = AlarmInfo.Command.ID.cmd.toByte()
        if (shouldAppendID) {
            //alarm id
            Log.d(TAG, "buildCreateAlarmPayload alarmID:${alarmInfo.alarmID}")
            val idBytes = alarmInfo.alarmID?.let { getIntByteArray(it) }
            payload = append(src = payload, cmd = command, bytes = idBytes)
        }
        //alarm time
        command = AlarmInfo.Command.Time.cmd.toByte()
        var  bytes = alarmInfo.alarmTime?.let { byteArrayOf(it.hour.toByte(), it.minute.toByte(), it.second.toByte()) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //light status
        command = AlarmInfo.Command.LightStatus.cmd.toByte()
        bytes = alarmInfo.isLightActive?.let { if (it) byteArrayOf(1) else byteArrayOf(0) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //light start time
        command = AlarmInfo.Command.LightStartsAt.cmd.toByte()
        bytes = alarmInfo.lightStartAt?.let { byteArrayOf(it.hour.toByte(), it.minute.toByte(), it.second.toByte()) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //repeat type
        command = AlarmInfo.Command.RepeatType.cmd.toByte()
        bytes = alarmInfo.repeatType?.let { byteArrayOf(it.code.toByte()) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //custom repeat type
        if (AlarmInfo.RepeatType.Custom == alarmInfo.repeatType) {
            command = AlarmInfo.Command.CustomRepeatDay.cmd.toByte()
            bytes = alarmInfo.repeatType?.value?.toOneByte()
            payload = append(src = payload, cmd = command, bytes = bytes)
        }
        //wakeup sound
        command = AlarmInfo.Command.WakeupSound.cmd.toByte()
        bytes = alarmInfo.wakeupSound?.let { byteArrayOf(it.type.code.toByte(), it.index.toByte()) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //is sun rise
        command = AlarmInfo.Command.IsSunrise.cmd.toByte()
        bytes = alarmInfo.isSunRise?.let { byteArrayOf(if (it) 1 else 0) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //volume
        command = AlarmInfo.Command.Volume.cmd.toByte()
        bytes = alarmInfo.volume?.let { byteArrayOf(it.toByte()) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        //is active
        command = AlarmInfo.Command.IsActive.cmd.toByte()
        bytes = alarmInfo.isActiveAlarm?.let { byteArrayOf(if (it) 1 else 0) }
        payload = append(src = payload, cmd = command, bytes = bytes)
        return payload
    }

    companion object {
        const val TAG = "SetAlarmInfoCommand"

        fun IGeneralCommand.ackSetAlarmInfoCommand(): Boolean {
            val receivedCommand = this
            val receivedPayload = receivedCommand.payload

            return GattPacketFormat.DEV_ACK == receivedCommand.commandID &&
                    null != receivedPayload &&
                    receivedPayload.size >= 2 &&
                    GattPacketFormat.SET_ALARM_INFO == receivedPayload[0] &&
                    GattPacketFormat.STATUS_CODE_SUCCESS == receivedPayload[1]
        }
    }
}