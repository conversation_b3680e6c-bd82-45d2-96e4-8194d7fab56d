package com.harman.command.partybox.spp.rsp

import com.harman.command.partybox.spp.EnumDfuOffsetStatus
import com.harman.command.common.EnumDfuStatus
import com.harman.command.partybox.spp.SppPacketFormat
import com.harman.command.partybox.spp.req.ReqDfuStartCommand
import com.harman.command.partybox.spp.req.ReqSetDfuDataCommand
import com.harman.discover.util.Tools.safetySubArray
import com.harman.discover.util.Tools.toBigEndianUInt

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/12.
 *
 * target req: [ReqDfuStartCommand] [ReqSetDfuDataCommand]
 * rsp: 0xaa2706 + DfuStatus(1 byte) + DfuOffset(4 bytes) + DfuOffsetStatus(1 byte)
 */
class RspNotiDfuStatusCommand(receivedPayload: ByteArray?) : BaseSppRspCommand(receivedPayload) {

    override val command: Byte = SppPacketFormat.Rsp.DFU_STATUS

    /**
     * [EnumDfuStatus]
     */
    val dfuStatus: EnumDfuStatus?
        get() {
            val value = receivedPayload?.safetySubArray(0, 1)?.getOrNull(0) ?: return null

            return EnumDfuStatus.from(value)
        }

    // package index. Not used yet
    val dfuOffset: UInt?
        get() = receivedPayload?.safetySubArray(1, 5)?.toBigEndianUInt()


    /**
     * [EnumDfuOffsetStatus]
     */
    val dfuOffsetStatus: EnumDfuOffsetStatus?
        get() {
            val value = receivedPayload?.safetySubArray(5, 6)?.getOrNull(0) ?: return null

            return EnumDfuOffsetStatus.from(value)
        }

    fun error(): Boolean = EnumDfuStatus.ERROR == dfuStatus
    fun ready(): Boolean = EnumDfuStatus.READY == dfuStatus
    fun downloading(): Boolean = EnumDfuStatus.DOWNLOADING == dfuStatus
    fun uploading(): Boolean = EnumDfuStatus.UPLOADING == dfuStatus

    override fun toString(): String {
        val sb = StringBuilder()

        sb.append("RspDfuStatus[")
        sb.append("dfuStatus[").append(dfuStatus?.desc).append("]")
        sb.append("dfuOffset[").append(dfuOffset).append("]")
        sb.append("dfuOffsetStatus[").append(dfuOffsetStatus?.desc).append("]")
        sb.append("]")
        sb.append(super.toString())

        return sb.toString()
    }
}