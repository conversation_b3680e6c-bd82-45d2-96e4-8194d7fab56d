package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName
import com.harman.discover.util.Tools.printList

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/22.
 *
 * {
 *      "devices":
 *      [
 *          {
 *              "id": "78669d803c63",
 *              "group_role": "1",
 *              "ota_flag": "1"
 *          },
 *          {
 *              "id": "78669d3603b27",
 *              "group_role": "1",
 *              "ota_flag": "0"
 *          }
 *          …
 *      ]
 * }
 */
data class GroupDeviceOTAStatusRsp(
    @SerializedName("devices")
    val devices: List<GroupDeviceOTAStatus>? = null
) {

    val anyInOTA: Boolean
        get() = true == devices?.any { device ->
            device.isInOTA
        }

    override fun toString(): String {
        return devices?.printList() ?: ""
    }
}

/**
 * key      Description                                     Value Type        Support Element
 *
 * id       The unique identifier for this device.          String              The value must match the id field returned in the response of getGroupParameter API call.(as described in section 4.10.10)
 *
 * group_role The group role of this device.                String              0: NONE 1: GO 2: GC
 *
 * ota_flag Indicator of whether this device is in OTA.     String              0: not in OTA. 1: in OTA
 */
data class GroupDeviceOTAStatus(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("group_role")
    val groupRole: String? = null,
    @SerializedName("ota_flag")
    val otaFlag: String? = null
) {

    val isInOTA: Boolean
        get() = "1" == otaFlag

    val isGC: Boolean
        get() = "2" == groupRole

    override fun toString(): String {
        return "id[$id] groupRole[$groupRole] otaFlag[$otaFlag]"
    }
}