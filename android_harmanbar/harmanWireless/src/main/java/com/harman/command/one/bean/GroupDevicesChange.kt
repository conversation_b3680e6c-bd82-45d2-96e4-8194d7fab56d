package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName
import com.harman.command.one.bean.upnp.AudioSyncResponse
import com.harman.command.one.bean.upnp.LipSync
import com.harman.command.one.bean.upnp.SodState
import com.harman.command.one.bean.upnp.SurroundState
import com.harman.command.one.bean.upnp.UpnpSurroundState
import com.harman.discover.info.OneRole

data class GroupDevicesChange(
    @SerializedName("devices")
    val devices: List<GroupDevicesChangeItem>?,
){
    fun getGC(): GroupDevicesChangeItem? {
        return devices?.first { it.groupRole == OneRole.GC.value }
    }
}

data class GroupDevicesChangeItem(
    @SerializedName("group_role")
    val groupRole: Int? = null,
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("payload")
    val payload: ResponseSet? = null,
)

data class ResponseSet(
    @SerializedName("rear_speaker_status")
    val rearSpeakerStatus: RearSpeakerResp? = null,

    @SerializedName("calibration")
    val calibration: Calibration? = null,

    @SerializedName("sod_state")
    val state: SodState? = null,

    @SerializedName("user_eq")
    val userEq: EQResponse? = null,

    @SerializedName("eqlist")
    val eqList: EQListResponse? = null,

    @SerializedName("battery_status")
    val batteryStatus: BatteryStatusResponse? = null,

    @SerializedName("volume_level")
    val volume: VolumeAndMuteResponse? = null,

    @SerializedName("prod_setting")
    val prodSetting: ProdSettingResponse<String,String>? = null,

    @SerializedName("surround_level")
    val surroundState: SurroundState? = null,

    @SerializedName("lip_sync")
    val audioSyncResponse: LipSync? = null,

    @SerializedName("personal_listening_mode")
    val personalListeningMode: StatusResp? = null,
)
