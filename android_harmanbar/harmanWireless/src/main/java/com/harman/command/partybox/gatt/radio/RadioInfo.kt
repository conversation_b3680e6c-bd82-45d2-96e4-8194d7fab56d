package com.harman.command.partybox.gatt.radio

import android.util.Log
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.util.append
import com.harman.util.getFloatValueWith
import com.harman.util.getFloatValueWith2
import com.harman.util.getIntFullByteArray
import com.harman.util.getIntValueWithFullByte
import com.harman.util.retTokenValueAndUnprocessedPayload
import com.harman.util.roundTo2DecimalPlaces
import com.harmanbar.ble.utils.HexUtil
import java.io.Serializable
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * <AUTHOR>
 * version：1.0
 * date：2024/9/9
 * desc：RadioInfo
 *
 */
class RadioInfo : Serializable {

    /**
     * config with default scan
     */
    var setting: Setting? = null

    /**
     * request type
     */
    var requestType: RequestType? = null

    var functionality: Command? = null

    var currentStation: RadioInfo.Station? = null

    var currentRadioType: RadioInfo.Type? = null

    /**
     * all preset station list
     */
    var presetStationList: MutableList<Station>? = mutableListOf()

    /**
     * station list scan by device
     */
    var scannedStationList: MutableList<Station>? = mutableListOf()

    enum class Command(val cmd: Int, val value: Int?, val desc: String?) {
        FunctionalityScan(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC1, desc = "1 byte 0xC1: Scan to find all as specified Radio Type."),
        FunctionalityStopScan(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC2, desc = "1 byte 0xC2: Stop Scan."),
        FunctionalityActiveStation(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC3, desc = "1 byte 0xC3: Set Active Station. Must with station’s frequency."),
        FunctionalityRemoveStation(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC4, desc = "1 byte 0xC4: Remove Station. Must with station’s frequency."),
        FunctionalityUpdateStation(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC5, desc = "1 byte 0xC5: Update preset station status/index. Must with station’s frequency."),
        FunctionalityScanResult(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC6, desc = "1 byte 0xC6: Return Scan Result."),
        FunctionalityPlay(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC7, desc = "1 byte 0xC7: Play."),
        FunctionalityStop(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC8, desc = "1 byte 0xC8: Stop."),
        FunctionalitySwitchRadioType(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xC9, desc = "1 byte Switch Radio Type."),
        FunctionalityPreviousStation(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xCA, desc = "1 byte 0xCA: Previous Station."),
        FunctionalityNextStation(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xCB, desc = "1 byte 0xCB: Next Station"),
        FunctionalityCurrentRadioList(cmd = GattPacketFormat.RadioToken.FUNCTIONALITY, value = 0xCC, desc = "1 byte 0xCC: Get Current Radio List"),
        RadioType(cmd = GattPacketFormat.RadioToken.RADIO_TYPE, value = null, desc = "1 byte	0x05: FM Radio 0x06: DAB Radio"),
        StationFrequency(cmd = GattPacketFormat.RadioToken.STATION_FREQUENCY, value = null, desc = "4 byte Int value in little endian. Unit is MHZ"),
        StationName(cmd = GattPacketFormat.RadioToken.STATION_NAME, value = null, desc = "32 byte UTF-8, max 32 bytes."),
        IsPresetStation(cmd = GattPacketFormat.RadioToken.IS_PRESET_STATION, value = null, desc = "1 byte 0x0: false 0x1: true"),
        PresetStationIndex(cmd = GattPacketFormat.RadioToken.PRESET_STATION_INDEX, value = null, desc = "1 byte Preset Station Index. Scope [1, 100]"),
        IsActiveStation(cmd = GattPacketFormat.RadioToken.IS_ACTIVE_STATION, value = null, desc = "1 byte 0x0: false 0x1: true"),
        DABRadioStationID(cmd = GattPacketFormat.RadioToken.DAB_RADIO_STATION_ID, value = null, desc = "4 byte DAB Radio Station ID"),
        PlayState(cmd = GattPacketFormat.RadioToken.PLAY_STATE, value = null, desc = "1 byte 0xC7: Playing 0xC8: Stop"),
        StartingRadioFrequency(cmd = GattPacketFormat.RadioToken.STARTING_RADIO_FREQUENCY, value = null, desc = "4 byte Int value, start value of radio frequency range"),
        EndingRadioFrequency(cmd = GattPacketFormat.RadioToken.ENDING_RADIO_FREQUENCY, value = null, desc = "4 byte Int value, end value of radio frequency range"),
        SeekStep(cmd = GattPacketFormat.RadioToken.SEEK_STEP, value = null, desc = "4 byte Int value in little endian. Unit is MHZ."),
        ManualSeekStep(cmd = GattPacketFormat.RadioToken.MANUAL_SEEK_STEP, value = null, desc = "4 byte Int value in little endian. Unit is MHZ."),
        IsScanBefore(cmd = GattPacketFormat.RadioToken.IS_SCAN_BEFORE, value = null, desc = "1 byte 0x00: false 0x01: true"),
        StationScanStatus(
            cmd = GattPacketFormat.RadioToken.STATION_SCAN_STATUS,
            value = null,
            desc = "1 byte, 0x00:Never scanned, 0x01:scanned and has some stations, 0x02: scanned and no stations"
        );

        companion object {
            @JvmStatic
            fun getCommandByCode(code: Int): Command? = entries.find { value -> value.cmd == code }

            @JvmStatic
            fun getCommandByCode(code: Int, subCode: Int): Command? = entries.find { value -> value.cmd == code && value.value == subCode }
        }
    }

    enum class Type(val code: Byte, val desc: String) {
        FMRadio(code = 0x05, desc = "1 byte 0x05: FM Radio"),
        DABRadio(code = 0x06, desc = "1 byte 0x06: DAB Radio");

        companion object {
            @JvmStatic
            fun getTypeBy(code: Int): Type? {
                return entries.find { type -> type.code == code.toByte() }
            }
        }
    }

    enum class State(val code: Int, val desc: String) {
        Playing(code = 0xC7, desc = "1 byte 0xC7: Playing"),
        Stopped(code = 0xC8, desc = "1 byte 0xC8: Stopped");

        companion object {
            @JvmStatic
            fun getStateBy(value: Int): State? {
                return entries.find { it.code == value }
            }
        }
    }

    enum class RequestType(val type: Int, val desc: String) {
        AllPreset(type = 0x00, desc = "request all preset Radio FM and DAB."),
        FMPreset(type = 0x01, desc = "request all preset Radio FM."),
        DABPreset(type = 0x02, desc = "request all preset Radio DAB."),
        RadioSetting(type = 0x03, desc = "request radio settings."),
        LastStation(type = 0x04, desc = "request last FM Radio or DAB"),
        DeviceNotify(type = 0xff, desc = "device push/or scan result.");

        companion object {
            @JvmStatic
            fun getRequestType(type: Int?): RequestType {
                return entries.find { it.type == type } ?: AllPreset
            }
        }
    }

    class Setting : Serializable {

        /**
         * 4 bytes
         * manual seek step
         * Float value in little endian. Unit is MHZ.
         */
        var manualSeekStep: Float? = null

        /**
         * 4 bytes
         * seek step
         * Float value in little endian. Unit is MHZ.
         */
        var seekStep: Float? = null

        /**
         * 4 bytes
         * start value of radio frequency range
         * Float value in little endian. Unit is MHZ.
         */
        var startFrequency: Float? = null

        /**
         * 4 bytes
         * end value of radio frequency range
         * Float value in little endian. Unit is MHZ.
         */
        var endFrequency: Float? = null

        /**
         * Device should reply to app with scan Before
         * 0x00: false
         * 0x01: true
         */
        var isScannedBefore: Boolean? = false

        override fun toString(): String {
            return "Setting(manualSeekStep=$manualSeekStep, seekStep=$seekStep, startFrequency=$startFrequency, endFrequency=$endFrequency, isScannedBefore=$isScannedBefore)"
        }

        fun deepCopy(): Setting {
            val newSetting = Setting()
            newSetting.manualSeekStep = this.manualSeekStep
            newSetting.seekStep = this.seekStep
            newSetting.startFrequency = this.startFrequency
            newSetting.endFrequency = this.endFrequency
            newSetting.isScannedBefore = this.isScannedBefore
            return newSetting
        }

    }

    class  Station : Serializable {
        var stationID: Int? = null
        var stationName: String? = null
        var isActive: Boolean? = false
        var stationIndex: Int? = -1
        var isPreset: Boolean? = false
        var frequency: Float? = null
        var state: State? = null

        /**
         * 1 byte
         * 0x05: FM Radio
         * 0x06: DAB Radio
         */
        var radioType: Type? = null
        var stationScanStatus: Int? = null

        // 手动深拷贝方法
        fun deepCopy(): Station {
            val newStation = Station()
            newStation.stationID = this.stationID
            newStation.stationName = this.stationName
            newStation.isActive = this.isActive
            newStation.stationIndex = this.stationIndex
            newStation.isPreset = this.isPreset
            newStation.frequency = this.frequency
            newStation.state = this.state
            newStation.radioType = this.radioType
            newStation.stationScanStatus = this.stationScanStatus

            return newStation
        }

        fun activeStationInfoBytes(): ByteArray {
            var bytes = byteArrayOf()
            //type
            radioType?.let { bytes = append(src = bytes, cmd = Command.RadioType.cmd.toByte(), bytes = byteArrayOf(it.code)) }
            if (RadioInfo.Type.FMRadio == this.radioType) {
                frequency?.let { frequency ->
                    (frequency * 1000).toInt().frequencyBytes().let { bytes = append(bytes, it) }
                }
            } else {
                stationID?.let { bytes = append(src = bytes, cmd = Command.DABRadioStationID.cmd.toByte(), bytes = getIntFullByteArray(it)) }
            }
            return bytes
        }

        fun presetInfoBytes(): ByteArray {
            var bytes = byteArrayOf()
            stationIndex?.let { bytes = append(src = bytes, cmd = Command.PresetStationIndex.cmd.toByte(), bytes = byteArrayOf(it.toByte())) }
            //append isPreset
            isPreset?.let { bytes = append(src = bytes, cmd = Command.IsPresetStation.cmd.toByte(), bytes = byteArrayOf(if (it) 1 else 0)) }
            return bytes
        }

        fun infoBytes(): ByteArray {
            var bytes = byteArrayOf()
            frequency?.let { itFrequency ->
                (itFrequency * 1000).toInt().frequencyBytes().let { bytes = append(bytes, it) }
            }
            //append stationIndex
            stationIndex?.let { bytes = append(src = bytes, cmd = Command.PresetStationIndex.cmd.toByte(), bytes = byteArrayOf(it.toByte())) }
            //append isPreset
            isPreset?.let { bytes = append(src = bytes, cmd = Command.IsPresetStation.cmd.toByte(), bytes = byteArrayOf(if (it) 1 else 0)) }
            //append isActive
            isActive?.let { bytes = append(src = bytes, cmd = Command.IsActiveStation.cmd.toByte(), bytes = byteArrayOf(if (it) 1 else 0)) }
            //station id
            stationID?.let { bytes = append(src = bytes, cmd = Command.DABRadioStationID.cmd.toByte(), bytes = getIntFullByteArray(it)) }
            //type
            radioType?.let { bytes = append(src = bytes, cmd = Command.RadioType.cmd.toByte(), bytes = byteArrayOf(it.code)) }
            return bytes
        }

        companion object {
            @JvmStatic
            fun Float.frequencyBytes(): ByteArray {
                val valueBytes = ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putFloat(this).array()
                //command and length byteArray
                val commandBytes = byteArrayOf(Command.StationFrequency.cmd.toByte(), 4)
                //append values bytes
                return append(src = commandBytes, bytes = valueBytes)
            }

            @JvmStatic
            fun Int.frequencyBytes(): ByteArray {
                val valueBytes = ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(this).array()
                //command and length byteArray
                val commandBytes = byteArrayOf(Command.StationFrequency.cmd.toByte(), 4)
                //append values bytes
                return append(src = commandBytes, bytes = valueBytes)
            }

        }

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Station

            if (Type.FMRadio == radioType) {
                if (frequency != other.frequency) return false
            } else {
                if (stationName != null && stationName != other.stationName) return false
            }

            return true
        }

        override fun hashCode(): Int {
            var result = stationName?.hashCode() ?: 0
            result = 31 * result + (frequency?.hashCode() ?: 0)
            return result
        }

        override fun toString(): String {
            return "Station(stationID=$stationID, stationName=$stationName, isActive=$isActive, stationIndex=$stationIndex, isPreset=$isPreset, frequency=$frequency, state=$state, radioType=$radioType, stationScanStatus=$stationScanStatus)"
        }

    }

    fun deepCopy(): RadioInfo {
        val newRadioInfo = RadioInfo()
        newRadioInfo.setting = this.setting?.deepCopy()
        newRadioInfo.requestType = this.requestType
        newRadioInfo.functionality = this.functionality
        newRadioInfo.currentStation = this.currentStation?.deepCopy()
        newRadioInfo.currentRadioType = this.currentRadioType
        newRadioInfo.presetStationList = this.presetStationList
        newRadioInfo.scannedStationList = this.scannedStationList
        return newRadioInfo
    }

    fun parseStationList(receivedPayload: String): MutableList<Station> {
        val list = mutableListOf<Station>()
        var unprocessedPayload = receivedPayload
        try {
            while (unprocessedPayload.isNotEmpty()) {
                val payloadLength = unprocessedPayload.substring(0, 2).toInt(16)
                val hexStringLength = payloadLength * 2 + 2
                val currentPayload = unprocessedPayload.substring(2, hexStringLength)
                Log.d(TAG, "buildRadioInfo parseStationList 0 payloadLength:$payloadLength")
                Log.d(TAG, "buildRadioInfo parseStationList 1 currentPayload:$currentPayload")

                parseStation(currentPayload)?.let { list.add(it) }

                unprocessedPayload = if (hexStringLength < unprocessedPayload.length) unprocessedPayload.substring(hexStringLength) else ""

                Log.d(TAG, "buildRadioInfo parseStationList 2 unprocessedPayload:$unprocessedPayload")
            }
        } catch (e: Exception) {
            Log.e(TAG, "buildRadioInfo parseStationList 3 Exception:${e.message.toString()}")
        }
        return list
    }

    private fun parseStation(currentPayload: String): RadioInfo.Station? {
        val station = Station()
        var unprocessedPayload = currentPayload
        while (unprocessedPayload.isNotEmpty()) {
            val tokenID = unprocessedPayload.substring(0, 2)
            val (tokenValue, remainingPayload) = retTokenValueAndUnprocessedPayload(unprocessedPayload)
            unprocessedPayload = remainingPayload ?: ""
            //if token value is  just continue
            if (tokenValue.isNullOrEmpty()) continue
            val command = Command.getCommandByCode(tokenID.toInt(16))
            Log.d(TAG, "parseStation command:$command, tokenValue:$tokenValue")
            when (command) {
                RadioInfo.Command.DABRadioStationID -> station.stationID = getIntValueWithFullByte(tokenValue)
                RadioInfo.Command.StationName -> station.stationName = HexUtil.hexStringToString(tokenValue)
                RadioInfo.Command.IsActiveStation -> station.isActive = tokenValue.toInt(16) == 1
                RadioInfo.Command.PresetStationIndex -> station.stationIndex = tokenValue.toInt(16)
                RadioInfo.Command.IsPresetStation -> station.isPreset = tokenValue.toInt(16) == 1
                RadioInfo.Command.StationFrequency -> {
                    val intValue = ByteBuffer.wrap(tokenValue.chunked(2).map { it.toInt(16).toByte() }.toByteArray()).order(ByteOrder.LITTLE_ENDIAN).int
                    Log.d(TAG, "parseStation stationFrequency1:${intValue}")
                    Log.d(TAG, "parseStation stationFrequency2:${getFloatValueWith2(tokenValue)}")

                    station.frequency = tokenValue.frequencyValue2()
                }

                RadioInfo.Command.PlayState -> station.state = RadioInfo.State.getStateBy(tokenValue.toInt(16))
                RadioInfo.Command.RadioType -> station.radioType = RadioInfo.Type.getTypeBy(tokenValue.toInt(16))
                RadioInfo.Command.StationScanStatus -> station.stationScanStatus = tokenValue.toInt(16)
                else -> Unit
            }
        }
        Log.d(TAG, "parseStation station:$station")
        return station
    }

    fun parseRadioConfig(receivedPayload: String): Setting? {
        val payloadLen = receivedPayload.substring(0, 2).toInt(16)
        if (payloadLen == 0) return null
        val setting = Setting()
        var unprocessedPayload = receivedPayload.substring(2)
        Log.d(TAG, "parseRadioConfig :$unprocessedPayload")
        while (unprocessedPayload.isNotEmpty()) {
            val tokenID = unprocessedPayload.substring(0, 2)
            val (tokenValue, remainingPayload) = retTokenValueAndUnprocessedPayload(unprocessedPayload)
            unprocessedPayload = remainingPayload ?: ""
            if (tokenValue.isNullOrEmpty()) continue
            val command = Command.getCommandByCode(tokenID.toInt(16))
            Log.d(TAG, "parseRadioConfig command:$command, tokenValue:$tokenValue")
            when (command) {
                RadioInfo.Command.StartingRadioFrequency -> setting.startFrequency = tokenValue.frequencyValue2()
                RadioInfo.Command.EndingRadioFrequency -> setting.endFrequency = tokenValue.frequencyValue2()
                RadioInfo.Command.SeekStep -> setting.seekStep = tokenValue.frequencyValue2()
                RadioInfo.Command.ManualSeekStep -> setting.manualSeekStep = tokenValue.frequencyValue2()
                RadioInfo.Command.IsScanBefore -> setting.isScannedBefore = tokenValue.toInt(16) == 1
                else -> Unit
            }
        }
        Log.d(TAG, "parseRadioConfig setting:$setting")
        return setting
    }

    private fun String.frequencyValue() = getFloatValueWith(this).roundTo2DecimalPlaces()

    private fun String.frequencyValue2() = getFloatValueWith2(this).roundTo2DecimalPlaces()

    fun cut2MaxLength(tokenValue: String) =
        (tokenValue ?: "").let { str ->
            if (str.length > MAX_STATION_NAME_BYTE_LENGTH) {
                str.subSequence(0, MAX_STATION_NAME_BYTE_LENGTH).toString()
            } else {
                str
            }
        }

    override fun toString(): String {
        return "RadioInfo(setting=$setting, requestType=$requestType, functionality=$functionality, " +
                "currentStation=$currentStation, currentRadioType=$currentRadioType, " +
                "presetStationList=$presetStationList, scannedStationList=$scannedStationList)"
    }

    companion object {
        private const val TAG = "RadioInfoTAG"
        const val MAX_STATION_NAME_BYTE_LENGTH = 32
    }

}