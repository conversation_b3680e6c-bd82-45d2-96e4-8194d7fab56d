package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/22.
 *
 * { "error_code": "0", "calibration": "1", "googlecast": "1" }
 *
 * key Description          Value Type          Support elements
 *
 * calibration Indicates if calibration is needed.
 * String
 * 0: No need to perform calibration. 1: Need to perform calibration.
 *
 * googlecast Indicates if Google Cast user confirmation is needed.
 * String
 * 0: No need to perform Google Cast user confirmation. 1: Need to perform Google Cast user confirmation.
 */
data class GetGroupDevicesFlagRsp(
    @SerializedName("error_code")
    val errorCode: Int? = null,
    @SerializedName("calibration")
    val calibration: String? = null,
    @SerializedName("googlecast")
    val googleCast: String? = null
) : Serializable {

    val needCalibration: Boolean
        get() = "1" == calibration

    val needGoogleCast: <PERSON>olean
        get() = "1" == googleCast

    override fun toString(): String {
        return "errorCode[$errorCode] calibration[$calibration] googleCast[$googleCast]"
    }
}