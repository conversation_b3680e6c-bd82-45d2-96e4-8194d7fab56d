package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName

/**
 * {
 *   "error_code":"0",
 *   "light_info":{
 *     "enable": "1",
 *     "active_pattern":{
 *       "id":"ocean",
 *       "level": "70"
 *     },
 *     "support_list":[
 *         {
 *           "id": "ocean",
 *           "level ": "70"
 *         },
 *         {
 *           "id": "aurora",
 *           "level ": "70"
 *         }
 *     ],
 *     "brightness": "50",
 *     "dynamic_level": "1",
 *     "light_element": "1"
 *   }
 * }
 *
 * @property errorCode
 * @property activePattern
 * @property brightness: [0, 100]
 * @property dynamicLevel: 1: low 2: mid 3: high
 * @property lightElement: 1: all 2: subwoofer 3 satellite 4: disc 5: sculpture
 *      For Aura Studio 5 Wifi, UI design rename the lightElement name:
 *      Disc ---> Core
 *      Sculpture ---> Projection
 *      And UI design only supports 3 lightElements: 1: all, 4: Core, 5: Projection
 */
data class GetLightInfoResponse(
    @SerializedName("error_code")
    val errorCode: String? = null,
    @SerializedName("light_info")
    val lightInfo: LightInfo? = null
) {
    fun success(): Boolean = "0" == errorCode
}

/**
 *
 * @property enable: "0"--off, "1"--on
 * @property activePattern
 * @property supportList
 * @property brightness: [0, 100]
 * @property dynamicLevel: 1: low 2: mid 3: high
 * @property lightElement: 1: all 2: subwoofer 3 satellite 4: disc 5: sculpture
 *      For Aura Studio 5 Wifi, UI design rename the lightElement name:
 *      Disc ---> Core
 *      Sculpture ---> Projection
 *      And UI design only supports 3 lightElements: 1: all, 4: Core, 5: Projection
 */
data class LightInfo(
    @SerializedName("enable")
    var enable: String? = null,
    @SerializedName("active_pattern")
    var activePattern: Pattern? = null,
    @SerializedName("support_list")
    var supportList: List<Pattern>? = null,
    @SerializedName("brightness")
    var brightness: String? = null,
    @SerializedName("dynamic_level")
    var dynamicLevel: String? = null,
    @SerializedName("light_element")
    var lightElement: String? = null
)

