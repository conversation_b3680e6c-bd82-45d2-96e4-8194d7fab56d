package com.harman.command.partybox.gatt

import com.harman.command.common.GeneralGattCommand
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.util.Tools.isNullOrEmpty
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil

/**
 * <AUTHOR>
 * version：1.0
 * date：2024/5/15
 * desc：ReqAlarmInfoCommand
 *
 */
class ReqAlarmInfoCommand(val requestType: AlarmInfo.RequestType) : GeneralGattCommand() {

    init {
        super.commandID = GattPacketFormat.REQ_ALARM_INFO
        super.payload = byteArrayOf(requestType.type)
        super.responseCommandIDs.add(GattPacketFormat.RET_ALARM_INFO)
        Logger.d(TAG, "ALARM_RELATED $TAG init command:${dataHexString()}")
    }

    override fun onNotify(device: BaseBTDevice<*, *>, receivedCommand: IGeneralCommand): Boolean {
        if (device !is PartyBoxBTDevice) {
            return false
        }
        Logger.d(TAG, "onNotify:commandID = ${receivedCommand.commandID}, payload = ${receivedCommand.dataHexString()}")

        if (!receivedCommand.ackReqAlarmInfoCommand()) {
            Logger.d(TAG, "onNotify() >>> illegal")
            return false
        }

        super.responseCommandIDs.clear()
        Logger.d(TAG, "onNotify() >>> success")

        val receivedPayload = HexUtil.encodeHexStr(receivedCommand.payload)
        Logger.d(TAG, "ALARM_RELATED $TAG onReceive called :$receivedPayload")
        parseAlarmInfo(device, receivedPayload)

        return true
    }

    companion object {
        const val TAG = "ReqAlarmInfoCommand"

        fun IGeneralCommand.ackReqAlarmInfoCommand(): Boolean {
            val receivedCommand = this

            return GattPacketFormat.RET_ALARM_INFO == receivedCommand.commandID &&
                    !receivedCommand.payload.isNullOrEmpty()
        }

        fun parseAlarmInfo(device: PartyBoxBTDevice, receivedPayload: String) {
            synchronized(receivedPayload) {
                val requestType = AlarmInfo.RequestType.getRequestType(receivedPayload.substring(0, 2).toInt(16))
                Logger.d(TAG, "parseAlarmInfo requestType:$requestType")
                val alarmInfo = device.alarmInfo ?: AlarmInfo()
                if (receivedPayload.length <= 4) {
                    Logger.d(TAG, "parseAlarmInfo receivedPayload: alarm info is empty")
                    return
                }

                when (requestType) {
                    AlarmInfo.RequestType.AlarmSetting -> {
                        alarmInfo.setting = alarmInfo.parseAlarmSetting(receivedPayload.substring(2))
                    }

                    else -> {
                        val parseAlarmList = alarmInfo.parseAlarmList(receivedPayload.substring(2))
                        alarmInfo.alarmList?.let { itAlarmList ->
                            itAlarmList.takeIf { it.isNotEmpty() }?.let {
                                val updateAlarmList = updateAlarmList(itAlarmList, parseAlarmList)
                                itAlarmList.clear()
                                itAlarmList.addAll(updateAlarmList)
                            }
                        } ?: let {
                            alarmInfo.alarmList = parseAlarmList
                        }


                    }

                }
                Logger.d(TAG, "parseAlarmInfo: alarmInfo:$alarmInfo")
                device.alarmInfo = alarmInfo
            }

        }

        private fun updateAlarmList(
            alarmList: MutableList<AlarmInfo.Alarm>,
            updatedAlarmList: MutableList<AlarmInfo.Alarm>?
        ): MutableList<AlarmInfo.Alarm> {
            if (alarmList.isNotEmpty()) {
                // 将现有列表转换为Map，以alarmID为键
                val alarmMap = alarmList.filter { it.alarmID != null }.associateBy { it.alarmID }

                updatedAlarmList?.filter { it.alarmID != null }?.let { itUpdatedAlarmList ->
                    for (updatedAlarm in itUpdatedAlarmList) {

                        if (alarmMap.containsKey(updatedAlarm.alarmID)) {
                            val index = alarmList.indexOfFirst { it.alarmID == updatedAlarm.alarmID }
                            if (index != -1) {
                                alarmList[index] = updatedAlarm
                            }
                        } else {
                            // 如果没有找到相同的alarmID，则添加
                            alarmList.add(updatedAlarm)
                        }

                    }

                }

            }

            return sortedAlarmList(alarmList.distinctLast())
        }

        private fun sortedAlarmList(alarmList: MutableList<AlarmInfo.Alarm>): MutableList<AlarmInfo.Alarm> {

            // Sort by hour, minute, second in Timee
            val sortedList = alarmList.sortedWith(
                compareBy(
                    { it.alarmTime?.hour },
                    { it.alarmTime?.minute },
                    { it.alarmTime?.second }
                )
            )

            return sortedList.toMutableList()
        }

        private fun <T> MutableList<T>.distinctLast(): MutableList<T> {
            return this.foldRight(mutableListOf<T>()) { item, acc ->
                if (item !in acc) acc.add(0, item)
                acc
            }
        }

    }
}