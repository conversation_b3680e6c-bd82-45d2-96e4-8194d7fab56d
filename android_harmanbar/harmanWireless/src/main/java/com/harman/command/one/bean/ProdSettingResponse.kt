package com.harman.command.one.bean

class ProdSettingResponse<K, V>(initialCapability: Int = 16): HashMap<String, String>(initialCapability) {

    val isPureVoiceOn: Boolean
        get() = ON.equals(this[ProdSetting.PureVoice.value], true)

    val isPureVoiceOff: Boolean
        get() = OFF.equals(this[ProdSetting.PureVoice.value], true)

    val isFlexListeningStereo: Boolean
        get() = FLEX_LISTENING_STEREO.toString().equals(this[ProdSetting.FlexListening.value], true)

    val isFlexListeningMono: Boolean
        get() = FLEX_LISTENING_MONO.toString().equals(this[ProdSetting.FlexListening.value], true)

    val isFlexListeningOff: Boolean
        get() = OFF.equals(this[ProdSetting.PureVoice.value], true)

    val isDeepSleepOn: <PERSON>olean
        get() = FLEX_LISTENING_MONO.toString().equals(this[ProdSetting.DeepSleep.value], true)

    val isDeepSleepOff: Boolean
        get() = OFF.equals(this[ProdSetting.DeepSleep.value], true)

    companion object {
        const val ON = "1"
        const val OFF = "0"
        const val FLEX_LISTENING_STEREO = 2
        const val FLEX_LISTENING_MONO = 1
    }
}

enum class ProdSetting(val value: String) {
    FlexListening("FlexListening"),
    DeepSleep("DeepSleep"),
    PureVoice("PureVoice"),
    SmartDetails("SmartDetails")
}
