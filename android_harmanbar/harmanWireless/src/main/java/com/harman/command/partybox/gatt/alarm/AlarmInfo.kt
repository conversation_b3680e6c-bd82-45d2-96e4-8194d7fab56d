package com.harman.command.partybox.gatt.alarm

import com.harman.discover.util.Tools.md5ForString
import com.harman.log.Logger
import com.harman.util.getIntValueWith
import com.harman.util.retTokenValueAndUnprocessedPayload
import java.io.Serializable
import kotlin.random.Random

/**
 * <AUTHOR>
 * version：1.0
 * date：2024/5/14
 * desc：AlarmInfo
 *
 */
class AlarmInfo : Serializable {

    var setting: Setting? = null

    var alarmList: MutableList<Alarm>? = mutableListOf()

    class Setting : Serializable {
        var snoozeStatus: Boolean? = false
        override fun toString(): String {
            return "Setting(snoozeStatus=$snoozeStatus)"
        }
    }

    class Alarm : Serializable {
        var alarmID: Int? = null
        var alarmTime: Timee? = null
        var isActiveAlarm: Boolean? = false
        var lightStartAt: Timee? = null
        var isLightActive: Boolean? = false
        var repeatType: RepeatType? = null
        var wakeupSound: WakeupSound? = null
        var isSunRise: Boolean? = false

        //Volume level, scope [0~100]
        var volume: Int? = 0

        // 手动深拷贝方法
        fun deepCopy(): Alarm {
            val newAlarm = Alarm()
            newAlarm.alarmID = this.alarmID
            newAlarm.alarmTime = this.alarmTime?.copy()
            newAlarm.isActiveAlarm = this.isActiveAlarm
            newAlarm.lightStartAt = this.lightStartAt?.copy()
            newAlarm.isLightActive = this.isLightActive
            newAlarm.repeatType = this.repeatType
            newAlarm.wakeupSound = this.wakeupSound?.copy()
            newAlarm.isSunRise = this.isSunRise
            newAlarm.volume = this.volume
            return newAlarm
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Alarm

            if (alarmID != other.alarmID) return false

            return true
        }

        override fun hashCode(): Int {
            val result = alarmID?.hashCode() ?: 0
            return result
        }

        override fun toString(): String {
            return "Alarm(alarmID=$alarmID, alarmTime=$alarmTime, isActiveAlarm=$isActiveAlarm, lightStartAt=$lightStartAt, isLightActive=$isLightActive, repeatType=$repeatType, wakeupSound=$wakeupSound, isSunRise=$isSunRise, volume=$volume)"
        }

        companion object {
            private const val MAX_ID_BYTE_LENGTH = 4

            @JvmStatic
            fun geneAlarmID(): String {
                val sb = StringBuilder()
                sb.append(System.currentTimeMillis())
                sb.append(Random.nextInt(1, 10000))

                var strMD5: String = md5ForString(sb.toString()) ?: ""
                if (strMD5.isBlank()) {
                    strMD5 = System.currentTimeMillis().toString()
                }

                strMD5 = if (strMD5.length > MAX_ID_BYTE_LENGTH) {
                    strMD5.substring(0, MAX_ID_BYTE_LENGTH)
                } else {
                    strMD5
                }

                // Make sure first 2 chars will not be both '0'.
                if (strMD5.length >= 2 && "00" == strMD5.substring(0, 2)) {
                    strMD5 = strMD5.replaceFirst("00", "01")
                }

                return strMD5
            }
        }

    }

    enum class RequestType(val type: Byte, val desc: String) {
        AlarmList(type = 0x00, desc = "request all alarms."),
        Alarm(type = 0x01, desc = "request alarm with specified Alarm ID."),
        AlarmSetting(type = 0x02, desc = "request alarm global settings.");

        companion object {
            @JvmStatic
            fun getRequestType(type: Int): RequestType {
                return RequestType.values().find { it.type == type.toByte() } ?: AlarmList
            }
        }
    }

    enum class Functionality(val code: Int, val desc: String) {
        CreateAlarm(code = 0xC1, desc = "Create Alarm"),
        UpdateAlarm(code = 0xC2, desc = "Update Alarm"),
        RemoveAlarm(code = 0xC3, desc = "Remove Alarm"),
        TurnOnTime(code = 0xC4, desc = "Turn on Time Display on Device"),
        TurnOffTime(code = 0xC5, desc = "Turn off Time Display on Device"),
        SetLightBeginning(code = 0xC6, desc = " Set Light beginning");

        companion object {
            @JvmStatic
            fun getByCode(code: Int): Functionality = Functionality.values().find { value -> value.code == code } ?: CreateAlarm
        }
    }

    enum class AlarmType(val code: Int, val desc: String) {
        Sunrise(code = 0x01, desc = "Sunrise"), Normal(code = 0x02, desc = "Normal");

        companion object {
            @JvmStatic
            fun getTypeByCode(code: Int): AlarmType = AlarmType.values().find { value -> value.code == code } ?: Sunrise
        }
    }

    enum class RepeatType(val code: Int, var value: String? = null, val desc: String) {
        EveryDay(code = 0x01, value = null, desc = "Everyday"),
        Once(code = 0x02, value = null, desc = "Once"),
        Weekday(code = 0x03, value = null, desc = "Weekday"),
        Weekend(code = 0x04, value = null, desc = "Weekend"),
        Custom(code = 0xC1, value = "0000000", desc = "Custom");

        companion object {
            @JvmStatic
            fun getTypeByCode(code: Int): RepeatType = RepeatType.values().find { value -> value.code == code } ?: EveryDay
        }

        override fun toString(): String {
            return "RepeatType(code=$code, value=$value, desc='$desc')"
        }

    }

    enum class WeekDay(val code: Int, val desc: String) {
        Sunday(code = 0, "Sunday"),
        Monday(code = 1, "Monday"),
        Tuesday(code = 2, "Tuesday"),
        Wednesday(code = 3, "Wednesday"),
        Thursday(code = 4, "Thursday"),
        Friday(code = 5, "Friday"),
        Saturday(code = 6, "Saturday"),
    }

    data class Timee(var hour: Int, var minute: Int, val second: Int) : Serializable

    enum class Command(val cmd: Int, val value: Int?, val desc: String?) {
        FunctionalityCreate(cmd = 0x71, value = 0xC1, desc = "1 bytes : Create Alarm."),
        FunctionalityUpdate(cmd = 0x71, value = 0xC2, desc = "1 bytes : Update Alarm."),
        FunctionalityRemove(cmd = 0x71, value = 0xC3, desc = "1 bytes : Remove Alarm."),
        FunctionalityPreviewVolume(cmd = 0x71, value = 0xC4, desc = "1 bytes : Preview Volume."),
        FunctionalityPlayDemo(cmd = 0x71, value = 0xC5, desc = "1 bytes : Play Demo (with Wake-up sound)."),
        FunctionalityUpdateSetting(cmd = 0x71, value = 0xC6, desc = "1 bytes : Update Alarm Global Setting."),
        FunctionalityStopPlayDemo(cmd = 0x71, value = 0xC7, desc = "1 bytes : Stop play Demo (with Wake-up sound)."),
        ID(cmd = 0x72, value = null, desc = "2 bytes Unique ID Generated by App."),
        Type(cmd = 0x73, value = null, desc = "2 bytes Unique ID Generated by App."),
        RepeatType(cmd = 0x74, value = null, desc = "2 bytes Unique ID Generated by App."),
        CustomRepeatDay(cmd = 0x75, value = null, desc = "2 bytes Unique ID Generated by App."),
        Time(cmd = 0x76, value = null, desc = "2 bytes Unique ID Generated by App."),
        IsSunrise(cmd = 0x77, value = null, desc = "2 bytes Unique ID Generated by App."),
        IsActive(cmd = 0x78, value = null, desc = "2 bytes Unique ID Generated by App."),
        Volume(cmd = 0x79, value = null, desc = "2 bytes Unique ID Generated by App."),
        WakeupSound(cmd = 0x7A, value = null, desc = "2 bytes WakeupSound"),
        LightStartsAt(cmd = 0x7C, value = null, desc = "3 bytes 1st byte: hour 2nd byte: minute 3rd byte: second."),
        LightStatus(cmd = 0x7D, value = null, desc = "1 byte 0x00: off 0x01: on"),
        SnoozeStatus(cmd = 0x7E, value = null, desc = "1 byte 0x00: off 0x01: on");

        companion object {
            @JvmStatic
            fun getCommandByCode(code: Int): Command = Command.values().find { value -> value.cmd == code } ?: FunctionalityCreate
        }
    }

    data class WakeupSound(val type: SoundType, val index: Int) : Serializable

    enum class SoundType(val code: Int, val desc: String) {
        PresetRadioStation(code = 0x01, desc = "PresetRadioStation"), RingTone(code = 0x02, desc = "RingTone");

        companion object {
            fun geTypeWithCode(code: Int): SoundType = SoundType.values().find { value -> value.code == code } ?: PresetRadioStation
        }
    }

    enum class AmbientAudio(val code: Int, val desc: String) {
        Forest(code = 0x01, desc = "Forest"),
        Rain(code = 0x02, desc = "Rain"),
        Ocean(code = 0x03, desc = "Ocean")
    }

    enum class RingTone(val code: Int, val desc: String) {
        Daybreak(code = 0x01, desc = "Daybreak"),
        Sunbeam(code = 0x02, desc = "Sunbeam"),
        SoftChimes(code = 0x03, desc = "Soft Chimes"),
        Forest(code = 0x04, desc = "Forest"),
        Buzzer(code = 0x05, desc = "Buzzer")
    }

    fun parseAlarmSetting(receivedPayload: String): Setting? {
        val payloadLen = receivedPayload.substring(0, 2).toInt(16)
        if (payloadLen == 0) return null
        var unprocessedPayload = receivedPayload.substring(2)
        Logger.d(TAG, "parseAlarmSetting :$unprocessedPayload")
        val setting = Setting()
        while (unprocessedPayload.isNotEmpty()) {
            val tokenID = unprocessedPayload.substring(0, 2)
            val (tokenValue, remainingPayload) = retTokenValueAndUnprocessedPayload(unprocessedPayload)
            unprocessedPayload = remainingPayload ?: ""
            //if token value is  just continue
            if (tokenValue.isNullOrEmpty()) continue
            val command = Command.getCommandByCode(tokenID.toInt(16))
            Logger.d(TAG, "parseAlarmSetting command:$command, tokenValue:$tokenValue")
            when (command) {
                Command.SnoozeStatus -> setting.snoozeStatus = tokenValue.toInt(16) == 1
                else -> Unit
            }
        }
        Logger.d(TAG, "ALARM_RELATED parseAlarmSetting: $setting")
        return setting
    }

    fun parseAlarmList(receivedPayload: String): MutableList<Alarm> {
        val list = mutableListOf<Alarm>()
        var unprocessedPayload = receivedPayload
        var payloadLength = 0
        while (unprocessedPayload.isNotEmpty()) {
            payloadLength = unprocessedPayload.substring(0, 2).toInt(16)
            val hexStringLength = payloadLength * 2 + 2
            if (hexStringLength > unprocessedPayload.length) {
                Logger.d(TAG, "parseAlarmList: buildAlarmInfoList -1:${unprocessedPayload.length},hexStringLength=$hexStringLength")
                break
            }
            val currentPayload = unprocessedPayload.substring(2, hexStringLength)
            Logger.d(TAG, "parseAlarmList: buildAlarmInfoList 0:$payloadLength")
            Logger.d(TAG, "parseAlarmList: buildAlarmInfoList 1:$currentPayload")
            //create alarm info
            parseAlarm(currentPayload)?.let { list.add(it) }
            //next alarm info payload
            unprocessedPayload = if (hexStringLength < unprocessedPayload.length) unprocessedPayload.substring(hexStringLength) else ""
            Logger.d(TAG, "parseAlarmList: buildAlarmInfoList 2:$unprocessedPayload")
        }
        Logger.d(TAG, "ALARM_RELATED parseAlarmList: buildAlarmInfoList:$list")
        return list
    }

    private fun parseAlarm(receivedPayload: String): Alarm? {
        var unprocessedPayload = receivedPayload
        val alarm = Alarm()
        try {
            while (unprocessedPayload.isNotEmpty()) {
                val tokenID = unprocessedPayload.substring(0, 2)
                val (tokenValue, remainingPayload) = retTokenValueAndUnprocessedPayload(unprocessedPayload)
                unprocessedPayload = remainingPayload ?: ""
                //if token value is empty just continue
                if (tokenValue.isNullOrEmpty()) continue
                val command = Command.getCommandByCode(tokenID.toInt(16))
                Logger.d(TAG, "parseAlarm command:$command, tokenValue:$tokenValue")
                when (command) {
                    Command.ID -> alarm.alarmID = getIntValueWith(tokenValue)
                    Command.RepeatType -> alarm.repeatType = RepeatType.getTypeByCode(tokenValue.toInt(16))
                    Command.CustomRepeatDay -> alarm.repeatType = RepeatType.Custom.apply { value = tokenValue.getCustomWeekday() }
                    Command.Time -> alarm.alarmTime = getTimee(tokenValue)
                    Command.IsActive -> alarm.isActiveAlarm = tokenValue.toInt(16) == 1
                    Command.Volume -> alarm.volume = tokenValue.toInt(16)
                    Command.WakeupSound -> alarm.wakeupSound = getWakeupSound(tokenValue)
                    Command.LightStartsAt -> alarm.lightStartAt = getTimee(tokenValue)
                    Command.LightStatus -> alarm.isLightActive = tokenValue.toInt(16) == 1
                    Command.IsSunrise -> alarm.isSunRise = tokenValue.toInt(16) == 1
                    else -> Unit
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "parseAlarm exception:${e.message}")
        }

        Logger.d(TAG, "ALARM_RELATED parseAlarm: alarm:$alarm")
        return alarm
    }

    private fun String.getCustomWeekday(): String = this.toInt(16).toPaddedBinaryString().reversed().substring(0, 7)

    private fun Int.toPaddedBinaryString(): String {
        return String.format("%8s", Integer.toBinaryString(this)).replace(' ', '0')
    }

    private fun getWakeupSound(tokenValue: String?): WakeupSound? {
        if (tokenValue == null || tokenValue.length < 4) return null
        val wakeupType = SoundType.geTypeWithCode(tokenValue.substring(0, 2).toInt())
        return WakeupSound(type = wakeupType, index = tokenValue.substring(2, 4).toInt())
    }

    private fun getTimee(tokenValue: String?): Timee? {
        if (tokenValue == null || tokenValue.length < 6) return null
        return tokenValue.let {
            Timee(hour = it.substring(0, 2).toInt(16), minute = it.substring(2, 4).toInt(16), second = it.substring(4, 6).toInt(16))
        }
    }

    override fun toString(): String {
        return "AlarmInfo(setting=$setting, alarmList=$alarmList)"
    }

    companion object {
        private const val serialVersionUID: Long = -7998938991360664513L
        const val TAG = "TAG_AlarmInfo"
    }

}