package com.harman.command.one.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.jbl.one.configuration.AppConfigurationUtils


data class SetCastGroup(
    @SerializedName("group_info")
    var groupInfo: GroupInfo = GroupInfo()
)

data class GroupInfo(
    @SerializedName("disabled")
    var disabled: Boolean = false,
    @SerializedName("group")
    var group: Group = Group(),
    @SerializedName("members")
    var members: List<Member> = emptyList(), // will return full members whether device online or not.
)  : java.io.Serializable{
    fun getValidGroupId(): String? {
        return if (members.any { item -> ONE_COMMANDER == item.deviceName})  group.id else group.groupId  //Commander will preset a group information before leaving the factory, and all group ids are ffff
    }
    fun getIds(): List<String?> {
        return members.map { item -> item.id }
    }

    fun needCalibrationResult(): Boolean {
        return !(notNeedCalibration() || getGroupType() == GroupType.STEREO)
    }

    fun notNeedCalibration(): Boolean {
        return (members.size == 2 && containBar() && members.any { it.deviceName?.contains("JBL Boombox 3 Wi-Fi") == true || it.deviceName?.contains("Harman Kardon Enchant Sub") == true})
    }

    fun containBar(): Boolean {
        return members.any { isSoundBar(it.deviceName)}
    }

    fun isSoundBar(modelName : String?): Boolean {
        return AppConfigurationUtils.getSupportModelList()?.modelList?.filterValues { modelName.equals(it.modelName, true) }
            ?.firstNotNullOfOrNull { it.key }
            ?.let { AppConfigurationUtils.isSoundBar(it) } == true
    }

    fun getGroupType(): GroupType {
        return if (members.size == 2 && (members[0].deviceName?.contains(members[1].deviceName!! ) == true || members[1].deviceName?.contains(members[0].deviceName!!) == true) ) GroupType.STEREO else GroupType.MULTICHANNEL
    }

    fun getLeftChannelMember(): Member? {
        return members.find { item -> item.channel.contains(Channel.Left.channel) }
    }

    fun getRightChannelMember(): Member? {
        return members.find { item -> item.channel.contains(Channel.Right.channel) }
    }

    fun getGCMembers(): List<Member> {
        return members.filter { item -> item.id != group.id }
    }

    fun getGOMember(): Member? {
        return members.filter { item -> item.id == group.id }.firstOrNull()
    }

    fun getGCIds(): List<String?> {
        return getGCMembers().map { item -> item.id?.lowercase()}
    }

    override fun equals(other: Any?): Boolean {
        if (other !is GroupInfo) {
            return false
        }

        val selfId = getValidGroupId()
        val otherId = other.getValidGroupId()

        return !selfId.isNullOrBlank() &&
            !otherId.isNullOrBlank() &&
            selfId.equals(otherId, true)
    }



    override fun hashCode(): Int {
        return getValidGroupId()?.hashCode() ?: super.hashCode()
    }

    override fun toString(): String {
        return "GroupInfo(disabled=$disabled, group=$group)"
    }

    companion object {
        private const val ONE_COMMANDER: String = "One Commander"
        private const val serialVersionUID: Long = 7094258044050891601
    }

}

data class Group(
    @SerializedName("group_id")
    var groupId: String = "",
    @SerializedName("id")
    var id: String? = null,
    @SerializedName("name")
    var name: String? = null,
    @SerializedName("p2p_mac")
    var p2pMac: String? = "",
    @SerializedName("type")
    var type: String = "",
    @SerializedName("combination_id")
    var combinationId: String? = null,
)  : java.io.Serializable{
    companion object {
        private const val serialVersionUID: Long = 7094258044050891602
    }

    override fun toString(): String {
        return "Group(groupId='$groupId', id=$id, name=$name, p2pMac=$p2pMac, type='$type', combinationId=$combinationId)"
    }


}

data class Member(
    @SerializedName("channel")
    var channel: List<String> = emptyList(),
    @SerializedName("color_id")
    var colorId: Int? = 0,
    @SerializedName("crc")
    var crc: String? = null,
    @SerializedName("device_name")
    var deviceName: String? = null,
    @SerializedName("friendly_name")
    var friendlyName: String? = null,
    @SerializedName("id")
    var id: String? = null,
//    @SerializedName("latency")
//    var latency: Float? = 0f,
//    @SerializedName("volume")
//    var volume: Float? = 0f
)  : java.io.Serializable{

    fun getMacAddr(): String? {
        return id?.chunked(2)?.joinToString(":")
    }

    override fun toString(): String {
        return "Member(channel=$channel, colorId=$colorId, crc=$crc, deviceName=$deviceName, friendlyName=$friendlyName, id=$id)"
    }

    companion object {
        private const val serialVersionUID: Long = 7094258044050891603
    }
}

@Keep
data class OneLineDeviceItem(

    @SerializedName("port")
    var port: String? = null,
    @SerializedName("id")
    var id: String? = null,
)  : java.io.Serializable{
    companion object {
        private const val serialVersionUID: Long = 7094258044050891604
    }
}

enum class Channel(val channel: String) {
    Left("front_left"), Right("front_right"), Side_Left("side_left"), Side_Right("side_right"), Left_Rear_Surround(
        "back_left"
    ),
    Right_Rear_Surround("back_right"), Subwoofer("low_frequency"), Center("center"), Front_Center("front_center");

}
