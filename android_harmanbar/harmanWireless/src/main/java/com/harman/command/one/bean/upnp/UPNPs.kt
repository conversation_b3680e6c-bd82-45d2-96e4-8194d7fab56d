package com.harman.command.one.bean.upnp

import com.google.gson.annotations.SerializedName
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.DiagnosisStatusResponse
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupInfo
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GroupDevicesChange
import com.harman.command.one.bean.NotifyRears
import com.harman.command.one.bean.NotifySoundscapeV2MusicState
import com.harman.command.one.bean.NotifySoundscapeV2Setting
import com.harman.command.one.bean.SetLightInfoRequest

/**
 * @see [LinkPlaySDKHelperProxy.parseSpecNotify]
 */
data class UpnpNotifyPassThrough(
    val passThrough: PassThrough? = null
)

data class UpnpNotifyOta(
    val otaEvent: OtaEvent? = null
)

data class PassThrough(
    val passString: PassString? = null
)

data class PassString(
    val command: String? = null,
    var anchor: SetAnchorResult? = null,
    var battery: BatteryStatusResponse? = null,
    var batteryList: List<BatteryStatusResponse>? = null,
    var soundScapeV2State: GetSoundscapeV2StateResponse? = null,
    var generalConfig: GeneralConfig? = null,
    var diagnosisStatus: DiagnosisStatusResponse? = null,
    var groupInfo: GetGroupInfo? = null,
    var rears: NotifyRears? = null,
    var groupcalibrationState: GetGroupCalibrationStateRsp? = null,
    var soundscapeV2MusicState : NotifySoundscapeV2MusicState? = null,
    var auraCastSqStatus: AuraCastSqModelResponse? = null,
    var eqList: EQListResponse? = null,
    var eqResponse: EQResponse? = null,
    var soundscapeV2Setting: NotifySoundscapeV2Setting? = null,
    var notifyLightInfo: SetLightInfoRequest? = null,
    var groupDevicesChange: GroupDevicesChange? = null
)

data class SetAnchorResult(
    @SerializedName("anchor_result")
    val anchorResult: Int? = null
){
    val success: Boolean
        get() = 0 == anchorResult
}

data class OtaEvent(
    @SerializedName("event_type")
    val eventType: String? = null,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("progress")
    val progress: Int? = null,
    @SerializedName("version")
    val version: String? = null
)

data class UpnpSodState(
    @SerializedName("sod_state")
    val state: SodState? = null
)

/**
 * Firmware will return state = 0 only after both two rear speakers detached.
 * And any one attached will make state = 1.
 */
data class SodState(
    @SerializedName("state")
    val state: Int? = null
) {

    val attached: Boolean
        get() = 1 == state

    override fun toString(): String {
        return "state[$state]"
    }
}

data class UpnpSurroundState(
    @SerializedName("surround_level")
    val surroundLevel: SurroundState? = null
)


data class SurroundState(
    @SerializedName("level")
    val level: Int? = null
)

data class AudioSyncResponse(
    @SerializedName("lip_sync")
    val lipSync: LipSync? = null
)


data class LipSync(
    @SerializedName("value")
    val value: Int? = null
)