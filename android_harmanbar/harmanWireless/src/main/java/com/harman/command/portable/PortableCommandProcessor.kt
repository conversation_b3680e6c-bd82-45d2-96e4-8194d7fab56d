package com.harman.command.portable

import android.text.TextUtils
import com.harman.command.common.IGeneralCommand
import com.harman.discover.bean.bt.PortableBTDevice
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.EnumPlatform
import com.harman.discover.info.GeneralRole
import com.harman.discover.info.PortableAuraCastSupportType
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/1.
 */
object PortableCommandProcessor {

    fun processCommand(device: PortableBTDevice, receiveCommand: IGeneralCommand): Boolean {
        Logger.d(TAG, "processCommand() >>> cmd[${receiveCommand.commandID}] payload:${receiveCommand.dataHexString()}")
        val receivedPayload = receiveCommand.payload ?: run {
            Logger.w(TAG, "processCommand() >>> empty payload in receiveCommand")
            return false
        }
        val payloadContent = HexUtil.encodeHexStr(receivedPayload)

        when (receiveCommand.commandID) {
            PortablePacketFormat.RET_DEV_INFO -> {
                processDeviceInfoRes(device = device, payloadContent = payloadContent)
                return true
            }
        }

        Logger.w(TAG, "processCommand() >>> unknown received cmd[${receiveCommand.commandID}]")
        return false
    }

    private fun processDeviceInfoRes(device: PortableBTDevice, payloadContent: String) {
        val deviceIndexString = payloadContent.substring(
            PortablePacketFormat.PAYLOAD_START_INDEX,
            PortablePacketFormat.DEVICE_INDEX_END_INDEX
        )
        
        val deviceIndex = deviceIndexString.toInt(16)
        val unprocessedPayload = payloadContent.substring(PortablePacketFormat.DEVICE_INDEX_END_INDEX)

        device.deviceIndex = deviceIndex
        //parsing from the end of payload
        if (unprocessedPayload.isEmpty()) {
            return
        }
        processVimicroDeviceInfoRes(device, unprocessedPayload)
    }

    private fun processTWSLinkedDeviceInfo(
        twsLinkedDevice: PortableBTDevice,
        unprocessedPayload: String
    ) {
        var unprocessedPayload = unprocessedPayload
        if (unprocessedPayload.length < 2) {
            return
        }
        var key = unprocessedPayload.substring(0, 2)
        var batteryStatus: String? = null
        var mid: String? = null
        var pid: String? = null
        var deviceName: String? = null
        var endPos = 0
        while (!TextUtils.isEmpty(key)) {
            if (key.equals(PortablePacketFormat.LINKED_DEVICE_COUNT, ignoreCase = true)) {
                /**
                 * Linked device count:
                 * 1 bit (MSB): linking status, 1 means device is linking.
                 * 7 bit (LSB): device count in current link system.
                 * The token only available for device 0.
                 */
                endPos = 2 + 2
            } else if (key.equals(PortablePacketFormat.BATTERY_STATUS, ignoreCase = true)) {
                /**
                 * Battery status:
                 * 1 bit (MSB): charging status, 1 means battery charging.
                 * 7 bit (LSB), 0-100 present 0% - 100%.
                 */
                batteryStatus = unprocessedPayload.substring(2, 4)
                val batteryPercent =
                    HexUtil.parseHexStringToInt(batteryStatus, PortablePacketFormat.BATTERY_STATUS)
                
                if (batteryPercent >= 128) {
                    twsLinkedDevice.masterBatteryLevel = batteryPercent - 128
                    twsLinkedDevice.isMasterCharging = true
                } else {
                    twsLinkedDevice.masterBatteryLevel = batteryPercent
                    twsLinkedDevice.isMasterCharging = false
                }
                
                endPos = 2 + 2
                Logger.d(TAG, "batteryStatus : $batteryStatus")
            } else if (key.equals(PortablePacketFormat.MODEL_ID, ignoreCase = true)) {
                /**
                 * Model ID:
                 */
                mid = unprocessedPayload.substring(2, 4)
                twsLinkedDevice.colorID = mid
                endPos = 2 + 2
                Logger.d(TAG, "mid : $mid")
            } else if (key.equals(PortablePacketFormat.PRODUCT_ID, ignoreCase = true)) {
                /**
                 * Product ID:
                 * Notes: Sometimes charge 4 only return first byte of PID
                 */
                try {
                    pid = unprocessedPayload.substring(2, 6)
                    twsLinkedDevice.pid = pid
                    endPos = 2 * 2 + 2
                    Logger.d(TAG, "pid : $pid")
                } catch (e: Exception) {
                    // Terminate parsing in case error happened.
                    endPos = unprocessedPayload.length + 1
                }
            } else if (key.equals(PortablePacketFormat.DEVICE_NAME, ignoreCase = true)) {
                /**
                 * Device Name
                 */
                val temp = unprocessedPayload.substring(2)
                val nameLen = Integer.valueOf(temp.substring(0, 2), 16)
                val end = nameLen * 2 + 2
                if(end > 2){
                    deviceName = HexUtil.hexStringToString(temp.substring(2, end))
                    endPos = nameLen * 2 + 2 + 2
                    if (!TextUtils.isEmpty(deviceName)) {
                        twsLinkedDevice.deviceName = deviceName
                        Logger.d(TAG, "deviceName : $deviceName")
                    }
                }

            } else {
                break
            }
            if (endPos > unprocessedPayload.length) {
                key = ""
            } else {
                unprocessedPayload = unprocessedPayload.substring(endPos)
                key = try {
                    unprocessedPayload.substring(0, 2)
                } catch (e: Exception) {
                    break
                }
            }
        }
    }

    private fun processVimicroDeviceInfoRes(deviceModel: PortableBTDevice, unprocessedPayload: String) {
        var unprocessedPayload = unprocessedPayload
        if (unprocessedPayload.length < 2) {
            return
        }
        var key = unprocessedPayload.substring(0, 2)
        var audioSource: String? = null
        var supportAuracast: String? = null
        var activeChannel: String? = null
        var batteryStatus: String? = null
        var mid: String? = null
        var pid: String? = null
        var deviceName: String? = null
        var endPos = 0
        while (!TextUtils.isEmpty(key)) {
            if (key.equals(PortablePacketFormat.MAC_ADDRESS_TOKEN_ID, ignoreCase = true)) {
                /**
                 * MAC Address
                 */
                endPos = 6 * 2 + 2
            } else if (key.equals(PortablePacketFormat.SUPPORT_AURACAST, ignoreCase = true)) {
                /**
                 * Support Auracast:
                 * 0x00: doesn’t support Auracast
                 * 0x01: support Auracast
                 */
                supportAuracast = unprocessedPayload.substring(2, 4)
                val auraCastSupportFromDevInfo =
                    if (!TextUtils.isEmpty(supportAuracast)) PortableAuraCastSupportType.getAuracastSourceType(supportAuracast) else PortableAuraCastSupportType.UNKNOWN
                deviceModel.auraCastSupportFromDevInfo = auraCastSupportFromDevInfo
                endPos = 2 + 2
                Logger.d(TAG, "auraCastSupportFromDevInfo : $auraCastSupportFromDevInfo")
            } else if (key.equals(PortablePacketFormat.AUDIO_SOURCE_TOKEN_ID, ignoreCase = true)) {
                /**
                 * Audio Source:
                 * 0x0: no audio playing
                 * 0x1: Bluetooth A2DP
                 * 0x2: Aux-in
                 */
                audioSource = unprocessedPayload.substring(2, 4)
                val audio = HexUtil.parseHexStringToInt(audioSource, PortablePacketFormat.AUDIO_SOURCE_TOKEN_ID)
                deviceModel.audioSource = audio
                endPos = 2 + 2
                Logger.d(TAG, "audio : $audio")
            } else if (key.equals(PortablePacketFormat.ACTIVE_CHANNEL_TOKEN_ID, ignoreCase = true)) {
                /**
                 * Active Channel:
                 * 0x0: Stereo
                 * 0x1: Left
                 * 0x2: Right
                 */
                activeChannel = unprocessedPayload.substring(2, 4)
                val activeChannels = AudioChannel.getAudioChannel(activeChannel)
                deviceModel._channel = activeChannels.value
                endPos = 2 + 2
                Logger.d(TAG, "activeChannels : $activeChannels")
            } else if (key.equals(PortablePacketFormat.LINKED_DEVICE_COUNT, ignoreCase = true)) {
                /**
                 * Linked device count:
                 * 1 bit (MSB): linking status, 1 means device is linking.
                 * 7 bit (LSB): device count in current link system.
                 * The token only available for device 0.
                 */
                val linkedCount = HexUtil.parseHexStringToInt(
                    unprocessedPayload.substring(2, 4),
                    PortablePacketFormat.LINKED_DEVICE_COUNT
                )
                Logger.d(TAG, "linkedCount : $linkedCount")
                if (deviceModel.deviceIndex == 0) {
                    deviceModel.roleValue = GeneralRole.NORMAL.value
                }
                endPos = 2 + 2
            } else if (key.equals(PortablePacketFormat.BATTERY_STATUS, ignoreCase = true)) {
                /**
                 * Battery status:
                 * 1 bit (MSB): charging status, 1 means battery charging.
                 * 7 bit (LSB), 0-100 present 0% - 100%.
                 */
                batteryStatus = unprocessedPayload.substring(2, 4)
                val batteryPercent =
                    HexUtil.parseHexStringToInt(batteryStatus, PortablePacketFormat.BATTERY_STATUS)
                if (batteryPercent >= 128) {
                    deviceModel.masterBatteryLevel = batteryPercent - 128
                    deviceModel.isMasterCharging = true
                } else {
                    deviceModel.masterBatteryLevel = batteryPercent
                    deviceModel.isMasterCharging = false
                }
                endPos = 2 + 2
                Logger.d(TAG, "batteryStatus : $batteryStatus")
            } else if (key.equals(PortablePacketFormat.MODEL_ID, ignoreCase = true)) {
                /**
                 * Model ID:
                 */
                mid = unprocessedPayload.substring(2, 4)
                deviceModel.colorID = mid
                endPos = 2 + 2
                Logger.d(TAG, "mid : $mid")
            } else if (key.equals(PortablePacketFormat.PRODUCT_ID, ignoreCase = true)) {
                /**
                 * Product ID:
                 * Notes: Sometimes charge 4 only return first byte of PID
                 */
                try {
                    pid = unprocessedPayload.substring(2, 6)
                    deviceModel.pid = pid
                    endPos = 2 * 2 + 2
                    Logger.d(TAG, "pid : $pid")
                } catch (e: Exception) {
                    // Terminate parsing in case error happened.
                    endPos = unprocessedPayload.length + 1
                }
            } else if (key.equals(PortablePacketFormat.DEVICE_NAME, ignoreCase = true)) {
                /**
                 * Device Name
                 */
                val temp = unprocessedPayload.substring(2)
                val nameLen = Integer.valueOf(temp.substring(0, 2), 16)
                deviceName = HexUtil.hexStringToString(temp.substring(2, nameLen * 2 + 2))
                var devName = deviceName
                if (deviceModel.pid == "20dc" && deviceName == "JBL Xtreme4") {
                    Logger.d(TAG, "update the device name to : JBL Xtreme 4")
                    devName = "JBL Xtreme 4"
                }
                deviceModel.deviceName = devName ?: ""
                endPos = nameLen * 2 + 2 + 2
                Logger.d(TAG, "deviceName : $deviceName")
            } else {
                break
            }
            if (endPos > unprocessedPayload.length) {
                key = ""
            } else {
                unprocessedPayload = unprocessedPayload.substring(endPos)
                key = try {
                    unprocessedPayload.substring(0, 2)
                } catch (e: Exception) {
                    break
                }
            }
        }
    }

    private fun isDevInfoFromViMicro(cmd: String): Boolean {
        val result = hasToken(cmd, PortablePacketFormat.MAC_ADDRESS_TOKEN_ID) +
                hasToken(cmd, PortablePacketFormat.AUDIO_SOURCE_TOKEN_ID) +
                hasToken(cmd, PortablePacketFormat.ACTIVE_CHANNEL_TOKEN_ID) +
                hasToken(cmd, PortablePacketFormat.LINKED_DEVICE_COUNT) +
                hasToken(cmd, PortablePacketFormat.BATTERY_STATUS) +
                hasToken(cmd, PortablePacketFormat.MODEL_ID) +
                hasToken(cmd, PortablePacketFormat.PRODUCT_ID) +
                hasToken(cmd, PortablePacketFormat.DEVICE_NAME)
        return !(result >= 7 && cmd.length > 36)
    }

    private fun hasToken(str: String, token: String): Int {
        return if (str.contains(token)) 1 else 0
    }

    private fun isViMicroProduct(device: PortableBTDevice): Boolean {
        return device.platform == EnumPlatform.VIMICRO
    }

    /**
     * Function to fetch each token value for a Speaker, these are fetched from RET_DEV_INFO response string
     *
     * @param tokenIDBytesLength current token ID length of bytes
     * @param tokenID            current token id
     * @param unprocessedPayload string left un-processed
     * @return processedUnprocessedResult This is a String[2], with first string as Token Value and second as unprocessed string left out of response string.
     */
    private fun retDevInfoTokenIds(
        tokenIDBytesLength: Int,
        tokenID: String,
        unprocessedPayload: String?
    ): Array<String?> {
        var content = unprocessedPayload
        val processedUnprocessedResult = arrayOfNulls<String>(2)
        var tokenIDValue: String? = null

        content?.also { unPayload ->
            val payloadLength = unPayload.length
            if (payloadLength >= tokenIDBytesLength) {
                tokenIDValue =
                    unPayload.substring(payloadLength - tokenIDBytesLength, payloadLength)
                if (tokenIDValue != null && tokenIDValue!!.startsWith(tokenID)) {
                    content = unPayload.substring(0, payloadLength - tokenIDBytesLength)
                } else if (unPayload.endsWith(tokenID)) {
                    content = unPayload.substring(0, payloadLength - tokenID.length)
                    tokenIDValue = null
                } else {
                    tokenIDValue = null
                }
            }
        }
        processedUnprocessedResult[0] = tokenIDValue
        processedUnprocessedResult[1] = content
        return processedUnprocessedResult
    }

    private fun retrieveConnectPlusDevName(tokenID: String, unprocessedPayload: String?): String? {
        unprocessedPayload?.also { unPayload ->
            val idPos = unPayload.indexOf(tokenID, 0, true)
            if (idPos >= 0 && idPos + 2 < unPayload.length) {
                val nameLen = Integer.valueOf(unPayload.substring(idPos + 2, idPos + 4), 16)
                return unPayload.substring(idPos + 4, idPos + 4 + nameLen * 2)
            }
        }
        return null
    }

    private const val TAG = "PortableCommandProcessor"


}