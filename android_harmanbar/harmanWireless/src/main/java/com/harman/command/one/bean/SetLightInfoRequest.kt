package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName

/**
 * {
 *   "enable" : "1",
 *   "active_pattern" : {
 *      "id": "ocean",
 *      "level ": "70"
 *    },
 *   "brightness ": "50",
 *   "dynamic_level": "1",
 *   "light_element": "1"
 * }
 *
 * @property enable: "0"--off, "1"--on
 * @property activePattern
 * @property brightness: [0, 100]
 * @property dynamicLevel: 1: low 2: mid 3: high
 * @property lightElement: 1: all 2: subwoofer 3 satellite 4: disc 5: sculpture
 *      For Aura Studio 5 Wifi, UI design rename the lightElement name:
 *      Disc ---> Core
 *      Sculpture ---> Projection
 *      And UI design only supports 3 lightElements: 1: all, 4: Core, 5: Projection
 */
data class SetLightInfoRequest(
    @SerializedName("enable")
    var enable: String? = null,
    @SerializedName("active_pattern")
    var activePattern: Pattern? = null,
    @SerializedName("brightness")
    var brightness: String? = null,
    @SerializedName("dynamic_level")
    var dynamicLevel: String? = null,
    @SerializedName("light_element")
    var lightElement: String? = null
)

/**
 * @property id: '1: ocean','2: aurora','3: blossom','4: sunrise','5: fireplace','6: static'
 * @property level: [0, 100]
 */
data class Pattern(
    @SerializedName("id")
    var id: String? = null,
    @SerializedName("level")
    var level: String? = null
) {
    fun getPatternName(): String? = id?.let { EnumPattern.fromId(it)?.pattern } ?: EnumPattern.Ocean.pattern
}

enum class EnumPattern(val pattern: String) {
    Ocean(pattern = "ocean"),
    Aurora(pattern = "aurora"),
    Blossom(pattern = "blossom"),
    Sunrise(pattern = "sunrise"),
    Fireplace(pattern = "fireplace"),
    Static("static");

    companion object {
        private val idMap = mapOf(
            "1" to Ocean,
            "2" to Aurora,
            "3" to Blossom,
            "4" to Sunrise,
            "5" to Fireplace,
            "6" to Static
        )

        fun fromId(id: String): EnumPattern? = idMap[id]
    }
}