package com.harman.command.partybox.gatt

import com.harman.command.common.GeneralGattCommand
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.info.AudioSource
import com.harman.discover.util.Tools.isNullOrEmpty
import com.harman.discover.util.Tools.safetySubString
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil
import java.util.concurrent.CopyOnWriteArrayList

/**
 * <AUTHOR>
 * version：1.0
 * date：2024/9/25
 * desc：ReqRadioInfoCommand
 */
class ReqRadioInfoCommand(
    private val requestType: RadioInfo.RequestType
) : GeneralGattCommand() {

    override fun toString(): String {
        return "requestType[${requestType.type}][${requestType.desc}]"
    }

    init {
        super.commandID = GattPacketFormat.REQ_RADIO_INFO
        super.payload = byteArrayOf(requestType.type.toByte())
        super.responseCommandIDs.add(GattPacketFormat.RET_RADIO_INFO)
        Logger.d(TAG, "init command:${dataHexString()}")
    }

    override fun onNotify(device: BaseBTDevice<*, *>, receivedCommand: IGeneralCommand): Boolean {
        if (!receivedCommand.isRadioInfoCommandRsp() || device !is PartyBoxBTDevice) {
            return false
        }

        Logger.i(TAG, "onNotify() >>> receivedCommand radioInfo pre: ${device.radioInfo}")
        responseCommandIDs.clear()
        val bytesPayload = receivedCommand.payload
        if (bytesPayload.isNullOrEmpty()) {
            Logger.w(TAG, "onNotify() >>> bytesPayload is empty")
            return false
        }

        val hexPayload = HexUtil.encodeHexStr(bytesPayload)
        if (hexPayload.isNullOrBlank()) {
            Logger.w(TAG, "onNotify() >>> hexPayload is empty")
            return false
        }
        Logger.d(TAG, "onNotify() >>> receivedCommand receivedPayload: $hexPayload")
        Logger.d(TAG, "onNotify() >>> receivedCommand requestType: $requestType")

        val radioInfo = parseAsRadioInfo(device = device, hexPayload = hexPayload)
        device.radioInfo = radioInfo

        Logger.d(TAG, "onNotify() >>> receivedCommand radioInfo after:${device.radioInfo}")

        return true
    }

    companion object {
        private const val TAG = "ReqRadioInfoCommand"

        private val scannedStationList = CopyOnWriteArrayList<RadioInfo.Station>()
        private var notifiedPresetStationList = CopyOnWriteArrayList<RadioInfo.Station>()

        fun IGeneralCommand.isRadioInfoCommandRsp(): Boolean {
            val receivedCommand = this

            return GattPacketFormat.RET_RADIO_INFO == receivedCommand.commandID &&
                    !receivedCommand.payload.isNullOrEmpty()
        }

        private fun parseAsRadioInfo(device: PartyBoxBTDevice, hexPayload: String): RadioInfo {
            synchronized(hexPayload) {
                /*val radioInfo = RadioInfo().apply {
                    this.requestType = requestType
                }*/

                val radioInfo = device.radioInfo ?: RadioInfo()
                Logger.d(TAG, "parseAsRadioInfo() >>>$TAG radioInfo pre: $radioInfo")

                val requestTypeHex = hexPayload.substring(0, 2).toIntOrNull(16)
                val requestType = RadioInfo.RequestType.getRequestType(requestTypeHex)
                Logger.d(TAG, "parseAsRadioInfo() >>>$TAG requestType[$requestType]")
                radioInfo.requestType = requestType

                // Only parse sub command when DevRadioToken radio was Functionality(0x71) .
                val functionalitySubCmd = parseFunctionalitySubCmd(hexPayload = hexPayload)
                radioInfo.functionality = functionalitySubCmd

                when (requestType) {
                    RadioInfo.RequestType.RadioSetting -> {
                        radioInfo.setting = radioInfo.parseRadioConfig(hexPayload.substring(2))
                    }

                    RadioInfo.RequestType.AllPreset -> {
                        radioInfo.presetStationList = radioInfo.parseStationList(hexPayload.substring(2))
                    }

                    RadioInfo.RequestType.LastStation -> {
                        radioInfo.parseStationList(hexPayload.substring(2)).elementAtOrNull(0)?.let {
                            radioInfo.currentStation = it
                            radioInfo.currentRadioType = it.radioType
                        }

                    }

                    RadioInfo.RequestType.DeviceNotify -> {
                        radioInfo.functionality?.let { itFunctionality ->
                            val parseStationList = radioInfo.parseStationList(hexPayload.substring(2))
                            when (itFunctionality) {
                                RadioInfo.Command.FunctionalitySwitchRadioType -> {
                                    //Switch Radio Type
                                    parseStationList.elementAtOrNull(0)?.let { itStation ->
                                        radioInfo.currentStation = itStation
                                        radioInfo.currentRadioType = itStation.radioType

                                        if (itStation.radioType == RadioInfo.Type.DABRadio) {
                                            if (itStation.stationScanStatus == 0 || itStation.stationScanStatus == 2) {
                                                radioInfo.presetStationList?.let { presetStationList ->
                                                    if (presetStationList.isNotEmpty()) {
                                                        for (presetStation in presetStationList) {
                                                            presetStation.state = RadioInfo.State.Stopped
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        radioInfo.scannedStationList?.let { scannedStations ->
                                            val filterStations = scannedStations.filter { filterStationListByRadioType(it, radioInfo) }
                                            radioInfo.scannedStationList = filterStations.toMutableList()
                                        }

                                    }
                                }

                                RadioInfo.Command.FunctionalityScanResult -> {
                                    radioInfo.scannedStationList = parseStationList
                                }

                                RadioInfo.Command.FunctionalityUpdateStation -> {
                                    radioInfo.presetStationList = parseStationList
                                }

                                RadioInfo.Command.FunctionalityActiveStation -> {
                                    parseStationList.filter { filterStationList(it) }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }

                                RadioInfo.Command.FunctionalityStop -> {
                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Stopped
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }

                                RadioInfo.Command.FunctionalityPlay -> {
                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Playing
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }

                                else -> {
                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Playing
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }
                            }

                            parseStationList.filter { filterStationList(it) }.forEach { stationItem ->
                                radioInfo.presetStationList?.let { itPresetStations ->
                                    if (stationItem.state == RadioInfo.State.Playing) {

                                        for (presetStation in itPresetStations) {

                                            if (stationItem.radioType == RadioInfo.Type.DABRadio) {

                                                if (presetStation.stationName != stationItem.stationName) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                } else {
                                                    presetStation.state = RadioInfo.State.Playing
                                                }

                                            } else {

                                                if (presetStation.frequency != stationItem.frequency) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                } else {
                                                    presetStation.state = RadioInfo.State.Playing
                                                }

                                            }
                                        }

                                    } else if (stationItem.state == RadioInfo.State.Stopped) {
                                        for (presetStation in itPresetStations) {

                                            if (stationItem.radioType == RadioInfo.Type.DABRadio) {
                                                if (presetStation.stationName == stationItem.stationName) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                }
                                            } else {
                                                if (presetStation.frequency == stationItem.frequency) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                }
                                            }

                                        }

                                    }
                                }

                            }

                        }
                    }

                    else -> {
                        if (hexPayload.length <= 4) {
                            radioInfo.scannedStationList = mutableListOf()
                        } else {
                            val parseStationList = radioInfo.parseStationList(hexPayload.substring(2))
                            radioInfo.scannedStationList?.addAll(parseStationList)
                        }
                    }

                    /*else -> {
                        radioInfo.scannedStationList = if (hexPayload.length <= 4) {
                            mutableListOf()
                        } else {
                            radioInfo.parseStationList(hexPayload.substring(2))
                        }
                    }*/
                }

                Logger.d(TAG, "parseAsRadioInfo() >>>$TAG radioInfo after: $radioInfo")
                return radioInfo
            }

        }

        fun parseAsRadioInfo(logTag: String, device: PartyBoxBTDevice, hexPayload: String): RadioInfo {
            synchronized(hexPayload) {
                Logger.i(TAG, "parseAsRadioInfo() >>>$logTag pre device radioInfo:${device.radioInfo}")

                val radioInfo = device.radioInfo ?: RadioInfo()

                val requestTypeHex = hexPayload.substring(0, 2).toIntOrNull(16)
                val requestType = RadioInfo.RequestType.getRequestType(requestTypeHex)
                Logger.d(TAG, "parseAsRadioInfo() >>>$logTag requestType[$requestType]")
                radioInfo.requestType = requestType

                // Only parse sub command when DevRadioToken radio was Functionality(0x71) .
                val functionalitySubCmd = parseFunctionalitySubCmd(hexPayload = hexPayload)
                Logger.d(TAG, "parseAsRadioInfo() >>>$logTag functionality[$functionalitySubCmd]")
                radioInfo.functionality = functionalitySubCmd

                when (requestType) {
                    RadioInfo.RequestType.RadioSetting -> {
                        radioInfo.setting = radioInfo.parseRadioConfig(hexPayload.substring(2))
                    }

                    RadioInfo.RequestType.AllPreset -> {
                        device.radioInfo?.let { itRadioInfo ->
                            val parseStationList = radioInfo.parseStationList(hexPayload.substring(2))
                            itRadioInfo.presetStationList?.let { itPresetStations ->
                                parseStationList.filter { filterStationList(it) }.takeIf { it.isNotEmpty() }?.let {
                                    if (itPresetStations.isNotEmpty()) {
                                        val updatePresetStationList = updateNotifiedPresetStationList(itPresetStations, parseStationList)
                                        itPresetStations.clear()
                                        itPresetStations.addAll(updatePresetStationList)
                                    }
                                }
                            } ?: let {
                                itRadioInfo.presetStationList = parseStationList
                            }

                        }

                    }

                    RadioInfo.RequestType.LastStation -> {
                        radioInfo.parseStationList(hexPayload.substring(2)).elementAtOrNull(0)?.let {
                            radioInfo.currentStation = it
                            radioInfo.currentRadioType = it.radioType
                        }
                    }

                    RadioInfo.RequestType.DeviceNotify -> {
                        radioInfo.functionality?.let { itFunctionality ->
                            val parseStationList = radioInfo.parseStationList(hexPayload.substring(2))
                            Logger.i(TAG, "parseAsRadioInfo() >>>$logTag parseStationList:$parseStationList")
                            when (itFunctionality) {
                                RadioInfo.Command.FunctionalitySwitchRadioType -> {
                                    //Switch Radio Type
                                    parseStationList.elementAtOrNull(0)?.let { itStation ->
                                        radioInfo.currentStation = itStation
                                        radioInfo.currentRadioType = itStation.radioType

                                        if (itStation.radioType == RadioInfo.Type.DABRadio) {
                                            if (itStation.stationScanStatus == 0 || itStation.stationScanStatus == 2) {
                                                radioInfo.presetStationList?.let { presetStationList ->
                                                    if (presetStationList.isNotEmpty()) {
                                                        for (presetStation in presetStationList) {
                                                            presetStation.state = RadioInfo.State.Stopped
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        /*radioInfo.scannedStationList?.let { scannedStations ->
                                            val filterStations = scannedStations.filter { filterStationListByRadioType(it, radioInfo) }
                                            radioInfo.scannedStationList = filterStations.toMutableList()
                                        }*/
                                    }
                                }

                                RadioInfo.Command.FunctionalityScanResult -> {
                                    radioInfo.scannedStationList?.let { itScannedStations ->
                                        itScannedStations.filter {
                                            filterStationList(it)
                                        }.filter {
                                            it.stationScanStatus == 1
                                        }.takeIf {
                                            it.isNotEmpty()
                                        }?.let {
                                            val updateScannedStations = updateScannedStationList(itScannedStations, parseStationList, radioInfo)
                                            itScannedStations.clear()
                                            Logger.i(TAG, "parseAsRadioInfo() >>>$logTag updateScannedStations:$updateScannedStations")
                                            itScannedStations.addAll(updateScannedStations)
                                        }

                                    } ?: let {
                                        radioInfo.scannedStationList = parseStationList.filter {
                                            filterStationList(it)
                                        }.filter {
                                            it.stationScanStatus == 1
                                        }.toMutableList()

                                        parseStationList.elementAtOrNull(0)?.let { itStation ->
                                            if (itStation.stationScanStatus == 0 || itStation.stationScanStatus == 2) {
                                                radioInfo.currentStation = itStation
                                                radioInfo.currentRadioType = itStation.radioType
                                            }

                                        }
                                    }

                                }

                                RadioInfo.Command.FunctionalityUpdateStation -> {
                                    radioInfo.presetStationList?.let { itPresetStations ->
                                        parseStationList.filter { filterStationList(it) }.takeIf { it.isNotEmpty() }?.let {
                                            if (itPresetStations.isNotEmpty()) {
                                                val updatePresetStations = updateNotifiedPresetStationList(itPresetStations, parseStationList)
                                                itPresetStations.clear()
                                                itPresetStations.addAll(updatePresetStations)
                                            } else {
                                                radioInfo.presetStationList = parseStationList
                                            }
                                        }
                                    } ?: let {
                                        radioInfo.presetStationList = parseStationList
                                    }

                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Playing
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }

                                }

                                RadioInfo.Command.FunctionalityActiveStation -> {
                                    parseStationList.filter { filterStationList(it) }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }

                                RadioInfo.Command.FunctionalityStop -> {
//                                    device.audioSource = AudioSource.NONE_AUDIO
                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Stopped
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }

                                RadioInfo.Command.FunctionalityPlay -> {
                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Playing
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }

                                else -> {
                                    parseStationList.filter { filterStationList(it) }.filter {
                                        it.state == RadioInfo.State.Playing
                                    }.elementAtOrNull(0)?.let {
                                        radioInfo.currentStation = it
                                        radioInfo.currentRadioType = it.radioType
                                    }
                                }
                            }

                            parseStationList.filter { filterStationList(it) }.forEach { stationItem ->
                                radioInfo.presetStationList?.let { itPresetStations ->
                                    if (stationItem.state == RadioInfo.State.Playing) {

                                        for (presetStation in itPresetStations) {

                                            if (stationItem.radioType == RadioInfo.Type.DABRadio) {

                                                if (presetStation.stationName != stationItem.stationName) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                } else {
                                                    presetStation.state = RadioInfo.State.Playing
                                                }

                                            } else {

                                                if (presetStation.frequency != stationItem.frequency) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                } else {
                                                    presetStation.state = RadioInfo.State.Playing
                                                }

                                            }
                                        }

                                    } else if (stationItem.state == RadioInfo.State.Stopped) {
                                        for (presetStation in itPresetStations) {

                                            if (stationItem.radioType == RadioInfo.Type.DABRadio) {
                                                if (presetStation.stationName == stationItem.stationName) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                }
                                            } else {
                                                if (presetStation.frequency == stationItem.frequency) {
                                                    presetStation.state = RadioInfo.State.Stopped
                                                }
                                            }

                                        }

                                    }
                                }

                            }

                        }
                    }

                    else -> {
                        if (hexPayload.length <= 4) {
                            radioInfo.scannedStationList = mutableListOf()
                        } else {
                            val parseStationList = radioInfo.parseStationList(hexPayload.substring(2))
                            radioInfo.scannedStationList?.addAll(parseStationList)
                        }
                    }
                }

                Logger.d(TAG, "parseAsRadioInfo() >>>$logTag after device radioInfo: $radioInfo")
                return radioInfo
            }

        }

        private fun parseFunctionalitySubCmd(hexPayload: String): RadioInfo.Command? {
            if (hexPayload.length < 10) {
                return null
            }

            val cmd = hexPayload.safetySubString(4, 6)?.toIntOrNull(16)
            val cmdValue = hexPayload.safetySubString(8, 10)?.toIntOrNull(16)
//            Logger.d(TAG, "parseFunctionalitySubCmd() >>> functionality cmd[$cmd] cmdValue[$cmdValue]")

            if (GattPacketFormat.RadioToken.FUNCTIONALITY != cmd || null == cmdValue) {
                return null
            }

            return RadioInfo.Command.getCommandByCode(code = cmd, subCode = cmdValue)
        }

        private fun updateNotifiedPresetStationList(
            stationList: MutableList<RadioInfo.Station>,
            updateStationList: MutableList<RadioInfo.Station>?
        ): MutableList<RadioInfo.Station> {
//            Logger.i(TAG, "RADIO_RELATED updateNotifiedPresetStationList stationList pre:${stationList}")
//            Logger.d(TAG, "RADIO_RELATED updateNotifiedPresetStationList updateStationList:${updateStationList}")

            val fmFrequencies = updateStationList?.filter { it.radioType == RadioInfo.Type.FMRadio }?.map { it.frequency }?.toSet()

            val dabNames = updateStationList?.filter { it.radioType == RadioInfo.Type.DABRadio }?.map { it.stationName }?.toSet()

            val filteredStationList = stationList.filter { station ->
                if (station.radioType == RadioInfo.Type.FMRadio) {
                    fmFrequencies?.contains(station.frequency) != true
                } else {
                    dabNames?.contains(station.stationName) != true
                }
            }.toMutableList()
//            Logger.i(TAG, "RADIO_RELATED updateNotifiedPresetStationList filteredStationList:${filteredStationList}")

            val stationMap = filteredStationList.filter { filterStationList(it) }.associateBy { station ->
                station.stationIndex
            }

//            Logger.d(TAG, "RADIO_RELATED updateNotifiedPresetStationList stationMap:${stationMap}")

            updateStationList?.filter { filterStationList(it) }?.let { itUpdateStationList ->

                for (updatedStation in itUpdateStationList) {

                    if (stationMap.containsKey(updatedStation.stationIndex)) {

                        val index = filteredStationList.indexOfFirst { it.stationIndex == updatedStation.stationIndex }
//                        Logger.i(TAG, "RADIO_RELATED updateNotifiedPresetStationList index:${index}")
                        if (index != -1) {
                            filteredStationList[index] = updatedStation
                        }

                    } else {
                        filteredStationList.add(updatedStation)
                    }

                }

            }

//            Logger.d(TAG, "RADIO_RELATED updateNotifiedPresetStationList stationList:${stationList}")
            return filteredStationList
        }

        private fun updateScannedStationList(
            stationList: MutableList<RadioInfo.Station>,
            updateStationList: MutableList<RadioInfo.Station>?,
            radioInfo: RadioInfo
        ): MutableList<RadioInfo.Station> {
            Logger.i(TAG, "updateNotifiedPresetStationList stationList pre:${stationList}")
            Logger.d(TAG, "updateNotifiedPresetStationList updateStationList:${updateStationList}")

            val stationMap = stationList.filter { filterStationList(it) }.associateBy { station ->
                when (station.radioType) {
                    RadioInfo.Type.FMRadio -> station.frequency
                    RadioInfo.Type.DABRadio -> station.stationName
                    else -> station.frequency
                }
            }

            updateStationList?.filter { filterStationList(it) }?.let { itUpdateStationList ->

                for (updatedStation in itUpdateStationList) {

                    if (updatedStation.radioType == RadioInfo.Type.DABRadio) {

                        if (stationMap.containsKey(updatedStation.stationName)) {
                            val index = stationList.indexOfFirst { it.stationName == updatedStation.stationName }
                            if (index != -1) {
                                stationList[index] = updatedStation
                            }

                        } else {
                            stationList.add(updatedStation)
                        }

                    } else {

                        if (stationMap.containsKey(updatedStation.frequency)) {

                            val index = stationList.indexOfFirst { it.frequency == updatedStation.frequency }
                            if (index != -1) {
                                stationList[index] = updatedStation
                            }

                        } else {
                            stationList.add(updatedStation)
                        }

                    }

                }

            }
            val toFilterList = stationList.filter { filterStationListByRadioType(it, radioInfo) }.toMutableList()
            Logger.i(TAG, "updateNotifiedPresetStationList stationList after:${toFilterList}")
            return toFilterList

        }

        private fun filterStationList(station: RadioInfo.Station) = when (station.radioType) {
            RadioInfo.Type.FMRadio -> station.frequency != null
            RadioInfo.Type.DABRadio -> !station.stationName.isNullOrBlank()
            else -> station.frequency != null
        }

        private fun filterStationListByRadioType(station: RadioInfo.Station, radioInfo: RadioInfo) = when(radioInfo.currentRadioType) {
            RadioInfo.Type.FMRadio -> {
                station.radioType == RadioInfo.Type.FMRadio
            }

            RadioInfo.Type.DABRadio -> {
                station.radioType == RadioInfo.Type.DABRadio
            }

            else -> {
                station.radioType == RadioInfo.Type.FMRadio
            }
        }

        const val MAX_STATION_NAME_BYTE_LENGTH = 32
    }
}