package com.harman.command.one.command

import com.harman.command.one.EnumCommandMapping

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/22.
 *
 * 0x1d12
 *
 * response @link [GroupDeviceOTAStatusList] [GroupDeviceOTAStatusObject]
 */
class GetGroupDevicesOtaStatusCommand : OneGattCommand() {

    override var commandID: Int = EnumCommandMapping.GET_GROUP_DEVICES_OTA_STATUS.bleCmd

    override val payload: ByteArray? = null

}