package com.harman.command.one.bean

import com.google.gson.annotations.SerializedName
import com.harman.discover.util.Tools.printList

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/4/9.
 *
 * {
 *   "error_code": "0",
 *   "active_eq_id ": "1",
 *   "eq_list": [
 *     {
 *       "eq_name": "<PERSON>",
 *       "eq_id": "1",
 *       "band": 3,
 *       "eq_payload": {
 *         "gain": [
 *           6,
 *           3,
 *           0
 *         ],
 *         "fs": [
 *           150,
 *           1000,
 *           5000
 *         ]
 *       }
 *     }
 *   ]
 * }
 */
data class EQListResponse(
    @SerializedName("error_code")
    val errorCode: String? = null,
    @SerializedName("active_eq_id")
    var activeEQID: String? = null,
    @SerializedName("eq_list")
    val eqList: List<EQListItem>? = null
) {

    fun success(): Boolean = !activeEQID.isNullOrEmpty() && !eqList.isNullOrEmpty()//upnp notify has no errorCode

    fun findCustomEQ(): EQListItem? = eqList?.firstOrNull { item ->
        CUSTOM_EQ_ID == item.eqID
    }

    override fun toString(): String {
        return "errorCode[$errorCode] activeEQID[$activeEQID] eqList[${eqList.printList()}]"
    }
}

data class EQListItem(
    @SerializedName("eq_name")
    val eqName: String? = null,
    @SerializedName("eq_id")
    val eqID: String? = null,
    @SerializedName("band")
    val band: Int? = null,
    @SerializedName("eq_payload")
    val eqListPayload: EQListPayload? = null
) {

    override fun toString(): String {
        return "eqName[$eqName] eqID[$eqID] band[$band] eqListPayload[$eqListPayload]"
    }
}

data class EQListPayload(
    @SerializedName("gain")
    var gain: List<Float>? = null,
    @SerializedName("fs")
    val fs: List<Float>? = null
) {

    override fun toString(): String {
        return "gain[${gain.printList()}] fs[${fs.printList()}]"
    }
}

const val CUSTOM_EQ_ID = "0"