package com.harman.command.partybox.gatt

import android.util.Log
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.battery.EnumBatteryStatusFeature
import com.harman.command.partybox.gatt.eq.EnumBandCount
import com.harman.command.partybox.gatt.eq.EnumCustomEQBandType
import com.harman.command.partybox.gatt.eq.EnumCustomEQLevel
import com.harman.command.partybox.gatt.eq.EnumEQScope
import com.harman.command.partybox.gatt.eq.RawBandC1
import com.harman.command.partybox.gatt.eq.RawBandC2
import com.harman.command.partybox.gatt.eq.RawEQSettings
import com.harman.command.partybox.gatt.light.Color
import com.harman.command.partybox.gatt.light.EnumLightPattern
import com.harman.command.partybox.gatt.light.EnumLightSwitch
import com.harman.command.partybox.gatt.light.EnumPatternLoop
import com.harman.command.partybox.gatt.sleep.SleepModeInfo
import com.harman.command.partybox.gatt.timer.AutoOffTimerRsp
import com.harman.connect.PartyBoxGattSession
import com.harman.connect.isHomeBtCategory
import com.harman.connect.isSupportLightControl
import com.harman.connect.workaroundToPercentageVolume
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.PartyBoxDevFeat
import com.harman.discover.info.AmbientLightInfo
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.AudioSource
import com.harman.discover.info.AuracastInfo
import com.harman.discover.info.EnumSyncOnOff
import com.harman.discover.info.EnumSyncOnOff.Companion.toEnumSyncOnOff
import com.harman.discover.info.PartyBoxSecondaryInfo
import com.harman.discover.info.PartyConnectStatus
import com.harman.discover.info.PlayerStatus
import com.harman.discover.info.ScreenDisplayInfo
import com.harman.discover.util.Tools.FLAG_AC_CABLE_WITHOUT_BATTERY
import com.harman.discover.util.Tools.isNullOrEmpty
import com.harman.discover.util.Tools.isPartyBoxCategory
import com.harman.discover.util.Tools.putExtra
import com.harman.discover.util.Tools.safetySubArray
import com.harman.discover.util.Tools.safetySubString
import com.harman.discover.util.Tools.toFloat
import com.harman.discover.util.Tools.toInt
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil
import com.jbl.one.configuration.model.EnumEQCategory
import com.jbl.one.configuration.model.EnumPresetEQBandType
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.Locale

object PartyBoxGattCommandProcessor {

    private const val TAG = "PartyBoxGattCommandProcessor"

    private const val PRIMARY_DEVICE_INDEX = 0x00.toByte()
    private const val SECONDARY_DEVICE_INDEX = 0x01.toByte()

    fun processCommand(device: PartyBoxBTDevice, receivedCommand: IGeneralCommand): Boolean {
        Logger.d(
            TAG,
            "processCommand() >>> cmd[${receivedCommand.commandID}] payload:${receivedCommand.dataHexString()}"
        )

        when (receivedCommand.commandID) {
            GattPacketFormat.RESP_DEV_INFO -> {
                val receivedPayload = receivedCommand.payload ?: run {
                    Logger.w(
                        TAG,
                        "processCommand() >>> empty payload in receiveCommand for ${receivedCommand.commandID}"
                    )
                    return false
                }

                return parseDeviceInfo(
                    device = device,
                    receiveCommand = receivedCommand,
                    receivedPayload = receivedPayload
                )
            }

            GattPacketFormat.RESP_PLAYER_INFO -> {
                val receivedPayload = receivedCommand.payload ?: run {
                    Logger.w(
                        TAG,
                        "processCommand() >>> empty payload in receiveCommand for ${receivedCommand.commandID}"
                    )
                    return false
                }

                return parsePlayerInfo(device = device, receivedPayload = receivedPayload)
            }

            GattPacketFormat.RET_ADVANCE_EQ -> {
                val receivedPayload = receivedCommand.payload ?: run {
                    Logger.w(
                        TAG,
                        "processCommand() >>> empty payload in receiveCommand for ${receivedCommand.commandID}"
                    )
                    return false
                }

                return parseAdvanceEQ(device = device, receivedPayload = receivedPayload)
            }

            GattPacketFormat.RES_DEVICE_FEATURE_INFO -> {
                val receivedPayload = receivedCommand.payload ?: run {
                    Logger.w(
                        TAG,
                        "processCommand() >>> empty payload in receiveCommand for ${receivedCommand.commandID}"
                    )
                    return false
                }

                return parseDeviceFeatureInfo(device = device, receivedPayload = receivedPayload)
            }

            GattPacketFormat.SET_SECONDARY_SPEAKER_ADDRESS -> {
                val address = parseAsMacAddress(receivedCommand.payload)
                device.secondaryInfo =
                    (device.secondaryInfo ?: PartyBoxSecondaryInfo()).also { info ->
                        info.macAddress = address
                    }
            }

            GattPacketFormat.RESP_LIGHT_INFO -> {
                return parseLightInfo(device, receivedCommand.payload)
            }

            GattPacketFormat.RESP_INACTIVE_LIGHT_PATTERN -> {
                return parseAsInActiveLightPattern(device, receivedCommand.payload)
            }

            GattPacketFormat.RET_FEEDBACK_TONE -> {
                return parseAsFeedbackTone(device, receivedCommand.payload)
            }

            GattPacketFormat.RET_BATTERY_STATUS -> {
                return parseAsBatteryStatus(device, receivedCommand.payload)
            }

            GattPacketFormat.RET_SCREEN_DISPLAY_INFO -> {
                return parseScreenDisplayInfo(device, receivedCommand.payload)
            }

            GattPacketFormat.RET_ALARM_INFO -> {
                return parseAlarmInfo(device, receivedCommand.payload)
            }

            GattPacketFormat.RET_SLEEP_MODE_INFO -> {
                return parseAndSetSleepModeInfo(device, receivedCommand.payload)
            }

            GattPacketFormat.DEV_ACK -> {
                val receivedPayload = receivedCommand.payload
                val result = (receivedPayload != null && receivedPayload.size > 1
                        && (GattPacketFormat.SET_DEV_INFO == receivedPayload[0]
                        || GattPacketFormat.SET_SCREEN_DISPLAY_INFO == receivedPayload[0]
                        || GattPacketFormat.SET_AMBIENT_LIGHT_INFO == receivedPayload[0]
                        )
                        && GattPacketFormat.STATUS_CODE_SUCCESS == receivedPayload[1])
                Logger.d(TAG, "DEV_ACK=${result}")
                return result
            }

            GattPacketFormat.RET_AMBIENT_LIGHT_INFO -> {
                return parseAmbientLightCommand(device, receivedCommand.payload)
            }

            GattPacketFormat.RET_AUTHENTICATION -> {
                /** let [PartyBoxGattSession.notifyReceive] handle response */
                return true
            }

            GattPacketFormat.RET_RADIO_INFO -> {
                return parseRadioInfo(device = device, bytesPayload = receivedCommand.payload)
            }

            GattPacketFormat.RET_AUTO_OFF_TIMER -> {
                return processAutoOffTimerResponse(device = device, payload = receivedCommand.payload)
            }
        }

        Logger.w(TAG, "processCommand() >>> unknown received cmd[${receivedCommand.commandID}]")
        return false
    }

    @OptIn(ExperimentalStdlibApi::class)
    private fun parseDeviceFeatureInfo(
        device: PartyBoxBTDevice,
        receivedPayload: ByteArray
    ): Boolean {
        if (receivedPayload.isEmpty()) {
            Logger.w(TAG, "parseDeviceFeatureInfo() >>> empty received payload")
            return false
        }

        var offset = 0
        while (offset < receivedPayload.size - 1) {
            val subCommand = receivedPayload[offset]
            val payloadLen = receivedPayload[offset + 1].toInt()

            val payload = if (payloadLen > 0) {
                receivedPayload.safetySubArray(start = offset + 2, end = offset + 2 + payloadLen)
            } else byteArrayOf()

            Logger.i(
                TAG,
                "subCommand=${subCommand.toHexString()},payload=${payload?.toHexString()}"
            )
            when (subCommand) {
                GattPacketFormat.DeviceFeatureInfo.VERSION -> {
                    device.firmwareVersion = formatVersion(HexUtil.byte2HexStr(payload))
                }

                else -> {
                    device.deviceFeature =
                        (device.deviceFeature ?: PartyBoxDevFeat()).also { feat ->
                            feat.update(command = subCommand, payload = payload)
                        }
                }
            }

            offset += (2 + payloadLen)
        }

        Logger.i(TAG, "parseDeviceFeatureInfo() >>> device feat.${device.deviceFeature}")
        return true
    }

    private fun parseDeviceInfo(
        device: PartyBoxBTDevice,
        receivedPayload: ByteArray,
        receiveCommand: IGeneralCommand
    ): Boolean {
        if (receivedPayload.isEmpty()) {
            Logger.w(TAG, "parseDeviceInfo() >>> empty received payload")
            return false
        }

        val deviceIndex = receivedPayload[0]
        Logger.d(TAG, "parseDeviceInfo() >>> Device Index = $deviceIndex")
        if (receivedPayload.size <= 1) {
            Logger.w(TAG, "parseDeviceInfo() >>> empty content after device index")
            return false
        }

        val unprocessedPayload = ByteArray(receivedPayload.size - 1).also { targetBytes ->
            System.arraycopy(receivedPayload, 1, targetBytes, 0, receivedPayload.size - 1)
        }

        Logger.d(
            TAG,
            "parseDeviceInfo() >>> unprocessed payload = ${HexUtil.byte2HexStr(unprocessedPayload)}"
        )

        return when (deviceIndex) {
            PRIMARY_DEVICE_INDEX -> {
                parsePrimary(
                    device = device,
                    unprocessedPayload = unprocessedPayload,
                    receiveCommand = receiveCommand
                )
                true
            }

            SECONDARY_DEVICE_INDEX -> {
                parseSecondary(device, unprocessedPayload)
                true
            }

            else -> {
                Logger.w(TAG, "parseDeviceInfo() >>> illegal device index[$deviceIndex]")
                false
            }
        }
    }

    private fun parseSecondary(device: PartyBoxBTDevice, unprocessedPayload: ByteArray) {
        var payload = unprocessedPayload
        var tokenID: Byte?
        var tokenValue: ByteArray? = null

        val secondaryInfo = device.secondaryInfo ?: PartyBoxSecondaryInfo()

        while (payload.size > 2) {
            tokenID = payload[0]
            val tokenLen = payload[1].toInt()
            if (tokenLen > 0) {
                tokenValue = ByteArray(tokenLen).also {
                    System.arraycopy(payload, 2, it, 0, tokenLen)
                }
            }

            // unprocessed payload
            payload = if (payload.size - tokenLen - 2 > 2) {
                ByteArray(payload.size - 2 - tokenLen).also {
                    System.arraycopy(payload, tokenLen + 2, it, 0, payload.size - 2 - tokenLen)
                }
            } else {
                byteArrayOf()
            }

            when (tokenID) {
                GattPacketFormat.PRODUCT_ID -> {
                    secondaryInfo.pid = tokenValue?.let {
                        HexUtil.byte2HexStr(it).lowercase()
                    }

                    Logger.d(TAG, "parseSecondary() >>> pid[${secondaryInfo.pid}]")
                }

                GattPacketFormat.COLOR_ID -> {
                    secondaryInfo.colorID = tokenValue?.let {
                        HexUtil.byte2HexStr(it).lowercase()
                    }
                    Logger.d(TAG, "parseSecondary() >>> cid[${secondaryInfo.colorID}]")
                }

                GattPacketFormat.BATTERY_STATUS -> {
                    val batteryValue: Byte = tokenValue?.getOrNull(0) ?: 0
                    secondaryInfo._charging = (batteryValue.toInt() and 0xff) shr 7
                    secondaryInfo._batteryLv = (batteryValue.toInt() and 0x7f)
                    secondaryInfo._isAcWithoutBattery = tokenValue?.let {
                        it[0] == FLAG_AC_CABLE_WITHOUT_BATTERY.toByte()
                    } ?: false
                }

                GattPacketFormat.SERIAL_NUMBER -> {
                    secondaryInfo.serialNumber = tokenValue?.let {
                        HexUtil.hexStringToString(HexUtil.encodeHexStr(tokenValue))
                    } ?: ""

                    Logger.d(TAG, "parseSecondary() >>> serial[${secondaryInfo.serialNumber}]")
                }

                GattPacketFormat.FIRMWARE_VERSION -> {
                    secondaryInfo.firmwareVersion = formatVersion(HexUtil.byte2HexStr(tokenValue))
                    Logger.d(TAG, "parseSecondary() >>> firmware[${secondaryInfo.firmwareVersion}]")
                }

                GattPacketFormat.DEVICE_NAME -> {
                    secondaryInfo.deviceName =
                        HexUtil.hexStringToString(HexUtil.encodeHexStr(tokenValue))
                    Logger.d(TAG, "parseSecondary() >>> deviceName[${secondaryInfo.deviceName}]")
                }

                GattPacketFormat.MAC_ADDRESS -> {
                    secondaryInfo.macAddress = parseAsMacAddress(tokenValue)
                    Logger.d(TAG, "parseSecondary() >>> mac[${secondaryInfo.macAddress}]")
                }

                else -> {
                    Logger.d(TAG, "Unknown token $tokenID and value $tokenValue received")
                }
            }
        }

        device.secondaryInfo = secondaryInfo
    }

    private fun parsePrimary(
        device: PartyBoxBTDevice,
        unprocessedPayload: ByteArray,
        receiveCommand: IGeneralCommand
    ) {
        var payload = unprocessedPayload

        while (payload.size >= 2) {
            val tokenID = payload[0]
            val tokenLen = payload[1].toInt()
            val tokenValue = if (tokenLen > 0) {
                ByteArray(tokenLen).also {
                    System.arraycopy(payload, 2, it, 0, tokenLen)
                }
            } else {
                byteArrayOf()
            }

            parseSubCommand(
                device = device,
                receiveCommand = receiveCommand,
                subCommand = tokenID,
                payload = tokenValue
            )

            // next round unprocessed payload
            val leftLength = payload.size - 2 - tokenLen
            if (leftLength > 0) {
                val remainingBuffer = ByteArray(leftLength)
                System.arraycopy(payload, tokenLen + 2, remainingBuffer, 0, remainingBuffer.size)
                payload = remainingBuffer
            } else {
                break
            }
        }
    }

    private fun parseSubCommand(
        device: PartyBoxBTDevice, receiveCommand: IGeneralCommand,
        subCommand: Byte, payload: ByteArray
    ) {
        Logger.w(
            TAG, "parseSubCommand() >>> command[${HexUtil.byte2HexStr(byteArrayOf(subCommand))}]" +
                    " payload[${HexUtil.byte2HexStr(payload)}]"
        )

        when (subCommand) {
            GattPacketFormat.PRODUCT_ID -> {
                device.pid = HexUtil.byte2HexStr(payload).lowercase()
                receiveCommand.putExtra(subCommand, device.pid)
                Logger.d(TAG, "parseSubCommand() >>> pid[${HexUtil.byte2HexStr(payload)}]")
            }

            GattPacketFormat.COLOR_ID -> {
                device.colorID = HexUtil.byte2HexStr(payload).lowercase()
                receiveCommand.putExtra(subCommand, device.colorID)
                Logger.d(TAG, "parseSubCommand() >>> cid[${HexUtil.byte2HexStr(payload)}]")
            }

            GattPacketFormat.BATTERY_STATUS -> {
                val batteryValue: Byte = if (payload.isNotEmpty()) payload[0] else 0
                device.charging = (batteryValue.toInt() and 0xff) shr 7
                device.batteryLv = (batteryValue.toInt() and 0x7f)
                device.acWithoutBattery = if (payload.isNotEmpty()) {
                    payload[0] == FLAG_AC_CABLE_WITHOUT_BATTERY.toByte()
                } else false

                Logger.d(
                    TAG, "parseSubCommand() >>>" +
                            " batteryStatus[${HexUtil.byte2HexStr(payload)}]" +
                            " charging[${device.charging}]" +
                            " batteryLv[${device.batteryLv}]" +
                            " acWithoutBattery[${device.acWithoutBattery}]"
                )
            }

            GattPacketFormat.ACTIVE_CHANNEL_TOKEN_ID -> {
                Logger.d(
                    TAG,
                    "parseChannel() before >>> channel[${device.audioChannel}] device $device"
                )
                device.audioChannel = if (payload.isNotEmpty()) {
                    AudioChannel.getAudioChannel(payload[0])
                } else AudioChannel.NONE_CHANNEL
                Logger.d(
                    TAG,
                    "parseChannel() after>>> channel[${device.audioChannel}] device $device"
                )
                receiveCommand.putExtra(subCommand, device.audioChannel)
                Logger.d(TAG, "parseSubCommand() >>> channel[${HexUtil.byte2HexStr(payload)}]")

            }

            GattPacketFormat.AUDIO_SOURCE_TOKEN_ID -> {
                device.audioSource = if (payload.isNotEmpty()) {
                    AudioSource.getAudioSource(payload[0])
                } else AudioSource.NONE_AUDIO

                receiveCommand.putExtra(subCommand, device.audioSource)
                Logger.d(TAG, "parseSubCommand() >>> audio source[${HexUtil.byte2HexStr(payload)}]")
            }

            GattPacketFormat.MAC_ADDRESS -> {
                device.macAddress = parseAsMacAddress(payload)
                receiveCommand.putExtra(subCommand, device.macAddress)
                Logger.d(TAG, "parseSubCommand() >>> mac address[${device.macAddress}]")
            }

            GattPacketFormat.ROLE_TOKEN_ID -> {
                val value = payload.getOrNull(0)?.toInt()
                device.roleValue = value
                device.auraCastRoleValue = value

                receiveCommand.putExtra(subCommand, device.roleValue)
                Logger.d(TAG, "parseSubCommand() >>> role[${HexUtil.byte2HexStr(payload)}]")
            }

            GattPacketFormat.PARTY_CONNECT_MODE_TOKEN_ID -> {
                handlePartyConnectMode(
                    device = device,
                    receiveCommand = receiveCommand,
                    tokenValue = payload
                )
            }

            GattPacketFormat.TWS_STEREO_GROUP_NAME -> {
                device.stereoGroupName = HexUtil.hexStringToString(HexUtil.encodeHexStr(payload))
                receiveCommand.putExtra(subCommand, device.stereoGroupName)
                Logger.d(TAG, "parseSubCommand() >>> group name:${device.stereoGroupName}")
            }

            GattPacketFormat.TWS_STEREO_GROUP_ID -> {
                device.groupIdentifier = HexUtil.byte2HexStr(payload)
                receiveCommand.putExtra(subCommand, device.groupIdentifier)
                Logger.d(TAG, "parseSubCommand() >>> stereo group id[${device.groupIdentifier}]")
            }

            GattPacketFormat.AURACAST_MODE -> {
                device.auraCastStatus = payload.getOrNull(0)?.toInt()
                receiveCommand.putExtra(subCommand, device.auraCastStatus)
                Logger.d(TAG, "parseSubCommand() >>> AuraCast status[${device.auraCastStatus}]")
            }

            GattPacketFormat.SYNC_ON_OFF -> {
                device.syncOnOff = payload.getOrNull(0)?.toInt()?.toEnumSyncOnOff()
                    ?: EnumSyncOnOff.OFF

                receiveCommand.putExtra(subCommand, device.syncOnOff)
                Logger.d(TAG, "parseSubCommand() >>> sync onOff status[${device.syncOnOff}]")
            }

            GattPacketFormat.SERIAL_NUMBER -> {
                device.serialNumber = HexUtil.hexStringToString(HexUtil.encodeHexStr(payload))
                receiveCommand.putExtra(subCommand, device.serialNumber)
                Logger.d(TAG, "parseSubCommand() >>> serial number[${device.serialNumber}]")
            }

            GattPacketFormat.FIRMWARE_VERSION -> {
                device.firmwareVersion = formatVersion(HexUtil.byte2HexStr(payload))

                receiveCommand.putExtra(subCommand, device.firmwareVersion)
                Logger.d(TAG, "parseSubCommand() >>> firmware version[${device.firmwareVersion}]")
            }

            GattPacketFormat.DEVICE_NAME -> {
                device.deviceName = HexUtil.hexStringToString(HexUtil.encodeHexStr(payload))

                receiveCommand.putExtra(subCommand, device.deviceName)
                Logger.d(TAG, "parseSubCommand() >>> Device Name[${HexUtil.byte2HexStr(payload)}]")
            }

            GattPacketFormat.AURACAST_SQ_BROADCAST -> {
                device.auracastInfo = (device.auracastInfo ?: AuracastInfo()).also { info ->
                    info.auracastSQBroadcast = (1 == payload.getOrNull(0)?.toInt())
                }
                receiveCommand.putExtra(subCommand, device.auracastInfo)
                Logger.d(
                    TAG,
                    "parseSubCommand()>>>subCommand=${subCommand}AURACAST_SQ_BROADCAST[${
                        HexUtil.byte2HexStr(
                            payload
                        )
                    }]"
                )
            }

            else -> {
                Logger.w(
                    TAG,
                    "parseSubCommand() >>> Unknown sub command ${
                        HexUtil.byte2HexStr(
                            byteArrayOf(subCommand)
                        )
                    }" +
                            " and value [${HexUtil.byte2HexStr(payload)}] received"
                )
            }
        }
    }

    private fun handlePartyConnectMode(
        device: PartyBoxBTDevice,
        receiveCommand: IGeneralCommand,
        tokenValue: ByteArray,
    ) {
        Logger.d(TAG, "handlePartyConnectMode() >>> [${HexUtil.byte2HexStr(tokenValue)}]")

        val status = tokenValue.getOrNull(0)?.toInt()
        device.rawPartyConnectStatus = status
        receiveCommand.putExtra(GattPacketFormat.PARTY_CONNECT_MODE_TOKEN_ID, status)

        if (PartyConnectStatus.PARTY_CONNECT_OFF == PartyConnectStatus.valueOf(status)) {
            // clear stereo group id and name if PartyConnectStatus changed to OFF
            device.groupIdentifier = null
            device.stereoGroupName = null
        }
    }

    private fun formatVersion(content: String): String {
        val stringBuffer = StringBuffer()
        var i = 0
        while (i < content.length) {
            if (i + 2 <= content.length) {
                stringBuffer.append(content.substring(i, i + 2).toInt(16))
                stringBuffer.append(".")
            }
            i += 2
        }
        stringBuffer.setLength(stringBuffer.length - 1)
        return stringBuffer.toString()
    }

    private fun parsePlayerInfo(
        device: PartyBoxBTDevice,
        receivedPayload: ByteArray,
    ): Boolean {
        if (receivedPayload.isEmpty() || PRIMARY_DEVICE_INDEX != receivedPayload[0]) {
            return false
        }

        val payloadContent = HexUtil.encodeHexStr(receivedPayload)
        Logger.d(TAG, "parsePlayerInfo() >>> raw:$payloadContent")
        if (payloadContent.length <= GattPacketFormat.DEVICE_INDEX_END_INDEX) {
            return false
        }

        // Skip the device index
        var unprocessedPayload = payloadContent.substring(GattPacketFormat.DEVICE_INDEX_END_INDEX)

        while (unprocessedPayload.length >= 2) {
            val tokenID = unprocessedPayload.substring(0, 2)
            val processedUnprocessedResult = retTokenValueAndUnprocessedPayload(unprocessedPayload)
            val tokenValue = processedUnprocessedResult[0].toString()
            unprocessedPayload = processedUnprocessedResult[1].toString()

            when (tokenID) {
                GattPacketFormat.PLAYER_AUDIO_SOURCE -> {
                    Logger.d(TAG, "parsePlayerInfo() >>> audio source[$tokenValue]")
                    device.audioSource = if (tokenValue.isNotEmpty()) {
                        AudioSource.getAudioSource(tokenValue)
                    } else AudioSource.NONE_AUDIO
                }

                GattPacketFormat.PLAYER_PLAY_TOKEN_ID -> {
                    Logger.d(
                        TAG, "parsePlayerInfo() >>> UUID[${device.UUID}] " +
                                "PlayerInfo: playStatus[$tokenValue]"
                    )
                    val playStatus = PlayerStatus.getStatus(tokenValue)
                    device.isPlaying = PlayerStatus.PLAYER_STATE_PLAY == playStatus
                }

                GattPacketFormat.PLAYER_VOL_TOKEN_ID,
                GattPacketFormat.PLAYER_VOL_PRIMARY -> {
                    val intVolume = tokenValue.toIntOrNull(16)
                    if (null == intVolume) {
                        Logger.w(
                            TAG, "parsePlayerInfo() >>> UUID[${device.UUID}] " +
                                    "invalid volume[$tokenValue]"
                        )
                        continue
                    }

                    val percentageVolume = device.workaroundToPercentageVolume(intVolume)

                    Logger.i(
                        TAG, "parsePlayerInfo() >>> UUID[${device.UUID}] " +
                                "raw[$tokenValue] int[$intVolume] percentage[$percentageVolume]"
                    )
                    device.volume = percentageVolume
                }

                GattPacketFormat.PLAYER_MUTE_TOKEN_ID -> {
                    Logger.d(
                        TAG, "parsePlayerInfo() >>> UUID[${device.UUID}] " +
                                "PlayerInfo: mute[$tokenValue]"
                    )
                    device.mute = tokenValue.toIntOrNull(2)
                }

                GattPacketFormat.PLAYER_SONG_NAME -> {
                    val hexName = HexUtil.hexStringToString(tokenValue)
                    Logger.d(
                        TAG, "parsePlayerInfo() >>> UUID[${device.UUID}] " +
                                "PlayerInfo: songName[$hexName]"
                    )
                    device.songName = hexName
                }

                GattPacketFormat.PLAYER_ARTIST_NAME -> {
                    val artistName = HexUtil.hexStringToString(tokenValue)
                    Logger.d(
                        TAG, "parsePlayerInfo() >>> UUID[${device.UUID}] " +
                                "PlayerInfo: artistName[$artistName]"
                    )
                    device.artistName = artistName
                }
            }
        }

        return true
    }

    private fun retTokenValueAndUnprocessedPayload(unprocessedPayload: String): Array<String?> {
        val processedUnprocessedResult = arrayOfNulls<String>(3)
        val tokenIDValue: String
        try {
            val tokenIdIndex = 0
            val lengthIndex = tokenIdIndex + 2
            val payloadIndex = lengthIndex + 2
            val dataLength = unprocessedPayload.substring(lengthIndex, payloadIndex).toInt(16)
            tokenIDValue = unprocessedPayload.substring(payloadIndex, payloadIndex + dataLength * 2)
            val nextInfoTokenIndex = payloadIndex + dataLength * 2
            val nextUnprocessedPayload = unprocessedPayload.substring(nextInfoTokenIndex)
            processedUnprocessedResult[0] = tokenIDValue
            processedUnprocessedResult[1] = nextUnprocessedPayload
            //add offset
            processedUnprocessedResult[2] = nextInfoTokenIndex.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return processedUnprocessedResult
    }

    /**
     * Demo rsp:
     *           [EQ ID]    [Level Scope] [Band Count] [Band + Level]
     * aae2 000d 03         00            05        0100 0200 0300 0400 0500
     * aae2 000d 0a         00            05        0100 0200 0300 0400 0500
     * aae2 000e c1         06            05        0106 0200 0300 0400 05fa
     * aae2 000e c1         06            05        0100 0200 0306 0404 05fd
     * aae2 000e c1         06            05        0100 0200 0306 0404 05fd
     */
    private fun parseAdvanceEQ(device: PartyBoxBTDevice, receivedPayload: ByteArray): Boolean {
        if (receivedPayload.isEmpty()) {
            return false
        }

        // may over 127
        val activeEqID = receivedPayload[0].toUByte().toInt()
        val activeEqCategory = EnumEQCategory.valueOf(activeEqID) ?: run {
            Logger.w(TAG, "parseAdvanceEQ() >>> unknown eq category for [$activeEqID]")
            return false
        }

        val customEQPayload = receivedPayload.safetySubArray(start = 1, end = receivedPayload.size)
        if (null == customEQPayload) {
            Logger.w(TAG, "parseAdvanceEQ() >>> null custom EQ payload")
            return false
        }

        // parse raw eq data: category, custom scope, custom band count, custom band level
        if (isPartyBoxCategory(device.pid)) {
            device.rawEQSettings =
                parseToPartyBoxRawEQSettings(
                    activeEQ = activeEqCategory,
                    customPayload = customEQPayload
                )
        } else {
            device.rawEQSettings =
                parseToOneRawEQSettings(
                    activeEQ = activeEqCategory,
                    customPayload = customEQPayload
                )
        }
        Logger.d(TAG, "parseAdvanceEQ() >>> parse result: ${device.rawEQSettings}")
        return true
    }

    private fun parseToOneRawEQSettings(
        activeEQ: EnumEQCategory,
        customPayload: ByteArray
    ): RawEQSettings? {
        Logger.d(
            TAG, "parseToOneRawEQSettings() >>> active[$activeEQ] " +
                    "custom payload:${HexUtil.byte2HexStr(customPayload)}"
        )
        if (customPayload.isEmpty()) {
            Logger.w(TAG, "parseToOneRawEQSettings() >>> empty custom EQ payload")
            return null
        }

        val iVersion = customPayload[0].toUByte().toInt()
        Logger.d(TAG, "parseToOneRawEQSettings() >>> iVersion: $iVersion")

        val version = EnumEQCategory.valueOf(iVersion)
        Logger.d(TAG, "parseToOneRawEQSettings() >>> version: $version")

        if (null == version) {
            Logger.w(TAG, "parseToOneRawEQSettings() >>> unknown version[$iVersion]")
            return null
        }

        val payload = customPayload.safetySubArray(start = 1, end = customPayload.size)
        if (null == payload || payload.isEmpty()) {
            Logger.w(TAG, "parseToOneRawEQSettings() >>> empty payload after parse version flag")
            return RawEQSettings(version = version, activeEQ = activeEQ)
        }

        return when (version) {
            EnumEQCategory.CUSTOM_FULL -> { // C2 Version
                parseAsCustomC2(activeEQ = activeEQ, customPayload = payload)
            }

            else -> { // C1 Version
                parseAsCustomC1(activeEQ = activeEQ, customPayload = payload)
            }
        }
    }

    private fun parseToPartyBoxRawEQSettings(
        activeEQ: EnumEQCategory,
        customPayload: ByteArray
    ): RawEQSettings? {
        Logger.d(
            TAG, "parseToPartyBoxRawEQSettings() >>> active[$activeEQ] " +
                    "custom payload:${HexUtil.byte2HexStr(customPayload)}"
        )
        if (customPayload.isEmpty()) {
            Logger.w(TAG, "parseToPartyBoxRawEQSettings() >>> empty custom EQ payload")
            return null
        }

        var iVersion = customPayload[0].toUByte().toInt()
        var startPos = 1
        if (iVersion < EnumEQCategory.CUSTOM.value.toUByte().toInt()) {
            Logger.w(
                TAG,
                "parseToPartyBoxRawEQSettings() >>> unknown version[$iVersion], assign to ${EnumEQCategory.CUSTOM.value}"
            )
            iVersion = EnumEQCategory.CUSTOM.value.toInt()
            startPos = 0
        }
        Logger.d(TAG, "parseToPartyBoxRawEQSettings() >>> iVersion: $iVersion, startPos: $startPos")

        val version = EnumEQCategory.valueOf(iVersion)
        Logger.d(TAG, "parseToPartyBoxRawEQSettings() >>> version: $version")

        if (null == version) {
            Logger.w(TAG, "parseToPartyBoxRawEQSettings() >>> unknown version[$iVersion]")
            return null
        }

        val payload = customPayload.safetySubArray(start = startPos, end = customPayload.size)
        if (null == payload || payload.isEmpty()) {
            Logger.w(
                TAG,
                "parseToPartyBoxRawEQSettings() >>> empty payload after parse version flag"
            )
            return RawEQSettings(version = version, activeEQ = activeEQ)
        }

        return when (version) {
            EnumEQCategory.CUSTOM_FULL -> { // C2 Version
                parseAsCustomC2(activeEQ = activeEQ, customPayload = payload)
            }

            else -> { // C1 Version
                parseAsCustomC1(activeEQ = activeEQ, customPayload = payload)
            }
        }
    }

    private fun parseAsCustomC2(
        activeEQ: EnumEQCategory,
        customPayload: ByteArray
    ): RawEQSettings {
        Logger.d(TAG, "parseAsCustomC2() >>> ${HexUtil.byte2HexStr(customPayload)}")
        // jump first one bytes: C2
        var subPayload = customPayload
        val bCount = subPayload.getOrNull(0) ?: run {
            Logger.w(TAG, "parseAsCustomC2() >>> not enough length while cut bCount")
            return RawEQSettings(version = EnumEQCategory.CUSTOM_FULL, activeEQ = activeEQ)
        }

        subPayload = subPayload.safetySubArray(start = 1, end = subPayload.size) ?: run {
            Logger.w(TAG, "parseAsCustomC2() >>> not enough length while cut sampleRate")
            return RawEQSettings(version = EnumEQCategory.CUSTOM_FULL, activeEQ = activeEQ)
        }

        val sampleRate: Int = subPayload.safetySubArray(0, 4)?.toInt() ?: run {
            Logger.w(
                TAG,
                "parseAsCustomC2() >>> fail to parse sample rate[${
                    HexUtil.byte2HexStr(
                        subPayload.safetySubArray(
                            0,
                            4
                        )
                    )
                }]"
            )
            return RawEQSettings(version = EnumEQCategory.CUSTOM_FULL, activeEQ = activeEQ)
        }

        subPayload = subPayload.safetySubArray(start = 4, end = subPayload.size) ?: run {
            Logger.w(TAG, "parseAsCustomC2() >>> not enough length while cut customBandC2")
            return RawEQSettings(version = EnumEQCategory.CUSTOM_FULL, activeEQ = activeEQ)
        }

        val result = RawEQSettings(
            version = EnumEQCategory.CUSTOM_FULL,
            activeEQ = activeEQ,
            customEQBandCount = EnumBandCount.valueOf(bCount) ?: EnumBandCount.PARAMETER_BAND_7,
            sampleRate = sampleRate,
            customBandC2 = subPayload.parseAsRawBandsC2()
        )

        Logger.i(TAG, "parseAsCustomC2() >>> custom eq settings after parse:\n$result")
        return result
    }

    private fun ByteArray.parseAsRawBandsC2(): List<RawBandC2> {
        val bands = mutableListOf<RawBandC2>()

        var index = 0

        while (index <= size - 13) {
            val band = EnumPresetEQBandType.valueOf(this[index]) ?: break
            val gain = safetySubArray(index + 1, index + 5)?.toFloat() ?: break
            val frequency = safetySubArray(index + 5, index + 9)?.toFloat() ?: break
            val q = safetySubArray(index + 9, index + 13)?.toFloat() ?: break

            bands.add(
                RawBandC2(
                    band = band,
                    gain = gain,
                    frequency = frequency,
                    q = q
                )
            )

            index += 13
        }

        return bands.toList()
    }

    private fun parseAsCustomC1(
        activeEQ: EnumEQCategory,
        customPayload: ByteArray
    ): RawEQSettings {
        Logger.d(TAG, "parseAsCustomC1() >>> ${HexUtil.byte2HexStr(customPayload)}")
        var subPayload = customPayload
        val bScope = subPayload.getOrNull(0) ?: run {
            Logger.w(
                TAG,
                "parseAsCustomC1() >>> fail to cat scope:${HexUtil.byte2HexStr(subPayload)}"
            )
            return RawEQSettings(version = EnumEQCategory.CUSTOM, activeEQ = activeEQ)
        }

        val bCount = subPayload.getOrNull(1) ?: run {
            Logger.w(
                TAG,
                "parseAsCustomC1() >>> fail to cat count:${HexUtil.byte2HexStr(subPayload)}"
            )
            return RawEQSettings(version = EnumEQCategory.CUSTOM, activeEQ = activeEQ)
        }

        // jump [Level Scope] and [Band Count]
        subPayload = subPayload.safetySubArray(start = 2, end = subPayload.size) ?: run {
            Logger.w(
                TAG,
                "parseAsCustomC1() >>> custom eq bands lack of length:${
                    HexUtil.byte2HexStr(subPayload)
                }"
            )
            return RawEQSettings(version = EnumEQCategory.CUSTOM, activeEQ = activeEQ)
        }

        val result = RawEQSettings(
            version = EnumEQCategory.CUSTOM,
            activeEQ = activeEQ,
            customEQScope = EnumEQScope.valueOf(bScope) ?: EnumEQScope.LEVEL_SCOPE_6,
            customEQBandCount = EnumBandCount.valueOf(bCount) ?: EnumBandCount.PARAMETER_BAND_5,
            customBandC1 = subPayload.parseAsRawBandsC1()
        )

        Logger.i(TAG, "parseAsCustomC1() >>> custom eq settings after parse:\n$result")
        return result
    }

    private fun ByteArray.parseAsRawBandsC1(): List<RawBandC1> {
        val bands = mutableListOf<RawBandC1>()
        var index = 0

        for (i in 0..this.size - 2 step 2) {
            val band = EnumCustomEQBandType.valueOf(this[i]) ?: break
            // may over 127
            val level = EnumCustomEQLevel.valueOf(this[i + 1].toUByte().toInt()) ?: break

            bands.add(
                RawBandC1(
                    band = band,
                    level = level
                )
            )

            index++
        }

        return bands.toList()
    }

    private fun parseAsMacAddress(bytes: ByteArray?): String? {
        if (null == bytes || bytes.isEmpty()) {
            Logger.d(TAG, "parseAsMacAddress() >>> mac address bytes is null")
            return null
        }

        val payloadContent = HexUtil.encodeHexStr(bytes).uppercase(Locale.ROOT)
        if (payloadContent.all { c -> c == 'F' || c == 'f' }) {
            Logger.d(TAG, "parseAsMacAddress() >>> mac address illegal[$payloadContent]")
            return null
        }

        //Device sending the secondary speaker mac address
        val address = payloadContent.chunked(2).joinToString(":")
        Logger.d(TAG, "parseAsMacAddress() >>> mac address received[$address]")
        return address
    }

    private fun parseLightInfo(device: PartyBoxBTDevice, payload: ByteArray?): Boolean {
        val isStudioDevice = device.pid.isHomeBtCategory() && device.pid.isSupportLightControl()
        Logger.d(TAG, "parseLightInfo() >>> isStudioDevice[$isStudioDevice]")
        return if (isStudioDevice) {
            ReqStudioLightInfoCommand.parseAsLightInfo(device, payload)
        } else {
            parseAsLightInfo(device, payload)
        }
    }

    /**
     * @return null if invalid payload.
     */
    private fun parseAsLightInfo(device: PartyBoxBTDevice, payload: ByteArray?): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }

        val deviceIdx = payload.safetySubArray(0, 1)?.getOrNull(0)?.toInt()
        if (0 != deviceIdx) { // didn't parse light info if not this is not the primary device.
            return false
        }

        var unProcessed = payload.safetySubArray(1, payload.size)

        while (null != unProcessed && unProcessed.isNotEmpty()) {
            val command = unProcessed.safetySubArray(0, 1)?.getOrNull(0) ?: break
            if (0x1 == command.toInt()) {
                break
            }

            val length = unProcessed.safetySubArray(1, 2)?.getOrNull(0)?.toInt()
            if (null == length || length < 0) {
                break
            }

            val subPayload = unProcessed.safetySubArray(2, 2 + length)
            parseLightInfoSubPayload(device = device, command = command, subPayload = subPayload)
            unProcessed = unProcessed.safetySubArray(2 + length, unProcessed.size)

            Logger.d(
                TAG, "parseAsLightInfo() >>>" +
                        " command[${HexUtil.byte2HexStr(byteArrayOf(command))}]" +
                        " length[$length]" +
                        " payload[${HexUtil.byte2HexStr(subPayload)}]" +
                        " remain[${HexUtil.byte2HexStr(unProcessed)}]"
            )
        }

        Logger.i(TAG, "parseAsLightInfo() >>> ${device.lightInfo}")
        return true
    }

    private fun parseLightInfoSubPayload(
        device: PartyBoxBTDevice,
        command: Byte,
        subPayload: ByteArray?
    ) {
        when (command) {
            GattPacketFormat.DevLightToken.PATTERN_ID -> {
                EnumLightPattern.getByValue(subPayload)?.let { pattern ->
                    device.lightInfo.pattern = pattern
                }
            }

            GattPacketFormat.DevLightToken.MAIN_SWITCH -> {
                EnumLightSwitch.valueOf(subPayload)?.let { mainSwitch ->
                    device.lightInfo.mainSwitch = mainSwitch
                }
            }

            GattPacketFormat.DevLightToken.STROBE_LIGHT,
            GattPacketFormat.DevLightToken.FIGURE_8_LIGHT,
            GattPacketFormat.DevLightToken.SIDE_2_RING_LIGHT,
            GattPacketFormat.DevLightToken.STRIPE_LIGHT_PATTERN,
            GattPacketFormat.DevLightToken.STAR_LIGHT_PATTERN,
            GattPacketFormat.DevLightToken.STROBE_UP_PATTERN,
            GattPacketFormat.DevLightToken.STROBE_DOWN_PATTERN,
            GattPacketFormat.DevLightToken.EDGE_PATTERN -> {
                EnumLightSwitch.valueOf(subPayload)?.let { switch ->
                    device.lightInfo.updateElement(logTag = TAG, element = command, switch = switch)
                }
            }

            GattPacketFormat.DevLightToken.PATTERN_LOOPING -> {
                EnumPatternLoop.valueOf(subPayload)?.let { loopingPattern ->
                    device.lightInfo.loopingPattern = loopingPattern
                }
            }

            GattPacketFormat.DevLightToken.CURRENT_COLOR -> {
                parseAsColor(payload = subPayload)?.let { color ->
                    device.lightInfo.color = color
                }
            }
            // More light info sub command to parse
        }
    }

    private fun parseAsColor(payload: ByteArray?): Color? {
        payload ?: return null
        val hex = HexUtil.byte2HexStr(payload)
        if (hex.isNullOrBlank()) {
            return null
        }

        val red = hex.safetySubString(0, 2)?.toIntOrNull(16) ?: return null
        val green = hex.safetySubString(2, 4)?.toIntOrNull(16) ?: return null
        val blue = hex.safetySubString(4, 6)?.toIntOrNull(16) ?: return null

        return Color(red = red, green = green, blue = blue)
    }

    private fun parseAsInActiveLightPattern(
        device: PartyBoxBTDevice,
        payload: ByteArray?
    ): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }

        val deviceIdx = payload.safetySubArray(0, 1)?.getOrNull(0)?.toInt()
        if (0 != deviceIdx) { // didn't parse light info if not this is not the primary device.
            return false
        }

        // clear all cached inactive patterns before load.
        // device will return empty payload if all patterns are available.
        device.lightInfo.inActivePatterns.clear()

        var unProcessed = payload.safetySubArray(1, payload.size)

        while (null != unProcessed && unProcessed.isNotEmpty()) {
            val bPattern = unProcessed.safetySubArray(0, 1)?.getOrNull(0) ?: break
            EnumLightPattern.getByValue(bPattern)?.let { pattern ->
                device.lightInfo.inActivePatterns.add(pattern)
            }

            unProcessed = unProcessed.safetySubArray(1, unProcessed.size)
        }

        return true
    }

    private fun parseAsFeedbackTone(device: PartyBoxBTDevice, payload: ByteArray?): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }

        val flag = payload.safetySubArray(0, 1)?.getOrNull(0)?.toInt() ?: return false
        device.isFeedbackToneOn = 0 != flag
        return true
    }

    /**
     * [Key ID(1 byte) + Value Len(1 byte) + Value(N Bytes)] * X
     *
     * Key ID: Refs [EnumBatteryStatusFeature]
     */
    private fun parseAsBatteryStatus(device: PartyBoxBTDevice, payload: ByteArray?): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }

        var index = 0

        while (index < payload.size) {
            val command = payload.getOrNull(index) ?: return false
            val subLength = payload.getOrNull(index + 1) ?: return false
            val subPayload = payload.safetySubArray(start = index + 2, end = index + 2 + subLength)
            if (null == subPayload || subPayload.isEmpty()) {
                return false
            }
            Logger.d(
                TAG,
                "parseAsBatteryStatus>>> command:${HexUtil.encodeHexStr(byteArrayOf(command))}, subPayload:${
                    HexUtil.encodeHexStr(subPayload)
                }"
            )
            when (command) {
                EnumBatteryStatusFeature.TOTAL_POWER_ON_DURATION.flag -> {
                    device.batteryStatusFeature.totalPowerOnDurationMins = subPayload.toInt()
                    Logger.d(
                        TAG,
                        "subPayload: ${HexUtil.encodeHexStr(subPayload)}, totalPowerOnDurationMins: ${device.batteryStatusFeature.totalPowerOnDurationMins}"
                    )

                }

                EnumBatteryStatusFeature.TOTAL_PLAYBACK_TIME_DURATION.flag -> {
                    device.batteryStatusFeature.totalPlaybackTimeDurationMins = subPayload.toInt()
                    Logger.d(
                        TAG,
                        "subPayload: ${HexUtil.encodeHexStr(subPayload)}, totalPlaybackTimeDurationMins: ${device.batteryStatusFeature.totalPlaybackTimeDurationMins}"
                    )

                }
            }

            index += (2 + subLength)
        }

        return true
    }

    /**
     *copy from the history code
     */
    private fun parseScreenDisplayInfo(
        device: PartyBoxBTDevice,
        payload: ByteArray?
    ): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }
        val receivedPayload = HexUtil.encodeHexStr(payload)
        Log.d(
            TAG,
            "ReqScreenDisplayCommand  onReceive:$receivedPayload"
        )
        device.screenDisplayInfo = ScreenDisplayInfo.parseAndSetScreenDisplayInfo(receivedPayload)
        return true
    }

    /**
     *copy from the history code
     */
    private fun parseAndSetSleepModeInfo(
        device: PartyBoxBTDevice,
        payload: ByteArray?
    ): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }
        val receivedPayload = HexUtil.encodeHexStr(payload)
        Log.d(
            TAG,
            "parseAndSetSleepModeInfo  onReceive:$receivedPayload"
        )
        device.sleepModeInfo = SleepModeInfo.parseAndSetSleepModeInfo(receivedPayload)
        return true
    }

    /**
     *copy from the history code
     */
    private fun parseAlarmInfo(
        device: PartyBoxBTDevice,
        payload: ByteArray?
    ): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }
        val receivedPayload = HexUtil.encodeHexStr(payload)
        Log.d(TAG, "parseAlarmInfo  onReceive:$receivedPayload")
        ReqAlarmInfoCommand.parseAlarmInfo(device, receivedPayload)
        return true
    }

    /**
     * copy from history
     */
    private fun parseAmbientLightCommand(
        device: PartyBoxBTDevice,
        payload: ByteArray?
    ): Boolean {
        if (null == payload || payload.isEmpty()) {
            return false
        }
        val receivedPayload = HexUtil.encodeHexStr(payload)
        Log.d(TAG, "parseAmbientLightCommand  onReceive:$receivedPayload")
        device.ambientLightInfo = AmbientLightInfo.parseAndSetAmbientLightInfo(receivedPayload)
        return true
    }

    private fun parseRadioInfo(device: PartyBoxBTDevice, bytesPayload: ByteArray?): Boolean {
        if (bytesPayload.isNullOrEmpty()) {
            Logger.d(TAG, "parseRadioInfo() >>> bytesPayload is empty")
            return false
        }

        val hexPayload = HexUtil.encodeHexStr(bytesPayload)
        if (hexPayload.isNullOrBlank()) {
            Logger.d(TAG, "parseRadioInfo() >>> hexPayload is empty")
            return false
        }

        Logger.d(TAG, "parseRadioInfo() >>> receivedPayload:$hexPayload")

        val radioInfo = ReqRadioInfoCommand.parseAsRadioInfo(logTag = TAG, device = device, hexPayload = hexPayload)
        device.radioInfo = radioInfo

        Logger.d(TAG, "parseRadioInfo() >>> radioInfo:${device.radioInfo}")

        return true
    }

     /**
     * Process auto off timer response
     * @param device The PartyBox device
     * @param payload The response payload (4 bytes)
     * @return true if processing was successful
     */
     private fun processAutoOffTimerResponse(device: PartyBoxBTDevice, payload: ByteArray?): Boolean {
        if (payload == null || payload.size != 4) {
            Logger.d(TAG, "processAutoOffTimerResponse() >>> invalid payload size: ${payload?.size}")
            return false
        }

        // Parse the response payload
        val timerSettings = ByteBuffer.wrap(payload.sliceArray(0..1))
            .order(ByteOrder.LITTLE_ENDIAN)
            .short.toInt()
        val remainingTime = ByteBuffer.wrap(payload.sliceArray(2..3))
            .order(ByteOrder.LITTLE_ENDIAN)
            .short.toInt()

        // Create response object
        val response = AutoOffTimerRsp(timerSettings = timerSettings, remainingTime = remainingTime)
        Logger.d(TAG, "processAutoOffTimerResponse() >>> timerSettings: $timerSettings, remainingTime: $remainingTime")

        // Update device state with the response
        device.autoOffTimerRsp = response
        return true
    }
}
