package com.harman.command.one.command

import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.SetLightInfoRequest
import com.harmanbar.ble.utils.GsonUtil

/**
 * 0x1836
 */
class ResetLightPatternColorCommand(id: String) : OneGattCommand() {

    override var commandID: Int = EnumCommandMapping.RESET_LIGHT_PATTERN_COLOR.bleCmd

    override val payload: ByteArray =
        EnumCommandMapping.RESET_LIGHT_PATTERN_COLOR.payloadFormat.format(id).toByteArray()

}