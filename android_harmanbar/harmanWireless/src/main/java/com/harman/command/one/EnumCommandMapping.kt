package com.harman.command.one

import com.harmanbar.ble.statistic.StatisticConstant

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/5.
 *
 * Mapping BLE command with WiFi command which representing the same meaning.
 */
enum class EnumCommandMapping(
    val bleCmd: Int, // might be a negative num if this is a WiFi only CMD.
    val wifiCmd: String = "", // might be blank if this is a UPNP only CMD.
    val payloadFormat: String = ""
) {
    GET_DEVICE_INFO(
        bleCmd = StatisticConstant.LP_GET_DEVICEINFO_CMD,
        wifiCmd = "getDeviceInfo"
    ),
    SET_DEVICE_NAME(
        bleCmd = StatisticConstant.SET_DEVICE_NAME,
        wifiCmd = "setDeviceName",
        payloadFormat = "{\"device_name\": \"%s\"}"
    ),
    LP_GET_AP_LIST_CMD(
        bleCmd = StatisticConstant.LP_GET_AP_LIST_CMD,
        wifiCmd = "getApList"
    ),
    LP_SET_CAST_GROUP(
        bleCmd = StatisticConstant.LP_SET_CAST_GROUP,
        wifiCmd = "setCastGroup"
    ),
    PLAY_PAUSE_MUSIC( // UPNP only
        bleCmd = StatisticConstant.SEND_APP_CONTROLLER,
        payloadFormat = "{\"key_pressed\": \"musicPlayPause\"}"
    ),
    PREV_MUSIC( // UPNP only
        bleCmd = StatisticConstant.SEND_APP_CONTROLLER,
        payloadFormat = "{\"key_pressed\": \"prev\"}"
    ),
    NEXT_MUSIC( // UPNP only
        bleCmd = StatisticConstant.SEND_APP_CONTROLLER,
        payloadFormat = "{\"key_pressed\": \"next\"}"
    ),
    SET_VOLUME_AND_MUTE(
        // UPNP only
        bleCmd = StatisticConstant.SET_VOLUME_LEVEL,
        payloadFormat = "{\"volume_level\": \"%s\",\"mute_status\": \"%s\"}",
    ),
    GET_FEATURE_SUPPORT(
        bleCmd = StatisticConstant.GET_FEATURE_SUPPORT,
        wifiCmd = "getFeatureSupport"
    ),
    GET_EQ_LIST(
        bleCmd = StatisticConstant.GET_EQ_LIST,
        wifiCmd = "getEQList"
    ),
    SET_ACTIVE_EQ(
        bleCmd = StatisticConstant.SET_ACTIVE_EQ,
        wifiCmd = "setActiveEQ"
        // payloadFormat as EQListItem
    ),
    GET_EQ(
        bleCmd = StatisticConstant.GET_EQ,
        wifiCmd = "getEQ"
    ),
    SET_EQ(
        bleCmd = StatisticConstant.SET_EQ,
        wifiCmd = "setEQ"
        // payloadFormat as EQItem
    ),
    AUTH_START(
        bleCmd = StatisticConstant.LP_AUTH_START,
        wifiCmd = "setAuthStart",
        payloadFormat = "{\"timeout\":%d}"
    ),
    AUTH_CANCEL(
        bleCmd = StatisticConstant.LP_AUTH_CANCEL,
        wifiCmd = "setAuthCancel"
    ),
    AUTH_PAIR(
        bleCmd = StatisticConstant.LP_AUTH_PAIR,
    ),
    GET_AP_LIST(
        bleCmd = StatisticConstant.LP_GET_AP_LIST_CMD,
        wifiCmd = "getApList"
    ),
    SET_WIFI_NETWORK(
        bleCmd = StatisticConstant.LP_SEND_SSID_PASSWORD,
        wifiCmd = "setWifiNetwork"
    ),
    GET_OTA_STATUS(
        bleCmd = StatisticConstant.LP_GET_OTA_STATUS,
        wifiCmd = "getOtaStatus"
    ),
    REQ_DEV_OTA(
        bleCmd = StatisticConstant.LP_REQ_DEV_OTA,
        wifiCmd = "requestDeviceOta"
    ),
    GET_OTA_ACCESS_POINT(
        bleCmd = StatisticConstant.LP_GET_OTA_ACCESS_POINT,
        wifiCmd = "getOTAAccessPoint"
    ),
    GET_BATTERY_STATUS(
        bleCmd = StatisticConstant.GET_BATTERY,
        wifiCmd = "getBatteryStatus"
    ),
    SET_CALIBRATION(
        bleCmd = StatisticConstant.SET_CALIBRATION,
        wifiCmd = "setCalibration"
    ),
    GET_CALIBRATION_STATE(
        bleCmd = StatisticConstant.GET_CALIBRATION_STATE,
        wifiCmd = "getCalibrationState"
    ),
    CANCEL_CALIBRATION(
        bleCmd = StatisticConstant.CANCEL_CALIBRATION,
        wifiCmd = "cancelCalibration"
    ),
    GET_C4A_PERMISSION_STATUS(
        bleCmd = StatisticConstant.GET_C4A_PERMISSION_STATUS,
        wifiCmd = "getC4aPermissionStatus"
    ),
    SET_C4A_PERMISSION_STATUS(
        bleCmd = StatisticConstant.SET_C4A_PERMISSION_STATUS,
        wifiCmd = "setC4aPermissionStatus",
        payloadFormat = "{\"status\": \"%d\"}"
    ),
    ALEXA_CBL_STATUS(
        bleCmd = StatisticConstant.GET_CBL_STATUS,
        wifiCmd = "alexaCBLStatus"
    ),
    ALEXA_CBL_LOGOUT(
        bleCmd = StatisticConstant.CBL_LOGOUT,
        wifiCmd = "CBLLogout"
    ),
    GET_LWA_STATE(
        bleCmd = -1,
        wifiCmd = "getLWAState"
    ),
    REQUEST_LWA_LOGOUT(
        bleCmd = -1,
        wifiCmd = "requestLWALogout"
    ),
    SET_LWA_AUTH_CODE(
        bleCmd = -1,
        wifiCmd = "setLWAAuthCode",
        payloadFormat = "{\"authorization_code\": \"%s\",\"client_id\": \"%s\",\"redirect_uri\": \"%s\"}"
    ),
    SET_VOICE_REQUEST_START_TONE(
        bleCmd = -1,
        wifiCmd = "setVoiceRequestStartTone",
        payloadFormat = "{\"source\": \"%s\",\"status\": \"%s\"}"
    ),
    GET_VOICE_REQUEST_START_TONE(
        bleCmd = -1,
        wifiCmd = "getVoiceRequestStartTone",
        payloadFormat = "{\"source\": \"%s\"}"
    ),
    SET_VOICE_REQUEST_END_TONE(
        bleCmd = -1,
        wifiCmd = "setVoiceRequestEndTone",
        payloadFormat = "{\"source\": \"%s\",\"status\": \"%s\"}"
    ),
    GET_VOICE_REQUEST_END_TONE(
        bleCmd = -1,
        wifiCmd = "getVoiceRequestEndTone",
        payloadFormat = "{\"source\": \"%s\"}"
    ),
    SET_VOICE_LANGUAGE(
        bleCmd = -1,
        wifiCmd = "setVoiceLanguage",
        payloadFormat = "{\"source\": \"%s\",\"locale\": \"%s\"}"
    ),
    GET_SUPPORTED_VOICE_LANGUAGE(
        bleCmd = -1,
        wifiCmd = "getSupportedVoiceLanguage",
        payloadFormat = "{\"source\": \"%s\"}"
    ),
    REQUEST_GOOGLE_LOGOUT(
        bleCmd = -1,
        wifiCmd = "requestGoogleLogout"
    ),
    GET_VOICE_LANGUAGE(
        bleCmd = -1,
        wifiCmd = "getVoiceLanguage",
        payloadFormat = "{\"source\": \"%s\"}"
    ),
    SET_DEBUG_MODE(
        bleCmd = -1,
        wifiCmd = "setDebugMode",
        payloadFormat = "{\"debug_mode\": \"%s\"}"
    ),
    GET_CHROMECAST_OPT_IN(
        bleCmd = StatisticConstant.GET_CHROME_CAST_OPT_IN,
        wifiCmd = "getChromecastOptIn"
    ),
    SET_CHROMECAST_OPT_IN(
        bleCmd = StatisticConstant.SET_CHROME_CAST_OPT_IN,
        wifiCmd = "setChromecastOptIn",
        payloadFormat = "{\"opt_in\": \"%s\"}"
    ),
    SET_WIFI_COUNTRY_CODE(
        bleCmd = StatisticConstant.SET_WIFI_COUNTRY_CODE,
        wifiCmd = "setWifiCountryCode",
        payloadFormat = "{\"country_code\": \"%s\"}"
    ),
    ENTER_AURA_CAST(
        bleCmd = StatisticConstant.ENTER_AURACAST,
        wifiCmd = "enterAuracast"
    ),
    EXIT_AURA_CAST(
        bleCmd = StatisticConstant.EXIT_AURACAST,
        wifiCmd = "exitAuracast"
    ),
    SET_AURA_CAST_SQ_MODE(
        bleCmd = StatisticConstant.SET_AURACAST_SQ_MODE,
        wifiCmd = "setAuracastSQMode",
        payloadFormat = "{\"status\": \"%s\", \"keepAlive\":{\"interval\":3}}}"
    ),
    GET_AURA_CAST_SQ_MODE(
        bleCmd = StatisticConstant.GET_AURACAST_SQ_MODE,
        wifiCmd = "getAuracastSQMode"
    ),
    NOTIFY_AURA_CAST_SQ_MODE(
        bleCmd = StatisticConstant.NOTIFY_AURACAST_SQ_MODE,
        wifiCmd = "notifyAuracastSQMode"
    ),
    PLAY_PARTY_SOUND(
        bleCmd = StatisticConstant.SET_PLAY_PARTY_SOUND,
        wifiCmd = "playPartySound"
    ),
    SET_DJ_EVENT(
        bleCmd = StatisticConstant.SET_DJ_EVENT,
        wifiCmd = "setDJEvent"
    ),
    GET_SMART_BTN_CONFIG(
        bleCmd = StatisticConstant.GET_SMART_BUTTON_CONFIG,
        wifiCmd = "getSmartButtonConfig"
    ),
    SET_SMART_BTN_CONFIG(
        bleCmd = StatisticConstant.SET_SMART_BUTTON_CONFIG,
        wifiCmd = "setSmartButtonConfig"
    ),
    PREVIEW_SOUNDSCAPE(
        bleCmd = StatisticConstant.PREVIEW_SOUND_SCAPE,
        wifiCmd = "previewSoundscape"
    ),
    CANCEL_SOUNDSCAPE(
        bleCmd = StatisticConstant.CANCEL_SOUND_SCAPE,
        wifiCmd = "cancelSoundscape"
    ),
    GET_SOUNDSCAPE_V2_CONFIG(
        bleCmd = StatisticConstant.GET_SOUNDSCAPE_V2_CONFIG,
        wifiCmd = "getSoundscapeV2Config"
    ),
    SET_SOUNDSCAPE_V2_CONFIG(
        bleCmd = StatisticConstant.SET_SOUNDSCAPE_V2_CONFIG,
        wifiCmd = "setSoundscapeV2Config"
    ),
    CONTROL_SOUNDSCAPE_V2(
        bleCmd = StatisticConstant.CONTROL_SOUNDSCAPE_V2,
        wifiCmd = "controlSoundscapeV2"
    ),
    GET_SOUNDSCAPE_V2_STATE(
        bleCmd = StatisticConstant.GET_SOUNDSCAPE_V2_STATE,
        wifiCmd = "getSoundscapeV2State"
    ),
    NOTIFY_SOUNDSCAPE_V2_STATE(
        bleCmd = StatisticConstant.NOTIFY_SOUNDSCAPE_V2_STATE,
        wifiCmd = "notifySoundscapeV2State"
    ),
    GET_DEVICE_USAGE(
        bleCmd = StatisticConstant.GET_PRODUCT_USAGE,
        wifiCmd = "getProductUsage"
    ),
    GET_DEVICE_NAME(
        bleCmd = StatisticConstant.GET_DEVICE_NAME,
        wifiCmd = "getDeviceName"
    ),
    PLAY_DEMO_SOUND(
        bleCmd = StatisticConstant.LP_PLAY_DEMO_SOUND,
        wifiCmd = "playDemoSound"
    ),
    CANCEL_DEMO_SOUND(
        bleCmd = StatisticConstant.LP_CANCEL_DEMO_SOUND,
        wifiCmd = "cancelDemoSound"
    ),
    GET_REAR_SPEAKER_VOLUME(
        bleCmd = StatisticConstant.GET_REAR_SPEAKER_VOLUME,
        wifiCmd = "getRearSpeakerVolume"
    ),
    SET_REAR_SPEAKER_VOLUME (
        bleCmd = StatisticConstant.SET_REAR_SPEAKER_VOLUME,
        wifiCmd = "setRearSpeakerVolume"
    ),
    GET_PROD_SETTING(
        bleCmd = StatisticConstant.GET_PROD_SETTING,
        wifiCmd = "getProdSetting"
    ),
    SET_PROD_SETTING(
        bleCmd = StatisticConstant.SET_PROD_SETTING,
        wifiCmd = "setProdSetting"
    ),
    SEND_APP_CONTROLLER(
        bleCmd = StatisticConstant.SEND_APP_CONTROLLER,
        wifiCmd = "sendAppController"
    ),
    TRIGGER_DIAGNOSIS(
        bleCmd = -1,
        wifiCmd = "triggerDiagnosis"
    ),
    NOTIFY_DIAGNOSIS_STATUS(
        bleCmd = -1,
        wifiCmd = "notifyDiagnosisStatus"
    ),
    SET_FACTORY_RESTORE(
        bleCmd = StatisticConstant.SET_FACTORY_RESTORE,
        wifiCmd = "setFactoryRestore"
    ),
    SET_AUTO_POWER_OFF_TIMER(
        bleCmd = StatisticConstant.SET_AUTO_POWER_OFF_TIMER,
        wifiCmd = "setAutoPowerOffTimer",
        payloadFormat = "{\"timer\": \"%s\"}"
    ),
    GET_AUTO_POWER_OFF_TIMER(
        bleCmd = StatisticConstant.GET_AUTO_POWER_OFF_TIMER,
        wifiCmd = "getAutoPowerOffTimer"
    ),
    SET_BLUETOOTH_CONFIG(
        bleCmd = StatisticConstant.SET_BLUETOOTH_CONFIG,
        wifiCmd = "setBluetoothConfig",
        payloadFormat = "{\"automatic\": \"%s\"}"
    ),
    GET_BLUETOOTH_CONFIG(
        bleCmd = StatisticConstant.GET_BLUETOOTH_CONFIG,
        wifiCmd = "getBluetoothConfig"
    ),
    SET_FEEDBACK_TONE_CONFIG(
        bleCmd = StatisticConstant.SET_FEED_BACK_TONE_CONFIG,
        wifiCmd = "setFeedbackToneConfig",
        payloadFormat = "{\"status\": \"%s\"}"
    ),
    GET_FEEDBACK_TONE_CONFIG(
        bleCmd = StatisticConstant.GET_FEED_BACK_TONE_CONFIG,
        wifiCmd = "getFeedbackToneConfig"
    ),
    GET_GENERAL_CONFIG(
        bleCmd = StatisticConstant.GET_GENERAL_CONFIG,
        wifiCmd = "getGernalConfig"
    ),
    SET_GENERAL_CONFIG(
        bleCmd = StatisticConstant.SET_GENERAL_CONFIG,
        wifiCmd = "setGeneralConfig"
    ),
    SET_BATTERY_SAVING_MODE(
        bleCmd = StatisticConstant.SET_BATTERY_SAVING_MODE,
        wifiCmd = "setBatterySavingMode",
        payloadFormat = "{\"status\": \"%s\"}"
    ),
    GET_BATTERY_SAVING_MODE(
        bleCmd = StatisticConstant.GET_BATTERY_SAVING_MODE,
        wifiCmd = "getBatterySavingMode"
    ),
    SET_IR_LEARN(
        bleCmd = StatisticConstant.SET_IR_LEARN,
        wifiCmd = "setIRLearn"
    ),
    GET_DJ_PAD(
        bleCmd = StatisticConstant.GET_DJ_PAD,
        wifiCmd = "getDJPad"
    ),
    SET_DJ_PAD(
        bleCmd = StatisticConstant.SET_DJ_PAD,
        wifiCmd = "setDJPad"
    ),
    TRIGGER_CAST_LED(
        bleCmd = StatisticConstant.LP_TRIGGER_CAST_LED,
        wifiCmd = "triggerCastLED"
    ),
    CLEAR_HISTORY_ONE_OS_VERSION(
        bleCmd = StatisticConstant.CLEAR_HISTORY_ONE_OS_VERSION,
        wifiCmd = "clearHistoryOneOSVersion"
    ),
    SET_ANCHOR_INDICATE(
        bleCmd = -1,
        wifiCmd = "setAnchorIndicate",
        payloadFormat = "{\"timeout\":%d}"
    ),
    SET_ANCHOR_CANCEL(
        bleCmd = -1,
        wifiCmd = "setAnchorCancel"
    ),
    DESTROY_CAST_GROUP(
        bleCmd = StatisticConstant.LP_DESTROY_CAST_GROUP,
        wifiCmd = "destroyCastGroup"
    ),
    GET_GROUP_INFO(
        bleCmd = StatisticConstant.LP_GET_GROUP_INFO,
        wifiCmd = "getGroupInfo "
    ),
    GROUP_CALIBRATION(
        bleCmd = StatisticConstant.LP_GROUP_CALIBRATION,
        wifiCmd = "groupCalibration",
        payloadFormat = "{\"anchor\": \"%s\",\"step\":%d}"
    ),
    SWITCH_STEREO_CHANNEL(
        bleCmd = StatisticConstant.LP_SWITCH_STEREO_CHANNEL,
        wifiCmd = "switchStereoChannel"
    ),
    SKIP_DEMO_SOUND(
        bleCmd = StatisticConstant.LP_SKIP_DEMO_SOUND,
        wifiCmd = "skipDemoSound"
    ),
    GET_GROUP_PARAMETER(
        bleCmd = StatisticConstant.LP_GET_GROUP_PARAMETER,
        wifiCmd = "getGroupParameter"
    ),
    GET_GROUP_CALIBRATION_STATE(
        bleCmd = -1,
        wifiCmd = "getGroupCalibrationState"
    ),
    RENAME_GROUP(
        bleCmd = StatisticConstant.LP_RENAME_GROUP,
        wifiCmd = "renameGroup",
        payloadFormat = "{\"name\": \"%s\"}"
    ),
    GET_STREAMING_STATUS(
        bleCmd = StatisticConstant.GET_STREAMING_STATUS,
        wifiCmd = "getStreamingStatus"
    ),
    SET_SLEEP_TIMER(
        bleCmd = StatisticConstant.SET_SLEEP_TIMER,
        wifiCmd = "setSleepTimer",
        payloadFormat = "{\"sleep_timer\": %d}"
    ),
    GET_SLEEP_TIMER(
        bleCmd = StatisticConstant.GET_SLEEP_TIMER,
        wifiCmd = "getSleepTimer"
    ),
    GET_PERSONAL_LISTENING_MODE(
        bleCmd = StatisticConstant.GET_PERSONAL_LISTENING_MODE,
        wifiCmd = "getPersonalListeningMode"
    ),
    SET_PERSONAL_LISTENING_MODE(
        bleCmd = StatisticConstant.SET_PERSONAL_LISTENING_MODE,
        wifiCmd = "setPersonalListeningMode"
    ),
    GET_MEDIA_SOURCE_STATUS(
        bleCmd = StatisticConstant.GET_MEDIA_SOURCE_STATUS,
        wifiCmd = "getMediaSourceStatus"
    ),
    GET_LIGHT_COLOR_PICKER(
        bleCmd = StatisticConstant.GET_LIGHT_COLOR_PICKER,
        wifiCmd = "getLightColorPicker"
    ),
    SET_LIGHT_COLOR_PICKER(
        bleCmd = StatisticConstant.SET_LIGHT_COLOR_PICKER,
        wifiCmd = "setLightColorPicker"
    ),
    GET_REAR_SPEAKER_STATUS(
        bleCmd = StatisticConstant.GET_REAR_SPEAKER_STATUS,
        wifiCmd = "getRearSpeakerStatus"
    ),
    GET_AUDIO_SYNC(
        bleCmd = -1,
        wifiCmd = "getAudioSync"
    ),
    SET_AUDIO_SYNC(
        bleCmd = -1,
        wifiCmd = "setAudioSync"
    ),
    NOTIFY_SOUNDSCAPE_V2_MUSIC_STATE(
        bleCmd = StatisticConstant.MONITOR_SOUNDSCAPE_V2_DEMO_TRACK,
        wifiCmd = "notifySoundscapeV2MusicState"
    ),
    SET_SMART_MODE(
        bleCmd = StatisticConstant.SET_SMART_MODE,
        wifiCmd = "setSmartMode",
        payloadFormat = "{\"status\": \"%s\"}"
    ),
    GET_SMART_MODE(
        bleCmd = StatisticConstant.GET_SMART_MODE,
        wifiCmd = "getSmartMode"
    ),
    NOTIFY_SOUNDSCAPE_V2_SETTING(
        bleCmd = StatisticConstant.MONITOR_SOUNDSCAPE_V2_SETTINGS,
        wifiCmd = "notifySoundscapeV2Setting"
    ),
    SET_LIGHT_INFO(
        bleCmd = StatisticConstant.SET_LIGHT_INFO,
        wifiCmd = "setLightInfo"
    ),
    GET_LIGHT_INFO(
        bleCmd = StatisticConstant.GET_LIGHT_INFO,
        wifiCmd = "getLightInfo"
    ),
    NOTIFY_LIGHT_INFO(
        bleCmd = StatisticConstant.NOTIFY_LIGHT_INFO,
        wifiCmd = "notifyLightInfo"
    ),
    RESET_LIGHT_PATTERN_COLOR(
        bleCmd = StatisticConstant.RESET_LIGHT_PATTERN_COLOR,
        wifiCmd = "resetLightPatternColor",
        payloadFormat = "{\"id\": \"%s\"}"
    ),
    GET_GROUP_DEVICES_OTA_STATUS(
        bleCmd = StatisticConstant.GET_GROUP_DEVICES_OTA_STATUS,
        wifiCmd = "getGroupDevicesOTAStatus"
    ),
    GET_GROUP_DEVICES_FLAG(
        bleCmd = StatisticConstant.GET_GROUP_DEVICES_FLAG,
        wifiCmd = "getGroupDevicesFlag"
    );

    companion object {
        fun Int?.wifiCmd(): String? = EnumCommandMapping.values()
            .firstOrNull { item ->
                item.bleCmd == this
            }?.wifiCmd
    }

}