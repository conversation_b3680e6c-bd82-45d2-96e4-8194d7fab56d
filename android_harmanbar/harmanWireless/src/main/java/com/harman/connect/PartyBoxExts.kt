package com.harman.connect

import android.bluetooth.BluetoothDevice
import android.content.Context
import com.harman.command.TimeoutException
import com.harman.command.WriteCmdCharacteristicException
import com.harman.command.common.GeneralGattCommand
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.FactoryResetCommand.Companion.ackFactoryResetCommand
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.command.partybox.gatt.GetFeedbackToneCommand.Companion.ackFeedBackToneCommand
import com.harman.command.partybox.gatt.IdentifyDeviceCommand.Companion.ackIdentifyCommand
import com.harman.command.partybox.gatt.ReqAlarmInfoCommand.Companion.ackReqAlarmInfoCommand
import com.harman.command.partybox.gatt.ReqAutoStandbyTimerCommand
import com.harman.command.partybox.gatt.ReqAutoStandbyTimerCommand.Companion.ackReqAutoStandbyTimerCommand
import com.harman.command.partybox.gatt.ReqDeviceFeatureInfoCommand.Companion.ackDeviceFeatureInfo
import com.harman.command.partybox.gatt.ReqLightInfoCommand.Companion.ackRspLightInActivePattern
import com.harman.command.partybox.gatt.ReqLightInfoCommand.Companion.ackRspLightInfoCommand
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.ReqRadioInfoCommand.Companion.isRadioInfoCommandRsp
import com.harman.command.partybox.gatt.ReqSleepModeInfoCommand.Companion.ackSleepModeInfoCommand
import com.harman.command.partybox.gatt.SetAlarmInfoCommand.Companion.ackSetAlarmInfoCommand
import com.harman.command.partybox.gatt.SetDevInfoCommand.Companion.ackSetDevInfoCommand
import com.harman.command.partybox.gatt.SetDjEffectToneCommand.Companion.ackSetDjToneCommand
import com.harman.command.partybox.gatt.SetDjEffectVoiceCommand.Companion.ackSetDjVoiceCommand
import com.harman.command.partybox.gatt.SetDjFilterCommand.Companion.ackSetDjFilterCommand
import com.harman.command.partybox.gatt.SetLightInfoCommand.Companion.ackSetLightInfoCommand
import com.harman.command.partybox.gatt.SetRadioInfoCommand.Companion.isSetRadioInfoAck
import com.harman.command.partybox.gatt.SetSleepModeCommand.Companion.ackSetSleepModeCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.command.partybox.gatt.auth.EnumAuthAction
import com.harman.command.partybox.gatt.eq.EQSettings
import com.harman.command.partybox.gatt.eq.RawEQSettings
import com.harman.command.partybox.gatt.identify.EnumIdentifyDevice
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.command.partybox.gatt.sleep.SleepModeInfo
import com.harman.connect.listener.IBaseSPPListener
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.discover.DeviceScanner
import com.harman.discover.EnumScanPerformance
import com.harman.discover.IHmDeviceObserver
import com.harman.discover.bean.Device
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.bean.bt.PartyBoxDevFeat
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.AuraCastStatus
import com.harman.discover.info.PartyConnectStatus
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.isAuraStudioBt
import com.harman.discover.util.Tools.isOnyx
import com.harman.discover.util.Tools.isPartyBoxCategory
import com.harman.discover.util.Tools.needEqAlgorithm
import com.harman.discover.util.Tools.safeResume
import com.harman.discover.util.Tools.u32ToPercentage
import com.harman.log.Logger
import com.jbl.one.configuration.AppConfigurationUtils
import com.jbl.one.configuration.model.ProductCategory
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.withTimeoutOrNull

/**
 * Created by gerrardzhang on 2024/5/23.
 *
 * Some tools make async works become sync ones.
 */
private const val TAG = "PartyBoxExts"

fun PartyBoxDevice.isOnyx(): Boolean = bleDevice?.isOnyx() ?: false
fun PartyBoxBTDevice.isOnyx(): Boolean = pid?.isOnyx() ?: false

fun PartyBoxDevice.isStereoGrouped(): Boolean =
    PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED == partyConnectStatus

/**
 * @return [PartyBoxDevice.groupName] or [PartyBoxDevice.deviceName] based on stereo state.
 */
fun PartyBoxDevice.toDisplayName(): String? = if (isStereoGrouped()) {
    groupName
} else {
    deviceName
}

/**
 * studio device but should a2dp is not connected
 */
fun PartyBoxDevice.isBTDeviceA2DPNotConnected(): Boolean =
    this.isHomeBtCategory() && !this.isA2DPConnected

private fun Exception.isTargetCommand(command: Byte): Boolean = when (this) {
    is WriteCmdCharacteristicException -> {
        command == this.sendCommand.commandID
    }

    is TimeoutException -> {
        command == this.sendCommand.commandID
    }

    else -> false
}

private suspend fun PartyBoxDevice.syncGetFirmwareVersion(protocol: Int): Boolean {
    val partyBoxDevice = this

    when (protocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            if (!partyBoxDevice.isGattConnected) {
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            if (!partyBoxDevice.isBrEdrConnected) {
                return false
            }
        }
    }

    return suspendCancellableCoroutine { continuation ->
        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_DEV_INFO) },
            protocol = protocol
        ) {

            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.hasFirmwareVersion()) {
                    // 0x12 notify may not contains firmware version first time received.
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }

            private fun IGeneralCommand.hasFirmwareVersion(): Boolean {
                if (GattPacketFormat.RESP_DEV_INFO != commandID) {
                    return false
                }

                return null != (this as? GeneralGattCommand)?.getExtraInfo(
                    GattPacketFormat.FIRMWARE_VERSION.toString()
                )
            }
        }

        partyBoxDevice.reqDeviceInfo(protocol = protocol)
    }
}

suspend fun PartyBoxDevice.syncGetFirmwareVersionWithTimeout(
    repeatTimes: Int = 0,
    timeoutMills: Long = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS,
    protocol: Int
): Boolean = Tools.repeatWithTimeout(repeatTimes = repeatTimes, timeoutMills = timeoutMills) {
    val device = this
    device.syncGetFirmwareVersion(protocol)
} ?: false


suspend fun PartyBoxDevice.syncSPPConnect(context: Context): Boolean {
    val device = this
    if (true == device.sppSession?.isConnected) {
        return true
    }

    return suspendCancellableCoroutine { continuation ->
        val session = device.sppSession ?: run {
            continuation.safeResume(false)
            return@suspendCancellableCoroutine
        }

        val listener = object : IBaseSPPListener {
            override fun onStatusChanged(status: EnumConnectionStatus, session: BaseSppSession) {
                when (status) {
                    EnumConnectionStatus.CONNECTED -> {
                        session.unregisterConnectionListener(this)
                        continuation.safeResume(true)
                    }

                    EnumConnectionStatus.DISCONNECTED -> {
                        session.unregisterConnectionListener(this)
                        continuation.safeResume(false)
                    }

                    else -> {
                        // no impl
                    }
                }
            }
        }

        continuation.invokeOnCancellation {
            session.unregisterConnectionListener(listener)
        }
        session.registerConnectionListener(listener)
        session.connect(context)
    }
}

suspend fun PartyBoxDevice.syncSPPConnectWithTimeout(
    context: Context,
    repeatTimes: Int = 0,
    timeoutMills: Long = 15 * 1000
): Boolean = Tools.repeatWithTimeout(repeatTimes = repeatTimes, timeoutMills = timeoutMills) {
    val device = this
    device.syncSPPConnect(context = context)
} ?: false


/**
 * Wait by UUID
 *
 * Workaround this scene: PartyBox BLE address changed immediate after prev. BLE adv received.
 */
suspend fun String.syncWaitPartyBoxOnline(context: Context): PartyBoxDevice? =
    suspendCancellableCoroutine { continuation ->
        val targetUUID = this

        val observer = object : IHmDeviceObserver {
            override fun onDeviceOnlineOrUpdate(device: Device) {
                if (device !is PartyBoxDevice) {
                    return
                }

                if (device.UUID == targetUUID) {
                    DeviceScanner.unregisterObserver(observer = this)
                    continuation.safeResume(device)
                }
            }

            override fun onBluetoothServiceFailed(errorCode: Int) {
                DeviceScanner.unregisterObserver(observer = this)
                continuation.safeResume(null)
            }
        }

        continuation.invokeOnCancellation {
            DeviceScanner.unregisterObserver(observer = observer)
        }
        DeviceScanner.startScan(context = context, EnumScanPerformance.HIGH)
        DeviceScanner.registerObserver(observer = observer)
    }

/**
 * Wait by UUID
 */
suspend fun String.syncWaitPartyBoxOnlineWithTimeout(
    context: Context,
    timeoutMills: Long,
): PartyBoxDevice? = Tools.repeatWithTimeout(
    repeatTimes = 0,
    timeoutMills = timeoutMills,
    onBlock = {
        val targetUUID = this
        targetUUID.syncWaitPartyBoxOnline(context = context)
    }
)

suspend fun PartyBoxDevice.syncGetPlayerStatus(protocol: Int): Unit =
    suspendCancellableCoroutine<Unit> { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Unit>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = Unit,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_PLAYER_INFO) },
            protocol = protocol
        ) {
            override fun onPlayerInfoUpdate() {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(Unit)
            }
        }

        partyBoxDevice.reqPlayerInfo(protocol = protocol)
    }

suspend fun PartyBoxDevice.blockGetPlayerStatusWithTimeout(protocol: Int): Unit =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGetPlayerStatus(protocol = protocol)
    } ?: Unit

private suspend fun PartyBoxDevice.syncGetAdvancedEQ(protocol: Int): RawEQSettings? =
    suspendCancellableCoroutine<RawEQSettings?> { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<RawEQSettings?>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = null,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_ADVANCE_EQ) },
            protocol = protocol
        ) {
            override fun onAdvanceEQUpdate() {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(partyBoxDevice.rawEQSettings)
            }
        }

        partyBoxDevice.reqAdvancedEQ(protocol = protocol)
    }

suspend fun PartyBoxDevice.syncGetAdvancedEQWithTimeout(protocol: Int): RawEQSettings? =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGetAdvancedEQ(protocol = protocol)
    }

private suspend fun PartyBoxDevice.syncSetAdvancedEQ(
    eqSettings: EQSettings,
    protocol: Int
): RawEQSettings? =
    suspendCancellableCoroutine<RawEQSettings?> { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<RawEQSettings?>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = null,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_ADVANCE_EQ) },
            protocol = protocol
        ) {
            override fun onAdvanceEQUpdate() {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(partyBoxDevice.rawEQSettings)
            }
        }
        if (isPartyBoxCategory(pid)) {
            partyBoxDevice.setAdvancedEQBE(
                protocol = protocol,
                eqSettings = eqSettings,
                isNeedAlgorithm = needEqAlgorithm(pid)
            )
        } else {
            partyBoxDevice.setAdvancedEQLE(
                protocol = protocol,
                eqSettings = eqSettings,
                isNeedAlgorithm = needEqAlgorithm(pid)
            )
        }
    }

suspend fun PartyBoxDevice.syncSetAdvancedEQWithTimeout(
    eqSettings: EQSettings,
    protocol: Int
): RawEQSettings? =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncSetAdvancedEQ(eqSettings = eqSettings, protocol = protocol)
    }

private suspend fun PartyBoxDevice.syncGen3GroupStereo(
    audioChannel: AudioChannel,
    connectStatus: PartyConnectStatus,
    groupID: String,
    groupName: String,
    targetPartyConnectStatus: PartyConnectStatus,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onPartyConnectStatusChanged(status: PartyConnectStatus) {
                if (targetPartyConnectStatus == status) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.gen3GroupStereo(
            protocol = protocol,
            audioChannel = audioChannel, connectStatus = connectStatus,
            groupID = groupID, groupName = groupName, secondaryDevice = secondaryDevice
        )
    }

private suspend fun PartyBoxDevice.syncGen4GroupStereo(
    audioChannel: AudioChannel,
    connectStatus: PartyConnectStatus,
    groupID: String,
    groupName: String,
    targetPartyConnectStatus: PartyConnectStatus,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onPartyConnectStatusChanged(status: PartyConnectStatus) {
                if (targetPartyConnectStatus == status) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }

            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                super.onCommandReceived(device, sendCommand, receivedCommand)
                val receivedPayload = receivedCommand.payload
                if (GattPacketFormat.DEV_ACK == receivedCommand.commandID &&
                    null != receivedPayload &&
                    receivedPayload.size >= 2 &&
                    GattPacketFormat.SET_DEV_INFO == receivedPayload[0] &&
                    GattPacketFormat.STATUS_CODE_SUCCESS == receivedPayload[1]
                ) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)

                }
            }
        }

        partyBoxDevice.gen3GroupStereo(
            protocol = protocol,
            audioChannel = audioChannel, connectStatus = connectStatus,
            groupID = groupID, groupName = groupName, secondaryDevice = secondaryDevice
        )
    }

const val TWS_SINGLE_GROUPING_TIMEOUT_MILLS = 30 * 1000L

private suspend fun PartyBoxDevice.syncGen3GroupStereoWithTimeout(
    audioChannel: AudioChannel,
    connectStatus: PartyConnectStatus,
    groupID: String,
    groupName: String,
    targetPartyConnectStatus: PartyConnectStatus,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = TWS_SINGLE_GROUPING_TIMEOUT_MILLS) {
        syncGen3GroupStereo(
            audioChannel = audioChannel,
            connectStatus = connectStatus,
            groupID = groupID,
            groupName = groupName,
            targetPartyConnectStatus = targetPartyConnectStatus,
            secondaryDevice = secondaryDevice,
            protocol = protocol
        )
    } ?: false

private suspend fun PartyBoxDevice.syncGen4GroupStereoWithTimeout(
    audioChannel: AudioChannel,
    connectStatus: PartyConnectStatus,
    groupID: String,
    groupName: String,
    targetPartyConnectStatus: PartyConnectStatus,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = TWS_SINGLE_GROUPING_TIMEOUT_MILLS) {
        syncGen4GroupStereo(
            audioChannel = audioChannel,
            connectStatus = connectStatus,
            groupID = groupID,
            groupName = groupName,
            targetPartyConnectStatus = targetPartyConnectStatus,
            secondaryDevice = secondaryDevice,
            protocol = protocol
        )
    } ?: false

suspend fun PartyBoxDevice.syncGen3GroupStereoWithTimeout(
    logTag: String,
    context: Context?,
    secondaryDevice: PartyBoxDevice,
    primaryAudioChannel: AudioChannel,
    groupID: String,
    groupName: String,
    primaryProtocol: Int,
    secondaryProtocol: Int
): Boolean {
    val primaryDevice = this


    Logger.d(
        logTag, "syncGen3GroupStereoWithTimeout() >>>" +
                " primary[${primaryDevice.UUID} ${primaryDevice.bleAddress}]" +
                " secondary[${secondaryDevice.UUID} ${secondaryDevice.bleDevice}]" +
                " primaryAudioChannel[${primaryAudioChannel.channelName}]" +
                " groupID[$groupID]" +
                " groupName[$groupName]" +
                " primaryProtocol[$primaryProtocol] secondaryProtocol[$secondaryProtocol]"
    )

    // gatt/brEdr connect with secondary partybox device
    when (secondaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            if (!secondaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen3GroupStereoWithTimeout() >>> fail to gatt connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            if (!secondaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen3GroupStereoWithTimeout() >>> fail to BR/EDR connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }
    }

    Logger.d(
        logTag,
        "syncGen3GroupStereoWithTimeout() >>> connect secondary device success:${secondaryDevice.UUID}"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!secondaryDevice.syncGen3GroupStereoWithTimeout(
            audioChannel = primaryAudioChannel.opposite(), // opposite channel
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            groupID = groupID,
            groupName = groupName,
            targetPartyConnectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING, // target status: connecting
            secondaryDevice = null,
            protocol = secondaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen3GroupStereoWithTimeout() >>> fail to set connecting status on secondary device:${secondaryDevice.UUID}"
        )
        return false
    }

    when (secondaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> secondaryDevice.disconnectGatt()
        BluetoothDevice.TRANSPORT_BREDR -> secondaryDevice.disconnectBrEdr()
    }

    // gatt/brEdr connect with primary partybox device
    when (primaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            if (!primaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen3GroupStereoWithTimeout() >>> fail to gatt connect with primary device:${primaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            if (!primaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen3GroupStereoWithTimeout() >>> fail to BR/EDR connect with primary device:${primaryDevice.UUID}"
                )
                return false
            }
        }
    }


    Logger.d(
        logTag,
        "syncGen3GroupStereoWithTimeout() >>> connect primary device success:${primaryDevice.UUID}"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!primaryDevice.syncGen3GroupStereoWithTimeout(
            audioChannel = primaryAudioChannel, // primary channel
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            groupID = groupID,
            groupName = groupName,
            targetPartyConnectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED, // target status: connected
            secondaryDevice = secondaryDevice,
            protocol = primaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen3GroupStereoWithTimeout() >>> fail to set connecting or wait for connected status on primary device:${secondaryDevice.UUID}"
        )
        return false
    }

    Logger.i(
        logTag, "syncGen3GroupStereoWithTimeout() >>> success. primary[${primaryDevice.UUID}]" +
                " secondary[${secondaryDevice.UUID}]"
    )
    return true
}

suspend fun PartyBoxDevice.syncGen4GroupStereoWithTimeout(
    logTag: String,
    context: Context?,
    secondaryDevice: PartyBoxDevice,
    primaryAudioChannel: AudioChannel,
    groupID: String,
    groupName: String,
    primaryProtocol: Int,
    secondaryProtocol: Int
): Boolean {
    val primaryDevice = this

    Logger.d(
        logTag, "syncGen4GroupStereoWithTimeout() >>>" +
                " primary[${primaryDevice.UUID} ${primaryDevice.bleDevice}]" +
                " secondary[${secondaryDevice.UUID} ${secondaryDevice.bleDevice}]" +
                " primaryAudioChannel[${primaryAudioChannel.channelName}]" +
                " groupID[$groupID]" +
                " groupName[$groupName]" +
                " primaryProtocol[$primaryProtocol] secondaryProtocol[$secondaryProtocol]"
    )

    when (secondaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            if (!secondaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen4GroupStereoWithTimeout() >>> fail to gatt connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            if (!secondaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen4GroupStereoWithTimeout() >>> fail to BR/EDR connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }
    }
    /**
     * this special for Aura Studio 5, which need exchange mac address to each other
     * with [GattPacketFormat.MAC_ADDRESS] update in [com.harman.command.partybox.gatt.GroupCommand]
     */
    val shouldExchangeMacAddress = secondaryDevice.pid?.isAuraStudioBt() == true

    Logger.d(
        logTag,
        "syncGen3GroupStereoWithTimeout() >>> connect secondary device success:${secondaryDevice.UUID}"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!secondaryDevice.syncGen4GroupStereoWithTimeout(
            audioChannel = primaryAudioChannel.opposite(), // opposite channel
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            groupID = groupID,
            groupName = groupName,
            targetPartyConnectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING, // target status: connecting
            secondaryDevice = if (shouldExchangeMacAddress) primaryDevice else null,
            protocol = secondaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen3GroupStereoWithTimeout() >>> fail to set connecting status on secondary device:${secondaryDevice.UUID}"
        )
        return false
    }

//    secondaryDevice.disconnectGatt()

    Logger.d(
        logTag,
        "syncGen3GroupStereoWithTimeout() >>> connect primary device success:${primaryDevice.UUID}"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!primaryDevice.syncGen4GroupStereoWithTimeout(
            audioChannel = primaryAudioChannel, // primary channel
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            groupID = groupID,
            groupName = groupName,
            targetPartyConnectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED, // target status: connected
            secondaryDevice = secondaryDevice,
            protocol = primaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen3GroupStereoWithTimeout() >>> fail to set connecting or wait for connected status on primary device:${secondaryDevice.UUID}"
        )
        return false
    }

    val waitResult = kotlin.runCatching {
        withTimeout(60 * 1000) {
            mainDeviceWaitDeviceInfo(partyBoxDevice = primaryDevice, protocol = primaryProtocol)
        }
    }

    Logger.d(logTag, "waitResult:${waitResult}")
    if (waitResult.isFailure || waitResult.getOrNull() != true) {
        return false
    }
    return true
}

private suspend fun mainDeviceWaitDeviceInfo(
    partyBoxDevice: PartyBoxDevice,
    protocol: Int
): Boolean {
    return suspendCancellableCoroutine { continuation ->

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.RESP_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onPartyConnectStatusChanged(status: PartyConnectStatus) {
                if (PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED == status) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                } else if (PartyConnectStatus.PARTY_CONNECT_OFF == status) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(false)
                }
            }
        }
    }
}

private suspend fun PartyBoxDevice.syncIdentify(
    protocol: Int, main: EnumIdentifyDevice?, co: EnumIdentifyDevice?
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.IDENTIFY_DEVICE) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackIdentifyCommand()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.identifyDevice(
        protocol = protocol,
        mainIdentify = main,
        coIdentify = co
    )
}

suspend fun PartyBoxDevice.syncIdentifyWithTimeout(
    main: EnumIdentifyDevice?, co: EnumIdentifyDevice?, protocol: Int
): Boolean = Tools.repeatWithTimeout(
    repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
) {
    syncIdentify(protocol = protocol, main = main, co = co)
} ?: false

private suspend fun PartyBoxDevice.syncGen3RenameStereo(protocol: Int, groupName: String): Boolean =
    suspendCancellableCoroutine<Boolean> { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackSetDevInfoCommand()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.gen3RenameStereo(protocol = protocol, groupName = groupName)
    }

suspend fun PartyBoxDevice.syncGen3RenameStereoWithTimeout(
    groupName: String,
    protocol: Int
): Boolean =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGen3RenameStereo(protocol = protocol, groupName = groupName)
    } ?: false

private suspend fun PartyBoxDevice.syncSetPrimaryChannel(
    channel: AudioChannel,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    suspendCancellableCoroutine<Boolean> { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackSetDevInfoCommand()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.setPrimaryChannel(
            protocol = protocol,
            channel = channel,
            secondaryDevice = secondaryDevice
        )
    }

suspend fun PartyBoxDevice.syncSetPrimaryChannelWithTimeout(
    channel: AudioChannel,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncSetPrimaryChannel(
            channel = channel,
            secondaryDevice = secondaryDevice,
            protocol = protocol
        )
    } ?: false

private suspend fun PartyBoxDevice.syncGetDeviceFeatureInfo(protocol: Int): PartyBoxDevFeat? {
    val partyBoxDevice = this
    if (null != partyBoxDevice.deviceFeature) {
        return partyBoxDevice.deviceFeature
    }

    return suspendCancellableCoroutine<PartyBoxDevFeat?> { continuation ->
        object : PartyBoxContinuationDeviceListener<PartyBoxDevFeat?>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = partyBoxDevice.deviceFeature,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.RET_DEV_FEATURE_INFO) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackDeviceFeatureInfo()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(partyBoxDevice.deviceFeature)
                }
            }
        }

        partyBoxDevice.reqDeviceFeatureInfo(protocol = protocol)
    }
}

suspend fun PartyBoxDevice.syncGetDeviceFeatureInfoWithTimeout(
    protocol: Int
): PartyBoxDevFeat? =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGetDeviceFeatureInfo(protocol = protocol)
    }

private val GEN3_PIDS = listOf("20dd", "20e2")
private val GEN2_PIDS = listOf("2062", "2063", "1f5d", "1f5f", "1f61")
fun PartyBoxDevice.isGen2(): Boolean = GEN2_PIDS.any { target ->
    target.equals(this.pid, true)
}

fun PartyBoxDevice.isGen3(): Boolean = GEN3_PIDS.any { target ->
    target.equals(this.pid, true)
}

private suspend fun PartyBoxDevice.syncGen2GroupTws(
    audioChannel: AudioChannel,
    connectStatus: PartyConnectStatus,
    targetPartyConnectStatus: PartyConnectStatus?,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onPartyConnectStatusChanged(status: PartyConnectStatus) {
                if (targetPartyConnectStatus == status) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }

            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (null == targetPartyConnectStatus && receivedCommand.ackSetDevInfoCommand()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.gen2GroupTws(
            protocol = protocol, audioChannel = audioChannel,
            connectStatus = connectStatus, secondaryDevice = secondaryDevice
        )
    }

private suspend fun PartyBoxDevice.syncGen2GroupTwsWithTimeout(
    audioChannel: AudioChannel,
    connectStatus: PartyConnectStatus,
    targetPartyConnectStatus: PartyConnectStatus?,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = TWS_SINGLE_GROUPING_TIMEOUT_MILLS) {
        syncGen2GroupTws(
            audioChannel = audioChannel,
            connectStatus = connectStatus,
            targetPartyConnectStatus = targetPartyConnectStatus,
            secondaryDevice = secondaryDevice,
            protocol = protocol
        )
    } ?: false

suspend fun PartyBoxDevice.syncGen23MixGroupTwsWithTimeout(
    logTag: String,
    context: Context?,
    secondaryDevice: PartyBoxDevice,
    primaryProtocol: Int,
    secondaryProtocol: Int
): Boolean {
    val primaryDevice = this
    Logger.d(
        logTag, "syncGen23MixGroupTwsWithTimeout() >>>" +
                " primary[${primaryDevice.pid} ${primaryDevice.UUID} ${primaryDevice.bleAddress}]" +
                " secondary[${secondaryDevice.pid} ${secondaryDevice.UUID} ${secondaryDevice.bleDevice}]" +
                " primaryProtocol[$primaryProtocol] secondaryProtocol[$secondaryProtocol]"
    )

    // gatt/brEdr connect with secondary partybox device
    when (secondaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            if (!secondaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen23MixGroupTwsWithTimeout() >>> fail to gatt connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            if (!secondaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen23MixGroupTwsWithTimeout() >>> fail to BR/EDR connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }
    }

    Logger.d(
        logTag,
        "syncGen23MixGroupTwsWithTimeout() >>> connect secondary device[${secondaryDevice.UUID}] protocol[$secondaryProtocol]"
    )
    // check cross tws support if it's gen3
    if (secondaryDevice.isGen3()) {
        val feat = secondaryDevice.syncGetDeviceFeatureInfoWithTimeout(protocol = secondaryProtocol)
        if (true != feat?.supportCrossTws) {
            Logger.w(
                logTag,
                "syncGen23MixGroupTwsWithTimeout() >>> secondary gen3 device didn't support cross tws"
            )
            return false
        }
    }

    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!secondaryDevice.syncGen2GroupTwsWithTimeout(
            audioChannel = AudioChannel.NONE_CHANNEL,
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            targetPartyConnectStatus = null, // target status: ACK
            secondaryDevice = null,
            protocol = secondaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen23MixGroupTwsWithTimeout() >>> fail to set connecting status on secondary device[${secondaryDevice.UUID}] protocol[$secondaryProtocol]"
        )
        return false
    }

    when (secondaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> secondaryDevice.disconnectGatt()
        BluetoothDevice.TRANSPORT_BREDR -> secondaryDevice.disconnectBrEdr()
        else -> {
            // no impl
        }
    }

    when (primaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            // gatt connect with primary partybox device
            if (!primaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen23MixGroupTwsWithTimeout() >>> fail to gatt connect with primary device:${primaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            // BR/EDR connect with primary partybox device
            if (!primaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen23MixGroupTwsWithTimeout() >>> fail to BR/EDR connect with primary device:${primaryDevice.UUID}"
                )
                return false
            }
        }
    }

    // check cross tws support if it's gen3
    if (primaryDevice.isGen3()) {
        val feat = primaryDevice.syncGetDeviceFeatureInfoWithTimeout(protocol = primaryProtocol)
        if (true != feat?.supportCrossTws) {
            Logger.w(
                logTag,
                "syncGen23MixGroupTwsWithTimeout() >>> primary gen3 device didn't support cross tws"
            )
            return false
        }
    }

    Logger.d(
        logTag,
        "syncGen23MixGroupTwsWithTimeout() >>> connect primary device[${primaryDevice.UUID}] protocol[$primaryProtocol]"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!primaryDevice.syncGen2GroupTwsWithTimeout(
            audioChannel = AudioChannel.NONE_CHANNEL,
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            targetPartyConnectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED, // target status: connected
            secondaryDevice = secondaryDevice,
            protocol = primaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen23MixGroupTwsWithTimeout() >>> fail to set connecting or wait for connected status on primary device:${primaryDevice.UUID}"
        )
        return false
    }

    Logger.i(
        logTag,
        "syncGen23MixGroupTwsWithTimeout() >>> success. primary[${primaryDevice.UUID}]" + " secondary[${secondaryDevice.UUID}]"
    )
    return true
}

suspend fun PartyBoxDevice.syncFlashLight(
    logTag: String,
    primary: Boolean,
    secondary: Boolean,
    protocol: Int
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_LIGHT_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackSetLightInfoCommand()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.flashLight(protocol = protocol, primary = primary, secondary = secondary)
}

suspend fun PartyBoxDevice.syncFlashLightWithTimeout(
    logTag: String,
    primary: Boolean,
    secondary: Boolean,
    protocol: Int
) = Tools.repeatWithTimeout(
    repeatTimes = 0,
    timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
) {
    syncFlashLight(logTag = logTag, primary = primary, secondary = secondary, protocol = protocol)
}

suspend fun PartyBoxDevice.syncGen2GroupTwsWithTimeout(
    logTag: String,
    context: Context?,
    secondaryDevice: PartyBoxDevice,
    primaryAudioChannel: AudioChannel,
    primaryProtocol: Int,
    secondaryProtocol: Int
): Boolean {
    val primaryDevice = this

    Logger.d(
        logTag, "syncGen2GroupTwsWithTimeout() >>>" +
                " primary[${primaryDevice.pid} ${primaryDevice.UUID} ${primaryDevice.bleAddress}]" +
                " secondary[${secondaryDevice.pid} ${secondaryDevice.UUID} ${secondaryDevice.bleDevice}]" +
                " primaryProtocol[$primaryProtocol] secondaryProtocol[$secondaryProtocol]"
    )

    when (primaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            // gatt connect with secondary partybox device
            if (!secondaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen2GroupTwsWithTimeout() >>> fail to gatt connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            // gatt connect with secondary partybox device
            if (!secondaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen2GroupTwsWithTimeout() >>> fail to BR/EDR connect with secondary device:${secondaryDevice.UUID}"
                )
                return false
            }
        }
    }

    Logger.d(
        logTag,
        "syncGen2GroupTwsWithTimeout() >>> connect secondary device:${secondaryDevice.UUID}"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!secondaryDevice.syncGen2GroupTwsWithTimeout(
            audioChannel = AudioChannel.NONE_CHANNEL,
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            targetPartyConnectStatus = null, // target status: ACK
            secondaryDevice = null,
            protocol = secondaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen2GroupTwsWithTimeout() >>> fail to set connecting status on secondary device[${secondaryDevice.UUID}] protocol[$secondaryProtocol]"
        )
        return false
    }

    when (secondaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> secondaryDevice.disconnectGatt()
        BluetoothDevice.TRANSPORT_BREDR -> secondaryDevice.disconnectBrEdr()
        else -> {
            // no impl.
        }
    }

    when (primaryProtocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            // gatt connect with primary partybox device
            if (!primaryDevice.syncGattConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen2GroupTwsWithTimeout() >>> fail to gatt connect with primary device:${primaryDevice.UUID}"
                )
                return false
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            // BR/EDR connect with primary partybox device
            if (!primaryDevice.syncBrEdrConnectWithTimeout(context)) {
                Logger.e(
                    logTag,
                    "syncGen2GroupTwsWithTimeout() >>> fail to BR/EDR connect with primary device:${primaryDevice.UUID}"
                )
                return false
            }
        }
    }

    Logger.d(
        logTag,
        "syncGen2GroupTwsWithTimeout() >>> connect primary device:${primaryDevice.UUID}"
    )
    // set connecting status on secondary partybox device and waiting for connecting status callback
    if (!primaryDevice.syncGen2GroupTwsWithTimeout(
            audioChannel = AudioChannel.NONE_CHANNEL,
            connectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTING,
            targetPartyConnectStatus = PartyConnectStatus.PARTY_CONNECT_WIRELESS_CONNECTED, // target status: connected
            secondaryDevice = secondaryDevice,
            protocol = primaryProtocol
        )
    ) {
        Logger.e(
            logTag,
            "syncGen2GroupTwsWithTimeout() >>> fail to set connecting or wait for connected status on primary device[${primaryDevice.UUID}] protocol[$primaryProtocol]"
        )
        return false
    }

    if (primaryDevice.pid == secondaryDevice.pid) {
        // auto assign stereo channel to primary device if they have same pid
        Logger.i(
            logTag,
            "syncGen2GroupTwsWithTimeout() >>> start to set stereo channel[${primaryAudioChannel.channelName}]"
        )
        if (!primaryDevice.syncSetPrimaryChannelWithTimeout(
                channel = primaryAudioChannel,
                secondaryDevice = secondaryDevice,
                protocol = primaryProtocol
            )
        ) {
            Logger.w(
                TAG,
                "syncGen2GroupTwsWithTimeout() >>> fail to stereo channel. Regard as party mode. protocol[$primaryProtocol]"
            )
        } else {
            Logger.i(
                TAG,
                "syncGen2GroupTwsWithTimeout() >>> set stereo channel suc. protocol[$primaryProtocol]"
            )
        }
    } else {
        Logger.i(
            TAG,
            "syncGen2GroupTwsWithTimeout() >>> Regard as party mode cause of different pid"
        )
    }

    return true
}

private suspend fun PartyBoxDevice.syncSetAuraCastStatus(logTag: String, open: Boolean, protocol: Int): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackSetDevInfoCommand()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.setAuraCastStatus(
            protocol = protocol,
            status = if (open) AuraCastStatus.ON else AuraCastStatus.OFF
        )
    }

/**
 * @param protocol [BluetoothDevice.TRANSPORT_LE] or [BluetoothDevice.TRANSPORT_BREDR]
 */
suspend fun PartyBoxDevice.syncSetAuraCastStatusWithTimeout(logTag: String, open: Boolean, protocol: Int) =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncSetAuraCastStatus(logTag = logTag, open = open, protocol = protocol)
    } ?: false

private suspend fun PartyBoxDevice.syncUnGroup(
    logTag: String,
    secondaryDevice: PartyBoxDevice?,
    protocol: Int
): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DEV_INFO) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackSetDevInfoCommand()) {
                    // update local party connect mode flag manually and callback listener for a quick response
                    partyBoxDevice.bleDevice?.updatePartyConnectStatusManually(PartyConnectStatus.PARTY_CONNECT_OFF)
                    secondaryDevice?.bleDevice?.updatePartyConnectStatusManually(PartyConnectStatus.PARTY_CONNECT_OFF)
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }
        partyBoxDevice.unGroup(protocol = protocol, secondaryDevice = secondaryDevice)
    }

suspend fun PartyBoxDevice.syncUnGroupWithTimeout(
    logTag: String,
    secondaryDevice: PartyBoxDevice? = null,
    protocol: Int
) =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncUnGroup(logTag = logTag, secondaryDevice = secondaryDevice, protocol = protocol)
    } ?: false

suspend fun PartyBoxDevice.syncDjEffectTone(logTag: String, toneIndex: Int, protocol: Int) =
    syncSetDjEffect(
        logTag = logTag,
        djEffectId = toneIndex,
        isDjEffectTone = true,
        isDjEffectVoice = false,
        protocol = protocol
    )

suspend fun PartyBoxDevice.syncDjEffectVoice(logTag: String, toneIndex: Int, protocol: Int) =
    syncSetDjEffect(
        logTag,
        toneIndex,
        isDjEffectTone = false,
        isDjEffectVoice = true,
        protocol = protocol
    )

suspend fun PartyBoxDevice.syncDjFilter(
    logTag: String,
    djFilterId: Int,
    level: Int,
    protocol: Int
) =
    syncSetDjFilter(logTag = logTag, djFilterId = djFilterId, level = level, protocol = protocol)

private suspend fun PartyBoxDevice.syncSetDjFilter(
    logTag: String,
    djFilterId: Int,
    level: Int,
    protocol: Int
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_DJEFFECT_FILTER_STATUS) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackSetDjFilterCommand()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setDjEffectFilter(protocol = protocol, djFilterID = djFilterId, level = level)
}

private suspend fun PartyBoxDevice.syncSetDjEffect(
    logTag: String,
    djEffectId: Int,
    isDjEffectTone: Boolean,
    isDjEffectVoice: Boolean,
    protocol: Int
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e ->
            e.isTargetCommand(GattPacketFormat.SET_DJEFFECT_TONE_STATUS) && isDjEffectTone
                    || e.isTargetCommand(GattPacketFormat.SET_DJEFFECT_TONE_STATUS) && isDjEffectVoice
        },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (isDjEffectTone && receivedCommand.ackSetDjToneCommand()
                || isDjEffectVoice && receivedCommand.ackSetDjVoiceCommand()
            ) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }
    if (isDjEffectTone) {
        partyBoxDevice.setDjEffectTone(protocol = protocol, toneID = djEffectId)
    } else if (isDjEffectVoice) {
        partyBoxDevice.setDjEffectVoice(protocol = protocol, voiceID = djEffectId)
    }
}

suspend fun PartyBoxDevice.syncGetAlarmInfo(
    logTag: String, protocol: Int, requestType: AlarmInfo.RequestType
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice, continuation = continuation, errorResult = false, logTag = logTag, matcher = { e ->
            e.isTargetCommand(GattPacketFormat.REQ_ALARM_INFO)
        }, protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackReqAlarmInfoCommand()) {
                Logger.d(logTag, "syncSetAlarmInfo() >>> ackReqAlarmInfo")
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }
    partyBoxDevice.reqAlarmInfo(protocol = protocol, requestType = requestType)
}

suspend fun PartyBoxDevice.syncSetAlarmInfo(
    logTag: String, protocol: Int, alarm: AlarmInfo.Alarm? = null, setting: AlarmInfo.Setting? = null, cmd: AlarmInfo.Command
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice, continuation = continuation, errorResult = false, logTag = logTag, matcher = { e ->
            e.isTargetCommand(GattPacketFormat.SET_ALARM_INFO)
        }, protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackSetAlarmInfoCommand()) {
                Logger.d(logTag, "syncSetAlarmInfo() >>> ackSetAlarmInfo")
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }
    partyBoxDevice.setAlarmInfo(protocol = protocol, alarm = alarm, setting = setting, cmd = cmd)
}

suspend fun PartyBoxDevice.syncGetSleepModeInfo(
    logTag: String, protocol: Int
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice, continuation = continuation, errorResult = false, logTag = logTag, matcher = { e ->
            e.isTargetCommand(GattPacketFormat.REQ_SLEEP_MODE_INFO)
        }, protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackSleepModeInfoCommand()) {
                Logger.d(logTag, "syncGetSleepModeInfo() >>> ackSleepMode")
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }
    partyBoxDevice.reqSleepModeInfo(protocol = protocol)
}

suspend fun PartyBoxDevice.syncSetSleepModeInfo(
    logTag: String, protocol: Int, sleepModeInfo: SleepModeInfo? = null, command: SleepModeInfo.Command
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice, continuation = continuation, errorResult = false, logTag = logTag, matcher = { e ->
            e.isTargetCommand(GattPacketFormat.SET_SLEEP_MODE_INFO)
        }, protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.ackSetSleepModeCommand()) {
                Logger.d(logTag, "syncSetSleepModeInfo() >>> ackSetSleepMode")
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }
    partyBoxDevice.setSleepModeInfo(protocol = protocol, sleepModeInfo = sleepModeInfo, command = command)
}

private suspend fun PartyBoxDevice.syncGetLightInfo(logTag: String, protocol: Int): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_LIGHT_INFO) },
            protocol = protocol
        ) {
            private var ackInfo: Boolean = false
            private var ackInActivePattern: Boolean = false

            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackRspLightInfoCommand()) {
                    Logger.d(logTag, "syncGetLightInfo() >>> ack light info")
                    ackInfo = true
                } else if (receivedCommand.ackRspLightInActivePattern()) {
                    Logger.d(logTag, "syncGetLightInfo() >>> ack inactive patterns")
                    ackInActivePattern = true
                }

                if (ackInfo && ackInActivePattern) {
                    Logger.i(logTag, "syncGetLightInfo() >>> all needs collected")
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.reqLightInfo(protocol = protocol)
    }

suspend fun PartyBoxDevice.syncGetLightInfoWithTimeout(logTag: String, protocol: Int): Boolean =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGetLightInfo(logTag = logTag, protocol = protocol)
    } ?: false

private suspend fun PartyBoxDevice.syncFactoryReset(logTag: String, protocol: Int): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.FACTORY_RESET) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackFactoryResetCommand()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.factoryReset(protocol = protocol)
    }

suspend fun PartyBoxDevice.syncGetStudioLightInfoWithTimeout(
    logTag: String,
    protocol: Int
): Boolean =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGetStudioLightInfo(logTag = logTag, protocol = protocol)
    } ?: false

private suspend fun PartyBoxDevice.syncGetStudioLightInfo(logTag: String, protocol: Int): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_LIGHT_INFO) },
            protocol = protocol
        ) {
            private var ackInfo: Boolean = false

            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackRspLightInfoCommand()) {
                    Logger.d(logTag, "syncGetStudioLightInfo() >>> ack light info")
                    ackInfo = true
                }

                if (ackInfo) {
                    Logger.i(logTag, "syncGetStudioLightInfo() >>> all needs collected")
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.getStudioLightInfo(protocol = protocol)
    }

suspend fun PartyBoxDevice.syncGetFeedbackToneWithTimeout(logTag: String, protocol: Int): Boolean =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncGetFeedbackTone(logTag = logTag, protocol = protocol)
    } ?: false

private suspend fun PartyBoxDevice.syncGetFeedbackTone(logTag: String, protocol: Int): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.RET_FEEDBACK_TONE) },
            protocol = protocol
        ) {
            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackFeedBackToneCommand()) {
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.reqFeedbackTone(protocol = protocol)
    }

suspend fun PartyBoxDevice.syncFactoryResetWithTimeout(logTag: String, protocol: Int): Boolean =
    Tools.repeatWithTimeout(
        repeatTimes = 0,
        timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS
    ) {
        syncFactoryReset(logTag = logTag, protocol = protocol)
    } ?: false

private suspend fun PartyBoxDevice.syncAuthentication(
    logTag: String,
    action: EnumAuthAction,
    durationSeconds: Int,
    protocol: Int
): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.RET_AUTHENTICATION) },
            protocol = protocol
        ) {
            override fun onAuthResult(success: Boolean) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(success)
            }
        }

        partyBoxDevice.setAuth(
            protocol = protocol,
            action = action,
            durationSeconds = durationSeconds
        )
    }

/**
 * @param timeoutMills The longest time which business expected before receiving 0xF6 notify CMD.
 */
suspend fun PartyBoxDevice.syncAuthenticationWithTimeout(
    timeoutMills: Long,
    logTag: String,
    action: EnumAuthAction,
    durationSeconds: Int,
    protocol: Int
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = timeoutMills) {
    syncAuthentication(
        logTag = logTag,
        action = action,
        durationSeconds = durationSeconds,
        protocol = protocol
    )
} ?: false

// TODO Arlo.Zheng Code Review plz
suspend fun <Resp> PartyBoxDevice.sendCmdSync(
    command: GeneralGattCommand,
    parser: ((payload: ByteArray) -> Resp)? = null,
    parserCmd: ((receivedCommand: IGeneralCommand) -> Resp)? = null,
    protocol: Int
): Resp? {
    val device = this
    when (protocol) {
        BluetoothDevice.TRANSPORT_LE -> {
            if (!device.isGattConnected) {
                return null
            }
        }

        BluetoothDevice.TRANSPORT_BREDR -> {
            if (!device.isBrEdrConnected) {
                return null
            }
        }
    }

    val completer = CompletableDeferred<Resp?>()
    val observer = object : IPartyBoxDeviceListener {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (sendCommand == command) {
                try {
                    unregisterDeviceListener(this)
                    if (null != parser) {
                        completer.complete(parser.invoke(receivedCommand.payload!!))
                        return
                    }
                    if (null != parserCmd) {
                        completer.complete(parserCmd.invoke(receivedCommand))
                        return
                    }
                    throw Exception("there is no parser to parse response")
                } catch (e: Exception) {
                    completer.complete(null)
                }
            }
        }
    }
    device.registerDeviceListener(observer)
    device.sendCommand(protocol = protocol, sendCommand = command)

    val ret = withTimeoutOrNull(command.requestTimeoutMills) {
        completer.await()
    }

    device.unregisterDeviceListener(observer)
    return ret
}

suspend fun PartyBoxDevice.syncGetRadioSettingsWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): RadioInfo.Setting? = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncGetRadioSettings(logTag = logTag, protocol = protocol)
}

// HorizonBLERepository.getRadioSetting
private suspend fun PartyBoxDevice.syncGetRadioSettings(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): RadioInfo.Setting? = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<RadioInfo.Setting?>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = null,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (device is PartyBoxBTDevice && receivedCommand.isRadioInfoCommandRsp()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(device.radioInfo?.setting)
            }
        }
    }

    partyBoxDevice.reqRadioInfo(
        protocol = protocol,
        cmd = ReqRadioInfoCommand(RadioInfo.RequestType.RadioSetting)
    )
}

suspend fun PartyBoxDevice.syncGetPresetRadioListWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): RadioInfo? = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncGetPresetRadioList(logTag = logTag, protocol = protocol)
}

// HorizonBLERepository.getRadioStationList
private suspend fun PartyBoxDevice.syncGetPresetRadioList(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): RadioInfo? = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<RadioInfo?>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = null,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (device is PartyBoxBTDevice && receivedCommand.isRadioInfoCommandRsp()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(device.radioInfo)
            }
        }
    }

    partyBoxDevice.reqRadioInfo(
        protocol = protocol,
        cmd = ReqRadioInfoCommand(RadioInfo.RequestType.AllPreset)
    )
}

suspend fun PartyBoxDevice.syncGetLastStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): RadioInfo.Station? = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncGetLastStation(logTag = logTag, protocol = protocol)
}

// HorizonBLERepository.getLastStation
private suspend fun PartyBoxDevice.syncGetLastStation(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): RadioInfo.Station? = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<RadioInfo.Station?>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = null,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.REQ_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (device is PartyBoxBTDevice && receivedCommand.isRadioInfoCommandRsp()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(device.radioInfo?.currentStation)
            }
        }
    }

    partyBoxDevice.reqRadioInfo(
        protocol = protocol,
        cmd = ReqRadioInfoCommand(RadioInfo.RequestType.LastStation)
    )
}

suspend fun PartyBoxDevice.syncSwitchRadioTypeWithTimeout(
    radioType: RadioInfo.Type,
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncSwitchRadioType(radioType = radioType, protocol = protocol, logTag = logTag)
} ?: false

// HorizonBLERepository.switchRadioType
private suspend fun PartyBoxDevice.syncSwitchRadioType(
    radioType: RadioInfo.Type,
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalitySwitchRadioType,
        cmdBytes = byteArrayOf(RadioInfo.Command.RadioType.cmd.toByte(), 1, radioType.code)
    )
}

suspend fun PartyBoxDevice.syncNotifyRadioListNotifyWithTimeout(
    radioType: RadioInfo.Type,
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncNotifyRadioListNotify(radioType = radioType, protocol = protocol, logTag = logTag)
} ?: false

// This request only trigger the device to notify the latest radio list. Won't return by rsp directly.
// getCurrentRadioList
private suspend fun PartyBoxDevice.syncNotifyRadioListNotify(
    radioType: RadioInfo.Type,
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityCurrentRadioList,
        cmdBytes = byteArrayOf(RadioInfo.Command.RadioType.cmd.toByte(), 1, radioType.code)
    )
}

suspend fun PartyBoxDevice.syncStartRadioScanWithTimeout(
    radioType: RadioInfo.Type,
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncStartRadioScan(radioType = radioType, protocol = protocol, logTag = logTag)
} ?: false

// Start when Station Scan Status(0x86) return 0x00(Never scanned) or
// 0x72(Has been scanned but no stations detected)
// startRadioScan
private suspend fun PartyBoxDevice.syncStartRadioScan(
    radioType: RadioInfo.Type,
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityScan,
        cmdBytes = byteArrayOf(RadioInfo.Command.RadioType.cmd.toByte(), 1, radioType.code)
    )
}

suspend fun PartyBoxDevice.syncStopRadioScanWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncStopRadioScan(protocol = protocol, logTag = logTag)
} ?: false

// stopRadioScan
private suspend fun PartyBoxDevice.syncStopRadioScan(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityStopScan,
        cmdBytes = null
    )
}

suspend fun PartyBoxDevice.syncPlayStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncPlayStation(protocol, logTag)
} ?: false

// playStation
private suspend fun PartyBoxDevice.syncPlayStation(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityPlay,
        cmdBytes = null
    )
}

suspend fun PartyBoxDevice.syncStopStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncStopStation(protocol = protocol, logTag = logTag)
} ?: false

// stopStation
private suspend fun PartyBoxDevice.syncStopStation(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityStop,
        cmdBytes = null
    )
}

suspend fun PartyBoxDevice.syncPrevStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncPrevOrNextStation(protocol = protocol, logTag = logTag, isPrev = true)
} ?: false

suspend fun PartyBoxDevice.syncNextStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncPrevOrNextStation(protocol = protocol, logTag = logTag, isPrev = false)
} ?: false

// prevOrNextStation
private suspend fun PartyBoxDevice.syncPrevOrNextStation(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    isPrev: Boolean
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = if (isPrev) {
            RadioInfo.Command.FunctionalityPreviousStation
        } else {
            RadioInfo.Command.FunctionalityNextStation
        },
        cmdBytes = null
    )
}

suspend fun PartyBoxDevice.syncAddFavouriteWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    station: RadioInfo.Station
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncAddFavourite(protocol = protocol, logTag = logTag, station = station)
} ?: false

// updateStation
private suspend fun PartyBoxDevice.syncAddFavourite(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    station: RadioInfo.Station
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityUpdateStation,
        cmdBytes = station.infoBytes()
    )
}

suspend fun PartyBoxDevice.syncActivePresetStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    station: RadioInfo.Station
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncActivePresetStation(protocol = protocol, logTag = logTag, station = station)
} ?: false

// activePresetStation
suspend fun PartyBoxDevice.syncActivePresetStation(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    station: RadioInfo.Station
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityActiveStation,
        cmdBytes = station.presetInfoBytes()
    )
}

suspend fun PartyBoxDevice.syncActiveStationWithTimeout(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    station: RadioInfo.Station
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncActiveStation(protocol = protocol, logTag = logTag, station = station)
} ?: false

// activeStation
private suspend fun PartyBoxDevice.syncActiveStation(
    protocol: Int = BluetoothDevice.TRANSPORT_LE,
    logTag: String,
    station: RadioInfo.Station
): Boolean = suspendCancellableCoroutine { continuation ->
    val partyBoxDevice = this

    object : PartyBoxContinuationDeviceListener<Boolean>(
        partyBoxDevice = partyBoxDevice,
        continuation = continuation,
        errorResult = false,
        logTag = logTag,
        matcher = { e -> e.isTargetCommand(GattPacketFormat.SET_RADIO_INFO) },
        protocol = protocol
    ) {
        override fun onCommandReceived(
            device: BaseBTDevice<*, *>,
            sendCommand: IGeneralCommand?,
            receivedCommand: IGeneralCommand
        ) {
            if (receivedCommand.isSetRadioInfoAck()) {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }
    }

    partyBoxDevice.setRadioInfo(
        protocol = protocol,
        cmd = RadioInfo.Command.FunctionalityActiveStation,
        cmdBytes = station.activeStationInfoBytes()
    )
}

private open class PartyBoxContinuationDeviceListener<Result>(
    private val logTag: String = TAG,
    private val partyBoxDevice: PartyBoxDevice,
    private val continuation: CancellableContinuation<Result>,
    private val errorResult: Result,
    private val matcher: (gattException: Exception) -> Boolean,
    private val protocol: Int
) : IPartyBoxDeviceListener {

    init {
        register()
    }

    override fun onGattStatusChanged(
        status: EnumConnectionStatus,
        session: BaseGattSession<*, *, *>
    ) {
        if (BluetoothDevice.TRANSPORT_LE == protocol) {
            Logger.e(
                logTag,
                "onGattStatusChanged() >>> UUID[${partyBoxDevice.UUID}] status[$status]"
            )
            onStatusChanged(status = status)
        }
    }

    override fun onBrEdrStatusChanged(
        status: EnumConnectionStatus,
        session: BaseBrEdrSession<*, *, *>
    ) {
        if (BluetoothDevice.TRANSPORT_BREDR == protocol) {
            Logger.e(
                logTag,
                "onBrEdrStatusChanged() >>> UUID[${partyBoxDevice.UUID}] status[$status]"
            )
            onStatusChanged(status = status)
        }
    }

    fun onStatusChanged(status: EnumConnectionStatus) {
        when (status) {
            EnumConnectionStatus.DISCONNECTED -> {
                partyBoxDevice.unregisterDeviceListener(this)
                continuation.safeResume(errorResult)
            }

            else -> {
                // do nothing
            }
        }
    }

    override fun onGattException(e: Exception, session: BaseGattSession<*, *, *>) {
        Logger.e(logTag, "onGattException() >>> gatt exception[${partyBoxDevice.UUID}] $e")
        onException(e = e)
    }

    override fun onBrEdrException(e: Exception, session: BaseBrEdrSession<*, *, *>) {
        Logger.e(logTag, "onBrEdrException() >>> brEdr exception[${partyBoxDevice.UUID}] $e")
        onException(e = e)
    }

    fun onException(e: Exception) {
        if (matcher(e)) {
            partyBoxDevice.unregisterDeviceListener(this)
            continuation.safeResume(errorResult)
        }
    }

    private fun register() {
        continuation.invokeOnCancellation {
            partyBoxDevice.unregisterDeviceListener(this)
        }

        partyBoxDevice.registerDeviceListener(this)
    }
}

/**
 * cause Onyx use 100 as max volume but others use 32 instead. We need a workaround function to union.
 */
fun PartyBoxBTDevice.workaroundToPercentageVolume(raw: Int): Int {
    return workaroundToPercentageVolume(pid = pid, raw = raw)
}

fun workaroundToPercentageVolume(pid: String?, raw: Int): Int {
    val maxVolume = pid?.let { AppConfigurationUtils.maxVolume(it) } ?: 100
    return if (maxVolume <= 32) {
        raw.u32ToPercentage()
    } else {
        raw
    }
}

fun String?.isHomeBtCategory() = ProductCategory.HomeBt.category.equals(
    AppConfigurationUtils.getCategory(this), true
)

fun String?.isSupportLightControl() = true == this?.let { AppConfigurationUtils.supportLightControl(it) }

fun PartyBoxDevice.isHomeBtCategory(): Boolean = pid.isHomeBtCategory()

fun PartyBoxDevice.isSupportLightControl(): Boolean = true == pid?.let { AppConfigurationUtils.supportLightControl(it) }

fun String?.isHorizonCategory() = when (this) {
    "2117" -> true
    else -> false
}

fun PartyBoxDevice.isHorizonCategory(): Boolean = pid.isHorizonCategory()

suspend fun PartyBoxDevice.syncGetScreenDisplayInfo(protocol: Int): Boolean {
    val device = this
    return withTimeout(PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { continuation ->
            val listener = object : IPartyBoxDeviceListener {
                override fun onScreenDisplayInfo() {
                    continuation.safeResume(true)
                    device.unregisterDeviceListener(this)
                }
            }
            device.registerDeviceListener(listener)
            device.reqScreenDisplayInfo(protocol)
            Logger.i(TAG, "asyncGetScreenDisplayInfo")
        }
    } ?: false
}

suspend fun PartyBoxDevice.syncGetAmbientLightInfo(protocol: Int): Boolean {
    val device = this
    return withTimeout(PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { continuation ->
            val listener = object : IPartyBoxDeviceListener {
                override fun onAmbientLightInfo() {
                    continuation.safeResume(true)
                    device.unregisterDeviceListener(this)
                }
            }
            device.registerDeviceListener(listener)
            device.reqAmbientLightInfo(protocol)
            Logger.i(TAG, "asyncGetAmbientLightInfo")
        }
    } ?: false
}
suspend fun PartyBoxDevice.syncGetAutoStandbyInfoWithTimeout(
    logTag: String,
    protocol: Int
): Boolean = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = PartyBoxGattSession.GENERAL_REQ_TIMEOUT_MILLS) {
    syncGetAutoStandbyInfo(logTag = logTag, protocol = protocol)
} ?: false

private suspend fun PartyBoxDevice.syncGetAutoStandbyInfo(logTag: String, protocol: Int): Boolean =
    suspendCancellableCoroutine { continuation ->
        val partyBoxDevice = this

        object : PartyBoxContinuationDeviceListener<Boolean>(
            partyBoxDevice = partyBoxDevice,
            continuation = continuation,
            errorResult = false,
            logTag = logTag,
            matcher = { e -> e.isTargetCommand(GattPacketFormat.RET_AUTO_STANDBY_TIMER) },
            protocol = protocol
        ) {
            private var ackInfo: Boolean = false

            override fun onCommandReceived(
                device: BaseBTDevice<*, *>,
                sendCommand: IGeneralCommand?,
                receivedCommand: IGeneralCommand
            ) {
                if (receivedCommand.ackReqAutoStandbyTimerCommand()) {
                    Logger.d(logTag, "syncGetStudioLightInfo() >>> ack light info")
                    ackInfo = true
                }

                if (ackInfo) {
                    Logger.i(logTag, "syncGetStudioLightInfo() >>> all needs collected")
                    partyBoxDevice.unregisterDeviceListener(this)
                    continuation.safeResume(true)
                }
            }
        }

        partyBoxDevice.sendCommand(protocol = protocol, ReqAutoStandbyTimerCommand())
    }

fun Device?.isConnectable(): Boolean = this is PartyBoxDevice && isConnectable

fun PartyBoxDevice?.supportAutoOff(): Boolean = true == this?.deviceFeature?.supportAutoOff