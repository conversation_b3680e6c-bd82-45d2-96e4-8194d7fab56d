package com.harman.connect

import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import androidx.annotation.AnyThread
import androidx.annotation.WorkerThread
import com.harman.command.TimeoutException
import com.harman.command.WriteCmdCharacteristicException
import com.harman.command.analytics.CleanAnalyticsCommand
import com.harman.command.analytics.OneDeviceAnalyticsParser
import com.harman.command.analytics.ReqAnalyticsCommand
import com.harman.command.common.GeneralGattCommand
import com.harman.command.common.GeneralGattCommandLongBE
import com.harman.command.common.GeneralGattCommandLongLE
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.AuraCastCommand
import com.harman.command.partybox.gatt.FactoryResetCommand
import com.harman.command.partybox.gatt.FlashCommand
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.command.partybox.gatt.Gen3StereoRenameCommand
import com.harman.command.partybox.gatt.GetFeedbackToneCommand
import com.harman.command.partybox.gatt.GroupCommand
import com.harman.command.partybox.gatt.IdentifyDeviceCommand
import com.harman.command.partybox.gatt.PartyBoxGattCommandProcessor
import com.harman.command.partybox.gatt.ReqAdvEQCommand
import com.harman.command.partybox.gatt.ReqAlarmInfoCommand
import com.harman.command.partybox.gatt.ReqAmbientLightCommand
import com.harman.command.partybox.gatt.ReqBatteryStatusCommand
import com.harman.command.partybox.gatt.ReqDevInfoCommand
import com.harman.command.partybox.gatt.ReqDeviceFeatureInfoCommand
import com.harman.command.partybox.gatt.ReqLightInfoCommand
import com.harman.command.partybox.gatt.ReqPlayerInfoCommand
import com.harman.command.partybox.gatt.ReqRadioInfoCommand
import com.harman.command.partybox.gatt.ReqScreenDisplayCommand
import com.harman.command.partybox.gatt.ReqSleepModeInfoCommand
import com.harman.command.partybox.gatt.ReqStudioLightInfoCommand
import com.harman.command.partybox.gatt.SetAdvEQCommandBE
import com.harman.command.partybox.gatt.SetAdvEQCommandLE
import com.harman.command.partybox.gatt.SetAlarmInfoCommand
import com.harman.command.partybox.gatt.SetAmbientLightCommand
import com.harman.command.partybox.gatt.SetAudioSourceCommand
import com.harman.command.partybox.gatt.SetAuthCommand
import com.harman.command.partybox.gatt.SetAuthCommand.Companion.isAuthSuccess
import com.harman.command.partybox.gatt.SetDevInfoCommand
import com.harman.command.partybox.gatt.SetDeviceNameCommand
import com.harman.command.partybox.gatt.SetDjEffectToneCommand
import com.harman.command.partybox.gatt.SetDjEffectVoiceCommand
import com.harman.command.partybox.gatt.SetDjFilterCommand
import com.harman.command.partybox.gatt.SetFeedbackToneCommand
import com.harman.command.partybox.gatt.SetLightColorCommand
import com.harman.command.partybox.gatt.SetLightElementCommand
import com.harman.command.partybox.gatt.SetLightMainSwitchCommand
import com.harman.command.partybox.gatt.SetLightPatternCommand
import com.harman.command.partybox.gatt.SetLightPatternLoopCommand
import com.harman.command.partybox.gatt.SetPlayerStatusCommand
import com.harman.command.partybox.gatt.SetPrimaryChannelCommand
import com.harman.command.partybox.gatt.SetProjectionCommand
import com.harman.command.partybox.gatt.SetRadioInfoCommand
import com.harman.command.partybox.gatt.SetRemoteVolumeCommand
import com.harman.command.partybox.gatt.SetResetPatternColorCommand
import com.harman.command.partybox.gatt.SetSQBoardcastCommand
import com.harman.command.partybox.gatt.SetScreenDisplayCommand
import com.harman.command.partybox.gatt.SetSleepModeCommand
import com.harman.command.partybox.gatt.SetStudioLightBrightnessCommand
import com.harman.command.partybox.gatt.SetStudioLightDynamicLevelCommand
import com.harman.command.partybox.gatt.SetStudioLightPatternCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.command.partybox.gatt.auth.EnumAuthAction
import com.harman.command.partybox.gatt.battery.EnumBatteryStatusFeature
import com.harman.command.partybox.gatt.eq.EQSettings
import com.harman.command.partybox.gatt.identify.EnumIdentifyDevice
import com.harman.command.partybox.gatt.light.Color
import com.harman.command.partybox.gatt.light.EnumLightDynamicLevel
import com.harman.command.partybox.gatt.light.EnumLightPattern
import com.harman.command.partybox.gatt.light.EnumLightSwitch.Companion.toSwitch
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.command.partybox.gatt.sleep.SleepModeInfo
import com.harman.command.partybox.gatt.timer.ReqAutoOffTimerCommand
import com.harman.command.partybox.gatt.timer.SetAutoOffTimerCommand
import com.harman.connect.listener.IPartyBoxDeviceListener
import com.harman.connect.session.PartyBoxBusinessSession
import com.harman.discover.bean.PartyBoxDevice
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.GattListenerProxy
import com.harman.discover.bean.bt.PartyBoxBTDevice
import com.harman.discover.info.AmbientLightInfo
import com.harman.discover.info.AudioChannel
import com.harman.discover.info.AudioSource
import com.harman.discover.info.AuraCastStatus
import com.harman.discover.info.GeneralRole
import com.harman.discover.info.PartyConnectStatus
import com.harman.discover.info.PlayerStatus
import com.harman.discover.info.ScreenDisplayInfo
import com.harman.discover.util.Tools.decodeHex
import com.harman.discover.util.Tools.decodeUTF8WithLimit
import com.harman.discover.util.Tools.hasExtra
import com.harman.discover.util.Tools.isLongCommandBE
import com.harman.discover.util.Tools.isLongCommandLE
import com.harman.discover.util.Tools.objectUniID
import com.harman.discover.util.Tools.percentageToU32
import com.harman.discover.util.Tools.targetCommand
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_DEFAULT
import com.harman.thread.DISPATCHER_IO
import com.harmanbar.ble.utils.HexUtil
import com.jbl.one.configuration.AppConfigurationUtils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.LinkedList
import java.util.Queue
import java.util.UUID

/**
 * Created by gerrardzhang on 2024/2/20.
 */
class PartyBoxGattSession(
    bleAddress: String?,
    device: PartyBoxBTDevice,
    gattListenerProxy: GattListenerProxy,
    private val writeIntervalMs: Long = 80,
) : BaseGattSession<PartyBoxBTDevice, GeneralRole, GeneralRole>(
    bleAddress = bleAddress,
    device = device,
    listenerProxy = gattListenerProxy
), PartyBoxBusinessSession {

    override val rxUUID: UUID = UUID.fromString("65786365-6C70-6F69-6E74-2E636F6D0001")

    override val delayMillsBeforeDiscoverServices: Long = 600L

    override val delayMillsBeforeRequestMTU: Long = 500L

    /**
     * Use [waitingCmdQueue] as a lock to protect async safety for both self [preWriteCmd] and [afterWriteCmd]
     * FIFO: [Queue.offer] and [Queue.poll]
     */
    private val waitingCmdQueue = LinkedList<GeneralGattCommand>()

    @Volatile
    private var preWriteCmd: GeneralGattCommand? = null

    @Volatile
    private var afterWriteCmd: GeneralGattCommand? = null

    @Volatile
    private var timeoutCheckJob: Job? = null

    /**
     * req
     */
    override fun reqGeneralGattCommand(protocol: Int, command: GeneralGattCommand) {
        sendOrWait(command)
    }

    /**
     * Req Device Info (0x11)
     */
    override fun reqDeviceInfo(protocol: Int) {
        sendOrWait(ReqDevInfoCommand())
    }

    /**
     * Set Device Info (0x13)
     */
    override fun setDeviceInfo(protocol: Int, contentBytes: ByteArray) {
        sendOrWait(SetDevInfoCommand(bytesPayload = contentBytes))
    }

    override fun playMusic(protocol: Int) {
        setPlayerStatus(protocol = protocol, status = PlayerStatus.PLAYER_STATE_PLAY)
    }

    override fun pauseMusic(protocol: Int) {
        setPlayerStatus(protocol = protocol, status = PlayerStatus.PLAYER_STATE_PAUSE)
    }

    override fun prevMusic(protocol: Int) {
        setPlayerStatus(protocol = protocol, status = PlayerStatus.PLAYER_STATE_PREV)
    }

    override fun nextMusic(protocol: Int) {
        setPlayerStatus(protocol = protocol, status = PlayerStatus.PLAYER_STATE_NEXT)
    }

    override fun setPlayerStatus(protocol: Int, status: PlayerStatus) {
        val bytesStatus: ByteArray = byteArrayOf(
            0.toByte(),
            GattPacketFormat.PLAYER_PLAY_TOKEN_ID.toByte(16),
            0x01, // data length
            status.value.toByte()
        )

        sendOrWait(SetPlayerStatusCommand(bytesStatus = bytesStatus))
    }

    /**
     * @param value [0 .. 100]
     *
     * Need to use [percentageToU32] to map value inside [0 .. 32]
     */
    override fun setRemoteVolume(protocol: Int, value: Int) {
        val maxVolume = device.pid?.let { AppConfigurationUtils.maxVolume(it) } ?: 100
        val volume = if (maxVolume <= 32) {
            value.percentageToU32()
        } else {
            value
        }

        sendOrWait(SetRemoteVolumeCommand(percentageVolume = value, bVolume = volume.toByte()))
        device.volume = volume
    }

    /**
     * 0x41 Req Player Info
     */
    override fun getRemoteVolume(protocol: Int) {
        sendOrWait(ReqPlayerInfoCommand())
    }

    /**
     * 0x41 Req Player Info
     */
    override fun reqPlayerInfo(protocol: Int) {
        sendOrWait(ReqPlayerInfoCommand())
    }

    /**
     * 0xE1 Req Advanced EQ
     */
    override fun reqAdvancedEQ(protocol: Int) {
        sendOrWait(ReqAdvEQCommand())
    }

    /**
     * 0xE3 Set Advanced EQ
     */
    override fun setAdvancedEQLE(protocol: Int, eqSettings: EQSettings, isNeedAlgorithm: Boolean?) {
        sendOrWait(SetAdvEQCommandLE(eqSettings = eqSettings, isNeedAlgorithm = isNeedAlgorithm))
    }

    /**
     * 0xE3 Set Advanced EQ
     */
    override fun setAdvancedEQBE(protocol: Int, eqSettings: EQSettings, isNeedAlgorithm: Boolean?) {
        sendOrWait(SetAdvEQCommandBE(eqSettings = eqSettings, isNeedAlgorithm = isNeedAlgorithm))
    }

    /**
     * 0x13 Set Device Info
     */
    override fun gen3GroupStereo(
        protocol: Int,
        audioChannel: AudioChannel,
        connectStatus: PartyConnectStatus,
        groupID: String,
        groupName: String,
        secondaryDevice: PartyBoxDevice?
    ) {
        val bytesGroupID = try {
            groupID.decodeHex()
        } catch (e: Exception) {
            Logger.w(
                TAG,
                "groupGen3Stereo() >>> illegal hex groupID[$groupID], will encode to ASCII code"
            )
            groupID.decodeUTF8WithLimit(MAX_GROUP_ID_BYTE_LENGTH)
        }

        val bytesGroupName = groupName.decodeUTF8WithLimit(MAX_GROUP_NAME_BYTE_LENGTH)
        val actualGroupID = HexUtil.hexStringToString(HexUtil.encodeHexStr(bytesGroupID))
        val actualGroupName = HexUtil.hexStringToString(HexUtil.encodeHexStr(bytesGroupName))

        Logger.i(
            TAG, "groupGen3Stereo() >>>\n" +
                    "group.id[$groupID] actual[$actualGroupID]\n" +
                    "group.name[$groupName] actual[$actualGroupName]"
        )

        sendOrWait(
            GroupCommand(
                audioChannel = audioChannel,
                connectStatus = connectStatus,
                bytesGroupID = bytesGroupID,
                bytesGroupName = bytesGroupName,
                groupID = actualGroupID,
                groupName = actualGroupName,
                secondaryDevice = secondaryDevice
            )
        )
    }

    /**
     * 0xF4 Identify device
     */
    override fun identifyDevice(
        protocol: Int,
        mainIdentify: EnumIdentifyDevice?,
        coIdentify: EnumIdentifyDevice?
    ) {
        sendOrWait(
            IdentifyDeviceCommand(mainIdentify = mainIdentify, coIdentify = coIdentify)
        )
    }

    /**
     * 0x13 Set Device Info
     */
    override fun gen3RenameStereo(protocol: Int, groupName: String) {
        val bytesGroupName = groupName.decodeUTF8WithLimit(MAX_GROUP_NAME_BYTE_LENGTH)
        val actualGroupName = HexUtil.hexStringToString(HexUtil.encodeHexStr(bytesGroupName))
        Logger.i(TAG, "groupGen3Stereo() >>> group.name[$groupName] actual[$actualGroupName]")

        sendOrWait(
            Gen3StereoRenameCommand(
                bytesGroupName = bytesGroupName,
                groupName = actualGroupName
            )
        )
    }

    /**
     * 0x13 Set Device Info
     */
    override fun setPrimaryChannel(
        protocol: Int,
        channel: AudioChannel,
        secondaryDevice: PartyBoxDevice?
    ) {
        sendOrWait(SetPrimaryChannelCommand(channel = channel, secondaryDevice = secondaryDevice))
    }

    /**
     * 0x13 Set Device Info
     */
    override fun unGroup(protocol: Int, secondaryDevice: PartyBoxDevice?) {
        sendOrWait(
            GroupCommand(
                audioChannel = AudioChannel.NONE_CHANNEL,
                connectStatus = PartyConnectStatus.PARTY_CONNECT_OFF,
                bytesGroupID = null,
                bytesGroupName = null,
                groupID = null,
                groupName = null,
                secondaryDevice = secondaryDevice
            )
        )
    }

    /**
     * 0xA1 Req Device Feature Support
     */
    override fun reqDeviceFeatureInfo(protocol: Int) {
        sendOrWait(ReqDeviceFeatureInfoCommand())
    }

    /**
     * 0x13 Set Device Info
     */
    override fun gen2GroupTws(
        protocol: Int,
        audioChannel: AudioChannel,
        connectStatus: PartyConnectStatus,
        secondaryDevice: PartyBoxDevice?
    ) {
        sendOrWait(
            GroupCommand(
                audioChannel = audioChannel,
                connectStatus = connectStatus,
                bytesGroupID = null,
                bytesGroupName = null,
                groupID = null,
                groupName = null,
                secondaryDevice = secondaryDevice
            )
        )
    }

    /**
     * 0x33 Set Light Info
     */
    override fun flashLight(protocol: Int, primary: Boolean, secondary: Boolean) {
        sendOrWait(
            FlashCommand(
                primarySoloPattern = if (primary) GattPacketFormat.SoloPatternID.TWS_SOLO_FLASH else null,
                primarySoloSpeed = if (primary) 1.toByte() else null,
                secondarySoloPattern = if (secondary) GattPacketFormat.SoloPatternID.TWS_SOLO_FLASH else null,
                secondarySoloSpeed = if (secondary) 1.toByte() else null,
            )
        )
    }

    /**
     * 0x13 Set Dev Info
     */
    override fun setAuraCastStatus(protocol: Int, status: AuraCastStatus) {
        sendOrWait(AuraCastCommand(status = status))
    }

    override fun setDjEffectTone(protocol: Int, toneID: Int) {
        sendOrWait(SetDjEffectToneCommand(toneID))
    }

    override fun setDjEffectVoice(protocol: Int, voiceID: Int) {
        sendOrWait(SetDjEffectVoiceCommand(voiceID))
    }

    override fun setDjEffectFilter(protocol: Int, djFilterID: Int, level: Int) {
        sendOrWait(SetDjFilterCommand(djFilterID, level))
    }

    /**
     * 0x33 Set Light Info
     */
    override fun setLightPattern(protocol: Int, pattern: EnumLightPattern) {
        sendOrWait(SetLightPatternCommand(pattern = pattern))
    }

    /**
     * 0x33 Set Light Info
     */
    override fun setLightMainSwitch(protocol: Int, on: Boolean) {
        sendOrWait(SetLightMainSwitchCommand(mainSwitch = on.toSwitch()))
    }

    /**
     * 0x33 Set Light Info
     */
    override fun setLightElementSwitch(protocol: Int, element: Byte, on: Boolean) {
        sendOrWait(SetLightElementCommand(element = element, switch = on.toSwitch()))
    }

    /**
     * 0x33 Set Light Info
     */
    override fun openLightPatternLoop(protocol: Int) {
        sendOrWait(SetLightPatternLoopCommand())
    }

    /**
     * 0x33 Set Light Info
     */
    override fun setLightColor(protocol: Int, color: Color) {
        sendOrWait(SetLightColorCommand(color = color))
    }

    /**
     * 0x31 Req Light Info
     */
    override fun reqLightInfo(protocol: Int) {
        sendOrWait(ReqLightInfoCommand())
    }

    override fun sendCommand(protocol: Int, sendCommand: GeneralGattCommand) {
        sendOrWait(sendCommand)
    }

    /**
     * 0xF1 Req Feedback tone state.
     */
    override fun reqFeedbackTone(protocol: Int) {
        sendOrWait(GetFeedbackToneCommand())
    }

    /**
     * 0xF3 Set Feedback tone state
     */
    override fun setFeedbackTone(protocol: Int, isOn: Boolean) {
        sendOrWait(SetFeedbackToneCommand(isOn = isOn))
    }

    /**
     * 0x13 Set Device Info
     */
    override fun setRemoteDeviceName(protocol: Int, name: String) {
        sendOrWait(SetDeviceNameCommand(name = name))
    }

    /**
     * 0xA5 Factory Reset
     */
    override fun factoryReset(protocol: Int) {
        sendOrWait(FactoryResetCommand())
    }

    /**
     * 0x9D ReqBatteryStatus
     */
    override fun reqBatteryStatus(protocol: Int, features: List<EnumBatteryStatusFeature>) {
        sendOrWait(ReqBatteryStatusCommand(features = features))
    }

    /**
     * 0xF5 Set Authentication
     */
    override fun setAuth(protocol: Int, action: EnumAuthAction, durationSeconds: Int) {
        sendOrWait(SetAuthCommand(action = action, durationSeconds = durationSeconds))
    }

    override fun getStudioLightInfo(protocol: Int) {
        sendOrWait(ReqStudioLightInfoCommand())
    }

    override fun enableLightSwitch(protocol: Int, isOn: Boolean) {
        sendOrWait(SetLightMainSwitchCommand(mainSwitch = isOn.toSwitch()))
    }

    override fun setPattern(protocol: Int, activePatternId: String, colorLevel: Int) {
        val pattern = EnumLightPattern.getByValue(activePatternId) ?: EnumLightPattern.OCEAN
        sendOrWait(SetStudioLightPatternCommand(pattern = pattern, colorLevel = colorLevel))
    }

    override fun setBrightness(protocol: Int, brightness: Int) {
        sendOrWait(SetStudioLightBrightnessCommand(brightness = brightness))
    }

    override fun setDynamicLevel(protocol: Int, level: Int) {
        sendOrWait(
            SetStudioLightDynamicLevelCommand(
                dynamicLevel = EnumLightDynamicLevel.getByLevel(
                    level
                )
            )
        )
    }

    override fun setProjection(protocol: Int, isOn: Boolean) {
        sendOrWait(SetProjectionCommand(projection = isOn.toSwitch()))
    }

    /**
     * 0x13 set device Audio source
     */
    override fun setMoodStatus(protocol: Int, status: Int) {
        val source = if (status == 2) AudioSource.MOOD else AudioSource.NONE_AUDIO
        sendOrWait(SetAudioSourceCommand(audioSource = source))
    }

    override fun resetPatternColor(protocol: Int, patternID: String) {
        val pattern = EnumLightPattern.getByValue(patternID) ?: EnumLightPattern.UNKNOWN
        sendOrWait(SetResetPatternColorCommand(pattern = pattern))
    }

    override fun reqScreenDisplayInfo(protocol: Int) {
        sendOrWait(ReqScreenDisplayCommand())
    }

    override fun updateScreenDisplayInfo(
        protocol: Int,
        screenDisplayInfo: ScreenDisplayInfo,
        command: ScreenDisplayInfo.Command
    ) {
        sendOrWait(
            SetScreenDisplayCommand(
                info = screenDisplayInfo,
                cmd = command
            )
        )
    }

    override fun reqAlarmInfo(protocol: Int, requestType: AlarmInfo.RequestType) {
        sendOrWait(ReqAlarmInfoCommand(requestType = requestType))
    }

    override fun setAlarmInfo(protocol: Int, alarm: AlarmInfo.Alarm?, setting: AlarmInfo.Setting?, cmd: AlarmInfo.Command) {
        sendOrWait(SetAlarmInfoCommand(alarm = alarm, setting = setting, cmd = cmd))
    }

    override fun reqSleepModeInfo(protocol: Int) {
        sendOrWait(ReqSleepModeInfoCommand())
    }

    override fun setSleepModeInfo(protocol: Int, sleepModeInfo: SleepModeInfo?, command: SleepModeInfo.Command) {
        sendOrWait(SetSleepModeCommand(info = sleepModeInfo, cmd = command))
    }

    override fun setAuraCastSqMode(protocol: Int, isOn: Boolean) {
        sendOrWait(
            SetSQBoardcastCommand(isOn)
        )
    }

    override fun reqAmbientLightInfo(protocol: Int) {
        sendOrWait(
            ReqAmbientLightCommand()
        )
    }

    override fun updateAmbientLightInfo(
        protocol: Int,
        ambientLightInfo: AmbientLightInfo,
        command: AmbientLightInfo.Command
    ) {
        sendOrWait(
            SetAmbientLightCommand(ambientLightInfo, command)
        )
    }

    /**
     * 0x75 Req Radio Info
     */
    override fun reqRadioInfo(protocol: Int, cmd: ReqRadioInfoCommand) {
        sendOrWait(cmd)
    }

    /**
     * 0x77 Set Radio Info
     */
    override fun setRadioInfo(protocol: Int, cmd: RadioInfo.Command, cmdBytes: ByteArray?) {
        sendOrWait(SetRadioInfoCommand(cmd = cmd, cmdBytes = cmdBytes))
    }

    override fun reqAnalyticsData(protocol: Int) {
        sendOrWait(ReqAnalyticsCommand())
    }

    override fun cleanAnalyticsData(protocol: Int) {
        sendOrWait(CleanAnalyticsCommand())
    }

    /**
     * 0xB5 Req Auto Off Timer
     */
    override fun reqAutoOffTimer(protocol: Int) {
        sendOrWait(ReqAutoOffTimerCommand())
    }

    /**
     * 0xB7 Set Auto Off Timer
     */
    override fun setAutoOffTimer(protocol: Int, seconds: Int) {
        sendOrWait(SetAutoOffTimerCommand(timerSettings = seconds))
    }

    private fun sendOrWait(sendCommand: GeneralGattCommand) {
        if (!needWaiting(sendCommand = sendCommand)) {
            Logger.d(
                TAG,
                "[$bleAddress] sendOrWait() >>> post command directly:[${sendCommand.objectUniID()}]"
            )
            // do write on I/O thread.
            postCommandDelay(sendCommand = sendCommand)
        } else {
            Logger.d(
                TAG,
                "[$bleAddress] sendOrWait() >>> put command in waiting queue:[${sendCommand.objectUniID()}] ${sendCommand.dataHexString()}"
            )
        }
    }

    @AnyThread
    override fun handleCharacteristicChanged(
        gatt: BluetoothGatt?,
        characteristic: BluetoothGattCharacteristic?,
        value: ByteArray?
    ) {
        super.handleCharacteristicChanged(gatt, characteristic, value)
        value?.also {
            Logger.d(
                TAG,
                "handleCharacteristicChanged() >>> receive original payload:${
                    HexUtil.byte2HexStr(value)
                }"
            )
        }

        if (null == value || value.size < 3) {
            Logger.w(TAG, "handleCharacteristicChanged() >>> invalid characteristic packet")
            return
        }

        val receiveCommand = if (value.isLongCommandLE(device.pid ?: "")) {
            GeneralGattCommandLongLE(value)
        } else if (value.isLongCommandBE(device.pid ?: "")) {
            GeneralGattCommandLongBE(value)
        } else {
            GeneralGattCommand(value)
        }

        val innerReqCmd = afterWriteCmd
        val receiveHex = receiveCommand.dataHexString()
        Logger.d(TAG, "handleCharacteristicChanged() >>> innerReqCmd:$innerReqCmd")
        if (null == innerReqCmd || !innerReqCmd.onNotify(
                device = device,
                receivedCommand = receiveCommand
            )
        ) {
            // Not request command waiting currently, or receiveCommand was not the request command's target.
            Logger.d(TAG, "handleCharacteristicChanged() >>> handleAsNotifyCommand receive notify payload:$receiveHex")
            handleAsNotifyCommand(receiveCommand = receiveCommand)
            return
        }

        if (!innerReqCmd.isProcessed) {
            /** [afterWriteCmd] hadn't not be full processed yet (might waiting more response)
             * swallow further steps and continue waiting. */
            Logger.d(TAG, "handleCharacteristicChanged() >>> payload un-processed yet:$receiveHex")
            return
        }

        Logger.i(
            TAG,
            "handleCharacteristicChanged() >>> receive send command payload:[${innerReqCmd.objectUniID()}] $receiveHex, payloadLen: ${receiveCommand.payloadLen}"
        )
        synchronized(waitingCmdQueue) {
            afterWriteCmd = null
        }

        notifyReceive(
            device = device,
            sendCommand = innerReqCmd,
            receiveCommand = receiveCommand
        )

        sendNext(cancelTimeoutCheck = true)
    }

    /** Non-related response with [afterWriteCmd], might be a noti. */
    private fun handleAsNotifyCommand(receiveCommand: GeneralGattCommand) {
        if (!PartyBoxGattCommandProcessor.processCommand(device, receiveCommand)) {
            // a command which can't be parsed by PartyBoxCommandProcessor,
            // split commands into pieces and callback upwards.
            Logger.d(TAG, "handleAsNotifyCommand() >>> ignore undefined: ${receiveCommand.dataHexString()}")
            return
        }

        notifyReceive(
            device = device,
            sendCommand = null,
            receiveCommand = receiveCommand
        )
    }

    private val deviceListeners: List<IPartyBoxDeviceListener>
        get() = listenerProxy.getter().filterIsInstance<IPartyBoxDeviceListener>()

    private fun notifyReceive(
        device: BaseBTDevice<*, *>,
        sendCommand: IGeneralCommand?,
        receiveCommand: IGeneralCommand
    ) {
        if (device !is PartyBoxBTDevice) {
            Logger.d(TAG, "notifyReceive() >>> device type is not match: uuid[${device.UUID}], type[${device.javaClass}]")
            return
        }

        Logger.d(
            TAG, "notifyReceive() >>>" +
                    " send cmd[0x${HexUtil.byte2HexStr(byteArrayOf(sendCommand?.commandID ?: 0b0))}]" +
                    " received cmd[0x${HexUtil.byte2HexStr(byteArrayOf(receiveCommand.commandID))}]"
        )

        listenerProxy.getter().forEach { listener ->
            Logger.d(TAG, "notifyReceive() >>> notify player info update")
            if (listener is IPartyBoxDeviceListener) {
                listener.onCommandReceived(
                    device = device,
                    sendCommand = sendCommand,
                    receivedCommand = receiveCommand
                )
            }
        }

        if (receiveCommand.targetCommand(GattPacketFormat.RESP_PLAYER_INFO)) {
            Logger.d(TAG, "notifyReceive() >>> notify player info update")
            deviceListeners.forEach { listener ->
                listener.onPlayerInfoUpdate()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_ADVANCE_EQ)) {
            Logger.d(TAG, "notifyReceive() >>> notify advanced eq update")
            deviceListeners.forEach { listener ->
                listener.onAdvanceEQUpdate()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RESP_DEV_INFO) &&
            receiveCommand.hasExtra(GattPacketFormat.PARTY_CONNECT_MODE_TOKEN_ID)
        ) {
            Logger.d(
                TAG,
                "notifyReceive() >>> notify party connect status[${device.partyConnectStatus?.channelName}]"
            )

            device.partyConnectStatus?.let { status ->
                deviceListeners.forEach { listener ->
                    listener.onPartyConnectStatusChanged(status = status)
                }
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RESP_DEV_INFO)) {
            Logger.d(TAG, "notifyReceive() >>> notify device info update")
            deviceListeners.forEach { listener ->
                listener.onDeviceInfoUpdate()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RES_DEVICE_FEATURE_INFO)) {
            Logger.d(TAG, "notifyReceive() >>> notify device feature update")
            deviceListeners.forEach { listener ->
                listener.onDeviceFeatureUpdate()
            }
        } else if (receiveCommand.targetCommand(
                GattPacketFormat.RESP_LIGHT_INFO,
                GattPacketFormat.RESP_INACTIVE_LIGHT_PATTERN
            )
        ) {
            Logger.d(TAG, "notifyReceive() >>> notify light info update")
            deviceListeners.forEach { listener ->
                listener.onLightInfoUpdate()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_FEEDBACK_TONE)) {
            Logger.d(TAG, "notifyReceive() >>> notify feedback tone")
            deviceListeners.forEach { listener ->
                listener.onFeedbackTone()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_BATTERY_STATUS)) {
            Logger.d(TAG, "notifyReceive() >>> notify battery status updated")
            deviceListeners.forEach { listener ->
                listener.onBatteryStatusUpdated()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_AUTHENTICATION)) {
            val ret = receiveCommand.isAuthSuccess()
            Logger.d(TAG, "notifyReceive() >>> notify authentication rsp. [$ret]")
            deviceListeners.forEach { listener ->
                listener.onAuthResult(success = ret)
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_SCREEN_DISPLAY_INFO)) {
            deviceListeners.forEach { listener ->
                listener.onScreenDisplayInfo()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_ALARM_INFO)) {
            deviceListeners.forEach { listener ->
                listener.onAlarmInfo(alarmInfo = device.alarmInfo)
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.DEV_ACK)) {
            when (sendCommand?.commandID) {
                GattPacketFormat.SET_DEV_INFO -> {
                    deviceListeners.forEach { listener ->
                        listener.onSetDeviceInfo()
                    }
                }

                GattPacketFormat.SET_SCREEN_DISPLAY_INFO -> {
                    deviceListeners.forEach { listener ->
                        listener.onScreenDisplayInfo()
                    }
                }
                GattPacketFormat.SET_AMBIENT_LIGHT_INFO->{
                    deviceListeners.forEach { listener ->
                        listener.onAmbientLightInfo()
                    }
                }
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_AMBIENT_LIGHT_INFO)) {
            deviceListeners.forEach { listener ->
                listener.onAmbientLightInfo()
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_RADIO_INFO)) {
            Logger.d(TAG, "notifyReceive() >>> notify RET_RADIO_INFO")
            deviceListeners.forEach { listener ->
                listener.onRadioInfo(radioInfo = device.radioInfo, functionality = device.radioInfo?.functionality)
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_AUTO_OFF_TIMER)) {
            deviceListeners.forEach { listener ->
                listener.onAutoOffTimerUpdate(device.autoOffTimerRsp)
            }
        } else if (receiveCommand.targetCommand(GattPacketFormat.RET_ONE_DEVICE_ANALYTICS)) {
            val receivedPayload = receiveCommand.payload
            if (null != receivedPayload && receivedPayload.size > 2) {
                device.analyticsData = OneDeviceAnalyticsParser.parse(receivedPayload)
                deviceListeners.forEach { listener ->
                    listener.onHomeDeviceAnalytics(device.analyticsData)
                }
            }
        }
    }

    /**
     * @param e @see [com.harman.command.Exceptions]
     */
    override fun notifyException(e: Exception) {
        listenerProxy.getter().forEach { listener ->
            if (listener !is IPartyBoxDeviceListener) {
                return@forEach
            }

            listener.onGattException(e = e, session = this@PartyBoxGattSession)
        }
    }

    /**
     * Do write to socket on I/O thread
     */
    @WorkerThread
    private fun writeCharacteristic(sendCommand: GeneralGattCommand) {
        if (!write(sendCommand.data())) {
            Logger.w(
                TAG,
                "[$bleAddress] writeCharacteristic() >>> fail to write:[${sendCommand.objectUniID()}] ${sendCommand.dataHexString()}"
            )

            notifyException(
                e = WriteCmdCharacteristicException(
                    device = <EMAIL>,
                    sendCommand = sendCommand
                )
            )

            sendNext(cancelTimeoutCheck = true)
            return
        }

        // post timeout check job
        if (sendCommand.isProcessed) {
            Logger.i(
                TAG,
                "[$bleAddress] writeCharacteristic() >>> write success, this request doesn't need to timeout check:[${sendCommand.objectUniID()}] ${sendCommand.dataHexString()}"
            )
            sendNext(cancelTimeoutCheck = false)
            return
        }

        Logger.i(
            TAG,
            "[$bleAddress] writeCharacteristic() >>> write success, post timeout check:[${sendCommand.objectUniID()}] ${sendCommand.dataHexString()}"
        )
        synchronized(waitingCmdQueue) {
            afterWriteCmd = sendCommand
        }
        timeoutCheckJob = postTimeoutCheckJob(innerSendCmd = sendCommand)
    }

    /**
     * launch a coroutine job for further command timeout check
     */
    private fun postTimeoutCheckJob(innerSendCmd: GeneralGattCommand): Job =
        GlobalScope.launch(DISPATCHER_DEFAULT) {
            delay(innerSendCmd.requestTimeoutMills)

            synchronized(waitingCmdQueue) {
                if (null != afterWriteCmd && innerSendCmd == afterWriteCmd) {
                    Logger.e(
                        TAG,
                        "[$bleAddress] postTimeoutCheckJob() >>> timeout happened while sending:${innerSendCmd.dataHexString()}"
                    )
                    notifyException(TimeoutException(device = device, sendCommand = innerSendCmd))
                    sendNext(cancelTimeoutCheck = false)
                }
            }
        }

    /**
     * @return true if some cmd is requesting and need to put [sendCommand] into waiting queue.
     */
    private fun needWaiting(sendCommand: GeneralGattCommand): Boolean =
        synchronized(waitingCmdQueue) {
            if (null == preWriteCmd) {
                preWriteCmd = sendCommand
                return false
            }

            if (sendCommand.displaceSameInQueue) {
                waitingCmdQueue.removeAll { waitCmd ->
                    waitCmd.commandID == sendCommand.commandID
                }
            }

            waitingCmdQueue.offer(sendCommand)
            return true
        }

    /**
     * clear processing cmd [afterWriteCmd] and
     * post next command in [waitingCmdQueue] if not it's not empty.
     */
    @AnyThread
    private fun sendNext(cancelTimeoutCheck: Boolean) {
        val nextCommand = synchronized(waitingCmdQueue) {
            if (cancelTimeoutCheck) {
                runCatching {
                    Logger.d(TAG, "[$bleAddress] sendNext() >>> cancel timeout check")
                    timeoutCheckJob?.cancel()
                }
            }

            preWriteCmd = waitingCmdQueue.poll()
            preWriteCmd
        }

        nextCommand ?: run {
            Logger.w(TAG, "sendNext() >>> no more next command")
            return
        }

        Logger.d(
            TAG,
            "[$bleAddress] sendNext() >>> poll next command:[${nextCommand.objectUniID()}] ${nextCommand.dataHexString()}"
        )
        postCommandDelay(sendCommand = nextCommand)
    }

    private fun postCommandDelay(sendCommand: GeneralGattCommand) {
        Logger.d(TAG, "[$bleAddress] postCommandDelay() >>> post:[${sendCommand.objectUniID()}]")

        GlobalScope.launch(DISPATCHER_IO) {
            // delay some time before each command sending.
            delay(writeIntervalMs)
            Logger.i(
                TAG,
                "[$bleAddress] postCommandDelay() >>> do real write after delay:[${sendCommand.objectUniID()}]"
            )
            writeCharacteristic(sendCommand = sendCommand)
        }
    }

    companion object {
        private const val TAG = "PartyBoxGattSession"

        const val GENERAL_REQ_TIMEOUT_MILLS: Long = 6 * 1000L

        const val MAX_GROUP_ID_BYTE_LENGTH = 8
        const val MAX_GROUP_NAME_BYTE_LENGTH = 26
    }
}