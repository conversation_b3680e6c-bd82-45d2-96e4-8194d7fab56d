package com.harman.connect

import android.content.Context
import android.text.TextUtils
import androidx.annotation.CallSuper
import androidx.annotation.MainThread
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.AuthResponse
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.BleGetEncryptionKeyResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.ColorPickerResp
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DJPadResponse
import com.harman.command.one.bean.DeviceInfo
import com.harman.command.one.bean.DeviceNameRspUnion
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.GetChromeCastOptInResponse
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetOtaAccessPointResponse
import com.harman.command.one.bean.GetSmartBtnConfigRsp
import com.harman.command.one.bean.GroupDeviceOTAStatusRsp
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.ValueResponse
import com.harman.command.one.bean.WifiSetupEndResponse
import com.harman.command.one.command.OneGattCommand
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.syncCmd
import com.harman.discover.bean.OneDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.getBarPort
import com.harman.discover.util.Tools.safeResume
import com.harman.discover.util.Tools.truncateSafety
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_FAST_MAIN
import com.harman.thread.DISPATCHER_IO
import com.harmanbar.ble.entity.BLEResponse
import com.harmanbar.ble.statistic.StatisticConstant
import com.harmanbar.ble.utils.BLEProfile
import com.harmanbar.ble.utils.GsonUtil
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.coroutineContext
import kotlin.coroutines.resume

/**
 * Created by gerrardzhang on 2024/4/17.
 *
 * Some tools make async works become sync ones.
 */

const val GENERAL_TIMEOUT_MILLS = 15 * 1000L
const val _TAG = "OneExtsTag"

/**
 * Request StartAuth CMD and wait for BleAuthEnd(0x100F):
 *
 *  It's sent from firmware. Depends on user has performed requested authentication action or not, device returns auth result:
 * 0: means success.
 * 1: means fail
 *
 * { "auth_result": "0" }
 */
private suspend fun OneDevice.syncGattAuthResult(logTag: String, timeoutSecs: Int): Boolean {
    val oneDevice = this
    val gattSession = oneDevice.gattSession ?: return false
    val btDevice = oneDevice.bleDevice ?: return false
    if (!gattSession.isConnected) {
        return false
    }

    return suspendCancellableCoroutine { continuation ->
        object : OneContinuationDeviceListener<Boolean>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = false
        ) {
            /**
             * GATT Notify
             */
            override fun onResponseReceived(response: BLEResponse) {
                if (StatisticConstant.LP_AUTH_RESULT != response.responsedCode) {
                    return
                }

                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(workaroundResult(response.responseData))
            }

            private fun workaroundResult(jsonStr: String?): Boolean {
                if (jsonStr.isNullOrBlank()) {
                    // Work around: json content might not be fully transferred.
                    // Regard this scenario as success
                    Logger.w(logTag, "workaroundResult() >>> work around as success.")
                    return true
                }

                val rsp = GsonUtil.parseJsonToBean(jsonStr, AuthResponse::class.java) ?: run {
                    // Work around: json content might not be fully transferred.
                    // Regard this scenario as success
                    Logger.w(logTag, "workaroundResult() >>> work around as success.")
                    return true
                }

                return rsp.success
            }
        }

        btDevice.startAuth(timeoutSecs = timeoutSecs)
    }
}

suspend fun OneDevice.syncGattAuthResultWithTimeout(logTag: String): Boolean = Tools.repeatWithTimeout(
    repeatTimes = 0, timeoutMills = StatisticConstant.CONFIG_SOUND_TIMEOUT * 1000L
) {
    syncGattAuthResult(logTag = logTag, timeoutSecs = StatisticConstant.CONFIG_SOUND_TIMEOUT)
} ?: false

/**
 * Request StartAuth CMD and wait for BleAuthEnd(0x100F):
 *
 *  It's sent from firmware. Depends on user has performed requested authentication action or not, device returns auth result:
 * 0: means success.
 * 1: means fail
 *
 * { "auth_result": "0" }
 */
private suspend fun OneDevice.syncGattPairResult(logTag: String, timeoutSecs: Int, flow: MutableStateFlow<EnumConnectionStatus>?): EnumConnectionStatus {
    val oneDevice = this
    val btDevice = oneDevice.bleDevice ?: return EnumConnectionStatus.DISCONNECTED

    return suspendCancellableCoroutine { continuation ->
        object : OneContinuationDeviceListener<EnumConnectionStatus>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = EnumConnectionStatus.DISCONNECTED
        ) {
            /**
             * GATT Notify
             */
            override fun onResponseReceived(response: BLEResponse) {
                Logger.d(logTag, "syncGattPairResult() onResponseReceived() >>> : $response")

                if (StatisticConstant.LP_AUTH_PAIR == response.responsedCode) {
                    oneDevice.unregisterDeviceListener(this)
                    CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                        flow?.emit(EnumConnectionStatus.CONNECTED)
                    }
                    continuation.safeResume(EnumConnectionStatus.CONNECTED)
                } else if (StatisticConstant.LP_AUTH_PAIRING == response.responsedCode) {
                    CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                        flow?.emit(EnumConnectionStatus.PAIRING)
                    }
                    // Note: Don't resume with EnumConnectionStatus.PAIRING here,
                    // App need to wait [StatisticConstant.LP_AUTH_PAIR] command ID to finish pairing.
                }
            }
        }

        btDevice.authPair(timeoutSecs = timeoutSecs)
    }
}

suspend fun OneDevice.syncGattPairResultWithTimeout(logTag: String, flow: MutableStateFlow<EnumConnectionStatus>?): EnumConnectionStatus =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = StatisticConstant.CONFIG_SOUND_TIMEOUT * 1000L) {
        syncGattPairResult(logTag = logTag, StatisticConstant.CONFIG_SOUND_TIMEOUT, flow = flow)
    } ?: EnumConnectionStatus.DISCONNECTED

private suspend fun OneDevice.gattSetWifi(logTag: String, req: SetWifiNetworkRequest): WifiSetupEndResponse? {
    val oneDevice = this
    val gattSession = oneDevice.gattSession ?: return null
    val btDevice = oneDevice.bleDevice ?: return null
    if (!gattSession.isConnected) {
        return null
    }

    return suspendCancellableCoroutine { continuation ->
        object : OneContinuationDeviceListener<WifiSetupEndResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onSetWifiNetworkResult(rsp: WifiSetupEndResponse) {
                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }

        btDevice.setWifiNetwork(req)
    }
}

suspend fun OneDevice.syncGattSetWifiWithTimeout(logTag: String, req: SetWifiNetworkRequest): WifiSetupEndResponse? =
    Tools.repeatWithTimeout(
        repeatTimes = 0, timeoutMills = BLEProfile.BLE_CONN_TIME_OUT // 90 secs
    ) {
        gattSetWifi(logTag = logTag, req = req)
    }

private suspend fun OneDevice.syncGetBatteryStatus(logTag: String): BatteryStatusResponse? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<BatteryStatusResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onBatteryStatus(rsp: BatteryStatusResponse) {
                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }

        oneDevice.getBatteryStatus()
    }

suspend fun OneDevice.syncGetBatteryStatusWithTimeout(logTag: String): BatteryStatusResponse? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetBatteryStatus(logTag = logTag)
    }

private suspend fun OneDevice.syncGetFeatureSupport(logTag: String, port: String?): FeatureSupport? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<FeatureSupport?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onFeatureSupport(featureSupport: FeatureSupport) {
                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(featureSupport)
            }
        }

        oneDevice.getFeatureSupport(port)
    }

suspend fun OneDevice.syncGetAuraCastSqModeWithTimeout(logTag: String): AuraCastSqModelResponse? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetAuraCastSqMode(logTag = logTag)
    }

private suspend fun OneDevice.syncGetAuraCastSqMode(logTag: String): AuraCastSqModelResponse? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<AuraCastSqModelResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onAuraCastSqMode(response: AuraCastSqModelResponse) {
                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(response)
            }
        }

        oneDevice.getAuraCastSqMode()
    }

private suspend fun OneDevice.syncGetOOBEEncryptionKey(logTag: String): BleGetEncryptionKeyResponse? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<BleGetEncryptionKeyResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onOOBEEncryptionKey(response: BleGetEncryptionKeyResponse) {
                super.onOOBEEncryptionKey(response)
                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(response)
            }
        }
        oneDevice.getOOBEEncryptionKey()
    }

suspend fun OneDevice.syncGetFeatureSupportWithTimeout(logTag: String, port: String? = null): FeatureSupport? {
    return Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetFeatureSupport(logTag = logTag, port)
    }
}

suspend fun OneDevice.syncGetOOBEEncryptionKeyWithTimeout(logTag: String, timeout: Long = GENERAL_TIMEOUT_MILLS): BleGetEncryptionKeyResponse? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = timeout) {
        syncGetOOBEEncryptionKey(logTag = logTag)
    }

suspend fun OneDevice.syncGetOtaAccessPoint(logTag: String): GetOtaAccessPointResponse? {
    return suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<GetOtaAccessPointResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGetOTAAccessPoint(rsp: GetOtaAccessPointResponse) {
                oneDevice.unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }

        oneDevice.getOtaAccessPoint()
    }
}

suspend fun OneDevice.syncGetOtaAccessPointWithTimeout(logTag: String): GetOtaAccessPointResponse? =
    Tools.repeatWithTimeout(
        repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS
    ) {
        syncGetOtaAccessPoint(logTag = logTag)
    }

private suspend fun OneDevice.syncRequestDeviceOta(logTag: String): BasicResponse? {
    return suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<BasicResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onRspSuccess(cmd: String?, content: String?, port: String?) {
                oneDevice.unregisterDeviceListener(this)

                val rsp = if (!content.isNullOrBlank()) {
                    GsonUtil.parseJsonToBean(content, BasicResponse::class.java)
                } else {
                    null
                }

                continuation.safeResume(rsp)
            }
        }

        oneDevice.requestDeviceOta()
    }
}

suspend fun OneDevice.syncRequestDeviceOtaWithTimeout(logTag: String): BasicResponse? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncRequestDeviceOta(logTag = logTag)
    }

suspend fun OneDevice.syncEnterAuraCastWithTimeout(logTag: String): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncEnterAuraCast(logTag = logTag)?.success()
    } ?: false

private suspend fun OneDevice.syncEnterAuraCast(logTag: String): BasicResponse? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<BasicResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onRspSuccess(cmd: String?, content: String?, port: String?) {
                oneDevice.unregisterDeviceListener(this)

                val rsp = if (!content.isNullOrBlank()) {
                    GsonUtil.parseJsonToBean(content, BasicResponse::class.java)
                } else {
                    null
                }

                continuation.safeResume(rsp)
            }

            override fun onResponseReceived(response: BLEResponse) {
                super.onResponseReceived(response)

                val content = response.responseData
                val rsp = if (!content.isNullOrBlank()) {
                    GsonUtil.parseJsonToBean(content, BasicResponse::class.java)
                } else {
                    null
                }

                continuation.safeResume(rsp)
            }
        }

        oneDevice.enterAuraCast()
    }

suspend fun OneDevice.syncExitAuraCastWithTimeout(logTag: String): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncExitAuraCast(logTag = logTag)?.success()
    } ?: false

private suspend fun OneDevice.syncExitAuraCast(logTag: String): BasicResponse? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<BasicResponse?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onRspSuccess(cmd: String?, content: String?, port: String?) {
                oneDevice.unregisterDeviceListener(this)

                val rsp = if (!content.isNullOrBlank()) {
                    GsonUtil.parseJsonToBean(content, BasicResponse::class.java)
                } else {
                    null
                }

                continuation.safeResume(rsp)
            }

            override fun onResponseReceived(response: BLEResponse) {
                oneDevice.unregisterDeviceListener(this)

                val content = response.responseData
                val rsp = if (!content.isNullOrBlank()) {
                    GsonUtil.parseJsonToBean(content, BasicResponse::class.java)
                } else {
                    null
                }

                continuation.safeResume(rsp)
            }
        }

        oneDevice.exitAuraCast()
    }

suspend fun OneDevice.syncSetSmartBtnConfigWithTimeout(logTag: String, config: SmartBtnConfig): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncSetSmartBtnConfig(logTag = logTag, config = config)
    } ?: false

private suspend fun OneDevice.syncSetSmartBtnConfig(logTag: String, config: SmartBtnConfig): Boolean =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = false
        ) {
            override fun onRspSuccess(cmd: String?, content: String?, port: String?) {
                oneDevice.unregisterDeviceListener(this)

                val rsp = if (!content.isNullOrBlank()) {
                    GsonUtil.parseJsonToBean(content, BasicResponse::class.java)
                } else {
                    null
                }

                continuation.safeResume(null != rsp && rsp.success())
            }
        }

        oneDevice.setSmartBtnConfig(config = config)
    }

suspend fun OneDevice.syncGetSmartBtnConfigWithTimeout(logTag: String): SmartBtnConfig? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetSmartBtnConfig(logTag = logTag)
    }

private suspend fun OneDevice.syncGetSmartBtnConfig(logTag: String): SmartBtnConfig? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<SmartBtnConfig?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGetSmartBtnConfig(rsp: GetSmartBtnConfigRsp) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.smartConfig)
            }
        }

        oneDevice.getSmartBtnConfig()
    }

suspend fun OneDevice.getChromeCastOptInWithTimeout(logTag: String): ChromeCastOpt? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        getChromeCastOptIn(logTag = logTag)
    }

private suspend fun OneDevice.getChromeCastOptIn(logTag: String): ChromeCastOpt? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<ChromeCastOpt?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onChromecastOptIn(rsp: GetChromeCastOptInResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(ChromeCastOpt.fromValue(rsp.optIn))
            }
        }

        oneDevice.getChromeCastOptIn()
    }

suspend fun OneDevice.setChromeCastOptInWithTimeout(logTag: String, optIn: ChromeCastOpt): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        setChromeCastOptIn(logTag = logTag, optIn = optIn)
    }

private suspend fun OneDevice.setChromeCastOptIn(logTag: String, optIn: ChromeCastOpt): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onChromecastOptIn(rsp: GetChromeCastOptInResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.success())
            }
        }

        oneDevice.setChromeCastOptIn(optIn = optIn)
    }

suspend fun OneDevice.getC4aPermissionStatusWithTimeout(logTag: String): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        getC4aPermissionStatus(logTag = logTag)
    }


private suspend fun OneDevice.getC4aPermissionStatus(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onC4aPermissionStatus(rsp: C4aPermissionStatusResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.enable)
            }
        }

        oneDevice.getC4aPermissionStatus()
    }

suspend fun OneDevice.requestGoogleLogoutWithTimeout(logTag: String): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        requestGoogleLogout(logTag = logTag)
    }


private suspend fun OneDevice.requestGoogleLogout(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onRequestGoogleLogout(rsp: BasicResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.success())
            }
        }

        oneDevice.requestGoogleLogout()
    }

suspend fun OneDevice.setC4aPermissionStatusWithTimeout(logTag: String, status: EnumC4aPermissionStatus): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        setC4aPermissionStatus(logTag = logTag, status = status)
    }

private suspend fun OneDevice.setC4aPermissionStatus(logTag: String, status: EnumC4aPermissionStatus): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onC4aPermissionStatus(rsp: C4aPermissionStatusResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(status.value == rsp.status)
            }
        }

        oneDevice.setC4aPermissionStatus(status = status)
    }

suspend fun OneDevice.getAlexaLwaLoggedWithTimeout(logTag: String): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        getAlexaLwaLogged(logTag = logTag)
    }

private suspend fun OneDevice.getAlexaLwaLogged(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGetLWAState(rsp: LwaStateResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.logged)
            }
        }

        oneDevice.getLWAState()
    }

suspend fun OneDevice.getAlexaCblLoggedWithTimeout(logTag: String): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        getAlexaCblLogged(logTag = logTag)
    }

private suspend fun OneDevice.getAlexaCblLogged(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onAlexaCBLStatus(rsp: AlexaCBLStatusResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.logged)
            }
        }

        oneDevice.alexaCBLStatus()
    }

suspend fun OneDevice.alexaLwaLogoutWithTimeout(logTag: String): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        alexaLwaLogout(logTag = logTag)
    }

private suspend fun OneDevice.alexaLwaLogout(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onRequestLWALogout(rsp: BasicResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.success())
            }
        }

        oneDevice.requestLWALogout()
    }

suspend fun OneDevice.alexaCblLogoutWithTimeout(logTag: String): Boolean? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        alexaCblLogout(logTag = logTag)
    }

private suspend fun OneDevice.alexaCblLogout(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this
        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onAlexaCBLLogout(rsp: BasicResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.success())
            }
        }

        oneDevice.alexaCBLLogout()
    }

suspend fun OneDevice.syncGetDeviceInfoWithTimeout(logTag: String): DeviceInfo? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetDeviceInfo(logTag = logTag)
    }

private suspend fun OneDevice.syncGetDeviceInfo(logTag: String): DeviceInfo? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<DeviceInfo?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onDeviceInfo(rsp: GetDeviceInfoResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.deviceInfo)
            }
        }

        oneDevice.getDeviceInfo()
    }

suspend fun OneDevice.syncGetDeviceNameWithTimeout(logTag: String): DeviceNameRspUnion? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetDeviceName(logTag = logTag)
    }

private suspend fun OneDevice.syncGetDeviceName(logTag: String): DeviceNameRspUnion? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<DeviceNameRspUnion?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onDeviceName(union: DeviceNameRspUnion) {
                unregisterDeviceListener(this)
                continuation.safeResume(union)
            }
        }

        oneDevice.getDeviceName()
    }

suspend fun OneDevice.syncGetGroupInfoWithTimeout(logTag: String, repeatTimes: Int, timeoutMills: Long): GetGroupInfoRsp? =
    Tools.repeatWithTimeout(repeatTimes = repeatTimes, timeoutMills = timeoutMills) {
        syncGetGroupInfo(logTag = logTag)
    }

private suspend fun OneDevice.syncGetGroupInfo(logTag: String): GetGroupInfoRsp? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<GetGroupInfoRsp?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGetGroupInfo(rsp: GetGroupInfoRsp) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }
        oneDevice.getGroupInfo()
    }

suspend fun OneDevice.syncGetGroupParameterWithTimeout(logTag: String, repeatTimes: Int, timeoutMills: Long): GetGroupParameterRsp? =
    Tools.repeatWithTimeout(repeatTimes = repeatTimes, timeoutMills = timeoutMills) {
        syncGetGroupParameter(logTag = logTag)
    }

private suspend fun OneDevice.syncGetGroupParameter(logTag: String): GetGroupParameterRsp? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<GetGroupParameterRsp?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGetGroupParameter(rsp: GetGroupParameterRsp) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }
        oneDevice.getGroupParameter()
    }

suspend fun OneDevice.syncGetGroupCalibrationStateWithTimeout(logTag: String, repeatTimes: Int, timeoutMills: Long): GetGroupCalibrationStateRsp? =
    Tools.repeatWithTimeout(repeatTimes = repeatTimes, timeoutMills = timeoutMills) {
        syncGetGroupCalibrationState(logTag = logTag)
    }

private suspend fun OneDevice.syncGetGroupCalibrationState(logTag: String): GetGroupCalibrationStateRsp? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<GetGroupCalibrationStateRsp?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGetGroupCalibrationState(rsp: GetGroupCalibrationStateRsp) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }
        oneDevice.getGroupCalibrationState()
    }

suspend fun OneDevice.syncSetRemoteDeviceNameWithTimeout(name: String, maxByteLength: Int, logTag: String): DeviceNameRspUnion? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncSetRemoteDeviceName(name = name, maxByteLength = maxByteLength, logTag = logTag)
    }

private suspend fun OneDevice.syncSetRemoteDeviceName(
    name: String, maxByteLength: Int, logTag: String
): DeviceNameRspUnion? =
    suspendCancellableCoroutine { continuation ->
        val truncateName = name.truncateSafety(maxByteLength)
        val oneDevice = this

        object : OneContinuationDeviceListener<DeviceNameRspUnion?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onDeviceName(union: DeviceNameRspUnion) {
                unregisterDeviceListener(this)
                continuation.safeResume(union)
            }
        }

        oneDevice.sendSetDeviceName(name = truncateName)
    }

suspend fun OneDevice.syncRestoreFactoryWithTimeout(logTag: String): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncRestoreFactory(logTag = logTag)
    } ?: false

private suspend fun OneDevice.syncRestoreFactory(logTag: String): Boolean? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<Boolean?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onRestoreFactory(success: Boolean) {
                unregisterDeviceListener(this)
                continuation.safeResume(success)
            }
        }

        oneDevice.setFactoryRestore()
    }

suspend fun OneDevice.syncGetAutoPowerOffTimerWithTimeout(logTag: String, port: String?): Int? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetAutoPowerOffTimer(logTag = logTag, port = port)
    }

private suspend fun OneDevice.syncGetAutoPowerOffTimer(logTag: String, port: String?): Int? =
    suspendCancellableCoroutine { continuation ->
        val oneDevice = this

        object : OneContinuationDeviceListener<Int?>(
            logTag = logTag,
            oneDevice = oneDevice,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onAutoPowerOffTimer(secs: Int) {
                unregisterDeviceListener(this)
                continuation.safeResume(secs)
            }
        }

        oneDevice.getAutoPowerOffTimer(port)
    }

suspend fun OneDevice.syncGetBluetoothConfigWithTimeout(logTag: String): EnumBluetoothConfig? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetBluetoothConfig(logTag = logTag)
    }

private suspend fun OneDevice.syncGetBluetoothConfig(logTag: String): EnumBluetoothConfig? =
    suspendCancellableCoroutine { continuation ->
        val device = this

        object : OneContinuationDeviceListener<EnumBluetoothConfig?>(
            logTag = logTag,
            oneDevice = device,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onBluetoothConfig(config: EnumBluetoothConfig) {
                unregisterDeviceListener(this)
                continuation.safeResume(config)
            }
        }

        device.getBluetoothConfig()
    }

suspend fun OneDevice.syncGetFeedbackToneConfigWithTimeout(logTag: String, port: String?): EnumFeedbackToneConfig? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetFeedbackToneConfig(logTag = logTag, port = port)
    }

private suspend fun OneDevice.syncGetFeedbackToneConfig(logTag: String, port: String?): EnumFeedbackToneConfig? =
    suspendCancellableCoroutine { continuation ->
        val device = this

        object : OneContinuationDeviceListener<EnumFeedbackToneConfig?>(
            logTag = logTag,
            oneDevice = device,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onFeedbackToneConfig(config: EnumFeedbackToneConfig) {
                unregisterDeviceListener(this)
                continuation.safeResume(config)
            }
        }

        device.getFeedbackToneConfig(port)
    }

suspend fun OneDevice.syncGetGeneralConfig(type: GeneralConfigType, logTag: String = "syncGetGeneralConfig"): ValueResponse? {
    return Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { completer ->
            val oneDevice = this
            object : OneContinuationDeviceListener<ValueResponse?>(
                logTag = logTag,
                oneDevice = oneDevice,
                continuation = completer,
                errorResult = null
            ) {
                override fun onGeneralConfig(value: ValueResponse) {
                    unregisterDeviceListener(this)
                    completer.safeResume(value)
                }
            }
            oneDevice.getGeneralConfig(type)
        }
    }
}

suspend fun OneDevice.syncSetGeneralConfig(config: GeneralConfig, logTag: String = "syncSetGeneralConfig"): Boolean {
    return Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { completer ->
            val oneDevice = this
            object : OneContinuationDeviceListener<Boolean>(
                logTag = logTag,
                oneDevice = oneDevice,
                continuation = completer,
                errorResult = false
            ) {
                override fun onSetGeneralConfig(success: Boolean) {
                    unregisterDeviceListener(this)
                    completer.resume(success)
                }
            }
            oneDevice.setGeneralConfig(config)
        }
    } ?: false
}

suspend fun OneDevice.syncGetDJPad(logTag: String = "syncGetDJPad"): DJPadResponse? {
    return Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { completer ->
            val oneDevice = this
            object : OneContinuationDeviceListener<DJPadResponse?>(
                logTag = logTag,
                oneDevice = oneDevice,
                continuation = completer,
                errorResult = null
            ) {
                override fun onDJPad(value: DJPadResponse) {
                    unregisterDeviceListener(this)
                    completer.resume(value)
                }
            }
            oneDevice.getDJPad()
        }
    }
}

suspend fun OneDevice.syncGetPersonalListeningMode(logTag: String = "syncGetPersonalListeningMode",port: String? = null): Boolean? {
    return Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { completer ->
            val oneDevice = this
            object : OneContinuationDeviceListener<Boolean?>(
                logTag = logTag,
                oneDevice = oneDevice,
                continuation = completer,
                errorResult = null
            ) {

                override fun onGetPersonalListeningMode(isOn: Boolean) {
                    unregisterDeviceListener(this)
                    completer.resume(isOn)
                }
            }
            oneDevice.getPersonalListeningMode(port)
        }
    }
}

suspend fun OneDevice.syncGetLightColorPicker(logTag: String = "syncGetLightColorPicker"): ColorPicker? {
    return Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        suspendCancellableCoroutine { completer ->
            val oneDevice = this
            object : OneContinuationDeviceListener<ColorPicker?>(
                logTag = logTag,
                oneDevice = oneDevice,
                continuation = completer,
                errorResult = null
            ) {
                override fun onGetColorPicker(colorPickerResp: ColorPickerResp) {
                    unregisterDeviceListener(this)
                    completer.resume(if (colorPickerResp.isSuccess()) colorPickerResp.convert2ColorPicker() else null)
                }
            }
            oneDevice.getColorPicker()
        }
    }
}

suspend fun OneDevice.syncControlSoundscapeV2WithTimeout(logTag: String, req: ControlSoundscapeV2): Boolean =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncControlSoundscapeV2(logTag = logTag, req = req)
    } ?: false

private suspend fun OneDevice.syncControlSoundscapeV2(logTag: String, req: ControlSoundscapeV2): Boolean =
    suspendCancellableCoroutine { continuation ->
        val device = this

        object : OneContinuationDeviceListener<Boolean>(
            logTag = logTag,
            oneDevice = device,
            continuation = continuation,
            errorResult = false
        ) {
            override fun onControlSoundscapeV2(rsp: BasicResponse) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp.success())
            }
        }

        device.controlSoundscapeV2(req = req)
    }

suspend fun OneDevice.syncGetGroupDevicesOtaStatusWithTimeout(logTag: String): GroupDeviceOTAStatusRsp? =
    Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
        syncGetGroupDevicesOtaStatus(logTag = logTag)
    }

private suspend fun OneDevice.syncGetGroupDevicesOtaStatus(logTag: String): GroupDeviceOTAStatusRsp? =
    suspendCancellableCoroutine { continuation ->
        val device = this

        object : OneContinuationDeviceListener<GroupDeviceOTAStatusRsp?>(
            logTag = logTag,
            oneDevice = device,
            continuation = continuation,
            errorResult = null
        ) {
            override fun onGroupDeviceOTAStatus(rsp: GroupDeviceOTAStatusRsp) {
                unregisterDeviceListener(this)
                continuation.safeResume(rsp)
            }
        }

        device.getGroupDevicesOtaStatus()
    }

/**
 * Generic synchronization requests
 */
suspend inline fun <reified Resp> OneDevice.syncCmd(
    reqCmd: EnumCommandMapping,
    params: Map<String, Any>? = null,
    paramsJsonString: String? = null,
    port: String? = null
): Resp? {
    if (!TextUtils.isEmpty(reqCmd.wifiCmd) && isWiFiOnline) {
        //1.gen and register observer
        val completer = CompletableDeferred<Resp?>()
        val obsr = object : IOneDeviceListener {
            override fun onRspSuccess(cmd: String?, content: String?, port: String?) {
                if (cmd == reqCmd.wifiCmd) {
                    unregisterDeviceListener(this)
                    try {
                        val ret = GsonUtil.parseJsonToBean(content!!, Resp::class.java)
                        completer.complete(ret)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        completer.complete(null)
                    }
                }
            }
        }
        registerDeviceListener(obsr)
        //2.send request
        wifiSession?.request(port = port, wifiCommand = reqCmd.wifiCmd, params = params?.let { GsonUtil.parseBeanToJson(it) } ?: paramsJsonString)
        //3.wait response
        val ret = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
            completer.await()
        }
        unregisterDeviceListener(obsr)
        return ret
    }
    if (reqCmd.bleCmd != -1 && isGattConnected) {
        //1.gen Gatt command
        val oneGattCmd = object : OneGattCommand() {
            override var commandID: Int
                get() = reqCmd.bleCmd
                set(value) {}
            override val payload: ByteArray?
                get() = (params?.let { GsonUtil.parseBeanToJson(it) } ?: paramsJsonString)?.toByteArray()
        }
        //2.gen and register observer
        val completer = CompletableDeferred<Resp?>()
        val obsr = object : IOneDeviceListener {
            override fun onResponseReceived(response: BLEResponse) {
                if (response.responsedCode == reqCmd.bleCmd) {
                    unregisterDeviceListener(this)
                    try {
                        val ret = GsonUtil.parseJsonToBean(response.responseData, Resp::class.java)
                        completer.complete(ret)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        completer.complete(null)
                    }
                }
            }
        }
        registerDeviceListener(obsr)
        //3.send request
        CoroutineScope(coroutineContext).launch {
            withContext(DISPATCHER_IO) {
                (gattSession as? OneGattSession)?.sendCommand(oneGattCmd)
            }
        }
        //4.wait response
        val ret = Tools.repeatWithTimeout(repeatTimes = 0, timeoutMills = GENERAL_TIMEOUT_MILLS) {
            completer.await()
        }
        unregisterDeviceListener(obsr)
        return ret
    }
    Logger.e(_TAG, "device not on wife and no gatt $this")
    return null
}

suspend fun OneDevice.synGetBatterySavingStatus(port: String? = null): EnumBatterySavingStatus? {
    return syncCmd<EnumBatterySavingStatus>(
        reqCmd = EnumCommandMapping.GET_BATTERY_SAVING_MODE,
        params = null,
        port = port
    )
}

/**
 * @param onPairing in pairing status, speaker is playing tone and app need to guide user to tap Auth button on speaker device.
 * @param onDisconnected BLE disconnected.
 * @param onPairFailed user reject pairing or pairing request timeout.
 * @param onConnected pair succeed and secure BLE connection has been created.
 *
 * @param onConnectResultReport
 * @param onStartAuthReport
 * @param onAuthResultReport
 */
suspend fun OneDevice.secureBleGattConnect(context: Context, logTag: String,
                                           @MainThread
                                           onPairing: (() -> Unit)? = null,
                                           @MainThread
                                           onDisconnected: (() -> Unit)? = null,
                                           @MainThread
                                           onPairFailed: (() -> Unit)? = null,
                                           @MainThread
                                           onConnected: (() -> Unit)? = null,
                                           iConnectReporter: IConnectReport?) {

    Logger.d(logTag, "secureBleGattConnect() >>> isGattConnected: $isGattConnected, $bleAddress")
    var needReportConnect = false

    val result = if (isGattConnected) {
        EnumConnectionStatus.CONNECTED
    } else {
        needReportConnect = true
        syncSecureBleGattConnectWithTimeout(context = context, repeatTimes = 2, logTag = logTag)
    }

    Logger.d(logTag, "secureBleGattConnect() >>> connect result: $result, $bleAddress")

    val isPaired = AtomicBoolean(false)

    val authStartTs = System.currentTimeMillis()

    if (result.isPrePair()) {
        if (needReportConnect) {
            iConnectReporter?.onConnectResultReport(true)
        }

        iConnectReporter?.onStartAuthReport()

        val flow = MutableStateFlow(EnumConnectionStatus.NONE)
        CoroutineScope(DISPATCHER_FAST_MAIN).launch {
            flow.collect { status ->
                when (status) {
                    EnumConnectionStatus.PAIRING -> {
                        onPairing?.invoke()
                    }
                    EnumConnectionStatus.DISCONNECTED -> {
                        iConnectReporter?.onAuthResultReport(false, authStartTs)
                        onDisconnected?.invoke()
                    }
                    EnumConnectionStatus.CONNECTED -> {
                        isPaired.set(true)
                        iConnectReporter?.onAuthResultReport(true, authStartTs)
                        Logger.d(logTag, "secureBleGattConnect() >>> pairStatus: $status, $bleAddress")
                        onConnected?.invoke()
                    }
                    else -> Unit
                }
            }
        }

        val pairStatus = syncGattPairResultWithTimeout(logTag = logTag, flow = flow)
        if (!isPaired.get()) {
            // this branch won't be run as designed.
            Logger.d(logTag, "secureBleGattConnect() >>> pairStatus: $pairStatus, $bleAddress")
            if (pairStatus.isConnected()) {
                iConnectReporter?.onAuthResultReport(true, authStartTs)
                CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                    onConnected?.invoke()
                }
            } else {
                // Timeout
                iConnectReporter?.onAuthResultReport(false, authStartTs)
                CoroutineScope(DISPATCHER_FAST_MAIN).launch {
                    onPairFailed?.invoke()
                }
            }
        } else {
            Logger.d(logTag, "secureBleGattConnect() >>> get pairStatus after Paired, ignore this status: $pairStatus, $bleAddress")
        }
    } else if (result.isConnected()) {
        if (needReportConnect) {
            iConnectReporter?.onConnectResultReport(true)
        }

        CoroutineScope(DISPATCHER_FAST_MAIN).launch {
            onConnected?.invoke()
        }
    } else if (result.isDisconnected()) {
        iConnectReporter?.onConnectResultReport(false)
        CoroutineScope(DISPATCHER_FAST_MAIN).launch {
            onDisconnected?.invoke()
        }
    }
}


private open class OneContinuationDeviceListener<Result>(
    private val logTag: String,
    private val oneDevice: OneDevice,
    private val continuation: CancellableContinuation<Result>,
    private val errorResult: Result
) : IOneDeviceListener {

    init {
        register()
    }

    @CallSuper
    override fun onRspFailure(cmd: String?, e: Exception, port: String?) {
        Logger.e(logTag, "onRspFailure() >>> [${oneDevice.UUID}] $e")
        oneDevice.unregisterDeviceListener(this)
        continuation.safeResume(errorResult)
    }

    @CallSuper
    override fun onGattException(e: Exception, session: BaseGattSession<*, *, *>) {
        Logger.e(logTag, "onGattException() >>> [${oneDevice.UUID}] $e", e)
        oneDevice.unregisterDeviceListener(this)
        continuation.safeResume(errorResult)
    }

    private fun register() {
        continuation.invokeOnCancellation {
            oneDevice.unregisterDeviceListener(this)
        }

        oneDevice.registerDeviceListener(this)
    }
}