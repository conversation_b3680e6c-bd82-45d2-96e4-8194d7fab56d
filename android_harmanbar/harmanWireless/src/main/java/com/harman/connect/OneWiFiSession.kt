package com.harman.connect

import androidx.annotation.IntRange
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.harman.command.HttpResponseCastException
import com.harman.command.one.AttachOneCommand
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.bean.APListResponse
import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.CalibrationState
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.ColorPickerResp
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DJPad
import com.harman.command.one.bean.DJPadResponse
import com.harman.command.one.bean.DebugMode
import com.harman.command.one.bean.DeviceNameRspUnion
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.EnumVoiceSource
import com.harman.command.one.bean.FeatureSupportResponse
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.GetAutoPowerOffTimerRsp
import com.harman.command.one.bean.GetBatterySavingStatusRsp
import com.harman.command.one.bean.GetBluetoothConfigRsp
import com.harman.command.one.bean.GetChromeCastOptInResponse
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.GetDeviceNameResponse
import com.harman.command.one.bean.GetFeedbackToneRsp
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetLightInfoResponse
import com.harman.command.one.bean.GetOtaAccessPointResponse
import com.harman.command.one.bean.GetOtaStatusResponse
import com.harman.command.one.bean.GetProductUsageResponse
import com.harman.command.one.bean.GetSleepTimerRsp
import com.harman.command.one.bean.GetSmartBtnConfigRsp
import com.harman.command.one.bean.GetSmartModeRsp
import com.harman.command.one.bean.GetSoundscapeV2ConfigResponse
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GetStreamingStatusRsp
import com.harman.command.one.bean.GetSupportedVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceToneResponse
import com.harman.command.one.bean.GroupDeviceOTAStatusRsp
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.MediaResponse
import com.harman.command.one.bean.PreviewSoundscapeRequest
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.RearSpeakerResp
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SendAppControllerRequest
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SetVoiceLanguageRequest
import com.harman.command.one.bean.SetVoiceToneRequest
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.StatusReq
import com.harman.command.one.bean.StatusResp
import com.harman.command.one.bean.TriggerDiagnosisRequest
import com.harman.command.one.bean.ValueResponse
import com.harman.command.one.bean.statusOff
import com.harman.command.one.bean.statusOn
import com.harman.command.one.command.EnumMuteStatus
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.session.OneBusiness
import com.harman.discover.bean.WiFiDevice
import com.harman.discover.bean.bt.WiFiListenerProxy
import com.harman.discover.util.Tools.toJSONObj
import com.harman.log.Logger
import com.harman.thread.DISPATCHER_API
import com.harmanbar.ble.utils.GsonUtil
import com.wifiaudio.model.DeviceItem
import com.wifiaudio.model.DlnaPlayerStatus
import com.wifiaudio.service.DlnaServiceProvider
import com.wifiaudio.service.DlnaServiceProviderPool
import com.wifiaudio.utils.okhttp.EncryptedOkHttp
import com.wifiaudio.utils.okhttp.HttpRequestUtils
import com.wifiaudio.utils.okhttp.IOkHttpRequestCallback
import com.wifiaudio.utils.okhttp.OkHttpResponseItem
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.AbstractQueue
import java.util.Locale

/**
 * Created by gerrardzhang on 2024/3/5.
 *
 * Represent a session of Http connection. Spec to JBL One Platform.
 * Key: [DeviceItem.IP]
 *
 * Some of the control events may processed through UPNP like [playMusic].
 */
class OneWiFiSession(
    // Keep ipAddress as const value cause device may change this once WiFi devices been discovered.
    val ipAddress: String,
    private val device: WiFiDevice,
    // listeners which store in WiFiDevice
    private val listenerProxy: WiFiListenerProxy,
) : OneBusiness {

    private val encryptOkHttp = EncryptedOkHttp.getInstance()

    private val deviceBindReqUtils: HttpRequestUtils
        get() = HttpRequestUtils.getRequestUtils(device.deviceItem)

    private val deviceBindHttpsPrefix: String
        get() = HttpRequestUtils.getRequestPrefix(device.deviceItem)

    /**
     * GET "getDeviceInfo"
     */
    override fun getDeviceInfo(port: String?) {
        encryptGet(command = EnumCommandMapping.GET_DEVICE_INFO.wifiCmd, port = port)
    }

    /**
     * POST "setDeviceName"
     */
    override fun sendSetDeviceName(name: String, port: String?) {
        val payload = EnumCommandMapping.SET_DEVICE_NAME.payloadFormat.format(name)
        encryptPost(command = EnumCommandMapping.SET_DEVICE_NAME.wifiCmd, port = port, payload = payload, passback = name)
    }

    /**
     * GET "getApList"
     */
    override fun getAPList(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.LP_GET_AP_LIST_CMD.wifiCmd)
    }

    /**
     * POST "setCastGroup"
     */
    override fun setCastGroup(payloadJson: String, port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.LP_SET_CAST_GROUP.wifiCmd, payload = payloadJson)
    }

    /**
     * UPNP
     */
    override fun playMusic() {
        Logger.i(TAG, "playMusic() >>> ")
        device.getDLNAProvider()?.makeDlnaPlay()
        device.deviceItem.devInfoExt?.setDlnaPlayStatusByLocal(DlnaPlayerStatus.IPlayStatus.Playing)
        listenerProxy.getter().forEach { listener ->
            listener.onPlayStatus(isPlay = true)
        }
    }

    /**
     * UPNP
     */
    override fun pauseMusic() {
        Logger.i(TAG, "pauseMusic() >>> ")
        device.getDLNAProvider()?.makeDlnaPause()
        device.deviceItem.devInfoExt?.setDlnaPlayStatusByLocal(DlnaPlayerStatus.IPlayStatus.Paused_playback)
        listenerProxy.getter().forEach { listener ->
            listener.onPlayStatus(isPlay = false)
        }
    }

    /**
     * UPNP
     */
    override fun prevMusic() {
        Logger.i(TAG, "prevMusic() >>> ")
        device.getDLNAProvider()?.makeDlnaPrevious()
    }

    /**
     * UPNP
     */
    override fun nextMusic() {
        Logger.i(TAG, "nextMusic() >>> ")
        device.getDLNAProvider()?.makeDlnaNext()
    }

    /**
     * UPNP
     */
    override fun setRemoteVolume(@IntRange(from = 0L, to = 100L) value: Int) {
        Logger.d(TAG, "setRemoteVolume() >>> [$value]")
        device.getDLNAProvider()?.makeDlnaSetVolume(value)

        // Update local volume param
        device.deviceItem.devInfoExt?.let { ext ->
            ext.dlnaDesireMute = EnumMuteStatus.UN_MUTE.value
            ext.setDlnaCurrentVolumeByLocal(value)
            ext.mVolumeDelayedTimer?.updateStartTime()
        }
    }

    /**
     * UPNP
     */
    override fun getRemoteVolume() {
        device.getDLNAProvider()?.makeDlnaGetVolume()
    }

    /**
     * UPNP
     */
    fun seek(secs: Long) {
        val provider = device.getDLNAProvider() ?: run {
            Logger.e(TAG, "seek() >>> missing DLNA provider of [${device.UUID}]")
            return
        }

        provider.makeDlnaSeek(secs.toInt())
        provider.ignoreRefreshPlayStatus()
        provider.ignoreRefreshGetPosition()

        device.deviceItem.devInfoExt?.let { infoExt ->
            infoExt.setDlnaTickTimeByLocal(secs)
            infoExt.mProgressAutoDelayedTimer.updateStartTime()
            infoExt.mProgressAutoDelayedTimer.setRefreshTime(false)
            infoExt.mProgressDelayedTimer.setRefreshTime(false)
        }

        Logger.d(TAG, "seek() >>> done. UUID[${device.UUID}] secs[$secs]")
    }

    /**
     * GET "getFeatureSupport"
     */
    override fun getFeatureSupport(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_FEATURE_SUPPORT.wifiCmd)
    }

    /**
     * GET "getEQList"
     */
    override fun getEQList(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_EQ_LIST.wifiCmd)
    }

    /**
     * POST "setActiveEQ"
     */
    override fun setActiveEQ(item: SetActiveEQItemRequest, port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_ACTIVE_EQ.wifiCmd,
            payload = GsonUtil.parseBeanToJson(item)
        )
    }

    /**
     * GET "getEQ"
     */
    override fun getEQ(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_EQ.wifiCmd)
    }

    /**
     * POST "setEQ"
     */
    override fun setEQ(eq: EQSetting, port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_EQ.wifiCmd,
            payload = GsonUtil.parseBeanToJson(eq)
        )
    }

    /**
     * POST "setAuthStart"
     */
    override fun startAuth(timeoutSecs: Int, port: String?) {
        val payload = EnumCommandMapping.AUTH_START.payloadFormat.format(timeoutSecs)
        encryptPost(port = port, command = EnumCommandMapping.AUTH_START.wifiCmd, payload = payload)
    }

    /**
     * GET "setAuthCancel"
     */
    override fun cancelAuth(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.AUTH_CANCEL.wifiCmd)
    }

    /**
     * POST "setWifiNetwork"
     */
    override fun setWifiNetwork(req: SetWifiNetworkRequest, port: String?) {
        val payload = GsonUtil.parseBeanToJson(req)
        encryptPost(port = port, command = EnumCommandMapping.SET_WIFI_NETWORK.wifiCmd, payload = payload)
    }

    /**
     * GET "getOtaStatus"
     */
    override fun getOtaStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_OTA_STATUS.wifiCmd)
    }

    /**
     * GET "requestDeviceOta"
     */
    override fun requestDeviceOta(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.REQ_DEV_OTA.wifiCmd)
    }

    /**
     * GET "getOTAAccessPoint"
     */
    override fun getOtaAccessPoint(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_OTA_ACCESS_POINT.wifiCmd)
    }

    /**
     * GET "getBatteryStatus"
     */
    override fun getBatteryStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_BATTERY_STATUS.wifiCmd)
    }

    /**
     * POST "setCalibration"
     */
    override fun setCalibration(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SET_CALIBRATION.wifiCmd, payload = null)
    }

    /**
     * GET "getCalibrationState"
     */
    override fun getCalibrationState(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_CALIBRATION_STATE.wifiCmd)
    }

    /**
     * POST "cancelCalibration"
     */
    override fun cancelCalibration(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.CANCEL_CALIBRATION.wifiCmd, payload = null)
    }

    /**
     * GET "getC4aPermissionStatus"
     */
    override fun getC4aPermissionStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_C4A_PERMISSION_STATUS.wifiCmd)
    }

    /**
     * POST setC4aPermissionStatus
     */
    override fun setC4aPermissionStatus(status: EnumC4aPermissionStatus, port: String?) {
        // update local cache directly to avoid status didn't sync from device side.
        device.onC4APermissionStatue(rsp = C4aPermissionStatusResponse(errorCode = null, status = status.value))

        val payload = EnumCommandMapping.SET_C4A_PERMISSION_STATUS.payloadFormat.format(status.value)
        encryptPost(port = port, command = EnumCommandMapping.SET_C4A_PERMISSION_STATUS.wifiCmd, payload = payload)
    }

    /**
     * GET getChromecastOptIn
     */
    override fun getChromeCastOptIn(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_CHROMECAST_OPT_IN.wifiCmd)
    }

    /**
     * POST setChromecastOptIn
     */
    override fun setChromeCastOptIn(optIn: ChromeCastOpt, port: String?) {
        val payload = EnumCommandMapping.SET_CHROMECAST_OPT_IN.payloadFormat.format(optIn.value)
        encryptPost(port = port, command = EnumCommandMapping.SET_CHROMECAST_OPT_IN.wifiCmd, payload = payload, passback = optIn)
    }

    /**
     * POST setWifiCountryCode
     */
    override fun setCountryCode(code: String, port: String?) {
        val payload = EnumCommandMapping.SET_WIFI_COUNTRY_CODE.payloadFormat.format(code)
        encryptPost(port = port, command = EnumCommandMapping.SET_WIFI_COUNTRY_CODE.wifiCmd, payload = payload)
    }

    /**
     * POST enterAuracast
     */
    override fun enterAuraCast(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.ENTER_AURA_CAST.wifiCmd, payload = null)
    }

    /**
     * POST exitAuracast
     */
    override fun exitAuraCast(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.EXIT_AURA_CAST.wifiCmd, payload = null)
    }

    override fun getAuraCastSqMode(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.GET_AURA_CAST_SQ_MODE.wifiCmd, payload = null)
    }

    override fun setAuraCastSqMode(on: Boolean, port: String?) {
        val status = if (on) "on" else "off"
        val payload = EnumCommandMapping.SET_AURA_CAST_SQ_MODE.payloadFormat.format(status)
        encryptPost(port = port, command = EnumCommandMapping.SET_AURA_CAST_SQ_MODE.wifiCmd, payload = payload)
    }

    /**
     * POST playPartySound
     */
    override fun setPlayPartySound(partySound: Int, port: String?) {
        val payload = String.format("{\"index\":%d}", partySound)
        encryptPost(port = port, command = EnumCommandMapping.PLAY_PARTY_SOUND.wifiCmd, payload = payload)
    }

    /**
     * POST setDJEvent
     */
    override fun setDjEvent(value: String, port: String?) {
        val payload = String.format("{\"set\": \"%s\"}}", value)
        encryptPost(port = port, command = EnumCommandMapping.SET_DJ_EVENT.wifiCmd, payload = payload)
    }

    /**
     * POST setDebugMode
     */
    override fun setDebugMode(debugMode: DebugMode, port: String?) {
        val payload = EnumCommandMapping.SET_DEBUG_MODE.payloadFormat.format(debugMode.value)
        encryptPost(port = port, command = EnumCommandMapping.SET_DEBUG_MODE.wifiCmd, payload = payload)
    }

    /**
     * GET alexaCBLStatus
     */
    override fun alexaCBLStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.ALEXA_CBL_STATUS.wifiCmd)
    }

    /**
     * GET alexaCBLLogout
     */
    override fun alexaCBLLogout(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.ALEXA_CBL_LOGOUT.wifiCmd)
    }

    /**
     * GET getLWAState
     */
    override fun getLWAState(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_LWA_STATE.wifiCmd)
    }

    /**
     * GET requestLWALogout
     */
    override fun requestLWALogout(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.REQUEST_LWA_LOGOUT.wifiCmd)
    }

    /**
     * POST setLWAAuthCode
     */
    override fun setLWAAuthCode(lwaInfo: LWAInfo, port: String?) {
        val payload = EnumCommandMapping.SET_LWA_AUTH_CODE.payloadFormat.format(
            lwaInfo.authorizationCode,
            lwaInfo.clientId,
            lwaInfo.redirectUri
        )
        encryptPost(port = port, command = EnumCommandMapping.SET_LWA_AUTH_CODE.wifiCmd, payload = payload)
    }

    /**
     * POST setVoiceLanguage
     */
    override fun setVoiceLanguage(setVoiceLanguageRequest: SetVoiceLanguageRequest, port: String?) {
        val payload = EnumCommandMapping.SET_VOICE_LANGUAGE.payloadFormat.format(
            setVoiceLanguageRequest.source,
            setVoiceLanguageRequest.local
        )
        encryptPost(port = port, command = EnumCommandMapping.SET_VOICE_LANGUAGE.wifiCmd, payload = payload)
    }

    /**
     * POST setVoiceRequestEndTone
     */
    override fun setVoiceRequestEndTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        val payload = EnumCommandMapping.SET_VOICE_REQUEST_END_TONE.payloadFormat.format(
            setVoiceToneRequest.source,
            setVoiceToneRequest.status
        )
        encryptPost(port = port, command = EnumCommandMapping.SET_VOICE_REQUEST_END_TONE.wifiCmd, payload = payload)
    }

    /**
     * POST setVoiceRequestStartTone
     */
    override fun setVoiceRequestStartTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        val payload = EnumCommandMapping.SET_VOICE_REQUEST_START_TONE.payloadFormat.format(
            setVoiceToneRequest.source,
            setVoiceToneRequest.status
        )
        encryptPost(port = port, command = EnumCommandMapping.SET_VOICE_REQUEST_START_TONE.wifiCmd, payload = payload)
    }

    /**
     * POST getVoiceLanguage
     */
    override fun getVoiceLanguage(source: EnumVoiceSource, port: String?) {
        val payload = EnumCommandMapping.GET_VOICE_LANGUAGE.payloadFormat.format(
            source.value
        )
        encryptPost(port = port, command = EnumCommandMapping.GET_VOICE_LANGUAGE.wifiCmd, payload = payload)
    }

    /**
     * POST getSupportedVoiceLanguage
     */
    override fun getSupportedVoiceLanguage(source: EnumVoiceSource, port: String?) {
        val payload = EnumCommandMapping.GET_SUPPORTED_VOICE_LANGUAGE.payloadFormat.format(
            source.value
        )
        encryptPost(port = port, command = EnumCommandMapping.GET_SUPPORTED_VOICE_LANGUAGE.wifiCmd, payload = payload)
    }

    /**
     * GET requestGoogleLogout
     */
    override fun requestGoogleLogout(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.REQUEST_GOOGLE_LOGOUT.wifiCmd)
    }

    /**
     * POST getVoiceRequestEndTone
     */
    override fun getVoiceRequestEndTone(source: EnumVoiceSource, port: String?) {
        val payload = EnumCommandMapping.GET_VOICE_REQUEST_END_TONE.payloadFormat.format(
            source.value
        )
        encryptPost(port = port, command = EnumCommandMapping.GET_VOICE_REQUEST_END_TONE.wifiCmd, payload = payload)
    }

    /**
     * POST getVoiceRequestStartTone
     */
    override fun getVoiceRequestStartTone(source: EnumVoiceSource, port: String?) {
        val payload = EnumCommandMapping.GET_VOICE_REQUEST_START_TONE.payloadFormat.format(source.value)
        encryptPost(port = port, command = EnumCommandMapping.GET_VOICE_REQUEST_START_TONE.wifiCmd, payload = payload)
    }

    /**
     * GET getSmartButtonConfig
     */
    override fun getSmartBtnConfig(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_SMART_BTN_CONFIG.wifiCmd)
    }

    /**
     * POST setSmartButtonConfig
     */
    override fun setSmartBtnConfig(config: SmartBtnConfig, port: String?) {
        val payload = GsonUtil.parseBeanToJson(config)
        encryptPost(port = port, command = EnumCommandMapping.SET_SMART_BTN_CONFIG.wifiCmd, payload = payload, passback = config)
    }

    /**
     * POST previewSoundscape
     */
    override fun previewSoundscape(request: PreviewSoundscapeRequest, port: String?) {
        val payload = GsonUtil.parseBeanToJson(request)
        encryptPost(port = port, command = EnumCommandMapping.PREVIEW_SOUNDSCAPE.wifiCmd, payload = payload)
    }

    /**
     * POST cancelSoundscape
     */
    override fun cancelSoundscape(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.CANCEL_SOUNDSCAPE.wifiCmd, payload = null)
    }

    /**
     * GET getSoundscapeV2Config
     */
    override fun getSoundscapeV2Config(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_SOUNDSCAPE_V2_CONFIG.wifiCmd)
    }

    /**
     * POST setSoundscapeV2Config
     */
    override fun setSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest, port: String?) {
        val payload = GsonUtil.parseBeanToJson(req)
        encryptPost(port = port, command = EnumCommandMapping.SET_SOUNDSCAPE_V2_CONFIG.wifiCmd, payload = payload, passback = req)
    }

    /**
     * POST controlSoundscapeV2
     */
    override fun controlSoundscapeV2(req: ControlSoundscapeV2, port: String?) {
        val payload = GsonUtil.parseBeanToJson(req)
        encryptPost(port = port, command = EnumCommandMapping.CONTROL_SOUNDSCAPE_V2.wifiCmd, payload = payload, passback = req)
    }

    /**
     * GET getSoundscapeV2State
     */
    override fun getSoundscapeV2State(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_SOUNDSCAPE_V2_STATE.wifiCmd)
    }

    override fun getProductUsage(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_DEVICE_USAGE.wifiCmd)
    }

    /**
     * GET getDeviceName
     */
    override fun getDeviceName(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_DEVICE_NAME.wifiCmd)
    }

    /**
     * POST "playDemoSound"
     */
    override fun playDemoSound(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.PLAY_DEMO_SOUND.wifiCmd, payload = null)
    }

    /**
     * POST "cancelDemoSound"
     */
    override fun cancelDemoSound(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.CANCEL_DEMO_SOUND.wifiCmd, payload = null)
    }

    /**
     * GET "getRearSpeakerVolume"
     */
    override fun getRearSpeakerVolume(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_REAR_SPEAKER_VOLUME.wifiCmd)
    }

    /**
     * POST "sendAppController"
     */
    override fun sendAppController(req: SendAppControllerRequest, port: String?) {
        val payload = GsonUtil.parseBeanToJson(req)
        encryptPost(port = port, command = EnumCommandMapping.SEND_APP_CONTROLLER.wifiCmd, payload = payload)
    }

    override fun triggerDiagnosis(req: TriggerDiagnosisRequest, port: String?) {
        val payload = GsonUtil.parseBeanToJson(req)
        encryptPost(port = port, command = EnumCommandMapping.TRIGGER_DIAGNOSIS.wifiCmd, payload = payload)
    }

    /**
     * GET "setFactoryRestore"
     */
    override fun setFactoryRestore(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.SET_FACTORY_RESTORE.wifiCmd)
    }

    /**
     * POST "setAutoPowerOffTimer"
     */
    override fun setAutoPowerOffTimer(secs: Int, port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_AUTO_POWER_OFF_TIMER.wifiCmd,
            payload = EnumCommandMapping.SET_AUTO_POWER_OFF_TIMER.payloadFormat
                .format(Locale.US, secs.toString()),
            passback = secs
        )
    }

    /**
     * POST "getAutoPowerOffTimer"
     */
    override fun getAutoPowerOffTimer(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.GET_AUTO_POWER_OFF_TIMER.wifiCmd, payload = null)
    }

    /**
     * POST "setBluetoothConfig"
     */
    override fun setBluetoothConfig(config: EnumBluetoothConfig, port: String?) {
        val payload = EnumCommandMapping.SET_BLUETOOTH_CONFIG.payloadFormat.format(config.value)
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_BLUETOOTH_CONFIG.wifiCmd,
            payload = payload,
            passback = config
        )
    }

    /**
     * GET "getBluetoothConfig"
     */
    override fun getBluetoothConfig(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_BLUETOOTH_CONFIG.wifiCmd)
    }

    /**
     * POST "setFeedbackToneConfig"
     */
    override fun setFeedbackToneConfig(config: EnumFeedbackToneConfig, port: String?) {
        val payload = EnumCommandMapping.SET_FEEDBACK_TONE_CONFIG.payloadFormat.format(config.value)
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_FEEDBACK_TONE_CONFIG.wifiCmd,
            payload = payload,
            passback = config
        )
    }

    /**
     * POST "getFeedbackToneConfig"
     */
    override fun getFeedbackToneConfig(port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.GET_FEEDBACK_TONE_CONFIG.wifiCmd,
            payload = null
        )
    }

    override fun getGeneralConfig(type: GeneralConfigType, port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.GET_GENERAL_CONFIG.wifiCmd,
            payload = GsonUtil.parseBeanToJson(type),
        )
    }

    override fun setGeneralConfig(req: GeneralConfig, port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_GENERAL_CONFIG.wifiCmd,
            payload = GsonUtil.parseBeanToJson(req),
        )
    }

    /**
     * POST "setBatterySavingStatus"
     */
    override fun setBatterySavingStatus(status: EnumBatterySavingStatus, port: String?) {
        val payload = EnumCommandMapping.SET_BATTERY_SAVING_MODE.payloadFormat.format(status.value)
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_BATTERY_SAVING_MODE.wifiCmd,
            payload = payload,
            passback = status
        )
    }

    /**
     * GET "getBatterySavingStatus"
     */
    override fun getBatterySavingStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_BATTERY_SAVING_MODE.wifiCmd)
    }

    /**
     * POST "setIRLearn"
     */
    override fun setIRLearn(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SET_IR_LEARN.wifiCmd, payload = null)
    }

    override fun getDJPad(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.GET_DJ_PAD.wifiCmd, payload = null)
    }

    override fun setDJPad(djPad: DJPad, port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SET_DJ_PAD.wifiCmd, payload = djPad.toJson())
    }

    /**
     * POST "triggerCastLED"
     */
    override fun triggerCastLED(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.TRIGGER_CAST_LED.wifiCmd, payload = null)
    }

    /**
     * POST "clearHistoryOneOSVersion"
     */
    override fun clearHistoryOneOSVersion(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.CLEAR_HISTORY_ONE_OS_VERSION.wifiCmd, payload = null)
    }

    /**
     * POST "setAnchorIndicate"
     */
    override fun setAnchorIndicate(timeoutSecs: Int, port: String?) {
        val payload = EnumCommandMapping.SET_ANCHOR_INDICATE.payloadFormat.format(timeoutSecs)
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_ANCHOR_INDICATE.wifiCmd,
            payload = payload
        )
    }

    /**
     * POST "setAnchorCancel"
     */
    override fun setAnchorCancel(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SET_ANCHOR_CANCEL.wifiCmd, payload = null)
    }

    /**
     * POST "destroyCastGroup"
     */
    override fun destroyCastGroup(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.DESTROY_CAST_GROUP.wifiCmd, payload = null)
    }

    /**
     * GET "getGroupInfo"
     */
    override fun getGroupInfo(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_GROUP_INFO.wifiCmd)
    }

    /**
     * POST "groupCalibration"
     */
    override fun groupCalibration(anchor: String, step: Int, port: String?) {
        val payload = EnumCommandMapping.GROUP_CALIBRATION.payloadFormat.format(anchor, step)
        encryptPost(
            port = port,
            command = EnumCommandMapping.GROUP_CALIBRATION.wifiCmd,
            payload = payload
        )
    }

    /**
     * POST "switchStereoChannel"
     */
    override fun switchStereoChannel(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SWITCH_STEREO_CHANNEL.wifiCmd, payload = null)
    }

    /**
     * POST "skipDemoSound"
     */
    override fun skipDemoSound(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SKIP_DEMO_SOUND.wifiCmd, payload = null)
    }

    /**
     * POST "getGroupParameter"
     */
    override fun getGroupParameter(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.GET_GROUP_PARAMETER.wifiCmd, payload = null)
    }

    /**
     * POST "getGroupCalibrationState"
     */
    override fun getGroupCalibrationState(port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.GET_GROUP_CALIBRATION_STATE.wifiCmd, payload = null)
    }

    /**
     * POST "renameGroup"
     */
    override fun renameGroup(name: String, port: String?) {
        val payload = EnumCommandMapping.RENAME_GROUP.payloadFormat.format(name)
        encryptPost(
            port = port,
            command = EnumCommandMapping.RENAME_GROUP.wifiCmd,
            payload = payload
        )
    }

    /**
     * GET "getStreamingStatus"
     */
    override fun getStreamingStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_STREAMING_STATUS.wifiCmd)
    }

    /**
     * POST "setSleepTimer"
     */
    override fun setSleepTimer(secs: Int, port: String?) {
        val payload = EnumCommandMapping.SET_SLEEP_TIMER.payloadFormat.format(secs)
        encryptPost(port = port, command = EnumCommandMapping.SET_SLEEP_TIMER.wifiCmd, payload = payload, passback = secs)
    }

    /**
     * GET "getSleepTimer"
     */
    override fun getSleepTimer(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_SLEEP_TIMER.wifiCmd)
    }

    override fun getPersonalListeningMode(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_PERSONAL_LISTENING_MODE.wifiCmd)
    }

    override fun setPersonalListeningMode(isOn: Boolean, port: String?) {
        encryptPost(
            port = port,
            command = EnumCommandMapping.SET_PERSONAL_LISTENING_MODE.wifiCmd,
            payload = StatusReq(if (isOn) statusOn else statusOff).toJSONObj().toString(),
        )
    }

    /**
     * GET "getMediaSource"
     */
    override fun getMediaSourceStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_MEDIA_SOURCE_STATUS.wifiCmd)
    }

    override fun getColorPicker(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_LIGHT_COLOR_PICKER.wifiCmd)
    }

    override fun setColorPicker(picker: ColorPicker, port: String?) {
        encryptPost(port = port, command = EnumCommandMapping.SET_LIGHT_COLOR_PICKER.wifiCmd, payload = GsonUtil.parseBeanToJson(picker))
    }

    /**
     * GET getnewprivatesyslog
     */
    override fun setTimeZone(formatDate: String, port: String?) {
        val command = String.format("getnewprivatesyslog:ip:$(date %s)", formatDate)
        encryptGet(port = port, command = command)
    }

    /**
     * POST "setSmartMode"
     */
    override fun setSmartMode(isOn: Boolean, port: String?) {
        val payload = EnumCommandMapping.SET_SMART_MODE.payloadFormat.format(if (isOn) "on" else "off")
        encryptPost(port = port, command = EnumCommandMapping.SET_SMART_MODE.wifiCmd, payload = payload)
    }

    /**
     * GET "getSmartMode"
     */
    override fun getSmartMode(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_SMART_MODE.wifiCmd)
    }

    /**
     * GET "getRearSpeakerStatus"
     */
    override fun getRearSpeakerStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_REAR_SPEAKER_STATUS.wifiCmd)
    }

    override fun setLightInfo(req: SetLightInfoRequest, port: String?) {
        val payload = GsonUtil.parseBeanToJson(req)
        encryptPost(port = port, command = EnumCommandMapping.SET_LIGHT_INFO.wifiCmd, payload = payload)
    }

    override fun getLightInfo(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_LIGHT_INFO.wifiCmd)
    }

    override fun resetLightPatternColor(id: String, port: String?) {
        val payload = EnumCommandMapping.RESET_LIGHT_PATTERN_COLOR.payloadFormat.format(id)
        encryptPost(port = port, command = EnumCommandMapping.RESET_LIGHT_PATTERN_COLOR.wifiCmd, payload = payload)
    }

    override fun getGroupDevicesOtaStatus(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_GROUP_DEVICES_OTA_STATUS.wifiCmd)
    }

    override fun getGroupDevicesFlag(port: String?) {
        encryptGet(port = port, command = EnumCommandMapping.GET_GROUP_DEVICES_FLAG.wifiCmd)
    }

    private fun WiFiDevice.getDLNAProvider(): DlnaServiceProvider? {
        val provider = DlnaServiceProviderPool.me().getDlanHelper(deviceItem.uuid) ?: run {
            Logger.e(TAG, "getDLNAProvider() >>> can't get provider by UUID[${deviceItem.uuid}]")
            return null
        }

        deviceItem.devInfoExt?.mPreviousDelayedTimer?.let { timer ->
            if (timer.isValidate) {
                Logger.i(TAG, "getDLNAProvider() >>> updateStartTime in timer")
                timer.updateStartTime()
            }
        }

        return provider
    }

    fun request(port: String? = null, wifiCommand: String, params: String? = null) {
        if (params.isNullOrEmpty()) {
            encryptGet(port = port, command = wifiCommand)
        } else {
            encryptPost(port = port, command = wifiCommand, payload = params)
        }
    }

    /**
     * @param passback store any param which need to be used after rsp came back.
     * @link [parseSpecBusinessResponse]
     */
    private fun encryptGet(port: String?, command: String, passback: Any? = null) =
        GlobalScope.launch(DISPATCHER_API) {
            val url = formatGetUrl(ipAddress = ipAddress, port = port, command = command)
            Logger.d(TAG, "encryptGet() >>> url:$url")
            encryptOkHttp.get(url, RspListener(command = command, passback = passback, port = port))
        }

    /**
     * @param passback store any param which need to be used after rsp came back.
     * @link [parseSpecBusinessResponse]
     */
    private fun encryptPost(port: String?, command: String, payload: String?, passback: Any? = null) =
        GlobalScope.launch(DISPATCHER_API) {
            val url = formatPostUrl(ipAddress = ipAddress, port = port)
            val body = formatPostBody(command = command, payload = payload)

            Logger.d(TAG, "encryptPost() >>> url: $url\nbody: $body")
            encryptOkHttp.post(url, RspListener(command = command, passback = passback, port = port), null, body)
        }

    /**
     * @param passback store any param which need to be used after rsp came back.
     * @link [parseSpecBusinessResponse]
     */
    private inner class RspListener(
        private val command: String,
        private val passback: Any?,
        private val port: String?
    ) : IOkHttpRequestCallback() {
        override fun onSuccess(response: Any?) {
            super.onSuccess(response)

            val item = (response as? OkHttpResponseItem) ?: run {
                listenerProxy.getter()
                    .notify(cmd = command, e = HttpResponseCastException(device = device, obj = response), port = port)
                return
            }
            parseSpecBusinessResponse(command = command, content = item.body, passback = passback, port = port)
            listenerProxy.getter().notify(command = command, content = item.body, port = port)
        }

        override fun onFailure(e: Exception) {
            super.onFailure(e)
            listenerProxy.getter().notify(cmd = command, e = e, port = port)
        }
    }

    private fun AbstractQueue<IOneDeviceListener>.notify(cmd: String?, e: Exception, port: String?) {
        this.forEach { listener ->
            listener.onRspFailure(cmd = cmd, e = e, port = port)
        }
    }

    private fun AbstractQueue<IOneDeviceListener>.notify(command: String, content: String?, port: String?) {
        Logger.d(TAG, "notify() >>> deviceName:${device.deviceItem.Name} command[$command] port[$port] content:$content")

        this.forEach { listener ->
            listener.onRspSuccess(cmd = command, content = content, port = port)
        }
    }

    /**
     * Parse response/notify and update business params which concerned.
     */
    private fun parseSpecBusinessResponse(command: String, content: String?, passback: Any?, port: String?) {
        Logger.d(TAG, "parseSpecBusinessResponse() >>> UUID[${device.UUID}] command[$command] port[$port] passback[$passback] content:$content")
        if (content.isNullOrBlank()) {
            onJsonFormatException(cmd = command, content = content, port = port)
            return
        }

        when (command) {
            EnumCommandMapping.GET_FEATURE_SUPPORT.wifiCmd -> {
                val featureSupportRsp = GsonUtil.parseJsonToBean(
                    content,
                    FeatureSupportResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                val featureSupport = featureSupportRsp.featureSupport ?: return
                if (port.isNullOrBlank()) {
                    device.onFeatureSupport(featureSupport)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onFeatureSupport(featureSupport = featureSupport)
                }
            }

            EnumCommandMapping.GET_EQ_LIST.wifiCmd -> {
                val eqListRsp = GsonUtil.parseJsonToBean(
                    content,
                    EQListResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onEQListResponse(eqListRsp = eqListRsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onEQList(eqListResponse = eqListRsp)
                }
            }

            EnumCommandMapping.SET_ACTIVE_EQ.wifiCmd -> {
                val basicRsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetActiveEQResult(basicRsp.errorCode)
                }
            }

            EnumCommandMapping.GET_EQ.wifiCmd -> {
                val eqRsp = GsonUtil.parseJsonToBean(
                    content,
                    EQResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onEQResponse(eqRsp = eqRsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onEQ(eqResponse = eqRsp)
                }
            }

            EnumCommandMapping.SET_EQ.wifiCmd -> {
                val basicRsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetEQResult(basicRsp.errorCode)
                }
            }

            EnumCommandMapping.GET_AP_LIST.wifiCmd -> {
                val apResponse = GsonUtil.parseJsonToBean(
                    content,
                    APListResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onAPList(apResponse.apList)
                }
            }

            EnumCommandMapping.GET_OTA_STATUS.wifiCmd -> {
                val otaStatus = GsonUtil.parseJsonToBean(
                    content,
                    GetOtaStatusResponse::class.java
                ).otaStatus ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onOtaStatus(otaStatus)
                }
            }

            EnumCommandMapping.GET_BATTERY_STATUS.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BatteryStatusResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onBatteryStatus(status = rsp)
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onBatteryStatus(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_CALIBRATION.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetCalibrationResult(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_CALIBRATION_STATE.wifiCmd -> {
                val calibration = GsonUtil.parseJsonToBean(
                    content,
                    CalibrationState::class.java
                )?.calibration ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onCalibration(calibration = calibration)
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onCalibration(calibration = calibration)
                }
            }

            EnumCommandMapping.CANCEL_CALIBRATION.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onCalibration(Calibration())
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onCalibrationCancelled(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_C4A_PERMISSION_STATUS.wifiCmd,
            EnumCommandMapping.SET_C4A_PERMISSION_STATUS.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    C4aPermissionStatusResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (EnumCommandMapping.GET_C4A_PERMISSION_STATUS.wifiCmd == command &&
                    rsp.success() &&
                    port.isNullOrBlank()
                ) {
                    device.onC4APermissionStatue(rsp = rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onC4aPermissionStatus(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_CHROMECAST_OPT_IN.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetChromeCastOptInResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onChromeCastOptIn(ChromeCastOpt.fromValue(optIn = rsp.optIn))
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onChromecastOptIn(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_CHROMECAST_OPT_IN.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                val opt = passback as? ChromeCastOpt

                if (rsp.success() && null != opt && port.isNullOrBlank()) {
                    device.onChromeCastOptIn(opt)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onChromecastOptIn(
                        rsp = GetChromeCastOptInResponse(
                            errorCode = rsp.errorCode,
                            optIn = if (rsp.success()) opt?.value else null
                        )
                    )
                }
            }

            EnumCommandMapping.SET_DEBUG_MODE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetDebugMode(rsp = rsp)
                }
            }

            EnumCommandMapping.ALEXA_CBL_STATUS.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    AlexaCBLStatusResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onAlexaCBLStatus(rsp = rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onAlexaCBLStatus(rsp = rsp)
                }
            }

            EnumCommandMapping.ALEXA_CBL_LOGOUT.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onAlexaCBLStatus(rsp = null)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onAlexaCBLLogout(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_LWA_STATE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    LwaStateResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onLwaStateResponse(rsp = rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetLWAState(rsp = rsp)
                }
            }

            EnumCommandMapping.REQUEST_LWA_LOGOUT.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onLwaStateResponse(rsp = null)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onRequestLWALogout(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_LWA_AUTH_CODE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetLWAAuthCode(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_VOICE_LANGUAGE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetVoiceLanguageResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onGetVoiceLanguage(rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetVoiceLanguage(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_VOICE_REQUEST_START_TONE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetVoiceToneResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onGetVoiceRequestStartTone(rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetVoiceRequestStartTone(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_VOICE_REQUEST_END_TONE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetVoiceToneResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onGetVoiceRequestEndTone(rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetVoiceRequestEndTone(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_VOICE_REQUEST_START_TONE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onSetVoiceRequestStartTone(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_VOICE_REQUEST_END_TONE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onSetVoiceRequestEndTone(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_VOICE_LANGUAGE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onSetVoiceLanguage(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_SUPPORTED_VOICE_LANGUAGE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetSupportedVoiceLanguageResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onGetSupportedVoiceLanguage(rsp = rsp)
                }
            }

            EnumCommandMapping.REQUEST_GOOGLE_LOGOUT.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }
                if (rsp.success() && port.isNullOrBlank()) {
                    device.onC4APermissionStatue(rsp = null)
                }
                listenerProxy.getter().forEach { listener ->
                    listener.onRequestGoogleLogout(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_OTA_ACCESS_POINT.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetOtaAccessPointResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetOTAAccessPoint(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_SMART_BTN_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetSmartBtnConfigRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                // set an empty SmartBtnConfig to clarify this config had been fetched before.
                if (port.isNullOrBlank()) {
                    device.onSmartBtnConfig(config = rsp.smartConfig ?: SmartBtnConfig())
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetSmartBtnConfig(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_SMART_BTN_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                // update internal SmartBtnConfig in device bean directly after setter req success.
                if (rsp.success() && passback is SmartBtnConfig && port.isNullOrBlank()) {
                    device.onSmartBtnConfig(passback)
                }
            }

            EnumCommandMapping.GET_SOUNDSCAPE_V2_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetSoundscapeV2ConfigResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    rsp.soundscapeV2List?.let { list ->
                        device.onSoundscapeV2List(list)
                    }
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetSoundscapeV2Config(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_SOUNDSCAPE_V2_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is SetSoundscapeV2ConfigRequest && port.isNullOrBlank()) {
                    device.onSoundscapeV2Config(req = passback)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetSoundscapeV2Config(rsp = rsp)
                }
            }

            EnumCommandMapping.CONTROL_SOUNDSCAPE_V2.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is ControlSoundscapeV2 && port.isNullOrBlank()) {
                    device.onControlSoundscapeV2(control = passback)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onControlSoundscapeV2(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_SOUNDSCAPE_V2_STATE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetSoundscapeV2StateResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onSoundscapeV2State(rsp = rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSoundscapeV2State(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_DEVICE_INFO.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetDeviceInfoResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && port.isNullOrBlank()) {
                    device.onDeviceInfo(info = rsp.deviceInfo)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onDeviceInfo(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_DEVICE_USAGE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetProductUsageResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onProductUsage(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_DEVICE_NAME.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetDeviceNameResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    listenerProxy.getter().forEach { listener ->
                        listener.onDeviceName(union = DeviceNameRspUnion(name = rsp.deviceName, editable = rsp.editable()))
                    }
                }
            }

            EnumCommandMapping.SET_DEVICE_NAME.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is String) {
                    listenerProxy.getter().forEach { listener ->
                        listener.onDeviceName(union = DeviceNameRspUnion(name = passback, editable = true))
                    }
                }
            }

            EnumCommandMapping.GET_REAR_SPEAKER_VOLUME.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    RearSpeakerVolumeResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onRearSpeakerVolume(rsp = rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onRearSpeakerVolume(rsp = rsp)
                }
            }

            EnumCommandMapping.SET_FACTORY_RESTORE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onRestoreFactory(success = rsp.success())
                }
            }

            EnumCommandMapping.GET_AUTO_POWER_OFF_TIMER.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetAutoPowerOffTimerRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                val secs = rsp.timer ?: return
                if (port.isNullOrBlank()) {
                    device.onAutoPowerOff(secs = secs)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onAutoPowerOffTimer(secs = secs)
                }
            }

            EnumCommandMapping.SET_AUTO_POWER_OFF_TIMER.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is Int && port.isNullOrBlank()) {
                    device.onAutoPowerOff(secs = passback)
                }
            }

            EnumCommandMapping.SET_BLUETOOTH_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is EnumBluetoothConfig && port.isNullOrBlank()) {
                    device.onBluetoothConfig(config = passback)
                }
            }

            EnumCommandMapping.GET_BLUETOOTH_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetBluetoothConfigRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                rsp.enum?.let { config ->
                    if (port.isNullOrBlank()) {
                        device.onBluetoothConfig(config = config)
                    }

                    listenerProxy.getter().forEach { listener ->
                        listener.onBluetoothConfig(config = config)
                    }
                }
            }

            EnumCommandMapping.SET_FEEDBACK_TONE_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is EnumFeedbackToneConfig && port.isNullOrBlank()) {
                    device.onFeedbackToneConfig(config = passback)
                }
            }

            EnumCommandMapping.GET_FEEDBACK_TONE_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetFeedbackToneRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                rsp.enum?.let { config ->
                    if (port.isNullOrBlank()) {
                        device.onFeedbackToneConfig(config = config)
                    }

                    listenerProxy.getter().forEach { listener ->
                        listener.onFeedbackToneConfig(config = config)
                    }
                }
            }

            EnumCommandMapping.GET_GENERAL_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content, ValueResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGeneralConfig(rsp)
                }
            }

            EnumCommandMapping.GET_BATTERY_SAVING_MODE.wifiCmd -> {
                val status = GsonUtil.parseJsonToBean(
                    content,
                    GetBatterySavingStatusRsp::class.java
                )?.enum ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onBatterySavingStatus(status = status)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onBatterySavingStatus(status = status)
                }
            }

            EnumCommandMapping.SET_BATTERY_SAVING_MODE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (rsp.success() && passback is EnumBatterySavingStatus && port.isNullOrBlank()) {
                    device.onBatterySavingStatus(status = passback)
                }
            }

            EnumCommandMapping.SET_IR_LEARN.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetIRLearn(success = rsp.success())
                }
            }

            EnumCommandMapping.GET_DJ_PAD.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    DJPadResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onDJPad(rsp)
                }
            }

            EnumCommandMapping.SET_GENERAL_CONFIG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetGeneralConfig(success = rsp.success())
                }
            }

            EnumCommandMapping.TRIGGER_CAST_LED.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onTriggerCastLED(success = rsp.success())
                }
            }

            EnumCommandMapping.DESTROY_CAST_GROUP.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onDestroyCastGroup(success = rsp.success())
                }
            }

            EnumCommandMapping.GROUP_CALIBRATION.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGroupCalibration(success = rsp.success())
                }
            }

            EnumCommandMapping.SWITCH_STEREO_CHANNEL.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSwitchStereoChannel(success = rsp.success())
                }
            }

            EnumCommandMapping.SKIP_DEMO_SOUND.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSkipDemoSound(success = rsp.success())
                }
            }

            EnumCommandMapping.GET_GROUP_INFO.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetGroupInfoRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onGroupInfo(rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetGroupInfo(rsp)
                }
            }

            EnumCommandMapping.GET_GROUP_PARAMETER.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetGroupParameterRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onGroupParameter(rsp)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetGroupParameter(rsp)
                }
            }

            EnumCommandMapping.GET_GROUP_CALIBRATION_STATE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetGroupCalibrationStateRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetGroupCalibrationState(rsp)
                }
            }

            EnumCommandMapping.RENAME_GROUP.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onRenameGroup(rsp.success())
                }
            }

            EnumCommandMapping.GET_STREAMING_STATUS.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetStreamingStatusRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onStreamingStatus(rsp = rsp)
                }

                rsp.status?.let { status ->
                    if (port.isNullOrBlank()) {
                        device.onStreamingStatus(status = status)
                    }

                    listenerProxy.getter().forEach { listener ->
                        listener.onDolbyAtoms(support = status.isAtoms)
                    }
                }
            }

            EnumCommandMapping.LP_SET_CAST_GROUP.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetCastGroup(success = rsp.success())
                }
            }

            EnumCommandMapping.SET_SLEEP_TIMER.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetSleepTimer(rsp = rsp)
                }

                if (rsp.success() && passback is Int && port.isNullOrBlank()) {
                    device.onSleepTimer(secs = passback)
                }
            }

            EnumCommandMapping.GET_SLEEP_TIMER.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetSleepTimerRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetSleepTimer(rsp = rsp)
                }

                if (rsp.success()) {
                    if (null != rsp.sleepTimerSec && port.isNullOrBlank()) {
                        device.onSleepTimer(secs = rsp.sleepTimerSec)
                    }

                    if (null != rsp.remainingTimeSec) {
                        listenerProxy.getter().forEach { listener ->
                            listener.onSleepTimerTick(remainSecs = rsp.remainingTimeSec)
                        }
                    }
                }
            }

            EnumCommandMapping.GET_PERSONAL_LISTENING_MODE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    StatusResp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetPersonalListeningMode(rsp.success() && rsp.status == statusOn)
                }
            }

            EnumCommandMapping.GET_MEDIA_SOURCE_STATUS.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    MediaResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onMediaSourceStatus(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_LIGHT_COLOR_PICKER.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    ColorPickerResp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetColorPicker(rsp)
                }
            }

            EnumCommandMapping.GET_REAR_SPEAKER_STATUS.wifiCmd -> {
                val rears = GsonUtil.parseJsonToBean(
                    content,
                    RearSpeakerResp::class.java
                )?.rears ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                if (port.isNullOrBlank()) {
                    device.onGetRearSpeaker(rears)
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSpeakerRearNotify(rears = rears)
                }
            }

            EnumCommandMapping.GET_SMART_MODE.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetSmartModeRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetSmartMode(isOn = rsp.isOn)
                }
            }

            EnumCommandMapping.GET_PROD_SETTING.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    ProdSettingResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                (rsp as? ProdSettingResponse<String, String>)?.let {
                    if (port.isNullOrBlank()) {
                        device.onProdSettingStatus(rsp = it)
                    }

                    listenerProxy.getter().forEach { listener ->
                        listener.onProdSettingStatus(rsp = it)
                    }
                }
            }

            EnumCommandMapping.SET_LIGHT_INFO.wifiCmd -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED setLightInfo() >>> UUID[${device.UUID}] content:$content")
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onSetLightInfo(rsp)
                }
            }

            EnumCommandMapping.GET_LIGHT_INFO.wifiCmd -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED getLightInfo() >>> UUID[${device.UUID}] content:$content")
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetLightInfoResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGetLightInfo(rsp)
                }

                if (rsp.success()) {
                    rsp.lightInfo?.let { itLightInfo ->
                        device.onLightInfo(lightInfo = itLightInfo)
                    }
                }
            }

            EnumCommandMapping.RESET_LIGHT_PATTERN_COLOR.wifiCmd -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED resetLightPatternColor() >>> UUID[${device.UUID}] content:$content")
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    BasicResponse::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onResetLightPatternColor(rsp)
                }
            }

            EnumCommandMapping.GET_GROUP_DEVICES_OTA_STATUS.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GroupDeviceOTAStatusRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                listenerProxy.getter().forEach { listener ->
                    listener.onGroupDeviceOTAStatus(rsp = rsp)
                }
            }

            EnumCommandMapping.GET_GROUP_DEVICES_FLAG.wifiCmd -> {
                val rsp = GsonUtil.parseJsonToBean(
                    content,
                    GetGroupDevicesFlagRsp::class.java
                ) ?: run {
                    onJsonFormatException(cmd = command, content = content, port = port)
                    return
                }

                device.onGroupDevicesFlag(rsp = rsp)

                listenerProxy.getter().forEach { listener ->
                    listener.onGroupDeviceFlag(rsp = rsp)
                }
            }

            else -> {
                if (command == (passback as? AttachOneCommand)?.wifiCommand &&
                    null != passback.targetRspType
                ) {
                    val rsp = GsonUtil.parseJsonToBean(
                        content,
                        passback.targetRspType
                    ) ?: run {
                        onJsonFormatException(cmd = command, content = content, port = port)
                        return
                    }

                    listenerProxy.getter().forEach { listener ->
                        listener.onAttachCommandReceived(rsp = rsp)
                    }
                }
            }
        }
    }

    override fun sendAttachCommand(command: AttachOneCommand, port: String?) {
        val cmd = command.wifiCommand
        if (cmd.isNullOrBlank()) {
            return
        }

        if (command.wifiPayload.isNullOrBlank()) {
            encryptGet(port = port, command = cmd, passback = command)
        } else {
            encryptPost(port = port, command = cmd, payload = command.wifiPayload, passback = command)
        }
    }

    private fun onJsonFormatException(cmd: String, content: String?, port: String?) {
        listenerProxy.getter().forEach { listener ->
            listener.onRspFailure(cmd = cmd, e = JsonSyntaxException("invalid response format for target cmd[$cmd]\n$content"), port = port)
        }
    }

    private fun formatGetUrl(ipAddress: String?, port: String?, command: String?): String =
        if (port.isNullOrBlank()) {
            "https://$ipAddress/httpapi.asp?command=$command"
        } else {
            "https://$ipAddress:$port/httpapi.asp?command=$command"
        }

    private fun formatPostUrl(ipAddress: String?, port: String?): String =
        if (port.isNullOrBlank()) {
            "https://$ipAddress/httpapi.asp?"
        } else {
            "https://$ipAddress:$port/httpapi.asp?"
        }

    private fun formatPostBody(command: String?, payload: String?): String =
        "command=$command&payload=$payload"

    companion object {
        private const val TAG = "OneWiFiSession"
    }
}