package com.harman.connect.listener

import androidx.annotation.WorkerThread
import com.harman.command.common.IGeneralCommand
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.command.partybox.gatt.Gen3StereoRenameCommand
import com.harman.command.partybox.gatt.ReqBatteryStatusCommand
import com.harman.command.partybox.gatt.alarm.AlarmInfo
import com.harman.command.partybox.gatt.radio.RadioInfo
import com.harman.command.partybox.gatt.timer.AutoOffTimerRsp
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.info.PartyConnectStatus

/**
 * Created by gerrar<PERSON><PERSON><PERSON> on 2024/4/12.
 */
interface IPartyBoxDeviceListener : IGattListener, IMediaListener {

    /**
     * callback when onCharacteristicChanged triggered and can parse the payload inside by
     * command processor (like [PartyBoxGattCommandProcessor] etc.).
     * [sendCommand] might be null if this is a noti event. (not triggered by a req.)
     */
    @WorkerThread
    fun onCommandReceived(device: BaseBTDevice<*, *>, sendCommand: IGeneralCommand?, receivedCommand: IGeneralCommand) {}

    /**
     * callback when receive [GattPacketFormat.RESP_PLAYER_INFO] whatever as a response or notify.
     */
    @WorkerThread
    fun onPlayerInfoUpdate() {}

    /**
     * callback when receive [GattPacketFormat.RET_ADVANCE_EQ] whatever as a response or notify
     */
    @WorkerThread
    fun onAdvanceEQUpdate() {}

    /**
     * callback when receive [GattPacketFormat.RESP_DEV_INFO] and contains sub command [GattPacketFormat.PARTY_CONNECT_MODE_TOKEN_ID]
     */
    @WorkerThread
    fun onPartyConnectStatusChanged(status: PartyConnectStatus) {}

    /**
     * callback when receive [GattPacketFormat.RESP_DEV_INFO]
     */
    @WorkerThread
    fun onDeviceInfoUpdate() {}

    /**
     * callback when receive [GattPacketFormat.RES_DEVICE_FEATURE_INFO]
     */
    @WorkerThread
    fun onDeviceFeatureUpdate() {}

    /**
     * callback when receive [GattPacketFormat.RESP_LIGHT_INFO]
     */
    @WorkerThread
    fun onLightInfoUpdate() {}

    /**
     * callback when req or receive notify of feedback tone.
     */
    @WorkerThread
    fun onFeedbackTone() {}

    /**
     * callback when req [SetDeviceNameCommand] regardless rsp.
     */
    @WorkerThread
    fun onDeviceNameUpdated(name: String) {}

    /**
     * callback when req [Gen3StereoRenameCommand] regardless rsp.
     */
    @WorkerThread
    fun onStereoGroupNameUpdated(groupName: String) {}

    /**
     * callback when req [ReqBatteryStatusCommand] and get rsp.
     */
    @WorkerThread
    fun onBatteryStatusUpdated() {}

    /**
     * callback when req [ReqAuthCommand] receive rsp
     */
    @WorkerThread
    fun onAuthResult(success: Boolean) {}

    /**
     * @link[]
     */
    @WorkerThread
    fun onScreenDisplayInfo(){}

    /**
     * @link[]
     */
    @WorkerThread
    fun onAlarmInfo(alarmInfo: AlarmInfo?){}

    @WorkerThread
    fun onSetDeviceInfo(){}

    @WorkerThread
    fun onAmbientLightInfo(){}

    @WorkerThread
    fun onRadioInfo(radioInfo: RadioInfo?, functionality: RadioInfo.Command?) {}

    /**
     * Callback when auto off timer information is updated
     */
    @WorkerThread
    fun onAutoOffTimerUpdate(response: AutoOffTimerRsp?) {
        // Default empty implementation
    }
}