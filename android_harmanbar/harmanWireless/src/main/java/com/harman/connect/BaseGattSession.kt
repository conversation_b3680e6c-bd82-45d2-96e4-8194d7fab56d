package com.harman.connect

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothProfile
import android.content.Context
import androidx.annotation.AnyThread
import androidx.annotation.CallSuper
import androidx.annotation.WorkerThread
import com.harman.connect.listener.IGattListener
import com.harman.discover.DeviceStore
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.GattListenerProxy
import com.harman.discover.util.Tools.bluetoothAdapter
import com.harman.discover.util.Tools.disconnectAndClose
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil
import com.harmanbar.clj.fastble.BleManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.Integer.max
import java.util.UUID
import kotlin.math.min

/**
 * Created by gerrardzhang on 2024/2/19.
 *
 * Represent a session of GATT connection.
 * Key: [BluetoothDevice.getAddress]
 */
abstract class BaseGattSession<DeviceType : BaseBTDevice<Role, AuraCastRole>, Role, AuraCastRole>(
    // Keep bleAddress as const value cause device may change this once BLE connection established.
    val bleAddress: String?,
    // Device which binding with this session.
    val device: DeviceType,
    // listeners which store in BaseBLEDevice
    val listenerProxy: GattListenerProxy
) : BluetoothGattCallback(), CoroutineScope by CoroutineScope(Dispatchers.Default) {

    protected abstract val rxUUID: UUID

    open val delayMillsBeforeDiscoverServices: Long = 500L

    open val delayMillsBeforeRequestMTU: Long = 500L

    open val writeReadLogContent: ((value: ByteArray, isWrite: Boolean, isSuccess: Boolean) -> Unit)? = null

    protected val txUUID: UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f6d0002")

    protected val txSecureUUID: UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f6d0003")

    private val bleRxTxUUID: UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f6d0000")

    private val gapUUID = UUID.fromString("00001801-0000-1000-8000-00805f9b34fb")

    private val rxTxUUIDs = arrayListOf(bleRxTxUUID, gapUUID)

    private val descriptorUUID: UUID = UUID.fromString("00002902-0000-1000-8000-00805F9B34FB")

    @Volatile
    var state: EnumConnectionStatus = EnumConnectionStatus.DISCONNECTED
        private set

    /** Bind lifecycle with [state]
     * Don't reset bluetoothGatt and secureConnect in [closeSession],
     * which might be called after next function [connect]
     * [bluetoothGatt], [secureConnect] only can be override in connect().
    */
    private var bluetoothGatt: BluetoothGatt? = null
    internal var secureConnect: Boolean = false

    /** Bind lifecycle with [state] */
    internal var txCharacteristic: BluetoothGattCharacteristic? = null
    internal var secureTxCharacteristic: BluetoothGattCharacteristic? = null

    val isDisconnected: Boolean
        get() = EnumConnectionStatus.DISCONNECTED == state

    val isConnecting: Boolean
        get() = EnumConnectionStatus.CONNECTING == state

    val isConnected: Boolean
        get() = EnumConnectionStatus.CONNECTED == state

    private val isPairing: Boolean
        get() = EnumConnectionStatus.PAIRING == state || EnumConnectionStatus.PRE_PAIR == state

    var mtuSize: Int = DEFAULT_MTU_SIZE

    /**
     * (mtuSize - 3) as split size of BLE Tx package.
     * Align with [BleManager.DEFAULT_WRITE_DATA_SPLIT_COUNT]
     */
    val packageSize: Int
        get() = max(mtuSize - 3, BleManager.DEFAULT_WRITE_DATA_SPLIT_COUNT)

    @SuppressLint("MissingPermission")
    @AnyThread
    fun connect(context: Context, secure: Boolean? = false): Boolean {
        Logger.d(TAG, "connect() >>> secure[$secure][$bleAddress] isSecureBleSupport[${device.isSecureBleSupport}]")
        secureConnect = (secure ?: false) && device.isSecureBleSupport

        synchronized(state) {
            if (!isDisconnected) {
                Logger.d(TAG, "[$bleAddress] connect() >>> already in connecting or connected state: $state")
                return true
            }

            Logger.d(TAG, "[$bleAddress] connect() >>> switch to connecting state")
            state = EnumConnectionStatus.CONNECTING
        }

        listenerProxy.getter().notify(EnumConnectionStatus.CONNECTING)

        val bluetoothDevice = getRemoteDevice(context) ?: run {
            Logger.e(TAG, "[$bleAddress] connect() >>> fail to get remote device")
            closeSession()
            return false
        }

        Logger.i(TAG, "ble[$bleAddress] connect() >>> start connect Gatt isA2DPConnected[${device.isA2DPConnected}]")

        bluetoothGatt = bluetoothDevice.connectGatt(
            context,
            false,
            this@BaseGattSession,
            BluetoothDevice.TRANSPORT_LE
        )
        return true
    }

    @SuppressLint("MissingPermission")
    @AnyThread
    fun disconnect() {
        closeSession()
    }

    /**
     * Can't invoke directly without
     */
    @OptIn(ExperimentalStdlibApi::class)
    @SuppressLint("MissingPermission")
    @WorkerThread
    protected fun write(bytes: ByteArray, secure: Boolean? = false): Boolean {
        synchronized(state) {
            if (!isConnected && !isPairing) {
                Logger.d(TAG, "[$bleAddress] write() >>> not in connected state: $state")
                return false
            }
        }
        Logger.d(TAG, "[$bleAddress] write() >>> in secure[$secure] channel,  ${bytes.toHexString()}")

        val gatt = bluetoothGatt ?: run {
            Logger.w(TAG, "[$bleAddress] write() >>> fail to write: bluetoothGatt is null")
            return false
        }
        val tx = if (true == secure) { secureTxCharacteristic } else { txCharacteristic } ?: run {
            Logger.w(TAG, "[$bleAddress] write() in secure[$secure] channel >>> fail to write: txCharacteristic is null")
            return false
        }

        tx.value = bytes
        val result = gatt.writeCharacteristic(tx)
        if (!result) {
            writeReadLogContent?.invoke(bytes, true, false) ?: run {
                Logger.w(TAG, "[$bleAddress] writeCharacteristic() secure[$secure] >>> gatt.writeCharacteristic fail  ${bytes.toHexString()}")
            }
        } else {
            writeReadLogContent?.invoke(bytes, true, true) ?: run {
                Logger.d(TAG, "[$bleAddress] writeCharacteristic success secure[$secure] ${bytes.toHexString()}")
            }
            listenerProxy.getter().forEach {
                it.onWriteSuccess(bytes)
            }
        }

        return result
    }

    override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
        super.onConnectionStateChange(gatt, status, newState)

        Logger.i(TAG, "mac[${device.macAddress}], ble[$bleAddress] onConnectionStateChange() >>> status[$status] newState[$newState]")

        when (newState) {
            BluetoothProfile.STATE_CONNECTED -> {
                Logger.d(TAG, "[$bleAddress] onConnectionStateChange() >>> post onConnectSuccess()")
                launch {
                    onConnectSuccess(gatt)
                }
            }

            BluetoothProfile.STATE_DISCONNECTED -> {
                notifyException(e = RuntimeException())
                closeSession()
                DeviceStore.onBLEDeviceExpired(device)
            }

            BluetoothProfile.STATE_CONNECTING,
            BluetoothProfile.STATE_DISCONNECTING -> Unit

            else -> Unit
        }
    }

    override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
        super.onServicesDiscovered(gatt, status)

        Logger.i(TAG, "[$bleAddress] onServicesDiscovered() >>> gatt services discovered. status[$status]")
        when (status) {
            BluetoothGatt.GATT_SUCCESS -> {
                launch {
                    onServicesDiscoverSuccess(gatt)
                }
            }

            else -> {
                closeSession()
            }
        }
    }

    @CallSuper
    override fun onMtuChanged(gatt: BluetoothGatt?, mtu: Int, status: Int) {
        super.onMtuChanged(gatt, mtu, status)
        Logger.i(TAG, "[$bleAddress] onMtuChanged() >>> mtu[$mtu]")
        if (BluetoothGatt.GATT_SUCCESS == status) {
            onGattConnected(mtu = mtu)
        } else {
            Logger.e(TAG, "[$bleAddress] onMtuChanged() >>> illegal status[$status]")
            closeSession()
        }
    }

    internal fun onGattConnected(mtu: Int) {
        //Due to device performance or protocol defects, the maximum mtu size should not be greater than MAX_MTU_SIZE
        mtuSize = min(mtu, MAX_MTU_SIZE)
        Logger.i(TAG, "[$bleAddress] onGattConnected() >>> size[$mtuSize] bytes")

        if (<EMAIL> != null) {
            synchronized(state) {
                state = EnumConnectionStatus.CONNECTED
            }

            listenerProxy.getter().notify(EnumConnectionStatus.CONNECTED)
            listenerProxy.getter().notify(mtuSize)
        }
    }

    internal fun onPrePair() {
        Logger.i(TAG, "[$bleAddress] PRE_PAIR() >>>")

        if (<EMAIL> != null) {
            synchronized(state) {
                state = EnumConnectionStatus.PRE_PAIR
            }
            listenerProxy.getter().notify(EnumConnectionStatus.PRE_PAIR)
        }
    }

    @Deprecated(
        "This method was deprecated in API level 33",
        ReplaceWith(
            "super.onCharacteristicChanged(gatt, characteristic)",
            "android.bluetooth.BluetoothGattCallback"
        )
    )
    override fun onCharacteristicChanged(
        gatt: BluetoothGatt?,
        characteristic: BluetoothGattCharacteristic?
    ) {
//        super.onCharacteristicChanged(gatt, characteristic)
        handleCharacteristicChanged(
            gatt = gatt,
            characteristic = characteristic,
            value = characteristic?.value
        )
    }

    override fun onDescriptorWrite(
        gatt: BluetoothGatt?,
        descriptor: BluetoothGattDescriptor?,
        status: Int
    ) {
        super.onDescriptorWrite(gatt, descriptor, status)
        Logger.d(TAG, "onDescriptorWrite() >>> characteristic?.uuid: ${descriptor?.characteristic?.uuid}")

        if (status == BluetoothGatt.GATT_SUCCESS) {
            Logger.d(TAG, "onDescriptorWrite status: GATT_SUCCESS")

        }
    }

    private fun getRemoteDevice(context: Context): BluetoothDevice? {
        if (bleAddress.isNullOrBlank()) {
            return null
        }

        val adapter = context.bluetoothAdapter() ?: return null
        return adapter.getRemoteDevice(bleAddress)
    }

    @SuppressLint("MissingPermission")
    @WorkerThread
    private suspend fun onConnectSuccess(gatt: BluetoothGatt?) {
        if (!isConnecting) {
            Logger.w(TAG, "[$bleAddress] onConnectSuccess() >>> not connecting")
            return
        }

        Logger.d(TAG, "[$bleAddress] onConnectSuccess() >>> start delay[$delayMillsBeforeDiscoverServices]ms")
        delay(delayMillsBeforeDiscoverServices)
        Logger.d(TAG, "[$bleAddress] onConnectSuccess() >>> start discoverServices")

        if (true == gatt?.discoverServices()) {
            // wait callback of onServicesDiscovered
            Logger.i(TAG, "[$bleAddress] onConnectSuccess() >>> start gatt services discover success")
        } else {
            // regarded as exception
            Logger.e(TAG, "[$bleAddress] onConnectSuccess() >>> start gatt services discover fail")
            closeSession()
        }
    }

    @WorkerThread
    private suspend fun onServicesDiscoverSuccess(gatt: BluetoothGatt?) {
        if (!isConnecting) {
            Logger.w(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> not connecting")
            return
        }

        if (gatt?.services.isNullOrEmpty()) {
            Logger.e(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> empty gatt services")
            closeSession()
            return
        }

        gatt.services.forEachIndexed { i, service ->
            Logger.e(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> service[$i][${service.uuid}]")
        }

        val gattService = rxTxUUIDs.firstNotNullOfOrNull { gatt.getService(it) } ?: run {
            Logger.e(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> cant get Rx/Tx services services = ${gatt?.services?.map { item -> item.uuid }}")
            closeSession()
            return
        }

        val rxCharacteristic = gattService.getCharacteristic(rxUUID) ?: run {
            Logger.e(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> cant not get Rx service on $rxUUID")
            closeSession()
            return
        }

        val txCharacteristic = gattService.getCharacteristic(txUUID) ?: run {
            Logger.e(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> cant not get Tx service on $txUUID")
            closeSession()
            return
        }

        <EMAIL> = txCharacteristic

        if (!setNotify(enable = true, gatt = gatt, rxCharacteristic = rxCharacteristic)) {
            Logger.w(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> fail to set notify")
            // ignore setNotify failure
        }

        Logger.i(TAG, "[$bleAddress] onServicesDiscoverSuccess() >>> establish BLE session with [$bleAddress] success")

        delay(delayMillsBeforeRequestMTU)

        val mtu = MAX_MTU_SIZE
        val ret = requestMTU(gatt = gatt, mtu = mtu)
        Logger.i(TAG, "[$bleAddress] request MTU() >>> [$bleAddress] result:$ret")
    }

    @SuppressLint("MissingPermission")
    private fun setNotify(
        enable: Boolean,
        gatt: BluetoothGatt,
        rxCharacteristic: BluetoothGattCharacteristic
    ): Boolean {
        if (!gatt.setCharacteristicNotification(rxCharacteristic, enable)) {
            Logger.e(TAG, "[$bleAddress] setNotify() >>> fail to set characteristic notification")
            return false
        }

        val descriptor = rxCharacteristic.getDescriptor(descriptorUUID) ?: run {
            Logger.e(TAG, "[$bleAddress] setNotify() >>> fail to get descriptor from Rx characteristic")
            return false
        }

        if (!descriptor.setValue(enable.toBluetoothGattDescriptorValue())) {
            Logger.e(TAG, "[$bleAddress] setNotify() >>> fail to set value to descriptor")
            return false
        }

        if (!gatt.writeDescriptor(descriptor)) {
            Logger.e(TAG, "[$bleAddress] setNotify() >>> fail to write descriptor")
            return false
        }

        return true
    }

    private fun Boolean.toBluetoothGattDescriptorValue() = if (this) {
        BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
    } else {
        BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
    }

    /**
     * An optional process
     */
    @SuppressLint("MissingPermission")
    private fun requestMTU(gatt: BluetoothGatt, mtu: Int): Boolean {
        return gatt.requestMtu(mtu)
    }

    /**
     * override in child class to impl command parser.
     */
    @AnyThread
    @CallSuper
    open fun handleCharacteristicChanged(
        gatt: BluetoothGatt?,
        characteristic: BluetoothGattCharacteristic?,
        value: ByteArray?
    ) {
        writeReadLogContent?.invoke(value ?: byteArrayOf(), false, true) ?: run {
            Logger.i(TAG, "[$bleAddress] handleCharacteristicChanged ${HexUtil.encodeHexStr(value)}")
        }
        listenerProxy.getter().forEach {
            it.onCharacteristicChanged(value ?: byteArrayOf())
        }
    }

    open fun setSecureCharacteristic() {
        Logger.d(TAG, "setSecureCharacteristic() >>> [$bleAddress]")
        if (!device.isSecureBleSupport) {
            Logger.d(TAG, "setSecureCharacteristic() >>> [$bleAddress] doesn't support secure BLE")
            return
        }

        val gatt = bluetoothGatt ?: run {
            Logger.e(TAG, "setSecureCharacteristic() >>> [$bleAddress] missing BluetoothGatt")
            closeSession()
            return
        }

        if (gatt.services.isNullOrEmpty()) {
            Logger.e(TAG, "[$bleAddress] setSecureCharacteristic() >>> empty service list in gatt")
            closeSession()
            return
        }

        gatt.services.forEachIndexed { i, service ->
            Logger.e(TAG, "[$bleAddress] setSecureCharacteristic() >>> service[$i][${service.uuid}]")
        }

        val gattService = rxTxUUIDs.firstNotNullOfOrNull { gatt.getService(it) } ?: run {
            Logger.e(TAG, "setSecureCharacteristic() >>> [$bleAddress] cant get Rx/Tx services services = ${bluetoothGatt?.services?.map { item -> item.uuid }}")
            closeSession()
            return
        }

        val rxCharacteristic = gattService.getCharacteristic(txSecureUUID) ?: run {
            Logger.e(TAG, "setSecureCharacteristic() >>>  [$bleAddress] cant not get Rx service on $rxUUID")
            closeSession()
            return
        }

        val txCharacteristic = gattService.getCharacteristic(txSecureUUID) ?: run {
            Logger.e(TAG, "setSecureCharacteristic() >>> [$bleAddress] cant not get Tx service on $txUUID")
            closeSession()
            return
        }

        <EMAIL> = txCharacteristic

        if (!setNotify(enable = true, gatt = gatt, rxCharacteristic = rxCharacteristic)) {
            Logger.w(TAG, "setSecureCharacteristic() >>> [$bleAddress] fail to set notify")
            // ignore setNotify failure
        }

        Logger.i(TAG, "setSecureCharacteristic() >>> [$bleAddress] establish BLE session with secure channel with [$bleAddress] success")
    }


    @AnyThread
    internal fun closeSession() {
        Logger.d(TAG, "[$bleAddress] closeSession() >>> ")
        synchronized(state) {
            state = EnumConnectionStatus.DISCONNECTED
            Logger.d(TAG, "[$bleAddress] closeSession() >>> switch to disconnected state")
        }

        listenerProxy.getter().notify(EnumConnectionStatus.DISCONNECTED)

        bluetoothGatt?.disconnectAndClose()

        mtuSize = DEFAULT_MTU_SIZE
        txCharacteristic = null
        secureTxCharacteristic = null
        Logger.d(TAG, "[$bleAddress] closeSession() >>> done")
    }

    fun List<IGattListener>.notify(newStatus: EnumConnectionStatus) =
        this.forEach { listener ->
            listener.onGattStatusChanged(status = newStatus, session = this@BaseGattSession)
        }

    private fun List<IGattListener>.notify(newSize: Int) =
        this.forEach { listener ->
            listener.onGattMtuChanged(size = newSize, session = this@BaseGattSession)
        }

    /**
     * @param e @see [com.harman.command.Exceptions]
     */
    protected open fun notifyException(e: Exception) {
        listenerProxy.getter().forEach { listener ->
            listener.onGattException(e = e, session = this)
        }
    }

    companion object {
        const val GATT_CONNECT_TIMEOUT_MILLS = 30 * 1000L

        private const val DEFAULT_MTU_SIZE = 23
        private const val MAX_MTU_SIZE = 500

        private const val TAG = "BaseGattSession"
    }
}