package com.harman.connect.listener

import android.bluetooth.BluetoothGatt
import androidx.annotation.WorkerThread
import com.harman.command.one.OneCommandProcessor
import com.harman.command.one.bean.APItem
import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.BleGetEncryptionKeyResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.ColorPickerResp
import com.harman.command.one.bean.DJPadResponse
import com.harman.command.one.bean.DeviceNameRspUnion
import com.harman.command.one.bean.DiagnosisStatusResponse
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.FeatureSupport
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.GetOtaAccessPointResponse
import com.harman.command.one.bean.GetSmartBtnConfigRsp
import com.harman.command.one.bean.OtaStatus
import com.harman.command.one.bean.GetChromeCastOptInResponse
import com.harman.command.one.bean.GetGroupCalibrationStateRsp
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetLightInfoResponse
import com.harman.command.one.bean.GetProductUsageResponse
import com.harman.command.one.bean.GetSleepTimerRsp
import com.harman.command.one.bean.WifiSetupEndResponse
import com.harman.command.one.bean.GetSoundscapeV2ConfigResponse
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GetStreamingStatusRsp
import com.harman.command.one.bean.GetSupportedVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceLanguageResponse
import com.harman.command.one.bean.GetVoiceToneResponse
import com.harman.command.one.bean.GroupDeviceOTAStatusRsp
import com.harman.command.one.bean.GroupInfo
import com.harman.command.one.bean.LwaStateResponse
import com.harman.command.one.bean.NotifySoundscapeV2MusicState
import com.harman.command.one.bean.NotifySoundscapeV2Setting
import com.harman.command.one.bean.ProdSettingResponse
import com.harman.command.one.bean.Rear
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.ValueResponse
import com.harman.command.one.bean.upnp.SodState
import com.harman.support.ILinkPlaySDKHelperEvents
import com.harmanbar.ble.entity.BLEResponse

/**
 * Created by gerrardzhang on 2024/4/12.
 */
interface IOneDeviceListener : IGattListener, IMediaListener, ILinkPlaySDKHelperEvents {

    /**
     * callback when Http req. do Get/Post success.
     */
    @WorkerThread
    fun onRspSuccess(cmd: String?, content: String?, port: String?) {}

    /**
     * callback when Http req. do Get/Post failure.
     */
    @WorkerThread
    fun onRspFailure(cmd: String?, e: Exception, port: String?) {}

    /**
     * callback when:
     * # success after doing [BluetoothGatt.writeCharacteristic]
     */
    @WorkerThread
    fun onCharacteristicWriteSuccess(bytes: ByteArray) {}

    /**
     * callback when [OneCommandProcessor.processCommand] finished parse and [BLEResponse.isCatchAll]
     */
    @WorkerThread
    fun onResponseReceived(response: BLEResponse) {}

    @WorkerThread
    fun onAttachCommandReceived(rsp: Any) {}

    @WorkerThread
    fun onFeatureSupport(featureSupport: FeatureSupport) {}

    @WorkerThread
    fun onEQList(eqListResponse: EQListResponse) {}

    @WorkerThread
    fun onSetActiveEQResult(errCode: String?) {}

    @WorkerThread
    fun onEQ(eqResponse: EQResponse) {}

    @WorkerThread
    fun onSetEQResult(errCode: String?) {}

    @WorkerThread
    fun onAPList(apList: List<APItem>?) {}

    @WorkerThread
    fun onSetWifiNetworkResult(rsp: WifiSetupEndResponse) {}

    @WorkerThread
    fun onOtaStatus(status: OtaStatus) {}

    @WorkerThread
    fun onBatteryStatus(rsp: BatteryStatusResponse) {}

    @WorkerThread
    fun onSetCalibrationResult(rsp: BasicResponse) {}

    @WorkerThread
    fun onCalibration(calibration: Calibration) {}

    @WorkerThread
    fun onCalibrationCancelled(rsp: BasicResponse) {}

    @WorkerThread
    fun onSetActiveEQResult(errCode: Int?) {}

    @WorkerThread
    fun onC4aPermissionStatus(rsp: C4aPermissionStatusResponse) {}

    @WorkerThread
    fun onSetDebugMode(rsp: BasicResponse) {}

    @WorkerThread
    fun onSetEQResult(errCode: Int?) {}

    @WorkerThread
    fun onAlexaCBLStatus(rsp: AlexaCBLStatusResponse) {}

    @WorkerThread
    fun onAlexaCBLLogout(rsp: BasicResponse) {}

    @WorkerThread
    fun onGetLWAState(rsp: LwaStateResponse) {}

    @WorkerThread
    fun onRequestLWALogout(rsp: BasicResponse) {}

    @WorkerThread
    fun onSetLWAAuthCode(rsp: BasicResponse) {}

    @WorkerThread
    fun onSetVoiceRequestStartTone(rsp: BasicResponse) {}

    @WorkerThread
    fun onSetVoiceRequestEndTone(rsp: BasicResponse) {}

    @WorkerThread
    fun onChromecastOptIn(rsp: GetChromeCastOptInResponse) {}

    @WorkerThread
    fun onSetVoiceLanguage(rsp: BasicResponse) {}

    @WorkerThread
    fun onGetSupportedVoiceLanguage(rsp: GetSupportedVoiceLanguageResponse) {}

    @WorkerThread
    fun onRequestGoogleLogout(rsp: BasicResponse) {}

    @WorkerThread
    fun onGetVoiceRequestStartTone(rsp: GetVoiceToneResponse) {}

    @WorkerThread
    fun onGetVoiceRequestEndTone(rsp: GetVoiceToneResponse) {}

    @WorkerThread
    fun onGetVoiceLanguage(rsp: GetVoiceLanguageResponse) {}

    @WorkerThread
    fun onGetOTAAccessPoint(rsp: GetOtaAccessPointResponse) {}

    @WorkerThread
    fun onGetSmartBtnConfig(rsp: GetSmartBtnConfigRsp) {}

    @WorkerThread
    fun onGetSoundscapeV2Config(rsp: GetSoundscapeV2ConfigResponse) {}

    @WorkerThread
    fun onSetSoundscapeV2Config(rsp: BasicResponse) {}

    @WorkerThread
    fun onControlSoundscapeV2(rsp: BasicResponse) {}

    @WorkerThread
    fun onSoundscapeV2State(rsp: GetSoundscapeV2StateResponse) {}

    @WorkerThread
    fun onDeviceInfo(rsp: GetDeviceInfoResponse) {}

    @WorkerThread
    fun onProductUsage(rsp: GetProductUsageResponse) {}

    @WorkerThread
    fun onDeviceName(union: DeviceNameRspUnion) {}

    @WorkerThread
    fun onRearSpeakerVolume(rsp: RearSpeakerVolumeResponse) {}

    @WorkerThread
    fun onDiagnosisStatus(rsp: DiagnosisStatusResponse) {}

    @WorkerThread
    fun onRestoreFactory(success: Boolean) {}

    @WorkerThread
    fun onAutoPowerOffTimer(secs: Int) {}

    @WorkerThread
    fun onBluetoothConfig(config: EnumBluetoothConfig) {}

    @WorkerThread
    fun onFeedbackToneConfig(config: EnumFeedbackToneConfig) {}

    @WorkerThread
    fun onGeneralConfig(value: ValueResponse) {}

    @WorkerThread
    fun onGeneralConfigNotify(value: GeneralConfig) {}

    @WorkerThread
    fun onBatterySavingStatus(status: EnumBatterySavingStatus) {}

    @WorkerThread
    fun onSetIRLearn(success: Boolean) {}
    @WorkerThread
    fun onSetCastGroup(success: Boolean) {}

    @WorkerThread
    fun onDJPad(value: DJPadResponse) {}

    @WorkerThread
    fun onSetGeneralConfig(success: Boolean) {}

    @WorkerThread
    fun onTriggerCastLED(success: Boolean) {}

    @WorkerThread
    fun onDestroyCastGroup(success: Boolean) {}

    @WorkerThread
    fun onGroupCalibration(success: Boolean) {}

    @WorkerThread
    fun onSwitchStereoChannel(success: Boolean) {}

    @WorkerThread
    fun onSkipDemoSound(success: Boolean) {}

    @WorkerThread
    fun onGetGroupInfo(rsp: GetGroupInfoRsp) {}

    @WorkerThread
    fun onGetGroupParameter(rsp: GetGroupParameterRsp) {}

    @WorkerThread
    fun onGetGroupCalibrationState(rsp: GetGroupCalibrationStateRsp) {}

    @WorkerThread
    fun onRenameGroup(success: Boolean) {}

    @WorkerThread
    fun onStreamingStatus(rsp: GetStreamingStatusRsp) {}

    @WorkerThread
    fun onDolbyAtoms(support: Boolean) {}

    @WorkerThread
    fun onDolbyAudio(support: Boolean) {}

    @WorkerThread
    fun onHD(support: Boolean) {}

    @WorkerThread
    fun onUHD(support: Boolean) {}

    @WorkerThread
    fun onHiRes(support: Boolean) {}

    @WorkerThread
    fun onHiResSampleRate(sampleRate: String?) {}

    @WorkerThread
    fun onCDQuality(support: Boolean) {}

    @WorkerThread
    fun onMp3Quality(support: Boolean) {}

    @WorkerThread
    fun onHighQuality(support: Boolean) {}

    @WorkerThread
    fun onSetSleepTimer(rsp: BasicResponse) {}

    @WorkerThread
    fun onGetSleepTimer(rsp: GetSleepTimerRsp) {}

    /**
     * Reused for both auto off timer and soundscape timer.
     */
    @WorkerThread
    fun onSleepTimerTick(remainSecs: Int) {}

    /**
     * Diff from [onSleepTimerTick], cannot get remain time directly, only know timer event happened.
     * Use [OneDevice.getSleepTimer] to fetch precious remain time.
     */
    @WorkerThread
    fun onSleepTimerEvent(dummyRemainSecs: Int) {}

    @WorkerThread
    fun onGetPersonalListeningMode(isOn: Boolean) {}

    @WorkerThread
    fun onGetColorPicker(colorPickerResp: ColorPickerResp) {}

    @WorkerThread
    fun onSpeakerRearNotify(rears: List<Rear>) {}

    @WorkerThread
    fun onGroupInfo(groupInfo: GroupInfo?) {}

    @WorkerThread
    fun onSoundscapeV2MusicState(state: NotifySoundscapeV2MusicState) {}

    @WorkerThread
    fun onOOBEEncryptionKey(response: BleGetEncryptionKeyResponse) {}

    @WorkerThread
    fun onAuraCastSqMode(response: AuraCastSqModelResponse) {}

    @WorkerThread
    fun onSodState(state: SodState) {}

    @WorkerThread
    fun onSurroundState(level: Int) {}

    @WorkerThread
    fun onAudioSync(level: Int) {}

    @WorkerThread
    fun onGetSmartMode(isOn: Boolean) {}

    @WorkerThread
    fun onSoundscapeV2Setting(setting: NotifySoundscapeV2Setting) {}

    @WorkerThread
    fun onDlnaPlayStatus(status: String?) {}

    @WorkerThread
    fun onProdSettingStatus(rsp: ProdSettingResponse<String, String>?) {}

    @WorkerThread
    fun onArtistId(artistId: String?) {}

    @WorkerThread
    fun onAlbumId(albumId: String?) {}

    @WorkerThread
    fun onSetLightInfo(response: BasicResponse?){}

    @WorkerThread
    fun onGetLightInfo(response: GetLightInfoResponse?) {}

    @WorkerThread
    fun onNotifyLightInfo(response: SetLightInfoRequest?){}

    @WorkerThread
    fun onResetLightPatternColor(response: BasicResponse?){}

    @WorkerThread
    fun onGroupDeviceOTAStatus(rsp: GroupDeviceOTAStatusRsp) {}

    @WorkerThread
    fun onGroupDeviceFlag(rsp: GetGroupDevicesFlagRsp) {}
}