package com.harman.connect

import android.content.Context
import com.harman.command.common.GeneralGattCommand
import com.harman.command.partybox.gatt.GattPacketFormat
import com.harman.connect.listener.IGattListener
import com.harman.discover.bean.Device
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.util.Tools
import com.harman.discover.util.Tools.append
import com.harman.discover.util.Tools.insert
import com.harman.discover.util.Tools.isNullOrEmpty
import com.harman.discover.util.Tools.safeResume
import com.harman.log.Logger
import com.harmanbar.ble.utils.HexUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.LinkedList

/**
 * Created by ger<PERSON><PERSON><PERSON><PERSON> on 2024/5/23.
 *
 * Some tools make async works become sync ones.
 */
const val BLE_CONNECT_TIMEOUT_MILLS = 15 * 1000L
const val PRIMARY_DEVICE_INDEX = 0x00.toByte()
const val SECONDARY_DEVICE_INDEX = 0x01.toByte()

suspend fun Device.syncGattConnect(context: Context): Boolean {
    val device = this
    if (device.isGattConnected) {
        return true
    }

    return suspendCancellableCoroutine { continuation ->
        val session = device.gattSession ?: run {
            continuation.safeResume(false)
            return@suspendCancellableCoroutine
        }

        val listener = object : IGattListener {
            override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
                when (status) {
                    EnumConnectionStatus.DISCONNECTED -> {
                        device.unregisterDeviceListener(this)
                        continuation.safeResume(false)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }

            override fun onGattMtuChanged(size: Int, session: BaseGattSession<*, *, *>) {
                device.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }

        continuation.invokeOnCancellation {
            device.unregisterDeviceListener(listener)
        }

        device.registerDeviceListener(listener)
        session.connect(context)
    }
}

suspend fun Device.syncSecureBleGattConnect(context: Context): EnumConnectionStatus {
    val device = this
    if (device.isGattConnected) {
        return EnumConnectionStatus.CONNECTED
    }

    return suspendCancellableCoroutine { continuation ->
        val session = device.gattSession ?: run {
            continuation.safeResume(EnumConnectionStatus.DISCONNECTED)
            return@suspendCancellableCoroutine
        }

        val listener = object : IGattListener {
            override fun onGattStatusChanged(status: EnumConnectionStatus, session: BaseGattSession<*, *, *>) {
                when (status) {
                    EnumConnectionStatus.DISCONNECTED -> {
                        device.unregisterDeviceListener(this)
                        continuation.safeResume(EnumConnectionStatus.DISCONNECTED)
                    }

                    EnumConnectionStatus.PAIRING -> {
                        device.unregisterDeviceListener(this)
                        continuation.safeResume(EnumConnectionStatus.PAIRING)
                    }

                    EnumConnectionStatus.PRE_PAIR -> {
                        device.unregisterDeviceListener(this)
                        continuation.safeResume(EnumConnectionStatus.PRE_PAIR)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }

            override fun onGattMtuChanged(size: Int, session: BaseGattSession<*, *, *>) {
                device.unregisterDeviceListener(this)
                continuation.safeResume(EnumConnectionStatus.CONNECTED)
            }
        }

        continuation.invokeOnCancellation {
            device.unregisterDeviceListener(listener)
        }

        device.registerDeviceListener(listener)
        session.connect(context = context, secure = true)
    }
}

suspend fun Device.syncBrEdrConnect(context: Context): Boolean {
    val device = this
    if (device.isBrEdrConnected) {
        return true
    }

    return suspendCancellableCoroutine { continuation ->
        val session = device.brEdrSession ?: run {
            continuation.safeResume(false)
            return@suspendCancellableCoroutine
        }

        val listener = object : IGattListener {
            override fun onBrEdrStatusChanged(status: EnumConnectionStatus, session: BaseBrEdrSession<*, *, *>) {
                when (status) {
                    EnumConnectionStatus.DISCONNECTED -> {
                        device.unregisterDeviceListener(this)
                        continuation.safeResume(false)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }

            override fun onBrEdrMtuChanged(size: Int, session: BaseBrEdrSession<*, *, *>) {
                device.unregisterDeviceListener(this)
                continuation.safeResume(true)
            }
        }

        continuation.invokeOnCancellation {
            device.unregisterDeviceListener(listener)
        }

        device.registerDeviceListener(listener)
        session.connect(context)
    }
}

fun Device.asyncGattConnect(context: Context) {
    val device = this
    if (true == device.gattSession?.isConnected || device.gattSession?.isConnecting == true) {
        return
    }

    val session = device.gattSession ?: return
    session.connect(context)
}

fun Device.asyncBrEdrConnect(context: Context) {
    val device = this
    if (true == device.brEdrSession?.isConnected || device.brEdrSession?.isConnecting == true) {
        return
    }

    val session = device.brEdrSession ?: return
    session.connect(context)
}

suspend fun Device.syncGattConnectWithTimeout(
    context: Context?,
    repeatTimes: Int = 0,
    timeoutMills: Long = BLE_CONNECT_TIMEOUT_MILLS,
): Boolean = Tools.repeatWithTimeout(
    repeatTimes = 0, // useless cause `syncGattConnect` won't throw exception.
    timeoutMills = timeoutMills
) {
    context ?: return@repeatWithTimeout false

    var lastTimes = repeatTimes
    var connectRst: Boolean
    val device = this

    do {
        connectRst = device.syncGattConnect(context)
        if (connectRst) {
            break
        }

        lastTimes--
    } while (lastTimes >= 0)

    connectRst
} ?: false

suspend fun Device.syncSecureBleGattConnectWithTimeout(
    context: Context?,
    repeatTimes: Int = 0,
    timeoutMills: Long = BLE_CONNECT_TIMEOUT_MILLS,
    logTag: String
): EnumConnectionStatus = Tools.repeatWithTimeout(
    repeatTimes = 0, // useless cause `syncGattConnect` won't throw exception.
    timeoutMills = timeoutMills
) {
    context ?: run {
        Logger.e(logTag, "syncSecureBleGattConnectWithTimeout() >>> missing context")
        return@repeatWithTimeout EnumConnectionStatus.DISCONNECTED
    }

    var lastTimes = repeatTimes
    var connectRst: EnumConnectionStatus
    val device = this

    do {
        Logger.d(logTag, "syncSecureBleGattConnectWithTimeout() >>> try to connect with leftTimes[$lastTimes]")
        connectRst = device.syncSecureBleGattConnect(context)
        Logger.d(logTag, "syncSecureBleGattConnectWithTimeout() >>> connect result[$connectRst] with leftTimes[$lastTimes]")

        if (EnumConnectionStatus.CONNECTED == connectRst
            || EnumConnectionStatus.PAIRING == connectRst
            || EnumConnectionStatus.PRE_PAIR == connectRst) {
            break
        }

        lastTimes--
        delay(100L) // have a short delay before next round retry.
    } while (lastTimes >= 0)

    Logger.i(logTag, "syncSecureBleGattConnectWithTimeout() >>> return connectRst[$connectRst]")
    connectRst
} ?: EnumConnectionStatus.DISCONNECTED

suspend fun Device.syncBrEdrConnectWithTimeout(
    context: Context?,
    repeatTimes: Int = 0,
    timeoutMills: Long = BLE_CONNECT_TIMEOUT_MILLS
): Boolean = Tools.repeatWithTimeout(repeatTimes = repeatTimes, timeoutMills = timeoutMills) {
    context ?: return@repeatWithTimeout false

    var lastTimes = repeatTimes
    var connectRst: Boolean
    val device = this

    do {
        connectRst = device.syncBrEdrConnect(context)
        if (connectRst) {
            break
        }

        lastTimes--
    } while (lastTimes >= 0)

    connectRst
} ?: false

fun Device.disconnectGatt() = bleDevice?.disconnectGatt()

fun Device.disconnectBrEdr() = bleDevice?.disconnectBrEdr()

fun BaseBTDevice<*, *>.disconnectGatt() {
    val device = this
    if (!device.isGattConnected) {
        return
    }

    val session = device.gattSession ?: return
    session.disconnect()
}

fun BaseBTDevice<*, *>.disconnectBrEdr() {
    val device = this
    if (!device.isBrEdrConnected) {
        return
    }

    val session = device.brEdrSession ?: return
    session.disconnect()
}

fun GeneralGattCommand.splitCommand(logTag: String): List<GeneralGattCommand> {
    if (payload.isNullOrEmpty()) {
        return emptyList()
    }

    val ret = LinkedList<GeneralGattCommand>()

    var unProcessedPayload = tryFillDeviceIndex(this, logTag)

    while (unProcessedPayload.size >= 4) {
        val deviceIndex = unProcessedPayload[0]
        val remainingLength = unProcessedPayload.size
        val processingLen = unProcessedPayload[2] + 3 // devIdx, Token ID, len
        unProcessedPayload[0]

        ByteArray(processingLen + 3).also {
            it[0] = identifier
            it[1] = commandID
            it[2] = processingLen.toByte()
            System.arraycopy(unProcessedPayload, 0, it, 3, processingLen)
            ret.add(GeneralGattCommand(buffer = it, deviceIndex = deviceIndex))
            Logger.d(logTag, "sub command: ${HexUtil.encodeHexStr(it)}")
        }

        unProcessedPayload = if (remainingLength - processingLen >= 4) {
            ByteArray(remainingLength - processingLen).also { System.arraycopy(unProcessedPayload, processingLen,
                it, 0, remainingLength - processingLen) }
        } else ByteArray(0)
    }
    return ret
}

private fun tryFillDeviceIndex(generalGattCommand:GeneralGattCommand, logTag: String): ByteArray {
    var index = 0
    var retOffset = 0
    val originPayload = generalGattCommand.payload
    val originPayloadLen = originPayload?.size ?: 0
    val originCommandId = generalGattCommand.commandID
    var ret = byteArrayOf().append(originPayload)
    var currDevIndex = PRIMARY_DEVICE_INDEX

    Logger.d(logTag, "originPayload: ${HexUtil.encodeHexStr(originPayload)}")

    while (index < originPayloadLen) {

        if (originPayloadLen >= 6
            && (originCommandId == GattPacketFormat.RESP_DEV_INFO
            || originCommandId == GattPacketFormat.RESP_PLAYER_INFO
            || originCommandId == GattPacketFormat.RESP_LIGHT_INFO
            || originCommandId == GattPacketFormat.RET_SIMPLE_EQ
            || originCommandId == GattPacketFormat.DEVICE_ANALYTICS_RESPONSE)) {
            // for command with device index
            if (originPayload?.get(index) == PRIMARY_DEVICE_INDEX
                || originPayload?.get(index) == SECONDARY_DEVICE_INDEX) {

                currDevIndex = originPayload[index]
                index += if (index + 2 > originPayload.size) {
                    originPayload.size
                } else {
                    (3 + originPayload[index + 2])
                }
                Logger.d(logTag, "with deviceIdx, position: $index, currDevIndex: $currDevIndex")

            } else {
                ret = ret.insert(index + retOffset, currDevIndex)
                retOffset ++

                index += if (index + 1 > originPayloadLen) {
                    originPayloadLen
                } else {
                    (2 + (originPayload?.get(index + 1) ?: originPayloadLen).toInt())
                }
                Logger.d(
                    logTag,
                    "without deviceIdx, position: $index, currDevIndex: $currDevIndex"
                )
            }
        } else { // for command without device index
            Logger.d(logTag, "command without deviceIdx")
            index += originPayloadLen
        }
    }
    Logger.d(logTag, "after fill in device index: ${HexUtil.encodeHexStr(ret)}")

    return ret
}