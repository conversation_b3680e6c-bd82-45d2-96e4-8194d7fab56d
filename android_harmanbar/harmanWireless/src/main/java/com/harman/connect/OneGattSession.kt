package com.harman.connect

import android.annotation.SuppressLint
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import androidx.annotation.AnyThread
import androidx.annotation.IntRange
import androidx.annotation.WorkerThread
import com.harman.command.WriteBytesCharacteristicException
import com.harman.command.one.AttachOneCommand
import com.harman.command.one.AttachOneGattCommand
import com.harman.command.one.EnumCommandMapping
import com.harman.command.one.EnumCommandMapping.Companion.wifiCmd
import com.harman.command.one.OneCommandProcessor
import com.harman.command.one.bean.APListResponse
import com.harman.command.one.bean.AlexaCBLStatusResponse
import com.harman.command.one.bean.AuraCastSqModelResponse
import com.harman.command.one.bean.BasicResponse
import com.harman.command.one.bean.BatteryStatusResponse
import com.harman.command.one.bean.BleGetEncryptionKeyResponse
import com.harman.command.one.bean.C4aPermissionStatusResponse
import com.harman.command.one.bean.Calibration
import com.harman.command.one.bean.CalibrationState
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.ColorPickerResp
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DJPad
import com.harman.command.one.bean.DJPadResponse
import com.harman.command.one.bean.DebugMode
import com.harman.command.one.bean.DeviceNameRspUnion
import com.harman.command.one.bean.EQListResponse
import com.harman.command.one.bean.EQResponse
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.EnumVoiceSource
import com.harman.command.one.bean.FeatureSupportResponse
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigResp
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.GetAutoPowerOffTimerRsp
import com.harman.command.one.bean.GetBatterySavingStatusRsp
import com.harman.command.one.bean.GetBluetoothConfigRsp
import com.harman.command.one.bean.GetChromeCastOptInResponse
import com.harman.command.one.bean.GetDeviceInfoResponse
import com.harman.command.one.bean.GetDeviceNameResponse
import com.harman.command.one.bean.GetFeedbackToneRsp
import com.harman.command.one.bean.GetGroupInfoRsp
import com.harman.command.one.bean.GetGroupParameterRsp
import com.harman.command.one.bean.GetLightInfoResponse
import com.harman.command.one.bean.GetOtaAccessPointResponse
import com.harman.command.one.bean.GetOtaStatusResponse
import com.harman.command.one.bean.GetProductUsageResponse
import com.harman.command.one.bean.GetSleepTimerRsp
import com.harman.command.one.bean.GetSmartBtnConfigRsp
import com.harman.command.one.bean.GetSmartModeRsp
import com.harman.command.one.bean.GetSoundscapeV2ConfigResponse
import com.harman.command.one.bean.GetSoundscapeV2StateResponse
import com.harman.command.one.bean.GetStreamingStatusRsp
import com.harman.command.one.bean.GroupDeviceOTAStatusRsp
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.MediaResponse
import com.harman.command.one.bean.MetaDataNotify
import com.harman.command.one.bean.NotifySoundscapeV2MusicState
import com.harman.command.one.bean.PreviewSoundscapeRequest
import com.harman.command.one.bean.RearSpeakerVolumeResponse
import com.harman.command.one.bean.SendAppControllerRequest
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SetVoiceLanguageRequest
import com.harman.command.one.bean.SetVoiceToneRequest
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.StatusReq
import com.harman.command.one.bean.StatusResp
import com.harman.command.one.bean.ValueResponse
import com.harman.command.one.bean.VolumeAndMuteResponse
import com.harman.command.one.bean.WifiSetupEndResponse
import com.harman.command.one.bean.statusOff
import com.harman.command.one.bean.statusOn
import com.harman.command.one.command.AlexaCBLLogoutCommand
import com.harman.command.one.command.AlexaCBLStatusCommand
import com.harman.command.one.command.CancelCalibrationCommand
import com.harman.command.one.command.CancelDemoSoundCommand
import com.harman.command.one.command.CancelSoundscapeCommand
import com.harman.command.one.command.ClearHistoryOneOSVersionCommand
import com.harman.command.one.command.ControlSoundscapeV2Command
import com.harman.command.one.command.DestroyCastGroupCommand
import com.harman.command.one.command.EnterAuraCastCommand
import com.harman.command.one.command.EnumMuteStatus
import com.harman.command.one.command.ExitAuraCastCommand
import com.harman.command.one.command.GetAPListCommand
import com.harman.command.one.command.GetAuraCastSqModeCommand
import com.harman.command.one.command.GetAutoPowerOffTimerCommand
import com.harman.command.one.command.GetBatterySavingStatusCommand
import com.harman.command.one.command.GetBatteryStatusCommand
import com.harman.command.one.command.GetBluetoothConfigCommand
import com.harman.command.one.command.GetC4APermissionStatusCommand
import com.harman.command.one.command.GetCalibrationStateCommand
import com.harman.command.one.command.GetChromeCastOptInCommand
import com.harman.command.one.command.GetColorPickerCommand
import com.harman.command.one.command.GetDJPadCommand
import com.harman.command.one.command.GetDevInfoCommand
import com.harman.command.one.command.GetDeviceNameCommand
import com.harman.command.one.command.GetEQCommand
import com.harman.command.one.command.GetEQListCommand
import com.harman.command.one.command.GetFeatureSupportCommand
import com.harman.command.one.command.GetFeedbackToneConfigCommand
import com.harman.command.one.command.GetGeneralConfigCommand
import com.harman.command.one.command.GetGroupDevicesFlagCommand
import com.harman.command.one.command.GetGroupDevicesOtaStatusCommand
import com.harman.command.one.command.GetGroupInfoCommand
import com.harman.command.one.command.GetGroupParameterCommand
import com.harman.command.one.command.GetMediaSourceStatusCommand
import com.harman.command.one.command.GetLightInfoCommand
import com.harman.command.one.command.GetOOBEEncryptionKeyCommand
import com.harman.command.one.command.GetOtaAccessPointCommand
import com.harman.command.one.command.GetOtaStatusCommand
import com.harman.command.one.command.GetPersonalListeningModeCommand
import com.harman.command.one.command.GetProductUsageCommand
import com.harman.command.one.command.GetRearSpeakerStatus
import com.harman.command.one.command.GetRearSpeakerVolumeCommand
import com.harman.command.one.command.GetSleepTimerCommand
import com.harman.command.one.command.GetSmartBtnConfigCommand
import com.harman.command.one.command.GetSmartModeCommand
import com.harman.command.one.command.GetSoundscapeV2ConfigCommand
import com.harman.command.one.command.GetSoundscapeV2StateCommand
import com.harman.command.one.command.GetStreamingStatusCommand
import com.harman.command.one.command.GetVolumeAndMuteCommand
import com.harman.command.one.command.GroupCalibrationCommand
import com.harman.command.one.command.NextMusicCommand
import com.harman.command.one.command.OneGattCommand
import com.harman.command.one.command.PlayDemoSoundCommand
import com.harman.command.one.command.PlayPartySoundCommand
import com.harman.command.one.command.PlayPauseMusicCommand
import com.harman.command.one.command.PrevMusicCommand
import com.harman.command.one.command.PreviewSoundscapeCommand
import com.harman.command.one.command.RenameGroupCommand
import com.harman.command.one.command.ReqDevOtaCommand
import com.harman.command.one.command.ResetLightPatternColorCommand
import com.harman.command.one.command.SendAppControllerCommand
import com.harman.command.one.command.SetActiveEQCommand
import com.harman.command.one.command.SetAuraCastSqModeCommand
import com.harman.command.one.command.SetAuthCancelCommand
import com.harman.command.one.command.SetAuthPairCommand
import com.harman.command.one.command.SetAuthStartCommand
import com.harman.command.one.command.SetAutoPowerOffTimerCommand
import com.harman.command.one.command.SetBatterySavingStatusCommand
import com.harman.command.one.command.SetBluetoothConfigCommand
import com.harman.command.one.command.SetC4APermissionStatusCommand
import com.harman.command.one.command.SetCalibrationCommand
import com.harman.command.one.command.SetCastGroupCommand
import com.harman.command.one.command.SetChromeCastOptInCommand
import com.harman.command.one.command.SetColorPickerCommand
import com.harman.command.one.command.SetDJPadCommand
import com.harman.command.one.command.SetDevNameCommand
import com.harman.command.one.command.SetDjEventCommand
import com.harman.command.one.command.SetEQCommand
import com.harman.command.one.command.SetFactoryRestoreCommand
import com.harman.command.one.command.SetFeedbackToneConfigCommand
import com.harman.command.one.command.SetGeneralConfigCommand
import com.harman.command.one.command.SetIRLearnCommand
import com.harman.command.one.command.SetLightInfoCommand
import com.harman.command.one.command.SetPersonalListeningModeCommand
import com.harman.command.one.command.SetSleepTimerCommand
import com.harman.command.one.command.SetSmartBtnConfigCommand
import com.harman.command.one.command.SetSmartModeCommand
import com.harman.command.one.command.SetSoundscapeV2ConfigCommand
import com.harman.command.one.command.SetVolumeAndMuteCommand
import com.harman.command.one.command.SetWifiCountryCodeCommand
import com.harman.command.one.command.SetWifiNetworkCommand
import com.harman.command.one.command.SkipDemoSoundCommand
import com.harman.command.one.command.SwitchStereoChannelCommand
import com.harman.command.one.command.TriggerCastLEDCommand
import com.harman.connect.listener.IOneDeviceListener
import com.harman.connect.session.OneBusiness
import com.harman.discover.LightInfoExt
import com.harman.discover.bean.bt.GattListenerProxy
import com.harman.discover.bean.bt.OneBTDevice
import com.harman.discover.info.OneAuracastRole
import com.harman.discover.info.OneRole
import com.harman.discover.util.Tools.sync
import com.harman.log.Logger
import com.harmanbar.ble.entity.BLEResponse
import com.harmanbar.ble.statistic.StatisticConstant
import com.harmanbar.ble.utils.BLEUtil
import com.harmanbar.ble.utils.GsonUtil
import com.harmanbar.ble.utils.HexUtil
import com.harmanbar.clj.fastble.BleManager
import com.harmanbar.clj.fastble.bluetooth.SplitWriter
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import org.json.JSONObject
import java.util.Queue
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.math.min
import com.harman.command.one.bean.GetGroupDevicesFlagRsp
import kotlinx.coroutines.Job


/**
 * Created by gerrardzhang on 2024/2/20.
 *
 * There's no need for waiting notify after write characteristic like [PartyBoxGattSession] or [PortableGattSession]
 * Keep writing after last [sendingBytes]
 */
class OneGattSession(
    bleAddress: String?,
    device: OneBTDevice,
    gattListenerProxy: GattListenerProxy
) : BaseGattSession<OneBTDevice, OneRole, OneAuracastRole>(
    bleAddress = bleAddress,
    device = device,
    listenerProxy = gattListenerProxy
), OneBusiness {

    /**
     * JBL One use the same UUID between txUUID and rxUUID
     */
    override val rxUUID: UUID = txUUID

    override val delayMillsBeforeDiscoverServices: Long = 500L

    override val delayMillsBeforeRequestMTU: Long = 500L

    private val unCatchLock = ReentrantLock()

    @Volatile
    private var unCatchAllRsp: BLEResponse? = null

    /*
     * TODO: not a best practise to map req. and rsp. command cause one device didn't block like
     *  a waiting queue as PartyBox did.
     */
    @Volatile
    private var lastReqCommand: OneGattCommand? = null

    /**
     * 0x1001
     */
    override fun getDeviceInfo(port: String?) {
        sendOrWait(GetDevInfoCommand())
    }

    /**
     * Ox1801
     */
    override fun sendSetDeviceName(name: String, port: String?) {
        sendOrWait(SetDevNameCommand(name = name))
    }

    /**
     * 0x1007
     * This is a long response command.
     */
    override fun getAPList(port: String?) {
        sendOrWait(GetAPListCommand())
    }

    /**
     * 0x1D01
     * This is a long request command.
     */
    override fun setCastGroup(payloadJson: String, port: String?) {
        sendOrWait(SetCastGroupCommand(payloadJson = payloadJson))
    }

    /**
     * 0x1812
     */
    override fun playMusic() {
        Logger.d(TAG, "playMusic() >>> ")
        sendOrWait(PlayPauseMusicCommand())
        loopListeners { listener ->
            listener.onPlayStatus(isPlay = true)
        }
    }

    /**
     * 0x1812
     */
    override fun pauseMusic() {
        Logger.d(TAG, "pauseMusic() >>> ")
        sendOrWait(PlayPauseMusicCommand())
        loopListeners { listener ->
            listener.onPlayStatus(isPlay = false)
        }
    }

    /**
     * 0x1812
     */
    override fun prevMusic() {
        sendOrWait(PrevMusicCommand())
    }

    /**
     * 0x1812
     */
    override fun nextMusic() {
        sendOrWait(NextMusicCommand())
    }

    /**
     * 0x1817
     */
    override fun setRemoteVolume(@IntRange(from = 0L, to = 100L) value: Int) {
        Logger.d(TAG, "setRemoteVolume() >>> value[$value]")
        sendOrWait(SetVolumeAndMuteCommand(volume = value, mute = EnumMuteStatus.UN_MUTE))
        loopListeners { listener ->
            listener.onVolume(volume = value)
        }
    }

    /**
     * 0x1818
     */
    override fun getRemoteVolume() {
        sendOrWait(GetVolumeAndMuteCommand())
    }

    /**
     * 0x1011
     */
    override fun getFeatureSupport(port: String?) {
        sendOrWait(GetFeatureSupportCommand())
    }

    /**
     * 0x1917
     */
    override fun getEQList(port: String?) {
        sendOrWait(GetEQListCommand())
    }

    /**
     * 0x1916
     */
    override fun setActiveEQ(item: SetActiveEQItemRequest, port: String?) {
        sendOrWait(SetActiveEQCommand(item = item))
    }

    /**
     * 0x1902
     */
    override fun getEQ(port: String?) {
        sendOrWait(GetEQCommand())
    }

    /**
     * 0x1901
     */
    override fun setEQ(eq: EQSetting, port: String?) {
        sendOrWait(SetEQCommand(eq = eq))
    }

    /**
     * 0x100E
     */
    override fun startAuth(timeoutSecs: Int, port: String?) {
        sendOrWait(SetAuthStartCommand(timeoutSecs = timeoutSecs))
    }

    /**
     * 0x1010
     */
    override fun cancelAuth(port: String?) {
        sendOrWait(SetAuthCancelCommand())
    }

    override fun authPair(timeoutSecs: Int, port: String?) {
        sendOrWait(SetAuthPairCommand(timeoutSecs = timeoutSecs))
    }

    /**
     * 0x1002
     */
    override fun setWifiNetwork(req: SetWifiNetworkRequest, port: String?) {
        sendOrWait(SetWifiNetworkCommand(req = req))
    }

    /**
     * 0x1306
     */
    override fun getOtaStatus(port: String?) {
        sendOrWait(GetOtaStatusCommand())
    }

    /**
     * 0x1307
     */
    override fun requestDeviceOta(port: String?) {
        sendOrWait(ReqDevOtaCommand())
    }

    /**
     * 0x1308
     */
    override fun getOtaAccessPoint(port: String?) {
        sendOrWait(GetOtaAccessPointCommand())
    }

    /**
     * 0x1815
     */
    override fun getBatteryStatus(port: String?) {
        sendOrWait(GetBatteryStatusCommand())
    }

    /**
     * 0x182D
     */
    override fun getSoundscapeV2State(port: String?) {
        sendOrWait(GetSoundscapeV2StateCommand())
    }

    override fun getProductUsage(port: String?) {
        sendOrWait(GetProductUsageCommand())
    }

    /**
     * 0x1905
     */
    override fun setCalibration(port: String?) {
        sendOrWait(SetCalibrationCommand())
    }

    /**
     * 0x1913
     */
    override fun getCalibrationState(port: String?) {
        sendOrWait(GetCalibrationStateCommand())
    }

    /**
     * 0x190F
     */
    override fun cancelCalibration(port: String?) {
        sendOrWait(CancelCalibrationCommand())
    }

    /**
     * 0x1B01
     */
    override fun setC4aPermissionStatus(status: EnumC4aPermissionStatus, port: String?) {
        sendOrWait(SetC4APermissionStatusCommand(status = status))
    }

    /**
     * 0x1B02
     */
    override fun getC4aPermissionStatus(port: String?) {
        sendOrWait(GetC4APermissionStatusCommand())
    }

    /**
     * 0x1B04
     */
    override fun getChromeCastOptIn(port: String?) {
        sendOrWait(GetChromeCastOptInCommand())
    }

    /**
     * 0x1B03
     */
    override fun setChromeCastOptIn(optIn: ChromeCastOpt, port: String?) {
        sendOrWait(SetChromeCastOptInCommand(opt = optIn))
    }

    override fun setDebugMode(debugMode: DebugMode, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    /**
     * 0x1209
     */
    override fun alexaCBLStatus(port: String?) {
        sendOrWait(AlexaCBLStatusCommand())
    }

    /**
     * 0x120A
     */
    override fun alexaCBLLogout(port: String?) {
        sendOrWait(AlexaCBLLogoutCommand())
    }

    override fun getLWAState(port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun requestLWALogout(port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun setLWAAuthCode(lwaInfo: LWAInfo, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun setVoiceLanguage(setVoiceLanguageRequest: SetVoiceLanguageRequest, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun setVoiceRequestEndTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun setVoiceRequestStartTone(setVoiceToneRequest: SetVoiceToneRequest, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun getVoiceLanguage(source: EnumVoiceSource, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun getSupportedVoiceLanguage(source: EnumVoiceSource, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun requestGoogleLogout(port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun getVoiceRequestEndTone(source: EnumVoiceSource, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun getVoiceRequestStartTone(source: EnumVoiceSource, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    /**
     * 0x1704
     */
    override fun setCountryCode(code: String, port: String?) {
        sendOrWait(SetWifiCountryCodeCommand(code = code))
    }

    /**
     * 0x1F01
     */
    override fun enterAuraCast(port: String?) {
        sendOrWait(EnterAuraCastCommand())
    }

    /**
     * 0x1F02
     */
    override fun exitAuraCast(port: String?) {
        sendOrWait(ExitAuraCastCommand())
    }

    override fun getAuraCastSqMode(port: String?) {
        sendOrWait(GetAuraCastSqModeCommand())
    }

    override fun setAuraCastSqMode(on: Boolean, port: String?) {
        sendOrWait(SetAuraCastSqModeCommand(on))
    }

    /**
     * 0x1E03
     */
    override fun setPlayPartySound(partySound: Int, port: String?) {
        val payload = String.format("{\"index\":%d}", partySound)
        sendOrWait(PlayPartySoundCommand(payload))
    }

    /**
     * 0x1E06
     */
    override fun setDjEvent(value: String, port: String?) {
        val payload = String.format("{\"set\": \"%s\"}}", value)
        sendOrWait(SetDjEventCommand(payload))
    }

    /**
     * 0x181C
     */
    override fun getSmartBtnConfig(port: String?) {
        sendOrWait(GetSmartBtnConfigCommand())
    }

    /**
     * 0x181B
     */
    override fun setSmartBtnConfig(config: SmartBtnConfig, port: String?) {
        sendOrWait(SetSmartBtnConfigCommand(config = config))
    }

    /**
     * 0x1821
     */
    override fun previewSoundscape(request: PreviewSoundscapeRequest, port: String?) {
        sendOrWait(PreviewSoundscapeCommand(request = request))
    }

    /**
     * 0x1822
     */
    override fun cancelSoundscape(port: String?) {
        sendOrWait(CancelSoundscapeCommand())
    }

    /**
     * 0x1830
     */
    override fun getSoundscapeV2Config(port: String?) {
        sendOrWait(GetSoundscapeV2ConfigCommand())
    }

    /**
     * 0x182F
     */
    override fun setSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest, port: String?) {
        sendOrWait(SetSoundscapeV2ConfigCommand(request = req))
    }

    /**
     * 0x182C
     */
    override fun controlSoundscapeV2(req: ControlSoundscapeV2, port: String?) {
        sendOrWait(ControlSoundscapeV2Command(request = req))
    }

    /**
     * 0x1802
     */
    override fun getDeviceName(port: String?) {
        sendOrWait(GetDeviceNameCommand())
    }

    override fun playDemoSound(port: String?) {
        sendOrWait(PlayDemoSoundCommand())
    }

    override fun cancelDemoSound(port: String?) {
        sendOrWait(CancelDemoSoundCommand())
    }

    override fun sendAppController(req: SendAppControllerRequest, port: String?) {
        sendOrWait(SendAppControllerCommand(req = req))
    }

    override fun getGeneralConfig(type: GeneralConfigType, port: String?) {
        sendOrWait(GetGeneralConfigCommand(type))
    }

    override fun setGeneralConfig(req: GeneralConfig, port: String?) {
        sendOrWait(SetGeneralConfigCommand(req))
    }

    /**
     * 0x1810
     */
    override fun getRearSpeakerVolume(port: String?) {
        sendOrWait(GetRearSpeakerVolumeCommand())
    }

    /**
     * 0x1809
     */
    override fun setFactoryRestore(port: String?) {
        sendOrWait(SetFactoryRestoreCommand())
    }

    /**
     * 0x181D
     */
    override fun setAutoPowerOffTimer(secs: Int, port: String?) {
        sendOrWait(SetAutoPowerOffTimerCommand(secs = secs))
    }

    /**
     * 0x181E
     */
    override fun getAutoPowerOffTimer(port: String?) {
        sendOrWait(GetAutoPowerOffTimerCommand())
    }

    /**
     * 0x1813
     */
    override fun setBluetoothConfig(config: EnumBluetoothConfig, port: String?) {
        sendOrWait(SetBluetoothConfigCommand(config = config))
    }

    /**
     * 0x1814
     */
    override fun getBluetoothConfig(port: String?) {
        sendOrWait(GetBluetoothConfigCommand())
    }

    /**
     * 0x181F
     */
    override fun setFeedbackToneConfig(config: EnumFeedbackToneConfig, port: String?) {
        sendOrWait(SetFeedbackToneConfigCommand(config = config))
    }

    /**
     * 0x1820
     */
    override fun getFeedbackToneConfig(port: String?) {
        sendOrWait(GetFeedbackToneConfigCommand())
    }

    /**
     * 0x1823
     */
    override fun setBatterySavingStatus(status: EnumBatterySavingStatus, port: String?) {
        sendOrWait(SetBatterySavingStatusCommand(status = status))
    }

    /**
     * 0x1824
     */
    override fun getBatterySavingStatus(port: String?) {
        sendOrWait(GetBatterySavingStatusCommand())
    }

    /**
     * 0x180A
     */
    override fun setIRLearn(port: String?) {
        sendOrWait(SetIRLearnCommand())
    }

    override fun getDJPad(port: String?) {
        sendOrWait(GetDJPadCommand())
    }

    override fun setDJPad(djPad: DJPad, port: String?) {
        sendOrWait(SetDJPadCommand(djPad))
    }

    /**
     * 0x1D07
     */
    override fun triggerCastLED(port: String?) {
        sendOrWait(TriggerCastLEDCommand())
    }

    /**
     * 0x1707
     */
    override fun clearHistoryOneOSVersion(port: String?) {
        sendOrWait(ClearHistoryOneOSVersionCommand())
    }

    override fun setAnchorIndicate(timeoutSecs: Int, port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    override fun setAnchorCancel(port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    /**
     * 0x1D02
     */
    override fun destroyCastGroup(port: String?) {
        sendOrWait(DestroyCastGroupCommand())
    }

    /**
     * 0x1D03
     */
    override fun getGroupInfo(port: String?) {
        sendOrWait(GetGroupInfoCommand())
    }

    /**
     * 0x1D04
     */
    override fun groupCalibration(anchor: String, step: Int, port: String?) {
        sendOrWait(GroupCalibrationCommand(anchor, step))
    }

    /**
     * 0x1D06
     */
    override fun switchStereoChannel(port: String?) {
        sendOrWait(SwitchStereoChannelCommand())
    }

    /**
     * 0x1D0A
     */
    override fun skipDemoSound(port: String?) {
        sendOrWait(SkipDemoSoundCommand())
    }

    /**
     * 0x1D0B
     */
    override fun getGroupParameter(port: String?) {
        sendOrWait(GetGroupParameterCommand())
    }

    override fun getGroupCalibrationState(port: String?) {
        // do nothing cause this is a WiFi session only command
    }

    /**
     * 0x1D05
     */
    override fun renameGroup(name: String, port: String?) {
        sendOrWait(RenameGroupCommand(name))
    }

    /**
     * 0x1912
     */
    override fun getStreamingStatus(port: String?) {
        sendOrWait(GetStreamingStatusCommand())
    }

    /**
     * 0x1807
     */
    override fun setSleepTimer(secs: Int, port: String?) {
        sendOrWait(SetSleepTimerCommand(secs = secs))
    }

    /**
     * 0x1808
     */
    override fun getSleepTimer(port: String?) {
        sendOrWait(GetSleepTimerCommand())
    }

    override fun getPersonalListeningMode(port: String?) {
        sendOrWait(GetPersonalListeningModeCommand())
    }

    override fun setPersonalListeningMode(isOn: Boolean, port: String?) {
        sendOrWait(SetPersonalListeningModeCommand(StatusReq(if (isOn) statusOn else statusOff)))
    }

    /**
     * 0x1806
     */
    override fun getMediaSourceStatus(port: String?) {
        sendOrWait(GetMediaSourceStatusCommand())
    }

    override fun getColorPicker(port: String?) {
        sendOrWait(GetColorPickerCommand())
    }

    override fun setColorPicker(picker: ColorPicker, port: String?) {
        sendOrWait(SetColorPickerCommand(picker))
    }

    override fun getOOBEEncryptionKey(port: String?) {
        sendOrWait(GetOOBEEncryptionKeyCommand())
    }

    override fun setTimeZone(formatDate: String, port: String?) {
        // no impl
    }

    /**
     * 0x1828
     */
    override fun setSmartMode(isOn: Boolean, port: String?) {
        sendOrWait(SetSmartModeCommand(isOn = isOn))
    }

    /**
     * 0x1829
     */
    override fun getSmartMode(port: String?) {
        sendOrWait(GetSmartModeCommand())
    }

    /**
     * 0x1825
     */
    override fun getRearSpeakerStatus(port: String?) {
        sendOrWait(GetRearSpeakerStatus())
    }

    /**
     * 0x1833
     */
    override fun setLightInfo(req: SetLightInfoRequest, port: String?) {
        val command = HexUtil.byte2HexStr(SetLightInfoCommand(req).sendBytes)
        val parseBeanToJson = GsonUtil.parseBeanToJson(req)
        Logger.d(TAG, "LIGHT_INFO_RELATED setLightInfo() >>> UUID[${device.UUID}] parseBeanToJson:${parseBeanToJson}")
        Logger.d(TAG, "LIGHT_INFO_RELATED setLightInfo() >>> UUID[${device.UUID}] command:${command}")
        sendOrWait(SetLightInfoCommand(req))
    }

    /**
     * 0x1834
     */
    override fun getLightInfo(port: String?) {
        val command = HexUtil.byte2HexStr(GetLightInfoCommand().sendBytes)
        Logger.d(TAG, "LIGHT_INFO_RELATED getLightInfo() >>> UUID[${device.UUID}] command:${command}")
        sendOrWait(GetLightInfoCommand())
    }

    override fun resetLightPatternColor(id: String, port: String?) {
        val command = HexUtil.byte2HexStr(ResetLightPatternColorCommand(id).sendBytes)
        Logger.d(TAG, "LIGHT_INFO_RELATED resetLightPatternColor() >>> UUID[${device.UUID}] command:${command}")
        sendOrWait(ResetLightPatternColorCommand(id))
    }

    /**
     * 0x1D12
     */
    override fun getGroupDevicesOtaStatus(port: String?) {
        sendOrWait(GetGroupDevicesOtaStatusCommand())
    }

    /**
     * 0x1D13
     */
    override fun getGroupDevicesFlag(port: String?) {
        sendOrWait(GetGroupDevicesFlagCommand())
    }

    fun sendCommand(command: OneGattCommand) {
        sendOrWait(command)
    }

    /**
     * This variable is used to provide a fixed thread context for coroutines that send commands
     */
    @OptIn(DelicateCoroutinesApi::class)
    private val sendCmdThreadCtx by lazy {
        newSingleThreadContext("${device.UUID}-sendCmdThread")
    }

    /**
     * This variable is used to provide a fixed thread context for coroutines that send commands
     */
    @OptIn(DelicateCoroutinesApi::class)
    private val sendCmdThreadCtxSecure by lazy {
        newSingleThreadContext("${device.UUID}-sendCmdThreadSecure")
    }

    /**
     * Stash the pending Job launched by [sendOrWait] by [OneGattCommand.commandID]
     * if this command is [OneGattCommand.displaceSameInQueue] to avoid too much same command waiting.
     */
    private val waitingCmdQueue = ConcurrentHashMap<Int, Job>()

    /**
     * [OneGattCommand] will be sent one by one,and a [OneGattSession] instance will has only one thread(by [sendCmdThreadCtx]) that will
     * be responsible for processing commands and queued for sending,and if there is currently a command
     * being sent when this method is called, it will wait.
     */
    @AnyThread
    private fun sendOrWait(command: OneGattCommand) {
        if (command.displaceSameInQueue) {
            waitingCmdQueue.remove(command.commandID)?.let { job ->
                Logger.w(TAG, "sendOrWait() >>> dequeue job and cancel it. command[${command.commandID}]")
                job.cancel()
            }
        }

        val secure = device.isSecureBleSupport && secureConnect && null != secureTxCharacteristic
        Logger.d(TAG, "sendOrWait() >>> secure[$secure] command[${command.commandID}] payload:${HexUtil.byte2HexStr(command.sendBytes)}")

        val ctx = if (secure) sendCmdThreadCtxSecure else sendCmdThreadCtx

        val job = launch(ctx) {
            Logger.d(TAG, "sendOrWait() after launch >>> secure[$secure] command[${command.commandID}] payload:${HexUtil.byte2HexStr(command.sendBytes)}")
            waitingCmdQueue.remove(command.commandID)

            val sendBytes = command.sendBytes
            val sendBytesQueue = SplitWriter.splitByte(sendBytes, mtuSize)
            if (sendBytesQueue.isEmpty()) {
                notifyException(Exception("this command: $command send bytes is empty."))
                return@launch
            }

            lastReqCommand = command
            for (eachPartBytes in sendBytesQueue) {
                val eachPartWriteRet =
                    writeCmdPartBytes(eachPartBytes, secure, needWaiting = EnumCommandMapping.AUTH_PAIR.bleCmd != command.commandID)
                if (!eachPartWriteRet) {
                    lastReqCommand = null
                    break
                }
            }
            Logger.d(TAG, "sendOrWait() sendCmdThreadCtx finish >>> secure[$secure] command[${command.commandID}] payload:${HexUtil.byte2HexStr(command.sendBytes)}}")
        }

        if (command.displaceSameInQueue) {
            waitingCmdQueue[command.commandID] = job
            Logger.d(TAG, "sendOrWait() >>> enqueue job. command[${command.commandID}] payload:${HexUtil.byte2HexStr(command.sendBytes)}}")
        }
    }

    @WorkerThread
    private fun writeCmdPartBytes(partBytes: ByteArray, secure: Boolean? = false, needWaiting: Boolean? = true): Boolean {
        if (!write(partBytes, secure)) {
            Logger.w(TAG, "[$bleAddress], secure[$secure] ConnectedRunnable >>> fail to write:${HexUtil.byte2HexStr(partBytes)}")

            //todo: biz layer dose not know which command happen gatt error
            notifyException(
                e = WriteBytesCharacteristicException(
                    device = <EMAIL>,
                    bytes = partBytes
                )
            )
            return false
        } else {
            Logger.d(TAG, "[$bleAddress], secure[$secure] ConnectedRunnable >>> write success:${HexUtil.byte2HexStr(partBytes)}")
            notifyWriteSuccess(bytes = partBytes)
            //if "gatt.writeCharacteristic" return "true",The next time data is written, you should wait until the "onCharacteristicWrite" callback occurs
            if (true == needWaiting) {
                writeWaitingCountDownLatch = CountDownLatch(1)
            }
            Logger.d(TAG, "[$bleAddress], secure[$secure] ConnectedRunnable >>> writeWaitingCountDownLatch:${writeWaitingCountDownLatch}")
            val canWriteNext = writeWaitingCountDownLatch?.await(60, TimeUnit.SECONDS) ?: true
            Logger.d(TAG, "[$bleAddress], secure[$secure] ConnectedRunnable >>> canWriteNext:${canWriteNext}")
            return canWriteNext
        }
    }

    /**
     * When the write command is queued, the next data piece needs to wait for the
     * [BluetoothGattCallback.onCharacteristicWrite] callback corresponding to
     * the previous data before continuing, and this variable is used to control this sequence
     */
    private var writeWaitingCountDownLatch: CountDownLatch? = null
    override fun onCharacteristicWrite(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?, status: Int) {
        super.onCharacteristicWrite(gatt, characteristic, status)
        if (status == BluetoothGatt.GATT_SUCCESS) {
            Logger.d(
                TAG,
                "ConnectedRunnable onCharacteristicWrite() >>> SUCCESS : $status    raw value: ${BLEUtil.bytesToHexString(characteristic?.value)} "
            )
        } else {
            Logger.e(
                TAG,
                "ConnectedRunnable onCharacteristicWrite() >>> FAIL : $status    raw value: ${BLEUtil.bytesToHexString(characteristic?.value)} "
            )
        }
        writeWaitingCountDownLatch?.countDown()
    }

    override fun handleCharacteristicChanged(
        gatt: BluetoothGatt?,
        characteristic: BluetoothGattCharacteristic?,
        value: ByteArray?
    ) {
        super.handleCharacteristicChanged(gatt, characteristic, value)
        Logger.d(TAG, "handleCharacteristicChanged() >>> raw value: ${BLEUtil.bytesToHexString(value)}")

        unCatchLock.sync {
            val response = try {
                OneCommandProcessor.processCommand(
                    device = device,
                    value = value,
                    unCatchAllRsp = unCatchAllRsp
                )
            } catch (e: Exception) {
                Logger.e(TAG, "handleCharacteristicChanged() >>> Exception while processing command, clear unCatchAllRsp. $e")
                unCatchAllRsp = null
                null
            } ?: return

            if (response.isCatchAll) {
//                Logger.i(TAG, "[${device.bleAddress}] handleCharacteristicChanged() >>> full recv. commandName = ${response.responsedCode.wifiCmd()} response = $response")
                unCatchAllRsp = null
                parseSpecBusinessResponse(response)
                notifyReceived(response)
                lastReqCommand = null
            } else {
                // callback onResponseReceived() cause sometimes fm didn't callback fully data and
                // need business to do workaround.
                unCatchAllRsp = response
                notifyReceived(response)
            }
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onMtuChanged(gatt: BluetoothGatt?, mtu: Int, status: Int) {
        Logger.i(
            TAG, "onMtuChanged() >>> [$bleAddress] mtu[$mtu] status[$status] " +
                    "isSecureBleSupport[${device.isSecureBleSupport}] secureConnect[$secureConnect]"
        )
        if (BluetoothGatt.GATT_SUCCESS == status) {
            if (device.isSecureBleSupport && secureConnect) {
                device.gattSession?.setSecureCharacteristic()
                mtuSize = mtu
                onPrePair()
            } else {
                onGattConnected(mtu = mtu)
            }
        } else {
            Logger.e(TAG, "[$bleAddress] onMtuChanged() >>> illegal status[$status]")
            closeSession()
        }

        // change package size depends on MTU size.if package size larger than 512,app will write data fail.iOS define this to 484 since party box's workaround
        BleManager.getInstance().setSplitWriteNum(min(mtuSize, BleManager.DEFAULT_MAX_MTU))
    }

    /**
     * Parse response/notify and update business params which concerned.
     */
    private fun parseSpecBusinessResponse(response: BLEResponse) {
        when (response.responsedCode) {
            StatisticConstant.GET_VOLUME_LEVEL,
            StatisticConstant.NOTIFY_VOLUME_LEVEL -> {
                val volumeAndMuteResponse = GsonUtil.parseJsonToBean(
                    response.responseData,
                    VolumeAndMuteResponse::class.java
                ) ?: return

                device.onGattVolumeAndMute(volumeAndMuteResponse)
            }

            StatisticConstant.NOTIFY_MEDIA_SOURCE_STATUS,
            StatisticConstant.GET_MEDIA_SOURCE_STATUS -> {
                val mediaRsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    MediaResponse::class.java
                ) ?: return

                device.onMediaSourceStatus(mediaRsp)
            }

            StatisticConstant.GET_FEATURE_SUPPORT -> {
                val featureSupportRsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    FeatureSupportResponse::class.java
                ) ?: return

                val featureSupport = featureSupportRsp.featureSupport ?: return
                device.onFeatureSupport(featureSupport)

                loopListeners { listener ->
                    listener.onFeatureSupport(featureSupport = featureSupport)
                }
            }

            StatisticConstant.GET_EQ_LIST -> {
                val eqListRsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    EQListResponse::class.java
                ) ?: return

                device.onEQListResponse(eqListRsp = eqListRsp)

                loopListeners { listener ->
                    listener.onEQList(eqListRsp)
                }
            }

            StatisticConstant.SET_ACTIVE_EQ -> {
                val basicRsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetActiveEQResult(basicRsp.errorCode)
                }
            }

            StatisticConstant.GET_EQ -> {
                val eqRsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    EQResponse::class.java
                ) ?: return

                device.onEQResponse(eqRsp = eqRsp)

                loopListeners { listener ->
                    listener.onEQ(eqResponse = eqRsp)
                }
            }

            StatisticConstant.SET_EQ -> {
                val basicRsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetEQResult(basicRsp.errorCode)
                }
            }

            StatisticConstant.NOTIFY_META_DATA -> {
                val metaNotify = GsonUtil.parseJsonToBean(
                    response.responseData,
                    MetaDataNotify::class.java
                ) ?: return

                device.onMetaNotify(metaNotify)
            }

            StatisticConstant.LP_GET_AP_LIST_CMD -> {
                val apList = GsonUtil.parseJsonToBean(
                    response.responseData,
                    APListResponse::class.java
                )?.apList ?: return

                loopListeners { listener ->
                    listener.onAPList(apList)
                }
            }

            StatisticConstant.LP_WIFI_SETUP_END -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    WifiSetupEndResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetWifiNetworkResult(rsp = rsp)
                }
            }

            StatisticConstant.LP_GET_OTA_STATUS -> {
                val status = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetOtaStatusResponse::class.java
                )?.otaStatus ?: return

                loopListeners { listener ->
                    listener.onOtaStatus(status = status)
                }
            }

            StatisticConstant.GET_BATTERY,
            StatisticConstant.RESPONSE_BATTERY_STATUS -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BatteryStatusResponse::class.java
                ) ?: return

                device.onBatteryStatus(status = rsp)

                loopListeners { listener ->
                    listener.onBatteryStatus(rsp = rsp)
                }
            }

            StatisticConstant.LP_GET_OTA_ACCESS_POINT -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetOtaAccessPointResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGetOTAAccessPoint(rsp = rsp)
                }
            }

            StatisticConstant.GET_SMART_BUTTON_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetSmartBtnConfigRsp::class.java
                ) ?: return

                rsp.smartConfig?.let { config ->
                    device.onSmartBtnConfig(config = config)
                }

                loopListeners { listener ->
                    listener.onGetSmartBtnConfig(rsp = rsp)
                }
            }

            StatisticConstant.SET_SMART_BUTTON_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                lastReqCommand?.let { cmd ->
                    if (rsp.success() && cmd is SetSmartBtnConfigCommand) {
                        device.onSmartBtnConfig(cmd.config)
                    }
                }
            }

            StatisticConstant.GET_SOUNDSCAPE_V2_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetSoundscapeV2ConfigResponse::class.java
                ) ?: return

                rsp.soundscapeV2List?.let { list ->
                    device.onSoundscapeV2List(list = list)
                }

                loopListeners { listener ->
                    listener.onGetSoundscapeV2Config(rsp = rsp)
                }
            }

            StatisticConstant.SET_SOUNDSCAPE_V2_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                lastReqCommand?.let { cmd ->
                    if (rsp.success() && cmd is SetSoundscapeV2ConfigCommand) {
                        device.onSoundscapeV2Config(req = cmd.request)
                    }
                }

                loopListeners { listener ->
                    listener.onSetSoundscapeV2Config(rsp = rsp)
                }
            }

            StatisticConstant.CONTROL_SOUNDSCAPE_V2 -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                lastReqCommand?.let { cmd ->
                    if (rsp.success() && cmd is ControlSoundscapeV2Command) {
                        device.onControlSoundscapeV2(control = cmd.request)
                    }
                }

                loopListeners { listener ->
                    listener.onControlSoundscapeV2(rsp = rsp)
                }
            }

            StatisticConstant.GET_SOUNDSCAPE_V2_STATE,
            StatisticConstant.NOTIFY_SOUNDSCAPE_V2_STATE -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetSoundscapeV2StateResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onSoundscapeV2State(rsp = rsp)
                }

                loopListeners { listener ->
                    listener.onSoundscapeV2State(rsp = rsp)
                }
            }

            StatisticConstant.LP_GET_DEVICEINFO_CMD -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetDeviceInfoResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onDeviceInfo(info = rsp.deviceInfo)
                }

                loopListeners { listener ->
                    listener.onDeviceInfo(rsp = rsp)
                }
            }

            StatisticConstant.GET_C4A_PERMISSION_STATUS,
            StatisticConstant.SET_C4A_PERMISSION_STATUS -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    C4aPermissionStatusResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onC4APermissionStatus(rsp = rsp)
                }

                loopListeners { listener ->
                    listener.onC4aPermissionStatus(rsp = rsp)
                }
            }

            StatisticConstant.GET_CBL_STATUS -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    AlexaCBLStatusResponse::class.java
                ) ?: return

                device.onAlexaCBLStatus(rsp = rsp)

                loopListeners { listener ->
                    listener.onAlexaCBLStatus(rsp = rsp)
                }
            }

            StatisticConstant.CBL_LOGOUT -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onAlexaCBLStatus(rsp = null)
                }

                loopListeners { listener ->
                    listener.onAlexaCBLLogout(rsp = rsp)
                }
            }

            StatisticConstant.GET_CHROME_CAST_OPT_IN -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetChromeCastOptInResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onChromeCastOptIn(ChromeCastOpt.fromValue(rsp.optIn))
                }

                loopListeners { listener ->
                    listener.onChromecastOptIn(rsp = rsp)
                }
            }

            StatisticConstant.SET_CHROME_CAST_OPT_IN -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                val opt = (lastReqCommand as? SetChromeCastOptInCommand)?.opt

                if (rsp.success()) {
                    device.onChromeCastOptIn(opt)
                }

                loopListeners { listener ->
                    listener.onChromecastOptIn(
                        rsp = GetChromeCastOptInResponse(
                            errorCode = rsp.errorCode,
                            optIn = if (rsp.success()) opt?.value else null
                        )
                    )
                }
            }

            StatisticConstant.GET_PRODUCT_USAGE -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetProductUsageResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onProductUsage(rsp = rsp)
                }
            }

            StatisticConstant.GET_DEVICE_NAME -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetDeviceNameResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onDeviceName(name = rsp.deviceName)

                    loopListeners { listener ->
                        listener.onDeviceName(union = DeviceNameRspUnion(name = rsp.deviceName, editable = rsp.editable()))
                    }
                }
            }

            StatisticConstant.SET_DEVICE_NAME -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                val cmd = lastReqCommand as? SetDevNameCommand

                if (rsp.success() && null != cmd) {
                    device.onDeviceName(name = cmd.name)

                    loopListeners { listener ->
                        listener.onDeviceName(union = DeviceNameRspUnion(name = cmd.name, editable = true))
                    }
                }
            }

            StatisticConstant.SET_CALIBRATION -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetCalibrationResult(rsp = rsp)
                }
            }

            StatisticConstant.GET_CALIBRATION_STATE -> {
                val calibration = GsonUtil.parseJsonToBean(
                    response.responseData,
                    CalibrationState::class.java
                )?.calibration ?: return

                device.onCalibration(calibration = calibration)
                loopListeners { listener ->
                    listener.onCalibration(calibration = calibration)
                }
            }

            StatisticConstant.CANCEL_CALIBRATION -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                if (rsp.success()) {
                    device.onCalibration(Calibration())
                }

                loopListeners { listener ->
                    listener.onCalibrationCancelled(rsp = rsp)
                }
            }

            StatisticConstant.GET_REAR_SPEAKER_STATUS -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    RearSpeakerVolumeResponse::class.java
                ) ?: return

                device.onRearSpeakerVolume(rsp = rsp)

                loopListeners { listener ->
                    listener.onRearSpeakerVolume(rsp = rsp)
                }
            }

            StatisticConstant.SET_FACTORY_RESTORE -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onRestoreFactory(success = rsp.success())
                }
            }

            StatisticConstant.GET_AUTO_POWER_OFF_TIMER -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetAutoPowerOffTimerRsp::class.java
                ) ?: return

                val secs = rsp.timer ?: return
                device.onAutoPowerOff(secs = secs)

                loopListeners { listener ->
                    listener.onAutoPowerOffTimer(secs = secs)
                }
            }

            StatisticConstant.SET_AUTO_POWER_OFF_TIMER -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                val cmd = lastReqCommand as? SetAutoPowerOffTimerCommand
                if (rsp.success()) {
                    device.onAutoPowerOff(secs = cmd?.secs)
                }
            }

            StatisticConstant.SET_BLUETOOTH_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                val cmd = lastReqCommand as? SetBluetoothConfigCommand
                if (rsp.success()) {
                    device.onBluetoothConfig(config = cmd?.config)
                }
            }

            StatisticConstant.GET_BLUETOOTH_CONFIG -> {
                val config = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetBluetoothConfigRsp::class.java
                )?.enum ?: return

                device.onBluetoothConfig(config = config)

                loopListeners { listener ->
                    listener.onBluetoothConfig(config = config)
                }
            }

            StatisticConstant.SET_FEED_BACK_TONE_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                val cmd = lastReqCommand as? SetFeedbackToneConfigCommand
                if (rsp.success()) {
                    device.onFeedbackToneConfig(config = cmd?.config)
                }
            }

            StatisticConstant.GET_FEED_BACK_TONE_CONFIG -> {
                val config = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetFeedbackToneRsp::class.java
                )?.enum ?: return

                device.onFeedbackToneConfig(config = config)

                loopListeners { listener ->
                    listener.onFeedbackToneConfig(config = config)
                }
            }

            StatisticConstant.GET_GENERAL_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    ValueResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGeneralConfig(rsp)
                }
            }

            StatisticConstant.GET_BATTERY_SAVING_MODE -> {
                val status = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetBatterySavingStatusRsp::class.java
                )?.enum ?: return

                device.onBatterySavingStatus(status = status)

                loopListeners { listener ->
                    listener.onBatterySavingStatus(status = status)
                }
            }

            StatisticConstant.SET_BATTERY_SAVING_MODE -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                val cmd = lastReqCommand as? SetBatterySavingStatusCommand
                if (rsp.success()) {
                    device.onBatterySavingStatus(status = cmd?.status)
                }
            }

            StatisticConstant.SET_IR_LEARN -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetIRLearn(rsp.success())
                }
            }

            StatisticConstant.LP_SET_CAST_GROUP -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetCastGroup(rsp.success())
                }
            }

            StatisticConstant.NOTIFY_GENERAL_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GeneralConfigResp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGeneralConfigNotify(rsp.payload)
                }
            }

            StatisticConstant.GET_DJ_PAD -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    DJPadResponse::class.java,
                ) ?: return
                loopListeners { listener ->
                    listener.onDJPad(rsp)
                }
            }

            StatisticConstant.SET_GENERAL_CONFIG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetGeneralConfig(rsp.success())
                }
            }

            StatisticConstant.LP_TRIGGER_CAST_LED -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onTriggerCastLED(rsp.success())
                }
            }

            StatisticConstant.LP_DESTROY_CAST_GROUP -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onDestroyCastGroup(rsp.success())
                }
            }

            StatisticConstant.LP_GROUP_CALIBRATION -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGroupCalibration(rsp.success())
                }
            }

            StatisticConstant.LP_SWITCH_STEREO_CHANNEL -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSwitchStereoChannel(rsp.success())
                }
            }

            StatisticConstant.LP_SKIP_DEMO_SOUND -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSkipDemoSound(rsp.success())
                }
            }

            StatisticConstant.LP_GET_GROUP_INFO -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetGroupInfoRsp::class.java
                ) ?: return
                device.onGroupInfo(rsp)
                loopListeners { listener ->
                    listener.onGetGroupInfo(rsp)
                }
            }

            StatisticConstant.LP_GET_GROUP_PARAMETER -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetGroupParameterRsp::class.java
                ) ?: return
                device.onGroupParameter(rsp)

                loopListeners { listener ->
                    listener.onGetGroupParameter(rsp)
                }
            }

            StatisticConstant.LP_RENAME_GROUP -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onRenameGroup(rsp.success())
                }
            }

            StatisticConstant.GET_STREAMING_STATUS -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetStreamingStatusRsp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onStreamingStatus(rsp = rsp)
                }

                rsp.status?.let { status ->
                    device.onStreamingStatus(status = status)

                    loopListeners { listener ->
                        listener.onDolbyAtoms(support = status.isAtoms)
                    }
                }
            }

            StatisticConstant.SET_SLEEP_TIMER -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetSleepTimer(rsp = rsp)
                }

                val cmd = lastReqCommand
                if (rsp.success() && cmd is SetSleepTimerCommand) {
                    device.onSleepTimer(cmd.secs)
                }
            }

            StatisticConstant.GET_SLEEP_TIMER -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetSleepTimerRsp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGetSleepTimer(rsp = rsp)
                }

                if (rsp.success()) {
                    if (null != rsp.sleepTimerSec) {
                        device.onSleepTimer(secs = rsp.sleepTimerSec)
                    }

                    if (null != rsp.remainingTimeSec) {
                        loopListeners { listener ->
                            listener.onSleepTimerTick(remainSecs = rsp.remainingTimeSec)
                        }
                    }
                }
            }

            StatisticConstant.GET_PERSONAL_LISTENING_MODE -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    StatusResp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGetPersonalListeningMode(rsp.success() && rsp.status == statusOn)
                }
            }

            StatisticConstant.GET_LIGHT_COLOR_PICKER -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    ColorPickerResp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGetColorPicker(rsp)
                }
            }

            StatisticConstant.MONITOR_SOUNDSCAPE_V2_DEMO_TRACK -> {
                val state = GsonUtil.parseJsonToBean(
                    response.responseData,
                    NotifySoundscapeV2MusicState::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSoundscapeV2MusicState(state = state)
                }
            }

            StatisticConstant.LP_NOTIFY_GROUP_PARAMETER -> {
                //special data format
                runCatching {
                    val resp = GsonUtil.parseJsonToBean(
                        JSONObject(response.responseData).getJSONObject("payload").toString(),
                        GetGroupParameterRsp::class.java
                    )
                    device.onGroupParameter(resp)
                }.onFailure { it.printStackTrace() }
            }

            StatisticConstant.GET_BLE_ENCRYPTION_KEY -> {
                val res = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BleGetEncryptionKeyResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onOOBEEncryptionKey(response = res)
                }
            }

            StatisticConstant.NOTIFY_AURACAST_SQ_MODE -> {
                val res = GsonUtil.parseJsonToBean(
                    response.responseData,
                    AuraCastSqModelResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onAuraCastSqMode(response = res)
                }
            }

            StatisticConstant.GET_SMART_MODE -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetSmartModeRsp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGetSmartMode(isOn = rsp.isOn)
                }
            }

            StatisticConstant.SET_LIGHT_INFO -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED setLightInfo() >>> UUID[${device.UUID}] response:${response.responseData}")
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onSetLightInfo(response = rsp)
                }

            }

            StatisticConstant.GET_LIGHT_INFO -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED getLightInfo() >>> UUID[${device.UUID}] response:${response.responseData}")

                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetLightInfoResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGetLightInfo(response = rsp)
                }

                if (rsp.success()) {
                    rsp.lightInfo?.let { itLightInfo ->
                        device.onLightInfo(lightInfo = itLightInfo)
                    }
                }
            }

            StatisticConstant.NOTIFY_LIGHT_INFO -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED notifyLightInfo() >>> UUID[${device.UUID}] response:${response.responseData}")

                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    SetLightInfoRequest::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onNotifyLightInfo(response = rsp)
                }
            }

            StatisticConstant.RESET_LIGHT_PATTERN_COLOR -> {
                Logger.d(TAG, "LIGHT_INFO_RELATED resetLightPatternColor() >>> UUID[${device.UUID}] response:${response.responseData}")

                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    BasicResponse::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onResetLightPatternColor(response = rsp)
                }
            }

            StatisticConstant.GET_GROUP_DEVICES_OTA_STATUS -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GroupDeviceOTAStatusRsp::class.java
                ) ?: return

                loopListeners { listener ->
                    listener.onGroupDeviceOTAStatus(rsp = rsp)
                }
            }

            StatisticConstant.GET_GROUP_DEVICES_FLAG -> {
                val rsp = GsonUtil.parseJsonToBean(
                    response.responseData,
                    GetGroupDevicesFlagRsp::class.java
                ) ?: return

                device.onGroupDevicesFlag(rsp = rsp)

                loopListeners { listener ->
                    listener.onGroupDeviceFlag(rsp = rsp)
                }
            }

            else -> {
                val last = lastReqCommand
                if (response.responsedCode == (last as? AttachOneGattCommand)?.commandID &&
                    null != last.targetRspType
                ) {
                    val rsp = GsonUtil.parseJsonToBean(
                        response.responseData,
                        last.targetRspType
                    ) ?: return

                    loopListeners { listener ->
                        listener.onAttachCommandReceived(rsp = rsp)
                    }
                }
            }
        }
    }

    override fun sendAttachCommand(command: AttachOneCommand, port: String?) {
        val cmd = command.gattCommand ?: return
        sendOrWait(cmd)
    }

    private fun loopListeners(cb: (IOneDeviceListener) -> Unit) =
        listenerProxy
            .getter()
            .filterIsInstance<IOneDeviceListener>()
            .forEach(cb)

    private fun notifyWriteSuccess(bytes: ByteArray) {
        listenerProxy.getter().forEach { listener ->
            if (listener !is IOneDeviceListener) {
                return@forEach
            }

            listener.onCharacteristicWriteSuccess(bytes = bytes)
        }
    }

    private fun notifyReceived(rsp: BLEResponse) {
        Logger.d(TAG, "notifyReceived() >>> : wifiCommandName = ${rsp.responsedCode.wifiCmd()} response = $rsp")

        if (StatisticConstant.LP_AUTH_PAIRING == rsp.responsedCode) {
            onPrePair()
        } else if (StatisticConstant.LP_AUTH_PAIR == rsp.responsedCode) {
            onGattConnected(mtu = mtuSize)
        }

        listenerProxy.getter().forEach { listener ->
            if (listener !is IOneDeviceListener) {
                return@forEach
            }

            listener.onResponseReceived(response = rsp)
        }
    }

    companion object {
        private const val TAG = "OneGattSession"
    }
}