package com.harman.connect

import com.harman.discover.bean.bt.GattListenerProxy
import com.harman.discover.bean.bt.PartyBandBTDevice
import com.harman.v5protocol.V5GattSession
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5DeviceOOBE
import com.harman.v5protocol.bean.devinfofeat.V5LeftDeviceBatteryStatus
import com.harman.v5protocol.bean.devinfofeat.V5LeftSerialNumber
import com.harman.v5protocol.discover.BatteryInfo

class PartyBandGattSession(
    device: PartyBandBTDevice,
    gattListenerProxy: GattListenerProxy,
) : V5GattSession<PartyBandBTDevice>(
    device = device,
    listenerProxy = gattListenerProxy
) {
    override fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>) {
        super.onDevFeat(devInfoMap)
        (devInfoMap[V5DevInfoFeatID.LeftSerialNumber] as? V5LeftSerialNumber)?.also {
            device.serialNumber = it.serialNumber
        }
        devInfoMap[V5DevInfoFeatID.AuracastStandardQuality]?.also {
            device.supportAuracastBroadcastQuality = true
        }
        (devInfoMap[V5DevInfoFeatID.DeviceOOBE] as? V5DeviceOOBE)?.also {
            device.sMusician = it
        }
        (devInfoMap[V5DevInfoFeatID.LeftDeviceBatteryStatus] as? V5LeftDeviceBatteryStatus)?.also {
            device.batteryInfo = BatteryInfo(it.isCharging, it.batteryPercent)
        }
    }
}