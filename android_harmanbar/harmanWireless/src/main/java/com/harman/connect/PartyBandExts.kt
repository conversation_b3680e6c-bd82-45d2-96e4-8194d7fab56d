package com.harman.connect

import androidx.annotation.AnyThread
import com.harman.discover.bean.PartyBandDevice
import com.harman.v5protocol.IV5GattListener
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.V5AuthenticateButtonIsPressed
import com.harman.v5protocol.bean.devinfofeat.V5AuthenticateMode
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.isFail
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.withTimeout

/**
 * @Description [PartyBandDevice]'s extensions
 * <AUTHOR>
 * @Time 2025/3/7
 */

suspend fun PartyBandDevice.auth(timeoutMills: Long = 30 * 1000): Boolean {
    //enter Authenticate Mode
    val writeRet = setDevInfoFeat(V5AuthenticateMode(V5AuthenticateMode.Mode.Enter))
    if (writeRet.isFail()) {
        return false
    }
    return null != awaitDevInfo<V5AuthenticateButtonIsPressed>(timeoutMills)
}
