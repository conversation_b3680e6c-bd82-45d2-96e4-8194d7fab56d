package com.harman.connect.session

import androidx.annotation.IntRange
import com.harman.command.one.AttachOneCommand
import com.harman.command.one.bean.EnumC4aPermissionStatus
import com.harman.command.one.bean.ChromeCastOpt
import com.harman.command.one.bean.ColorPicker
import com.harman.command.one.bean.ControlSoundscapeV2
import com.harman.command.one.bean.DJPad
import com.harman.command.one.bean.DebugMode
import com.harman.command.one.bean.EQSetting
import com.harman.command.one.bean.EnumBatterySavingStatus
import com.harman.command.one.bean.EnumBluetoothConfig
import com.harman.command.one.bean.EnumFeedbackToneConfig
import com.harman.command.one.bean.EnumVoiceSource
import com.harman.command.one.bean.GeneralConfig
import com.harman.command.one.bean.GeneralConfigType
import com.harman.command.one.bean.LWAInfo
import com.harman.command.one.bean.PreviewSoundscapeRequest
import com.harman.command.one.bean.SendAppControllerRequest
import com.harman.command.one.bean.SetActiveEQItemRequest
import com.harman.command.one.bean.SetLightInfoRequest
import com.harman.command.one.bean.SetSoundscapeV2ConfigRequest
import com.harman.command.one.bean.SetVoiceLanguageRequest
import com.harman.command.one.bean.SetVoiceToneRequest
import com.harman.command.one.bean.SetWifiNetworkRequest
import com.harman.command.one.bean.SmartBtnConfig
import com.harman.command.one.bean.TriggerDiagnosisRequest

interface OneBusiness : BaseBusinessSession {

    fun playMusic()

    fun pauseMusic()

    fun prevMusic()

    fun nextMusic()

    fun setRemoteVolume(@IntRange(from = 0L, to = 100L) value: Int)

    fun getRemoteVolume()

    fun sendAttachCommand(command: AttachOneCommand, port: String? = null)

    fun getDeviceInfo(port: String? = null)

    /**
     * Distinguished from [BaseBTDevice.deviceName] for same JVM signature declarations.
     */
    fun sendSetDeviceName(name: String, port: String? = null)

    fun getAPList(port: String? = null)

    fun setCastGroup(payloadJson: String, port: String? = null)

    fun getFeatureSupport(port: String? = null)

    fun getEQList(port: String? = null)

    fun setActiveEQ(item: SetActiveEQItemRequest, port: String? = null)

    fun getEQ(port: String? = null)

    fun setEQ(eq: EQSetting, port: String? = null)

    fun startAuth(timeoutSecs: Int, port: String? = null)

    fun cancelAuth(port: String? = null)

    fun authPair(timeoutSecs: Int, port: String? = null) {}

    fun setWifiNetwork(req: SetWifiNetworkRequest, port: String? = null)

    fun getOtaStatus(port: String? = null)

    fun requestDeviceOta(port: String? = null)

    fun getOtaAccessPoint(port: String? = null)

    fun getBatteryStatus(port: String? = null)

    fun setCalibration(port: String? = null)

    fun getCalibrationState(port: String? = null)

    fun cancelCalibration(port: String? = null)

    fun getC4aPermissionStatus(port: String? = null)

    fun setC4aPermissionStatus(status: EnumC4aPermissionStatus, port: String? = null)

    fun getChromeCastOptIn(port: String? = null)

    fun setChromeCastOptIn(optIn: ChromeCastOpt, port: String? = null)

    fun setCountryCode(code: String, port: String? = null)

    fun enterAuraCast(port: String? = null)

    fun exitAuraCast(port: String? = null)

    fun getAuraCastSqMode(port: String? = null)

    /**
     * @param on: true: turn SQ; false: turn off SQ
     */
    fun setAuraCastSqMode(on: Boolean, port: String? = null)

    fun setPlayPartySound(partySound: Int, port: String? = null)

    fun setDjEvent(value: String, port: String? = null)

    fun setDebugMode(debugMode: DebugMode, port: String? = null)

    fun alexaCBLStatus(port: String? = null)

    fun alexaCBLLogout(port: String? = null)

    fun getLWAState(port: String? = null)

    fun requestLWALogout(port: String? = null)

    fun setLWAAuthCode(lwaInfo: LWAInfo, port: String? = null)

    fun getVoiceRequestStartTone(source: EnumVoiceSource, port: String? = null)

    fun getVoiceRequestEndTone(source: EnumVoiceSource, port: String? = null)

    fun getVoiceLanguage(source: EnumVoiceSource, port: String? = null)

    fun getSupportedVoiceLanguage(source: EnumVoiceSource, port: String? = null)

    fun requestGoogleLogout(port: String? = null)

    fun setVoiceRequestStartTone(setVoiceToneRequest: SetVoiceToneRequest, port: String? = null)

    fun setVoiceRequestEndTone(setVoiceToneRequest: SetVoiceToneRequest, port: String? = null)

    fun setVoiceLanguage(setVoiceLanguageRequest: SetVoiceLanguageRequest, port: String? = null)

    fun getSmartBtnConfig(port: String? = null)

    fun setSmartBtnConfig(config: SmartBtnConfig, port: String? = null)

    fun previewSoundscape(request: PreviewSoundscapeRequest, port: String? = null)

    fun cancelSoundscape(port: String? = null)

    fun getSoundscapeV2Config(port: String? = null)

    fun setSoundscapeV2Config(req: SetSoundscapeV2ConfigRequest, port: String? = null)

    fun controlSoundscapeV2(req: ControlSoundscapeV2, port: String? = null)

    fun getSoundscapeV2State(port: String? = null)

    fun getProductUsage(port: String? = null)

    fun getDeviceName(port: String? = null)

    fun playDemoSound(port: String? = null)

    fun cancelDemoSound(port: String? = null)

    fun getRearSpeakerVolume(port: String? = null)

    fun sendAppController(req: SendAppControllerRequest, port: String? = null)

    /**
     * Http command only, implement in Wi-Fi session.
     */
    fun triggerDiagnosis(req: TriggerDiagnosisRequest, port: String? = null) {}

    fun setFactoryRestore(port: String? = null)

    fun setAutoPowerOffTimer(secs: Int, port: String? = null)

    fun getAutoPowerOffTimer(port: String? = null)

    fun setBluetoothConfig(config: EnumBluetoothConfig, port: String? = null)

    fun getBluetoothConfig(port: String? = null)

    fun setFeedbackToneConfig(config: EnumFeedbackToneConfig, port: String? = null)

    fun getFeedbackToneConfig(port: String? = null)

    fun getGeneralConfig(type: GeneralConfigType, port: String? = null)

    fun setGeneralConfig(req: GeneralConfig, port: String? = null)

    fun setBatterySavingStatus(status: EnumBatterySavingStatus, port: String? = null)

    fun getBatterySavingStatus(port: String? = null)

    fun setIRLearn(port: String? = null)

    fun getDJPad(port: String? = null)

    fun setDJPad(djPad: DJPad, port: String? = null)

    fun triggerCastLED(port: String? = null)

    fun clearHistoryOneOSVersion(port: String? = null)

    fun setAnchorIndicate(timeoutSecs: Int, port: String? = null)

    fun setAnchorCancel(port: String? = null)

    fun destroyCastGroup(port: String? = null)

    fun getGroupInfo(port: String? = null)

    fun groupCalibration(anchor: String, step: Int, port: String? = null)

    fun switchStereoChannel(port: String? = null)

    fun skipDemoSound(port: String? = null)

    fun getGroupParameter(port: String? = null)

    fun getGroupCalibrationState(port: String? = null)

    fun renameGroup(name: String, port: String? = null)

    fun getStreamingStatus(port: String? = null)

    /**
     * Auto off for player.
     */
    fun setSleepTimer(secs: Int, port: String? = null)

    fun getSleepTimer(port: String? = null)

    fun getPersonalListeningMode(port: String? = null)

    fun getMediaSourceStatus(port: String? = null)

    fun setPersonalListeningMode(isOn: Boolean, port: String? = null)

    fun getColorPicker(port: String? = null)

    fun setColorPicker(picker: ColorPicker, port: String? = null)

    fun getOOBEEncryptionKey(port: String? = null) {}

    fun setTimeZone(formatDate: String, port: String? = null)

    fun setSmartMode(isOn: Boolean, port: String? = null)

    fun getSmartMode(port: String? = null)

    fun getRearSpeakerStatus(port: String? = null)

    fun setLightInfo(req: SetLightInfoRequest, port: String? = null)

    fun getLightInfo(port: String? = null)

    fun resetLightPatternColor(id: String, port: String? = null)

    fun getGroupDevicesOtaStatus(port: String? = null)

    fun getGroupDevicesFlag(port: String? = null)

}