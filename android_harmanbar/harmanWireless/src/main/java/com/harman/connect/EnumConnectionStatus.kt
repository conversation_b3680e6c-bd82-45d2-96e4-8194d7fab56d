package com.harman.connect

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/2/19.
 *
 * Insecure
 * [DISCONNECTED] < -- > [CONNECTING] -- > [CONNECTED]
 *    ^                                 |
 *    |                                 |
 *    -----------------------------------
 *
 * Secure
 *  [DISCONNECTED] < -- > [CONNECTING] -- > [PRE_PAIR] --> [PAIRING] --> [CONNECTED]
 *     ^                                 |              |             |
 *     |                                 |              |             |
 *     ---------------------------------------------------------------
 */
enum class EnumConnectionStatus {
    NONE,
    DISCONNECTED,
    CONNECTING,
    PRE_PAIR, // for SECURE BLE
    PAIRING,  // for SECURE BLE
    CONNECTED
}

fun EnumConnectionStatus.isConnected(): Boolean =
    EnumConnectionStatus.CONNECTED == this

fun EnumConnectionStatus.isConnecting(): Boolean =
    EnumConnectionStatus.CONNECTING == this

fun EnumConnectionStatus.isDisconnected(): Boolean =
    EnumConnectionStatus.DISCONNECTED == this

fun EnumConnectionStatus.isPairing(): Boolean =
    EnumConnectionStatus.PAIRING == this

fun EnumConnectionStatus.isPrePair(): Boolean =
    EnumConnectionStatus.PRE_PAIR == this