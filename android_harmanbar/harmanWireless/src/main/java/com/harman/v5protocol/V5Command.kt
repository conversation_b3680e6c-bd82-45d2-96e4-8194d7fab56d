package com.harman.v5protocol

import com.harman.discover.util.Tools.isNullOrEmpty
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.ceil
import kotlin.math.max

/**
 * @Description Define the communication protocol and data format for v5
 * <AUTHOR>
 * @Time 2024/12/4
 */
/**
 * app defined.
 * One instruction with all the data,Consolidated by one or more packs
 */
interface IV5Command {
    val identifier: V5Identifier
    val commandId: Short
    val payload: ByteArray?
    val payloadLength: Int
        get() = payload?.size ?: 0
    val timeoutMills: Long
        get() = 6 * 1000

    fun splitCommand(mtuSize: Int): List<V5Package> {
        val eachPackageMaxPayloadLength = mtuSize - 8
        //packageCount minimum is 1
        val packageCount = max(1, ceil(payloadLength / eachPackageMaxPayloadLength.toFloat()).toInt())
        val packages = List(packageCount) { index ->
            val eachPackagePayload = if (null != payload) {
                val commandPayloadBuffer = ByteBuffer.wrap(payload!!).order(ByteOrder.LITTLE_ENDIAN)
                if (index < packageCount - 1) {
                    ByteArray(eachPackageMaxPayloadLength).also { commandPayloadBuffer.get(it) }
                } else {
                    ByteArray(commandPayloadBuffer.remaining()).also { commandPayloadBuffer.get(it) }
                }
            } else null

            V5Package(
                identifier,
                commandId,
                packageCount,
                index,
                eachPackagePayload?.size ?: 0,
                eachPackagePayload,
            )
        }
        return packages
    }

    fun toV5Package(packageCount: Int = 1, packageIndex: Int = 0): V5Package = V5Package(
        identifier,
        commandId,
        packageCount,
        packageIndex,
        payloadLength,
        payload,
    )
}

/**
 * app defined.
 * All the data of a command replied by the device defined by the app is combined by one or more packets
 */
class V5ResponseCommand(
    override val identifier: V5Identifier,
    override val commandId: Short,
    override val payload: ByteArray,
) : IV5Command

enum class V5Identifier(val id: Short) {
    Direct((0xDD00).toShort()),// Send to direct-connected device
    Forward((0xDD01).toShort()),//Need to forward to accessory device
}

/**
 * All command id supported by the V5 protocol will be defined here
 */
internal object V5CommandID {
    const val GET_DEVICE_INFO: Short = 0x0001
    const val SET_DEVICE_INFO: Short = 0x0002
    const val NOTIFY_DEVICE_INFO: Short = 0x0003
    const val GET_DEVICE_ANALYTICS_DATA: Short = 0x0201
    const val CLEAN_DEVICE_ANALYTICS_DATA: Short = 0x02FF

    const val START_OTA: Short = 0x0101
    const val SEND_OTA_DATA: Short = 0x0102
    const val STOP_OTA: Short = 0x0103
    const val OTA_NOTIFICATION: Short = 0x0104
}


typealias V5Parser<Resp> = (payload: ByteArray) -> Resp

/**
 * The data format of a packet of the v5 protocol
 */
class V5Package(
    val v5Identifier: V5Identifier,
    val commandId: Short,
    val packageCount: Int,
    val packageIndex: Int,
    val payloadLength: Int,
    val payload: ByteArray? = null,
) {

    fun toBytes(): ByteArray {
        val buffer = ByteBuffer.allocate(8 + payloadLength).order(ByteOrder.LITTLE_ENDIAN)
        buffer.putShort(v5Identifier.id)
        buffer.putShort(commandId)
        buffer.put(packageCount.toByte())
        buffer.put(packageIndex.toByte())
        buffer.putShort(payloadLength.toShort())
        if (!payload.isNullOrEmpty()) {
            buffer.put(payload!!)
        }
        return buffer.array()
    }

    companion object {
        fun fromDevice(onePackageBytes: ByteArray): V5Package {
            val buffer = ByteBuffer.wrap(onePackageBytes).order(ByteOrder.LITTLE_ENDIAN)
            val v5Identifier = V5Identifier.entries.find { buffer.getShort() == it.id }!!
            val commandId = buffer.getShort()
            val packageCount = buffer.get().toUByte().toInt()
            val packageIndex = buffer.get().toUByte().toInt()
            val payloadLength = buffer.getShort().toUShort().toInt()
            val payload = ByteArray(payloadLength).also { buffer.get(it) }
            return V5Package(v5Identifier, commandId, packageCount, packageIndex, payloadLength, payload)
        }
    }
}



