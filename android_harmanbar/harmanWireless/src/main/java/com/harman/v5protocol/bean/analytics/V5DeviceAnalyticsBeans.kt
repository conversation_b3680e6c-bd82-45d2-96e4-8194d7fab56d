package com.harman.v5protocol.bean.analytics

import com.harman.discover.util.Tools.printList
import com.harman.discover.util.Tools.toLittleEndianUInt
import com.harman.v5protocol.bean.analytics.AnalyticsPreset.Companion.toPresetList
import com.harman.v5protocol.bean.analytics.AnalyticsStem.Companion.toStemList
import com.harman.v5protocol.bean.analytics.AnalyticsVolume.Companion.toVolumeList
import kotlin.reflect.KClass

/**
 * @Description Define all the corresponding data model for 'DEVICE ANALYTICS DATA' for v5 protocols,
 * All data model need to be prefixed with 'V5'
 * <AUTHOR>
 * @Time 2025/5/12
 */

/**
 * All device analytics data supported by the V5 protocol will be defined here,The type of data to be analyzed is determined by [categoryId] and [featureId]
 * @param valueLength the analytics value payload size, [Int.MIN_VALUE] indicates that size is dynamic
 * @param valueClz This [categoryId] and [featureId] corresponds to the data model
 */
enum class V5DevAnalyticsId(
    val categoryId: Int,
    val featureId: Int,
    val valueLength: Int,
    val valueClz: KClass<*>,
) {

    /**
     * Playback
     */
    DurationOfBTSourceInBatteryMode(0x01, 0x01, 2, V5DurationOfBTSourceInBatteryMode::class),
    DurationOfUSBSourceInBatteryMode(0x01, 0x02, 2, V5DurationOfUSBSourceInBatteryMode::class),
    DurationOfAUXSourceInBatteryMode(0x01, 0x03, 2, V5DurationOfAUXSourceInBatteryMode::class),
    DurationOfUACSourceInBatteryMode(0x01, 0x04, 2, V5DurationOfUACSourceInBatteryMode::class),
    DurationOfBTSourceInACMode(0x01, 0x05, 2, V5DurationOfBTSourceInACMode::class),
    DurationOfUSBSourceInACMode(0x01, 0x06, 2, V5DurationOfUSBSourceInACMode::class),
    DurationOfAUXSourceInACMode(0x01, 0x07, 2, V5DurationOfAUXSourceInACMode::class),
    DurationOfUACSourceInACMode(0x01, 0x08, 2, V5DurationOfUACSourceInACMode::class),

    /**
     * Button
     */
    TimesOfDutPowerOn(0x02, 0x01, 2, V5TimesOfDutPowerOn::class),
    DurationOfDutPowerOn(0x02, 0x02, 2, V5DurationOfDutPowerOn::class),

    /**
     * Volume
     */
    TimesThatUsersAdjustVolumeThroughAVRCPLEA(0x03, 0x01, 2, V5TimesThatUsersAdjustVolumeThroughAVRCPLEA::class),
    DurationOfEachMusicVolume(0x03, 0x02, Int.MIN_VALUE, V5DurationOfEachMusicVolume::class),
    DurationOfEachCH1MICVolume(0x03, 0x03, Int.MIN_VALUE, V5DurationOfEachCH1MICVolume::class),
    DurationOfEachCH1GuitarVolume(0x03, 0x04, Int.MIN_VALUE, V5DurationOfEachCH1GuitarVolume::class),
    DurationOfEachInstrumentVolume(0x03, 0x05, Int.MIN_VALUE, V5DurationOfEachInstrumentVolume::class),
    DurationOfEachMasterVolume(0x03, 0x06, Int.MIN_VALUE, V5DurationOfEachMasterVolume::class),
    DurationOfEachCH2MICVolume(0x03, 0x07, Int.MIN_VALUE, V5DurationOfEachCH2MICVolume::class),
    DurationOfEachCH3GuitarVolume(0x03, 0x08, Int.MIN_VALUE, V5DurationOfEachCH3GuitarVolume::class),

    /**
     * Guitar Preset
     */
    DurationOfUserCustomizedPreset(0x04, 0x01, 2, V5DurationOfUserCustomizedPreset::class),
    DurationOfEachGuitarPresetID(0x04, 0x02, Int.MIN_VALUE, V5DurationOfEachGuitarPresetID::class),

    /**
     * UAC
     */
    TimesOfUACRecording(0x05, 0x01, 2, V5TimesOfUACRecording::class),
    TimesOfUACMultiChannelRecording(0x05, 0x02, 2, V5TimesOfUACMultiChannelRecording::class),

    /**
     * Battery
     */
    BatteryPackModel(0x06, 0x01, 12, V5BatteryPackModel::class),
    BatteryCellConnection(0x06, 0x02, 2, V5BatteryCellConnection::class),
    BatterySN(0x06, 0x03, 10, V5BatterySN::class),

    /**
     * Stem
     */
    TimesOfStemON(0x07, 0x01, 2, V5TimesOfStemON::class),
    DurationOfEachStemMode(0x07, 0x02, Int.MIN_VALUE, V5DurationOfEachStemMode::class),

    /**
     * Drum
     */
    TimesOfDrumFeatureTriggered(0x08, 0x01, 2, V5TimesOfDrumFeatureTriggered::class),

    /**
     * Metronome
     */
    TimesOfMetronomeFeatureTriggered(0x09, 0x01, 2, V5TimesOfMetronomeFeatureTriggered::class),

    /**
     * Tuner
     */
    TimesOfTunerFeatureTriggered(0x0A, 0x01, 2, V5TimesOfTunerFeatureTriggered::class),

    /**
     * Looper
     */
    TimesOfLooperFeatureTriggered(0x0B, 0x01, 2, V5TimesOfLooperFeatureTriggered::class),

    /**
     * CH1 Input
     */
    TimesOfCH1MICPluggedIn(0x0C, 0x01, 2, V5TimesOfCH1MICPluggedIn::class),
    TimesOfCH1GuitarPluggedIn(0x0C, 0x02, 2, V5TimesOfCH1GuitarPluggedIn::class),

    /**
     * CH2 Input
     */
    TimesOfCH2MICPluggedIn(0x0D, 0x01, 2, V5TimesOfCH2MICPluggedIn::class),
    TimesOfCH2InstrumentPluggedIn(0x0D, 0x02, 2, V5TimesOfCH2InstrumentPluggedIn::class),

    /**
     * CH3 Input
     */
    TimesOfCH3PluggedIn(0x0E, 0x01, 2, V5TimesOfCH3PluggedIn::class),

    /**
     * AUX Input
     */
    TimesOfAuxInstrumentPluggedIn(0x0F, 0x01, 2, V5TimesOfAuxInstrumentPluggedIn::class),
    TimesOfAuxMusicPluggedIn(0x0F, 0x02, 2, V5TimesOfAuxMusicPluggedIn::class),

    /**
     * Built-in MIC
     */
    TimesOfBuiltInMICUsed(0x10, 0x01, 2, V5TimesOfBuiltInMICUsed::class),

    /**
     * Headphone
     */
    TimesOfHeadphoneOutUsed(0x11, 0x01, 2, V5TimesOfHeadphoneOutUsed::class),

    /**
     * Daisy Chain
     */
    TimesOfDaisyChainOutUsed(0x12, 0x01, 2, V5TimesOfDaisyChainOutUsed::class), ;

    override fun toString(): String {
        return "category[$categoryId] feature[$featureId] valueLength[$valueLength]"
    }
}

data class V5DurationOfBTSourceInBatteryMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfBTSourceInBatteryMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("battery_bt_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfBTSourceInBatteryMode(valueBytes.toLittleEndianUInt())
    }

    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfUSBSourceInBatteryMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfUSBSourceInBatteryMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("battery_usb_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfUSBSourceInBatteryMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfAUXSourceInBatteryMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfAUXSourceInBatteryMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("battery_aux_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfAUXSourceInBatteryMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfUACSourceInBatteryMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfUACSourceInBatteryMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("battery_uac_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfUACSourceInBatteryMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfBTSourceInACMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfBTSourceInACMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ac_bt_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfBTSourceInACMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfUSBSourceInACMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfUSBSourceInACMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ac_usb_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfUSBSourceInACMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfAUXSourceInACMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfAUXSourceInACMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ac_aux_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfAUXSourceInACMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfUACSourceInACMode(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id = V5DevAnalyticsId.DurationOfUACSourceInACMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ac_uac_playback_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfUACSourceInACMode(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5TimesOfDutPowerOn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfDutPowerOn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("btn_power_on_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfDutPowerOn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5DurationOfDutPowerOn(val durationInMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfDutPowerOn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("btn_power_on_duration", durationInMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfDutPowerOn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationInMinutes]"
    }
}

data class V5TimesThatUsersAdjustVolumeThroughAVRCPLEA(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesThatUsersAdjustVolumeThroughAVRCPLEA
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("volume_change_by_avrcp_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesThatUsersAdjustVolumeThroughAVRCPLEA(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5DurationOfEachMusicVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachMusicVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "music_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachMusicVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfEachCH1MICVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachCH1MICVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "ch1_mic_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachCH1MICVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfEachCH1GuitarVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachCH1GuitarVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "ch1_guitar_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachCH1GuitarVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfEachInstrumentVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachInstrumentVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "instrument_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachInstrumentVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfEachMasterVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachMasterVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "master_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachMasterVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfEachCH2MICVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachCH2MICVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "ch2_mic_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachCH2MICVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfEachCH3GuitarVolume(val volumes: List<AnalyticsVolume>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachCH3GuitarVolume
    override val cloudKVs: List<Pair<String, UInt>>
        get() = volumes.mapNotNull { volume ->
            if (volume.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "ch3_guitar_volume_level_${volume.volumeLevel}_duration",
                volume.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachCH3GuitarVolume(valueBytes.toVolumeList())
    }
    
    override fun toString(): String {
        return "$id ${volumes.printList()}"
    }
}

data class V5DurationOfUserCustomizedPreset(val durationMinutes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfUserCustomizedPreset
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("guitar_customized_duration", durationMinutes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfUserCustomizedPreset(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$durationMinutes]"
    }
}

data class V5DurationOfEachGuitarPresetID(val presets: List<AnalyticsPreset>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachGuitarPresetID
    override val cloudKVs: List<Pair<String, UInt>>
        get() = presets.mapNotNull { preset ->
            if (preset.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "guitar_preset_${preset.presetId}_duration",
                preset.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachGuitarPresetID(valueBytes.toPresetList())
    }
    
    override fun toString(): String {
        return "$id ${presets.printList()}"
    }
}

data class V5TimesOfUACRecording(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfUACRecording
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("uac_record_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfUACRecording(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfUACMultiChannelRecording(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfUACMultiChannelRecording
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("uac_multi_channel_record_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfUACMultiChannelRecording(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5BatteryPackModel(val model: String) : IV5AnalyticsValue<String> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.BatteryPackModel
    override val cloudKVs: List<Pair<String, String>>
        get() = listOf(Pair("battery_pack_model", model))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5BatteryPackModel(String(valueBytes))
    }
    
    override fun toString(): String {
        return "$id [$model]"
    }
}

data class V5BatteryCellConnection(val connectionValue: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.BatteryCellConnection
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("battery_cell_connection", connectionValue))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5BatteryCellConnection(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$connectionValue]"
    }
}

data class V5BatterySN(val serialNumber: String) : IV5AnalyticsValue<String> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.BatterySN
    override val cloudKVs: List<Pair<String, String>>
        get() = listOf(Pair("battery_sn", serialNumber))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5BatterySN(String(valueBytes))
    }
    
    override fun toString(): String {
        return "$id [$serialNumber]"
    }
}

data class V5TimesOfStemON(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfStemON
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("stem_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfStemON(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5DurationOfEachStemMode(val stems: List<AnalyticsStem>) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.DurationOfEachStemMode
    override val cloudKVs: List<Pair<String, UInt>>
        get() = stems.mapNotNull { stem ->
            if (stem.durationInMinutes <= 0u) {
                return@mapNotNull null
            }

            return@mapNotNull Pair(
                "stem_mode_${stem.modeId}_duration",
                stem.durationInMinutes
            )
        }

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5DurationOfEachStemMode(valueBytes.toStemList())
    }
    
    override fun toString(): String {
        return "$id ${stems.printList()}"
    }
}

data class V5TimesOfDrumFeatureTriggered(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfDrumFeatureTriggered
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("drum_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfDrumFeatureTriggered(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfMetronomeFeatureTriggered(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfMetronomeFeatureTriggered
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("metronome_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfMetronomeFeatureTriggered(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfTunerFeatureTriggered(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfTunerFeatureTriggered
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("tuner_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfTunerFeatureTriggered(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfLooperFeatureTriggered(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfLooperFeatureTriggered
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("looper_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfLooperFeatureTriggered(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfCH1MICPluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfCH1MICPluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ch1_mic_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfCH1MICPluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfCH1GuitarPluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfCH1GuitarPluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ch1_guitar_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfCH1GuitarPluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfCH2MICPluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfCH2MICPluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ch2_mic_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfCH2MICPluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfCH2InstrumentPluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfCH2InstrumentPluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ch2_instrument_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfCH2InstrumentPluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfCH3PluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfCH3PluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("ch3_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfCH3PluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfAuxInstrumentPluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfAuxInstrumentPluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("aux_instrument_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfAuxInstrumentPluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfAuxMusicPluggedIn(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfAuxMusicPluggedIn
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("aux_music_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfAuxMusicPluggedIn(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfBuiltInMICUsed(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfBuiltInMICUsed
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("built_in_mic_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfBuiltInMICUsed(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfHeadphoneOutUsed(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfHeadphoneOutUsed
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("headphone_out_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfHeadphoneOutUsed(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}

data class V5TimesOfDaisyChainOutUsed(val numberOfTimes: UInt) : IV5AnalyticsValue<UInt> {
    override val id: V5DevAnalyticsId = V5DevAnalyticsId.TimesOfDaisyChainOutUsed
    override val cloudKVs: List<Pair<String, UInt>>
        get() = listOf(Pair("daisy_chain_out_times", numberOfTimes))

    companion object {
        @V5AnalyticsValueCreate
        fun fromValueBytes(valueBytes: ByteArray) = V5TimesOfDaisyChainOutUsed(valueBytes.toLittleEndianUInt())
    }
    
    override fun toString(): String {
        return "$id [$numberOfTimes]"
    }
}
