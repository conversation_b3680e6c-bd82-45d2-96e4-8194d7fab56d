package com.harman.v5protocol.discover

/**
 * @Description defines the data model in the V5 BLE broadcast protocol
 * <AUTHOR>
 * @Time 2024/12/3
 */
data class BatteryInfo(
    val isCharging: Boolean = false,
    val batteryLevel: Int? = null,
)

data class DeviceMiscInfo(
    val isConnectable: Boolean = false,
    val isStandbyModel: Boolean = false,
    val isMuteOn: Boolean = false,
    val isLedOn: Boolean = false,
    val isMic1Connected: Boolean = false,
    val isMic2Connected: Boolean = false,
    val bassBoost: BassBoost = BassBoost.Off,
    val isSupportBrEdr: Boolean = false,
    val isJblAppConnected: Boolean = false,
    val isDongleConnected: Boolean = false,
    val isSoundbarConnected: Boolean = false,
    val isUsbAudioConnected: Boolean = false,
    val isAuxConnected: Boolean = false,
) {
    enum class BassBoost(val value: Int) {
        Off(0),
        BassBoost1(1),
        BassBoost2(2),
    }
}


data class PartyMethodInfo(
    val isAuracastSupport: Boolean = false,
    val isAuracastLongLastingStereoSupport: Boolean = false,
)

data class PartyInfo(
    val isPartyOn: Boolean = false,
    val groupType: GroupType = GroupType.Normal,
    val connStatus: ConnStatus = ConnStatus.Normal,
    val role: Role = Role.Normal,
    val channelType: ChannelType = ChannelType.FullChannel,
) {
    enum class GroupType(val value: Int) {
        Normal(0),
        Party(1),
        Stereo(2),
    }

    enum class ConnStatus(val value: Int) {
        Normal(0),
        Connecting(1),
        Connected(2),
        Wired(3),
    }

    enum class Role(val value: Int) {
        Normal(0),
        Secondary(1),
        Primary(2),
    }

    enum class ChannelType(val value: Int) {
        FullChannel(0),
        LeftChannel(1),
        RightChannel(2),
    }
}

data class PartyLightInfo(
    val isLinkToPartyBox: Boolean,
    val stageNumber: Int,
)

