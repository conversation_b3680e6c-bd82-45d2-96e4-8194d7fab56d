package com.harman.v5protocol.discover

import android.annotation.SuppressLint
import android.bluetooth.le.ScanResult
import android.os.ParcelUuid
import com.harman.log.Logger
import com.harman.v5protocol.V5Util.bitSetToInt
import com.harmanbar.ble.utils.HexUtil
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.BitSet

/**
 * @Description BLE Broadcast parser that follows the v5 discovery protocol
 * <AUTHOR>
 * @Time 2024/12/6
 */
@SuppressLint("MissingPermission")
internal interface V5BLELegacyAdvParseMixin {
    companion object {
        private val serviceUuid = ParcelUuid.fromString("0000fddf-0000-1000-8000-00805f9b34fb")
        private const val TAG = "V5BLEAdvParseMixin"
    }

    fun v5EnableParse(scanResult: ScanResult?): Boolean {
        if (null == scanResult) {
            return false
        }
        val deviceName = scanResult.scanRecord?.deviceName
        if (deviceName.isNullOrBlank() || scanResult.scanRecord?.serviceData.isNullOrEmpty()) {
            return false
        }
        return null != scanResult.scanRecord?.serviceData?.get(serviceUuid)
    }


    @OptIn(ExperimentalStdlibApi::class)
    fun v5Parse(scanResult: ScanResult): V5ParseResult? {
        try {
            val buffer = ByteBuffer.wrap(scanResult.scanRecord?.serviceData?.get(serviceUuid)!!).order(ByteOrder.LITTLE_ENDIAN)

            /** pid START*/
            val pidString = buffer.getShort().toHexString()

            /** supported data status START*/
            val supportDataStatusBits =
                ByteArray(2).also { buffer.get(it) }.let { BitSet.valueOf(ByteBuffer.wrap(it).order(ByteOrder.LITTLE_ENDIAN)) }
            val supportColorId = supportDataStatusBits[15]
            val supportSrcName1Crc = supportDataStatusBits[14]
            val supportSrcName2Crc = supportDataStatusBits[13]
            val supportBTMacAddressCrc = supportDataStatusBits[12]
            val supportBTMacAddress = supportDataStatusBits[11]
            val supportBattery = supportDataStatusBits[10]
            val supportDeviceMusicInfo = supportDataStatusBits[9]
            val supportPartyMethodInfo = supportDataStatusBits[8]
            val supportPartyInfo = supportDataStatusBits[7]
            val supportStereoGroupId = supportDataStatusBits[6]
            val supportFirmwareVersion3 = supportDataStatusBits[5]
            val supportDeviceId = supportDataStatusBits[4]
            val supportPartyLightInfo = supportDataStatusBits[3]
            val supportFirmwareVersion5 = supportDataStatusBits[2]
//            val supportReserved2 = supportDataStatusBits[1]
//            val supportReserved3 = supportDataStatusBits[0]
            /** supported data status END*/
            /** data START*/
            val colorId = if (supportColorId) buffer.get().toString() else null
            val srcName1Crc = if (supportSrcName1Crc) buffer.getShort().toHexString() else null
            val srcName2Crc = if (supportSrcName2Crc) buffer.getShort().toHexString() else null
            val btMacAddressCrc = if (supportBTMacAddressCrc) buffer.getShort().toHexString() else null
            val btMacAddress = if (supportBTMacAddress) ByteArray(6).also {
                buffer.get(it)
            }.joinToString(":") { it.toHexString() } else null
            val batteryInfo = if (supportBattery) {
                val batteryBits = byteArrayOf(buffer.get()).let { BitSet.valueOf(it) }
                val isCharging = batteryBits[7]
                val level = bitSetToInt(batteryBits.get(0, 7), 7)
                BatteryInfo(isCharging, level)
            } else null
            val deviceMiscInfo = if (supportDeviceMusicInfo) {
                val deviceMusicInfoBits =
                    ByteArray(2).also { buffer.get(it) }.let { BitSet.valueOf(ByteBuffer.wrap(it).order(ByteOrder.LITTLE_ENDIAN)) }
                DeviceMiscInfo(
                    deviceMusicInfoBits[15],
                    deviceMusicInfoBits[14],
                    deviceMusicInfoBits[13],
                    deviceMusicInfoBits[12],
                    deviceMusicInfoBits[11],
                    deviceMusicInfoBits[10],
                    DeviceMiscInfo.BassBoost.entries.find { bitSetToInt(deviceMusicInfoBits[8, 10], 2) == it.value }!!,
                    deviceMusicInfoBits[7],
                    deviceMusicInfoBits[6],
                    deviceMusicInfoBits[5],
                    deviceMusicInfoBits[4],
                    deviceMusicInfoBits[3],
                    deviceMusicInfoBits[2],
                )
            } else null
            val partyMethodInfo = if (supportPartyMethodInfo) {
                val partyMethodInfoBits = byteArrayOf(buffer.get()).let { BitSet.valueOf(it) }
                PartyMethodInfo(
                    partyMethodInfoBits[7],
                    partyMethodInfoBits[6],
                )
            } else null
            val partyInfo = if (supportPartyInfo) {
                val partyInfoBits =
                    ByteArray(2).also { buffer.get(it) }.let { BitSet.valueOf(ByteBuffer.wrap(it).order(ByteOrder.LITTLE_ENDIAN)) }
                PartyInfo(
                    partyInfoBits[15],
                    PartyInfo.GroupType.entries.find { bitSetToInt(partyInfoBits[13, 15], 2) == it.value }!!,
                    PartyInfo.ConnStatus.entries.find { bitSetToInt(partyInfoBits[11, 13], 2) == it.value }!!,
                    PartyInfo.Role.entries.find { bitSetToInt(partyInfoBits[9, 11], 2) == it.value }!!,
                    PartyInfo.ChannelType.entries.find { bitSetToInt(partyInfoBits[7, 9], 2) == it.value }!!,
                )
            } else null
            val stereoGroupId = if (supportStereoGroupId) buffer.get().toUByte().toInt() else null
            val firmwareVersion3 = if (supportFirmwareVersion3) ByteArray(3).also {
                buffer.get(it)
            }.joinToString(".") { it.toUByte().toInt().toString() } else null
            val deviceId = if (supportDeviceId) ByteArray(3).also {
                buffer.get(it)
            }.toHexString() else null
            val partyLightInfo = if (supportPartyLightInfo) {
                val partyLightInfoBits =
                    ByteArray(2).also { buffer.get(it) }.let { BitSet.valueOf(ByteBuffer.wrap(it).order(ByteOrder.LITTLE_ENDIAN)) }
                PartyLightInfo(
                    partyLightInfoBits[15],
                    bitSetToInt(partyLightInfoBits[8, 15], 7)
                )
            } else null
            val firmwareVersion5 = if (supportFirmwareVersion5) ByteArray(5).also {
                buffer.get(it)
            }.joinToString(".") { it.toUByte().toInt().toString() } else null
            scanResult.scanRecord?.serviceData?.get(serviceUuid)?.also {
                Logger.d(TAG, "parse() >>> UUID[$btMacAddressCrc] deviceName[${scanResult.scanRecord?.deviceName}] ${HexUtil.encodeHexStr(it)}")

            }
            /** data END*/
            return V5ParseResult(
                pidString,
                colorId,
                srcName1Crc,
                srcName2Crc,
                btMacAddressCrc,
                btMacAddress,
                batteryInfo,
                deviceMiscInfo,
                partyMethodInfo,
                partyInfo,
                stereoGroupId,
                firmwareVersion3,
                deviceId,
                partyLightInfo,
                firmwareVersion5,
            )
        } catch (e: Exception) {
            return null
        }
    }
}

internal data class V5ParseResult(
    val pid: String?,
    val colorId: String?,
    val srcName1Crc: String?,
    val srcName2Crc: String?,
    val btMacAddressCrc: String?,
    val btMacAddress: String?,
    val batteryInfo: BatteryInfo?,
    val deviceMiscInfo: DeviceMiscInfo?,
    val partyMethodInfo: PartyMethodInfo?,
    val partyInfo: PartyInfo?,
    val stereoGroupId: Int?,
    val firmwareVersion3: String?,
    val deviceId: String?,
    val partyLightInfo: PartyLightInfo?,
    val firmwareVersion5: String?,
)