package com.harman.v5protocol

import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.BitSet

/**
 * @Description
 * <AUTHOR>
 * @Time 2024/12/19
 */
data object V5Util {
    fun bitSetToInt(bitSet: BitSet, bitCount: Int): Int {
        var value = 0
        for (i in 0 until bitCount) {
            if (bitSet[i]) {
                value = value or (1 shl i)
            }
        }
        return value
    }

    fun bitSetToByte(bitSet: BitSet, bitCount: Int = 8): Byte {
        return bitSetToInt(bitSet, bitCount).toByte()
    }

    /**
     * Get a [BitSet] with 8 bits and all bits are 1
     */
    fun bitSet8All1() = BitSet(8).apply { set(0, 8) }

    @OptIn(ExperimentalStdlibApi::class)
    fun bytes2HexWithSpace(bytes: ByteArray): String {
        return bytes.toHexString().chunked(2).joinToString(" ")
    }

//    fun bytes2UShort(bytes: ByteArray, order: ByteOrder = ByteOrder.LITTLE_ENDIAN): Int {
//        val value = ByteBuffer.wrap(byteArrayOf(bytes[0], bytes[1])).order(order).getShort().toInt()
//        return value and 0x0000FFFF
//    }

    fun int2Bytes(value: Int) = ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN).putShort(value.toShort()).array()
    fun int4Bytes(value: Int) = ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(value).array()

    fun bitsetToBinaryString(bitSet: BitSet): String {
        val sb = StringBuilder()
        for (i in 0 until bitSet.size()) {
            sb.append(if (bitSet[i]) "1" else "0")
        }
        return sb.toString()
    }

    fun ignore2Null(byte: Byte) = if (byte == 0xff.toByte()) null else byte
    fun ignore2Null(float: Float) = if (float == 0xffffffff.toFloat()) null else float
}