package com.harman.v5protocol.bean.devinfofeat

import java.io.Serializable

/**
 * @Description Define the read and write permission specifications for the dev feat data model that interacts with the device
 * <AUTHOR>
 * @Time 2024/12/23
 */

/**
 * If a dev feat data model can parse from a device's notification or read data, it needs to be implemented
 * the data model must Implement static methods that generated from payload,like:
 *        companion object {
 *            @V5Create
 *            fun fromPayload(payload: ByteArray): V5FirmwareVersion {
 *                val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
 *                val version = ByteArray(5).also {
 *                    buffer.get(it)
 *                }.joinToString(".") { it.toUInt().toInt().toString() }
 *                return V5FirmwareVersion(version)
 *            }
 *        }
 * However, some special feats can not define ,see [V5Create]
 */
interface IV5Payload : Serializable {
    val devFeatId: V5DevInfoFeatID
}

/**
 * If a dev feat data model can be written to a device, it needs to be implemented
 */
interface IV5Write : IV5Payload {
    fun valuePayload(): ByteArray
}

/**
 * If a dev feat data model can be read from a device, it needs to be implemented
 */
interface IV5Read : IV5Payload

/**
 * If a dev feat data model can both be written to and read from a device, it needs to be implemented
 */
interface IV5WriteRead : IV5Write, IV5Read


/**
 * All data models that implement [IV5Payload] must define a method for creating a model from payload in its companion object,
 * However, some data models will not be notified by the device, so you don't need to use them, such as [V5FactoryReset]
 * and decorate it with this annotation so that the communication layer can create the model by reflection
 */
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.FUNCTION)
annotation class V5Create