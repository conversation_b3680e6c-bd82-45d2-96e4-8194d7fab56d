package com.harman.v5protocol.bean.devinfofeat

import com.harman.discover.util.Tools.toByteArray
import com.harman.v5protocol.V5Util
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.ratio0To100ValueToFloat
import com.harman.v5protocol.bean.devinfofeat.SlotValue.Companion.ratioTo0100Value
import com.jbl.one.configuration.model.PresetEQDataConfig
import java.io.Serializable
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.roundToInt

/**
 * @Description Define a common data model that can be reused by the v5 protocol
 * <AUTHOR>
 * @Time 2025/2/26
 */
data class SlotValue(
    val id: Byte,
    var value: Float,
    val type: SlotValueTypeEnum
) : Serializable {

    fun valueString() = when (type) {
        SlotValueTypeEnum.Ratio -> value.toString()
        else -> "${value.toInt()}.0"
    }


    companion object {
        /**
         * 如果type类型是[SlotValueTypeEnum.Ratio],它的范围是[0,100],但是fw给过来的是经过下面算法转换后的小数，app用小数实现ui交互
         */
        fun Float.ratioTo0100Value() = run {
            ((100 - 100 / this) * 100 / 99).roundToInt()
        }

        /**
         * 如果type类型是[SlotValueTypeEnum.Time],它的范围是[20,1000],可调整步长为10
         */
        fun Float.timeTo0100Value() = run {
            ((this / 10) - 2).toInt()
        }

        //
        fun Int.ratio0To100ValueToFloat() = String.format("%.2f", 100f / (100 - this * 99 / 100f)).toFloat()
    }
}

fun main() {
    val a = 16.1f.ratioTo0100Value()
    val b = a.ratio0To100ValueToFloat()
    print("$a$b")
}

data class V5SlotItem(
    val slotId: SlotId,
    var algorithm: Algorithm? = null,
    var isBypass: Boolean? = null,
    var valueList: List<SlotValue>? = null,
) : Serializable {
    fun copyWith(new: V5SlotItem) = V5SlotItem(
        new.slotId,
        new.algorithm ?: algorithm,
        new.isBypass ?: isBypass,
        valueList?.toMutableList()?.also { oldList ->
            oldList.forEachIndexed { idx, orig ->
                new.valueList?.forEach {
                    if (it.id == orig.id) {
                        oldList[idx] = it
                    }
                }
            }
        }
            ?: new.valueList
    )

    fun valuePayload(): ByteArray {
        val bytes = mutableListOf<Byte>(
            slotId.id,
            (algorithm?.id ?: 0xff).toByte(),
            (algorithm?.configId ?: 0xff).toByte(),
            (if (true == isBypass) 1 else if (false == isBypass) 0 else 0xff).toByte(),
            valueList?.size?.toByte() ?: 0
        )
        valueList?.forEach {
            bytes.add(it.id)
            bytes.addAll(it.value.toByteArray().toList())
        }
        return bytes.toByteArray()
    }

    companion object {
        fun fromPayloadBuffer(buffer: ByteBuffer) = run {
            val slotId = buffer.get().let { SlotId.entries.find { e -> e.id == it } }!!
            val algorithmId = buffer.get()
            val configId = buffer.get()
            val algorithm = Algorithm.entries.find { it.id == algorithmId && it.configId == configId }
            val isBypass = buffer.get().let {
                if (it == 0xff.toByte()) null else it == 1.toByte()
            }
            val controlNumber = buffer.get().toUByte().toInt()
            var slotValues: MutableList<SlotValue>?
            if (controlNumber > 0) {
                slotValues = mutableListOf<SlotValue>()
                repeat(controlNumber) {
                    val controlId = buffer.get()
                    val value = buffer.getFloat()
                    slotValues!!.add(
                        SlotValue(
                            controlId,
                            value,
                            //业务上存在slotvalue的时候，algorithm必须不为空
                            algorithm?.valueTypes?.find { controlId == it.id }!!.type
                        )
                    )
                }
            } else {
                slotValues = null
            }
            V5SlotItem(slotId, algorithm, isBypass, slotValues)
        }
    }
}

data class GuitarPresetSimpleInfo(
    val presetId: Int,
    var genre: V5MusicGenreEnum,
    var name: String,
) : Serializable

data class SlotValueDefined(val type: SlotValueTypeEnum, val id: Byte)

data class V5EqInfo(
    val category: EQCategoryEnum,
    //bandbox ignore this field
    var status: EQStatusEnum,
    val bandCount: Int,
    val bandInfos: List<EQBandInfo>,
    var name: String? = null,
    val calibration: Float? = null,
    val sampleRateHz: Float? = null,
    val leftTotalGain: Float? = null,
    val rightTotalGain: Float? = null,
) : Serializable {

    fun copyWith(new: V5EqInfo) = V5EqInfo(
        new.category,
        new.status,
        new.bandCount,
        new.bandInfos,
        new.name ?: name,
        new.calibration ?: calibration,
        new.leftTotalGain ?: leftTotalGain,
        new.rightTotalGain ?: rightTotalGain,
    )

    fun valuePayload() = run {
        var arr = byteArrayOf(category.value, status.value, bandCount.toByte())
        arr += calibration?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        arr += sampleRateHz?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        arr += leftTotalGain?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        arr += rightTotalGain?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        bandInfos.forEach {
            arr += it.toValuePayload()
        }
        arr += name?.toByteArray(Charsets.UTF_8) ?: byteArrayOf()
        arr
    }

    data class EQBandInfo(val type: EQBandTypeEnum, val frequency: Float, var gain: Float, val qValue: Float) : Serializable {
        fun toValuePayload(): ByteArray =
            ByteBuffer.allocate(13).order(ByteOrder.LITTLE_ENDIAN).apply {
                put(type.value)
                putFloat(frequency)
                putFloat(gain)
                putFloat(qValue)
            }.array()
    }

    companion object {

        fun fromPayload(payload: ByteArray) = run {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val categoryId = buffer.get().let { EQCategoryEnum.entries.find { e -> e.value == it } }!!
            val status = buffer.get().let { EQStatusEnum.entries.find { e -> e.value == it } }!!
            val bandCount = buffer.get().toUByte().toInt()
            val calibration = V5Util.ignore2Null(buffer.getFloat())
            val sampleRateHz = V5Util.ignore2Null(buffer.getFloat())
            val leftTotalGain = V5Util.ignore2Null(buffer.getFloat())
            val rightTotalGain = V5Util.ignore2Null(buffer.getFloat())
            val bandInfos = mutableListOf<EQBandInfo>()
            repeat(bandCount) {
                val type = buffer.get().let { EQBandTypeEnum.entries.find { e -> e.value == it } }!!
                val frequency = buffer.getFloat()
                val gain = buffer.getFloat()
                val qValue = buffer.getFloat()
                bandInfos.add(EQBandInfo(type, frequency, gain, qValue))
            }
            val name = if (buffer.hasRemaining()) {
                ByteArray(buffer.remaining()).also { buffer.get(it) }.let { String(it, Charsets.UTF_8) }
            } else null
            V5EqInfo(categoryId, status, bandCount, bandInfos, name, calibration, sampleRateHz, leftTotalGain, rightTotalGain)
        }

        fun fromCloudPreset(eq: PresetEQDataConfig) = run {
            V5EqInfo(
                category = EQCategoryEnum.entries.find { it.name == eq.categoryId }!!,
                status = EQStatusEnum.Inactive,
                bandCount = eq.bands?.size ?: 0,
                bandInfos = eq.bands?.map {
                    EQBandInfo(
                        type = EQBandTypeEnum.entries.find { e -> e.name == it.type }!!,
                        frequency = it.frequency ?: 0f,
                        gain = it.gain ?: 0f,
                        qValue = it.qValue ?: 0f,
                    )
                } ?: listOf(),
                name = eq.displayName,
                sampleRateHz = eq.sampleRate?.toFloat(),
            )
        }
    }
}
