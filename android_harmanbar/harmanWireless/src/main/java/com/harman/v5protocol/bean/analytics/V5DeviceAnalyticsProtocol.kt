package com.harman.v5protocol.bean.analytics

import com.harman.v5protocol.bean.devinfofeat.V5Create

/**
 * @Description About the interface definition of device analytics
 * <AUTHOR>
 * @Time 2025/5/12
 */
/** Data analysis model interface */
interface IV5AnalyticsValue<CloudValueType> {

    val id: V5DevAnalyticsId

    /**
     * One cloud report k-value, [0 .. Int.MAX_VALUE]
     */
    val cloudKVs: List<Pair<String, CloudValueType>>
}

/** The underlying entity creates [IV5AnalyticsValue] by reflection，see also [V5Create]*/
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.FUNCTION)
annotation class V5AnalyticsValueCreate