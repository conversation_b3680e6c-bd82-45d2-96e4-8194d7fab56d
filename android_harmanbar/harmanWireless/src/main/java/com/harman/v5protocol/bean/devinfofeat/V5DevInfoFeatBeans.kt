package com.harman.v5protocol.bean.devinfofeat

import androidx.annotation.Keep
import com.harman.discover.util.Tools.toByteArray
import com.harman.v5protocol.V5Util
import com.harman.v5protocol.bean.devinfofeat.V5DrumTempo.Companion.MAX
import com.harman.v5protocol.bean.devinfofeat.V5DrumTempo.Companion.MIN
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeTempo.Companion.MAX
import com.harman.v5protocol.bean.devinfofeat.V5MetronomeTempo.Companion.MIN
import com.harman.v5protocol.bean.devinfofeat.V5PitchChange.Companion.MAX
import com.harman.v5protocol.bean.devinfofeat.V5PitchChange.Companion.MIN
import com.harmanbar.ble.utils.HexUtil
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.BitSet
import kotlin.reflect.KClass

/**
 * @Description Define all the corresponding data model for 'DEVICE INFO FEATURE' for v5 protocols,
 * All data model commands need to be prefixed with 'V5'
 * <AUTHOR>
 * @Time 2024/12/6
 */

/**
 * All device info feature supported by the V5 protocol will be defined here
 * @param id the feat id , 2 bytes
 * @param valueSize the feat value payload size, [Int.MIN_VALUE] indicates that size is dynamic
 * @param payloadClz This [id] corresponds to the data model
 */

enum class V5DevInfoFeatID(
    val id: Int,
    val valueSize: Int,
    val payloadClz: KClass<*>,
) {
    UnsupportedFeatureList(0x0000, Int.MIN_VALUE, V5UnsupportedFeatureList::class),
    FirmwareVersion(0x000c, 0x000e, V5FirmwareVersion::class),
    PrepareOtaPath(0x3800, 0x0001, V5PrepareOtaPath::class),
    DeviceOOBE(0x3801, 0x0001, V5DeviceOOBE::class),
    PitchChange(0x30c1, 0x0001, V5PitchChange::class),
    PitchStartEnd(0x30c0, 0x0001, V5PitchStartEnd::class),
    DrumStartEnd(0x3000, 0x0001, V5DrumStartEnd::class),
    MetronomeStartEnd(0x3040, 0x0001, V5MetronomeStartEnd::class),
    DrumTempo(0x3001, 0x0001, V5DrumTempo::class),
    MetronomeTempo(0x3041, 0x0001, V5MetronomeTempo::class),
    DrumSequence(0x3002, 0x0001, V5DrumSequence::class),
    DrumTap(0x3003, 0x0000, V5DrumTap::class),
    MetronomeTap(0x3043, 0x0000, V5MetronomeTap::class),
    MetronomeSequence(0x3042, 0x0001, V5MetronomeSequence::class),
    LooperRecordingStartStopFeature(0x3140, 0x0001, V5LooperRecordingStartStopFeature::class),
    LooperMusicSignalReportIntervalFeature(0x3141, 0x0002, V5LooperMusicSignalReportIntervalFeature::class),
    LooperMusicSignalFeature(0x3142, 0x0004, V5LooperMusicSignalFeature::class),
    LooperRecordTimeLengthFeature(0x3143, 0x0002, V5LooperRecordTimeLengthFeature::class),
    AudioDataTrimmingFeature(0x3144, 0x0004, V5AudioDataTrimmingFeature::class),
    LooperRecordPlayFeature(0x3145, 0x0001, V5LooperRecordPlayFeature::class),
    LooperRecordPlayJumpFeature(0x3146, 0x0002, V5LooperRecordPlayJumpFeature::class),
    LooperNumberOfBars(0x3147, 0x0001, V5LooperNumberOfBarsFeature::class),
    OutputRecordStorageLocationFeature(0x3180, 0x0001, V5OutputRecordStorageLocationFeature::class),
    OutputRecordingStartStopFeature(0x3181, 0x0001, V5OutputRecordingStartStopFeature::class),
    OutputSeparateTracksEnableFeature(0x3182, 0x0001, V5OutputSeparateTracksEnableFeature::class),
    LeftSerialNumber(0x0008, Int.MIN_VALUE, V5LeftSerialNumber::class),
    TotalPlaybackTime(0x1008, 0x0004, V5TotalPlaybackTime::class),
    DeviceName(0x0006, Int.MIN_VALUE, V5DeviceName::class),
    FeedbackToneStatus(0x200f, 0x0001, V5FeedbackToneStatus::class),
    BackwardScreen(0x3802, 0x0001, V5BackwardScreen::class),
    FactoryReset(0x000f, 0x0000, V5FactoryReset::class),
    AuracastStandardQuality(0x2005, 0x0001, V5AuracastStandardQuality::class),
    TunerStartEnd(0x3100, 0x0001, V5TunerStartEnd::class),
    TunerMode(0x3101, 0x0001, V5TunerMode::class),
    TunerBassMode(0x3102, 0x0001, V5TunerBassMode::class),
    TunerTempo(0x3103, 0x0002, V5TunerTempo::class),
    TunerString(0x3104, 0x0001, V5TunerString::class),
    TunerResult(0x3106, 0x000a, V5TunerResult::class),
    AIStemSeparationKaraokeMode(0x3080, 0x0001, V5AIStemSeparationKaraokeMode::class),
    AIStemSeparationTrackType(0x3081, 0x0002, V5AIStemSeparationTrackType::class),
    AIStemSeparationTrackVolume(0x3082, 0x0002, V5AIStemSeparationTrackVolume::class),
    AIStemSeparationTrackTypeQuery(0x3083, 0x0001, V5AIStemSeparationTrackTypeQuery::class),
    AIStemSeparationTrackVolumeQuery(0x3084, 0x0001, V5AIStemSeparationTrackVolumeQuery::class),
    AIStemSeparationMode(0x3085, 0x0001, V5AIStemSeparationMode::class),
    SyncTimeFeature(0x0031, 0x0009, V5SyncTimeFeature::class),
    MicSensitivityInLevelFeature(0x0C0B, 0x0001, V5MicSensitivityInLevelFeature::class),
    Mic2SensitivityInLevelFeature(0x0C0F, 0x0001, V5Mic2SensitivityInLevelFeature::class),
    Mic1SensitivityIndBFeature(0x0C0C, 0x0004, V5Mic1SensitivityIndBFeature::class),
    Mic2SensitivityIndBFeature(0x0C10, 0x0004, V5Mic2SensitivityIndBFeature::class),
    Mic1Echo(0x0c09, 0x0001, V5Mic1Echo::class),
    Mic1Reverb(0x0c0a, 0x0001, V5Mic1Reverb::class),
    Mic2Echo(0x0c0d, 0x0001, V5Mic2Echo::class),
    Mic2Reverb(0x0c0e, 0x0001, V5Mic2Reverb::class),
    ChannelInputStatusQuery(0x3400, 0x0001, V5ChannelInputStatusQuery::class),
    ChannelInputStatus(0x3401, 0x0003, V5ChannelInputStatus::class),
    AUXType(0x3404, 0x0001, V5AUXType::class),
    ChannelVolumeQuery(0x3405, 0x0002, V5ChannelVolumeQuery::class),
    ChannelVolume(0x3406, 0x0003, V5ChannelVolume::class),
    ActiveAudioSource(0x0BA1, 0x0001, V5ActiveAudioSource::class),
    AudioVolume(0x2d42, 0x0001, V5AudioVolume::class),
    USBStatusFeature(0x200A, 0x0001, V5USBStatusFeature::class),
    LeftDeviceSerialNumberFeature(0x0008, Int.MIN_VALUE, V5LeftDeviceSerialNumberFeature::class),
    RightDeviceSerialNumberFeature(0x0009, Int.MIN_VALUE, V5RightDeviceSerialNumberFeature::class),
    AuthenticateMode(0x003a, 0x0001, V5AuthenticateMode::class),
    AuthenticateButtonIsPressed(0x003b, 0x0001, V5AuthenticateButtonIsPressed::class),
    LeftDeviceBatteryStatus(0x000D, 0x0001, V5LeftDeviceBatteryStatus::class),
    AuracastMode(0x2004, 0x0001, V5AuracastMode::class),
    PartyStatus(0x2C00, 0x0001, V5PartyStatus::class),
    PartyGroupType(0x2C01, 0x0001, V5PartyGroupType::class),
    PartyConnectionStatus(0x2C02, 0x0001, V5PartyConnectionStatus::class),
    PartyRole(0x2C03, 0x0001, V5PartyRole::class),
    GuitarPresetAllSimpleInfo(0x3440, Int.MIN_VALUE, V5GuitarPresetAllSimpleInfoList::class),
    GuitarPresetSelectQuery(0x3441, 0x0001, V5GuitarPresetSelectQuery::class),
    GuitarPresetSelect(0x3442, 0x0002, V5GuitarPresetSelect::class),
    GuitarPresetInfoQuery(0x3443, 0x0001, V5GuitarPresetInfoQuery::class),
    GuitarPresetInfo(0x3444, Int.MIN_VALUE, V5GuitarPresetInfo::class),
    GuitarPresetSlotInfo(0x3445, Int.MIN_VALUE, V5GuitarPresetSlotInfo::class),
    GuitarPresetOperation(0x3446, Int.MIN_VALUE, V5GuitarPresetOperation::class),
    GuitarPresetOnProductList(0x3447, Int.MIN_VALUE, V5GuitarPresetOnProductList::class),
    EqInfoQuery(0x0e01, 0x0001, V5EqInfoQuery::class),
    EqInfo(0x0e02, Int.MIN_VALUE, V5EqInfoWrap::class),
    MasterControlEQInfoQuery(0x31c0, 0x0001, V5MasterControlEQInfoQuery::class),
    MasterControlEQInfo(0x31c1, Int.MIN_VALUE, V5MasterControlEQInfo::class),
    MicKeyboardEQInfoQuery(0x31c2, 0x0002, V5MicKeyboardEQInfoQuery::class),
    MicKeyboardEQInfo(0x31c3, 0x000e, V5MicKeyboardEQInfo::class),
    GuitarPickupQuery(0x3402, 0x0001, V5GuitarPickupQuery::class),
    GuitarPickup(0x3403, 0x0002, V5GuitarPickup::class),
}

data class V5GuitarPickup(
    val chNum: V5ChannelNumber,
    val type: PickupTypeEnum,
) : IV5Write {
    override fun valuePayload() = byteArrayOf(chNum.value, type.value)
    override val devFeatId = V5DevInfoFeatID.GuitarPickup

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = run {
            val chNum = payload[0].let { V5ChannelNumber.entries.find { e -> e.value == it } }!!
            val type = payload[1].let { PickupTypeEnum.entries.find { e -> e.value == it } }!!
            V5GuitarPickup(chNum, type)
        }
    }
}

data class V5GuitarPickupQuery(val chNum: V5ChannelNumber) : IV5Write {
    override fun valuePayload() = byteArrayOf(chNum.value)
    override val devFeatId = V5DevInfoFeatID.GuitarPickupQuery
}

data class V5PartyRole(val role: PartyRoleEnum) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(role.value)
    override val devFeatId = V5DevInfoFeatID.PartyRole

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5PartyRole(PartyRoleEnum.entries.find { it.value == payload[0] }!!)
    }
}

data class V5PartyConnectionStatus(val status: PartyConnectionStatusEnum) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.PartyConnectionStatus

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5PartyConnectionStatus(PartyConnectionStatusEnum.entries.find { it.value == payload[0] }!!)
    }
}

data class V5PartyStatus(val isOn: Boolean) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(if (isOn) 0x01 else 0x00)
    override val devFeatId = V5DevInfoFeatID.PartyStatus

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5PartyStatus(BitSet.valueOf(payload)[7])
    }
}

data class V5MicKeyboardEQInfo(
    val chNum: V5ChannelNumber,
    val inputType: V5CHInputType,
    val bassGain: Float? = null,
    val midGain: Float? = null,
    val trebleGain: Float? = null,
) : IV5Write {
    override fun valuePayload() = run {
        var arr = byteArrayOf(chNum.value, inputType.value)
        arr += bassGain?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        arr += midGain?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        arr += trebleGain?.toByteArray() ?: 0xffffffff.toFloat().toByteArray()
        arr
    }

    override val devFeatId = V5DevInfoFeatID.MicKeyboardEQInfo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = run {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val chNum = buffer.get().let { V5ChannelNumber.entries.find { e -> e.value == it } }!!
            val inputType = buffer.get().let { V5CHInputType.entries.find { e -> e.value == it } }!!
            val bassGain = V5Util.ignore2Null(buffer.getFloat())
            val midGain = V5Util.ignore2Null(buffer.getFloat())
            val trebleGain = V5Util.ignore2Null(buffer.getFloat())
            V5MicKeyboardEQInfo(chNum, inputType, bassGain, midGain, trebleGain)
        }
    }
}

data class V5MicKeyboardEQInfoQuery(val chNum: V5ChannelNumber, val inputType: V5CHInputType) : IV5Write {
    override fun valuePayload() = byteArrayOf(chNum.value, inputType.value)
    override val devFeatId = V5DevInfoFeatID.MicKeyboardEQInfoQuery
}

data class V5MasterControlEQInfo(val info: V5EqInfo) : IV5Write {
    override fun valuePayload() = info.valuePayload()
    override val devFeatId = V5DevInfoFeatID.MasterControlEQInfo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5MasterControlEQInfo(V5EqInfo.fromPayload(payload))
    }
}

data class V5MasterControlEQInfoQuery(val category: EQCategoryEnum) : IV5Write {
    override fun valuePayload() = byteArrayOf(category.value)
    override val devFeatId = V5DevInfoFeatID.MasterControlEQInfoQuery
}

data class V5EqInfoWrap(val info: V5EqInfo) : IV5Write {
    override fun valuePayload() = info.valuePayload()
    override val devFeatId = V5DevInfoFeatID.EqInfo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5EqInfoWrap(V5EqInfo.fromPayload(payload))
    }
}

data class V5EqInfoQuery(val category: EQCategoryEnum) : IV5Write {
    override fun valuePayload() = byteArrayOf(category.value)
    override val devFeatId = V5DevInfoFeatID.EqInfoQuery
}

data class V5GuitarPresetOnProductList(val presetIds: MutableList<Int>) : IV5WriteRead {
    override fun valuePayload() = presetIds.map { it.toByte() }.toByteArray()
    override val devFeatId = V5DevInfoFeatID.GuitarPresetOnProductList

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5GuitarPresetOnProductList(payload.map { it.toInt() }.toMutableList())
    }
}

data class V5GuitarPresetOperation(
    val chNum: V5ChannelNumber,
    val operation: V5GuitarPresetOperationEnum,
    val sourcePresetId: Int? = null,
    val newPresetId: Int? = null,
    val genre: V5MusicGenreEnum? = null,
    val name: String? = null,
) : IV5Write {
    override fun valuePayload() = byteArrayOf(
        chNum.value,
        operation.value,
        (sourcePresetId ?: 0xff).toByte(),
        (newPresetId ?: 0xff).toByte(),
        (genre?.value ?: 0xff).toByte(),
        (name?.length ?: 0).toByte(),
    ) + (name?.toByteArray(Charsets.UTF_8) ?: byteArrayOf())

    override val devFeatId = V5DevInfoFeatID.GuitarPresetOperation

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = run {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val chNum = buffer.get().let { V5ChannelNumber.entries.find { e -> e.value == it }!! }
            val operation = buffer.get().let { V5GuitarPresetOperationEnum.entries.find { e -> e.value == it }!! }
            val sourcePresetId = V5Util.ignore2Null(buffer.get())?.toInt()
            val newPresetId = V5Util.ignore2Null(buffer.get())?.toInt()
            val genre = buffer.get().let { V5MusicGenreEnum.fromByte(it) }
            val nameLen = buffer.get().toUByte().toInt()
            val name = if (nameLen > 0) ByteArray(nameLen).also { buffer.get(it) }.let { String(it, Charsets.UTF_8) } else null
            V5GuitarPresetOperation(chNum, operation, sourcePresetId, newPresetId, genre, name)
        }
    }
}

data class V5GuitarPresetSlotInfo(
    val chNum: V5ChannelNumber,
    val presetId: Int,
    val change: SlotChangeEnum,
    val slotItem: V5SlotItem,
) : IV5Write {
    override fun valuePayload() = byteArrayOf(chNum.value, presetId.toByte(), change.value) + slotItem.valuePayload()
    override val devFeatId = V5DevInfoFeatID.GuitarPresetSlotInfo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = run {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val chNum = buffer.get().let { V5ChannelNumber.entries.find { en -> en.value == it }!! }
            val presetId = buffer.get().toInt()
            val change = buffer.get().let { SlotChangeEnum.entries.find { en -> en.value == it }!! }
            val slot = V5SlotItem.fromPayloadBuffer(buffer)
            V5GuitarPresetSlotInfo(chNum, presetId, change, slot)
        }
    }
}


data class V5GuitarPresetInfo(
    val presetId: Int,
    var genre: V5MusicGenreEnum,
    var name: String,
    var slots: MutableList<V5SlotItem>,
) : IV5Payload {
    override val devFeatId = V5DevInfoFeatID.GuitarPresetInfo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5GuitarPresetInfo {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val presetId = buffer.get().toInt()
            val genre = V5MusicGenreEnum.fromByte(buffer.get())!!
            val nameLen = buffer.get().toUByte().toInt()
            val name = if (nameLen > 0) ByteArray(nameLen).also { buffer.get(it) }.let { String(it, Charsets.UTF_8) } else ""
            val slotNum = buffer.get().toUByte().toInt()
            val slots = mutableListOf<V5SlotItem>()
            repeat(slotNum) {
                slots.add(V5SlotItem.fromPayloadBuffer(buffer))
            }
            return V5GuitarPresetInfo(presetId, genre, name, slots)
        }
    }
}

data class V5GuitarPresetInfoQuery(val presetId: Int) : IV5Write {
    override fun valuePayload() = byteArrayOf(presetId.toByte())
    override val devFeatId = V5DevInfoFeatID.GuitarPresetInfoQuery
}

data class V5GuitarPresetSelect(val chNum: V5ChannelNumber, val presetId: Int) : IV5Write {
    override fun valuePayload() = byteArrayOf(chNum.value, presetId.toByte())
    override val devFeatId = V5DevInfoFeatID.GuitarPresetSelect

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5GuitarPresetSelect(V5ChannelNumber.entries.find { it.value == payload[0] }!!, payload[1].toInt())
    }
}


data class V5GuitarPresetSelectQuery(val chNum: V5ChannelNumber) : IV5Write {
    override fun valuePayload() = byteArrayOf(chNum.value)
    override val devFeatId = V5DevInfoFeatID.GuitarPresetSelectQuery
}


data class V5GuitarPresetAllSimpleInfoList(val list: MutableList<GuitarPresetSimpleInfo>) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.GuitarPresetAllSimpleInfo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5GuitarPresetAllSimpleInfoList {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val list = mutableListOf<GuitarPresetSimpleInfo>()
            while (buffer.hasRemaining()) {
                val presetId = buffer.get().toInt()
                val genre = buffer.get().let { V5MusicGenreEnum.entries.find { en -> en.value == it }!! }
                val nameLen = buffer.get().toUByte().toInt()
                val name = if (nameLen > 0) ByteArray(nameLen).also { buffer.get(it) }.let { String(it, Charsets.UTF_8) } else ""
                list.add(GuitarPresetSimpleInfo(presetId, genre, name))
            }
            return V5GuitarPresetAllSimpleInfoList(list)
        }
    }
}


@Keep
data class V5PartyGroupType(val type: V5PartyGroupTypeEnum) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(type.value)
    override val devFeatId = V5DevInfoFeatID.PartyGroupType

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5PartyGroupType(V5PartyGroupTypeEnum.entries.find { it.value == payload[0] }!!)
    }
}

@Keep
data class V5AuracastMode(val mode: V5AuracastModeEnum) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(mode.value)
    override val devFeatId = V5DevInfoFeatID.AuracastMode

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AuracastMode(V5AuracastModeEnum.entries.find { it.value == payload[0] }!!)
    }
}

@Keep
data class V5LeftDeviceBatteryStatus(val isCharging: Boolean, val batteryPercent: Int) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.LeftDeviceBatteryStatus

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = BitSet.valueOf(payload).let {
            V5LeftDeviceBatteryStatus(it[7], V5Util.bitSetToInt(it, 7))
        }
    }
}

class V5AuthenticateButtonIsPressed : IV5Payload {
    override val devFeatId = V5DevInfoFeatID.AuthenticateButtonIsPressed

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AuthenticateButtonIsPressed()
    }
}

@Keep
data class V5AuthenticateMode(val mode: Mode) : IV5Write {
    enum class Mode(val value: Byte) {
        Exit(0x00),
        Enter(0x01)
    }

    override fun valuePayload() = byteArrayOf(mode.value)
    override val devFeatId = V5DevInfoFeatID.AuthenticateMode
}

@Keep
data class V5ChannelInputStatus(val channelNumber: V5ChannelNumber, val isDetected: Boolean, val inputType: V5CHInputType) : IV5Payload {
    override val devFeatId = V5DevInfoFeatID.ChannelInputStatus

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5ChannelInputStatus {
            return V5ChannelInputStatus(
                V5ChannelNumber.entries.find { it.value == payload[0] }!!,
                payload[1] == 1.toByte(),
                V5CHInputType.entries.find { it.value == payload[2] }!!,
            )
        }
    }
}

@Keep
data class V5ChannelInputStatusQuery(val channelNumber: V5ChannelNumber) : IV5Write {
    override fun valuePayload() = byteArrayOf(channelNumber.value)
    override val devFeatId = V5DevInfoFeatID.ChannelInputStatusQuery
}


/**
 * @param value range is [0,32]
 */
@Keep
data class V5AudioVolume(val value: Int) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(value.toByte())
    override val devFeatId = V5DevInfoFeatID.AudioVolume

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AudioVolume(payload[0].toUByte().toInt())
    }
}

@Keep
data class V5ActiveAudioSource(val source: V5AudioSource) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(source.value)
    override val devFeatId = V5DevInfoFeatID.ActiveAudioSource

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5ActiveAudioSource(V5AudioSource.entries.find { it.value == payload[0] }!!)
    }
}

/**
 * @param volume range is [0,32]
 */
@Keep
data class V5ChannelVolume(val channelNumber: V5ChannelNumber, val inputType: V5CHInputType, val volume: Int) : IV5Write {
    override fun valuePayload() = byteArrayOf(channelNumber.value, inputType.value, volume.toByte())
    override val devFeatId = V5DevInfoFeatID.ChannelVolume

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5ChannelVolume(
            V5ChannelNumber.entries.find { it.value == payload[0] }!!,
            V5CHInputType.entries.find { it.value == payload[1] }!!,
            payload[2].toUByte().toInt(),
        )
    }
}

@Keep
data class V5ChannelVolumeQuery(val channelNumber: V5ChannelNumber, val inputType: V5CHInputType) : IV5Write {
    override fun valuePayload() = byteArrayOf(channelNumber.value, inputType.value)
    override val devFeatId = V5DevInfoFeatID.ChannelVolumeQuery
}

@Keep
data class V5AUXType(val type: Type) : IV5WriteRead {
    enum class Type(val value: Byte) {
        Instrument(0x01),
        Music(0x02),
    }

    override fun valuePayload() = byteArrayOf(type.value)
    override val devFeatId = V5DevInfoFeatID.AUXType

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AUXType(Type.entries.find { it.value == payload[0] }!!)
    }
}

/**
 * @param value range is [0,12]
 */
@Keep
data class V5Mic2Reverb(val value: Int) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(value.toByte())
    override val devFeatId = V5DevInfoFeatID.Mic2Reverb

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5Mic2Reverb(payload[0].toInt())
    }
}

/**
 * @param value range is [0,12]
 */
@Keep
data class V5Mic2Echo(val value: Int) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(value.toByte())
    override val devFeatId = V5DevInfoFeatID.Mic2Echo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5Mic2Echo(payload[0].toInt())
    }
}

/**
 * @param value range is [0,12]
 */
@Keep
data class V5Mic1Reverb(val value: Int) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(value.toByte())
    override val devFeatId = V5DevInfoFeatID.Mic1Reverb

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5Mic1Reverb(payload[0].toInt())
    }
}

/**
 * @param value range is [0,12]
 */
@Keep
data class V5Mic1Echo(val value: Int) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(value.toByte())
    override val devFeatId = V5DevInfoFeatID.Mic1Echo

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5Mic1Echo(payload[0].toInt())
    }
}

@Keep
data class V5TunerString(val string: TString) : IV5WriteRead {
    enum class TString(val value: Byte, val mName: String, val stringNames: List<String>, val stringType: StringType) {
        //Guitar
        Standard(0x00, "Standard", listOf("E", "A", "D", "G", "B", "E"), StringType.Guitar),
        DropD(0x01, "Drop D", listOf("D", "A", "D", "G", "B", "E"), StringType.Guitar),
        FullStepDown(0x02, "Full Step Down", listOf("D", "G", "C", "F", "A", "D"), StringType.Guitar),
        HalfStepUp(0x03, "Half Step Up", listOf("F", "A#", "D#", "G#", "C", "F"), StringType.Guitar),
        OpenC(0x04, "Open C", listOf("C", "G", "C", "G", "C", "E"), StringType.Guitar),
        OpenD(0x05, "Open D", listOf("D", "A", "D", "F#", "A", "D"), StringType.Guitar),
        OpenE(0x06, "Open E", listOf("E", "B", "E", "G#", "B", "E"), StringType.Guitar),
        OpenF(0x07, "Open F", listOf("F", "C", "F", "A", "C", "F"), StringType.Guitar),
        OpenG(0x08, "Open G", listOf("D", "G", "D", "G", "B", "D"), StringType.Guitar),
        OpenA(0x09, "Open A", listOf("E", "A", "E", "A", "C#", "E"), StringType.Guitar),
        DropCHash(0x0A, "Drop C#", listOf("C#", "G#", "C#", "F#", "A", "D#"), StringType.Guitar),//DropC#
        DropCHashAlt1(0x0B, "Drop C# (Alt 1)", listOf("C#", "G#", "C#", "E", "A", "D#"), StringType.Guitar), //DropC#Alt1
        DropCHashAlt2(0x0C, "Drop C# (Alt 2)", listOf("C#", "G#", "C#", "F", "A", "D#"), StringType.Guitar), //DropC#Alt2
        DropC(0x0D, "Drop C", listOf("C", "G", "C", "F", "A", "D"), StringType.Guitar),// DropC
        DropB(0x0E, "Drop B", listOf("B", "F#", "B", "E", "G#", "C#"), StringType.Guitar), //DropB
        DropA(0x0F, "Drop A", listOf("A", "E", "A", "D", "F#", "B"), StringType.Guitar),// DropA
        DADGAD(0x10, "DADGAD", listOf("D", "A", "D", "G", "A", "D"), StringType.Guitar),// DADGAD
        HalfStepDown(0x11, "Half Step Down", listOf("D#", "G#", "C#", "F#", "A#", "D#"), StringType.Guitar),//HalfStepDown

        //Bass
        BassStandard(0x12, "Standard", listOf("E", "A", "D", "G"), StringType.Bass4String),
        BassDropD(0x13, "DropD", listOf("D", "A", "D", "G"), StringType.Bass4String),
        BassDropC(0x14, "DropC", listOf("C", "G", "C", "F"), StringType.Bass4String),
        BassBASSDeep(0x15, "BASSDeep", listOf("B", "E", "A", "D"), StringType.Bass4String),
        BassOpenD(0x16, "OpenD", listOf("D", "A", "D", "G"), StringType.Bass4String),
        BassLowered(0x17, "Lowered", listOf("B", "E", "A", "D"), StringType.Bass4String),
//        BassString5(0x18, "String5", listOf("B", "E", "A", "D", "G"), StringType.Bass4String),

        //Ukulele
        UStandard(0x19, "Standard", listOf("G", "C", "E", "A"), StringType.Ukulele),
        ULowG(0x1a, "LowG", listOf("G", "C", "E", "A"), StringType.Ukulele),
        UD(0x1b, "D", listOf("D", "F#", "A", "B"), StringType.Ukulele),
        UCanadian(0x1c, "Canadian", listOf("D", "G", "B", "D"), StringType.Ukulele),
        UVestapol(0x1d, "Vestapol", listOf("D", "G", "B", "E"), StringType.Ukulele),
        UOpenC(0x1e, "OpenC", listOf("C", "G", "C", "E"), StringType.Ukulele),
        UOpenD(0x1f, "OpenD", listOf("D", "A", "D", "F#"), StringType.Ukulele),
    }

    /** The app's classification of [TString] according to the UI definition */
    enum class StringType {
        Guitar,
        Bass4String,
        Ukulele,
    }

    override fun valuePayload() = byteArrayOf(string.value)
    override val devFeatId = V5DevInfoFeatID.TunerString

    companion object {
        fun getTunerStringByStringType(type: StringType) = TString.entries.filter { it.stringType == type }

        @V5Create
        fun fromPayload(payload: ByteArray) = V5TunerString(TString.entries.find { it.value == payload[0] }!!)
    }
}


/** @param result range in [[MIN_RESULT],[MAX_RESULT]] */
@Keep
data class V5TunerResult(val frequency: Float, val tunerMode: V5TunerMode.Mode, val result: Float, val hasSignal: Boolean) :
    IV5Payload {
    enum class Appraise(val range: IntRange) {
        Perfect(-3..3),
        ToneUpSlightly(-30 until -3),
        ToneUpSlightly2(-130 until -30),
        ToneUp(-150 until -130),
        ToneDownSlightly(4..30),
        ToneDownSlightly2(31..130),
        ToneDown(131..150),
    }

    fun appraise(): Appraise? {
        return if (hasSignal) {
            Appraise.entries.find { result.toInt() in it.range }
        } else {
            null
        }
    }

    companion object {
        const val MIN_RESULT = -150f
        const val MAX_RESULT = 150f

        @V5Create
        fun fromPayload(payload: ByteArray): V5TunerResult {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val frequency = buffer.getFloat()
            val string = buffer.get()
            val result = buffer.getFloat()
            val hasSignal = buffer.get()
            return V5TunerResult(
                frequency,
                V5TunerMode.Mode.entries.find { it.value == string }!!,
                result,
                hasSignal == 1.toByte(),
            )
        }
    }

    override val devFeatId = V5DevInfoFeatID.TunerResult
}

/** @param hzInt range in [[MIN_HZ],[MAX_HZ]] */
@Keep
data class V5TunerTempo(val hzInt: Int) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN).putShort(hzInt.toShort()).array()
    }

    override val devFeatId = V5DevInfoFeatID.TunerTempo

    fun hzFloat() = hzInt / 10f

    companion object {
        const val MIN_HZ = 4200
        const val MAX_HZ = 4600
        const val STANDARD_HZ = 4400

        @V5Create
        fun fromPayload(payload: ByteArray): V5TunerTempo {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN).order(ByteOrder.LITTLE_ENDIAN)
            return V5TunerTempo(buffer.getShort().toUShort().toInt())
        }
    }
}

@Keep
data class V5AIStemSeparationMode(val isOn: Boolean) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(if (isOn) 1 else 0)
    override val devFeatId = V5DevInfoFeatID.AIStemSeparationMode

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AIStemSeparationMode(payload[0] == 1.toByte())
    }
}

@Keep
data class V5AIStemSeparationTrackVolumeQuery(val trackNumber: V5TrackNumber) : IV5Write {

    override fun valuePayload() = byteArrayOf(trackNumber.value)
    override val devFeatId = V5DevInfoFeatID.AIStemSeparationTrackVolumeQuery
}

@Keep
data class V5AIStemSeparationTrackTypeQuery(val trackNumber: V5TrackNumber) : IV5Write {
    override fun valuePayload() = byteArrayOf(trackNumber.value)
    override val devFeatId = V5DevInfoFeatID.AIStemSeparationTrackTypeQuery
}

/**
 * @param volume range in [[MIN_VOLUME],[MAX_VOLUME]]
 */
@Keep
data class V5AIStemSeparationTrackVolume(val trackNumber: V5TrackNumber, val volume: Int) : IV5Write {
    override fun valuePayload() = byteArrayOf(trackNumber.value, volume.toByte())
    override val devFeatId = V5DevInfoFeatID.AIStemSeparationTrackVolume

    companion object {
        const val MIN_VOLUME = 0
        const val MAX_VOLUME = 32

        @V5Create
        fun fromPayload(payload: ByteArray) = V5AIStemSeparationTrackVolume(
            V5TrackNumber.entries.find { it.value == payload[0] }!!,
            payload[1].toUByte().toInt(),
        )
    }
}

@Keep
data class V5AIStemSeparationTrackType(val trackNumber: V5TrackNumber, val trackType: Type) : IV5Write {
    enum class Type(val value: Byte) {
        Nothing(0x00),// Separate Nothing from Original Music to Track specified in 1st byte
        Guitar(0x01), //Separate Guitar from Original Music to Track specified in 1st byte
        Vocal(0x02),// Separate Vocal from Original Music to Track specified in 1st byte
        Drum(0x03),//Separate Drum from Original Music to Track specified in 1st byte
    }

    override fun valuePayload() = byteArrayOf(trackNumber.value, trackType.value)

    override val devFeatId = V5DevInfoFeatID.AIStemSeparationTrackType

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AIStemSeparationTrackType(
            V5TrackNumber.entries.find { it.value == payload[0] }!!,
            Type.entries.find { it.value == payload[1] }!!,
        )
    }
}

@Keep
data class V5AIStemSeparationKaraokeMode(val isOn: Boolean) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(if (isOn) 1 else 0)
    override val devFeatId = V5DevInfoFeatID.AIStemSeparationKaraokeMode

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5AIStemSeparationKaraokeMode {
            return V5AIStemSeparationKaraokeMode(1.toByte() == payload[0])
        }
    }
}

@Keep
data class V5TunerBassMode(val mode: Mode) : IV5WriteRead {
    enum class Mode(val value: Byte, val mName: String) {
        Guitar33(0x00, "Guitar 3+3"),
        Guitar6InLine(0x01, "Guitar 6-in-line"),
        Bass4String(0x02, "Bass 4-String"),
        Ukulele(0x03, "Ukulele"),
    }

    override fun valuePayload() = byteArrayOf(mode.value)

    override val devFeatId = V5DevInfoFeatID.TunerBassMode

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5TunerBassMode {
            return V5TunerBassMode(Mode.entries.find { it.value == payload[0] }!!)
        }
    }
}

@Keep
data class V5TunerMode(val mode: Mode) : IV5WriteRead {
    enum class Mode(val value: Byte) {
        Auto(0x00),
        String1(0x01),
        String2(0x02),
        String3(0x03),
        String4(0x04),
        String5(0x05),
        String6(0x06);

        fun isAuto() = this == Auto
    }

    override fun valuePayload() = byteArrayOf(mode.value)

    override val devFeatId = V5DevInfoFeatID.TunerMode

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5TunerMode {
            return V5TunerMode(Mode.entries.find { it.value == payload[0] }!!)
        }
    }
}

@Keep
data class V5TunerStartEnd(val start: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(if (start) 1 else 0)
    }

    override val devFeatId = V5DevInfoFeatID.TunerStartEnd

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5TunerStartEnd {
            return V5TunerStartEnd(1.toByte() == payload[0])
        }
    }
}

@Keep
data class V5AuracastStandardQuality(val isOn: Boolean) : IV5WriteRead {
    override fun valuePayload() = byteArrayOf(if (isOn) 1 else 0)

    override val devFeatId = V5DevInfoFeatID.AuracastStandardQuality

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5AuracastStandardQuality(payload[0] == 1.toByte())
    }
}

class V5FactoryReset : IV5Write {
    override fun valuePayload() = byteArrayOf()

    override val devFeatId = V5DevInfoFeatID.FactoryReset

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5FactoryReset()
    }
}

@Keep
data class V5BackwardScreen(val enable: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        val bitSet = V5Util.bitSet8All1().apply { set(7, enable) }
        return byteArrayOf(V5Util.bitSetToByte(bitSet))
    }

    override val devFeatId = V5DevInfoFeatID.BackwardScreen

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5BackwardScreen {
            val bitset = BitSet.valueOf(payload)
            return V5BackwardScreen(bitset[7])
        }
    }
}


@Keep
data class V5FeedbackToneStatus(val enable: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        val bitSet = V5Util.bitSet8All1().apply { set(7, enable) }
        return byteArrayOf(V5Util.bitSetToByte(bitSet))
    }

    override val devFeatId = V5DevInfoFeatID.FeedbackToneStatus

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5FeedbackToneStatus {
            val bitset = BitSet.valueOf(payload)
            return V5FeedbackToneStatus(bitset[7])
        }
    }
}

@Keep
data class V5DeviceName(val name: String) : IV5WriteRead {

    override fun valuePayload(): ByteArray {
        return name.toByteArray(Charsets.UTF_8)
    }

    override val devFeatId = V5DevInfoFeatID.DeviceName

    companion object {
        //The maximum length of the name after it is converted to an array of bytes
        const val MAX_NAME_BYTE_LENGTH = 32

        @V5Create
        fun fromPayload(payload: ByteArray): V5DeviceName {
            return V5DeviceName(String(payload, Charsets.UTF_8))
        }
    }
}

@Keep
data class V5TotalPlaybackTime(val minutes: Long) : IV5Read {
    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5TotalPlaybackTime {
            val minutes = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN).getInt().toUInt().toLong()
            return V5TotalPlaybackTime(minutes)
        }
    }

    override val devFeatId = V5DevInfoFeatID.TotalPlaybackTime
}

data class V5LeftSerialNumber(val serialNumber: String) : IV5Read {
    companion object {
        @OptIn(ExperimentalStdlibApi::class)
        @V5Create
        fun fromPayload(payload: ByteArray): V5LeftSerialNumber {
            return V5LeftSerialNumber(HexUtil.hexStringToString(payload.toHexString()) ?: "--")
        }
    }

    override val devFeatId = V5DevInfoFeatID.LeftSerialNumber
}

class V5DrumTap() : IV5Payload {
    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5DrumTap()
    }

    override val devFeatId = V5DevInfoFeatID.DrumTap
}

class V5MetronomeTap : IV5Payload {
    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5MetronomeTap()
    }

    override val devFeatId = V5DevInfoFeatID.MetronomeTap
}

data class V5UnsupportedFeatureList(val unsupported: List<V5DevInfoFeatID>) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.UnsupportedFeatureList

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5UnsupportedFeatureList {
            val list = mutableListOf<V5DevInfoFeatID>()
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            while (buffer.hasRemaining()) {
                val feat = buffer.getShort()
                V5DevInfoFeatID.entries.find { feat == it.id.toShort() }?.also {
                    list.add(it)
                }
            }
            return V5UnsupportedFeatureList(list)
        }
    }
}

data class V5MetronomeSequence(val beat: Beat) : IV5WriteRead {
    enum class Beat(val value: Byte, val beatName: String) {
        B24(0x00, "2/4"),
        B34(0x01, "3/4"),
        B44(0x02, "4/4"),
        B68(0x03, "6/8"),
    }

    override fun valuePayload(): ByteArray = byteArrayOf(beat.value)
    override val devFeatId = V5DevInfoFeatID.MetronomeSequence

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5MetronomeSequence {
            return V5MetronomeSequence(Beat.entries.find { it.value == payload[0] }!!)
        }
    }
}

data class V5DrumSequence(val type: Type) : IV5WriteRead {
    enum class Type(val value: Byte, val beat: Beat) {
        Floor44(0x07, Beat.B44),// Type is Floor, Time signature is 4/4
        Beat44(0x08, Beat.B44),// Type is Beat, Time signature is 4/4
        Rock44(0x09, Beat.B44),// Type is Rock, Time signature is 4/4
        Funk44(0x0A, Beat.B44),//Type is Funk, Time signature is 4/4
        Clave44(0x0B, Beat.B44),// Type is Clave, Time signature is 4/4
        Reggaeton44(0x0C, Beat.B44),//Type is Reggaeton, Time signature is 4/4
        Hiphop44(0x0D, Beat.B44),//Type is Hiphop, Time signature is 4/4
        Drum43(0x05, Beat.B43),//Type is Drum, Time signature is 3/4
        Drum42(0x04, Beat.B42),//Type is Drum, Time signature is 2/4
        Drum68(0x06, Beat.B68),// Type is Drum, Time signature is 6/8
        Jazz68(0x0E, Beat.B68),//Type is Jazz, Time signature is 6/8
    }

    enum class Beat(val beatName: String) { B44("4/4"), B43("4/3"), B42("4/2"), B68("6/8") }

    override fun valuePayload(): ByteArray {
        return byteArrayOf(type.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.DrumSequence

    companion object {
        val beatTypes = mapOf(
            Beat.B44 to listOf(Type.Floor44, Type.Beat44, Type.Rock44, Type.Funk44, Type.Clave44, Type.Reggaeton44, Type.Hiphop44),
            Beat.B43 to listOf(Type.Drum43),
            Beat.B42 to listOf(Type.Drum42),
            Beat.B68 to listOf(Type.Drum68, Type.Jazz68)
        )

        @V5Create
        fun fromPayload(payload: ByteArray): V5DrumSequence {
            return V5DrumSequence(Type.entries.find { it.value == payload[0] }!!)
        }
    }
}

/** @param value range in [[MIN],[MAX]] */
data class V5MetronomeTempo(val value: Int) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(value.toByte())
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.MetronomeTempo

    companion object {
        const val MIN = 30
        const val MAX = 240

        @V5Create
        fun fromPayload(payload: ByteArray): V5MetronomeTempo {
            return V5MetronomeTempo(payload[0].toUByte().toInt())
        }
    }
}

/** @param value range in [[MIN],[MAX]] */
data class V5DrumTempo(val value: Int) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(value.toByte())
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.DrumTempo

    companion object {
        const val MIN = 30
        const val MAX = 240

        @V5Create
        fun fromPayload(payload: ByteArray): V5DrumTempo {
            return V5DrumTempo(payload[0].toUByte().toInt())
        }
    }
}

data class V5MetronomeStartEnd(val start: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(if (start) 1 else 0)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.MetronomeStartEnd

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5MetronomeStartEnd {
            return V5MetronomeStartEnd(1.toByte() == payload[0])
        }
    }
}

data class V5DrumStartEnd(val start: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(if (start) 1 else 0)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.DrumStartEnd

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5DrumStartEnd {
            return V5DrumStartEnd(1.toByte() == payload[0])
        }
    }
}

class V5FirmwareVersion private constructor(val version: String) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.FirmwareVersion

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5FirmwareVersion {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val version = ByteArray(5).also {
                buffer.get(it)
            }.joinToString(".") { it.toUByte().toInt().toString() }
            return V5FirmwareVersion(version)
        }
    }
}

/** Let the device exit or enter OTA mode true-iap false-uac */
data class V5PrepareOtaPath(val enable: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        val bitSet = V5Util.bitSet8All1().apply { set(7, enable) }
        return byteArrayOf(V5Util.bitSetToByte(bitSet))
    }

    override val devFeatId = V5DevInfoFeatID.PrepareOtaPath

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5PrepareOtaPath {
            val bitset = BitSet.valueOf(payload)
            return V5PrepareOtaPath(bitset[7])
        }
    }
}

/**
 * The bandbox solo device musician data model based on the V5 protocol
 * @param musician The value range is defined in 'companion object'
 */
data class V5DeviceOOBE(val musician: SoloMusicianEnum) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(musician.value)
    }

    override val devFeatId = V5DevInfoFeatID.DeviceOOBE
    val isReset = musician == SoloMusicianEnum.RESET

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray) = V5DeviceOOBE(SoloMusicianEnum.entries.find { e -> e.value == payload[0] }!!)
    }
}

/**
 *  set pitch value
 * @param value range in [[MIN],[MAX]]
 */
data class V5PitchChange(val value: Int) : IV5WriteRead {

    override fun valuePayload(): ByteArray {
        return byteArrayOf(value.toByte())
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.PitchChange

    companion object {
        const val MAX = 5
        const val MIN = -5

        @V5Create
        fun fromPayload(payload: ByteArray): V5PitchChange {
            return V5PitchChange(payload[0].toInt())
        }
    }
}

data class V5PitchStartEnd(val start: Boolean) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(if (start) 1 else 0)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.PitchStartEnd

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5PitchStartEnd {
            return V5PitchStartEnd(1.toByte() == payload[0])
        }
    }
}

/**
 * Looper Recording Start / Stop
 * 0x3140	R / W	0x0001
 * 0x00: Stop  false
 * 0x01: Start true
 */
data class V5LooperRecordingStartStopFeature(val statue: LooperRecordingStartStopFeature) : IV5WriteRead {
    enum class LooperRecordingStartStopFeature(val value: Byte) {
        Stop(0x00),      //Stop
        Start1st(0x01),  //Start 1st layer
        Start2nd(0x02),  //Start 2nd layer
        Start3rd(0x03),  //Start 3rd layer: Merge two layers to one and start 2nd layer.
        Undo(0x04),      //Undo: Undo the 2nd layer
        Discard(0x05)    //Discard all layer
    }

    override fun valuePayload(): ByteArray {
        return byteArrayOf(statue.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.LooperRecordingStartStopFeature

    companion object {

        @V5Create
        fun fromPayload(payload: ByteArray): V5LooperRecordingStartStopFeature {
            return V5LooperRecordingStartStopFeature(LooperRecordingStartStopFeature.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/**
 * Looper Music Signal Report Interval
 * 0x3141	R / W
 * 0x0002	The time in millisecond
 */

data class V5LooperMusicSignalReportIntervalFeature(val time: Int) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        val payLoadBuffer =
            ByteBuffer.allocate(V5DevInfoFeatID.LooperMusicSignalReportIntervalFeature.valueSize)
                .order(ByteOrder.LITTLE_ENDIAN).putShort(time.toShort())
        return payLoadBuffer.array()
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.LooperMusicSignalReportIntervalFeature


    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5LooperMusicSignalReportIntervalFeature {
            return V5LooperMusicSignalReportIntervalFeature(
                ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN).getShort().toUShort()
                    .toInt()
            )
        }
    }
}

/**
 * Looper Music Signal	0x3142	-	0x0004
 * The music signal value in Float type reported by device in each Looper Music Signal Report Interval
 */
class V5LooperMusicSignalFeature private constructor(val signal: Float) : IV5Payload {
    override val devFeatId = V5DevInfoFeatID.LooperMusicSignalFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5LooperMusicSignalFeature {
            val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            val signal = buffer.getFloat()
            return V5LooperMusicSignalFeature(signal)
        }
    }
}

/**
 *
 * Looper Record Time Length
 * 0x3143	R	0x0002	The time length in millisecond.
 * The max value is 60000.
 * Only can get the time length after the recording is stopped.
 *
 */
class V5LooperRecordTimeLengthFeature private constructor(val timeLength: Int) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.LooperRecordTimeLengthFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5LooperRecordTimeLengthFeature {
            return V5LooperRecordTimeLengthFeature(
                ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN).getShort().toUShort()
                    .toInt()
            )
        }
    }
}

/**
 * Looper Audio Data Trimming	0x3144	W	0x0004	1st ~ 2nd bytes: The start time in millisecond
 * 3rd ~ 4th bytes: The end time in millisecond
 */
data class V5AudioDataTrimmingFeature(val startTime: Int, val endTime: Int) : IV5Write {
    override fun valuePayload(): ByteArray {
        val payLoadBuffer =
            ByteBuffer.allocate(V5DevInfoFeatID.AudioDataTrimmingFeature.valueSize)
                .order(ByteOrder.LITTLE_ENDIAN)
        payLoadBuffer.putShort(startTime.toShort())
        payLoadBuffer.putShort(endTime.toShort())
        return payLoadBuffer.array()
    }

    override val devFeatId = V5DevInfoFeatID.AudioDataTrimmingFeature
}

/**
 * Looper Record Play	0x3144	R / W	0x0001
 * 0x00: Stop
 * 0x01: Start
 * 0x02: Pause
 * 0x03: Resume
 */
data class V5LooperRecordPlayFeature(val status: LooperRecordPlayStatus) : IV5WriteRead {
    enum class LooperRecordPlayStatus(val value: Byte) {
        Stop(0x00),
        Start(0x01),
        Pause(0x02),
        Resume(0x03),
    }

    override fun valuePayload(): ByteArray {
        return byteArrayOf(status.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.LooperRecordPlayFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5LooperRecordPlayFeature {
            return V5LooperRecordPlayFeature(LooperRecordPlayStatus.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/**
 *
 * Looper Record Play Jump
 * 0x3146
 * W
 * 0x0002
 * Jump to a new time to play
 * The time unit is millisecond
 */
data class V5LooperRecordPlayJumpFeature(val currentPosition: Int) : IV5Write {

    override fun valuePayload(): ByteArray {
        val payLoadBuffer =
            ByteBuffer.allocate(V5DevInfoFeatID.LooperRecordPlayJumpFeature.valueSize)
                .order(ByteOrder.LITTLE_ENDIAN)
        payLoadBuffer.putShort(currentPosition.toShort())
        return payLoadBuffer.array()
    }

    override val devFeatId: V5DevInfoFeatID = V5DevInfoFeatID.LooperRecordPlayJumpFeature
}

/**
 * Looper Number of Bars
 * 0x3147
 * R / W 0x0001
 * The bar number.
 */
data class V5LooperNumberOfBarsFeature(val number: Int): IV5WriteRead{
    override fun valuePayload(): ByteArray {
        val array = ByteBuffer.allocate(V5DevInfoFeatID.LooperNumberOfBars.valueSize)
            .order(ByteOrder.LITTLE_ENDIAN)
            .putShort(number.toShort()).array()
        return array
    }

    override val devFeatId: V5DevInfoFeatID = V5DevInfoFeatID.LooperNumberOfBars

    companion object{
        @V5Create
        fun fromPayload(payload: ByteArray): V5LooperNumberOfBarsFeature{
            return V5LooperNumberOfBarsFeature(ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
                .getShort().toUShort().toInt())
        }
    }

}

/**
 * Output Record Storage Location
 * 0x3180	R / W	0x0001
 * 0x00: Store on phone
 * 0x01: Store on flash driver
 *
 */
data class V5OutputRecordStorageLocationFeature(val location: OutputLocation) : IV5WriteRead {
    enum class OutputLocation(val value: Byte) {
        StoreOnPhone(0x00),
        StoreOnFlashDriver(0x01),
    }

    override fun valuePayload(): ByteArray {
        return byteArrayOf(location.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.OutputRecordStorageLocationFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5OutputRecordStorageLocationFeature {
            return V5OutputRecordStorageLocationFeature(OutputLocation.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/**
 * Output Recording Start / Stop
 * 0x3181	R / W	0x0001
 * 0x00: Stop
 * 0x01: Start
 *
 */
data class V5OutputRecordingStartStopFeature(val recording: Recording) : IV5WriteRead {
    enum class Recording(val value: Byte) {
        Stop(0x00),
        Start(0x01),
    }

    override fun valuePayload(): ByteArray {
        return byteArrayOf(recording.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.OutputRecordingStartStopFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5OutputRecordingStartStopFeature {
            return V5OutputRecordingStartStopFeature(Recording.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/***
 * Output Separate Tracks Enable / Disable
 * 0x3182	R / W	0x0001
 * 0x00: Disable
 * 0x01: Enable
 *
 */
data class V5OutputSeparateTracksEnableFeature(val status: SeparateTracksStatus) : IV5WriteRead {
    enum class SeparateTracksStatus(val value: Byte) {
        Disable(0x00),
        Enable(0x01),
    }

    override fun valuePayload(): ByteArray {
        return byteArrayOf(status.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.OutputSeparateTracksEnableFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5OutputSeparateTracksEnableFeature {
            return V5OutputSeparateTracksEnableFeature(SeparateTracksStatus.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/**
 * Sync Time	0x0031	R / W	0x0009
 * 1st byte: Second (0 ~ 59)
 * 2nd byte: Minute (0 ~ 59)
 * 3rd byte:  Hour (0 ~ 23)
 * 4th byte: Day of week (1 ~ 7, 1 is Monday)
 * 5th byte: Day (1 ~ 31)
 * 6th byte: Month (1 ~ 12)
 * 7th ~ 8th bytes: Year
 *
 * 9th byte: Time display format
 * 0x01: 12 Hour
 * 0x02: 24 Hour
 *
 */
data class V5SyncTimeFeature(
    val second: Int,
    val minute: Int,
    val hour: Int,
    val dayOfWeek: Int,
    val day: Int,
    val month: Int,
    val year: Int,
    val timeFormat: TimeFormat
) : IV5WriteRead {
    enum class TimeFormat(val value: Byte) {
        Hour12(0x01),
        Hour24(0x02),
    }

    override fun valuePayload(): ByteArray {//write
        val byteBuffer = ByteBuffer.allocate(V5DevInfoFeatID.SyncTimeFeature.valueSize)
            .order(ByteOrder.LITTLE_ENDIAN)
        byteBuffer.put(second.toByte())
        byteBuffer.put(minute.toByte())
        byteBuffer.put(hour.toByte())
        byteBuffer.put(dayOfWeek.toByte())
        byteBuffer.put(day.toByte())
        byteBuffer.put(month.toByte())
        byteBuffer.putShort(year.toShort())
        byteBuffer.put(timeFormat.value)
        return byteBuffer.array()
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.SyncTimeFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5SyncTimeFeature {
            val data = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN).let { bf ->
                val second = bf.get()
                val minute = bf.get()
                val hour = bf.get()
                val dayOfWeek = bf.get()
                val day = bf.get()
                val month = bf.get()
                val year = bf.getShort()
                val timeFormatByte = bf.get()
                val timeFormat = TimeFormat.entries.find {
                    it.value == timeFormatByte
                }
                V5SyncTimeFeature(
                    second = second.toInt(),
                    minute = minute.toInt(),
                    hour = hour.toInt(),
                    dayOfWeek = dayOfWeek.toInt(),
                    day = day.toInt(),
                    month = month.toInt(),
                    year = year.toInt(),
                    timeFormat = timeFormat!!
                )
            }
            return data
        }
    }
}

/**
 *
 * Mic Sensitivity in Level
 * 0x0C0B
 * R / W
 * 0x0001
 * 0x00: Auto
 * 0x01: Low
 * 0x02: Mid
 * 0x03: High
 */
enum class MicSensitivityInLevel(val value: Byte) {
    Auto(0x00),
    Low(0x01),
    Mid(0x02),
    High(0x03),
}
data class V5MicSensitivityInLevelFeature(val level: MicSensitivityInLevel) : IV5WriteRead {
    override fun valuePayload(): ByteArray {
        return byteArrayOf(level.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.MicSensitivityInLevelFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5MicSensitivityInLevelFeature {
            return V5MicSensitivityInLevelFeature(MicSensitivityInLevel.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/**
 *
 * Mic2 Sensitivity in Level
 * 0x0C0F
 * R / W
 * 0x0001
 * 0x00: Auto
 * 0x01: Low
 * 0x02: Mid
 * 0x03: High
 */

data class V5Mic2SensitivityInLevelFeature(val level:MicSensitivityInLevel): IV5WriteRead{
    override fun valuePayload(): ByteArray {
        return byteArrayOf(level.value)
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.Mic2SensitivityInLevelFeature

    companion object{
        @V5Create
        fun fromPayload(payload: ByteArray):V5Mic2SensitivityInLevelFeature{
            return V5Mic2SensitivityInLevelFeature(MicSensitivityInLevel.entries.find {
                it.value == payload[0]
            }!!)
        }
    }
}

/**
 *
 * Mic1 Sensitivity in dB
 * 0x0C0C
 * R / W
 * 0x0001
 * The value type is signed char
 * Mic1 Sensitivity in dB
 */
data class V5Mic1SensitivityIndBFeature(val db: Float) : IV5WriteRead {

    override fun valuePayload(): ByteArray {
        return ByteBuffer.allocate(V5DevInfoFeatID.Mic1SensitivityIndBFeature.valueSize).order(
            ByteOrder.LITTLE_ENDIAN
        ).putFloat(db).array()
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.Mic1SensitivityIndBFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5Mic1SensitivityIndBFeature {
            val bf = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            return V5Mic1SensitivityIndBFeature(bf.getFloat())
        }
    }
}

/**
 * The value type is float
 */
data class V5Mic2SensitivityIndBFeature(val db: Float) : IV5WriteRead {

    override fun valuePayload(): ByteArray {
        return ByteBuffer.allocate(V5DevInfoFeatID.Mic2SensitivityIndBFeature.valueSize).order(
            ByteOrder.LITTLE_ENDIAN
        ).putFloat(db).array()
    }

    override val devFeatId: V5DevInfoFeatID
        get() = V5DevInfoFeatID.Mic2SensitivityIndBFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5Mic2SensitivityIndBFeature {
            val bf = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
            return V5Mic2SensitivityIndBFeature(bf.getFloat())
        }
    }
}

/**
 * read or notify
 * USB Status 0x200A R 0x0001
 * 7th bit: Status
 * 0: Not inserted
 * 1: Inserted
 * 6th ~ 0th bit: U
 * SB Flash Disk or UAC Refer to
 * Table: Audio Source When USB status
 * is changed, it will also notify this Feature ID
 */
data class V5USBStatusFeature(val status: Status, val audioSource: V5AudioSource) : IV5Read {

    enum class Status(val value: Int) {
        NOT_INSERTED(0x00),
        INSERTED(0x01)
    }

    override val devFeatId = V5DevInfoFeatID.USBStatusFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5USBStatusFeature {
            val data = payload[0].toInt()
            val status = data.shr(7).and(0x01)
            val audioSource = data.and(0x7F)
            return V5USBStatusFeature(
                status = V5USBStatusFeature.Status.entries.find {
                    it.value == status
                } ?: Status.NOT_INSERTED,
                audioSource = V5AudioSource.entries.find {
                    it.value == audioSource.toByte()
                } ?: V5AudioSource.None
            )
        }
    }
}

/**
 * Left Device Serial Number 0x0008 R Max to 0x0040 ASCII string,
 * max 64 bytes. For standalone device or left device.
 * If the device is HID device, the SN should be same with th
 */

data class V5LeftDeviceSerialNumberFeature(val leftDeivceSerialNum: String) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.LeftDeviceSerialNumberFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5LeftDeviceSerialNumberFeature {
            return V5LeftDeviceSerialNumberFeature(String(payload, Charsets.US_ASCII))
        }
    }
}

data class V5RightDeviceSerialNumberFeature(val rightDeivceSerialNum: String) : IV5Read {
    override val devFeatId = V5DevInfoFeatID.RightDeviceSerialNumberFeature

    companion object {
        @V5Create
        fun fromPayload(payload: ByteArray): V5LeftDeviceSerialNumberFeature {
            return V5LeftDeviceSerialNumberFeature(String(payload, Charsets.US_ASCII))
        }
    }
}