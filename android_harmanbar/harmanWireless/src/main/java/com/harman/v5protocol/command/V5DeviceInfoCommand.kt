package com.harman.v5protocol.command

import com.blankj.utilcode.util.ToastUtils
import com.harman.log.Logger
import com.harman.v5protocol.IV5Command
import com.harman.v5protocol.V5CommandID
import com.harman.v5protocol.V5Identifier
import com.harman.v5protocol.V5Parser
import com.harman.v5protocol.bean.V5Status
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.IV5Write
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.bean.devinfofeat.V5Create
import com.harman.wireless.BuildConfig
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.reflect.full.companionObject
import kotlin.reflect.full.companionObjectInstance
import kotlin.reflect.full.functions

/**
 * @Description All the commands for v5 protocol device info are defined here
 * <AUTHOR>
 * @Time 2024/12/9
 */
/**
 * request get device info command
 */
class V5GetDeviceInfoCommand(feats: List<V5DevInfoFeatID>) : IV5Command {
    override val identifier: V5Identifier
        get() = V5Identifier.Direct
    override val commandId: Short
        get() = V5CommandID.GET_DEVICE_INFO
    override val payload: ByteArray = run {
        val buffer = ByteBuffer.allocate(feats.size * 2).order(ByteOrder.LITTLE_ENDIAN)
        feats.forEach {
            buffer.putShort(it.id.toShort())
        }
        return@run buffer.array()
    }
}

/**
 * request set device info command
 */
class V5SetDeviceInfoCommand(featValues: List<IV5Write>) : IV5Command {
    override val identifier: V5Identifier
        get() = V5Identifier.Direct
    override val commandId: Short
        get() = V5CommandID.SET_DEVICE_INFO
    override val payload: ByteArray = run {
        var totalPayloadSize = 0
        featValues.forEach {
            totalPayloadSize += (2 + 2 + it.valuePayload().size)
        }
        val buffer = ByteBuffer.allocate(totalPayloadSize).order(ByteOrder.LITTLE_ENDIAN)
        featValues.forEach {
            val payload = it.valuePayload()
            buffer.putShort(it.devFeatId.id.toShort())
            buffer.putShort(payload.size.toShort())
            buffer.put(payload)
        }
        return@run buffer.array()
    }
}

/**
 * Convenient v5 device info response or notification parser
 */
@OptIn(ExperimentalStdlibApi::class)
val v5DeviceInfoParser: V5Parser<List<IV5Payload>> = { payload ->
    val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
    val statusCode = ByteArray(2).also { buffer.get(it) }
    val status = V5Status.fromPayload(statusCode)
    if (!status.isSuccess()) {
        if (BuildConfig.DEBUG) {
            ToastUtils.showLong("parse device feat error,status is wrong: ${payload.toHexString()}")
        }
        listOf()
    } else {
        val ret = mutableListOf<IV5Payload>()
        var isError = false
        while (buffer.hasRemaining() && !isError) {
            try {
                val featId = buffer.getShort()
                val valueSize = buffer.getShort().toUShort().toInt()
                val value = ByteArray(valueSize).also { buffer.get(it) }
                V5DevInfoFeatID.entries.find { it.id.toShort() == featId }?.also { id ->
                    val companionObjectInstance = id.payloadClz.companionObjectInstance
                    id.payloadClz.companionObject!!.functions.find { func -> func.annotations.any { ann -> ann is V5Create } }!!
                        .also { createFunction ->
                            ret.add(createFunction.call(companionObjectInstance, value) as IV5Payload)
                        }
                } ?: run {
                    if (BuildConfig.DEBUG) {
                        ToastUtils.showLong("featId: ${featId.toHexString()}, this featId not defined in V5DevInfoFeatID")
                    }
                }
            } catch (e: Exception) {
                isError = true
                if (BuildConfig.DEBUG) {
                    ToastUtils.showLong("parse device feat error : ${payload.toHexString()} \n $e")
                }
            }
        }
        ret
    }
}