package com.harman.v5protocol.command

import com.blankj.utilcode.util.ToastUtils
import com.harman.util.toHex
import com.harman.v5protocol.IV5Command
import com.harman.v5protocol.V5CommandID
import com.harman.v5protocol.V5Identifier
import com.harman.v5protocol.V5Parser
import com.harman.v5protocol.bean.V5Status
import com.harman.v5protocol.bean.analytics.IV5AnalyticsValue
import com.harman.v5protocol.bean.analytics.V5AnalyticsValueCreate
import com.harman.v5protocol.bean.analytics.V5DevAnalyticsId
import com.harman.wireless.BuildConfig
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.reflect.full.companionObject
import kotlin.reflect.full.companionObjectInstance
import kotlin.reflect.full.functions
import com.harman.log.Logger

/**
 * @Description All the commands for v5 protocol device analytics data are defined here
 * <AUTHOR>
 * @Time 2025/5/12
 */
class V5GetDeviceAnalyticsDataCommand : IV5Command {
    override val identifier = V5Identifier.Direct
    override val commandId = V5CommandID.GET_DEVICE_ANALYTICS_DATA
    override val payload = null
}

class V5CleanDeviceAnalyticsDataCommand : IV5Command {
    override val identifier = V5Identifier.Direct
    override val commandId = V5CommandID.CLEAN_DEVICE_ANALYTICS_DATA
    override val payload = null
}

@OptIn(ExperimentalStdlibApi::class)
val v5DeviceAnalyticsDataParser: V5Parser<List<IV5AnalyticsValue<*>>> = { payload ->
    val buffer = ByteBuffer.wrap(payload).order(ByteOrder.LITTLE_ENDIAN)
    val statusCode = ByteArray(2).also { buffer.get(it) }
    val status = V5Status.fromPayload(statusCode)

    Logger.d("v5DeviceAnalyticsDataParser", "status[$status] payload:${payload.toHexString()}")
    if (!status.isSuccess()) {
        if (BuildConfig.DEBUG) {
            ToastUtils.showLong("parse device Analytics data error,status is wrong: ${payload.toHexString()}")
        }
        listOf()
    } else {
        val ret = mutableListOf<IV5AnalyticsValue<*>>()
        var isError = false
        while (buffer.hasRemaining() && !isError) {
            try {
                val categoryId = buffer.get()
                val featureId = buffer.get()
                val valueLength = buffer.getShort().toUShort().toInt()
                val value = ByteArray(valueLength).also { buffer.get(it) }

                V5DevAnalyticsId.entries.find { it.categoryId.toByte() == categoryId && it.featureId.toByte() == featureId }?.also { analyticsItem ->
                    val companionObjectInstance = analyticsItem.valueClz.companionObjectInstance
                    analyticsItem.valueClz.companionObject?.functions?.find { func ->
                        func.annotations.any { ann ->
                            ann is V5AnalyticsValueCreate
                        }
                    }?.also { createFunction ->
                        ret.add(
                            createFunction.call(companionObjectInstance, value) as IV5AnalyticsValue<*>
                        )
                    }
                } ?: run {
                    if (BuildConfig.DEBUG) {
                        ToastUtils.showLong("categoryId: ${categoryId.toHex()}, featureId: ${featureId.toHex()},this analytics value not defined in V5DevAnalyticsId")
                    }
                }
            } catch (e: Exception) {
                isError = true
                if (BuildConfig.DEBUG) {
                    ToastUtils.showLong("parse device Analytics data error : ${payload.toHexString()} \n $e")
                }
            }
        }
        ret
    }
}