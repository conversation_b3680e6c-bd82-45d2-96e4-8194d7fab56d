package com.harman.v5protocol.bean.analytics

import com.harman.discover.util.Tools.toLittleEndianUInt
import com.harman.util.safeSubArray
import java.nio.ByteBuffer

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/5/12.
 */

/**
 * Each item consists of 3 bytes:
 * - 1st byte: Volume level, range:[0, 32]
 * - 2nd&3rd bytes: Duration in minutes
 */
data class AnalyticsVolume(
    val volumeLevel: Int,
    val durationInMinutes: UInt
) {
    override fun toString(): String {
        return "volume[$volumeLevel] duration[$durationInMinutes]"
    }

    companion object {
        fun ByteArray.toVolumeList(): List<AnalyticsVolume> {
            if (isEmpty() || 0 != (size % 3)) {
                return emptyList()
            }

            val buffer = ByteBuffer.wrap(this)
            val output = mutableListOf<AnalyticsVolume>()

            while (buffer.hasRemaining()) {
                val volume = runCatching {
                    buffer.get().toUByte().toInt()
                }.getOrNull() ?: break

                val duration = buffer.safeSubArray(2)?.toLittleEndianUInt() ?: break

                output.add(AnalyticsVolume(volume, duration))
            }

            return output.toList()
        }
    }
}

/**
 * Each item consists of 3 bytes:
 * - 1st byte: Preset ID, range:[1, 30]
 * - 2nd&3rd bytes: Duration in minutes
 */
data class AnalyticsPreset(
    val presetId: Int,
    val durationInMinutes: UInt
) {
    override fun toString(): String {
        return "preset[$presetId] duration[$durationInMinutes]"
    }

    companion object {
        fun ByteArray.toPresetList(): List<AnalyticsPreset> {
            if (isEmpty() || 0 != (size % 3)) {
                return emptyList()
            }

            val buffer = ByteBuffer.wrap(this)
            val output = mutableListOf<AnalyticsPreset>()

            while (buffer.hasRemaining()) {
                val presetId = runCatching {
                    buffer.get().toUByte().toInt()
                }.getOrNull() ?: break

                val duration = buffer.safeSubArray(2)?.toLittleEndianUInt() ?: break

                output.add(AnalyticsPreset(presetId, duration))
            }

            return output.toList()
        }
    }
}

/**
 * Each item consists of 3 bytes:
 * - 1st byte: Mode ID, range:[1, 7]
 * - 2nd&3rd bytes: Duration in minutes
 */
data class AnalyticsStem(
    val modeId: Int,
    val durationInMinutes: UInt
) {
    override fun toString(): String {
        return "mode[$modeId] duration[$durationInMinutes]"
    }

    companion object {
        fun ByteArray.toStemList(): List<AnalyticsStem> {
            if (isEmpty() || 0 != (size % 3)) {
                return emptyList()
            }

            val buffer = ByteBuffer.wrap(this)
            val output = mutableListOf<AnalyticsStem>()

            while (buffer.hasRemaining()) {
                val modeId = runCatching {
                    buffer.get().toUByte().toInt()
                }.getOrNull() ?: break

                val duration = buffer.safeSubArray(2)?.toLittleEndianUInt() ?: break

                output.add(AnalyticsStem(modeId, duration))
            }

            return output.toList()
        }
    }
}
