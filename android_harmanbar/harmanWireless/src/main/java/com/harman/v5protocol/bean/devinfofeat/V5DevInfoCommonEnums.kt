package com.harman.v5protocol.bean.devinfofeat

/**
 * @Description Define a common enumeration type that can be reused by the v5 protocol
 * <AUTHOR>
 * @Time 2025/2/26
 */
enum class V5CHInputType(val value: Byte) {
    Unknown(0x00),
    Mic(0x01),
    Guitar(0x02),
    LineIn(0x03),
    AUX_INSTRUMENT(0x04),
}

enum class V5TrackNumber(val value: Byte) {
    Track1(0x01),//Track 1
    Track2(0x02),//Track 2
    Track3(0x03),//Track 3
}

enum class V5ChannelNumber(val value: Byte) {
    Master(0x00),
    CH1(0x01),
    CH2(0x02),
    CH3(0x03),
    CH4(0x04),
}

enum class V5AudioSource(val value: Byte) {
    None(0x00),
    BT(0x01),
    AUX(0x02),
    USBFlashDisk(0x03),
    Optical(0x04),
    FMRadio(0x05),
    DABRadio(0x06),
    UAC(0x07),
}

enum class V5AuracastModeEnum(val value: Byte) {
    Normal(0x00),// Normal mode (Turn off Auracast)
    On(0x01),// Auracast mode (Turn on Auracast)
    OnUnlinked(0x02),//: Auracast on and unlinked (For Party Light)
    OnLinked(0x03),//Auracast on and linked (For Party Light)
}

enum class V5PartyGroupTypeEnum(val value: Byte) {
    Normal(0x00),//: Normal
    AuracastParty(0x01),//: Auracast Party
    AuracastStereo(0x02),//: Auracast Stereo
    TWSStereo(0x03),//: TWS Stereo
    LongLastingStereo(0x04)//: Long Lasting Stereo
}

enum class V5MusicGenreEnum(val value: Byte) {
    General(0x00),
    Pop(0x01),
    Blues(0x02),
    Rock(0x03),
    Metal(0x04),
    Alternative(0x05);

    companion object {
        fun fromByte(byte: Byte) = V5MusicGenreEnum.entries.find { byte == it.value }
    }
}

enum class V5GuitarPresetOperationEnum(val value: Byte) {
    Duplicate(0x01),
    ResetToDefault(0x02),
    Rename(0x03),
    SavePreset(0x04),
    SaveToOnProductList(0x05),
    RemoveFromOnProductList(0x06),
    Delete(0x07),
    ExitPreview(0x08),
    SaveAs(0x09),
}

enum class SlotId(val id: Byte) {
    Fx1(0x00),
    Fx2(0x01),
    Fx3(0x02),
    Amp(0x03),
    Cab(0x04),
    Mod(0x05),
    Delay(0x06),
    Reverb(0x07),
}

private val ampValueDefineds = listOf(
    SlotValueDefined(SlotValueTypeEnum.Drive, 0),
    SlotValueDefined(SlotValueTypeEnum.Bass, 1),
    SlotValueDefined(SlotValueTypeEnum.Mid, 2),
    SlotValueDefined(SlotValueTypeEnum.Treble, 3),
    SlotValueDefined(SlotValueTypeEnum.OutVol, 4),
)

enum class Algorithm(val id: Byte, val configId: Byte, val valueTypes: List<SlotValueDefined>) {


    FxNoiseGate(0x00, 0x00, listOf(SlotValueDefined(SlotValueTypeEnum.Threshold, 0), SlotValueDefined(SlotValueTypeEnum.Release, 1))),
    FxExpander(0x1c, 0x00, listOf(SlotValueDefined(SlotValueTypeEnum.Threshold, 0), SlotValueDefined(SlotValueTypeEnum.Strength, 1))),

    FxCompressor(
        0x01, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Threshold, 0),
            SlotValueDefined(SlotValueTypeEnum.Ratio, 1),
            SlotValueDefined(SlotValueTypeEnum.Attack, 2),
            SlotValueDefined(SlotValueTypeEnum.Release, 3),
            SlotValueDefined(SlotValueTypeEnum.OutputLevel, 4),
        )
    ),

    FxWah(
        0x02, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Rate, 1),
            SlotValueDefined(SlotValueTypeEnum.Freq, 2),
            SlotValueDefined(SlotValueTypeEnum.Depth, 3),
            SlotValueDefined(SlotValueTypeEnum.Reso, 4),
        )
    ),
    FxAutoWah(
        0x03, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Sensitivity, 1),
            SlotValueDefined(SlotValueTypeEnum.Freq, 2),
            SlotValueDefined(SlotValueTypeEnum.Reso, 3),
        )
    ),

//    FxDistortion(0x04, 0x00), //could not include bandbox product
//    AmpPolynomials(0x05, 0x00), //could not include bandbox product

    AmpBlackface(0x06, 0x00, ampValueDefineds),
    AmpJazzSound(0x06, 0x01, ampValueDefineds),
    AmpYellowTweed(0x06, 0x02, ampValueDefineds),
    AmpUKCrunch(0x06, 0x03, ampValueDefineds),
    AmpBritishLead(0x06, 0x04, ampValueDefineds),
    AmpDriveJM45(0x06, 0x05, ampValueDefineds),
    AmpJump6507(0x06, 0x06, ampValueDefineds),
    AmpUSMetal(0x06, 0x07, ampValueDefineds),

//    CabBiquads(0x07, 0x00),//could not include bandbox product

    CabIrChampion1x8(0x08, 0x00, listOf()),
    CabIrVoks2x12(0x08, 0x01, listOf()),
    CabIrTR652x12(0x08, 0x02, listOf()),
    CabIrUK25502x12(0x08, 0x03, listOf()),
    CabIrBM594x10(0x08, 0x04, listOf()),
    CabIrTanger4x12(0x08, 0x05, listOf()),
    CabIrUK19604x12(0x08, 0x06, listOf()),
    CabIr65054x12(0x08, 0x07, listOf()),
    CabIrMebo4x12(0x08, 0x08, listOf()),
    CabIrMBass2x10(0x08, 0x09, listOf()),
    CabIrABass8x10(0x08, 0x0a, listOf()),

    ModMxrPhase90(0x0e, 0x00, listOf(SlotValueDefined(SlotValueTypeEnum.Speed, 0))),
    ModGreenPhaser(
        0x0f, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
        )
    ),
    ModNotchPhaser(
        0x11, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
            SlotValueDefined(SlotValueTypeEnum.Level, 3),
        )
    ),
    ModeMinivibe(
        0x12, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
        )
    ),
    ModTremolo(
        0x13, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Shape, 1),
            SlotValueDefined(SlotValueTypeEnum.Speed, 2),
            SlotValueDefined(SlotValueTypeEnum.Depth, 3),
            SlotValueDefined(SlotValueTypeEnum.Bias, 4),
        )
    ),
    ModSimpleChorus(
        0x0b, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
            SlotValueDefined(SlotValueTypeEnum.Offset, 2),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 3),
            SlotValueDefined(SlotValueTypeEnum.Wet, 4),
        )
    ),
    ModShiverVibrato(
        0x0c, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
        )
    ),
    ModJetterFlanger(
        0x0d, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
            SlotValueDefined(SlotValueTypeEnum.Offset, 2),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 3),
        )
    ),
    ModGrandVibrato(
        0x09, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Speed, 0),
            SlotValueDefined(SlotValueTypeEnum.Depth, 1),
            SlotValueDefined(SlotValueTypeEnum.Volume, 2),
        )
    ),
//    ModGrandChorus(0x0a, 0x00),//could not include bandbox product
//    ModTwirlNPhaser(0x10, 0x00),//could not include bandbox product


    DelayPureEko(
        0x14, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Time, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
        )
    ),
    DelayAnalogEko(
        0x15, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Time, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
        )
    ),
    DelayEkopress900(
        0x16, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Time, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
        )
    ),
    DelaySweetie(
        0x18, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Time, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
        )
    ),
    DelaySweepEko(
        0x17, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.Mix, 0),
            SlotValueDefined(SlotValueTypeEnum.Time, 1),
            SlotValueDefined(SlotValueTypeEnum.Feedback, 2),
            SlotValueDefined(SlotValueTypeEnum.Speed, 3),
            SlotValueDefined(SlotValueTypeEnum.Depth, 4),
        )
    ),

    ReverbDattorro(
        0x1b, 0x00, listOf(
            SlotValueDefined(SlotValueTypeEnum.PreDelay, 0),
            SlotValueDefined(SlotValueTypeEnum.PreFilter, 1),
            SlotValueDefined(SlotValueTypeEnum.Decay, 2),
            SlotValueDefined(SlotValueTypeEnum.Damp, 3),
            SlotValueDefined(SlotValueTypeEnum.Amount, 4),
        )
    );
//    ReverbSchroeder(0x19, 0x00),//could not include bandbox product
//    ReverbSimple(0x1a, 0x00),//could not include bandbox product
}

enum class SlotValueTypeEnum {
    Threshold,
    Release,
    Strength,
    Ratio,
    Attack,
    OutputLevel,
    Mix,
    Rate,
    Freq,
    Depth,
    Reso,
    Sensitivity,
    Drive,
    Bass,
    Mid,
    Treble,
    OutVol,
    Speed,
    Feedback,
    Level,
    Shape,
    Bias,
    Offset,
    Wet,
    Volume,
    Time,
    PreDelay,
    PreFilter,
    Decay,
    Damp,
    Amount,
}

enum class SlotChangeEnum(val value: Byte) {
    AllInfo(0x00), //All info
    BypassStatus(0x01),//Bypass status
    AlgorithmId(0x02),// Algorithm ID
    ConfigId(0x03),// Config ID
    UserControlInfo(0x04),// User control info
}

enum class EQCategoryEnum(val value: Byte) {
    JBL_SIGNATURE(0x80.toByte()),
    VOCAL(0x02),
    MOVIE(0x84.toByte()),
    CHILL(0x85.toByte()),
    CUSTOM_EQ(0xc1.toByte()),
    CURRENT_EQ(0xff.toByte()),
}

enum class EQStatusEnum(val value: Byte) {
    Inactive(0x00),
    Active(0x01),//Active, which means active this setting.
    Preview(0x02),//Preview, which means just preview this setting.
}


enum class EQBandTypeEnum(val value: Byte) {
    LowShelf(0x00),
    Peaking(0x01),
    HighShelf(0x02),
    LPF(0x03),//Low Pass Filter
    HPF(0x04),//High Pass Filter
    PassThrough(0x05),
    BPF0(0x06),
    BPF1(0x07),
    Notch(0x08),
    APF(0x09),
    QTY(0x0A),

    Bypass2(0x80.toByte()),
    FirstOrderHighPass(0x81.toByte()),
    SecondOrderHighPass(0x82.toByte()),
    FirstOrderLowPass(0x85.toByte()),
    SecondOrderLowPass(0x86.toByte()),
    FirstOrderAllPass(0x89.toByte()),
    SecondOrderAllPass(0x8A.toByte()),
    PeakingFilterEqualizer(0x8B.toByte()),
    FirstOrderHighShelf(0x8C.toByte()),
    FirstOrderLowShelf(0x8D.toByte()),
    SecondOrderBandPass(0x8E.toByte()),
    Amplifier(0x8F.toByte()),
    SecondOrderHighShelf(0x90.toByte()),
    SecondOrderLowShelf(0x91.toByte()),
    HighButterworth2(0x93.toByte()),
    HighBessel2(0x94.toByte()),
    HighLinqwitzRiley2(0x95.toByte()),
    HighButterworth3(0x96.toByte()),
    HighBessel3(0x97.toByte()),
    HighButterworth4(0x98.toByte()),
    HighBessel4(0x99.toByte()),
    HighLinqwitzRiley4(0x9A.toByte()),
    LowButterworth2(0x9B.toByte()),
    LowBessel2(0x9C.toByte()),
    LowLinqwitzRiley2(0x9D.toByte()),
    LowButterworth3(0x9E.toByte()),
    LowBessel(0x9F.toByte()),
    LowButterworth4(0xA0.toByte()),
    LowBessel4(0xA1.toByte()),
    LowLinqwitzRiley4(0xA2.toByte()),
    SecondOrderBandstop(0xA8.toByte()),
}

enum class SoloMusicianEnum(val value: Byte) {
    RESET(0x00),
    GUITARIST(0x01),
    GUITAR_VOCALIST(0x02),
    SINGER(0x03),
}

enum class PartyConnectionStatusEnum(val value: Byte) {
    Normal(0x00),
    Connecting(0x01),
    Connected(0x02),
    Wired(0x03), //(Daisy Chain)
}

enum class PartyRoleEnum(val value: Byte) {
    Normal(0x00),
    Secondary(0x01),
    Primary(0x02),
}

enum class PickupTypeEnum(val value: Byte) {
    Passive(0x00),
    Active(0x01),
}
