package com.harman.v5protocol

import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import androidx.annotation.AnyThread
import androidx.annotation.WorkerThread
import com.blankj.utilcode.util.ToastUtils
import com.harman.connect.BaseGattSession
import com.harman.connect.listener.IGattListener
import com.harman.discover.bean.bt.BaseBTDevice
import com.harman.discover.bean.bt.GattListenerProxy
import com.harman.discover.info.DefaultRole
import com.harman.log.Logger
import com.harman.v5protocol.bean.V5Status
import com.harman.v5protocol.bean.analytics.IV5AnalyticsValue
import com.harman.v5protocol.bean.devinfofeat.IV5Payload
import com.harman.v5protocol.bean.devinfofeat.IV5Read
import com.harman.v5protocol.bean.devinfofeat.IV5Write
import com.harman.v5protocol.bean.devinfofeat.V5DevInfoFeatID
import com.harman.v5protocol.command.V5CleanDeviceAnalyticsDataCommand
import com.harman.v5protocol.command.V5GetDeviceAnalyticsDataCommand
import com.harman.v5protocol.command.V5GetDeviceInfoCommand
import com.harman.v5protocol.command.V5SetDeviceInfoCommand
import com.harman.v5protocol.command.v5DeviceAnalyticsDataParser
import com.harman.v5protocol.command.v5DeviceInfoParser
import com.harman.v5protocol.discover.PartyInfo
import com.harman.wireless.BuildConfig
import com.harmanbar.ble.utils.GsonUtil
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.withContext
import java.util.UUID
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit

/**
 * @Description Implemented the v5 communication protocol Gattsession,
 * Later devices using the v5 protocol can either create it directly or inherit it for use
 * <AUTHOR>
 * @Time 2024/12/4
 */
open class V5GattSession<DeviceType : BaseBTDevice<DefaultRole, PartyInfo.Role>>(
    device: DeviceType,
    listenerProxy: GattListenerProxy,
) : BaseGattSession<DeviceType, DefaultRole, PartyInfo.Role>(
    bleAddress = device.bleAddress!!,
    device = device,
    listenerProxy = listenerProxy,
) {
    companion object {
        const val TAG = "V5GattSession"
    }

    override val rxUUID: UUID = UUID.fromString("65786365-6C70-6F69-6E74-2E636F6D0001")

    /**
     * Cache subcontracting until a complete instruction can be composed
     */
    private val receivingPackage = mutableListOf<V5Package>()

    /**
     * Due to the existence of a subcontracting mechanism, the commands that are being sent need to be cached
     */
    private var sendingCommand: IV5Command? = null

    /**
     * When a complete [IV5Command] is successfully written,
     * It is necessary to wait for a reply from the device until it is composed of a complete [V5ResponseCommand] before sending the next request,
     * and this variable is used to wake up the write thread
     */
    private var characteristicChangedWaitingCompleter: CompletableFuture<V5ResponseCommand>? = null
    override fun handleCharacteristicChanged(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?, value: ByteArray?) {
        super.handleCharacteristicChanged(gatt, characteristic, value)
        val onePackage = V5Package.fromDevice(value!!)
        receivingPackage.add(onePackage)
        if (onePackage.packageIndex == onePackage.packageCount - 1) {
            //last package
            handleReceiveCommand()
        }
    }

    /**
     * When the write command is queued, the next data piece needs to wait for the
     * [BluetoothGattCallback.onCharacteristicWrite] callback corresponding to
     * the previous data before continuing, and this variable is used to control this sequence
     */
    private var writeWaitingCompleter: CompletableFuture<Boolean>? = null
    override fun onCharacteristicWrite(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?, status: Int) {
        super.onCharacteristicWrite(gatt, characteristic, status)
        writeWaitingCompleter?.complete(status == BluetoothGatt.GATT_SUCCESS)
    }

    @OptIn(ExperimentalStdlibApi::class)
    override val writeReadLogContent: ((value: ByteArray, isWrite: Boolean, isSuccess: Boolean) -> Unit)?
        get() = { value, isWrite, isSuccess ->
            val logString = V5Util.bytes2HexWithSpace(value)
            if (isWrite) {
                if (isSuccess) {
                    Logger.d(TAG, "write success [${bleAddress}]: $logString")
                } else {
                    Logger.e(TAG, "write fail [${bleAddress}]: $logString")
                }
            } else {
                Logger.d(TAG, "characteristic changed [${bleAddress}]: $logString")
            }
        }

    private fun handleReceiveCommand() {
        var playlod = ByteArray(0)
        receivingPackage.forEach {
            playlod += it.payload ?: byteArrayOf()
        }
        val responseCommand = V5ResponseCommand(
            receivingPackage.first().v5Identifier,
            receivingPackage.first().commandId,
            playlod
        )
        receivingPackage.clear()
        if (null == sendingCommand || responseCommand.commandId != sendingCommand!!.commandId) {
            //The device is actively notified
            listenerProxy.getter().forEach {
                (it as? IV5GattListener)?.onDeviceNotify(responseCommand)
            }
            //this notify is device info change notify
            if (V5CommandID.NOTIFY_DEVICE_INFO == responseCommand.commandId) {
                handleDeviceInfoFeatData(playlod, true)
            }
        } else {
            listenerProxy.getter().forEach {
                (it as? IV5GattListener)?.onDeviceResponse(responseCommand)
            }
            if (responseCommand.commandId == V5CommandID.GET_DEVICE_INFO) {
                handleDeviceInfoFeatData(playlod, false)
            }
            //response for request
            characteristicChangedWaitingCompleter?.complete(responseCommand)
        }
    }

    private fun handleDeviceInfoFeatData(payload: ByteArray, isNotify: Boolean) {
        v5DeviceInfoParser.invoke(payload).also { devInfos ->
            val mapList = mutableListOf<MutableMap<V5DevInfoFeatID, IV5Payload>>()
            val devInfosM: MutableList<IV5Payload?> = devInfos.toMutableList()
            while (true) {
                val map = mutableMapOf<V5DevInfoFeatID, IV5Payload>()
                devInfosM.forEachIndexed { index, iV5Payload ->
                    if (null == iV5Payload || map.containsKey(iV5Payload.devFeatId)) {
                        return@forEachIndexed
                    }
                    map[iV5Payload.devFeatId] = iV5Payload
                    devInfosM[index] = null
                }
                if (map.isEmpty()) {
                    break
                } else {
                    mapList.add(map)
                }
            }
            mapList.forEach {
                it.forEach {
                    Logger.d(TAG, "parse device info feature: [featureId] = ${it.key.name},[payloadJson] = ${GsonUtil.parseBeanToJson(it.value)}")
                }
                listenerProxy.getter().forEach { listener ->
                    (listener as? IV5GattListener)?.onDevFeat(it, isNotify)
                }
                onDevFeat(it)
            }
        }
    }

    fun debugNotifyDevFeat(devFeat: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean = true) {
        if (BuildConfig.DEBUG) {
            listenerProxy.getter().forEach { listener ->
                (listener as? IV5GattListener)?.onDevFeat(devFeat, isNotify)
            }
        }
    }

    /**
     * This method will be called when device info feature is obtained,
     * and the subclass can override the business logic for this method to handle the data for device info feature
     */
    open fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>) {}

    /**
     * This variable is used to provide a fixed thread context for coroutines that send commands
     */
    @OptIn(DelicateCoroutinesApi::class)
    private val sendCmdThreadCtx by lazy {
        newSingleThreadContext("${device.UUID}-sendCmdThread")
    }

    /** Executed in [sendCmdThreadCtx]'s thread, Ensure the timing of writes and reads of a [IV5Command] */
    @OptIn(ExperimentalStdlibApi::class)
    @WorkerThread
    private fun <Resp> blockWriteRead(command: IV5Command, parser: V5Parser<Resp>? = null): V5WriteResponse<Resp> {
        //write command
        try {
            sendingCommand = command
            characteristicChangedWaitingCompleter = CompletableFuture()
            val packages = command.splitCommand(mtuSize)
            packages.forEach {
                writeWaitingCompleter = CompletableFuture()
                val writeRet = write(it.toBytes())
                if (writeRet) {
                    val waitWriteRet = runCatching {
                        writeWaitingCompleter!!.get(command.timeoutMills, TimeUnit.MILLISECONDS)
                    }.onFailure {
                        throw V5WriteTimeoutException(device, command)
                    }
                    if (!waitWriteRet.getOrThrow()) {
                        throw V5WriteCmdCharacteristicException(device, command)
                    }
                } else {
                    throw V5WriteCmdCharacteristicException(device, command)
                }
            }
        } catch (e: Exception) {
            val logMsg = "syncSendCommand failed $e: ${V5Util.bytes2HexWithSpace(sendingCommand!!.splitCommand(mtuSize).first().toBytes())}"
            Logger.e(TAG, logMsg)
            if (BuildConfig.DEBUG) {
                ToastUtils.showLong(logMsg)
            }
            listenerProxy.getter().forEach {
                it.onGattException(e, this@V5GattSession)
            }
            sendingCommand = null
            return V5WriteResponse(false, e)
        } finally {
            writeWaitingCompleter = null
        }

        //waiting device reply
        val responseCommand = runCatching {
            characteristicChangedWaitingCompleter!!.get(command.timeoutMills, TimeUnit.MILLISECONDS)
        }.onFailure {
            val logMsg =
                "response timeout : [sendingCommand] = ${V5Util.bytes2HexWithSpace(sendingCommand!!.splitCommand(mtuSize).first().toBytes())}"
            Logger.e(TAG, logMsg)
            if (BuildConfig.DEBUG) {
                ToastUtils.showLong(logMsg)
            }
            return V5WriteResponse(false, e = V5ResponseTimeoutException(device, command))
        }.getOrThrow()
        //parse response
        try {
            val resp = parser?.invoke(responseCommand.payload)
            return V5WriteResponse(true, responseCommand = responseCommand, resp = resp)
        } catch (e: Exception) {
            val logMsg = "parse response error : ${responseCommand.payload.toHexString()} \n ${e.stackTraceToString()}"
            Logger.e(TAG, logMsg)
            if (BuildConfig.DEBUG) {
                ToastUtils.showLong(logMsg)
            }
            return V5WriteResponse(
                false,
                responseCommand = responseCommand,
                e = V5ResponseParseException(device, command),
            )
        } finally {
            sendingCommand = null
        }
    }

    @AnyThread
    suspend fun <Resp> syncSendCommand(command: IV5Command, parser: V5Parser<Resp>? = null): V5WriteResponse<Resp> {
        //Because the lifetime of a V5GattSession is bound to be longer than that of a page,so switch to inner coroutine scope
        return this.async {
            withContext(sendCmdThreadCtx) {
                return@withContext blockWriteRead(command, parser)
            }
        }.await()
    }

    @AnyThread
    fun asyncSendCommand(command: IV5Command) {
        launch {
            syncSendCommand<Unit>(command)
        }
    }

    fun writeForDebug(debugData: ByteArray) {
        if (BuildConfig.DEBUG) {
            write(debugData)
        }
    }

    @AnyThread
    fun asyncGetDevInfoFeat(vararg ids: V5DevInfoFeatID) {
        asyncSendCommand(V5GetDeviceInfoCommand(ids.toList()))
    }

    /**
     * Request one device info feature,If you want to request more than one feature at a time,use:
     *  [getDevInfoFeat2],
     *  [getDevInfoFeat3],
     *  [syncSendCommand],syncSendCommand(V5GetDeviceInfoCommand(feat,feat2,feat3), v5DeviceInfoParser)
     * and you need to parse it yourself.
     */
    @AnyThread
    suspend inline fun <reified Resp : IV5Read> getDevInfoFeat(): Resp? {
        try {
            val id = V5DevInfoFeatID.entries.find { it.payloadClz == Resp::class }!!
            return syncSendCommand(
                V5GetDeviceInfoCommand(listOf(id)), v5DeviceInfoParser
            ).resp?.first() as? Resp
        } catch (e: Exception) {
            Logger.e(TAG, "parse device info feature data exception ${Resp::class},${e.stackTraceToString()}")
            return null
        }
    }

    @AnyThread
    suspend inline fun <reified Resp1 : IV5Read, reified Resp2 : IV5Read> getDevInfoFeat2(): Pair<Resp1?, Resp2?> {
        try {
            val id1 = V5DevInfoFeatID.entries.find { it.payloadClz == Resp1::class }!!
            val id2 = V5DevInfoFeatID.entries.find { it.payloadClz == Resp2::class }!!
            return syncSendCommand(
                V5GetDeviceInfoCommand(listOf(id1, id2)),
                v5DeviceInfoParser,
            ).resp?.let {
                Pair(it.getOrNull(0) as? Resp1, it.getOrNull(1) as? Resp2)
            } ?: Pair(null, null)
        } catch (e: Exception) {
            Logger.e(V5GattSession.TAG, "parse 2 device info feature data exception ${Resp1::class} , ${Resp2::class} , ${e.stackTraceToString()}")
            return Pair(null, null)
        }
    }

    @AnyThread
    suspend inline fun <reified Resp1 : IV5Read, reified Resp2 : IV5Read, reified Resp3 : IV5Read> getDevInfoFeat3(): Triple<Resp1?, Resp2?, Resp3?> {
        try {
            val id1 = V5DevInfoFeatID.entries.find { it.payloadClz == Resp1::class }!!
            val id2 = V5DevInfoFeatID.entries.find { it.payloadClz == Resp2::class }!!
            val id3 = V5DevInfoFeatID.entries.find { it.payloadClz == Resp3::class }!!
            return syncSendCommand(
                V5GetDeviceInfoCommand(listOf(id1, id2, id3)),
                v5DeviceInfoParser,
            ).resp?.let {
                Triple(it.getOrNull(0) as? Resp1, it.getOrNull(1) as? Resp2, it.getOrNull(2) as? Resp3)
            } ?: Triple(null, null, null)
        } catch (e: Exception) {
            Logger.e(
                V5GattSession.TAG,
                "parse 3 device info feature data exception ${Resp1::class} , ${Resp2::class} , ${Resp3::class} ${e.stackTraceToString()}"
            )
            return Triple(null, null, null)
        }
    }

    /**
     * sync set device info feature
     * Multiple feature value's can be set at one time
     */
    @AnyThread
    suspend fun setDevInfoFeat(vararg featValues: IV5Write): V5Status? {
        return syncSendCommand(V5SetDeviceInfoCommand(featValues.toList()), V5Status::fromPayload).resp
    }

    @AnyThread
    fun asyncSetDevInfoFeat(vararg featValues: IV5Write) {
        asyncSendCommand(V5SetDeviceInfoCommand(featValues.toList()))
    }

    @AnyThread
    suspend fun getGetDeviceAnalyticsData(): List<IV5AnalyticsValue<*>>? {
        try {
            return syncSendCommand(
                V5GetDeviceAnalyticsDataCommand(), v5DeviceAnalyticsDataParser
            ).resp
        } catch (e: Exception) {
            Logger.e(TAG, "parse device analytics data exception,${e.stackTraceToString()}")
            return null
        }
    }

    @AnyThread
    fun asyncCleanDeviceAnalyticsData() {
        asyncSendCommand(V5CleanDeviceAnalyticsDataCommand())
    }
}

interface IV5GattListener : IGattListener {
    @WorkerThread
    fun onDeviceNotify(command: V5ResponseCommand) = Unit

    @WorkerThread
    fun onDeviceResponse(command: V5ResponseCommand) = Unit


    /**
     * This callback is triggered when the device actively notifies device info feature or
     * the app requests device info feature from the device and gets a response
     * @param isNotify true if device actively notifies
     */
    @WorkerThread
    fun onDevFeat(devInfoMap: Map<V5DevInfoFeatID, IV5Payload>, isNotify: Boolean) = Unit
}

/**
 * write response for [V5GattSession.syncSendCommand]
 * @param isOk true if the write succeeds and the device returns the corresponding result and parses to [Resp]
 * @param e exception for write failed,if [isOk] is false,this param is Nonnull
 * @param responseCommand The full result returned by the device,if [isOk] is true or [e] is [V5ResponseParseException],this param is Nonnull
 * @param resp The parsed data model,if [isOk] is true,this param is Nonnull
 */
data class V5WriteResponse<Resp>(
    val isOk: Boolean,
    val e: Exception? = null,
    val responseCommand: V5ResponseCommand? = null,
    val resp: Resp? = null,
)