package com.harman.wireless

import com.harman.discover.util.Tools.toLittleEndianUInt
import com.harman.discover.util.Tools.toBigEndianUInt
import org.junit.Test

import org.junit.Assert.*

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {

    val cases = listOf(
        byteArrayOf(0x00.toByte(), 0x00.toByte(), 0x00.toByte(), 0x00.toByte()),
        byteArrayOf(0xff.toByte(), 0xff.toByte(), 0xff.toByte(), 0xff.toByte()),
        byteArrayOf(0x01.toByte(), 0x01.toByte(), 0x01.toByte(), 0x00.toByte()),
        byteArrayOf(0x01.toByte(), 0x01.toByte(), 0x00.toByte(), 0x00.toByte()),
        byteArrayOf(0x01.toByte(), 0x00.toByte(), 0x00.toByte(), 0x00.toByte()),

        byteArrayOf(0x01.toByte(), 0x00.toByte()),
        byteArrayOf(0x00.toByte(), 0x01.toByte()),
        byteArrayOf(0xff.toByte(), 0x00.toByte()),
        byteArrayOf(0x00.toByte(), 0xff.toByte()),

        byteArrayOf(0x01.toByte()),
        byteArrayOf(0xff.toByte())
    )

    @Test
    fun test_toLittleEndianUInt() {
        assertEquals(0u, cases[0].toLittleEndianUInt())
        assertEquals(4294967295u, cases[1].toLittleEndianUInt())
        assertEquals(65793u, cases[2].toLittleEndianUInt())
        assertEquals(257u, cases[3].toLittleEndianUInt())
        assertEquals(1u, cases[4].toLittleEndianUInt())

        assertEquals(1u, cases[5].toLittleEndianUInt())
        assertEquals(256u, cases[6].toLittleEndianUInt())
        assertEquals(255u, cases[7].toLittleEndianUInt())
        assertEquals(65280u, cases[8].toLittleEndianUInt())

        assertEquals(1u, cases[9].toLittleEndianUInt())
        assertEquals(255u, cases[10].toLittleEndianUInt())
    }

    @Test
    fun test_toBigEndianUInt() {
        assertEquals(0u, cases[0].toBigEndianUInt())
        assertEquals(4294967295u, cases[1].toBigEndianUInt())
        assertEquals(16843008u, cases[2].toBigEndianUInt())
        assertEquals(16842752u, cases[3].toBigEndianUInt())
        assertEquals(16777216u, cases[4].toBigEndianUInt())

        assertEquals(256u, cases[5].toBigEndianUInt())
        assertEquals(1u, cases[6].toBigEndianUInt())
        assertEquals(65280u, cases[7].toBigEndianUInt())
        assertEquals(255u, cases[8].toBigEndianUInt())

        assertEquals(1u, cases[9].toBigEndianUInt())
        assertEquals(255u, cases[10].toBigEndianUInt())
    }

    @Test
    fun test_byte2UInt() {
        val byte = 0xff
        assertEquals(255u, byte.toUInt())
        assertEquals(255, byte.toUInt().toInt())
    }
}