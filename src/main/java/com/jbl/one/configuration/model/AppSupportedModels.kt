package com.jbl.one.configuration.model

import com.google.gson.annotations.SerializedName
import java.util.LinkedList

data class AppSupportedModels(
    @SerializedName("is_support_customer_service")
    val isSupportCustomerService: Boolean? = true,
    @SerializedName("sign_up_news_letter_url")
    val signUpUrl: String? = null,
    @SerializedName("wifi_setup_fail_url")
    val wifiSetupFailUrl: String? = null,
    @SerializedName("google_cast_intro")
    val googleCastIntro: String? = null,
    @SerializedName("set_up_multi_room_control")
    val setUpMultiRoomControl: String? = null,
    @SerializedName("google_play_google_home")
    val googlePlayGoogleHome: String? = null,
    @SerializedName("music_streaming_with_alexa_cast")
    val musicStreamingWithAlexaCast: String? = null,
    @SerializedName("multi_room_music")
    val multiRoomMusic: String? = null,
    @SerializedName("setup_a_preferred_speaker")
    val setupPreferredSpeaker: String? = null,
    @SerializedName("google_play_amazon_alexa")
    val googlePlayAmazonAlexa: String? = null,
    @SerializedName("how_to_stream_with_spotify_connect")
    val howToStreamWithSpotifyConnect: String? = null,
    @SerializedName("google_play_spotify")
    val googlePlaySpotify: String? = null,
    @SerializedName("tidal_connect")
    val tidalConnect: String? = null,
    @SerializedName("google_play_tidal")
    val googlePlayTidal: String? = null,
    @SerializedName("roon_home")
    val roonHome: String? = null,
    @SerializedName("roon_redeem_format")
    val roonRedeemFormat: String? = null,
    @SerializedName("google_play_roon")
    val googlePlayRoon: String? = null,
    @SerializedName("alexa_mrm")
    val alexaMrm: String? = null,
    @SerializedName("chromecast_built_in")
    val chromecastBuiltIn: String? = null,
    @SerializedName("multiroom_music_google")
    val multiroomMusicGoogle: String? = null,
    @SerializedName("my_assistant_activity")
    val myAssistantActivity: String? = null,
    @SerializedName("music_streaming_with_alexacast")
    val musicStreamingWithAlexacast: String? = null,
    @SerializedName("multiroom_music_alexa")
    val multiroomMusicAlexa: String? = null,
    @SerializedName("voice_language")
    val voiceLanguage: String? = null,
    @SerializedName("learn_more_about_va")
    val learnMoreAboutVa: String? = null,
    @SerializedName("learn_more_about_airplay")
    val learnMoreAboutAirplay: String? = null,
    @SerializedName("product_list")
    val modelList: HashMap<String, Model>? = null,
    @SerializedName("learn_more_about_qobuz")
    val learnMoreAboutQobuz: String? = null,
    @SerializedName("google_play_qobuz")
    val googlePlayQobuz: String? = null,
)

data class Model(
    @SerializedName("model_name")
    val modelName: String? = null,
    @SerializedName("category")
    val category: String? = null, // 'one', 'partylight', 'partybox', 'portable'
    @SerializedName("redirection_app")
    val redirectionApp: String? = null, // 'soundbar', 'partybox', 'portable'
    @SerializedName("device_control_package")
    val deviceControlPage: String? = null,
    @SerializedName("adv_format")
    val advFormat: String? = null,
    @SerializedName("cmd_format")
    val cmdFormat: String? = null,
    @SerializedName("auth_button")
    val authButton: String? = null
){
    //It's best to  define it in config file
    val shorName: String?
        get() = modelName?.replace("jbl","",true)?.replace("harman kardon","",true)

}

data class ModelConfig(
    @SerializedName("reference_name")
    val referenceName: String? = null,
    @SerializedName("pid")
    val pid: String? = null,
    @SerializedName("chip_platform")
    val chipPlatform: String? = null,
    @SerializedName("capability")
    val capability: LinkedList<ProductFeature>? = null,
    @SerializedName("stream_service")
    val streamService: LinkedList<StreamService>? = null,
    @SerializedName("tag")
    val tags: LinkedList<String>? = null,
    @SerializedName("url")
    val urls: Urls? = null,
    @SerializedName("ota_mcu_name")
    val mcuName: String? = null,
    @SerializedName("ota_fw_uid")
    val fwUid: String? = null,
    @SerializedName("image_version")
    val imageVer: String? = null,
    @SerializedName("orientation")
    /**
     * The value of orientation is one of horizontal and vertical. The default value is horizontal.
     */
    val orientation: String? = "horizontal",
    @SerializedName("remote_controller_style")
    val remoteControllerStyle: String? = null,
    /**
     * colorNamesMap item: <color Id, Official color name>
     */
    @SerializedName("color_names")
    val colorNamesMap: Map<String, String>?,
    @SerializedName("has_preset_eq_config")
    val hasPresetEqConfig: Boolean?,
    @SerializedName("max_volume")
    val maxVolume: Int?,
    @SerializedName("ota_battery_threshold")
    val otaBatteryThreshold: Int?,
    @SerializedName("device_analytics_category")
    val deviceAnalyticsCategory: String?

)
enum class Orientation(var value: String) {
    HORIZONTAL("horizontal"),
    VERTICAL("vertical")
}

enum class RemoteControllerStyle(var value: String) {
    /**
     * For Gen 3 JBL Bar 300, JBL Bar 500,
     */
    REMOTE_CONTROLLER_STYLE_1("remote_controller_style_1"),

    /**
     * For Gen 3 JBL Bar 700,
     */
    REMOTE_CONTROLLER_STYLE_2("remote_controller_style_2"),

    /**
     * For Gen 3 JBL Bar 800, JBL Bar 1000, JBL Bar 1300,
     */
    REMOTE_CONTROLLER_STYLE_3("remote_controller_style_3"),
    /**
     * For Gen 4 JBL Bar 300MK2, JBL Bar 500MK2
     */
    REMOTE_CONTROLLER_STYLE_4("remote_controller_style_4"),
    /**
     * For Gen 4 JBL Bar 700MK2, JBL Bar 800MK2
     */
    REMOTE_CONTROLLER_STYLE_5("remote_controller_style_5"),
    /**
     * For Gen 4 JBL Bar 1000MK2, JBL Bar 1300MK2
     */
    REMOTE_CONTROLLER_STYLE_6("remote_controller_style_6"),
    /**
     * For Harman Kardon Enchant 900, Harman Kardon Enchant 1100
     */
    REMOTE_CONTROLLER_STYLE_7("remote_controller_style_7");

    companion object {

        @JvmStatic
        fun getByValue(value: String?) =
            entries.find { tmp -> tmp.value == value }
    }
}

data class Urls(
    @SerializedName("url_support")
    val urlSupport: String? = null,
    @SerializedName("url_qsg")
    val urlQsg: String? = null,
    @SerializedName("url_faq")
    val urlFaq: String? = null,
    @SerializedName("url_product_register")
    val urlProductRegister: String? = null
):java.io.Serializable