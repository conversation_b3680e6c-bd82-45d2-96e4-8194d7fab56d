package com.jbl.one.configuration

import android.content.Context
import androidx.annotation.WorkerThread
import com.jbl.one.configuration.AppConfigurationUtils.loadAddProductZipFile
import com.jbl.one.configuration.AppConfigurationUtils.loadMusicService
import com.jbl.one.configuration.AppConfigurationUtils.loadOtaConfig
import com.jbl.one.configuration.AppConfigurationUtils.loadPresetEQConfig
import com.jbl.one.configuration.AppConfigurationUtils.loadRedirectionSupportList
import com.jbl.one.configuration.AppConfigurationUtils.loadSupportList
import com.jbl.one.configuration.impl.ConfigurationImpl
import com.jbl.one.configuration.impl.ConfigurationImpl.loadRedirectionSupportList
import com.jbl.one.configuration.model.AppSupportedModels
import com.jbl.one.configuration.model.AuraCastSupportedModels
import com.jbl.one.configuration.model.AuthButton
import com.jbl.one.configuration.model.EnumDeviceAnalyticsCategory
import com.jbl.one.configuration.model.HmAuraCastCmdFormat
import com.jbl.one.configuration.model.HmAuraCastPartyIcon
import com.jbl.one.configuration.model.HmBleAdvFormat
import com.jbl.one.configuration.model.ModelConfig
import com.jbl.one.configuration.model.MultichannelCombinationConfig
import com.jbl.one.configuration.model.MultichannelPlacementConfig
import com.jbl.one.configuration.model.MusicServiceConfig
import com.jbl.one.configuration.model.OtaConfig
import com.jbl.one.configuration.model.PresetEQDataConfig
import com.jbl.one.configuration.model.ProductFeature
import com.jbl.one.configuration.model.RemoteControllerStyle
import com.jbl.one.configuration.model.StreamService
import com.jbl.one.configuration.observer.LoadObserver
import com.jbl.one.remote.configuration.BuildConfig
import java.util.LinkedList

object AppConfigurationUtils {

    /**
     * Make sure to call this function during app initialization.
     * Then developer can retrieve preset EQ config, model config and OTConfig
     * by function [loadPresetEQConfig], [loadMusicService] and [loadOtaConfig] accordingly
     * @see loadSupportList(context: [Context])
     * @see loadRedirectionSupportList(context: [Context])
     * @see loadAddProductZipFile()
     * @see loadMusicService()
     */
    fun loadEssentialConfig(context: Context) {
        loadSupportList(context)
        loadRedirectionSupportList(context)
        loadCrossAuraCastSupportList(context)
        loadMultichannelCombination(context)
        loadMultichannelPlacement(context)
        loadAddProductZipFile()
        loadMusicService()
        loadOneDeviceAbnormalZipFile()

    }

    /**
     * Load device list which support redirection.
     */
    fun loadRedirectionSupportList(context: Context) {
        loadRedirectionSupportList(
            context, BuildConfig.APP_CONFIGURATION_HOST
                    + BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.REDIRECTION_SUPPORT_LIST
        )
    }

    /**
     * Load AddProduct zip files.
     * @param loadObserver, observe progress of loading AddManual zip file.
     */
    fun loadAddProductZipFile(loadObserver: LoadObserver? = null) {
        ConfigurationImpl.loadAddProductZipFile(
            BuildConfig.APP_CONFIGURATION_HOST +
                    BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.ADD_PRODUCT_ZIP_FILE, loadObserver
        )
    }

    /**
     * Load preset EQ config once App detect device.
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun loadPresetEQConfig(pid: String) {
        val pidLowercase = pid.lowercase()
        val configUrl =
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}${getCategory(pidLowercase)}/$pidLowercase/${BuildConfig.PRESET_EQ_CONFIG_NAME}"
        return ConfigurationImpl.loadPresetEQDataConfig(configUrl)
    }

    fun loadBandBoxMicModels(pid: String) {
        val pidLowercase = pid.lowercase()
        val configUrl =
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}${getCategory(pidLowercase)}/$pidLowercase/${BuildConfig.BAND_BOX_MIC_MODELS}"
        return ConfigurationImpl.loadBandBoxMicModels(configUrl)
    }

    fun getBandBoxMicModels() = ConfigurationImpl.bandBoxMicModels

    fun getRemoteEq(pid: String): LinkedList<PresetEQDataConfig>? {
        val pidLowercase = pid.lowercase()
        val configUrl =
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}${getCategory(pidLowercase)}/$pidLowercase/${BuildConfig.PRESET_EQ_CONFIG_NAME}"
        return ConfigurationImpl.getPresetEQDataConfig(configUrl)
    }

    /**
     * Load device control pages.
     * @param loadObserver, observe progress of loading device control zip file.
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    @WorkerThread
    fun loadDeviceControlZipFile(pid: String, loadObserver: LoadObserver? = null, retry: Boolean? = false) {
        ConfigurationImpl.loadDeviceControlZipFile(pid, loadObserver, retry)
    }

    /**
     * Get local hybrid file url
     * - **Warning**: Avoid frequent calls to this method. This method performs a check to verify
     * if the ZIP package has been correctly extracted, which takes approximately 20 milliseconds.
     */
    fun getLocalDeviceControlHybridFilesUrl(pid: String): String? = ConfigurationImpl.getLocalDeviceControlHybridFilesUrl(pid)

    /**
     * ${addNewProductFilePath}/index.html
     * - **Warning**: Avoid frequent calls to this method. This method performs a check to verify
     * if the ZIP package has been correctly extracted, which takes approximately 20 milliseconds.
     */
    fun getLocalAddProductHybridFilesUrl(): String? = ConfigurationImpl.getLocalAddProductHybridFilesUrl()

    /**
     * - **Warning**: Avoid frequent calls to this method. This method performs a check to verify
     * if the ZIP package has been correctly extracted, which takes approximately 20 milliseconds.
     */
    fun getOneDeviceAbnormalUrl(): String? = ConfigurationImpl.oneDeviceAbnormalUrl()

    /**
     * ${addNewProductFilePath}/pairing.html
     * - **Warning**: Avoid frequent calls to this method. This method performs a check to verify
     * if the ZIP package has been correctly extracted, which takes approximately 20 milliseconds.
     */
    fun getLocalSetupProductHybridFilesUrl(): String? = ConfigurationImpl.getLocalSetupProductHybridFilesUrl()

    /**
     * - **Warning**: Avoid frequent calls to this method. This method performs a check to verify
     * if the ZIP package has been correctly extracted, which takes approximately 20 milliseconds.
     */
    fun getLocalWhatIsNewHybridFilesUrl(): String? = ConfigurationImpl.getLocalWhatIsNewHybridFilesUrl()

    /**
     * Get supported model list
     */
    fun getSupportModelList(): AppSupportedModels? = ConfigurationImpl.supportList()

    /**
     * Get supported model name list
     */
    fun getSupportPidList(): List<String>? = getSupportModelList()?.modelList?.map { it.key }

    /**
     * Get supported model name list
     */
    fun getSupportModelNameList(): List<String>? = getSupportModelList()?.modelList?.map { it.value.modelName ?: "" }

    /**
     * Get redirection supported model list
     */
    fun getRedirectionSupportList(): AppSupportedModels? = ConfigurationImpl.redirectionSupportList()

    /**
     * Get cross aura cast supported model list
     */
    fun getCrossAuraCastSupportList(): AuraCastSupportedModels? = ConfigurationImpl.crossAuraCastSupportList()

    /**
     * Load music service config once App launched
     */
    fun getMusicService(): MusicServiceConfig? = ConfigurationImpl.musicServiceConfig()

    fun getMultichannelCombinationConfig(): MultichannelCombinationConfig? = ConfigurationImpl.multichannelCombinationConfig()

    fun getMultichannelPlacementConfig(): MultichannelPlacementConfig? = ConfigurationImpl.multichannelPlacementConfig()

    /**
     * Get model render full path
     * @param pid Product ID
     * @param colorId color ID
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getModelRenderPath(pid: String, colorId: String?) = getCategory(pid)?.let { category ->
        "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}$category/" +
                "${pid.lowercase()}/${mapCid(colorId)}.png"
    }

    fun getAuraCastModelRenderPath(pid: String, colorId: String?) = ConfigurationImpl.crossAuraCastSupportList()
        ?.modelList?.get(pid)?.let {
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.CROSS_AURACAST_RENDER_PATH}" +
                    "${pid.lowercase()}/${mapCid(colorId)}.png"
        }

    /**
     * Get Pid from model's render path.
     * @param renderPath full path of model render picture
     */
    fun getPidByRenderPath(renderPath: String?): String? {
        if (renderPath == null) {
            return null
        }
        val indices = renderPath.lastIndexOf('/').let { last ->
            if (last == -1) {
                return null
            }

            val secondLast = renderPath.substring(0, last).lastIndexOf('/')
            if (secondLast == -1) {
                return null
            }

            listOf(secondLast, last)
        }
        return renderPath.substring(indices[0] + 1, indices[1])
    }


    /**
     * Get demo img about device OOBE auth
     * @param pid Product ID
     * @param colorId color ID
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getOOBEAuthPath(pid: String, colorId: String?) = getCategory(pid)?.let { category ->
        "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}$category" +
                "/${pid.lowercase()}/${mapCid(colorId)}_oobe_auth.png"
    }

    /**
     * Get model render full path
     * @param modelName device model name
     * @param colorId color ID
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getModelRenderPathByModelName(modelName: String, colorId: String = "01"): String? {
        return getPidByModelName(modelName)?.let { getModelRenderPath(it, colorId) }
    }

    /**
     * Get sign up news letter Url
     */
    fun getSignupNewsLetterUrl(): String? {
        return ConfigurationImpl.signupNewsLetterUrl()
    }

    /**
     * Get wifi setup fail url
     */
    fun getWifiSetupFailUrl(): String? {
        return ConfigurationImpl.wifiSetupFailUrl()
    }

    /**
     * Google Cast intro.
     */
    fun getGoogleCastIntroUrl(): String? = ConfigurationImpl.googleCastIntroUrl()

    /**
     * Google Cast Set Up Multi Room Control intro.
     */
    fun getSetUpMultiRoomControlUrl(): String? = ConfigurationImpl.setUpMultiRoomControlUrl()

    /**
     * Google Home link in Google Play.
     */
    fun getGPGoogleHomeUrl(): String? = ConfigurationImpl.googlePlayGoogleHomeUrl()

    /**
     * Music streaming with Alexa cast
     */
    fun getMusicStreamingWithAlexaCastUrl(): String? = ConfigurationImpl.musicStreamingWithAlexaCastUrl()

    /**
     * Multi-room music
     */
    fun getMultiRoomMusicUrl(): String? = ConfigurationImpl.multiRoomMusicUrl()

    /**
     * Setup a Preferred Speaker
     */
    fun getSetupPreferredSpeakerUrl(): String? = ConfigurationImpl.setupPreferredSpeakerUrl()

    /**
     * Amazon Alexa link in Google Play.
     */
    fun getGPAmazonAlexaUrl(): String? = ConfigurationImpl.googlePlayAmazonAlexaUrl()

    /**
     * Spotify stream.
     */
    fun getStreamWithSpotifyUrl(): String? = ConfigurationImpl.streamWithSpotifyUrl()

    /**
     * Spotify app in Google Play
     */
    fun getGPSpotifyUrl(): String? = ConfigurationImpl.googlePlaySpotifyUrl()

    /**
     * Tidal connect
     */
    fun getTidalConnectUrl(): String? = ConfigurationImpl.tidalConnectUrl()

    /**
     * Tidal app in Google Play
     */
    fun getGPTidalUrl(): String? = ConfigurationImpl.googlePlayTidalUrl()

    /**
     * Home of Roon Ready
     */
    fun getRoonHomeUrl(): String? = ConfigurationImpl.roonHomeUrl()

    /**
     * Url format of Roon Ready redeem url with placeholder of %s
     */
    fun getRoonRedeemFormat(): String? = ConfigurationImpl.roonRedeemFormat()

    /**
     * Roon Ready app in Google Play
     */
    fun getGPRoonUrl(): String? = ConfigurationImpl.googlePlayRoonUrl()

    /**
     * Alexa MRM intro. in WiFi Streaming page.
     */
    fun getAlexaMRMUrl(): String? = ConfigurationImpl.alexaMRMUrl()

    /**
     * if Google VA and  Alexa VA are all enabled ,app will show Learn more about va page.
     */
    fun getLearnMoreAboutVaUrl(): String? = ConfigurationImpl.learnMoreAboutVaUrl()

    fun getChromecastBuiltInUrl(): String? = ConfigurationImpl.chromecastBuiltInUrl()

    fun getMultiroomMusicGoogleUrl(): String? = ConfigurationImpl.multiroomMusicGoogleUrl()

    fun getMyAssistantActivity(): String? = ConfigurationImpl.myAssistantActivity()

    fun getMultiroomMusicAlexaUrl(): String? = ConfigurationImpl.multiRoomMusicAlexaUrl()

    fun getVoiceLanguageUrl(): String? = ConfigurationImpl.voiceLanguageUrl()

    fun getLearnMoreAboutAirPlayUrl(): String? = ConfigurationImpl.learnMoreAboutAirPlayUrl()

    fun getLearnMoreAboutQobuzUrl(): String? = ConfigurationImpl.learnMoreAboutQobuzUrl()

    fun getGPQobuzUrl(): String? = ConfigurationImpl.googlePlayQobuzUrl()

    /**
     * Get moment trigger image path
     * @param pid Product ID
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getModelMomentTriggerImgPath(pid: String, cid: String? = "01"): String? {
        val pidLowercase = pid.lowercase()
        val realCid = mapCid(cid)
        return getCategory(pidLowercase)?.let {
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}$it/$pidLowercase/${realCid}_moment_trigger.png"
        }
    }

    /**
     * Load [OtaConfig] by specified [pid]
     */
    fun loadOtaConfig(pid: String) {
        val configUrl = getOtaConfigPath(pid)
        ConfigurationImpl.loadOtaConfig(configUrl = configUrl, pid = pid.lowercase())
    }

    /**
     * Get [OtaConfig] by specified [pid]
     */
    fun getOtaConfig(pid: String): OtaConfig? {
        val configUrl = getOtaConfigPath(pid)
        return ConfigurationImpl.getOtaConfig(configUrl)
    }

    /**
     * Get USB/BT(BLE, EDR, SPP) OTA root dir path by specified [pid] like:
     * [BuildConfig.APP_CONFIGURATION_HOST] / [BuildConfig.REMOTE_CONFIG_FLAVOR]/ota/210b/
     */
    fun getOtaRootPath(pid: String): String {
        val pidLowercase = pid.lowercase()
        if (getModelConfig(pid)?.capability?.contains(ProductFeature.BTOta) == false
            && getModelConfig(pid)?.capability?.contains(ProductFeature.USBOta) == false
        ) {
            return ""
        }
        return if (BuildConfig.REMOTE_CONFIG_FLAVOR.contains("jbl_one")
            || BuildConfig.REMOTE_CONFIG_FLAVOR.contains("hk_one")
        ) {
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR}/ota/$pidLowercase/"
        } else {
            ""
        }
    }

    /**
     * Get [ModelConfig] by specified [pid]
     */
    fun getModelConfig(pid: String): ModelConfig? {
        val pidLowercase = pid.lowercase()
        val configUrl =
            "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}${getCategory(pidLowercase)}/$pidLowercase/${BuildConfig.MODEL_CONFIG_NAME}"
        return ConfigurationImpl.modelConfig(configUrl)
    }

    /**
     * Get [ModelConfig] by specified [modelName]
     */
    fun getModelConfigByModelName(modelName: String) = getPidByModelName(modelName)?.let { getModelConfig(it) }

    fun getPidByModelName(modelName: String) = ConfigurationImpl.pid(modelName)

    /**
     * Check if app support [pid]
     * @return true if app support [pid], otherwise false
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun isSupportedDevice(pid: String?) = pid?.let { ConfigurationImpl.isSupportedDevice(it) } ?: false

    /**
     * Check if app support [pid] for redirection feature
     * @return true if app support [pid], otherwise false
     * @throws IllegalStateException if app didn't call function [loadRedirectionSupportList] first
     */
    fun isSupportedRedirection(pid: String?) = pid?.let { ConfigurationImpl.isSupportedRedirection(it) } ?: false

    /**
     * Check if device [pid] support cross auraCast
     * @return true if device [pid] support cross auraCast, otherwise false
     * @throws IllegalStateException if app didn't call function [loadRedirectionSupportList] first
     */
    fun isSupportedCrossAuraCast(pid: String?) = pid?.let { ConfigurationImpl.isSupportedCrossAuraCast(it) } ?: false

    /**
     * Check if device [pid] support [ProductFeature.Battery]
     * @return true if app support [ProductFeature.Battery], otherwise false
     */
    fun isSupportBattery(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(pid)?.capability?.contains(ProductFeature.Battery) ?: false
    } ?: false

    /**
     * Check if device [pid] support [ProductFeature.BleControl]
     * @return true if app support [ProductFeature.BleControl], otherwise false
     */
    fun isSupportBleControl(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(pid)?.capability?.contains(ProductFeature.BleControl) ?: false
    } ?: false

    /**
     * Check if device [pid] support [ProductFeature.PairStereo]
     * @return true if app support [ProductFeature.PairStereo], otherwise false
     */
    fun isSupportPairStereo(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(pid)?.capability?.contains(ProductFeature.PairStereo) ?: false
    } ?: false

    /**
     * Check if device [pid] support [ProductFeature.WiFiControl]
     * @return true if app support [ProductFeature.WiFiControl], otherwise false
     */
    fun isSupportWiFiControl(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(pid)?.capability?.contains(ProductFeature.WiFiControl) ?: false
    } ?: false

    /**
     * Check if device [pid] support [StreamService.QobuzConnect]
     * @return true if app support [StreamService.QobuzConnect], otherwise false
     */
    fun isSupportStreamServiceQobuzConnect(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(it)?.streamService?.contains(StreamService.QobuzConnect)
    } ?: false

    /**
     * Check if device [pid] support [StreamService.RoonReady]
     * @return true if app support [StreamService.RoonReady], otherwise false
     */
    fun isSupportStreamServiceRoonReady(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(it)?.streamService?.contains(StreamService.RoonReady)
    } ?: false

    /**
     * check light info support [ProductFeature.Mood] or not by [pid]
     */
    fun iSupportMood(pid: String?) = pid?.lowercase()?.let {
        getModelConfig(it)?.capability?.contains(ProductFeature.Mood)
    } ?: false

    /**
     * check if app support customer service.
     */
    fun isSupportCustomerService() = getSupportModelList()?.isSupportCustomerService

    /**
     * Get default model name by specified [pid]
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getDefaultModelName(pid: String?) = pid?.let { ConfigurationImpl.defaultModelName(pid) }

    /**
     * Get category by specified [pid]
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getCategory(pid: String?) = pid?.let { ConfigurationImpl.category(pid) }

    /**
     * Get redirection app by specified [pid]
     * @throws IllegalStateException if app didn't call function [loadRedirectionSupportList] first
     */
    fun getRedirectionApp(pid: String?) = pid?.let { ConfigurationImpl.redirectionApp(pid) }


    /**
     * Get image version by specified pid
     */
    fun getImageVer(pid: String): String? = getModelConfig(pid)?.imageVer

    /**
     * Get Auracast device image version by specified pid
     */
    fun getAuraCastImageVer(pid: String): String? = getCrossAuraCastSupportList()?.modelList?.get(pid)?.imageVer


    /**
     * Get AuraCast FAQ url
     */
    fun getAuraCastFaqUrl() = "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.CROSS_AURACAST_FAQ}"

    fun getAuraCastPartyIcon(pid: String): HmAuraCastPartyIcon = ConfigurationImpl.getAuraCastPartyIcon(pid)

    /**
     * Get Create Party FAQ url
     */
    fun getCreatePartyFaqUrl() = "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.CROSS_AURACAST_CREATE_PARTY}"

    fun getOfficialColorName(pid: String, colorId: String): String? = getModelConfig(pid = pid)?.colorNamesMap?.getOrDefault(colorId, null)

    fun getColorIdByOfficialColorName(pid: String, officialColorName: String): String? =
        getModelConfig(pid = pid)?.colorNamesMap?.filter { it.value == officialColorName }?.keys?.firstOrNull()

    /**
     * Remote Controller Style:
     * @return
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_1] for For Gen 3 JBL Bar 300, JBL Bar 500
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_2] for Gen 3 JBL Bar 700
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_3] for Gen 3 JBL Bar 800, JBL Bar 1000, JBL Bar 1300
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_4] for Gen 4 JBL Bar 300MK2, JBL Bar 500MK2
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_5] for Gen 4 JBL Bar 700MK2, JBL Bar 800MK2
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_6] for Gen 4 JBL Bar 1000MK2, JBL Bar 1300MK2
     * * [RemoteControllerStyle.REMOTE_CONTROLLER_STYLE_7] for Harman Kardon Enchant 900, Harman Kardon Enchant 1100
     */
    fun remoteControllerStyle(pid: String): RemoteControllerStyle? =
        getModelConfig(pid = pid)?.remoteControllerStyle?.let { RemoteControllerStyle.getByValue(it) }

    /**
     * Get BLE ADV format.
     * @return type [HmBleAdvFormat]
     */
    fun getBleAdvFormat(pid: String): HmBleAdvFormat? = ConfigurationImpl.getBleAdvFormat(pid)

    /**
     * Get BLE ADV format.
     * @return type [HmAuraCastCmdFormat]
     */
    fun getAuraCastCmdFormat(pid: String): HmAuraCastCmdFormat? = ConfigurationImpl.getAuraCastCmdFormat(pid)

    /**
     * Get physical authentication button.
     * @return type [HmAuraCastCmdFormat]
     */
    fun getAuthButton(pid: String): AuthButton = ConfigurationImpl.getAuthButton(pid)

    /**
     * Make sure to call this function or function [loadSupportList] (context: [Context], configUrl: [String]) during app initialization.
     * Then developer can retrieve preset EQ config, model config and OTConfig
     * by function [loadPresetEQConfig], and [loadOtaConfig] accordingly
     * @see loadSupportList(context: [Context], configUrl: [String])
     */
    private fun loadSupportList(context: Context) {
        ConfigurationImpl.loadSupportList(
            context, BuildConfig.APP_CONFIGURATION_HOST
                    + BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.PRODUCT_LIST_CONFIG_NAME
        )
    }

    /**
     * Load multichannel config of combination.
     */
    private fun loadMultichannelCombination(context: Context) {
        ConfigurationImpl.loadMultichannelCombination(
            context, BuildConfig.APP_CONFIGURATION_HOST
                    + BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.MULTICHANNEL_COMBINATION_CONFIG_NAME
        )
    }

    /**
     * Load multichannel config of placement.
     */
    private fun loadMultichannelPlacement(context: Context) {
        ConfigurationImpl.loadMultichannelPlacement(
            context, BuildConfig.APP_CONFIGURATION_HOST
                    + BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.MULTICHANNEL_PLACEMENT_CONFIG_NAME
        )
    }

    /**
     * Load device list which support cross auraCast.
     */
    private fun loadCrossAuraCastSupportList(context: Context) {
        ConfigurationImpl.loadCrossAuraCastSupportList(
            context, BuildConfig.APP_CONFIGURATION_HOST + BuildConfig.CROSS_AURACAST_SUPPORT_LIST
        )
    }

    /**
     * Load One Device Abnormal zip files.
     * @param loadObserver, observe progress of loading AddManual zip file.
     */
    private fun loadOneDeviceAbnormalZipFile(loadObserver: LoadObserver? = null) {
        ConfigurationImpl.loadOneDeviceAbnormalZipFile(
            BuildConfig.APP_CONFIGURATION_HOST +
                    BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.ONE_DEVICE_ABNORMAL, loadObserver
        )
    }

    /**
     * Load music service config once App launched
     */
    private fun loadMusicService() {
        ConfigurationImpl.loadMusicService(BuildConfig.APP_CONFIGURATION_HOST + BuildConfig.REMOTE_CONFIG_FLAVOR_PATH + BuildConfig.MUSIC_SERVICE_CONFIG_NAME)
    }

    /**
     * Get Ota config json file path by specified [pid] like:
     * https://tools.onecloud.harman.com/files/jbl_partybox/ota/2108/ota_config.json
     */
    private fun getOtaConfigPath(pid: String): String {
        val rootPath = getOtaRootPath(pid = pid)

        return if (rootPath.isBlank()) {
            ""
        } else {
            rootPath + "ota_config.json"
        }
    }

    /**
     * has Preset EQ configuration.
     */
    fun hasPresetEqConfig(pid: String?) = true == pid?.let { getModelConfig(it)?.hasPresetEqConfig }

    /**
     * whether need app to calculate EQ curve
     */
    fun needEqAlgorithm(pid: String?) = true == pid?.let {
        getModelConfig(it)?.capability?.contains(
            ProductFeature.NeedEqAlgorithm
        )
    }

    fun tags(pid: String) = getModelConfig(pid.lowercase())?.tags

    fun isSoundBar(pid: String): Boolean = true == tags(pid)?.any { it.equals("soundbar", ignoreCase = true) }

    fun isSub(pid: String): Boolean = true == tags(pid)?.any { it.equals("sub", ignoreCase = true) }

    fun isOneCommander(pid: String): Boolean = true == tags(pid)?.any { it.equals("oneCommander", ignoreCase = true) }

    fun supportLightControl(pid: String): Boolean = true == pid?.let {
        getModelConfig(it)?.capability?.contains(
            ProductFeature.LightControl
        )
    }

    fun maxVolume(pid: String): Int = getModelConfig(pid.lowercase())?.maxVolume ?: 100

    fun supportBluetoothReconnect(pid: String?): Boolean = true == pid?.let {
        getModelConfig(it)?.capability?.contains(ProductFeature.BluetoothConfiguration)
    }

    /**
     * OTA battery threshold
     */
    fun otaBatteryThreshold(pid: String): Int = getModelConfig(pid.lowercase())?.otaBatteryThreshold ?: 30

    /**
     * Device analytics category
     * @return value of [EnumDeviceAnalyticsCategory]
     */
    fun deviceAnalyticsCategory(pid: String) = getModelConfig(pid.lowercase())?.deviceAnalyticsCategory ?: EnumDeviceAnalyticsCategory.Home.value

    private fun mapCid(colorId: String?): String = (colorId?.takeIf {
        it.isNotBlank() && "null" != it // To avoid same illegal cache
    } ?: "01")
        .removePrefix("0x")
        .padStart(2, '0')
        .replace("00", "01")

    /**
     * Get model render full path
     * @param pid Product ID
     * @param colorId color ID
     * @throws IllegalStateException if app didn't call function [loadSupportList] first
     */
    fun getGroupDevicesOtaTipsRenderPath(pid: String, colorId: String?) = getCategory(pid)?.let { category ->
        "${BuildConfig.APP_CONFIGURATION_HOST}${BuildConfig.REMOTE_CONFIG_FLAVOR_PATH}$category/" +
                "${pid.lowercase()}/${mapCid(colorId)}_group_devices_ota_tips.png"
    }
}
